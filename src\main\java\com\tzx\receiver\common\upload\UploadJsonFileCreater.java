package com.tzx.receiver.common.upload;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.tzx.publics.util.DateUtil;
import com.tzx.receiver.common.utils.DBUtils;
import com.tzx.receiver.common.utils.SignUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.jdbc.support.rowset.SqlRowSetMetaData;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.sql.Types;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2019-04-22
 * @Descption
 **/
public class UploadJsonFileCreater implements IUploadFileCreater{
    private final Logger logger = LoggerFactory.getLogger(getClass());
    @Override
    public boolean createUploadFile(UploadTask uploadTask) {
        String guid = uploadTask.getGuid();
        try{
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            SimpleDateFormat sdf2 = new SimpleDateFormat("yyyyMMddHHmmss");
            String basePath = new File(System.getProperty("user.dir")).getParent()  + File.separator + "UploadCaches";
            File baseFile = new File(basePath);
            String path = basePath + File.separator + sdf.format(new Date());
            File file = new File(path);
            //文件夹不存在，则创建
            if(!file.exists()){
                file.mkdirs();
            }
            //删除以前数据
            File[] files = baseFile.listFiles();
            if (files != null){
                String firstFile = basePath + sdf.format(DateUtils.addDays(new Date(),-UploadGloVar.KEEY_FILE_DAY));
                for (File file1 : files) {
                    //比较名称比他小，就删除
                    if( file1.isDirectory()&&file1.getAbsolutePath().compareTo(firstFile)<0){
                        file1.delete();
                    }
                }
            }


            //文件名称
            String fileName = path + File.separator + "[" + uploadTask.getParam().getCommand() + "]" + sdf2.format(new Date()) + ".json";
            //最外层JSON体
            JsonObject jsonRoot = new JsonObject();
            //如果有要求签名，把需要签名字段放到第一层
            UploadParam uploadParam = uploadTask.getParam();
            if(StringUtils.isNotBlank(uploadParam.getSignKeys())){
                String signStr = uploadParam.getSignKeys();
                String[] signArray = signStr.split("\\|");
                Map<String,Object> map = new HashMap<>();
                for (String temp : signArray) {
                    String[] tempArray = temp.split("=");
                    if(tempArray.length==2){
                        String jsonKey = tempArray[0];
                        String objectKey = tempArray[1];
                        Object objectValue = null;
                        Class taskClass = uploadTask.getClass();
                        String getMethodName = "get" + objectKey.substring(0,1).toUpperCase()+ objectKey.substring(1);
                        Method getMethod = null;
                        try {
                            getMethod = taskClass.getMethod(getMethodName);
                        } catch (NoSuchMethodException e) {
                            logger.info("没有找到get方法");
    //                        e.printStackTrace();
                        }
                        if(getMethod!=null){
                            try {
                                objectValue = getMethod.invoke(uploadTask);
                            } catch (IllegalAccessException e) {
                                e.printStackTrace();
                            } catch (InvocationTargetException e) {
                                e.printStackTrace();
                            }

                        }else if(uploadTask.getExtendProperty().containsKey(objectKey)){
                            objectValue = uploadTask.getExtendProperty().get(objectKey);
                        }else{
                            logger.info("没有找到get方法");
                            return false;
                        }
                        if(objectValue==null){
                            logger.info("没有找到签名参数" + objectKey +"的值");
                            return false;
                        }
                        map.put(jsonKey,objectValue);
                        jsonRoot.addProperty(jsonKey,objectValue.toString());
                    }

                }
                if(map.size()>0){
                    String sign = SignUtils.getSign(map, SignUtils.KEY);
                    jsonRoot.addProperty("sign",sign);
                }
            }
            //然后开始写入表的内容
            for (Map.Entry<String,String> entry : uploadTask.getParam().getTables().entrySet()) {
                String jsonKey = entry.getKey();//json属性名称
                String tableName = entry.getValue();//表名称
                String jsonStyle = "0";//json属性形式，0为jsonObject ，1为jsonArray。默认为0
                String notEmpty = "0";
                //表名称由#分开，#前面是真正的表名称，后面如果是0 ，json对象是array，如果是1是obejct
                String[] tableInfos = tableName.split("#");
                if(tableInfos.length==1){
                    tableName = tableInfos[0];
                }else if(tableInfos.length==2){
                    tableName = tableInfos[0];
                    if(tableInfos[1].equals("1")){
                        jsonStyle = "1";
                    }
                }else if(tableInfos.length==3){
                    tableName = tableInfos[0];
                    if(tableInfos[1].equals("1")){
                        jsonStyle = "1";
                    }
                    if(tableInfos[2].equals("1")){
                        notEmpty = "1";
                    }
                }else{
                    logger.info("非法的json属性配置");
                    return false;
                }
                SqlRowSet dataByTable = DBUtils.getDataByTable(tableName);
                JsonArray jsonArray = new JsonArray();
                while(dataByTable.next()){
                    JsonObject rowJsonObject = new JsonObject();
                    SqlRowSetMetaData metaData = dataByTable.getMetaData();
                    for (int i = 1; i <= metaData.getColumnCount(); i++) {
                        String columnName =  metaData.getColumnName(i);
                        String columnValue = dataByTable.getString(i);
                        try{
                            if(metaData.getColumnType(i) == Types.DATE||metaData.getColumnType(i) == Types.TIME
                                    ||metaData.getColumnType(i) == Types.TIMESTAMP){
                                if(dataByTable.getDate(i)!=null){
                                    SimpleDateFormat sdf3 = null;
                                    if (columnName.equals("BBRQ")){
                                        sdf3 = new SimpleDateFormat("yyyy-MM-dd");
                                    }else{
                                        sdf3 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                                    }
                                    columnValue =  sdf3.format( dataByTable.getDate(i)) ;
                                    if (StringUtils.isNotEmpty(columnValue)){
                                        rowJsonObject.addProperty(columnName,columnValue);
                                    }
                                }

                                //数值型的
                            }else if(metaData.getColumnType(i) == Types.BIT||metaData.getColumnType(i) == Types.TINYINT
                                    ||metaData.getColumnType(i) == Types.SMALLINT||metaData.getColumnType(i) == Types.INTEGER
                                    ||metaData.getColumnType(i) == Types.BIGINT){
                                if(StringUtils.isNotBlank(columnValue)){
                                    Integer intValue = Integer.parseInt(columnValue);
                                    rowJsonObject.addProperty(columnName,intValue);
                                }
                                //浮点型
                            }else if(metaData.getColumnType(i) == Types.FLOAT
                                    ||metaData.getColumnType(i) == Types.REAL||metaData.getColumnType(i) == Types.DOUBLE
                                    ||metaData.getColumnType(i) == Types.NUMERIC||metaData.getColumnType(i) == Types.DECIMAL){
                                if(StringUtils.isNotBlank(columnValue)){
                                    Double doubleValue = Double.parseDouble(columnValue);
                                    rowJsonObject.addProperty(columnName,doubleValue);
                                }
                                //字符型
                            }else if(metaData.getColumnType(i) == Types.CHAR||metaData.getColumnType(i) == Types.VARCHAR||
                                    metaData.getColumnType(i) == Types.LONGVARCHAR){
                                if(columnValue!=null){
                                    rowJsonObject.addProperty(columnName,columnValue);
                                }
                                //其他类型
                            }else{
                                if(StringUtils.isNotBlank(columnValue)){
                                    columnValue = StringUtils.isBlank(columnValue)?"":columnValue;
                                    rowJsonObject.addProperty(columnName,columnValue);
                                }
                            }

                        }catch (Exception e){
                            logger.info(columnName);
                            logger.info(columnValue);
                            e.printStackTrace();

                        }

                    }
                    jsonArray.add(rowJsonObject);
                }
                //判断非空数组是否为0
                if("1".equals(notEmpty)&&jsonArray.size() == 0){
                    DBUtils.updateUploadDBLog("exceptionmsg", "生成JSON文件出错(" + jsonKey + "不能为空)" , guid);
                    DBUtils.updateUploadDBLog("upprocess", "100%", guid);
                    DBUtils.updateUploadDBLog("endtime", DateUtil.getNowDateYYDDMMHHMMSS(), guid);
                    DBUtils.updateUploadDBLog("runresult", "2", guid);
                    return false;
                }
                //如果数量大于0
                if(jsonArray.size()>0){
                    if(jsonStyle.equals("1")){
                        jsonRoot.add(jsonKey,jsonArray.get(0));
                    }else{
                        jsonRoot.add(jsonKey,jsonArray);
                    }
                }

            }

            //然后将其保存到文件中
            String jsonStr = jsonRoot.toString();
            FileWriter fileWriter = null;
            try {
                fileWriter = new FileWriter(fileName);
                fileWriter.write(jsonStr);
            } catch (IOException e) {
                e.printStackTrace();
                return false;
            }finally {
                try {
                    fileWriter.flush();
                    fileWriter.close();
                } catch (IOException e) {
                    e.printStackTrace();
                    return false;
                }
            }
            uploadTask.setFileName(fileName);
        }catch (Exception e){
            DBUtils.updateUploadDBLog("exceptionmsg", "生成JSON文件出错", guid);
            DBUtils.updateUploadDBLog("upprocess", "100%", guid);
            DBUtils.updateUploadDBLog("endtime", DateUtil.getNowDateYYDDMMHHMMSS(), guid);
            DBUtils.updateUploadDBLog("runresult", "2", guid);
            logger.error(e.getMessage());
            return false;
        }

        //最后设置一下文件名

        DBUtils.updateUploadDBLog("uploadfilename", uploadTask.getFileName(), uploadTask.getGuid());
        DBUtils.updateUploadDBLog("upprocess", "75%", uploadTask.getGuid());
        return true;
    }
}
