package com.tzx.mobilepos.rest.model;

import javax.persistence.Table;

@Table(name = "TQ_MEMBERINFO")
public class TqMemberInfo {

	private String billid; // 账单编号
	private String scancode; // 会员卡号
	private String datatype;
	private String remark; // 微生活返回该会员卡信息json
	private String canvipprice; // 是否使用会员价
	private int balance; // 卡余额，单位分
	private int is_usable; // 可用状态
	private int credit; // 积分余额，单位个
	private String phone; // 会员手机号

	public String getBillid() {
		return billid;
	}

	public void setBillid(String billid) {
		this.billid = billid;
	}

	public String getScancode() {
		return scancode;
	}

	public void setScancode(String scancode) {
		this.scancode = scancode;
	}

	public String getDatatype() {
		return datatype;
	}

	public void setDatatype(String datatype) {
		this.datatype = datatype;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getCanvipprice() {
		return canvipprice;
	}

	public void setCanvipprice(String canvipprice) {
		this.canvipprice = canvipprice;
	}

	public int getBalance() {
		return balance;
	}

	public void setBalance(int balance) {
		this.balance = balance;
	}

	public int getIs_usable() {
		return is_usable;
	}

	public void setIs_usable(int is_usable) {
		this.is_usable = is_usable;
	}

	public int getCredit() {
		return credit;
	}

	public void setCredit(int credit) {
		this.credit = credit;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

}
