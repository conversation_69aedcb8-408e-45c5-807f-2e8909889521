package com.tzx.publics.service.impl;

import com.alibaba.druid.util.Base64;
import com.tzx.publics.listener.InitDataListener;
import com.tzx.publics.mapper.InitDataMapper;
import com.tzx.publics.service.IInVoiceService;
import com.tzx.publics.util.*;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.Date;
import java.util.Map;

/**
 * Created by Zhouxh on 2019-12-18.
 */
@Service
public class InVoiceServiceImpl implements IInVoiceService{
    private final static Logger LOGGER = LoggerFactory.getLogger(InVoiceServiceImpl.class);
    @Autowired
    private InitDataMapper initDataMapper;

    @Override
    public String createEInvoice(String kdzdbh, Date jzsj, Date bbrq) {
        try {
            String organId = InitDataListener.organVo.getJgxh() + ""; // 机构id
            String tenancyId = InitDataListener.ggcsMap.get("tenancy_id"); // 发票商户id？
            String billId = kdzdbh; // 账单号
            double amount = initDataMapper.getKpje(kdzdbh); // 开票金额
            String ddrq = DateUtil.getYYYYMMDDFromDate(jzsj);// 结账时间 YYYYMMDD
            String time = DateUtil.gethhmmss(); // 当前时间HHMMSS
            String serviceType = "1"; // ?
            String url = InitDataListener.ggcsMap.get("KFPUrl"); // 电子发票请求地址（乡村基）
            String posInvoiceChanel = InitDataListener.ggcsMap.get("POS_INVOICE_CHANEL"); // 开发票渠道类型
            String dzfpqqdz = InitDataListener.ggcsMap.get("DZFPQQDZ");// 电子发票请求地址（通用）
            String sl = InitDataListener.organVo.getSl() + ""; // 电子发票税率
            String key = InitDataListener.organVo.getDzfpmy(); // 电子发票密钥
            String tssfkfp = InitDataListener.organVo.getTssfkfp();

            String lvNewUrl = "";

            LOGGER.info("开具电子发票：堂食是否开发票= " + tssfkfp + ",类型= " + posInvoiceChanel);

            if ("Y".equals(tssfkfp) && amount != 0) {
                if ("1".equals(posInvoiceChanel)) {
                    // 乡村基
                    if ("".equals(url)) {
                        LOGGER.info("没有配置总部电子发票请求地址，请正确设置后再重试。");
                        lvNewUrl = "";
                    } else {
                        String lvDH = billId + "@" + tenancyId + organId + ddrq + time; // 这个要保存到数据库中，取消用
                        String lvUrlPara = tenancyId + "#" + organId + "#" + ddrq + "#" + billId + "@" + time + "#" + serviceType + "#" + sl + "#" + amount;
                        LOGGER.info("开具电子发票：明文APlaintext= " + lvUrlPara + ",AKey = " + key);

                        String aCiphertext = AESUtil.aesEncodeNew(lvUrlPara, key);
                        aCiphertext = delBlankStr(aCiphertext); // 删除空格
                        String aMD5Value = MD5Util.md5(aCiphertext).toLowerCase();
                        aMD5Value = aMD5Value.substring(0, 4);
                        lvUrlPara = Base64.byteArrayToBase64((delBlankStr(lvUrlPara) + "#" + aMD5Value).getBytes());
                        lvUrlPara = delBlankStr(lvUrlPara);
                        LOGGER.info("开具电子发票：密文= " + lvUrlPara); // 记录操作日志
                        lvNewUrl = url + lvUrlPara; // 返回的字符串
                        LOGGER.info("开具电子发票：二维码内容= " + lvNewUrl);
                        // 乡村基电子发票 更新账单数据
                        initDataMapper.updateByInvoice(billId, lvDH, amount, "Y");
                    }
                } else {
                    // 通用
                    if ("".equals(dzfpqqdz)) {
                        LOGGER.info("没有配置总部电子发票请求地址，请正确设置后再重试。");
                        lvNewUrl = "";
                    } else {
                        String bbrqs = DateUtil.format(bbrq, "yyyy-MM-dd");
                        String lvUrlPara = tenancyId + "#" + organId + "#" + bbrqs + "#" + billId + "#" + serviceType + "#" + 0 + "#" + amount;
                        LOGGER.info("开具电子发票：明文APlaintext= " + lvUrlPara + ",AKey = TZXSAASZFP2222I9");
                        lvUrlPara = AESUtil.aesEncodeNew(lvUrlPara, "TZXSAASZFP2222I9");
                        lvUrlPara = Base64.byteArrayToBase64(lvUrlPara.getBytes());
                        LOGGER.info("开具电子发票：密文= " + lvUrlPara); // 记录操作日志
                        lvNewUrl = dzfpqqdz + "/elecinvoice/get?para=" + lvUrlPara;
                        LOGGER.info("开具电子发票：二维码内容= " + lvNewUrl);
                        initDataMapper.updateByInvoice(billId, "", amount, "Y");
                    }
                }
            }

            return lvNewUrl;
        } catch (Exception e) {
            LOGGER.error("创建电子发票报错:", e);
            return "";
        }

    }
    @Override
    public int CancelInvoice(Map<String,Object> data){
        int lvResult = -1;
        try {
            String organId = InitDataListener.organVo.getJgxh() + ""; // 机构id
            String tenancyId = InitDataListener.ggcsMap.get("tenancy_id"); // 发票商户id？
            String billId = data.get("kdzdbh").toString(); // 账单号
            String sfkfp = "N";
            String tssfkfp = InitDataListener.organVo.getTssfkfp();
            String wfsfkfp = InitDataListener.organVo.getTssfkfp();
            Integer wmtype = null==data.get("wmtype")?0:Integer.parseInt(data.get("wmtype").toString());
            sfkfp = tssfkfp;
            if(wmtype==2){
                sfkfp = wfsfkfp;
            }
            String posInvoiceChanel = InitDataListener.ggcsMap.get("POS_INVOICE_CHANEL"); // 开发票渠道类型
            LOGGER.info("开具电子发票,账单号"+billId+"：是否开发票= " + sfkfp + ",类型= " + posInvoiceChanel);

            if ("Y".equals(sfkfp)) {
                if ("1".equals(posInvoiceChanel)) {
                    String dh = data.get("dh").toString();
                    if(dh.trim().equals("")){
                        LOGGER.info("取消发票失败"+billId +",单号为空,取消发票直接返回成功");
                        return  0;
                    }
                    String url = "http://127.0.0.1:8081/BOHInterface/invoice/post"; // 电子发票请求地址（乡村基）
                    // 乡村基
                    if ("".equals(url)) {
                        LOGGER.info("没有配置取消发票地址，请正确设置后再重试。");
                    } else {
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("store_id", StringUtil.getInt(organId, 0));
                        jsonObject.put("tenancy_id", tenancyId);
                        jsonObject.put("type",  "CANCLE_LECTRONIC_INVOICE_NEWPOS");

                        JSONObject jsonTemp = new JSONObject();
                        jsonTemp.put("DH",dh);
                        JSONArray jsonArray =  new JSONArray();
                        jsonArray.add(jsonTemp);
                        jsonObject.put("data",jsonArray);

                        LOGGER.info("url:" + url + "  request  " + jsonObject.toString());
                        String resp = Util.jsonPostRequest(url, jsonObject.toString());
                        LOGGER.info("url:" + url + "  response  " + resp);

                        JSONObject json = JSONObject.fromObject(resp);
                        JSONArray jarry = json.getJSONArray("data");
                        JSONObject jreturntemp = jarry.getJSONObject(0);
                        LOGGER.info("取消电子发票：解析Json，code=" + jreturntemp.get("code")+",msg="+jreturntemp.get("msg"));
                        if(jreturntemp.get("code").equals("0000") || jreturntemp.get("code").equals("0004")){
                            return  0;
                        }
                    }
                } else {
                    String lvNewUrl = "";
                    String dzfpqqdz = InitDataListener.ggcsMap.get("DZFPQQDZ");// 电子发票请求地址（通用）
                    // 通用
                    if("".equals(dzfpqqdz)) {
                        LOGGER.info("没有配置总部电子发票请求地址，请正确设置后再重试。");
                    } else {
                        String bbrqs =  DateUtil.format(data.get("bbrq"),"yyyy-MM-dd");
                        String serviceType = "1";
//                        BigDecimal bd = new BigDecimal(data.get("kfpje").toString());
//                        DecimalFormat df = new DecimalFormat("0.00");
                        String lvUrlPara = "tzx" + "#" + organId + "#" + bbrqs + "#" + billId + "#" + serviceType + "#" + 0 + "#0.00" ;
                        LOGGER.info("取消电子发票：明文APlaintext= " + lvUrlPara + ",AKey = TZXSAASZFP2222I9");
                        lvUrlPara = AESUtil.aesEncodeNew(lvUrlPara, "TZXSAASZFP2222I9");
                        lvUrlPara = Base64.byteArrayToBase64(lvUrlPara.getBytes());
                        LOGGER.info("取消电子发票：密文= " + lvUrlPara); // 记录操作日志
                        lvNewUrl = dzfpqqdz + "/elecinvoice/cancel";
                        String resp = Util.sendGet(lvNewUrl,"para=" + lvUrlPara);
                        LOGGER.info("url:" + lvNewUrl + "  response  " + resp);

                        JSONObject json = JSONObject.fromObject(resp);
                        LOGGER.info("取消电子发票：解析Json，success=" + json.get("success"));
                        if(json.get("success").equals("true")){
                            return  0;
                        }
                    }
                }
            }else{
                LOGGER.info("取消发票失败"+billId +",未开发过,取消发票直接返回成功");
                return  0;
            }

        } catch (Exception e) {
            LOGGER.error("取消电子发票报错:", e);
        }
        return  lvResult;
    }
    public String delBlankStr(String str){
        str = str.replace(" ", "").replaceAll("[\\t\\n\\r]", "");
        return str;
    }
    public static void main(String args[]){
//       String lvUrlPara = "tzx#6#2019-12-24#992000000869#1#0#12.00";
//       lvUrlPara = AESUtil.aesEncodeNew(lvUrlPara, "TZXSAASZFP2222I9");
//       lvUrlPara = Base64.byteArrayToBase64(lvUrlPara.getBytes());
//
//       System.out.println(lvUrlPara);


       String lvEnStr = "eGlhbmdjaiM0NjgxIzIwMjAxMjMxIzAyMDAwMDEzMjc0OEAxMjMwNTAjMSMwLjA2IzIyLjAwIzlmMGQ=";
       String lvStr = new String(Base64.base64ToByteArray(lvEnStr));
        System.out.println(lvStr);
//       lvStr = "Jg5z475UI3d3vAFHgyUUgpY+D+wAyydJL5JtEzBeZDa96k+L+ZST5GmO8XR2EbM+";
//       lvStr =  AESUtil.aesDecryptNew(lvStr,"TZXSAASZFP2222I9");
//       System.out.println(lvStr);
    }
}
