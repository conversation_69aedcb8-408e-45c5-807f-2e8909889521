package com.tzx.ecoserver.common;

import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019-10-26.
 * 用于通用标识锁
 */
@Service
public class LockerByIdent {
    private  Map<String, Integer> identList = new ConcurrentHashMap<String, Integer>();

    public  boolean IsLock(String ident){
        return  identList.containsKey(ident);
    }
    public Boolean Lock(String ident, Integer status){
        if(IsLock(ident)){
            return  false;
        };
        boolean hasGetLock = false;
        synchronized (this){
            if(IsLock(ident)){
                return  false;
            };
            identList.put(ident, status);
            hasGetLock = true;
        }
        if(!hasGetLock){
            return  false;
        }
        return IsLock(ident);
    }
    public Boolean UnLock(String ident){
        if(!IsLock(ident)){
            return  true;
        };
        boolean hasGetLock = false;
        synchronized (this){
            if(!IsLock(ident)){
                return  true;
            };
            identList.remove(ident);
            hasGetLock = true;
        }
        return !IsLock(ident);
    }

    public static void main(String[] args){
        Map<String, Integer> id = new ConcurrentHashMap<String, Integer>();
        id.put("123",1);
        id.put("123",1);
        System.out.println(id.toString());

    }
}
