package com.tzx.config;

import com.tzx.receiver.common.utils.SpringContextHolder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

/**
 * <AUTHOR>
 * @Date 2019-04-11
 * @Descption
 **/
@Configuration
public class SpringContextHolderConfiguration {

    /**
     * 获取bean工具注入
     *
     * @return
     */
    @Lazy(true)
    @Bean(name = "springContextHolder")
    public SpringContextHolder registrySpringContextHolder() {
        SpringContextHolder springContextHolder =  new SpringContextHolder();
//        springContextHolder.setApplicationContext();
        return springContextHolder;
    }
}
