package com.tzx.publics.util;

import net.sf.json.JSONObject;

import java.sql.Timestamp;
import java.util.Date;
import java.util.Map;

/**
 *
 * <AUTHOR> 2015年7月21日-上午11:30:34
 */
public class ParamUtil
{
	/**
	 * 获取map里的参数值，如果isExce为true，则抛出自定义异常
	 * 
	 * @param map
	 *            Map<String,Object>
	 * @param paramName
	 *            参数名称，如：bill_num
	 * @param isExce
	 *            boolena true：抛异常，false：不抛异常
	 * @param errorCode
	 *            自定义的异常 PosErrorCode.NOT_NULL_BILL_NUM(账单编号不能为空)
	 * @return 返回字符串类型
	 */
	public static String getStringValue(Map<String, Object> map, String paramName, boolean isExce, ErrorCode errorCode)
	{
		String result = null;
		if (Tools.isNullOrEmpty(map.get(paramName)) == false)
		{
			result = (map.get(paramName).toString()).trim();

		}
		else
		{
			if (isExce)
			{
				throw new SystemException(errorCode);
			}
		}
		return result;
	}

	/**
	 * @param object
	 * @param paramName
	 * @param isExce
	 * @param errorCode
	 * @return
	 */
	public static String getStringValueByObject(JSONObject object, String paramName, boolean isExce, ErrorCode errorCode)
	{
		String result = null;
		if (Tools.isNullOrEmpty(object.get(paramName)) == false)
		{
			result = (object.get(paramName).toString()).trim();
			if("null".equals(result))
			{
				result = null;
			}
		}
		else
		{
			if (isExce)
			{
				throw new SystemException(errorCode);
			}
		}
		return result;
	}
	
	
	public static String getStringValueForNullByObject(JSONObject object, String paramName)
	{
		String result = "";
		if (Tools.isNullOrEmpty(object.get(paramName)) == false)
		{
			result = (object.get(paramName).toString()).trim();
			if("null".equals(result))
			{
				result = "";
			}
		}

		return result;
	}

	/**
	 * @param object
	 * @param paramName
	 * @return
	 */
	public static String getStringValueByObject(JSONObject object, String paramName)
	{
		return getStringValueByObject(object, paramName, false, null);
	}

	/**
	 * 获取map里的参数值，如果isExce为true，则抛出自定义异常
	 * 
	 * @param map
	 *            Map<String,Object>
	 * @param paramName
	 *            参数名称，如：shift_id
	 * @param isExce
	 *            boolena true：抛异常，false：不抛异常
	 * @param errorCode
	 *            自定义的异常 PosErrorCode.NOT_NULL_SHIFT_ID(班次不能为空)
	 * @return 返回Integer类型
	 */
	public static Integer getIntegerValue(Map<String, Object> map, String paramName, boolean isExce, ErrorCode errorCode)
	{
		Integer result = null;
		if (Tools.isNullOrEmpty(map.get(paramName)) == false)
		{
			result = Integer.parseInt(map.get(paramName).toString());
		}
		else
		{
			if (isExce)
			{
				throw new SystemException(errorCode);
			}
		}
		return result;
	}

	/**
	 * @param jsonObject
	 * @param paramName
	 * @param isExce
	 * @param errorCode
	 * @return
	 */
	public static Integer getIntegerValueByObject(JSONObject jsonObject, String paramName, boolean isExce, ErrorCode errorCode)
	{
		Integer result = null;
		if (jsonObject.containsKey(paramName) && Tools.isNullOrEmpty(jsonObject.get(paramName)) == false && "null".equals(jsonObject.get(paramName).toString()) == false)
		{
//			result = Integer.parseInt(jsonObject.get(paramName).toString());
			result = jsonObject.optInt(paramName);
		}
		else
		{
			if (isExce)
			{
				throw new SystemException(errorCode);
			}
		}
		return result;
	}

	/**
	 * @param jsonObject
	 * @param paramName
	 * @return
	 */
	public static Integer getIntegerValueByObject(JSONObject jsonObject, String paramName)
	{
		return getIntegerValueByObject(jsonObject, paramName, false, null);
	}

	/**
	 * 获取map里的参数值，如果isExce为true，则抛出自定义异常
	 * 
	 * @param map
	 *            Map<String,Object>
	 * @param paramName
	 *            参数名称，如：price_amount
	 * @param isExce
	 *            boolena true：抛异常，false：不抛异常
	 * @param errorCode
	 *            自定义的异常 PosErrorCode.NOT_NULL_PRICE_AMOUNT(商品价格不能为空)
	 * @return 返回Double类型
	 */
	public static Double getDoubleValue(Map<String, Object> map, String paramName, boolean isExce, ErrorCode errorCode)
	{
		Double result = 0d;
		if (map.containsKey(paramName) && Tools.isNullOrEmpty(map.get(paramName)) == false)
		{
			result = Double.parseDouble(map.get(paramName).toString());
			if (null == result || result.isNaN())
			{
				result = 0d;
			}
		}
		else
		{
			if (isExce)
			{
				throw new SystemException(errorCode);
			}
		}
		return result;
	}

	/**
	 * @param object
	 * @param paramName
	 * @param isExce
	 * @param errorCode
	 * @return
	 */
	public static Double getDoubleValueByObject(JSONObject object, String paramName, boolean isExce, ErrorCode errorCode)
	{
		Double result = 0d;
		if (object.containsKey(paramName) && Tools.isNullOrEmpty(object.get(paramName)) == false && "null".equals(object.opt(paramName).toString()) == false)
		{
			result = Double.parseDouble(object.get(paramName).toString());
			if (null == result || result.isNaN())
			{
				result = 0d;
			}
		}
		else
		{
			if (isExce)
			{
				throw new SystemException(errorCode);
			}
		}
		return result;
	}

	public static Double getDoubleValueByObject(JSONObject object, String paramName)
	{
		return getDoubleValueByObject(object, paramName, false, null);
	}

	/**
	 * @param map
	 * @param paramName
	 * @param isExce
	 * @param errorCode
	 * @return
	 */
	public static Date getDateValue(Map<String, Object> map, String paramName, boolean isExce, ErrorCode errorCode)
	{
		Object obj = map.get(paramName);
		Date result = null;
		if (Tools.isNullOrEmpty(obj) == false)
		{
			String strDate = obj.toString();
			String[] date = null;
			if (strDate.contains("/"))
			{
				date = strDate.split("/");
			}
			else if (strDate.contains("-"))
			{
				date = strDate.split("-");
			}
			if (date[0].length() < 4)
			{
				date[0] = ("20" + date[0]).substring(0, 4);
			}
			if (date[1].length() < 2)
			{
				date[1] = ("0" + date[1]).substring(0, 2);
			}
			if (date[2].length() < 2)
			{
				date[2] = ("0" + date[2]).substring(0, 2);
			}
			String ndate = date[0] + "-" + date[1] + "-" + date[2];
			result = DateUtil.parseDate(ndate);
		}
		else
		{
			if (isExce)
			{
				throw new SystemException(errorCode);
			}
		}

		return result;
	}
	
	public static Date getDateValueByObject(JSONObject object, String paramName, boolean isExce, ErrorCode errorCode)
	{
		Date result = null;
		if (object.containsKey(paramName) && Tools.isNullOrEmpty(object.get(paramName)) == false)
		{
			result = DateUtil.parseDate(object.get(paramName).toString());
		}
		else
		{
			if (isExce)
			{
				throw new SystemException(errorCode);
			}
		}
		return result;
	}
	
	public static Date getDateValueByObject(JSONObject object, String paramName)
	{
		return getDateValueByObject(object, paramName, false, null);
	}

	/**
	 * @param map
	 * @param paramName
	 * @param isExce
	 * @param errorCode
	 * @return
	 */
	public static Timestamp getTimestampValue(Map<String, Object> map, String paramName, boolean isExce, ErrorCode errorCode)
	{
		Timestamp result = null;
		if (Tools.isNullOrEmpty(map.get(paramName)) == false)
		{
			result = DateUtil.parseTimestamp(map.get(paramName).toString());
		}
		else
		{
			if (isExce)
			{
				throw new SystemException(errorCode);
			}
		}

		return result;
	}
	
	public static Timestamp getTimestampValueByObject(JSONObject object, String paramName, boolean isExce, ErrorCode errorCode)
	{
		Timestamp result = null;
		if (object.containsKey(paramName) && Tools.isNullOrEmpty(object.get(paramName)) == false)
		{
			result = DateUtil.parseTimestamp(object.get(paramName).toString());
		}
		else
		{
			if (isExce)
			{
				throw new SystemException(errorCode);
			}
		}
		return result;
	}
	
	public static Timestamp getTimestampValueByObject(JSONObject object, String paramName)
	{
		return getTimestampValueByObject(object, paramName, false, null);
	}

	/**
	 * @param param
	 * @param paramName
	 * @return
	 */
	@SuppressWarnings("deprecation")
	public static String getDateStringValue(JSONObject param, String paramName)
	{
		String result = null;
		String strDate = param.optString(paramName);
		if (Tools.hv(strDate))
		{
			String[] date = null;
			if (strDate.contains("/"))
			{
				date = strDate.split("/");
			}
			else if (strDate.contains("-"))
			{
				date = strDate.split("-");
			}

			if (date[0].length() < 4)
			{
				String year2 = String.valueOf(((new Date()).getYear() + 1900) / 100);
				date[0] = (year2 + date[0]).substring(0, 4);
			}

			if (date[1].length() < 2)
			{
				date[1] = ("0" + date[1]).substring(0, 2);
			}

			if (date[2].length() < 2)
			{
				date[2] = ("0" + date[2]).substring(0, 2);
			}

			result = date[0] + "-" + date[1] + "-" + date[2];
		}
		return result;
	}

	/**
	 * 为打印设置参数
	 * 
	 * @param tenancyid
	 * @param billno
	 * @param print_format
	 * @param rwid
	 * @param printer_id
	 * @param organId
	 * @return
	 */
	public static JSONObject setDishParam(String tenancyid, String billno, String printCode, String printFormat, Integer rwid, Integer printer_id, Integer organId, String print_property)
	{
		JSONObject obj = new JSONObject();
		obj.put("printer_id", printer_id);
		obj.put("print_code", printCode);
		obj.put("print_format", printFormat);
		obj.put("rwid", rwid);
		obj.put("bill_num", billno);
		obj.put("tenancy_id", tenancyid);
		obj.put("store_id", organId);
		obj.put("print_property", print_property);
		return obj;
	}

	/**
	 * 为模板打印设置参数
	 * 
	 * @param tenancyid
	 * @param billno
	 * @param print_code
	 * @param printer_id
	 * @param organId
	 * @return
	 */
	public static JSONObject setTempParam(String mode, String tenancyid, String billno, String print_code, Integer printer_id, Integer organId, String paymentUrl)
	{
		JSONObject obj = new JSONObject();
		obj.put("mode", mode);
		obj.put("printer_id", printer_id);
		obj.put("print_code", print_code); // 模板对应的id
		obj.put("bill_num", billno);
		obj.put("tenancy_id", tenancyid);
		obj.put("store_id", organId);
		obj.put("payment_url", paymentUrl);
		return obj;
	}

}
