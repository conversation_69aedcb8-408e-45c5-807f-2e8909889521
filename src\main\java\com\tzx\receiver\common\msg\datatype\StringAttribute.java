package com.tzx.receiver.common.msg.datatype;


/**
 * 字符串数据类型的类型属性类。
 * 
 * <AUTHOR>
 * 
 */
public class StringAttribute extends DataTypeAttribute {

	private Boolean fixed;

	private int size;

	public StringAttribute() {
		size = DataTypeUnit.DefaultSize;
	}

	public Boolean getFixed() {
		return fixed;
	}

	public void setFixed(Boolean fixed) {
		this.fixed = fixed;
	}

	/**
	 * 获取字段长度。
	 * 
	 * @return 字段长度的值。
	 */
	public int getSize() {
		return size;
	}

	/**
	 * 设置字段长度。
	 * 
	 * @param size
	 *            字段长度的值。
	 */

	public void setSize(int size) {
		this.size = size;
	}

	public String toString() {
		return "StringAttribute(size=" + size + ")";
	}

}
