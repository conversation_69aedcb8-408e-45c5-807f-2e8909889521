package com.tzx.fmcgbi.rest.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.tzx.fmcgbi.common.FmcgbiData;
import com.tzx.fmcgbi.rest.mapper.FmcgbiCalcuFloatAmtMapper;
import com.tzx.fmcgbi.rest.service.IFmcgbiCalcuFloatAmtService;
import com.tzx.fmcgbi.rest.vo.ProdVo;
import com.tzx.publics.util.ArithUtil;
import com.tzx.publics.util.GsonUtil;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

@Service
public class FmcgbiCalcuFloatAmtServiceImpl implements IFmcgbiCalcuFloatAmtService {
	private final static Logger LOGGER = LoggerFactory.getLogger(FmcgbiCalcuFloatAmtServiceImpl.class);

	@Autowired
	private FmcgbiCalcuFloatAmtMapper calcuFloatAmtMapper;

	/**
	 * 计算优惠方案，只计算
	 */
	@Transactional
	public FmcgbiData calcuFloatAmt(JSONObject requestJson) {
		LOGGER.info("1.计算优惠方案");
		FmcgbiData data = new FmcgbiData();
		JSONObject responseJson = new JSONObject();
		double payAmt = 0;
		double favorAmt = 0;
		// 菜品信息
		JSONArray detailList = requestJson.optJSONArray("DetailList");
		
		// 先循环一次，拿到所有已点菜品编码
		List<String> itemCodeList = new ArrayList<String>();
		for (int i = 0; i < detailList.size(); i++) {
			JSONObject item = detailList.getJSONObject(i);
			itemCodeList.add(item.optString("ProdNo", ""));
		}
		LOGGER.info("1.计算优惠方案-itemCodeList：" + GsonUtil.GsonString(itemCodeList));
		// 根据得到的菜品编码获取菜品价格
		List<ProdVo> prodList = new ArrayList<ProdVo>();
		if (itemCodeList.size() > 0) {
			prodList = calcuFloatAmtMapper.queryItemPrice(itemCodeList);
		}
		LOGGER.info("1.计算优惠方案-prodList：" + GsonUtil.GsonString(prodList));
		// 以编号为key转换为map
		Map<String, Double> priceMap = new HashMap<String, Double>();
		for (ProdVo item : prodList) {
			priceMap.put(item.getProdNo(), Double.parseDouble(item.getPrice()));
		}
		LOGGER.info("1.计算优惠方案-priceMap：" + GsonUtil.GsonString(priceMap));
		// 在循环一次传入数据，根据菜品数量计算出账单应付金额
		for (int i = 0; i < detailList.size(); i++) {
			JSONObject item = detailList.getJSONObject(i);
			String itemCode = item.optString("ProdNo", "");
			double itemNum = item.optDouble("Qty", 0);
			double itemPrice = ArithUtil.mul(priceMap.get(itemCode), itemNum);
			payAmt = ArithUtil.add(payAmt, itemPrice);
		}
		LOGGER.info("1.计算优惠方案-payAmt：" + payAmt);
		// 计算优惠，暂时没有加入真正的优惠，现在计算优惠仅为自助机器价格与后台真实价格的差额
		favorAmt = ArithUtil.sub(requestJson.optDouble("OrderAmt", 0), payAmt);
		LOGGER.info("1.计算优惠方案-favorAmt：" + favorAmt);
		responseJson.put("status", "0");
		responseJson.put("msg", "计算优惠方案成功");
		responseJson.put("FavorAmt", favorAmt + "");// 折扣金额
		responseJson.put("PayAmt", payAmt + "");// 应付金额
		responseJson.put("PrefName", "");// 商品活动
		data.setCode(0);
		data.setMsg("计算优惠方案成功");
		data.setData(responseJson);
		LOGGER.info("1.计算优惠方案成功");
		return data;
	}

}
