package com.tzx.miniapp.rest.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tzx.miniapp.rest.mapper.MiniAppShopBaseInfoMapper;
import com.tzx.miniapp.rest.service.IData;
import com.tzx.miniapp.rest.vo.Shops;
import com.tzx.miniapp.rest.vo.ZsDishInfoEntity;
import com.tzx.miniapp.rest.vo.ZsDishInfoVo;
import com.tzx.miniapp.rest.vo.ZsDishPackDetailsEntity;
import com.tzx.miniapp.rest.vo.ZsDishTypeInfoEntity;
import com.tzx.miniapp.rest.vo.ZsDishUnitEntity;
import com.tzx.miniapp.rest.vo.ZsDishVo;
import com.tzx.publics.util.ArithUtil;

/**
 * MiniAppZsSyncDish
 * 
 * <AUTHOR> 2019年06月17日
 */
public class MiniAppZsSyncDish extends IData<ZsDishVo> {

	MiniAppShopBaseInfoMapper shopBaseInfoMapper;
	String typeStr = "CMSX_DP";

	public MiniAppZsSyncDish(MiniAppShopBaseInfoMapper shopBaseInfoMapper, String typeStr) {
		this.shopBaseInfoMapper = shopBaseInfoMapper;
		this.typeStr = typeStr;
		
	}
	
	@Override
	public String getParams() throws JsonProcessingException {
		ZsDishVo dv = new ZsDishVo();
		Shops shops = shopBaseInfoMapper.findShopsData();
		dv.setDataVersion(System.currentTimeMillis() + "");
		dv.setStoreId(shops.getSid() + "");
		if ("CMSX_DP".equals(typeStr)) { // 如果是菜品接口，才同步类别，否则不同步
			dv.setCategoryList(buildType(shops.getSid() + ""));
		}
		List<ZsDishInfoEntity> dishList = shopBaseInfoMapper.findZsDishData(shops.getSid() + "", typeStr);
		for (ZsDishInfoEntity dish : dishList) {
			// 规格数据，没有多规格，直接使用菜品数据组装
			List<ZsDishUnitEntity> unitList = new ArrayList<>();
			ZsDishUnitEntity unit1 = new ZsDishUnitEntity();
			unit1.setUnitId(dish.getDishId());
			unit1.setMerUnitId(dish.getMerDishId());
			unit1.setUnitName(dish.getDwname());
			unit1.setIsDefault(1);
			unit1.setStandardPrice((long) ArithUtil.mul(Double.parseDouble(dish.getPrice()), 100));
			unit1.setValidState(1);
			unit1.setCountRate(new BigDecimal("1"));
			unitList.add(unit1);
			dish.setUnitList(unitList);
			if ("CMSX_TC".equals(typeStr)) { // 套餐同步接口，才同步套餐明细
				dish.setPackDetailsList(buildPackDetails(dish.getMerDishId(), shops.getSid() + ""));
			}
			dish.setPrice(null);
			dish.setDwname(null);
		}
		
		dv.setDishList(dishList);

		return buildReturn(dv, 2, shopBaseInfoMapper);

	}

	private List<ZsDishTypeInfoEntity> buildType(String sid) {
		List<ZsDishTypeInfoEntity> typeList = shopBaseInfoMapper.findZsDishKindsData(sid);
		// 先增加一个默认分类，作为所有类别的父类别
//		ZsDishTypeInfoEntity type = new ZsDishTypeInfoEntity();
//		type.setCategoryId(sid + "_-1");
//		type.setMerCategoryId("-1");
//		type.setParentCategoryId("");
//		type.setName("默认分类");
//		type.setStatus(0);
//		typeList.add(type);
		return typeList;
	}
	
	private List<ZsDishPackDetailsEntity> buildPackDetails(String xmid, String sid) {
		List<ZsDishPackDetailsEntity> typeList = shopBaseInfoMapper.findZsDishPackDetailsData(sid, Integer.parseInt(xmid));
		return typeList;
	}

	public String getUrl() {
		if ("CMSX_DP".equals(typeStr)) {
			return "store/syncStoreProduct";
		} else {
			return "store/syncStorePkgProduct";
		}

	}

}
