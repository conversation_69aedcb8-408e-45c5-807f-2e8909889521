package com.tzx.commapi.rest.service.impl;

import com.tzx.commapi.rest.mapper.CommApiMapper;
import com.tzx.commapi.rest.mapper.XinShangTieApiMapper;
import com.tzx.commapi.rest.service.IXinShangTieApiService;
import com.tzx.commapi.rest.vo.TqThirdTempOrder;
import com.tzx.commapi.rest.vo.Vq_JPWY_Zdk;
import com.tzx.commapi.rest.vo.XstApiData;
import com.tzx.publics.listener.InitDataListener;
import com.tzx.publics.util.JsonUtils;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;


@Service
public class XinShangTieApiService implements IXinShangTieApiService {
    private final static Logger LOGGER = LoggerFactory.getLogger(XinShangTieApiService.class);
    @Autowired
    private XinShangTieApiMapper xinShangTieApiMapper;
    @Autowired
    private CommApiMapper commApiMapper;

    @Override
    public void getSaleOrders(XstApiData data, JSONObject jsondata) {
        try {
            String startTime = jsondata.optString("startTime");
            int count = jsondata.optInt("count", 0);
            List<TqThirdTempOrder> list = xinShangTieApiMapper.getTqThirdTempOrder("WUYE3", startTime, count);
            JSONArray jsonArray = createData(list);
            data.setCode(0);
            data.setCount(jsonArray.size());
            data.setData(jsonArray);
            data.setMsg("成功");
        } catch (Exception e) {
            data.setCode(1);
            data.setCount(0);
            data.setMsg("系统异常！");
            LOGGER.error(e.getMessage(), e);
        }
    }

    private JSONArray createData(List<TqThirdTempOrder> list) {
        JSONArray jsonArray = new JSONArray();
        JSONObject wyObj = InitDataListener.wyObj;
        String stationname = wyObj.optString("STATIONNAME");
        String stationid = wyObj.optString("STATIONID");
        String shopname = wyObj.optString("SHOPNAME");
        String shopno = wyObj.optString("SHOPNO");
        String branch = wyObj.optString("BRANCH");

        for (TqThirdTempOrder param : list) {
            List<Vq_JPWY_Zdk> listZdk = commApiMapper.getVqJpwyZdk(param.getBillnum(), param.getReportdate());
            if (null == listZdk || listZdk.size() == 0) {
                LOGGER.error("getSaleOrders 失败 {}", "未获取到账单信息");
                continue;
            }
            Vq_JPWY_Zdk vq_zdk = listZdk.get(0);
            List<Map<String, Object>> listZdmxk = commApiMapper.getCYZdmxList(param.getBillnum(), param.getReportdate());
            List<JSONObject> listJsonZdmxk = JsonUtils.mapToJsonList(listZdmxk);
            if (null == listJsonZdmxk) {
                LOGGER.error("getSaleOrders 失败 {}", "未获取到账单明细信息");
                continue;
            }
            List<Map<String, Object>> listFklsk = commApiMapper.getFXFklsList(param.getBillnum());
            List<JSONObject> listJsonFkls = JsonUtils.mapToJsonList(listFklsk);
            if (null == listJsonFkls) {
                LOGGER.error("getSaleOrders 失败 {}", "未获取到付款明细信息,或为0元账单；");
                continue;
            }

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("STATIONNAME", stationname);
            jsonObject.put("STATIONID", stationid);
            jsonObject.put("SHOPNAME", shopname);
            jsonObject.put("SHOPNO", shopno);
            jsonObject.put("BRANCH", branch);

            jsonObject.put("BILLNO",  param.getOrderno());

            JSONArray billGoods = new JSONArray();
            for (JSONObject zdmxObj : listJsonZdmxk) {
                JSONObject goods = new JSONObject();
                goods.put("GOODSTYPE", "");
                goods.put("GOODSNAME", zdmxObj.optString("cmmc1", ""));
                goods.put("GOODSPRICE", zdmxObj.optString("sjje", "0"));
                goods.put("GOODSNUMBER", "1");
                goods.put("GOODSAMOUNT", zdmxObj.optDouble("sjje", 0));
                goods.put("SPECIFICATION", "");
                billGoods.add(goods);
            }
            jsonObject.put("BILLGOODS", billGoods);

            jsonObject.put("DISCOUNTS", "0");
            jsonObject.put("BILLALLPRICES", vq_zdk.getFkje());
            jsonObject.put("OVERCHARGED", 0);
            jsonObject.put("BILLTIME", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(vq_zdk.getJzsj()));
            jsonObject.put("CREATETIME", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(param.getCreatetime()));

            String payment = "其他";
            if(listJsonFkls.size() == 1){
                for (JSONObject fklsObj : listJsonFkls) {
                    String yl3 = fklsObj.optString("yl3", "");
                    String fklxsx = fklsObj.optString("fklxsx", "");
                    if ("ERP_FKFS_WX".equals(yl3)) {
                        payment = "微信";
                    } else if ("ERP_FKFS_ZFB".equals(yl3)) {
                        payment = "支付宝";
                    } else if ("FKSX_XJ".equals(fklxsx)) {
                        payment = "现金";
                    }
                }
            }
            jsonObject.put("PAYMENT", payment);
            if ("ZDZT_QJ".equals(vq_zdk.getZdzt())) {
                jsonObject.put("TRANSTYPE", "退货");
            } else {
                jsonObject.put("TRANSTYPE", "销售");
            }
            jsonObject.put("SOURCETYPE", "POS机");
            jsonObject.put("SOURCENO", vq_zdk.getKtskjh());

            jsonArray.add(jsonObject);
        }
        return jsonArray;
    }


}
