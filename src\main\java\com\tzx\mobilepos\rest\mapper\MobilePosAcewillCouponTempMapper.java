package com.tzx.mobilepos.rest.mapper;

import com.tzx.mobilepos.rest.model.TqAcewilCouponCache;
import com.tzx.mobilepos.rest.vo.AcewillProductsVo;
import com.tzx.mobilepos.rest.vo.PayMentVo;
import com.tzx.mobilepos.rest.vo.WdDishVo;
import com.tzx.publics.base.MyMapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2019-3-27
 */

public interface MobilePosAcewillCouponTempMapper extends MyMapper<TqAcewilCouponCache> {

	public int getFkfsid(@Param("fkfsbh") String fkfsbh);

	public WdDishVo getWdDish(@Param("zdbh") String zdbh, @Param("cmbh") String cmbh);

	public double getZdje(@Param("zdbh") String zdbh);

	public int delTwct(@Param("zdbh") String zdbh);

	public int delTmi(@Param("zdbh") String zdbh);

	public int delFkls(@Param("zdbh") String zdbh, @Param("fklxsx") String fklxsx);

	public TqAcewilCouponCache getCouponByCode(@Param("zdbh") String zdbh, @Param("couponcode") String couponcode);

	public List<Map<String, Object>> getCouponUseCount(@Param("zdbh") String zdbh, @Param("templateid") String templateid, @Param("t") String t, @Param("datatype") String datatype);

	public double getCouponPrice(@Param("zdbh") String zdbh);

	public int updateUsaByCardcode(@Param("zdbh") String zdbh, @Param("cardcode") String cardcode, @Param("usable") int usable);

	public List<TqAcewilCouponCache> getAcewillCoupons(@Param("zdbh") String zdbh, @Param("cardcode") String cardcode);

	public int updateRwid(@Param("zdbh") String zdbh, @Param("couponcode") String couponcode, @Param("rwid") int rwid, @Param("cmbh") String cmbh);

	public List<Integer> getRefundRwid(@Param("zdbh") String zdbh, @Param("yhfsid") int yhfsid);

	public List<TqAcewilCouponCache> getCouponByUseok(@Param("zdbh") String zdbh, @Param("useok") int useok);

	public double findZdAmount(@Param("zdbh") String zdbh);

	public double findPaymentAmount(@Param("zdbh") String zdbh);

	public List<AcewillProductsVo> findProducts(@Param("zdbh") String zdbh);

	public int delDsfls(@Param("zdbh") String zdbh, @Param("yhfs") String yhfs);

	public int insertTqZddsflsk(@Param("skjh") String skjh, @Param("kdzdbh") String kdzdbh,
								@Param("kdbbrq") Date kdbbrq, @Param("czsj") Date czsj, @Param("yhfs") String yhfs,
								@Param("qrcode") String qrcode, @Param("czlx") String czlx, @Param("scbj") int scbj, @Param("yl3") String yl3, @Param("remark") String remark);

	public Map<String, String> findZddsflsk(@Param("zdbh") String zdbh, @Param("yhfs") String yhfs, @Param("yl3") String yl3);

	public int updateZddsflsk(@Param("zdbh") String zdbh, @Param("yl2") String yl2, @Param("yl3") String yl3, @Param("remark") String remark, @Param("yhfs") String yhfs);

	public int updateUsaByUseok(@Param("zdbh") String zdbh, @Param("couponcode") String couponcode, @Param("useok") int useok);

	public PayMentVo checkFkls(@Param("zdbh") String zdbh, @Param("fkfsbh") String fkfsbh);

	public String getDiscountRwid(@Param("zdbh") String zdbh, @Param("fklsid") int fklsid);

	public int getIsVipPriceCount(@Param("kdzdbh") String kdzdbh);

	public List<WdDishVo> getWdDishDiscount(@Param("zdbh") String zdbh, @Param("products") List<String> products, @Param("productsExt") List<String> productsExt);

	public int insertWdAndYh(@Param("zdbh") String zdbh, @Param("products") List<String> products, @Param("productsExt") List<String> productsExt, @Param("yhfsid") int yhfsid);

	public double getCountCouponPrice(@Param("zdbh") String zdbh);

	public int getWdAndYhSize(@Param("zdbh") String zdbh);

	public int insertAcewilDealdetails(@Param("cardno") String cardno, @Param("billno") String billno, @Param("amount") double amount, @Param("balance") double balance, @Param("credit") double credit, @Param("bbrq") Date bbrq, @Param("fwyh") String fwyh, @Param("skjh") String skjh, @Param("canuse") int canuse, @Param("dtl") String dtl);

	public int delAcewilDealdetails(@Param("zdbh") String zdbh);

	double getMemberPromotionAmount(@Param("kdzdbh") String kdzdbh);

	int updateTqZdkAfterShare(@Param("kdzdbh") String kdzdbh);

	Map<String, BigDecimal> selectFkjeAndSjjeByKzdzbh(@Param("kdzdbh") String kdzdbh);
}