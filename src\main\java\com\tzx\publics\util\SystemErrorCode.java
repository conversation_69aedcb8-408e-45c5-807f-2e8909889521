package com.tzx.publics.util;

/**
 * 系统错误，编码10000开始
 * 
 * <AUTHOR>
 * 
 */
public enum SystemErrorCode implements ErrorCode
{

	/**
	 * 系统内部错误
	 */
	SYSTEM_ERROR(101,"系统内部错误"),
	TABLE_OCCUPY(102,"桌位被占用"),
	;
   
	private final int	Code;
	private final String	Message;

	private SystemErrorCode(int Code,String Message)
	{
		this.Code = Code;
		this.Message=Message;
	}

	public int getCode()
	{
		return Code;
	}

	@Override
	public String getMessage()
	{
		return Message;
	}

	
}
