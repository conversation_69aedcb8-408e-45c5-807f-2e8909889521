package com.tzx.mobilepos.rest.service;


import com.tzx.mobilepos.common.Data;

public interface IMobilePosLoginService {
	 public void login(Data data, Data result);

	 public void logout(Data data, Data result);

	 public void updateLoginShiftState(Data data, Data result);
	
	 /**
	* @Description: 修改密码
	* @param @param data
	* @param @param result
	* @param @throws Exception
	* @return void
	* @throws
	* <AUTHOR>
	* @email  <EMAIL>
	* @date 2018-12-3
	*/
	public void updatePassword(Data data, Data result) ;
		
		
}
