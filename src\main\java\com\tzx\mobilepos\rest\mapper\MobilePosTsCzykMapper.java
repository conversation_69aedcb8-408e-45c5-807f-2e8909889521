package com.tzx.mobilepos.rest.mapper;

import com.tzx.publics.base.MyMapper;
import com.tzx.mobilepos.rest.model.TsCzyk;
import org.apache.ibatis.annotations.Param;

/**
 *
 * <AUTHOR>
 * @since 2018-05-15
 */

public interface MobilePosTsCzykMapper extends MyMapper<TsCzyk> {

	public TsCzyk findLogin(@Param("czybh") String czybh, @Param("czymm") String czymm);
	
	public TsCzyk findByCzybh(@Param("czybh") String czybh);

	public int updatePassword(@Param("czybh") String czybh, @Param("xczymm") String xczymm);
	
	public int updatePasswordXf(@Param("czybh") String czybh, @Param("xczymm") String xczymm);

}