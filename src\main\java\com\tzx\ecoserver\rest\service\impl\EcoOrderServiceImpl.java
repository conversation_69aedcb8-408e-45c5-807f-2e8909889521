package com.tzx.ecoserver.rest.service.impl;

import com.alibaba.druid.util.Base64;
import com.tzx.commapi.rest.service.ISaleOutApiService;
import com.tzx.commapi.rest.vo.CommApiData;
import com.tzx.commapi.rest.vo.ReqParam;
import com.tzx.ecoserver.common.Constant;
import com.tzx.ecoserver.common.EcoException;
import com.tzx.ecoserver.common.EcoRepData;
import com.tzx.ecoserver.rest.mapper.EcoOrderMapper;
import com.tzx.ecoserver.rest.service.IEcoOrderService;
import com.tzx.ecoserver.rest.vo.BtTcSelectMx;
import com.tzx.ecoserver.rest.vo.EcoBtPayments;
import com.tzx.ecoserver.rest.vo.EcoBtYdd;
import com.tzx.ecoserver.rest.vo.EcoBtYdxm1;
import com.tzx.ecoserver.rest.vo.EcoBtYdxm2;
import com.tzx.ecoserver.rest.vo.EcoErrorOrderdiscount;
import com.tzx.ecoserver.rest.vo.EcoErrorYdd;
import com.tzx.ecoserver.rest.vo.EcoErrorYdxm1;
import com.tzx.ecoserver.rest.vo.EcoErrorYdxm2;
import com.tzx.ecoserver.rest.vo.EcoOrderdiscount;
import com.tzx.miniapp.rest.mapper.MiniAppFirstPayMapper;
import com.tzx.miniapp.rest.mapper.MiniAppOrderPrecheckMapper;
import com.tzx.miniapp.rest.mapper.MiniAppShopStatusMapper;
import com.tzx.miniapp.rest.model.TqJtztk;
import com.tzx.miniapp.rest.model.TsCmk;
import com.tzx.miniapp.rest.model.TsGgcsk;
import com.tzx.miniapp.rest.model.TsYhfssdk;
import com.tzx.miniapp.rest.vo.BtYdxm2;
import com.tzx.miniapp.rest.vo.TqYyddylsk;
import com.tzx.publics.common.BillNoData;
import com.tzx.publics.listener.InitDataListener;
import com.tzx.publics.service.IInVoiceService;
import com.tzx.publics.util.ArithUtil;
import com.tzx.publics.util.DatagramUtil;
import com.tzx.publics.util.DateUtil;
import com.tzx.publics.util.GsonUtil;
import com.tzx.publics.util.InvitationCodeUtils;
import com.tzx.publics.util.NumberHelper;
import com.tzx.publics.util.PosPrintJna;
import com.tzx.publics.util.PosPrintKichenJna;
import com.tzx.publics.util.PropertiesUtil;
import com.tzx.publics.util.StringFilterUtil;
import com.tzx.publics.util.Util;
import com.tzx.receiver.common.upload.UploadGloVar;
import com.tzx.receiver.common.utils.DBUtils;
import com.tzx.receiver.entity.msg.EcoTypeDic;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by Zhouxh on 2019-10-26.
 */

@Service
public class EcoOrderServiceImpl implements IEcoOrderService {
    private final static Logger LOGGER = LoggerFactory.getLogger(EcoOrderServiceImpl.class);
    // 门店状态查询接口注入对象
    @Autowired
    private MiniAppShopStatusMapper shopStatusMapper;
    // ECO 接单相关接口
    @Autowired
    private EcoOrderMapper ecoOrderMapper;
    private ConcurrentHashMap<String, String> cachMap = new ConcurrentHashMap<String, String>();
    @Autowired
    private IInVoiceService iInVoiceService;
    @Autowired
    private ISaleOutApiService saleOutApiService;
    @Autowired
    private MiniAppFirstPayMapper firstPayMapper;

    @Autowired
    private MiniAppOrderPrecheckMapper orderPrecheckMapper;

    private Boolean Is_Init = false;

    /**
     * 第三方平台外卖 (美团、饿了么、抖音)
     */
    public final static int ORDER_TYPE_THIRD = 1;

    /**
     * ECO外卖优惠属性id
     */
    public final static String ECO_DISCOUNT_YHSX = "69";

    public final static String CMSX_DP="CMSX_DP";
    public final static String CMSX_TC="CMSX_TC";
    public final static String CMSX_MX="CMSX_MX";

    /**专送配送方式
     * 5-美团专配
     * 6-饿了么专配
     * 28-抖音专配
     * 29-京东秒送专配
     */
    public final static List<String> ZS_DELIVERY_TYPE=Arrays.asList("5","6","28","29");


    // @Transactional
    // public BillNoData getBillNoData(){
    // BillNoData billNoData = new BillNoData();
    // LOGGER.info("获取新账单号开始"+ this);
    // StringBuffer kdzdbh = new
    // StringBuffer(ecoOrderMapper.getbillid("99","TQ_ZDK","KDZDBH"));
    // StringBuffer jzzdbh = new
    // StringBuffer(ecoOrderMapper.getbillid("99","TQ_ZDK","JZZDBH"));
    // StringBuffer lsdh = new
    // StringBuffer(ecoOrderMapper.getbillid("99","TQ_ZDK","LSDH"));
    // kdzdbh.replace(2,3,"8");
    // jzzdbh.replace(2,3,"8");
    // lsdh.replace(2,3,"8");
    // billNoData.setKdzdbh(kdzdbh.toString());
    // billNoData.setJzzdbh(jzzdbh.toString());
    // billNoData.setLsdh(lsdh.toString());
    // LOGGER.info("获取新账单号成功" + billNoData.toString());
    // return billNoData;
    // }

    private String getKDishsNo(JSONObject jsonObject) {

        String dishNo=jsonObject.optString("speccode");
        if(StringUtils.isEmpty(dishNo)){
            dishNo=jsonObject.optString("kdishsno");
        }
        if(StringUtils.isEmpty(dishNo)){
            dishNo=jsonObject.optString("itemid");
        }
        return dishNo;

       /* if (null != jsonObject.get("kdishsno") && !jsonObject.optString("kdishsno").equals("null") && !jsonObject.optString("kdishsno").equals("")) {
            return jsonObject.optString("kdishsno");
        }
        if (null != jsonObject.get("itemid") && !jsonObject.optString("itemid").equals("null") && !jsonObject.optString("itemid").equals("")) {
            return jsonObject.optString("itemid");
        }
        return null;*/
    }

    private boolean checkOrderStatus(String outOrderIdInDB, EcoRepData data) {
        // 1 表示已经完成的正常单子， 2表示正在进行的单子 其他未失败或者不存在的单子
        // 1 和2的状态都不再接收了 其他的状态做数据清理，保持干净的单号
        // **************************************************
        Integer orderstatus = 0;
        LOGGER.info("第二步：校验订单状态……");
        if (ecoOrderMapper.getYddCountByYddh(outOrderIdInDB) > 0) {
            orderstatus = 1;
        }
        if (1 == orderstatus) {
            String checkId = ecoOrderMapper.getkdzdbhByYddh(outOrderIdInDB);
            data.setSuccess("repeat");
            data.setMsg("接单成功：重复单，订单已经接单成功");
            data.setTagstr("101");
            data.setCheck_id(checkId);
            return false;
        } else {
            ecoOrderMapper.clearYdxm2(outOrderIdInDB);
            ecoOrderMapper.clearEcoOrderdiscount(outOrderIdInDB);
            ecoOrderMapper.clearYdd(outOrderIdInDB);
            return true;
        }
    }

    private boolean checkOrderBackStatus(String outOrderIdInDB, EcoRepData data) {
        LOGGER.info("校验订单退单状态……");
        List<Map<String, String>> yddmaplst = ecoOrderMapper.getBtYdd(outOrderIdInDB);
        if (null == yddmaplst || yddmaplst.size() == 0) {
            data.setMsg("退单失败：未找到原订单，请确认订单是否已经接单成功");
            data.setTagstr("101");
            return false;
        }
        Object isPlatformObj = yddmaplst.get(0).get("isplatform");
        data.setIsPlatform((Integer)isPlatformObj);
        if (yddmaplst.get(0).get("ddzt").equals("7")) {
            data.setMsg("退单失败：订单已经退单成功");
            data.setTagstr("101");
            return false;
        }
        if (null != yddmaplst.get(0).get("refund_type") && yddmaplst.get(0).get("refund_type").equals("2")) {
            data.setHasDoneOrderPat("Y");
        } else {
            data.setHasDoneOrderPat("N");
        }
        if ("Y".equals(data.getIsOrderPart())) {
            if ("Y".equals(data.getHasDoneOrderPat())) {
                data.setTagstr("101");
                data.setMsg("退单失败：部分退菜已经退过");
                return false;
            }
        }
        return true;
    }

    private boolean checkPreZDKInfo(String outOrderIdInDB, EcoRepData data) {
        LOGGER.info("校验订单是临时账单还是历史账单……");
        List<Map<String, String>> zdks = ecoOrderMapper.getPreZDKByYddh(outOrderIdInDB);
        if (null == zdks || zdks.size() == 0) {
            List<Map<String, String>> zdlsks = ecoOrderMapper.getPreZDLSKByYddh(outOrderIdInDB);
            if (null == zdlsks || zdlsks.size() == 0) {
                data.setMsg("退单失败：未找到账单");
                return false;
            } else {
                data.setYgdlcs(zdlsks.get(0).get("ygdlcs"));
                data.setFwyh(zdlsks.get(0).get("fwyh"));
                data.setBbrq(DateUtil.formatDate(zdlsks.get(0).get("kdbbrq")));
                data.setIshis("1");
                data.setCheck_id(zdlsks.get(0).get("kdzdbh"));
                data.setSource(zdlsks.get(0).get("source"));
            }
        } else {
            data.setYgdlcs(zdks.get(0).get("ygdlcs"));
            data.setFwyh(zdks.get(0).get("fwyh"));
            data.setBbrq(DateUtil.formatDate(zdks.get(0).get("kdbbrq")));
            data.setIshis("0");
            data.setCheck_id(zdks.get(0).get("kdzdbh"));
            data.setSource(zdks.get(0).get("source"));
        }

        return true;
    }

    private boolean checkPrintOrderStatus(String outOrderIdInDB, EcoRepData data) {
        LOGGER.info("校验订单是否可打印状态……");
        List<Map<String, String>> yddmaplst = ecoOrderMapper.getBtYdd(outOrderIdInDB);
        if (null == yddmaplst || yddmaplst.size() == 0) {
            data.setMsg("打印失败：未找到原订单，请确认订单是否已经接单成功");
            return false;
        }
        data.setCheck_id(yddmaplst.get(0).get("ddzt"));
        return true;
    }

    private int checkItemInArrayList(List<Map<String, String>> dishs, List<Map<String, String>> dishtcs, String itemcode, String cmsx) {
        int irt = 0;
        String posCmsx = "";
        for (int i = 0; i < dishs.size(); i++) {
            if (dishs.get(i).get("cmbh").equals(itemcode)) {
                posCmsx = dishs.get(i).get("cmsx").toUpperCase();
                irt = 1;
                break;
                // if(dishs.get(i).get("cmsx").toUpperCase().equals(cmsx)){
                // irt = 1;
                // break;
                // }
                // else{
                // irt = -1;
                // break;
                // }
            }
        }
        if (irt == 1 && posCmsx.equals("CMSX_TC")) {
            for (int i = 0; i < dishtcs.size(); i++) {
                if (dishtcs.get(i).get("tcbh").equals(itemcode)) {
                    if (!dishtcs.get(i).get("mxlx").toUpperCase().equals("ERP_MXLX_SINGLE")) {
                        irt = -2;
                        break;
                    }
                    ;
                }
            }
        }
        return irt;
    }
    private int checkItemInArrayListNone(List<Map<String, String>> dishs, String itemcode, JSONObject dish) {
        int irt = 0;
        for (int i = 0; i < dishs.size(); i++) {
            if (dishs.get(i).get("cmbh").equals(itemcode)) {
                irt = 1;
                dish.put("cmid",dishs.get(i).get("cmid"));
                break;
            }
        }
        return irt;
    }

    /**
     * 检测菜品是否在POS系统中
     *
     * @param outOrderIdInDB
     * @param orderInfo
     * @param data
     * @return
     */
    private boolean checkDishInPos(String outOrderIdInDB, JSONObject orderInfo, EcoRepData data) {

        int isPlatform = orderInfo.optInt("isplatform", 1);
        // 校验订单是否存在对应的渠道或者对应渠道的付款编号是否存在
        if (orderInfo.optString("source") != null && InitDataListener.ecoTypeDicMap.containsKey(orderInfo.optString("source"))) {
            EcoTypeDic ecoTypeDic = InitDataListener.ecoTypeDicMap.get(orderInfo.optString("source"));
            if (ecoTypeDic == null) {
                data.setMsg(String.format("接单失败,POS渠道表不存在eco来源为{0}的渠道！", orderInfo.optString("source")));
                return false;
            }
            if (ecoTypeDic.getFkfsbh() == null || ecoTypeDic.getFkfsbh().equals("")) {
                data.setMsg(String.format("接单失败,POS渠道表eco来源为{0}的渠道对应的pos付款方式编号为空！", orderInfo.optString("source")));
                return false;
            }
            if (ecoTypeDic.getTzxcode() == null || ecoTypeDic.getTzxcode().equals("")) {
                data.setMsg(String.format("接单失败,POS渠道表eco来源为{0}的渠道对应的pos本地渠道编码为空！", orderInfo.optString("source")));
                return false;
            }
            if (ecoTypeDic.getTzxname() == null || ecoTypeDic.getTzxname().equals("")) {
                data.setMsg(String.format("接单失败,POS渠道表eco来源为{0}的渠道对应的pos本地渠道名称为空！", orderInfo.optString("source")));
                return false;
            }
        } else {
            data.setMsg("接单失败,渠道source为空！");
            return false;
        }

        // if(orderInfo.optString("source").equals("9"))
        // by.setYl4("MT08");
        // else if(orderInfo.optString("source").equals("10"))
        // by.setYl4("EL09");
        // else if(orderInfo.optString("source").equals("3"))
        // by.setYl4("WX02");
        // else if(orderInfo.optString("source").equals("2"))
        // by.setYl4("HW03");
        // else if(orderInfo.optString("source").equals("11"))
        // by.setYl4("DJ04");

        //ECO_RECEIVE_PATTERN ,1: 正常接单，菜品匹配不上就异常  ;
        //                     2: 异常接单，所有菜品都按异常菜品映射 ;
        //                     3: 异常接单，只有异常菜品才按异常菜品映射
        boolean chkBool = true;
        String ecoReceivePattern = "1";
        if (null != InitDataListener.ggcsMap.get("ECO_RECEIVE_PATTERN") && !"".equals(InitDataListener.ggcsMap.get("ECO_RECEIVE_PATTERN"))) {
            ecoReceivePattern = InitDataListener.ggcsMap.get("ECO_RECEIVE_PATTERN");
        }
        if ("1".equals(ecoReceivePattern) || "3".equals(ecoReceivePattern)) {
            ArrayList<String> wherestrs = new ArrayList<String>();
            ArrayList<String> wheretcstrs = new ArrayList<String>();
            JSONArray itemInfos = orderInfo.optJSONArray("items");
            if (1 == isPlatform) {
                for (int j = 0; j < itemInfos.size(); j++) {
                    wherestrs.add(getKDishsNo(itemInfos.getJSONObject(j)));
                }
            } else {
                String errMsg = "";
                boolean chkBool1 = true;
                for (int j = 0; j < itemInfos.size(); j++) {
                    wherestrs.add(getKDishsNo(itemInfos.getJSONObject(j)));
                    if (!"0".equals(itemInfos.getJSONObject(j).optString("bsetmeal"))) {
                        if (null != itemInfos.getJSONObject(j).get("details")) {
                            JSONArray detaisArr = itemInfos.getJSONObject(j).getJSONArray("details");
                            if (null == detaisArr || detaisArr.size() <= 0) {
                                errMsg = errMsg + "菜品 " + itemInfos.getJSONObject(j).optString("itemname") + "(" + getKDishsNo(itemInfos.getJSONObject(j)) + ") 是套餐但没有传明细数据;";
                                chkBool1 = false;
                            } else {
                                for (int i = 0; i < detaisArr.size(); i++) {
                                    wherestrs.add(getKDishsNo(detaisArr.getJSONObject(i)));
                                }
                            }
                        } else {
                            errMsg = errMsg + "菜品 " + itemInfos.getJSONObject(j).optString("itemname") + "(" + getKDishsNo(itemInfos.getJSONObject(j)) + ") 是套餐但没有传明细数据;";
                            chkBool1 = false;
                        }
                    }
                }
                if (!chkBool1) {
                    data.setMsg("接单失败," + errMsg.substring(0, errMsg.length() - 1));
                    return chkBool;
                }
            }
            List<Map<String, String>> dishs = ecoOrderMapper.findDishInfos(wherestrs);

            String cmsx = "";
            String errMsg = "";
            int irt = 0;

            if (1 == isPlatform) {
                for (int j = 0; j < dishs.size(); j++) {
                    if ("CMSX_TC".equals(dishs.get(j).get("cmsx").toUpperCase()))
                        wheretcstrs.add(dishs.get(j).get("cmbh"));
                }
                List<Map<String, String>> dishtcs = null;
                if (wheretcstrs.size() > 0) {
                    dishtcs = ecoOrderMapper.findDishTcInfos(wheretcstrs);
                }
//                if ("3".equals(ecoReceivePattern)) {
//                    String erroItemCode = "";
//                    ArrayList<String> erroItemCodes = new ArrayList<String>();
//                    if (null != InitDataListener.ggcsMap.get("ERROR_ITEM_CODE") && !"".equals(InitDataListener.ggcsMap.get("ERROR_ITEM_CODE"))) {
//                        erroItemCode = InitDataListener.ggcsMap.get("ERROR_ITEM_CODE");
//                    }
//                    erroItemCodes.add(erroItemCode);
//                    List<Map<String, String>> erroDishs = ecoOrderMapper.findDishInfos(erroItemCodes);
//                    if(erroDishs.size() <= 0) {
//                        errMsg = "当前接单模式为异常接单模式，但未绑定异常菜品 ERROR_ITEM_CODE";
//                        chkBool = false;
//                    } else {
//                        JSONArray newItemsArr = new JSONArray();
//                        for (int j = 0; j < itemInfos.size(); j++) {
//                            JSONObject itemInfo = itemInfos.getJSONObject(j);
//                            if ("0".equals(itemInfos.getJSONObject(j).optString("bsetmeal"))) {
//                                cmsx = "CMSX_DP";
//                            } else {
//                                cmsx = "CMSX_TC";
//                            }
//                            irt = checkItemInArrayList(dishs, dishtcs, getKDishsNo(itemInfos.getJSONObject(j)), cmsx);
//                            if(1 != irt) {
//                                itemInfo.put("bsetmeal", "0"); // 异常菜品后续全部按照单品处理
////                                itemInfo.put("dish_sno_other", erroItemCode);
////                                itemInfo.put("dishsno", erroItemCode);
////                                itemInfo.put("itemid", erroItemCode);
////                                itemInfo.put("kdishsno", erroItemCode);
//                            }
//                            newItemsArr.add(itemInfo);
//                        }
//                        orderInfo.put("items", newItemsArr);
//                    }
//                }
                if ("3".equals(ecoReceivePattern)) {
                    JSONArray newItemsArr = new JSONArray();
                    for (int j = 0; j < itemInfos.size(); j++) {
                        JSONObject itemInfo = itemInfos.getJSONObject(j);
                        if ("0".equals(itemInfos.getJSONObject(j).optString("bsetmeal"))) {
                            cmsx = "CMSX_DP";
                        } else {
                            cmsx = "CMSX_TC";
                        }
                        irt = checkItemInArrayList(dishs, dishtcs, getKDishsNo(itemInfos.getJSONObject(j)), cmsx);
                        if (0== irt) {
                            itemInfo.put("bsetmeal", "-1"); // 新增异常菜品项目属性，CMSX_YC
                        }
                        newItemsArr.add(itemInfo);
                    }
                    orderInfo.put("items", newItemsArr);
                } else {
                    for (int j = 0; j < itemInfos.size(); j++) {
                        if ("0".equals(itemInfos.getJSONObject(j).optString("bsetmeal")))
                            cmsx = "CMSX_DP";
                        else
                            cmsx = "CMSX_TC";
                        irt = checkItemInArrayList(dishs, dishtcs, getKDishsNo(itemInfos.getJSONObject(j)), cmsx);
                        switch (irt) {
                            case 0:
                                errMsg = errMsg + "菜品 " + itemInfos.getJSONObject(j).optString("itemname") + "(" + getKDishsNo(itemInfos.getJSONObject(j)) + ") POS系统不存在;";
                                chkBool = false;
                                break;
                            case 1:
                                break;
                            case -1:
                                errMsg = errMsg + "菜品 " + itemInfos.getJSONObject(j).optString("itemname") + "(" + getKDishsNo(itemInfos.getJSONObject(j)) + ") 跟POS系统菜品属性不一致;";
                                chkBool = false;
                                break;
                            case -2:
//                                errMsg = errMsg + "套餐菜品 " + itemInfos.getJSONObject(j).optString("itemname") + "(" + getKDishsNo(itemInfos.getJSONObject(j)) + ")存在可选分组菜品;";
                                chkBool = true;
                                break;
                            default:
                                errMsg = errMsg + "菜品 " + itemInfos.getJSONObject(j).optString("itemname") + "(" + getKDishsNo(itemInfos.getJSONObject(j)) + ") 检查未知存在错误;";
                                chkBool = false;
                                break;
                        }
                    }
                }
            } else {
                for (int j = 0; j < itemInfos.size(); j++) {
                    JSONObject dishMain = itemInfos.getJSONObject(j);
                    irt = checkItemInArrayListNone(dishs, getKDishsNo(dishMain), dishMain);
                    switch (irt) {
                        case 0:
                            errMsg = errMsg + "菜品 " + itemInfos.getJSONObject(j).optString("itemname") + "(" + getKDishsNo(itemInfos.getJSONObject(j)) + ") POS系统不存在;";
                            chkBool = false;
                            break;
                        case 1:
                            break;
                        case -1:
                            errMsg = errMsg + "菜品 " + itemInfos.getJSONObject(j).optString("itemname") + "(" + getKDishsNo(itemInfos.getJSONObject(j)) + ") 跟POS系统菜品属性不一致;";
                            chkBool = false;
                            break;
                        default:
                            errMsg = errMsg + "菜品 " + itemInfos.getJSONObject(j).optString("itemname") + "(" + getKDishsNo(itemInfos.getJSONObject(j)) + ") 检查未知存在错误;";
                            chkBool = false;
                            break;
                    }
                    if (!"0".equals(itemInfos.getJSONObject(j).optString("bsetmeal"))) {
                        if (null != itemInfos.getJSONObject(j).get("details")) {
                            JSONArray detaisArr = itemInfos.getJSONObject(j).getJSONArray("details");
                            for (int i = 0; i < detaisArr.size(); i++) {
                                JSONObject dishDetail = detaisArr.getJSONObject(i);
                                irt = checkItemInArrayListNone(dishs, getKDishsNo(dishDetail), dishDetail);
                                switch (irt) {
                                    case 0:
                                        errMsg = errMsg + "菜品 " + detaisArr.getJSONObject(i).optString("itemname") + "(" + getKDishsNo(itemInfos.getJSONObject(j)) + ") POS系统不存在;";
                                        chkBool = false;
                                        break;
                                    case 1:
                                        break;
                                    case -1:
                                        errMsg = errMsg + "菜品 " + detaisArr.getJSONObject(i).optString("itemname") + "(" + getKDishsNo(itemInfos.getJSONObject(j)) + ") 跟POS系统菜品属性不一致;";
                                        chkBool = false;
                                        break;
                                    default:
                                        errMsg = errMsg + "菜品 " + detaisArr.getJSONObject(i).optString("itemname") + "(" + getKDishsNo(itemInfos.getJSONObject(j)) + ") 检查未知存在错误;";
                                        chkBool = false;
                                        break;
                                }
                            }
                        }
                    }
                }
            }
            if (!chkBool) {
                data.setMsg("接单失败," + errMsg.substring(0, errMsg.length() - 1));
            }
        }
        return chkBool;
    }

    /**
     * 检查订单价格是否一致
     *
     * @param outOrderIdInDB
     * @param orderInfo
     * @param data
     * @return
     */
    private boolean checkOrderPrice(String outOrderIdInDB, JSONObject orderInfo, EcoRepData data) {
        int isPlatform = orderInfo.optInt("isplatform", 1);
        double yl2 = 0d;// 优惠总金额
        double shop_rate = 0d;// 门店承担优惠金额
        JSONArray activityInfos = orderInfo.optJSONArray("activityInfo");
        for (int i = 0; i < activityInfos.size(); i++) {
            yl2 = ArithUtil.add(yl2, activityInfos.getJSONObject(i).optDouble("discount_acmount"));
            shop_rate = ArithUtil.add(shop_rate, activityInfos.getJSONObject(i).optDouble("shop_rate"));
        }
        JSONArray itemInfos = orderInfo.optJSONArray("items");
        double originalprice = 0d;
        double price = 0d;
        for (int j = 0; j < itemInfos.size(); j++) {

            JSONObject itemInfo = itemInfos.getJSONObject(j);


            double iPrice = itemInfo.optDouble("price", 0d);
            String nature = itemInfo.optString("nature", "");

//            double itemCount=itemInfo.optDouble("itemcount", 1d);

            JSONArray toppings= itemInfo.optJSONArray("toppings");
            double addPriceSum = 0d;
            if(CollectionUtils.isNotEmpty(toppings)) {
                for (int k = 0; k < toppings.size(); k++) {
                    JSONObject topping = toppings.optJSONObject(k);
                    double addprice = topping.optDouble("tpprice", 0d);
                    double aCount = topping.optDouble("count", 0d);
                    double addpriceAll = ArithUtil.mul(addprice, aCount);
                    addPriceSum = ArithUtil.add(addPriceSum, addpriceAll);
                }
//                itemInfo.put("price", iPrice);
            }

           /* String otJsonStr = itemInfo.optString("ot_json", "");
            if(StringUtils.isNotEmpty(otJsonStr) && !"null".equals(otJsonStr)){
                JSONObject otJson = JSONObject.fromObject(otJsonStr);
                JSONArray toppings = otJson.optJSONArray("toppings");
                if (null != toppings) {
                    for (int k = 0; k < toppings.size(); k++) {
                        JSONObject topping = toppings.optJSONObject(k);
                        double addprice = topping.optDouble("addprice", 0d);
                        double aCount = topping.optDouble("count", 0d);
                        double addpriceAll = ArithUtil.mul(addprice, aCount);
                        iPrice = ArithUtil.add(iPrice, addpriceAll);
                        nature = nature + " " + topping.optString("tpname", "") + "(+" +  addpriceAll + "元) ";
                    }
                }
                itemInfo.put("price", iPrice);
                itemInfo.put("nature", nature);
            }*/
            originalprice = ArithUtil.add(originalprice, itemInfos.getJSONObject(j).optDouble("originalprice"));
            price = ArithUtil.add(price, itemInfos.getJSONObject(j).optDouble("price"));
            //添加配料价格
            price = ArithUtil.add(price, addPriceSum);
        }
        double total = orderInfo.optDouble("total");// 商品金额+配送费+餐盒费-优惠总金额
        double totalprice = orderInfo.optDouble("totalprice");// 商品金额+自配送费+餐盒费
        double yl1 = totalprice;
        double order_delivery_pay = orderInfo.optDouble("order_delivery_pay",0);// 配送费
        double shopfee = orderInfo.optDouble("shopfee");// 商家实收
        double package_fee = orderInfo.optDouble("package_fee");// 餐盒费
        double service_money = orderInfo.optDouble("service_money");// 佣金金额
        int delivery_type = NumberUtils.toInt(orderInfo.optString("delivery_type"), 5);
        double real_end_price = orderInfo.optDouble("real_end_price");// 客付金额
        double end_total = orderInfo.optDouble("end_total");// 客付金额
        double total_shop_rate = orderInfo.optDouble("shop_rate");// 商家承担费用

        double cmje = 0d;
        double orderprice = 0d;
        double sjje = 0d;
        if (!ZS_DELIVERY_TYPE.contains(delivery_type)) {
            cmje = ArithUtil.sub(ArithUtil.sub(price, package_fee), order_delivery_pay);
            orderprice = price;
            sjje = ArithUtil.add(ArithUtil.sub(ArithUtil.sub(orderprice, yl2), service_money), (ArithUtil.sub(yl2, shop_rate)));
        } else {
            cmje = ArithUtil.sub(price, package_fee);
            orderprice = ArithUtil.add(price, order_delivery_pay);
            sjje = ArithUtil.add(ArithUtil.sub(ArithUtil.sub(ArithUtil.sub(orderprice, yl2), service_money), order_delivery_pay), ArithUtil.sub(yl2, shop_rate));
        }


        LOGGER.info("商品金额={}，餐盒费={}，外送费={}，订单总金额（菜品、配送费、餐盒费）={}，" + "优惠金额={}，客付价={}，商家补贴={}，平台佣金={}，净收入={}，结算金额={}", cmje, package_fee, order_delivery_pay, orderprice, yl2,
                ArithUtil.sub(orderprice, yl2), shop_rate, service_money, sjje, real_end_price);
        if (price != totalprice) {
            LOGGER.info("接单失败 明细金额{}跟总金额{}不一致，请检查明细是否包含配送费", price, totalprice);
            data.setMsg(String.format("接单失败明细金额%s跟总金额%s不一致，请检查明细是否包含配送费", price, totalprice));
            return false;
        } else
            LOGGER.info("接单成功 明细金额 {} 跟总金额 {} 一致", price, totalprice);


        //自运营外卖需要校验
        if(1 != isPlatform) {
            if (checkIllegalPayInfos(orderInfo, data, sjje)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 校验付款明细
     * @param orderInfo
     * @param data
     * @param sjje
     * @return
     */
    private static boolean checkIllegalPayInfos(JSONObject orderInfo, EcoRepData data, double sjje) {
        //自运营外卖校验是否包含付款明细
        if(null== orderInfo.get("payinfos")){
            data.setMsg("接单失败,自运营外卖未找到付款明细！");
            return true;
        }
        if(orderInfo.get("payinfos") instanceof JSONArray){
            JSONArray payinfos = orderInfo.optJSONArray("payinfos");
            double payAmountDetail = 0;//付款流水
            String payindex = "";
            List payindexList = new ArrayList();
            for (int i = 0; i < payinfos.size(); i++) {
                payAmountDetail = ArithUtil.add(payAmountDetail, payinfos.getJSONObject(i).optDouble("amount"));
                if(payinfos.getJSONObject(i).optDouble("amount") < payinfos.getJSONObject(i).optDouble("storepay")){
                    data.setMsg(String.format("接单失败，%s付款金额%f不能小于实付金额%f！", payinfos.getJSONObject(i).optString("pay_name"),
                            payinfos.getJSONObject(i).optDouble("amount"),payinfos.getJSONObject(i).optDouble("storepay")));
                    return true;
                }
                payindex = payinfos.getJSONObject(i).optString("payindex","");
                if("".equals(payindex)){
                    data.setMsg("接单失败，自运营外卖付款流水的付款序号不能为空！");
                    return true;
                }
                if(payindexList.contains(payindex)){
                    data.setMsg("接单失败，自运营外卖付款流水的付款序号不能重复！");
                    return true;
                }
                payindexList.add(payindex);
            }
            if(sjje != payAmountDetail){
                data.setMsg(String.format("接单失败，付款明细金额%f跟实付金额%f不一致！", payAmountDetail, sjje));
                return true;
            }

        }
        else{
            data.setMsg("接单失败,自运营外卖未找到付款明细！");
            return true;
        }
        return false;
    }

    public String processEcoTOBillCode(Integer code) {
        String codemsg = "";
        switch (code) {
            case -100:
                codemsg = "订单已经接单成功，无法接单！";
                break;
            case -200:
                codemsg = "订单已经接单成功，无法接单！";
                break;
            case -800:
                codemsg = "POS已经交班，无法接单！";
                break;
            case -121:
                codemsg = "有菜品在POS系统不存在，无法接单！";
                break;
            case -400:
                codemsg = "订单菜品数量为0，无法接单！";
                break;
            case -103:
                codemsg = "POS系统不存在外卖活动优惠方式，无法接单！";
                break;
            case -500:
                codemsg = "金额转账单计算错误，无法接单！";
                break;
            case -700:
                codemsg = "付款明细金额不等于实付金额，无法接单！";
                break;
            case -600:
                codemsg = "套餐金额不等于套餐明细金额，无法接单！";
                break;
            case -104:
                codemsg = "POS暂不支持此订单来源的付款方式，无法接单！";
                break;
            case -105:
                codemsg = "POS不存在对应此订单的付款类型，无法接单！";
                break;
            case -106:
                codemsg = "POS保存付款失败，无法接单！";
                break;
            case -107:
                codemsg = "商家承担优惠明细总和不能于汇总，无法接单！";
                break;
            case -108:
                codemsg = "POS系统不存在佣金优惠方式，无法接单！";
                break;
            case -850:
                codemsg = "POS正在进行交班，无法接单！";
                break;
            case -875:
                codemsg = "POS正在进行交班，无法接单！";
                break;
            case -109:
                codemsg = "POS系统不存在付款拆分对应优惠方式，无法接单！";
                break;
            case -110:
                codemsg = "POS系统付款拆分优惠实付跟明细实结不一致，无法接单！";
                break;
            case -122:
                codemsg = "异常接单模式，但POS未绑定异常菜品，无法接单！";
                break;
            default:
                codemsg = "未知错误,错误码" + code.toString();
                break;
        }
        return codemsg;
    }

    public String processCancelEcoBillCode(Integer code) {
        String codemsg = "";
        switch (code) {
            case -100:
                codemsg = "预订单不存在，无法退单！";
                break;
            case -200:
                codemsg = "账单不存在，无法退单！";
                break;
            case -201:
                codemsg = "账单已经交班，无法退单！";
                break;
            case -202:
                codemsg = "部分退款金额错误，无法退单！";
                break;
            case -203:
                codemsg = "部分退款原单总金额错误，无法退单！";
                break;
            case -204:
                codemsg = "部分退款金额大于原单总金额，无法退单！";
                break;
            case -205:
                codemsg = "部分退款菜品无法找到原单菜品，无法退单！";
                break;
            case -121:
                codemsg = "有菜品在POS系统不存在，无法退单！";
                break;
            case -400:
                codemsg = "订单菜品数量为0，无法退单！";
                break;
            case -103:
                codemsg = "POS系统不存在外卖对应优惠方式，无法退单！";
                break;
            case -500:
                codemsg = "金额转账单计算错误，无法退单！";
                break;
            case -600:
                codemsg = "套餐金额不等于套餐明细金额，无法退单！";
                break;
            case -104:
                codemsg = "POS暂不支持此订单来源的付款方式，无法退单！";
                break;
            case -105:
                codemsg = "POS不存在对应此订单的付款类型，无法退单！";
                break;
            case -106:
                codemsg = "POS保存付款失败，无法退单！";
                break;
            case -107:
                codemsg = "商家承担优惠明细总和不能于汇总，无法退单！";
                break;
            case -108:
                codemsg = "POS系统不存在佣金优惠方式，无法退单！";
                break;
            case -800:
                codemsg = "POS已经交班，无法退单！";
                break;
            case -850:
                codemsg = "POS正在进行交班，无法退单！";
                break;
            case -875:
                codemsg = "POS正在进行交班，无法退单！";
                break;
            case -109:
                codemsg = "POS系统不存在付款拆分对应优惠方式，无法退单！";
                break;
            case -110:
                codemsg = "POS系统付款拆分优惠实付跟明细实结不一致，无法退单！";
                break;
            case -301:
                codemsg = "自运营外卖套餐不支持部分数量退单，无法部分退单！";
                break;
            case -302:
                codemsg = "不支持套餐明细部分退单，无法部分退单！";
                break;
            case -303:
                codemsg = "自营外卖菜品明细退款跟跟付款明细退款不一致，无法部分退单！";
                break;
            case -304:
                codemsg = "自营外卖存款流水退款金额大于原单，无法部分退单！";
                break;
            case -122:
                codemsg = "异常接单模式，但POS未绑定异常菜品，无法退单！";
                break;
            default:
                codemsg = "未知错误,错误码" + code.toString();
                break;
        }
        return codemsg;
    }

    /**
     * 门店校验订单信息 1.检查门店状态 2.检查门店是否已经接单 3.检查门店是否已经退单 4.检查订单金额
     * @param data
     * @param inparam
     * @param orderData
     * @return
     */
    public EcoRepData orderPrecheckBefore(EcoRepData data, Map<String, String> inparam, JSONObject orderData) {
        // ECO预定账单编号
        String outOrderId = orderData.optString("outorderid");
        inparam.put("outOrderId", outOrderId);
        // 我们系统内部预定账单编号，规则：增加一个前缀
        String outOrderIdInDB = Constant.ECOBILL_PREFIX + outOrderId;
        inparam.put("outOrderIdInDB", outOrderIdInDB);
        // 默认失败
        data.setSuccess("false");
        data.setMsg("接单失败：未知原因");
        LOGGER.info("门店开始接单，平台预定账单编号：" + outOrderId);
        // **************************************************
        // 第一步：检查门店状态
        // **************************************************
        LOGGER.info("第一步：检查门店状态……");
        // 可能没有开店、没有开班、已经打烊等等
        HashMap<String, String> param = new HashMap<String, String>();
        EcoRepData dataShopStatus = shopStatus(param);
        String bbrq = param.get("bbrq");
        inparam.put("bbrq", bbrq);
        if (dataShopStatus.getSuccess() != "true") {
            data.setMsg("接单失败：" + dataShopStatus.getMsg());
            return data;
        }
        TqJtztk jtzt = ecoOrderMapper.getJtZtk(DateUtil.parseDate(bbrq));
        if (null == jtzt) {
            data.setMsg("接单失败：未获取到登录人员，请确认门店是否登录");
            return data;
        }
        inparam.put("rybh", jtzt.getRybh());
        inparam.put("ygdlcs", jtzt.getYgdlcs());

        //预处理订单数据
        orderData = preDealOrderData(orderData);

        // **************************************************
        // 第二步：校验订单当前状态
        if (!checkOrderStatus(outOrderIdInDB, data)) {
            return data;
        }
        if (!checkDishInPos(outOrderIdInDB, orderData, data)) {
            return data;
        }

        //第四步: 检查订单金额
        if (!checkOrderPrice(outOrderIdInDB, orderData, data)) {
            return data;
        }

        // 只有订单转成账单成功场景无须异常处理
        inparam.put("allow_error", "0");
        data.setSuccess("true");
        return data;
    }


    private JSONObject preDealOrderData(JSONObject orderData) {
        //实收包含佣金
        if(isActualPayIncludeCommission()){
           // 佣金金额
           double service_money = orderData.optDouble("service_money",0d);
           LOGGER.info("ECO外卖实收是包含平台佣金:"+service_money);
           orderData.put("service_money",0d);

           assert orderData.optDouble("service_money")==0d;
        }

        JSONArray itemInfos = orderData.optJSONArray("items");

        Map<String,List<String>> tcmxMap=new HashMap<>();

        final List<TsCmk> allDishes = orderPrecheckMapper.getAllDishes();
        Map<String, TsCmk> tsCmkMap = allDishes.stream().collect(Collectors.toMap(TsCmk::getCmbh, Function.identity()));
        Set<String> removeMx=new HashSet<>();

        boolean isEnableMapByDishName = isEnableMapByDishName();
        if(CollectionUtils.isNotEmpty(itemInfos)){
            Iterator<Object> iterator = itemInfos.iterator();
            while (iterator.hasNext()){
                JSONObject next = (JSONObject)iterator.next();

                String otJson= next.optString("ot_json");
                if(StringUtils.isNotBlank(otJson)){
                    if(otJson.contains("\"foodType\":3")){
                        iterator.remove();
                        continue;
                    }
                }

                String dishNo=next.optString("kdishsno");
                String dishName=next.optString("itemname");
                TsCmk tsCmk=orderPrecheckMapper.getDishByCode(dishNo);

                if(null==tsCmk&&isEnableMapByDishName){
                    tsCmk=orderPrecheckMapper.getDishByName(dishName);
                    if(tsCmk!=null){
                        next.put("kdishsno",tsCmk.getCmbh());
                        dishNo=tsCmk.getCmbh();
                    }
                }

                String bsetmeal=next.optString("bsetmeal","0");
                String bsetmealid=next.optString("bsetmealid","0");




                if("1".equals(bsetmeal)){

                    //此标识用于保存eco套餐主项的uniqueId到bt_ydxm2.bsetmealid,用于eco是可选套餐门店是单品打印套餐明细
                    if( "Y".equals(InitDataListener.ggcsMap.get("EcoTcToDp"))) {
                        next.put("setmeal", "Y");
                    }


                    //        如果门店是单品，ECO是可选套餐
                    //          按单品处理，删除可选套餐明细
                    if(tsCmkMap.containsKey(dishNo)
                            &&CMSX_DP.equals(tsCmkMap.get(dishNo).getCmsx())){
                        next.put("cmsx",CMSX_DP);
                        removeMx.add(next.optString("uniqueId"));
                    }else {

                        //乡村基可选套餐转单品
                        if( "Y".equals(InitDataListener.ggcsMap.get("EcoTcToDp"))){
                            next.put("cmsx",CMSX_DP);
                            removeMx.add(next.optString("uniqueId"));
                        }else {
                            next.put("cmsx",CMSX_TC);
                        }

                    }
                }
                if("0".equals(bsetmeal)&&!"0".equals(bsetmealid)){

                    if(removeMx.contains(next.optString("bsetmealid"))){
                        iterator.remove();
                        continue;
                    }else {
                        next.put("cmsx",CMSX_MX);
                        tcmxMap.compute(bsetmealid,(k,v)->{
                            if(null==v){
                                v=new ArrayList<>();
                            }
                            v.add(next.optString("uniqueId"));
                            return v;
                        });
                    }

                }
                if("0".equals(bsetmeal)&&"0".equals(bsetmealid)){
                    //        如果门店是套餐，ECO是单品
                    //          按套餐处理，根据餐谱添加套餐明细
                    if(tsCmkMap.containsKey(dishNo)
                            &&CMSX_TC.equals(tsCmkMap.get(dishNo).getCmsx())){
                        next.put("cmsx",CMSX_TC);
                    }else {
                        next.put("cmsx",CMSX_DP);
                    }
                }


                JSONArray toppings=next.optJSONArray("toppings");

                if(CollectionUtils.isNotEmpty(toppings)){
                    Iterator<Object> tpIter = toppings.iterator();

                    while (tpIter.hasNext()){
                        JSONObject tp = (JSONObject)tpIter.next();
                        String tpno=tp.optString("tpno");
                        String tpName=tp.optString("tpname");

                        TsCmk cmk=orderPrecheckMapper.getDishByCode(tpno);
                        if(null==cmk&&isEnableMapByDishName){
                            cmk=orderPrecheckMapper.getDishByName(tpName);
                            if(cmk!=null){
                                tp.put("tpno",cmk.getCmbh());
                            }
                        }
                    }
                }
            }

            orderData.put("tcmx",tcmxMap);
        }

        return orderData;
    }

    private boolean isEnableMapByDishName() {
        return "Y".equals(DBUtils.getGGCSK("EcoItemMapByName"));
    }

    private boolean isActualPayIncludeCommission() {
        return "Y".equals(DBUtils.getGGCSK("EcoActualPayIncludeCommission"));
    }


    private EcoRepData DecSaleOut(EcoRepData data, Map<String, String> inparam, JSONObject orderData) {
        if (!(null != InitDataListener.ggcsMap.get("ISREALTIMEDISHCOUNT") && InitDataListener.ggcsMap.get("ISREALTIMEDISHCOUNT").equals("Y"))) {
            data.setSuccess("true");
            LOGGER.info("未启用实时沽清参数ISREALTIMEDISHCOUNT");
            return data;
        }
        // 菜品列表
        List<ReqParam> reqParams = new ArrayList<ReqParam>();
        JSONArray itemInfos = orderData.optJSONArray("items");
        ArrayList<String> wherestrs = new ArrayList<String>();
        ArrayList<String> wheretcstrs = new ArrayList<String>();
        HashMap<String, String> cmsxMap = new HashMap<String, String>();
        for (int j = 0; j < itemInfos.size(); j++) {
            wherestrs.add(getKDishsNo(itemInfos.getJSONObject(j)));
            ReqParam reqParam = new ReqParam();
            reqParam.setItem_id(getKDishsNo(itemInfos.getJSONObject(j)));
            reqParam.setItem_name(itemInfos.getJSONObject(j).optString("itemname"));
            reqParam.setCount(new BigDecimal(itemInfos.getJSONObject(j).optDouble("itemcount")));
            reqParams.add(reqParam);
        }
        List<Map<String, String>> dishs = ecoOrderMapper.findDishInfos(wherestrs);
        for (int j = 0; j < dishs.size(); j++) {
            if ("CMSX_TC".equals(dishs.get(j).get("cmsx").toUpperCase()))
                wheretcstrs.add(dishs.get(j).get("cmbh"));
            cmsxMap.put(dishs.get(j).get("cmbh"), dishs.get(j).get("cmsx").toUpperCase());
        }
        List<Map<String, Object>> dishtcs = null;
        if (wheretcstrs.size() > 0) {
            dishtcs = ecoOrderMapper.findDishTcInfosObj(wheretcstrs);
        }

        String posCmsx = "";
        BigDecimal tcsl = null;

        for (int i = 0; i < itemInfos.size(); i++) {
            posCmsx = cmsxMap.get(getKDishsNo(itemInfos.getJSONObject(i))).toUpperCase();
            if (posCmsx.equals("CMSX_TC")) {
                // 增加套餐明细
                for (int j = 0; j < dishtcs.size(); j++) {
                    ReqParam reqParam = new ReqParam();
                    reqParam.setItem_id(dishtcs.get(j).get("cmbh").toString());
                    reqParam.setItem_name(dishtcs.get(j).get("cmmc1").toString());
                    reqParam.setCount(new BigDecimal(ArithUtil.mul(new BigDecimal(dishtcs.get(j).get("cmsl").toString()).doubleValue(), itemInfos.getJSONObject(i).optDouble("itemcount"))));
                    reqParams.add(reqParam);
                }
            }
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("dishinfo", GsonUtil.GsonString(reqParams));
        CommApiData commApiData = new CommApiData();
        saleOutApiService.SaleOutDec(commApiData, jsonObject);
        if (commApiData.getCode().equals(0)) {
            data.setSuccess("true");
        } else {
            data.setSuccess("false");
            data.setMsg(commApiData.getMsg());
        }
        return data;
    }

    private EcoRepData AddSaleOut(EcoRepData data, Map<String, String> inparam, JSONObject orderData) {
        if (!(null != InitDataListener.ggcsMap.get("ISREALTIMEDISHCOUNT") && InitDataListener.ggcsMap.get("ISREALTIMEDISHCOUNT").equals("Y"))) {
            data.setSuccess("true");
            LOGGER.info("未启用实时沽清参数ISREALTIMEDISHCOUNT");
            return data;
        }
        // 菜品列表
        List<ReqParam> reqParams = new ArrayList<ReqParam>();
        ArrayList<String> wherestrs = new ArrayList<String>();
        ArrayList<String> wheretcstrs = new ArrayList<String>();
        HashMap<String, String> cmsxMap = new HashMap<String, String>();
        List<Map<String, Object>> ydxm2s = null;
        // 部分退款和全部退款组装
        if (null != data.getIsOrderPart() && data.getIsOrderPart().equals("Y")) {
            JSONArray itemInfos = orderData.optJSONArray("orderPart");
            for (int j = 0; j < itemInfos.size(); j++) {
                if (!("2".equals(itemInfos.getJSONObject(j).optString("partstatus")))) {
                    continue;
                }
                if (itemInfos.getJSONObject(j).optDouble("partcount", 0) <= 0) {
                    continue;
                }
                wherestrs.add(getKDishsNo(itemInfos.getJSONObject(j)));
                ReqParam reqParam = new ReqParam();
                reqParam.setItem_id(getKDishsNo(itemInfos.getJSONObject(j)));
                reqParam.setItem_name(itemInfos.getJSONObject(j).optString("itemname"));
                reqParam.setCount(new BigDecimal(itemInfos.getJSONObject(j).optDouble("partcount")));
                reqParams.add(reqParam);
            }
        } else {
            // ECO预定账单编号
            String outOrderId = orderData.optString("outorderid");
            // 我们系统内部预定账单编号，规则：增加一个前缀
            String outOrderIdInDB = Constant.ECOBILL_PREFIX + outOrderId;
            ydxm2s = ecoOrderMapper.getYdxm2ByYddh(inparam.get("outOrderIdInDB"));
            if (null != ydxm2s && ydxm2s.size() > 0) {
                for (int i = 0; i < ydxm2s.size(); i++) {
                    wherestrs.add(ydxm2s.get(i).get("xmbh").toString());
                    ReqParam reqParam = new ReqParam();
                    reqParam.setItem_id(ydxm2s.get(i).get("xmbh").toString());
                    reqParam.setItem_name(ydxm2s.get(i).get("xmmc").toString());
                    reqParam.setCount(new BigDecimal(ydxm2s.get(i).get("xmsl").toString()));
                    reqParams.add(reqParam);
                }
            } else {
                data.setSuccess("false");
                LOGGER.info("获取沽清明细菜品失败");
                return data;
            }
        }

        List<Map<String, String>> dishs = ecoOrderMapper.findDishInfos(wherestrs);
        for (int j = 0; j < dishs.size(); j++) {
            if ("CMSX_TC".equals(dishs.get(j).get("cmsx").toUpperCase()))
                wheretcstrs.add(dishs.get(j).get("cmbh"));
            cmsxMap.put(dishs.get(j).get("cmbh"), dishs.get(j).get("cmsx").toUpperCase());
        }
        List<Map<String, Object>> dishtcs = null;
        if (wheretcstrs.size() > 0) {
            dishtcs = ecoOrderMapper.findDishTcInfosObj(wheretcstrs);
        }

        String posCmsx = "";
        if (null != data.getIsOrderPart() && data.getIsOrderPart().equals("Y")) {
            JSONArray itemInfos = orderData.optJSONArray("orderPart");
            for (int i = 0; i < itemInfos.size(); i++) {
                if (!("2".equals(itemInfos.getJSONObject(i).optString("partstatus")))) {
                    continue;
                }
                if (itemInfos.getJSONObject(i).optDouble("partcount", 0) <= 0) {
                    continue;
                }
                posCmsx = cmsxMap.get(getKDishsNo(itemInfos.getJSONObject(i))).toUpperCase();
                if (posCmsx.equals("CMSX_TC")) {
                    // 增加套餐明细
                    for (int j = 0; j < dishtcs.size(); j++) {
                        ReqParam reqParam = new ReqParam();
                        reqParam.setItem_id(dishtcs.get(j).get("cmbh").toString());
                        reqParam.setItem_name(dishtcs.get(j).get("cmmc1").toString());
                        reqParam.setCount(new BigDecimal(ArithUtil.mul(new BigDecimal(dishtcs.get(j).get("cmsl").toString()).doubleValue(), itemInfos.getJSONObject(i).optDouble("partcount"))));
                        reqParams.add(reqParam);
                    }
                }
            }
        } else {
            if (null != ydxm2s && ydxm2s.size() > 0) {
                for (int i = 0; i < ydxm2s.size(); i++) {
                    posCmsx = cmsxMap.get(ydxm2s.get(i).get("xmbh").toString()).toUpperCase();
                    if (posCmsx.equals("CMSX_TC")) {
                        // 增加套餐明细
                        for (int j = 0; j < dishtcs.size(); j++) {
                            ReqParam reqParam = new ReqParam();
                            reqParam.setItem_id(dishtcs.get(j).get("cmbh").toString());
                            reqParam.setItem_name(dishtcs.get(j).get("cmmc1").toString());
                            reqParam.setCount(new BigDecimal(ArithUtil.mul(new BigDecimal(dishtcs.get(j).get("cmsl").toString()).doubleValue(),
                                    new BigDecimal(ydxm2s.get(i).get("xmsl").toString()).doubleValue())));
                            reqParams.add(reqParam);
                        }
                    }
                }
            } else {
                data.setSuccess("false");
                LOGGER.info("获取沽清明细菜品失败");
                return data;
            }
        }

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("dishinfo", GsonUtil.GsonString(reqParams));
        CommApiData commApiData = new CommApiData();
        saleOutApiService.SaleOutAdd(commApiData, jsonObject);
        if (commApiData.getCode().equals(0)) {
            data.setSuccess("true");
        } else {
            data.setSuccess("false");
            data.setMsg(commApiData.getMsg());
        }
        return data;
    }

    /**
     * 保存订单并转为账单
     * @param billNoData
     * @param data
     * @param inparam
     * @param orderData
     * @return
     */
    @Transactional
    public EcoRepData orderPrecheckProcess(BillNoData billNoData, EcoRepData data, Map<String, String> inparam, JSONObject orderData) {

        //第三方平台外卖=默认值1
        int isPlatform = orderData.optInt("isplatform", 1);
        JSONArray mergePay = orderData.optJSONArray("merge_pay");
        String source = orderData.optString("source", "");
        
        String otJsonStr = orderData.optString("ot_json", "{}");
        JSONObject otJson = JSONObject.fromObject(otJsonStr);
        String otherInfoStr = otJson.optString("other_info", "[]");
        JSONArray otherInfo = JSONArray.fromObject(otherInfoStr);

        // ECO外卖是否启用微信外卖混合支付，0：否，1：是， 默认0'
        String sfqywxwmhhzf = InitDataListener.ggcsMap.get("POS_ECO_SFQYWXWMHHZF");
        if (Util.isNullOrEmpty(sfqywxwmhhzf)) {
            sfqywxwmhhzf = "0";
        }

        if ("1".equals(sfqywxwmhhzf)) {
            // 2为eco 微生活,企迈小程序外卖，与原自运营外卖冲突，较为特殊
            if ("3".equals(source) && otherInfo.size() > 0 && null != otherInfo && isPlatform != 0) {
                orderData.put("isplatform", 2);
                isPlatform = 2;
            }
            // 3为eco 企迈小程序堂食
            if ("6".equals(source) && otherInfo.size() > 0 && null != otherInfo && isPlatform != 0) {
                orderData.put("isplatform", 3);
                isPlatform = 3;
            }
        }

        //sqtNeedInvoice 是否需要开发票 1不需要开发票 2需开发票 0降级；
        String sqtNeedInvoice=otJson.optString("sqtNeedInvoice","2");
        if("1".equals(sqtNeedInvoice)){
            orderData.put("need_invoice","N");
        }

        LOGGER.info("第三步：保存订单信息");
        EcoBtYdd eby = jointYdd(orderData);
        ecoOrderMapper.insertBtYdd(eby);

        inparam.put("channel", eby.getYl4());
        inparam.put("jgxh", InitDataListener.ggcsMap.get("FDJGXH"));
        if (eby.getYl4().equalsIgnoreCase("WX02")) {
            inparam.put("ddlx", "wx");
        } else {
            inparam.put("ddlx", "wm");
        }


        List<EcoBtYdxm2> bymx = null;

        try {
            bymx = joinBtYdxm2(orderData);
        } catch (IllegalArgumentException e) {
            LOGGER.error("保存订单明细表失败", e);
            data.setSuccess("false");
            data.setMsg(e.getMessage());
            return data;
        }

        if (null != bymx && bymx.size() > 0)
            ecoOrderMapper.insertBtYdxm2(bymx);
        LOGGER.info("保存订单明细表成功");
        List<EcoOrderdiscount> ecoOrderdiscounts = joinEcoOrderDiscount(orderData);
        if (ecoOrderdiscounts.size() > 0)
            ecoOrderMapper.insertEcoOrderdiscount(ecoOrderdiscounts);
        LOGGER.info("保存订单活动明细成功");

        if(isPlatform == 0){
            LOGGER.info("第四步：保存订单付款信息");
            List<EcoBtPayments> payments = joinBtPayments(orderData);
            if (null != payments && payments.size() > 0)
                ecoOrderMapper.insertBtPayments(payments);
            LOGGER.info("保存订单付款明细成功");
        }

        // 微生活小程序外卖，与之前自运营外卖处理付款类似，但是单独处理吧，一起太乱了
        if(isPlatform == 2 || isPlatform == 3){
            LOGGER.info("第四步：保存订单付款信息");
            List<EcoBtPayments> payments = joinBtPaymentsWsh(orderData);
            if (null != payments && payments.size() > 0)
                ecoOrderMapper.insertBtPayments(payments);
            LOGGER.info("保存订单付款明细成功");
        }


        LOGGER.info("第五步：订单转账单逻辑处理");

        //ECO接单模式	1：正常模式; 2：虚拟菜品模式; 3：异常接单模式
        String ecoReceivePattern = "1";
        if (null != InitDataListener.ggcsMap.get("ECO_RECEIVE_PATTERN") && !"".equals(InitDataListener.ggcsMap.get("ECO_RECEIVE_PATTERN"))) {
            ecoReceivePattern = InitDataListener.ggcsMap.get("ECO_RECEIVE_PATTERN");
        }

        Integer irt = -1;

        String outOrderIdInDB = inparam.get("outOrderIdInDB");
        String kdzdbh = billNoData.getKdzdbh();
        LOGGER.info("订单转账单过程参数" + outOrderIdInDB + "；" + kdzdbh + "；" + billNoData.getJzzdbh() + "；" + billNoData.getLsdh() + "；" + 1 + "；" + DateUtil.parseDate(inparam.get("bbrq")) + "；" + inparam.get("rybh") + "；" + "99" + "；" + Integer.parseInt(inparam.get("ygdlcs")));
        if ("2".equals(ecoReceivePattern)) {
            irt = ecoOrderMapper.ecoOrderToBillNoItem(outOrderIdInDB, kdzdbh, billNoData.getJzzdbh(), billNoData.getLsdh(), 1, DateUtil.parseDate(inparam.get("bbrq")),
                    inparam.get("rybh"), "99", Integer.parseInt(inparam.get("ygdlcs")));
        } else {
            irt = ecoOrderMapper.ecoOrderToBill(outOrderIdInDB, kdzdbh, billNoData.getJzzdbh(), billNoData.getLsdh(), 1, DateUtil.parseDate(inparam.get("bbrq")),
                    inparam.get("rybh"), "99", Integer.parseInt(inparam.get("ygdlcs")));
        }
        LOGGER.info("执行订单转账单过程返回" + billNoData.toString() + ",返回=" + irt);

        if (!irt.equals(0)) {
            String codemsg = processEcoTOBillCode(irt);
            if (!orderData.optString("is_refund").isEmpty() && orderData.optString("is_refund").equals("1") ) {
                if (irt.equals(-100) || irt.equals(-200)) {
                    // 订单已经完成正常接单，但是订单存在退单数据，异常接单接口还需处理退单数据，所以不能抛出异常，需要正常返回
                    // 无须处理打印问题在于
//            		inparam.put("need_print", "0");
//            		data.setSuccess("true");
                    data.setTagstr("101");
                    return data;
                }
            } else {
                if(isDebugMode()){
                   LOGGER.error("接单失败：订单转账单失败，错误信息: " + codemsg);
                }else {
                    throw new EcoException("接单失败：订单转账单失败，错误信息: " + codemsg);
                }
            }
        }

        // 关联加价菜
        List<BtYdxm2> jjcList = firstPayMapper.getJjcidList(outOrderIdInDB);
        for (BtYdxm2 jjc : jjcList) {
            firstPayMapper.updateYdJjcrwid(kdzdbh, jjc.getYdxm2id(), jjc.getJjcydxm2id());
        }

        //更新tcid
        firstPayMapper.updateTcid(kdzdbh);

        // 统一调冲减沽清接口
        DecSaleOut(data, inparam, orderData);
        if (!data.getSuccess().equals("true")) {
            throw new EcoException("接单失败:" + data.getMsg());
        }

        data.setSuccess("true");
        data.setMsg("接单成功");
        data.setCheck_id(kdzdbh);
        return data;
    }

    /**
     * ECO 接口具体业务实现 参照微生活小程序落单接口，略作改动
     *
     * @param orderData
     * @return
     */
    @Transactional
    public EcoRepData orderPrecheck(JSONObject orderData) {
        return null;
        // // ECO预定账单编号
        // String outOrderId = orderData.optString("outorderid");
        // // 我们系统内部预定账单编号，规则：增加一个前缀
        // String outOrderIdInDB = Constant.ECOBILL_PREFIX + outOrderId;
        // // 创建返回数据对象
        // EcoRepData data = new EcoRepData();
        // // 默认失败
        // data.setSuccess("false");
        // data.setMsg("接单失败：未知原因");
        // LOGGER.info("门店开始接单，平台预定账单编号：" + outOrderId);
        // // **************************************************
        // // 第一步：检查门店状态
        // // **************************************************
        // LOGGER.info("第一步：检查门店状态……");
        // // 可能没有开店、没有开班、已经打烊等等
        // HashMap<String,String> param = new HashMap<String,String>();
        // EcoRepData dataShopStatus = shopStatus(param);
        // String bbrq = param.get("bbrq");
        // if (dataShopStatus.getSuccess()!="true") {
        // data.setMsg("接单失败：" + dataShopStatus.getMsg());
        // return data;
        // }
        // TqJtztk jtzt = ecoOrderMapper.getJtZtk(DateUtil.parseDate(bbrq));
        // if(null==jtzt){
        // data.setMsg("接单失败：未获取到登录人员，请确认门店是否登录");
        // return data;
        // }
        // // **************************************************
        // // 第二步：校验订单当前状态
        // if(!checkOrderStatus(outOrderIdInDB, data)){
        // return data;
        // }
        // if(!checkDishInPos(outOrderIdInDB, orderData, data)){
        // return data;
        // }
        // LOGGER.info("第三步：保存订单信息");
        // EcoBtYdd eby = jointYdd(orderData);
        // ecoOrderMapper.insertBtYdd(eby);
        // List<EcoBtYdxm2> bymx = joinBtYdxm2(orderData);
        // if(null!=bymx &&bymx.size() > 0)
        // ecoOrderMapper.insertBtYdxm2(bymx);
        // LOGGER.info("保存订单明细表成功");
        // List<EcoOrderdiscount> ecoOrderdiscounts =
        // joinEcoOrderDiscount(orderData);
        // if(ecoOrderdiscounts.size() > 0)
        // ecoOrderMapper.insertEcoOrderdiscount(ecoOrderdiscounts);
        // LOGGER.info("保存订单活动明细成功");
        //
        // LOGGER.info("第四步：订单转账单逻辑处理");
        //
        // Integer irt =
        // ecoOrderMapper.ecoOrderToBill(outOrderIdInDB,billNoData.getKdzdbh(),billNoData.getJzzdbh(),billNoData.getLsdh(),
        // 1, DateUtil.parseDate(bbrq),jtzt.getRybh(),"99",
        // Integer.parseInt(jtzt.getYgdlcs()));
        // LOGGER.info("执行订单转账单过程返回" + billNoData.toString() + ",返回=" + irt);
        //
        // if(!irt.equals(0)){
        // String codemsg = processEcoTOBillCode(irt);
        // throw new EcoException("接单失败：订单转账单失败，错误信息: " + codemsg);
        // }
        // //发送打印
        // sendPrint(outOrderIdInDB,bbrq,"jzd");
        // //发送后厨
        // sendPrintKichen(billNoData.getKdzdbh());
        // data.setSuccess("true");
        // data.setMsg("接单成功");
        // data.setCheck_id(billNoData.getKdzdbh());
        // return data;
    }

    @Override
    public EcoRepData printOrder(JSONObject orderData) {
        String shopreject_reason = orderData.optString("shopreject_reason");// 拒单原因
        // ECO预定账单编号
        String outOrderId = orderData.optString("outorderid");
        // 我们系统内部预定账单编号，规则：增加一个前缀
        String outOrderIdInDB = Constant.ECOBILL_PREFIX + outOrderId;
        // 创建返回数据对象
        EcoRepData data = new EcoRepData();
        // 默认失败
        data.setSuccess("false");
        data.setMsg("打印失败：未知原因");
        LOGGER.info("开始打印，平台预定账单编号：" + outOrderId);
        // **************************************************
        // 第一步：检查门店状态
        // **************************************************
        LOGGER.info("第一步：检查门店状态……");
        // 可能没有开店、没有开班、已经打烊等等
        HashMap<String, String> param = new HashMap<String, String>();
        EcoRepData dataShopStatus = shopStatus(param);
        String bbrq = param.get("bbrq");
        if (dataShopStatus.getSuccess() != "true") {
            data.setMsg("打印失败：" + dataShopStatus.getMsg());
            return data;
        }
        TqJtztk jtzt = ecoOrderMapper.getJtZtk(DateUtil.parseDate(bbrq));
        if (null == jtzt) {
            data.setMsg("打印失败：未获取到登录人员，请确认门店是否登录");
            return data;
        }
        // **************************************************
        // 第二步：校验订单当前状态
        if (!checkPrintOrderStatus(outOrderIdInDB, data)) {
            return data;
        }

        // 检查是否是历史订单
        if (!checkPreZDKInfo(outOrderIdInDB, data)) {
            return data;
        }
        String tempStr = InitDataListener.ggcsMap.get("POS_ECO_REPAIRZDPRINTQD");
        String ecoPringType = InitDataListener.ggcsMap.get("ECO_PRING_TYPE");
        // 乡村基使用老打印模式，如果参数不设置默认走新打印或者dll打印
        if ("3".equals(ecoPringType)) {
            HashMap<String, String> inparam = new HashMap<String, String>();

            inparam.put("outOrderIdInDB", outOrderIdInDB);
            inparam.put("jgxh", InitDataListener.ggcsMap.get("FDJGXH"));
            inparam.put("bbrq", bbrq);
            inparam.put("channel", data.getSource());
            if (data.getSource().equalsIgnoreCase("WX02")) {
                inparam.put("ddlx", "wx");
            } else {
                inparam.put("ddlx", "wm");
            }
            // 乡村基版本 xz 结账单， qx 取消单
            if (data.getCheck_id().equals("7")) {
                inparam.put("dylx", "qx");
            } else {
                inparam.put("dylx", "xz");
            }
            execPrintSingle(inparam);
        } else {
            if (data.getCheck_id().equals("7")) {
                if (!(null != data.getIshis() && data.getIshis().equals("1")) && (null != tempStr && "Y".equals(tempStr))) {
                    sendQXPrintQD(data, data.getCheck_id(), data.getBbrq());
                }
                sendPrint(data, outOrderIdInDB, bbrq, "zdqx");
            } else {
                if (!(null != data.getIshis() && data.getIshis().equals("1")) && (null != tempStr && "Y".equals(tempStr))) {
                    sendPrintQD(data, data.getCheck_id(), data.getBbrq());
                }
                sendPrint(data, outOrderIdInDB, bbrq, "jzd");
            }
        }
        data.setSuccess("true");
        data.setMsg("打印成功");
        return data;
    }

    public EcoRepData orderbackBefore(EcoRepData data, Map<String, String> inparam, JSONObject orderData) {
        // ECO预定账单编号
        String outOrderId = orderData.optString("outorderid");
        // 我们系统内部预定账单编号，规则：增加一个前缀
        String outOrderIdInDB = Constant.ECOBILL_PREFIX + outOrderId;
        // 默认失败
        data.setSuccess("false");
        data.setMsg("退单失败：未知原因");
        LOGGER.info("门店开始退单，平台预定账单编号：" + outOrderId);
        // **************************************************
        // 第一步：检查门店状态
        // **************************************************
        LOGGER.info("第一步：检查门店状态……");
        // 可能没有开店、没有开班、已经打烊等等
        HashMap<String, String> param = new HashMap<String, String>();
        EcoRepData dataShopStatus = shopStatus(param);
        String bbrq = param.get("bbrq");
        if (dataShopStatus.getSuccess() != "true") {
            data.setMsg("退单失败：" + dataShopStatus.getMsg());
            return data;
        }
        TqJtztk jtzt = ecoOrderMapper.getJtZtk(DateUtil.parseDate(bbrq));
        if (null == jtzt) {
            data.setMsg("退单失败：未获取到登录人员，请确认门店是否登录");
            return data;
        }

        // 获取退单是否单品退单
        JSONArray orderParts = orderData.optJSONArray("orderPart");
        data.setIsOrderPart("N");
        double refundPayTotal = 0;
        double refundTotal = 0;
        if (null != orderParts && orderParts.size() > 0) {
            for (int j = 0; j < orderParts.size(); j++) {
                double refund_price = 0.00;
                double partcount = 0.0;
                if ("2".equals(orderParts.getJSONObject(j).optString("partstatus"))) {
                    if (null != orderParts.getJSONObject(j).optString("refund_price")) {
                        refund_price = orderParts.getJSONObject(j).optDouble("refund_price", 0);
                    }
                    if (null != orderParts.getJSONObject(j).optString("partcount")) {
                        partcount = orderParts.getJSONObject(j).optDouble("partcount", 0);
                    }
                    refundTotal = ArithUtil.add(refundTotal, ArithUtil.mul(refund_price, partcount));
                }
            }
            if (refundTotal > 0) {
                data.setIsOrderPart("Y");
            } else {
                data.setMsg("退单失败：部分退款退款金额小于等于0");
                data.setTagstr("101");
                return data;
            }
        }

        // **************************************************
        // 第二步：校验订单当前状态
        if (!checkOrderBackStatus(outOrderIdInDB, data)) {
            return data;
        }
        // 检查是否是历史订单
        if (!checkPreZDKInfo(outOrderIdInDB, data)) {
            return data;
        }

        if(data.getIsPlatform().equals(0) && data.getIsOrderPart().equals("Y")) {
            if(null==orderData.get("payinfos")){
                data.setMsg("退单失败：自运营外卖部分退款退款没有包含付款流水！");
                return data;
            }
            JSONArray payParts = orderData.optJSONArray("payinfos");
            String payindex = "";
            List payindexList = new ArrayList();
            if (null != payParts && payParts.size() > 0) {
                for (int j = 0; j < payParts.size(); j++) {
                    refundPayTotal = ArithUtil.add(refundPayTotal,payParts.getJSONObject(j).optDouble("amount"));
                    if(payParts.getJSONObject(j).optDouble("amount") < payParts.getJSONObject(j).optDouble("storepay")){
                        data.setMsg(String.format("退单失败，%s退款金额%f不能小于实退金额%f！", payParts.getJSONObject(j).optString("pay_name"),
                                payParts.getJSONObject(j).optDouble("amount"),payParts.getJSONObject(j).optDouble("storepay")));
                        return data;
                    }
                    payindex = payParts.getJSONObject(j).optString("payindex","");
                    if("".equals(payindex)){
                        data.setMsg("退单失败，自运营外卖付款流水的付款序号不能为空！");
                        return data;
                    }
                    if(payindexList.contains(payindex)){
                        data.setMsg("退单失败，自运营外卖付款流水的付款序号不能重复！");
                        return data;
                    }
                    payindexList.add(payindex);
                }
                if (refundPayTotal <= 0) {
                    data.setMsg("退单失败：自运营外卖部分退款付款流水必须大于0");
                    return data;
                }

            }

            if(refundPayTotal!=refundTotal){
                data.setMsg("退单失败：自运营外卖部分退款付款流水退款金额不等于明细退款金额");
                return data;
            }

        }
        inparam.put("outOrderId", outOrderId);
        inparam.put("outOrderIdInDB", outOrderIdInDB);
        inparam.put("bbrq", bbrq);
        if (null != data.getIshis() && data.getIshis().equals("1")) {
            LOGGER.info("历史订单");
            inparam.put("rybh", data.getFwyh());
            inparam.put("ygdlcs", data.getYgdlcs());
            if (!DateUtil.formatTimestamp(bbrq, "yyyy-mm-dd").equals(DateUtil.formatTimestamp(data.getBbrq(), "yyyy-mm-dd"))) {
                data.setMsg("退单失败：非当前报表日期订单");
                return data;
            }

        } else {
            LOGGER.info("非历史订单");
            inparam.put("rybh", jtzt.getRybh());
            inparam.put("ygdlcs", jtzt.getYgdlcs());
        }
        data.setSuccess("true");
        return data;
    }

    @Transactional
    public EcoRepData orderbackProcess(BillNoData billNoData, EcoRepData data, Map<String, String> inparam, JSONObject orderData, BillNoData partBillNoData) {
        String shopreject_reason = orderData.optString("shopreject_reason");// 拒单原因
        List<Map<String, String>> zdks = null;
        if (null != data.getIshis() && data.getIshis().equals("1")) {
            zdks = ecoOrderMapper.getZdByYddhhis(inparam.get("outOrderIdInDB"));
        } else {
            zdks = ecoOrderMapper.getZdByYddh(inparam.get("outOrderIdInDB"));
        }
        HashMap<String, Object> invoiceMap = new HashMap<String, Object>();
        if (null != zdks && zdks.size() > 0) {
            invoiceMap.put("yhfkje", zdks.get(0).get("yhfkje"));
            invoiceMap.put("kdzdbh", zdks.get(0).get("kdzdbh"));
            data.setCheck_id(zdks.get(0).get("kdzdbh"));
            invoiceMap.put("sfkfp", zdks.get(0).get("sfkfp"));
            invoiceMap.put("dh", zdks.get(0).get("dh"));
            invoiceMap.put("bbrq", zdks.get(0).get("kdbbrq"));
            invoiceMap.put("kfpje", zdks.get(0).get("kfpje"));
            invoiceMap.put("wmtype", zdks.get(0).get("wmtype"));

            inparam.put("channel", zdks.get(0).get("source"));
            inparam.put("jgxh", InitDataListener.ggcsMap.get("FDJGXH"));
            if (zdks.get(0).get("source").equalsIgnoreCase("WX02")) {
                inparam.put("ddlx", "wx");
            } else {
                inparam.put("ddlx", "wm");
            }

        } else {
            throw new EcoException("退单失败：错误信息：账单不存在！");
        }
        LOGGER.info("invoiceMap" + invoiceMap.toString());
        // 执行部分退单
        if ("Y".equals(data.getIsOrderPart())) {
            List<EcoBtYdxm1> bymx = joinBtYdxm1(orderData);
            if (bymx.size() <= 0) {
                throw new EcoException("退单失败：错误信息:部分退款菜品列表为空！");
            }
            ecoOrderMapper.insertBtYdxm1(bymx);
            LOGGER.info("插入预定明细1成功！");

            if(data.getIsPlatform()==0){
                LOGGER.info("保存部分付款订单付款信息");
                List<EcoBtPayments> payments = joinBtPayments(orderData);
                if (null != payments && payments.size() > 0)
                    ecoOrderMapper.insertBtPaymentsPart(payments);
                LOGGER.info("保存订单付款明细成功");
            }

            if(data.getIsPlatform() == 2 || data.getIsPlatform() == 3){
                LOGGER.info("保存部分付款订单付款信息");
                List<EcoBtPayments> payments = joinBtPaymentsWsh(orderData);
                if (null != payments && payments.size() > 0)
                    ecoOrderMapper.insertBtPaymentsPart(payments);
                LOGGER.info("保存订单付款明细成功");
            }
            Integer irt = -1;
            // 执行账单部分退款原单冲减
            if (null != data.getIshis() && data.getIshis().equals("1")) {
                irt = ecoOrderMapper.cancelecoBillhis(inparam.get("outOrderIdInDB"), billNoData.getKdzdbh(), billNoData.getJzzdbh(), billNoData.getLsdh(), 1, DateUtil.parseDate(inparam.get("bbrq")),
                        inparam.get("rybh"), "99", Integer.parseInt(inparam.get("ygdlcs")), shopreject_reason);

            } else {
                irt = ecoOrderMapper.cancelecoBill(inparam.get("outOrderIdInDB"), billNoData.getKdzdbh(), billNoData.getJzzdbh(), billNoData.getLsdh(), 1, DateUtil.parseDate(inparam.get("bbrq")),
                        inparam.get("rybh"), "99", Integer.parseInt(inparam.get("ygdlcs")), shopreject_reason);
            }
            LOGGER.info("执行账单部分退款原单冲减过程返回：" + billNoData.toString() + "，返回=" + irt);
            if (!irt.equals(0)) {
                String codemsg = processCancelEcoBillCode(irt);
                throw new EcoException("退单失败：部分退单原单冲减过程返回失败，错误信息: " + codemsg);
            }
            // 执行账单部分退款新账单生成
            String ecoReceivePattern = "1";
            if (null != InitDataListener.ggcsMap.get("ECO_RECEIVE_PATTERN") && !"".equals(InitDataListener.ggcsMap.get("ECO_RECEIVE_PATTERN"))) {
                ecoReceivePattern = InitDataListener.ggcsMap.get("ECO_RECEIVE_PATTERN");
            }
            LOGGER.info("订单转账单过程参数" + inparam.get("outOrderIdInDB") + "；" + billNoData.getKdzdbh() + "；" + billNoData.getJzzdbh() + "；" + billNoData.getLsdh() + "；" + 1 + "；" + DateUtil.parseDate(inparam.get("bbrq")) + "；" + inparam.get("rybh") + "；" + "99" + "；" + Integer.parseInt(inparam.get("ygdlcs")));
            if("2".equals(ecoReceivePattern)) {
                if (null != data.getIshis() && data.getIshis().equals("1")) {
                    irt = ecoOrderMapper.singlecancelecobillNoItemHis(inparam.get("outOrderIdInDB"), partBillNoData.getKdzdbh(), partBillNoData.getJzzdbh(), partBillNoData.getLsdh(), 1,
                            DateUtil.parseDate(inparam.get("bbrq")), inparam.get("rybh"), "99", Integer.parseInt(inparam.get("ygdlcs")), shopreject_reason);
                } else {
                    irt = ecoOrderMapper.singlecancelecobillNoItem(inparam.get("outOrderIdInDB"), partBillNoData.getKdzdbh(), partBillNoData.getJzzdbh(), partBillNoData.getLsdh(), 1,
                            DateUtil.parseDate(inparam.get("bbrq")), inparam.get("rybh"), "99", Integer.parseInt(inparam.get("ygdlcs")), shopreject_reason);
                }
            } else {
                if (null != data.getIshis() && data.getIshis().equals("1")) {
                    irt = ecoOrderMapper.singlecancelecobillhis(inparam.get("outOrderIdInDB"), partBillNoData.getKdzdbh(), partBillNoData.getJzzdbh(), partBillNoData.getLsdh(), 1,
                            DateUtil.parseDate(inparam.get("bbrq")), inparam.get("rybh"), "99", Integer.parseInt(inparam.get("ygdlcs")), shopreject_reason);
                } else {
                    irt = ecoOrderMapper.singlecancelecobill(inparam.get("outOrderIdInDB"), partBillNoData.getKdzdbh(), partBillNoData.getJzzdbh(), partBillNoData.getLsdh(), 1,
                            DateUtil.parseDate(inparam.get("bbrq")), inparam.get("rybh"), "99", Integer.parseInt(inparam.get("ygdlcs")), shopreject_reason);
                }
            }

            LOGGER.info("执行账单部分退单新账单生成过程返回：" + billNoData.toString() + "，返回=" + irt);
            if (!irt.equals(0)) {
                String codemsg = processCancelEcoBillCode(irt);
                throw new EcoException("退单失败：部分退单新账单生成返回失败，错误信息: " + codemsg);
            }
            else{
                //开始更新订单表的配送费信息
                double package_fee = orderData.optDouble("package_fee",0.00);
                if(package_fee > 0){
                  ecoOrderMapper.updateYddPackageBoxFee(package_fee, inparam.get("outOrderIdInDB"));
                }
            }
        } else {
            Integer irt = -1;
            // 执行账单整单退款原单冲减
            if (null != data.getIshis() && data.getIshis().equals("1")) {
                irt = ecoOrderMapper.cancelecoBillhis(inparam.get("outOrderIdInDB"), billNoData.getKdzdbh(), billNoData.getJzzdbh(), billNoData.getLsdh(), 1, DateUtil.parseDate(inparam.get("bbrq")),
                        inparam.get("rybh"), "99", Integer.parseInt(inparam.get("ygdlcs")), shopreject_reason);

            } else {
                irt = ecoOrderMapper.cancelecoBill(inparam.get("outOrderIdInDB"), billNoData.getKdzdbh(), billNoData.getJzzdbh(), billNoData.getLsdh(), 1, DateUtil.parseDate(inparam.get("bbrq")),
                        inparam.get("rybh"), "99", Integer.parseInt(inparam.get("ygdlcs")), shopreject_reason);
            }
            LOGGER.info("执行账单整单退单过程返回：" + billNoData.toString() + "，返回=" + irt);
            if (!irt.equals(0)) {
                String codemsg = processCancelEcoBillCode(irt);
                throw new EcoException("退单失败：整单退单过程部返回失败，错误信息: " + codemsg);
            }
        }
        // 取消发票 暂时不支持发票部分退，所以直接全退
        iInVoiceService.CancelInvoice(invoiceMap);

        // 统一调新增 冲减沽清接口
        AddSaleOut(data, inparam, orderData);
        if (!data.getSuccess().equals("true")) {
            throw new EcoException("退单失败：" + data.getMsg());
        }
        data.setSuccess("true");
        data.setMsg("退单成功");
        return data;
    }

    @Override
    @Transactional
    public EcoRepData sendover(EcoRepData data, Map<String, String> inparam, JSONObject orderData) {
        data.setSuccess("true");
        data.setMsg("完成成功");
        return data;
    }

    @Override
    public EcoRepData printCommon(EcoRepData data, Map<String, String> inparam) {
        String channel = inparam.get("channel");
        if (inparam.get("ptype").equals("zdqx")) {
            if (!(null != data.getIshis() && data.getIshis().equals("1"))) {
                // 打印取消清单
                sendQXPrintQD(data, inparam.get("newKdzdbh"), inparam.get("bbrq"));
                // 发送后厨
                sendPrintKichen(data, inparam.get("newKdzdbh"), channel);
            }
            // 发送打印
            if(!"0".equals(inparam.get("print_jzd"))){
                sendPrint(data, inparam.get("outOrderIdInDB"), inparam.get("bbrq"), "zdqx");
            }
        } else {
            if (!(null != data.getIshis() && data.getIshis().equals("1")) && !(null != data.getIsOrderPart() && "Y".equals(data.getIsOrderPart()))) {
                // 发送清单
                sendPrintQD(data, inparam.get("newKdzdbh"), inparam.get("bbrq"));
                // 发送后厨
                if (inparam.containsKey("need_send_kitchen")) {
                    if (inparam.get("need_send_kitchen").equals("1")) {
                        sendPrintKichen(data, inparam.get("newKdzdbh"), channel);
                    }
                } else {
                    // 兼容以前处理方式
                    sendPrintKichen(data, inparam.get("newKdzdbh"), channel);
                }
            }
            // 发送打印
            if (inparam.containsKey("need_print_bill")) {
                if (inparam.get("need_print_bill").equals("1")) {
                    if(!"0".equals(inparam.get("print_jzd"))) {
                        sendPrint(data, inparam.get("outOrderIdInDB"), inparam.get("bbrq"), "jzd");
                    }
                }
            } else {
                if(!"0".equals(inparam.get("print_jzd"))){
                    sendPrint(data, inparam.get("outOrderIdInDB"), inparam.get("bbrq"), "jzd");
                }
            }
        }
        return data;
    }

    @Override
    @Transactional
    public EcoRepData orderback(JSONObject orderData) {
        return null;
        // String shopreject_reason =
        // orderData.optString("shopreject_reason");//拒单原因
        // // ECO预定账单编号
        // String outOrderId = orderData.optString("outorderid");
        // // 我们系统内部预定账单编号，规则：增加一个前缀
        // String outOrderIdInDB = Constant.ECOBILL_PREFIX + outOrderId;
        // // 创建返回数据对象
        // EcoRepData data = new EcoRepData();
        // // 默认失败
        // data.setSuccess("false");
        // data.setMsg("退单失败：未知原因");
        // LOGGER.info("门店开始退单，平台预定账单编号：" + outOrderId);
        // // **************************************************
        // // 第一步：检查门店状态
        // // **************************************************
        // LOGGER.info("第一步：检查门店状态……");
        // // 可能没有开店、没有开班、已经打烊等等
        // HashMap<String,String> param = new HashMap<String,String>();
        // EcoRepData dataShopStatus = shopStatus(param);
        // String bbrq = param.get("bbrq");
        // if (dataShopStatus.getSuccess()!="true") {
        // data.setMsg("退单失败：" + dataShopStatus.getMsg());
        // return data;
        // }
        // TqJtztk jtzt = ecoOrderMapper.getJtZtk(DateUtil.parseDate(bbrq));
        // if(null==jtzt){
        // data.setMsg("退单失败：未获取到登录人员，请确认门店是否登录");
        // return data;
        // }
        // // **************************************************
        // // 第二步：校验订单当前状态
        // if(!checkOrderBackStatus(outOrderIdInDB, data)){
        // return data;
        // }
        //
        //
        // LOGGER.info("第四步：账单退单逻辑处理");

        //
        //
        // Integer irt =
        // ecoOrderMapper.cancelecoBill(outOrderIdInDB,billNoData.getKdzdbh(),billNoData.getJzzdbh(),billNoData.getLsdh(),
        // 1, DateUtil.parseDate(bbrq),jtzt.getRybh(),"99",
        // Integer.parseInt(jtzt.getYgdlcs()),shopreject_reason);
        // LOGGER.info("执行账单退单过程返回" + billNoData.toString() + ",返回=" + irt);
        // if(!irt.equals(0)){
        // String codemsg = processCancelEcoBillCode(irt);
        // throw new EcoException("接单失败：退单过程返回失败，错误信息: " + codemsg);
        // }
        // //发送打印
        // sendPrint(outOrderIdInDB,bbrq,"zdqx");
        // //发送后厨
        // sendPrintKichen(billNoData.getKdzdbh());
        // data.setSuccess("true");
        // data.setMsg("退单成功");
        // return data;
    }

    @Override
    public EcoRepData ordernum(HttpServletRequest request) {
        String msgBody = "ACTION=ordernum|FDJGXH=" + UploadGloVar.getOrganizeID() + "|YDDH=" + "";
        msgBody = msgBody + "|neworder1=" + request.getParameter("neworder1");
        msgBody = msgBody + "|receiveorder1=" + request.getParameter("receiveorder1");// 制作中
        msgBody = msgBody + "|deliveryorder1=" + request.getParameter("deliveryorder1");// 配送中
        msgBody = msgBody + "|waitorder_hand1=" + request.getParameter("waitorder_hand1");
        msgBody = msgBody + "|endorder1=" + request.getParameter("endorder1");// 配送完成
        msgBody = msgBody + "|quitorder1=" + request.getParameter("quitorder1");// 已退订单
        DatagramUtil.sendUdpMsg(msgBody, Integer.valueOf("5634").intValue(), "127.0.0.1");

        Integer neworder1 = NumberUtils.toInt(request.getParameter("neworder1"), 0);

        return null;
    }

    /**
     * 处理预定账单数据
     *
     * @param orderInfo
     * @return
     */
    public EcoBtYdd jointYdd(JSONObject orderInfo) {
        double yl2 = 0d;// 优惠总金额
        double shop_rate = 0d;// 门店承担优惠金额
        EcoBtYdd by = new EcoBtYdd();

        JSONArray activityInfos = orderInfo.optJSONArray("activityInfo");
        for (int i = 0; i < activityInfos.size(); i++) {
            yl2 = ArithUtil.add(yl2, activityInfos.getJSONObject(i).optDouble("discount_acmount"));
            shop_rate = ArithUtil.add(shop_rate, activityInfos.getJSONObject(i).optDouble("shop_rate"));
        }
        // 添加预订单号加上TS前缀
        by.setYddh(Constant.ECOBILL_PREFIX + orderInfo.optString("outorderid"));
        by.setLxr(orderInfo.optString("contact", ""));
        by.setLxrdh(orderInfo.optString("mobile", ""));
        by.setYdrs(orderInfo.optInt("dinners_number"));
        by.setMen(0);
        by.setWomen(0);
        by.setEldernum(0);
        by.setChildnum(0);
        by.setTotalprice(orderInfo.optDouble("totalprice"));
        by.setYdrq(DateUtil.getNowDateYYDDMM());
        by.setQdsj(DateUtil.getNowDateYYDDMMHHMMSS());
        by.setDdsj(orderInfo.optString("createtime", ""));
        by.setDdzt("5"); // 默认状态
        by.setYl1(orderInfo.optDouble("totalprice"));
        by.setPaychannel(orderInfo.optString("wpaytype"));
        by.setYl2(yl2);
        by.setYl3(orderInfo.optDouble("order_delivery_pay"));
        by.setShop_real_amount(orderInfo.optDouble("shopfee"));
        by.setTaxpayerid(orderInfo.optString("taxer_id"));
        by.setRecieptdept(orderInfo.optString("invoiceinfo"));
        by.setPick_type(orderInfo.optInt("ordertype"));
        by.setKhjlbh(orderInfo.optString("order_index"));
        by.setKhjlmc(orderInfo.optString("sname"));
        by.setPackage_box_fee(orderInfo.optDouble("package_fee"));
        by.setYl5(orderInfo.optString("conTactMem"));
        by.setKhtmtz(orderInfo.optString("sourcename"));
        by.setKwxh(orderInfo.optString("comment"));
        by.setShop_rate(shop_rate);
        by.setPlatform_rate(ArithUtil.sub(yl2, shop_rate));
        by.setCommission_amount(orderInfo.optDouble("service_money"));
        by.setChannel(1);
        by.setMember_address(orderInfo.optString("address"));
        by.setJk(orderInfo.optString("payTypeName"));
        by.setBz(orderInfo.optString("diningtime"));
        //是否开发票
        by.setShrbh(orderInfo.optString("need_invoice","Y"));
        // 校验订单是否存在对应的渠道或者对应渠道的付款编号是否存在
        //券购买金额
        by.setYsje(orderInfo.optDouble("userPayCouponAmount",0));
        //客户实付价
        by.setZlje(orderInfo.optDouble("customer_price",0));
        String source = orderInfo.optString("source");
        if (source != null && InitDataListener.ecoTypeDicMap.containsKey(source)) {
            EcoTypeDic ecoTypeDic = InitDataListener.ecoTypeDicMap.get(source);
            by.setYl4(ecoTypeDic.getTzxcode());
            if (StringUtils.isBlank(orderInfo.optString("payTypeName"))) {
                by.setJk(ecoTypeDic.getTzxname() + "付款");
            }
        }
        // 下单来源 “0”为门店下单；“1”为线上下单；默认为“0”。V1.1新增
        if (orderInfo.containsKey("channel")) {
            int channel = orderInfo.optInt("channel", 0); // 默认为0
        }
        String orderType = orderInfo.optString("ordertype", "");
        if ("3".equals(orderType)) {
            by.setDiningway("XSMS_TS"); // 堂食
        } else if ("2".equals(orderType)) {
            by.setDiningway("XSMS_WM"); // 外带/自取
        } else {
            by.setDiningway("XSMS_WS"); // 外卖
        }

        if (ZS_DELIVERY_TYPE.contains(orderInfo.optString("delivery_type"))) {
            by.setShippingmethod(NumberUtils.toInt(orderInfo.optString("delivery_type")));
        } else {
            by.setShippingmethod(-1);
        }

        by.setZkfa(0);
        by.setParkingnum(orderInfo.optInt("isplatform", 1));

        by.setZlbh(orderInfo.optString("tablenumber"));

        // 增加美团一维码保存
        if(by.getYl4().equals("MT08")) {
            String oneDimOrderCode = NumberHelper.codeToOneDimCode(orderInfo.optString("outorderid"));
            by.setPdrbh(oneDimOrderCode);
        } else if(by.getYl4().equals("EL09")) {
            String oneDimOrderCode = "E" + InvitationCodeUtils.encodeStr(orderInfo.optString("outorderid", "0"));
            by.setPdrbh(oneDimOrderCode);

          //京东秒送一维码
        } else if("JD31".equals(by.getYl4())){
            by.setPdrbh( orderInfo.optString("outorderid"));
            //乡村基品牌，SAAS接美团饿了么平台外卖、ECO接京东秒送单子，兼容老模板
            by.setYdbc( orderInfo.optString("outorderid"));
        }
        return by;
    }

    public void joinBtYdxm2Details(List<EcoBtYdxm2> bymx,String yddh,Integer tcid, Integer tcdch, Integer dcxh, JSONArray details) {
        for (int j = 0; j < details.size(); j++) {
            EcoBtYdxm2 ydmx2 = new EcoBtYdxm2();
            ydmx2.setIsactive(0);
            ydmx2.setYddh(yddh);
            ydmx2.setXmbh(getKDishsNo(details.getJSONObject(j)));
            ydmx2.setXmmc(details.getJSONObject(j).optString("itemname"));
            ydmx2.setXmsx("CMSX_MX");

            ydmx2.setXmid(details.getJSONObject(j).optInt("cmid",-1));
            // 单价还是菜品原价
            ydmx2.setXmdj(new BigDecimal(details.getJSONObject(j).optString("originalprice")));
            ydmx2.setXmsl(new BigDecimal(details.getJSONObject(j).optString("itemcount")));
            ydmx2.setZkl(100);
            // 菜品实结金额
            ydmx2.setTotalprice(new BigDecimal(details.getJSONObject(j).optString("price")));
            ydmx2.setDwbh(details.getJSONObject(j).optString("properties"));
            ydmx2.setKwbh(details.getJSONObject(j).optString("nature"));
            // 菜品金额使用原价*数量
            ydmx2.setCmje(new BigDecimal(details.getJSONObject(j).optString("price")));

            ydmx2.setTcbh("");
            ydmx2.setTcdch(tcdch);
            ydmx2.setFzsl(0);
            ydmx2.setFzje(new BigDecimal(0));
            ydmx2.setDcxh(dcxh);
            Integer packId = details.getJSONObject(j).optInt("packid", -1);
            if (packId != -1) {
                ydmx2.setPackid(packId);
            } else
                ydmx2.setPackid(null);
            ydmx2.setTop_item_id(tcid);
            bymx.add(ydmx2);

        }
    }
    public void joinErrBtYdxm2Details(List<EcoErrorYdxm2> bymx,String yddh,Integer tcid, Integer tcdch, Integer dcxh, JSONArray details) {
        for (int j = 0; j < details.size(); j++) {
            EcoErrorYdxm2 ydmx2 = new EcoErrorYdxm2();
            ydmx2.setIsactive(0);
            ydmx2.setYddh(yddh);
            ydmx2.setXmbh(getKDishsNo(details.getJSONObject(j)));
            ydmx2.setXmmc(details.getJSONObject(j).optString("itemname"));
            ydmx2.setXmsx("CMSX_MX");

            ydmx2.setXmid(details.getJSONObject(j).optInt("cmid",-1));
            // 单价还是菜品原价
            ydmx2.setXmdj(new BigDecimal(details.getJSONObject(j).optString("originalprice")));
            ydmx2.setXmsl(new BigDecimal(details.getJSONObject(j).optString("itemcount")));
            ydmx2.setZkl(100);
            // 菜品实结金额
            ydmx2.setTotalprice(new BigDecimal(details.getJSONObject(j).optString("price")));
            ydmx2.setDwbh(details.getJSONObject(j).optString("properties"));
            ydmx2.setKwbh(details.getJSONObject(j).optString("nature"));
            // 菜品金额使用原价*数量
            ydmx2.setCmje(new BigDecimal(details.getJSONObject(j).optString("price")));

            ydmx2.setTcbh("");
            ydmx2.setTcdch(tcdch);
            ydmx2.setFzsl(0);
            ydmx2.setFzje(new BigDecimal(0));
            ydmx2.setDcxh(dcxh);
            Integer packId = details.getJSONObject(j).optInt("packid", -1);
            if (packId != -1) {
                ydmx2.setPackid(packId);
            } else
                ydmx2.setPackid(null);
            ydmx2.setTop_item_id(tcid);
            bymx.add(ydmx2);

        }
    }
    public List<EcoBtYdxm2> joinBtYdxm2(JSONObject orderInfo) {


        //预订单号
        String yddh = Constant.ECOBILL_PREFIX + orderInfo.optString("outorderid");

        int isPlatform = orderInfo.optInt("isplatform", 1);
        final JSONObject tcmx = orderInfo.optJSONObject("tcmx");
        Set kxtcItemSet=new HashSet<>();
        tcmx.forEach((k,v)->{
            kxtcItemSet.add(k);
            kxtcItemSet.addAll((Collection) v);

        });

        // 菜品列表
        List<EcoBtYdxm2> bymx = new ArrayList<EcoBtYdxm2>();
        JSONArray itemInfos = orderInfo.optJSONArray("items");
        Integer dcxh = Integer.MAX_VALUE;
        Integer tcdcxh = Integer.MAX_VALUE;
        Map<Integer,Integer> dcxhMap=new HashMap<>();
        Map<Integer,Integer> tcdcxhMap=new HashMap<>();

        final String errorItemCode = InitDataListener.ggcsMap.get("ERROR_ITEM_CODE");

        for (int j = 0; j < itemInfos.size(); j++) {
            final JSONObject itemInfo = itemInfos.getJSONObject(j);
            //目前没有明确的字段区分套餐明细和单品，暂时用这个字段
//            String properties=itemInfo.optString("properties");
//            if("1".equals(properties)){
//                continue;
//            }
            dcxh -= 1;
            EcoBtYdxm2 ydmx2 = new EcoBtYdxm2();
            ydmx2.setIsactive(0);

            ydmx2.setYddh(yddh);
            ydmx2.setXmid(itemInfo.optInt("cmid",-1));
            ydmx2.setXmbh(getKDishsNo(itemInfo));
            String xmmc = itemInfo.optString("itemname");
            String ecoIsreplacEItemsymbol = InitDataListener.ggcsMap.get("ECO_ISREPLACE_ITEMSYMBOL");
            // 乡村基是否替换菜品中的空格为+
            if ("Y".equals(ecoIsreplacEItemsymbol)) {
                xmmc = xmmc.replace(" ", "＋");
            }
            ydmx2.setXmmc(xmmc);

            ydmx2.setDcxh(dcxh);
            ydmx2.setTcdch(0);

            ydmx2.setXmsl(new BigDecimal(itemInfo.optString("itemcount")));
            // 单价还是菜品原价
            ydmx2.setXmdj(new BigDecimal(itemInfo.optString("originalprice")));
            // 菜品实结金额
            ydmx2.setTotalprice(new BigDecimal(itemInfo.optString("price")));
            // 菜品金额使用原价*数量
            ydmx2.setCmje(new BigDecimal(itemInfo.optString("price")));

            //保存ECO套餐id
            if("Y".equals(itemInfo.optString("setmeal"))){
                ydmx2.setBsetmealid(itemInfo.optString("uniqueId"));
            }

            if ("-1".equals(itemInfo.optString("bsetmeal"))) {
                if (3 == isPlatform) { //3为堂食小程序，不使用异常菜功能
                    throw new EcoException("接单失败：菜品【" + xmmc + "】门店不存在");
                } else {
                    // 可选套餐异常菜直接替换
                    if(kxtcItemSet.contains(itemInfo.optString("uniqueId"))){
                        ydmx2.setXmbh(errorItemCode);
                    }else {
                        ydmx2.setXmsx("CMSX_YC");
                    }
                    ydmx2.setXmbh(errorItemCode);
                }
            }

            if(!"CMSX_YC".equals(ydmx2.getXmsx())){
                if (CMSX_DP.equals(itemInfo.optString("cmsx"))){
                    ydmx2.setXmsx("CMSX_DP");
                }
                if (CMSX_TC.equals(itemInfo.optString("cmsx"))){
                    ydmx2.setXmsx("CMSX_TC");

                    int uniqueId = (int) (NumberUtils.toLong(itemInfo.optString("uniqueId"),-1L) & 0xFFFFFFFFL);

                    if (-1 == uniqueId) {
                        uniqueId = itemInfo.optString("uniqueId").intern().hashCode();
                    }

                    ydmx2.setTop_item_id(uniqueId);

                    if(!tcdcxhMap.containsKey(uniqueId)){
                        tcdcxh -= 1;
                        ydmx2.setTcdch(tcdcxh);

                        dcxhMap.put(uniqueId,dcxh);
                        tcdcxhMap.put(uniqueId,tcdcxh);
                    }else {
                        ydmx2.setDcxh(dcxhMap.get(uniqueId));
                        ydmx2.setTcdch(tcdcxhMap.get(uniqueId));
                    }

                    if(tcmx.containsKey(itemInfo.optString("uniqueId"))){
                        ydmx2.setIsactivity(1); //标记为可选套餐
                    }

                }

                if (CMSX_MX.equals(itemInfo.optString("cmsx"))){
                    ydmx2.setXmsx("CMSX_MX");
                    int bsetmealid = (int) (NumberUtils.toLong(itemInfo.optString("bsetmealid"), -1L) & 0xFFFFFFFFL);

                    if (-1 == bsetmealid) {
                        bsetmealid = itemInfo.optString("bsetmealid").intern().hashCode();
                    }

                    ydmx2.setTop_item_id(bsetmealid);
                    ydmx2.setIsactivity(1); //标记为可选套餐

                    if(tcdcxhMap.containsKey(bsetmealid)){
                        ydmx2.setDcxh(dcxhMap.get(bsetmealid));
                        ydmx2.setTcdch(tcdcxhMap.get(bsetmealid));
                    }else {
                        dcxhMap.put(bsetmealid,dcxh);

                        tcdcxh -= 1;
                        ydmx2.setTcdch(tcdcxh);
                        tcdcxhMap.put(bsetmealid,tcdcxh);
                    }

                }
            }

            ydmx2.setZkl(100);

            ydmx2.setDwbh(itemInfo.optString("properties"));
            ydmx2.setKwbh(itemInfo.optString("nature"));


            ydmx2.setTcbh("");
            ydmx2.setFzsl(0);
            ydmx2.setFzje(new BigDecimal(0));
            ydmx2.setNature(itemInfo.optString("nature"));
            Integer packId = itemInfo.optInt("packid" );
            if (packId != -1) {
                ydmx2.setPackid(packId);
            } else
                ydmx2.setPackid(null);
            bymx.add(ydmx2);
            //平台外卖 没有套餐明细，自运营外卖需要传套餐明细，微生活暂时也不在这里处理明细
            if(isPlatform != 1 && isPlatform != 2){
                if(null!= itemInfo.get("details") && "1".equals(itemInfo.optString("bsetmeal"))) {
                    JSONArray detaisArr = itemInfo.getJSONArray("details");
                    if (null != detaisArr && detaisArr.size() > 0) {
                        joinBtYdxm2Details(bymx, ydmx2.getYddh(),ydmx2.getXmid(),tcdcxh, dcxh, detaisArr);
                    }
                }
            }

            //添加配料菜品
           List<EcoBtYdxm2> toppinsYdxm2= getItemToppins(yddh,dcxh,itemInfo);
            if(toppinsYdxm2!=null && toppinsYdxm2.size()>0){
                bymx.addAll(toppinsYdxm2);
            }

        }
        return bymx;
    }

    private List<EcoBtYdxm2> getItemToppins(String yddh,int dcxh,JSONObject jo)  {

        List<EcoBtYdxm2> bymx = new ArrayList<>();

//        int itemCount=jo.optInt("itemcount",1);

        JSONArray toppings=jo.optJSONArray("toppings");

        if(CollectionUtils.isNotEmpty(toppings)){
            for(int i=0;i<toppings.size();i++){
                JSONObject topping=toppings.getJSONObject(i);
                String dishCode=topping.optString("tpno");

                //如果配料菜品编码为空，则不处理
                if(StringUtils.isEmpty(dishCode)){
                    LOGGER.info("配料菜品编码为空，异常菜品不处理");
                    continue;
                }

                double xmdj = topping.optDouble("tpprice", 0);
                int number=topping.optInt("count");//*itemCount;

                TsCmk cmk=orderPrecheckMapper.getDishByCode(dishCode);

                //ECO_RECEIVE_PATTERN ,1: 正常接单，菜品匹配不上就异常  ;
                //                     2: 异常接单，所有菜品都按异常菜品映射 ;
                //                     3: 异常接单，只有异常菜品才按异常菜品映射

                String ecoReceivePattern = "1";
                if (null != InitDataListener.ggcsMap.get("ECO_RECEIVE_PATTERN")
                        && !"".equals(InitDataListener.ggcsMap.get("ECO_RECEIVE_PATTERN"))) {
                    ecoReceivePattern = InitDataListener.ggcsMap.get("ECO_RECEIVE_PATTERN");
                }

                if(cmk==null) {
                  /*  if("1".equals(ecoReceivePattern)){
                        throw new IllegalArgumentException("配料菜品 " + topping.optString("tpname") + "(" + dishCode + ") POS系统不存在;");
                    }else*/
                    cmk = new TsCmk();
                    cmk.setCmid(-1);
                    cmk.setCmbh(dishCode);
                    cmk.setCmsx("CMSX_YC_J");
                }


                //使用平台菜品名称
                cmk.setCmmc1(topping.optString("tpname"));

                EcoBtYdxm2 ydxm2 = initJjcToYdxm2(yddh, cmk, xmdj, number, dcxh);
                bymx.add(ydxm2);
            }
        }
        return bymx;
    }

    private EcoBtYdxm2 initJjcToYdxm2(String yddh, TsCmk cmk, double xmdj, int xmsl, int dcxh){
        EcoBtYdxm2 ydmx2 = new EcoBtYdxm2();
        ydmx2.setIsactive(0);
        ydmx2.setYddh(yddh);
        ydmx2.setXmid(cmk.getCmid());
        ydmx2.setXmbh(cmk.getCmbh());
        ydmx2.setXmmc(cmk.getCmmc1());
        if(cmk.getCmsx().equals("CMSX_YC_J")) {
            ydmx2.setXmsx(cmk.getCmsx());
        }else {
            ydmx2.setXmsx("加价菜");
        }
        // 单价还是菜品原价
        ydmx2.setXmdj(BigDecimal.valueOf(xmdj));
        ydmx2.setXmsl(BigDecimal.valueOf(xmsl));
        ydmx2.setZkl(100);
        // 菜品实结金额
        ydmx2.setTotalprice(BigDecimal.valueOf(ArithUtil.mul(xmdj, xmsl)));
        ydmx2.setDwbh(cmk.getDwbh());
        ydmx2.setKwbh("");
        // 菜品金额使用原价*数量
        ydmx2.setCmje( BigDecimal.valueOf(ArithUtil.mul(xmdj, xmsl)));
        ydmx2.setTcbh("");
        ydmx2.setTcdch(0);
        ydmx2.setFzsl(0);
        ydmx2.setFzje(BigDecimal.valueOf(0));
        ydmx2.setDcxh(dcxh);
        ydmx2.setYl3(ydmx2.getTotalprice().toString());

        return ydmx2;
    }

    public boolean isNumeric(String str) {
        for (int i = str.length(); --i >= 0;) {
            if (!Character.isDigit(str.charAt(i))) {
                return false;
            }
        }
        return true;
    }
    public List<EcoBtPayments> joinBtPayments(JSONObject orderInfo) {
        int isPlatform = orderInfo.optInt("isplatform", 1);
        // 付款列表
        List<EcoBtPayments> payments = new ArrayList<EcoBtPayments>();
        JSONArray payinfos = orderInfo.optJSONArray("payinfos");
        JSONArray mergePay = orderInfo.optJSONArray("merge_pay");
        String payNo = "";
        for (int j = 0; j < payinfos.size(); j++) {
            EcoBtPayments btPayments = new EcoBtPayments();
            btPayments.setYddh(Constant.ECOBILL_PREFIX + orderInfo.optString("outorderid"));
            btPayments.setPay_name(payinfos.getJSONObject(j).optString("pay_name"));
            payNo = payinfos.getJSONObject(j).optString("pay_no");
            btPayments.setPay_no(payNo);
            double amount = payinfos.getJSONObject(j).optDouble("amount");
            double storePay =payinfos.getJSONObject(j).optDouble("storepay");
            double disAmount = ArithUtil.sub(amount, storePay);
            double yDisAmount = disAmount;
            //默认不拆分
            storePay = amount;
            disAmount = 0d;
            if("1973".equals(payNo)){
                //按赠送拆分
                if("1".equals(InitDataListener.ggcsMap.get("POS_MEMBALANCE_USEDIS"))) {
                    disAmount = yDisAmount;
                }//按比例拆分
                else if("2".equals(InitDataListener.ggcsMap.get("POS_MEMBALANCE_USEDIS"))) {
                    String acewillDiscount = InitDataListener.ggcsMap.get("ACEWILLDISCOUNT");
                    if (null != acewillDiscount) {
                        if (isNumeric(acewillDiscount) && Integer.parseInt(acewillDiscount) > 0 && Integer.parseInt(acewillDiscount) <= 100) {
                            double scale = Integer.parseInt(acewillDiscount);
                            double scale_pay = ArithUtil.sub(amount, ArithUtil.mul(amount, ArithUtil.div(scale, 100, 2)));
                            scale_pay = ArithUtil.round(scale_pay, 2);
                            disAmount = scale_pay;
                            storePay = ArithUtil.sub(amount, disAmount);
                        } else {
                            LOGGER.error("企迈储值未拆分:拆分比例为空");
                        }
                    } else {
                        LOGGER.error("企迈储值未拆分:未设置拆分比例");
                    }
                }
            }
            else if("1972".equals(payNo)){
                if ("1".equals(InitDataListener.ggcsMap.get("POS_MEMCREDIT_USEDIS"))) {
                    storePay = 0d;
                    disAmount = amount;
                }
            }
            btPayments.setPay_count(new BigDecimal(amount));
            btPayments.setVocount(new BigDecimal(storePay));
            btPayments.setFzzhje(new BigDecimal(yDisAmount));
            btPayments.setPay_channel(payNo);
            payments.add(btPayments);
        }
        return payments;
    }

    public List<EcoBtYdxm1> joinBtYdxm1(JSONObject orderInfo) {
        // 菜品列表
        List<EcoBtYdxm1> bymx = new ArrayList<EcoBtYdxm1>();
        JSONArray itemInfos = orderInfo.optJSONArray("orderPart");
        for (int j = 0; j < itemInfos.size(); j++) {
            if (!("2".equals(itemInfos.getJSONObject(j).optString("partstatus")))) {
                continue;
            }
            if (itemInfos.getJSONObject(j).optDouble("partcount", 0) <= 0) {
                continue;
            }
            EcoBtYdxm1 ydmx1 = new EcoBtYdxm1();
            ydmx1.setYddh(Constant.ECOBILL_PREFIX + orderInfo.optString("outorderid"));
            String itemid = "";
            itemid = getKDishsNo(itemInfos.getJSONObject(j));
            ydmx1.setXmbh(itemid);
            ydmx1.setXmmc(itemInfos.getJSONObject(j).optString("itemname"));
            // 单价还是菜品原价
            ydmx1.setYl1(new BigDecimal(itemInfos.getJSONObject(j).optString("refund_price")));
            ydmx1.setYl2(new BigDecimal(itemInfos.getJSONObject(j).optString("partcount")));
            ydmx1.setYl3(itemInfos.getJSONObject(j).optString("partstatus"));
            bymx.add(ydmx1);
        }
        return bymx;
    }

    public List<EcoOrderdiscount> joinEcoOrderDiscount(JSONObject orderInfo) {
        // 活动列表
        List<EcoOrderdiscount> ecoOrderdiscounts = new ArrayList<EcoOrderdiscount>();
        JSONArray items = orderInfo.optJSONArray("activityInfo");
        for (int j = 0; j < items.size(); j++) {
            EcoOrderdiscount item = new EcoOrderdiscount();
            item.setOrder_code(Constant.ECOBILL_PREFIX + orderInfo.optString("outorderid"));
            // ydmx2.setXmid(jo.optInt("did"));
            String activityType = items.getJSONObject(j).optString("activity_type");
            item.setDiscount_type(activityType);

            //抖音外卖没有传商家优惠活动名称
            item.setDiscount_desc(getActivityDesc(items, j));
            item.setDiscount_fee(new BigDecimal(items.getJSONObject(j).optDouble("discount_acmount")));
            item.setShop_rate(new BigDecimal(items.getJSONObject(j).optDouble("shop_rate")));
            item.setPlatform_rate(new BigDecimal(ArithUtil.sub(items.getJSONObject(j).optDouble("discount_acmount"), items.getJSONObject(j).optDouble("shop_rate"))));
            item.setActivity_id(items.getJSONObject(j).optString("activity_name"));
            item.setPay_no(items.getJSONObject(j).optString("pay_no", ""));
            ecoOrderdiscounts.add(item);
        }
        return ecoOrderdiscounts;
    }

    /**
     * 获取活动名称
     * @param items
     * @param j
     * @return
     */
    private  String getActivityDesc(JSONArray items, int j) {

        //如果有活动名称，就用活动名称，如果没有，就需要查询ts_yhfssdkc表，yhsx=69的yhfsmc1
        String activityDesc= items.getJSONObject(j).optString("activity_desc");
        if(StringUtils.isNotEmpty(activityDesc)){
            return activityDesc;
        }
        TsYhfssdk yhfs = orderPrecheckMapper.getYhfs(ECO_DISCOUNT_YHSX);
        if(yhfs!=null){
            return yhfs.getYhfsmc1();
        }
        return "";
    }

    /**
     * 下单前检查门店状态
     *
     * @return
     */
    public EcoRepData shopStatus(Map<String, String> param) {
        EcoRepData data = new EcoRepData();

        Map<String, String> bbrqMap = shopStatusMapper.findBbrq();
        String bbrq = DateUtil.getNowDateYYDDMM();
        if (null != bbrqMap && bbrqMap.size() != 0) {
            bbrq = bbrqMap.get("bbrq");
        }
        param.put("bbrq", bbrq);

        int start = shopStatusMapper.checkOpenStart(DateUtil.parseDate(bbrq));
        int end = shopStatusMapper.checkOpenEnd(DateUtil.parseDate(bbrq));
        int ld = shopStatusMapper.loginDlcs(DateUtil.parseDate(bbrq));
        int lt = shopStatusMapper.loginTccs(DateUtil.parseDate(bbrq));
        TsGgcsk il = null;
        // TsGgcsk il = firstPayMapper.getGgcsToWs("ISLOGINOUTING");
        if (null != il && "Y".equals(il.getSdnr())) {
            data.setSuccess("false");
            data.setMsg("POS系统门店正在盘点，请稍后再试");
        } else if (0 == start) {
            data.setSuccess("false");
            data.setMsg("POS系统门店未营业");
        } else if (0 < end) {
            data.setSuccess("false");
            data.setMsg("POS系统门店已打烊");
        } else if (ld <= lt) {
            data.setSuccess("false");
            data.setMsg("POS系统员工未开班");
        }/* else if (checkDysj(bbrq) > 0) {
            data.setSuccess("false");
            data.setMsg("系统日期已经大于营业日期，请POS做打烊后再进行当前操作！");
        } */else {
            data.setSuccess("true");
            data.setMsg("门店POS正常营业");
        }
        return data;
    }

    /**
     * 下单前检查是否打烊
     *
     * @param bbrq
     * @return
     */
    public int checkDysj(String bbrq) {
        String sys24yy = shopStatusMapper.getSys24yy();
        String dysjsz = shopStatusMapper.getDyxzsjd();
        Date bbrqD = DateUtil.parseDateAll(bbrq + " 00:00:00");
        if ("Y".equals(sys24yy)) {
            int day = DateUtil.daysBetween(bbrqD, DateUtil.parseDateAll(DateUtil.getNowDateYYDDMM() + " 00:00:00"));
            return day;
        } else {
            if ("".equals(dysjsz) || null == dysjsz) {
                dysjsz = "00:00:00";
            }
            Date bbrqDy = DateUtil.parseDateAll(bbrq + " " + dysjsz);
            Date dysj = DateUtil.parseDateAll(DateUtil.getNowDateYYDDMMHHMMSS());
            int day = DateUtil.daysBetween(DateUtil.getPlusDay(bbrqDy, 1), dysj);
            return day;
        }
    }

    public void sendPrint(EcoRepData data, String outOrderIdInDB, String jzbbrq, String ptype) {
        try {
            // eco打印
            int printId = 70105;
            switch (ptype) {
                case "jzd":
                    printId = 70105;
                    break;
                case "zdqx":
                    printId = 70106;
                    break;
                case "error":
                    // 异常账单
                    printId = 70107;
                    break;
                case "errorBack":
                    // 异常账单
                    printId = 70107;
                    break;
            }

            LOGGER.info("发送打印信息,预订单编号：" + outOrderIdInDB + ",打印类型" + ptype + ",printId" + printId);
            String printStr = "";
            if (null != data.getIshis() && data.getIshis().equals("1")) {
                printStr = "桌位名称='';预定单号='" + outOrderIdInDB + "';操作员='99';报表日期='" + jzbbrq + "';查询类型='0';渠道='ECO'";
            } else {
                printStr = "桌位名称='';预定单号='" + outOrderIdInDB + "';操作员='99';报表日期='" + jzbbrq + "';查询类型='1';渠道='ECO'";
            }

            PosPrintJna print = new PosPrintJna();
            print.tzxReportlib(printId, printStr, "99");
            LOGGER.info("结帐单打印完成" + outOrderIdInDB);
        } catch (Exception e) {
            LOGGER.error("Ignore this exception", e);
        }
    }

    /**
     * 清单模版
     *
     * @param szdbh
     * @param jzbbrq
     */
    public void sendPrintQD(EcoRepData data, String szdbh, String jzbbrq) {
        try {
            String tempStr = InitDataListener.ggcsMap.get("POS_ECO_JZSFDYQD");
            if ("Y".equals(tempStr)) {
                // eco打印清单
                int printId = 10158;
                String printStr = "账单编号='" + szdbh + "';报表日期='" + jzbbrq + "';查询类型='1';渠道='ECO'";
                LOGGER.info("打印" + printStr);
                PosPrintJna print = new PosPrintJna();
                print.tzxReportlib(printId, printStr, "99");
                LOGGER.info("清单打印完成" + printStr);
            } else {
                LOGGER.info("公共参数POS_ECO_JZSFDYQD未开启，ECO不打印清单");
            }
        } catch (Exception e) {
            LOGGER.error("Ignore this exception", e);
        }
    }

    /**
     * 清单模版
     *
     * @param szdbh
     * @param jzbbrq
     */
    public void sendQXPrintQD(EcoRepData data, String szdbh, String jzbbrq) {
        try {
            String tempStr = InitDataListener.ggcsMap.get("POS_ECO_QXJZSFDYQD");
            if ("Y".equals(tempStr)) {
                // eco打印清单
                int printId = 10158;
                String printStr = "账单编号='" + szdbh + "';报表日期='" + jzbbrq + "';查询类型='1';渠道='ECO'";
                LOGGER.info("打印" + printStr);
                PosPrintJna print = new PosPrintJna();
                print.tzxReportlib(printId, printStr, "99");
                LOGGER.info("清单打印完成" + printStr);
            } else {
                LOGGER.info("公共参数 POS_ECO_QXJZSFDYQD未开启，ECO退单不打印清单");
            }
        } catch (Exception e) {
            LOGGER.error("Ignore this exception", e);
        }
    }

    /**
     * 订单完成后，送厨打
     *
     * @param szdbh
     */
    public void sendPrintKichen(EcoRepData data, String szdbh, String channel) {
        try {
            String sfscd = InitDataListener.ggcsMap.get("POS_ECO_JZSFDYCD");      // 控制三合一是否对ECO订单厨打
            String printChannel = InitDataListener.ggcsMap.get("CCPrintChannel"); // 控制三合一对ECO外卖渠道的厨打

            if ("Y".equals(sfscd) && StringUtils.isNotEmpty(printChannel)) {
                List<String> printChannelList = Arrays.asList(printChannel.split(","));
                LOGGER.info("ECO厨打渠道" + printChannel + " 当前订单渠道: " + channel);
                if (printChannelList.contains(channel)) {
                    String printKichenStr = "账单号='" + szdbh + "';渠道='ECO';类型=''";
                    PosPrintKichenJna printKichen = new PosPrintKichenJna();
                    printKichen.tzxReportlib("99", printKichenStr);
                    LOGGER.info("厨打完成" + szdbh);
                }
            } else {
                LOGGER.info("公共参数POS_ECO_JZSFDYCD未开启，ECO不送厨打");
            }
        } catch (Exception e) {
            LOGGER.error("送厨打系统错误！", e);
        }
    }

    public void sendPrintErrorKichen(EcoRepData data, String szdbh) {
        try {
            String sfscd = InitDataListener.ggcsMap.get("POS_ECO_JZSFDYCD");
            if ("Y".equals(sfscd)) {
                String useNewPrint = InitDataListener.ggcsMap.get("USENEWPRINT");
                if ((null != useNewPrint) && useNewPrint.equals("Y")) {
                    // 新版按照统一方式处理
                    // 注意增加两个参数：渠道、类型
                    // 渠道当然还是：ECO，但是类型需要正常设置成为：ERROR，默认：为空，异常分单需要使用
                    String printKichenStr = "账单号='" + szdbh + "';渠道='ECO';类型='ERROR'";
                    PosPrintKichenJna printKichen = new PosPrintKichenJna();
                    printKichen.tzxReportlib("99", printKichenStr);
                } else {
                    // 旧版模式异常订单需要调用特殊分单过程
                    ecoOrderMapper.sendErrorPrint(szdbh);
                }
                LOGGER.info("厨打完成" + szdbh);
            } else {
                LOGGER.info("公共参数POS_ECO_JZSFDYCD未开启，ECO不送厨打");
            }
        } catch (Exception e) {
            LOGGER.error("送厨打系统错误！", e);
        }
    }

    @Override
    public void sendUdpMsg(String action, String partMsgBody) {
        String msgBody = "ACTION=" + action + "|FDJGXH=" + UploadGloVar.getOrganizeID();
        msgBody = msgBody + partMsgBody;
        DatagramUtil.sendUdpMsg(msgBody, Integer.valueOf(Constant.ECO_BOH_PORT).intValue(), "127.0.0.1");

    }

    @Override
    public void sendShowMsg(String msg) {
        try {
            msg = Base64.byteArrayToBase64(URLEncoder.encode(msg, "utf-8").getBytes());
            sendUdpMsg("showmsg", "|msg=" + msg);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 异常订单处理过程
     */
    @Transactional
    public EcoRepData orderErrorPrecheckProcess(EcoRepData data, Map<String, String> inparam, JSONObject orderData) {
        int isPlatform = orderData.optInt("isplatform", 1);
        LOGGER.info("第三步：保存异常订单相关信息");
        
        EcoErrorYdd eby = jsonToErrorYdd(orderData);
        ecoOrderMapper.insertErrorYdd(eby);
        LOGGER.info("保存异常订单成功");

        List<EcoErrorYdxm2> bymx = jsonToErrorYdxm2(orderData);
        if (null != bymx && bymx.size() > 0)
            ecoOrderMapper.insertErrorYdxm2(bymx);
        LOGGER.info("保存异常订单明细表成功");

        List<EcoErrorOrderdiscount> ecoOrderdiscounts = jsonToErrorOrderDiscount(orderData);
        if (ecoOrderdiscounts.size() > 0)
            ecoOrderMapper.insertErrorOrderdiscount(ecoOrderdiscounts);
        LOGGER.info("保存异常订单活动明细成功");


        if(isPlatform==0){
            LOGGER.info("第四步：保存订单付款信息");
            List<EcoBtPayments> payments = joinBtPayments(orderData);
            if (null != payments && payments.size() > 0)
                ecoOrderMapper.insertErrBtPayments(payments);
            LOGGER.info("保存订单付款明细成功");
        }


        // 异常订单没有转成正式账单处理过程，但是现在缺少付款信息
        // 按照现在账单结构无须处理付款信息

        data.setSuccess("true");
        data.setMsg("异常接单成功！");
        
        // 异常接单回传空的单号
        data.setCheck_id("");
        
        return data;
    }

    /**
     * 处理异常账单数据
     *
     * @param orderInfo
     * @return
     */
    public EcoErrorYdd jsonToErrorYdd(JSONObject orderInfo) {
        double yl2 = 0d;// 优惠总金额
        double shop_rate = 0d;// 门店承担优惠金额
        EcoErrorYdd by = new EcoErrorYdd();

        JSONArray activityInfos = orderInfo.optJSONArray("activityInfo");
        for (int i = 0; i < activityInfos.size(); i++) {
            yl2 = ArithUtil.add(yl2, activityInfos.getJSONObject(i).optDouble("discount_acmount"));
            shop_rate = ArithUtil.add(shop_rate, activityInfos.getJSONObject(i).optDouble("shop_rate"));
        }
        // 添加预订单号加上TS前缀
        by.setYddh(Constant.ECOBILL_PREFIX + orderInfo.optString("outorderid"));
        by.setLxr(orderInfo.optString("contact", ""));
        by.setLxrdh(orderInfo.optString("mobile", ""));
        by.setYdrs(orderInfo.optInt("dinners_number"));
        by.setMen(0);
        by.setWomen(0);
        by.setEldernum(0);
        by.setChildnum(0);
        by.setTotalprice(orderInfo.optDouble("totalprice"));
        by.setYdrq(DateUtil.getNowDateYYDDMM());
        by.setQdsj(DateUtil.getNowDateYYDDMMHHMMSS());
        by.setDdsj(orderInfo.optString("createtime", ""));
        by.setDdzt("5"); // 默认状态
        by.setYl1(orderInfo.optDouble("totalprice"));
        by.setPaychannel(orderInfo.optString("wpaytype"));
        by.setYl2(yl2);
        by.setYl3(orderInfo.optDouble("order_delivery_pay"));
        by.setShop_real_amount(orderInfo.optDouble("shopfee"));
        by.setTaxpayerid(orderInfo.optString("taxer_id"));
        by.setRecieptdept(orderInfo.optString("invoiceinfo"));
        by.setPick_type(orderInfo.optInt("ordertype"));
        by.setKhjlbh(orderInfo.optString("order_index"));
        by.setPackage_box_fee(orderInfo.optDouble("package_fee"));
        by.setYl5(orderInfo.optString("conTactMem"));
        by.setKhtmtz(orderInfo.optString("sourcename"));
        by.setKwxh(orderInfo.optString("comment"));
        by.setShop_rate(shop_rate);
        by.setPlatform_rate(ArithUtil.sub(yl2, shop_rate));
        by.setCommission_amount(orderInfo.optDouble("service_money"));
        by.setChannel(1);
        by.setMember_address(orderInfo.optString("address"));
        by.setJk(orderInfo.optString("payTypeName"));
        by.setBz(orderInfo.optString("diningtime"));
        // 校验订单是否存在对应的渠道或者对应渠道的付款编号是否存在
        if (orderInfo.optString("source") != null && InitDataListener.ecoTypeDicMap.containsKey(orderInfo.optString("source"))) {
            EcoTypeDic ecoTypeDic = InitDataListener.ecoTypeDicMap.get(orderInfo.optString("source"));
            by.setYl4(ecoTypeDic.getTzxcode());
            if (orderInfo.optString("payTypeName").equals("")) {
                by.setJk(ecoTypeDic.getTzxname() + "付款");
            }
        }
        // 下单来源 “0”为门店下单；“1”为线上下单；默认为“0”。V1.1新增
        if (orderInfo.containsKey("channel")) {
            int channel = orderInfo.optInt("channel", 0); // 默认为0
        }
        by.setDiningway("XSMS_WM");
        if (ZS_DELIVERY_TYPE.contains(orderInfo.optString("delivery_type"))) {
            by.setShippingmethod(NumberUtils.toInt(orderInfo.optString("delivery_type")));
        } else {
            by.setShippingmethod(-1);
        }
        by.setZkfa(0);
        by.setParkingnum(orderInfo.optInt("isplatform", 1));
        return by;
    }

    public List<EcoErrorYdxm2> jsonToErrorYdxm2(JSONObject orderInfo) {
        int isPlatform = orderInfo.optInt("isplatform", 1);
        // 菜品列表
        List<EcoErrorYdxm2> bymx = new ArrayList<EcoErrorYdxm2>();
        JSONArray itemInfos = orderInfo.optJSONArray("items");
        Integer dcxh = 1;
        Integer tcdcxh = 1;
        for (int j = 0; j < itemInfos.size(); j++) {
            dcxh += 1;
            EcoErrorYdxm2 ydmx2 = new EcoErrorYdxm2();
            ydmx2.setIsactive(0);
            ydmx2.setYddh(Constant.ECOBILL_PREFIX + orderInfo.optString("outorderid"));
            ydmx2.setXmid(itemInfos.getJSONObject(j).optInt("cmid",-1));
            ydmx2.setXmbh(getKDishsNo(itemInfos.getJSONObject(j)));
            ydmx2.setXmmc(itemInfos.getJSONObject(j).optString("itemname"));
            ydmx2.setTcdch(0);
            if ("0".equals(itemInfos.getJSONObject(j).optString("bsetmeal")))
                ydmx2.setXmsx("CMSX_DP");
            else {
                tcdcxh += 1;
                ydmx2.setXmsx("CMSX_TC");
                ydmx2.setTop_item_id(itemInfos.getJSONObject(j).optInt("cmid",-1));
                ydmx2.setTcdch(tcdcxh);
            }

            // 单价还是菜品原价
            ydmx2.setXmdj(new BigDecimal(itemInfos.getJSONObject(j).optString("originalprice")));
            ydmx2.setXmsl(new BigDecimal(itemInfos.getJSONObject(j).optString("itemcount")));
            ydmx2.setZkl(100);
            // 菜品实结金额
            ydmx2.setTotalprice(new BigDecimal(itemInfos.getJSONObject(j).optString("price")));
            ydmx2.setDwbh(itemInfos.getJSONObject(j).optString("properties"));
            ydmx2.setKwbh(itemInfos.getJSONObject(j).optString("nature"));
            // 菜品金额使用原价*数量
            ydmx2.setCmje(new BigDecimal(itemInfos.getJSONObject(j).optString("price")));

            ydmx2.setTcbh("");
            ydmx2.setFzsl(0);
            ydmx2.setFzje(new BigDecimal(0));
            ydmx2.setDcxh(dcxh);
            Integer packId = itemInfos.getJSONObject(j).optInt("packid", -1);
            if (packId != -1) {
                ydmx2.setPackid(packId);
            } else
                ydmx2.setPackid(null);
            bymx.add(ydmx2);
            //平台外卖 没有套餐明细，自运营外卖需要传套餐明细
            if(isPlatform==1){

            }
            else {
                if(null!= itemInfos.getJSONObject(j).get("details") && "1".equals(itemInfos.getJSONObject(j).optString("bsetmeal"))) {
                    JSONArray detaisArr = itemInfos.getJSONObject(j).getJSONArray("details");
                    if (null != detaisArr && detaisArr.size() > 0) {
                        joinErrBtYdxm2Details(bymx, ydmx2.getYddh(),ydmx2.getXmid(),tcdcxh, dcxh, detaisArr);
                    }
                }
            }
        }
        return bymx;
    }

    public List<EcoErrorOrderdiscount> jsonToErrorOrderDiscount(JSONObject orderInfo) {
        // 活动列表
        List<EcoErrorOrderdiscount> ecoOrderdiscounts = new ArrayList<EcoErrorOrderdiscount>();
        JSONArray items = orderInfo.optJSONArray("activityInfo");
        for (int j = 0; j < items.size(); j++) {
            EcoErrorOrderdiscount item = new EcoErrorOrderdiscount();
            item.setOrder_code(Constant.ECOBILL_PREFIX + orderInfo.optString("outorderid"));
            // ydmx2.setXmid(jo.optInt("did"));
            item.setDiscount_type(items.getJSONObject(j).optString("activity_type"));
            item.setDiscount_desc(getActivityDesc(items, j));
            item.setDiscount_fee(new BigDecimal(items.getJSONObject(j).optDouble("discount_acmount")));
            item.setShop_rate(new BigDecimal(items.getJSONObject(j).optDouble("shop_rate")));
            item.setPlatform_rate(new BigDecimal(ArithUtil.sub(items.getJSONObject(j).optDouble("discount_acmount"), items.getJSONObject(j).optDouble("shop_rate"))));
            item.setActivity_id(items.getJSONObject(j).optString("activity_name"));
            ecoOrderdiscounts.add(item);
        }
        return ecoOrderdiscounts;
    }

    @Override
    public EcoRepData printErrorBill(EcoRepData data, Map<String, String> inparam) {
//        if (!(null != data.getIshis() && data.getIshis().equals("1"))) {
//            // 发送清单，是否还需清单？
//            // 暂不考虑
//            //sendPrintQD(data, inparam.get("newKdzdbh"), inparam.get("bbrq"));
//            // 发送后厨，不能再走原来方式，直接调用存储过程生成厨打
//            if (inparam.containsKey("need_send_kitchen") && inparam.get("need_send_kitchen").equals("1")) {
//                sendPrintErrorKichen(data, inparam.get("outOrderIdInDB"));
//            }
//        }
        // 发送异常账单打印
        if (inparam.containsKey("need_print_bill") && inparam.get("need_print_bill").equals("1")) {
            sendPrint(data, inparam.get("outOrderIdInDB"), inparam.get("bbrq"), "error");
        }
        return data;
    }

    @Override
    public boolean checkErrorOrderStatus(String outOrderIdInDB, EcoRepData data) {
        Integer orderstatus = 0;
        LOGGER.info("第三步：校验异常订单状态……");
        if (ecoOrderMapper.getErrorYddCountByYddh(outOrderIdInDB) > 0) {
            orderstatus = 1;
        }
        if (1 == orderstatus) {
            data.setMsg("接单失败：异常订单已经接单成功");
            return false;
        }
        return true;
    }

    private boolean checkOrderBackErrorStatus(String outOrderIdInDB, EcoRepData data) {
        LOGGER.info("校验异常订单退单状态……");
        List<Map<String, String>> yddmaplst = ecoOrderMapper.getErrorYdd(outOrderIdInDB);
        if (null == yddmaplst || yddmaplst.size() == 0) {
            data.setMsg("异常退单失败：未找到原异常订单，敬请确认订单是否已经接单成功！");
            data.setTagstr("101");
            return false;
        }
        if (yddmaplst.get(0).get("ddzt").equals("7")) {
            data.setMsg("异常退单失败：订单已经退单成功！");
            data.setTagstr("101");
            return false;
        }
        // 获取是否单品退单标志
        LOGGER.info("校验异常订单退单状态……");
        if (null != yddmaplst.get(0).get("refund_type") && yddmaplst.get(0).get("refund_type").equals("2")) {
            data.setHasDoneOrderPat("Y");
        } else {
            data.setHasDoneOrderPat("N");
        }
        if ("Y".equals(data.getIsOrderPart())) {
            if ("Y".equals(data.getHasDoneOrderPat())) {
                LOGGER.info("异常退单失败：部分退菜已经退过！");
                data.setMsg("异常退单失败：部分退菜已经退过！");
                data.setTagstr("101");
                return false;
            }
        }
        return true;
    }

    public EcoRepData orderbackErrorBefore(EcoRepData data, Map<String, String> inparam, JSONObject orderData) {
        // ECO预定账单编号
        String outOrderId = orderData.optString("outorderid");
        // 我们系统内部预定账单编号，规则：增加一个前缀
        String outOrderIdInDB = Constant.ECOBILL_PREFIX + outOrderId;
        // 默认失败
        data.setSuccess("false");
        data.setMsg("异常退单失败：未知原因！");
        LOGGER.info("门店开始异常退单，平台预定账单编号：" + outOrderId);

        // **************************************************
        // 第一步：检查退单是否单品退单
        // **************************************************
        JSONArray orderParts = orderData.optJSONArray("orderPart");
        data.setIsOrderPart("N");
        if (null != orderParts && orderParts.size() > 0) {
            double refundTotal = 0;
            for (int j = 0; j < orderParts.size(); j++) {
                double refund_price = 0.00;
                double partcount = 0.0;
                if ("2".equals(orderParts.getJSONObject(j).optString("partstatus"))) {
                    if (null != orderParts.getJSONObject(j).optString("refund_price")) {
                        refund_price = orderParts.getJSONObject(j).optDouble("refund_price", 0);
                    }
                    if (null != orderParts.getJSONObject(j).optString("partcount")) {
                        partcount = orderParts.getJSONObject(j).optDouble("partcount", 0);
                    }
                    refundTotal = ArithUtil.add(refundTotal, ArithUtil.mul(refund_price, partcount));
                }
            }
            if (refundTotal > 0) {
                data.setIsOrderPart("Y");
            } else {
                data.setMsg("异常退单失败：部分退款金额小于等于0！");
                data.setTagstr("101");
                return data;
            }
        }

        // **************************************************
        // 第二步：校验异常订单当前状态
        // **************************************************
        // 需要检查是否存在异常订单、是否已经部分退单、是否已经全退
        if (!checkOrderBackErrorStatus(outOrderIdInDB, data)) {
            return data;
        }

        inparam.put("outOrderId", outOrderId);
        inparam.put("outOrderIdInDB", outOrderIdInDB);
        inparam.put("bbrq", "");
        inparam.put("rybh", "");
        inparam.put("ygdlcs", "");

        data.setSuccess("true");

        return data;
    }

    public List<EcoErrorYdxm1> joinErrorYdxm1(JSONObject orderInfo) {
        // 菜品列表
        List<EcoErrorYdxm1> bymx = new ArrayList<EcoErrorYdxm1>();
        JSONArray itemInfos = orderInfo.optJSONArray("orderPart");
        for (int j = 0; j < itemInfos.size(); j++) {
            if (!("2".equals(itemInfos.getJSONObject(j).optString("partstatus")))) {
                continue;
            }
            if (itemInfos.getJSONObject(j).optDouble("partcount", 0) <= 0) {
                continue;
            }
            EcoErrorYdxm1 ydmx1 = new EcoErrorYdxm1();
            ydmx1.setYddh(Constant.ECOBILL_PREFIX + orderInfo.optString("outorderid") + "_B");
            String itemid = "";
            itemid = getKDishsNo(itemInfos.getJSONObject(j));
            ydmx1.setXmbh(itemid);
            ydmx1.setXmmc(itemInfos.getJSONObject(j).optString("itemname"));
            // 单价还是菜品原价
            ydmx1.setYl1(new BigDecimal(itemInfos.getJSONObject(j).optString("refund_price")));
            ydmx1.setYl2(new BigDecimal(itemInfos.getJSONObject(j).optString("partcount")));
            ydmx1.setYl3(itemInfos.getJSONObject(j).optString("partstatus"));
            bymx.add(ydmx1);
        }
        return bymx;
    }

    @Transactional
    public EcoRepData orderErrorbackProcess(EcoRepData data, Map<String, String> inparam, JSONObject orderData) {
        // 执行部分退单
        if ("Y".equals(data.getIsOrderPart())) {
            List<EcoErrorYdxm1> bymx = joinErrorYdxm1(orderData);
            if (bymx.size() <= 0) {
                throw new EcoException("异常退单失败：错误信息：部分退款菜品列表为空");
            }
            ecoOrderMapper.insertErrorYdxm1(bymx);
            LOGGER.info("插入异常预定明细成功");
            Integer irt = -1;
            // 执行账单部分退款异常账单生成
            irt = ecoOrderMapper.singleCancelEcoErrorBill(inparam.get("outOrderIdInDB"));
            LOGGER.info("执行账单部分退单过程返回：" +inparam.get("outOrderIdInDB") + "，返回=" + irt);
            if (!irt.equals(0)) {
                String codemsg = processCancelEcoBillCode(irt);
                throw new EcoException("部分退单失败：退单返回失败，错误信息: " + codemsg);
            }
        } else {
            Integer irt = -1;
            // 执行账单退款异常账单生成
            irt = ecoOrderMapper.cancelEcoErrorBill(inparam.get("outOrderIdInDB"));
            LOGGER.info("执行账单全单退单过程返回：" + inparam.get("outOrderIdInDB") + "，返回=" + irt);
            if (!irt.equals(0)) {
                String codemsg = processCancelEcoBillCode(irt);
                throw new EcoException("整单退单失败：退单返回失败，错误信息: " + codemsg);
            }
        }

        data.setSuccess("true");
        data.setMsg("异常退单成功");

        return data;
    }

    @Override
    @Transactional
    public EcoRepData sendKvsData(EcoRepData data, Map<String, String> inparam) {
        if(null!=InitDataListener.ggcsMap.get("POS_ECO_SFQYKVS") && "Y".equals(InitDataListener.ggcsMap.get("POS_ECO_SFQYKVS") )){
            try {
                String kdzdbh = inparam.get("newKdzdbh");
                ecoOrderMapper.callP_SendKVSData(kdzdbh);
            }
            catch (Exception e){
              LOGGER.error("执行发送kvs数据异常 " + e.getMessage());
            }
        }
        return data;
    }

    @Override
    @Transactional
    public EcoRepData deleteKvsData(EcoRepData data, Map<String, String> inparam) {
        if(null!=InitDataListener.ggcsMap.get("POS_ECO_SFQYKVS")  && "Y".equals(InitDataListener.ggcsMap.get("POS_ECO_SFQYKVS") )) {
            try
            {
                String kdzdbh = inparam.get("newKdzdbh");
                ecoOrderMapper.deleteKvsMXK(kdzdbh);
                ecoOrderMapper.updateKvsMxkTime();
                ecoOrderMapper.updateKvsChangeTime();
                ecoOrderMapper.updateKdsChangeTime();
            }
            catch (Exception e) {
                LOGGER.error("执行删除kvs数据异常 " + e.getMessage());
            }
        }
        return data;
    }

    public List<EcoBtPayments> joinBtPaymentsWsh(JSONObject orderInfo) {
        // 付款列表
        List<EcoBtPayments> payments = new ArrayList<EcoBtPayments>();
        //JSONArray mergePay = orderInfo.optJSONArray("merge_pay");
        String otJsonStr = orderInfo.optString("ot_json", "{}");
        JSONObject otJson = JSONObject.fromObject(otJsonStr);
        String otherInfoStr = otJson.optString("other_info", "[]");
        JSONArray otherInfo = JSONArray.fromObject(otherInfoStr);
        String payNo = "";
        for (int j = 0; j < otherInfo.size(); j++) {
            JSONObject payObj = otherInfo.getJSONObject(j);
            EcoBtPayments btPayments = new EcoBtPayments();
            btPayments.setYddh(Constant.ECOBILL_PREFIX + orderInfo.optString("outorderid"));
            btPayments.setPay_name("");

            String wpaytype = payObj.optString("wpaytype");
            EcoTypeDic etd = firstPayMapper.findEcotypedicByCode("-101", wpaytype);
            payObj.put("pay_no", etd.getFkfsbh());
            payNo = payObj.optString("pay_no");
            btPayments.setPay_no(payNo);
            double amount = payObj.optDouble("pay_money", 0d);
            //double storePay = payObj.optDouble("stored_sale_pay", 0d);
            double storePay = payObj.optDouble("pay_money", 0d);
            double disAmount = ArithUtil.sub(amount, storePay);
            //默认不拆分
            if("1973".equals(payNo) || "2001".equals(payNo)){
                //按赠送拆分
                //按比例拆分
                if("2".equals(InitDataListener.ggcsMap.get("POS_MEMBALANCE_USEDIS"))) {
                    String acewillDiscount = InitDataListener.ggcsMap.get("ACEWILLDISCOUNT");
                    if (null != acewillDiscount) {
                        if (isNumeric(acewillDiscount) && Integer.parseInt(acewillDiscount) > 0 && Integer.parseInt(acewillDiscount) <= 100) {
                            double scale = Integer.parseInt(acewillDiscount);
                            double scale_pay = ArithUtil.sub(amount, ArithUtil.mul(amount, ArithUtil.div(scale, 100, 2)));
                            scale_pay = ArithUtil.round(scale_pay, 2);
                            disAmount = scale_pay;
                            storePay = ArithUtil.sub(amount, disAmount);
                        } else {
                            LOGGER.error("储值未拆分:拆分比例为空");
                        }
                    } else {
                        LOGGER.error("储值未拆分:未设置拆分比例");
                    }
                }
            } else if("1972".equals(payNo) || "2002".equals(payNo)){
                if ("1".equals(InitDataListener.ggcsMap.get("POS_MEMCREDIT_USEDIS"))) {
                    storePay = 0d;
                    disAmount = amount;
                }
            }
            btPayments.setPay_count(new BigDecimal(amount));
            btPayments.setVocount(new BigDecimal(storePay));
            btPayments.setFzzhje(new BigDecimal(disAmount));
            btPayments.setPay_channel(wpaytype);
            payments.add(btPayments);
        }
        return payments;
    }

    /**
     * 订单打印-乡村基
     *  orderId           订单号
     *  dylx              打印类型
     *  ddlx              订单类型
     *  dyjmc             打印机名称
     *  need_invoice      是否开发票
     *  jgxh              机构序号
     *  bbrq              报表日期
     */
    @Override
    public void execPrintSingle(Map<String, String> inparam) {
        String orderId = inparam.get("outOrderIdInDB");
        try {
            int jgxh = Integer.valueOf(inparam.get("jgxh"));
            String bbrq = inparam.get("bbrq");
            String channel = inparam.get("channel");
            String dyjmc = getPrintNameByProperties(channel);
            String dylx = inparam.get("dylx");
            String ddlx = inparam.get("ddlx");

            TqYyddylsk yyddyls = new TqYyddylsk();
            yyddyls.setFdjgxh(jgxh);
            yyddyls.setYddh(orderId);
            yyddyls.setBbrq(DateUtil.formatTimestamp(bbrq, "yyyy-MM-dd"));
            yyddyls.setDyzt(0);
            yyddyls.setDyjmc(dyjmc);
            yyddyls.setDylx(dylx);
            yyddyls.setNeed_invoice("Y");
            yyddyls.setDdlx(ddlx);
            int ri = ecoOrderMapper.insertTqYyddylsk(yyddyls);
            LOGGER.error("{" + orderId + "} 打印库入库完成");
            if (ri > 0) {
                // 发送UDP消息给POS
                DatagramUtil.sendUdpMsg("ACTION=TZXYDDPRINT|FDJGXH=" + jgxh + "|YDDH=" + orderId, Integer.valueOf("8007").intValue(), "127.0.0.1");
                LOGGER.error("{" + orderId + "} UDP发送完成");
            }
        } catch (Exception e) {
            LOGGER.error("{" + orderId + "}订单打印失败:" + e.getMessage(), e);
        }
    }

    /**
     *
     * 解析打印机配置(固定GBK编码)
     * @param channel 外卖渠道
     * @return
     */
    private static String getPrintNameByProperties(String channel) throws Exception {
        String printName = "";
        if (channel.equalsIgnoreCase("BD06") || channel.equalsIgnoreCase("TH03")) {
            printName = PropertiesUtil.readValueForClasses("/application.properties", "baiduprintname");
        } else if (channel.equalsIgnoreCase("MT08") || channel.equalsIgnoreCase("MT11")) {
            printName = PropertiesUtil.readValueForClasses("/application.properties", "meituanprintname");
        } else if (channel.equalsIgnoreCase("WX02")) {
            printName = PropertiesUtil.readValueForClasses("/application.properties", "wx02printname");
        } else if (channel.equalsIgnoreCase("EL09")) {
            printName = PropertiesUtil.readValueForClasses("/application.properties", "elmprintname");
        } else {
            printName = PropertiesUtil.readValueForClasses("/application.properties", "elmprintname");
        }

        if (printName != null) {
            printName = new String(printName.getBytes("ISO-8859-1"), "UTF-8");
        }

        if (StringUtils.isBlank(printName)) {
            String path = System.getProperty("user.dir");
            File file = new File(path);
            String filePath = file.getParent() + File.separator + "nebula_boh" + File.separator + "wmprint" + File.separator + "printsetting.properties";
            if (channel.equalsIgnoreCase("BD06") || channel.equalsIgnoreCase("TH03")) {
                printName = PropertiesUtil.readValue(filePath, "baiduprintname", "GBK");
            } else if (channel.equalsIgnoreCase("MT08") || channel.equalsIgnoreCase("MT11")) {
                printName = PropertiesUtil.readValue(filePath, "meituanprintname", "GBK");
            } else if (channel.equalsIgnoreCase("WX02")) {
                printName = PropertiesUtil.readValue(filePath, "wx02printname", "GBK");
            } else if (channel.equalsIgnoreCase("EL09")) {
                printName = PropertiesUtil.readValue(filePath, "elmprintname", "GBK");
            } else {
                printName = PropertiesUtil.readValue(filePath, "elmprintname", "GBK");
            }
        }

        // 特殊打印机名称转换
        printName = StringFilterUtil.convertEscapeOfData(printName);
        return printName == null ? "" : printName;
    }

    /**
     * 保存订单明细到bt_tcselectmx表
     * @param orderData 订单JSON对象
     * @param yddh 订单号
     * @return 插入的记录数
     */
    public int saveTcSelectMx(JSONObject orderData, String yddh) {
        try {
            JSONArray items = orderData.getJSONArray("items");
            if (items == null || items.size() == 0) {
                return 0;
            }

            // 构建实体对象列表
            List<BtTcSelectMx> mxList = new ArrayList<>();
            // 明细序号从1开始递增
            long dcxh = 1;

            for (int i = 0; i < items.size(); i++) {
                JSONObject item = items.getJSONObject(i);

                // 只处理bsetmeal=0且bsetmealid!=0的记录
                if ("0".equals(item.getString("bsetmeal")) &&
                        !"0".equals(item.getString("bsetmealid"))) {

                    BtTcSelectMx mx = new BtTcSelectMx();
                    mx.setYddh(yddh);
                    mx.setFzsl(new BigDecimal(item.getString("itemcount")));
                    mx.setFzje(new BigDecimal(item.getString("originalprice")));
                    mx.setCmmc(item.getString("itemname"));
                    mx.setBsetmealid(item.getString("bsetmealid"));
                    // 设置明细序号
                    mx.setDcxh(dcxh++);

                    mxList.add(mx);
                }
            }

            // 如果没有符合条件的记录，直接返回0
            if (mxList.isEmpty()) {
                return 0;
            }

            // 批量插入数据
            return ecoOrderMapper.insertBtTcSelectMx(mxList);

        } catch (Exception e) {
            LOGGER.error("保存订单明细失败: " + e.getMessage(), e);
            throw new RuntimeException("保存订单明细失败", e);
        }
    }

    private boolean isDebugMode(){
        return "true".equals(System.getProperty("DebugMode"));
    }
}