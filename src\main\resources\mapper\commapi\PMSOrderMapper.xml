<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.commapi.rest.mapper.PMSOrderMapper">
	<resultMap id="pmsorderItem" type="com.tzx.commapi.rest.vo.PMSOrderItem" autoMapping="true">
		<id column="order_item_id" property="order_item_id" />
		<result property="order_id" column="order_id"/>
		<!--不用SQL拼接了，用代码处理。因为SQL拼接会用到TQ_ZFKWWDK，但是副机并不上传这个表。且小程序和ECO也可能不写这个表 -->
		<!--
		<collection property="condiments" column="order_item_id" autoMapping="false"
					ofType="com.tzx.commapi.rest.vo.PMSCondiment">
			<result column="condiment_id" property="condiment_id"/>
			<result column="kwprice" property="price" />
			<result column="kw_order_item_id" property="order_item_id"/>
			<result column="condiment_code" property="condiment_code"/>
			<result column="condiment_name" property="condiment_name"/>
			<result column="sequence" property="sequence"/>
			<result column="qty" property="qty"/>
		</collection>
		-->

	</resultMap>
	<select id="findOrderChanel" resultType="com.tzx.commapi.rest.vo.PMSOrder"  parameterType="map">
        <!--基本原则认为如果没有预定单号的都是堂食。有预定单号的是小程序和外卖，外卖的XSMS是外送。 -->
		select case
		       when coalesce(yddh,'') = '' then 'POS'
		       when xsms &lt;&gt; 'XSMS_WS' then 'WECHATMP'
		       when xsms = 'XSMS_WS' AND ZWBH LIKE '饿了么%' then 'ELE'
		       else  'MEITUAN'
		   end as order_channel ,
		   <!--美团和饿了么不用区分，其他区分为堂食和外带 -->
		   case
		     when coalesce(yddh,'') &lt;&gt; '' and  xsms = 'XSMS_WS' then ''
		     else case when xsms = 'XSMS_TS' then '1' ELSE '2' end
		   end as order_type_id from vq_zdk
		where kdzdbh = #{kdzdbh,jdbcType=VARCHAR}
		limit 1
	</select>
	<select id="findPMSOrderByKdzdbh" resultType="com.tzx.commapi.rest.vo.PMSOrder"  parameterType="map">
        select
          case when zdbz = 'ZDZT_SS' then 0 else 1 end  staffmealflag,
          kdzdbh as order_id,
          case when yddh like 'ECO%' then yddh else '' end as order_id_eco,
          yddh AS  third_party_id ,
          '5' AS order_status,
          FWYH AS waiter_user_id,
          KTCZRY AS order_user_id,
          JZCZRY AS cashier_user_id,
          KTSKJH AS workstation_id,
          'POS'||KTSKJH AS workstation_name,
          ktsj as order_time,
          ktsj as checkopen_time,
          ksjzsj as checkout_time,
          case
		       when coalesce(yddh,'') = '' then 'POS'
		       when xsms &lt;&gt; 'XSMS_WS' then 'WECHATMP'
		       when xsms = 'XSMS_WS' AND ZWBH LIKE '饿了么%' then 'ELE'
		       else  'MEITUAN'
		   end as order_channel ,
		  coalesce(xfks,1) as people_num,
          case
             WHEN XSMS = 'XSMS_WS' THEN 'TAKE_OUT'
             WHEN XSMS = 'XSMS_WM' THEN  'TO_GO'
             else 'FOR_HERE'
          end as   order_type,
          case
		     when coalesce(yddh,'') &lt;&gt; '' and  xsms = 'XSMS_WS' then ''
		     else case when xsms = 'XSMS_TS' then '1' ELSE '2' end
		  end as order_type_id,
		  'NO' as take_style,
		  qch as take_no,
		  zdje as order_total,
		  fkje as order_cost,
		  zdje as products_total,
		  fkje as products_cost,
		  mlje as rounding_total,
		  ktsj as accept_date,
		  <!--外卖配送信息KVS不需要，暂时不做处理。当有其他业务需要时，再做 -->
		  jzzdbh as billid,
		  zwbh as table_code,
		  1 as checkprint
        from vq_zdk
		where kdzdbh = #{kdzdbh,jdbcType=VARCHAR}
		limit 1
	</select>
	<select id="findPMSOrderItems" resultMap="pmsorderItem" parameterType="map">
       select
         ALLITEM.kdzdbh as order_id,
         ALLITEM.rwid as order_item_id,
		 '0001' as  order_item_user_id,
         ALLITEM.tmbj as order_time,
         ALLITEM.cmid as product_id,
         ALLITEM.cmbh as product_code,
         ALLITEM.cmmc1 as product_name,
         ALLITEM.cmsl as product_qty,
         0 as is_hurry,
         0 as is_add,
         0 as is_gift,
         ALLITEM.cmje as item_total,
         ALLITEM.sjje as item_cost,
         coalesce(ALLITEM.zrje) + coalesce(ALLITEM.yhje) + coalesce(ALLITEM.dpzkje) as discount_total,
         0 as cooker_bonus,
         0 as waiter_bonus,
         '' as spec_id,
         '' as spec_name,
		 ALLITEM.cmdj as price,
		 'INIT' as make_status ,
		 #{makeTime,jdbcType=INTEGER} as make_time,
		 'WAIT' as wake_status,
		 case
		   when ALLITEM.cmsx = 'CMSX_DP' THEN 'SINGLE'
		   when ALLITEM.cmsx = 'CMSX_TC' THEN 'COMBO'
		   else 'COMBO'
		 end as   item_type,
		 case
		   when ALLITEM.cmsx = 'CMSX_MX' THEN '0'
		   else '1'
		 end as main_flag,
         case
           when ALLITEM.cmsx = 'CMSX_MX' THEN  cast ( baseITEM.RWID as varchar(50) )
           ELSE ''
         end as main_order_detail_id,
         0 AS b_print,
		 ALLITEM.DCXH AS sort,
		 ALLITEM.kwbz
		<!--不用SQL拼接了，用代码处理。因为SQL拼接会用到TQ_ZFKWWDK，但是副机并不上传这个表。且小程序和ECO也可能不写这个表 -->
		<!--
		 case when kwbase.id is not null then ALLITEM.rwid else null end  as kw_order_item_id,
		 kwbase.id as   condiment_id,
		 kwbase.kwbh as condiment_code,
		 kwbase.kwnr1 as condiment_name,
		 case when kwbase.id is not null then 0 else null end  as kwprice,
		 case when kwbase.id is not null then 0 else null end  as condimen_total,
		 case
		    when kwbase.id is not null then row_number() over(partition by kw.rwid ORDER BY kw.id)
		    else null
		 end  as sequence,
		 case when kwbase.id is not null then 0 else null end  as qty
		 -->
       from vq_zdmxk ALLITEM
       left join vq_zdmxk baseITEM ON ALLITEM.DCXH = baseITEM.DCXH AND ALLITEM.TCID = baseITEM.TCID
          AND ALLITEM.TCDCH = baseITEM.TCDCH AND ALLITEM.CMSX = 'CMSX_MX' AND baseITEM.CMSX = 'CMSX_TC'
          AND ALLITEM.KDZDBH = baseITEM.KDZDBH
		<!--不用SQL拼接了，用代码处理。因为SQL拼接会用到TQ_ZFKWWDK，但是副机并不上传这个表。且小程序和ECO也可能不写这个表 -->
		<!--
       left join tq_zfkwwdk  kw on  ALLITEM.rwid = kw.rwid and zfkwid is not null
       left join ts_kwsdk kwbase on kw.zfkwid = kwbase.id
       -->
       where ALLITEM.kdzdbh = #{kdzdbh,jdbcType=VARCHAR} and coalesce(ALLITEM.wdbz,'') = ''
       <if test="!haveDiscount" >
		   and ALLITEM.cmid is not null
	   </if>
       order by ALLITEM.rwid ,ALLITEM.dcxh ,ALLITEM.tcdch,ALLITEM.fzdch
	</select>



</mapper>
