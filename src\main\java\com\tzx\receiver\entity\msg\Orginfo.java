package com.tzx.receiver.entity.msg;

import java.io.Serializable;

public class Orginfo implements Serializable {
    private Long id;
    private Integer jgxh;
    private Integer sjxh;
    private String jgbh;
    private String jgmc;
    private String jgsx;
    private String jgdz;
    private String jgdh;
    private Long dqbh;
    private Long fdjb;
    private String jydd;
    private Double yymj;
    private Double yfz;
    private Integer zwsl;
    private Integer skjsl;
    private Integer deygs;
    private String jssx;
    private String syjg;
    private String cpsjzzq;
    private String sjfjzzq;
    private String cppzzzq;
    private String sf24yy;
    private String jybh;
    private String orgsname;
    private String jgtxbh;

    private String cwjgdm;
    private String jgmc1;
    private Integer jgver;
    private Integer jgxmver;
    private String cwdm;
    private String tssfkfp;
    private String wmsfkfp;
    private String dzfpmy;
    private Double sl;
    private String frswhm;
    private String jgtxmc;
    private String rotationpic1;//轮播图1
    private String rotationpic2;//轮播图2
    private String rotationpic3;//轮播图3
    private String rotationpic4;//轮播图4
    private String rotationpic5;//轮播图5
    private String rotationpic6;//轮播图6
    private String rotationpic7;//轮播图7

    private Integer miniappshopid;//微生活小程序门店ID
    private String uuid;

    private String acewillshopid;
    private String ompid;

    public String getCwjgdm() {
        return cwjgdm;
    }

    public void setCwjgdm(String cwjgdm) {
        this.cwjgdm = cwjgdm;
    }

    public String getJgmc1() {
        return jgmc1;
    }

    public void setJgmc1(String jgmc1) {
        this.jgmc1 = jgmc1;
    }

    public Integer getJgver() {
        return jgver;
    }

    public void setJgver(Integer jgver) {
        this.jgver = jgver;
    }

    public Integer getJgxmver() {
        return jgxmver;
    }

    public void setJgxmver(Integer jgxmver) {
        this.jgxmver = jgxmver;
    }

    public String getCwdm() {
        return cwdm;
    }

    public void setCwdm(String cwdm) {
        this.cwdm = cwdm;
    }

    public String getTssfkfp() {
        return tssfkfp;
    }

    public void setTssfkfp(String tssfkfp) {
        this.tssfkfp = tssfkfp;
    }

    public String getWmsfkfp() {
        return wmsfkfp;
    }

    public void setWmsfkfp(String wmsfkfp) {
        this.wmsfkfp = wmsfkfp;
    }

    public String getDzfpmy() {
        return dzfpmy;
    }

    public void setDzfpmy(String dzfpmy) {
        this.dzfpmy = dzfpmy;
    }

    public Double getSl() {
        return sl;
    }

    public void setSl(Double sl) {
        this.sl = sl;
    }

    public String getFrswhm() {
        return frswhm;
    }

    public void setFrswhm(String frswhm) {
        this.frswhm = frswhm;
    }

    public String getJgtxmc() {
        return jgtxmc;
    }

    public void setJgtxmc(String jgtxmc) {
        this.jgtxmc = jgtxmc;
    }

    public String getJybh() {
        return this.jybh;
    }

    public void setJybh(String jybh) {
        this.jybh = jybh;
    }

    public String getCppzzzq() {
        return this.cppzzzq;
    }

    public void setCppzzzq(String cppzzzq) {
        this.cppzzzq = cppzzzq;
    }

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getJgxh() {
        return this.jgxh;
    }

    public void setJgxh(Integer jgxh) {
        this.jgxh = jgxh;
    }

    public String getJgbh() {
        return this.jgbh;
    }

    public void setJgbh(String jgbh) {
        this.jgbh = jgbh;
    }

    public String getJgmc() {
        return this.jgmc;
    }

    public void setJgmc(String jgmc) {
        this.jgmc = jgmc;
    }

    public String getJgsx() {
        return this.jgsx;
    }

    public void setJgsx(String jgsx) {
        this.jgsx = jgsx;
    }

    public String getJgdz() {
        return this.jgdz;
    }

    public void setJgdz(String jgdz) {
        this.jgdz = jgdz;
    }

    public String getJgdh() {
        return this.jgdh;
    }

    public void setJgdh(String jgdh) {
        this.jgdh = jgdh;
    }

    public Long getDqbh() {
        return this.dqbh;
    }

    public void setDqbh(Long dqbh) {
        this.dqbh = dqbh;
    }

    public Long getFdjb() {
        return this.fdjb;
    }

    public void setFdjb(Long fdjb) {
        this.fdjb = fdjb;
    }

    public String getJydd() {
        return this.jydd;
    }

    public void setJydd(String jydd) {
        this.jydd = jydd;
    }

    public Double getYymj() {
        return this.yymj;
    }

    public void setYymj(Double yymj) {
        this.yymj = yymj;
    }

    public Double getYfz() {
        return this.yfz;
    }

    public void setYfz(Double yfz) {
        this.yfz = yfz;
    }

    public Integer getZwsl() {
        return this.zwsl;
    }

    public void setZwsl(Integer zwsl) {
        this.zwsl = zwsl;
    }

    public Integer getSkjsl() {
        return this.skjsl;
    }

    public void setSkjsl(Integer skjsl) {
        this.skjsl = skjsl;
    }

    public Integer getDeygs() {
        return this.deygs;
    }

    public void setDeygs(Integer deygs) {
        this.deygs = deygs;
    }

    public String getJssx() {
        return this.jssx;
    }

    public void setJssx(String jssx) {
        this.jssx = jssx;
    }

    public String getSyjg() {
        return this.syjg;
    }

    public void setSyjg(String syjg) {
        this.syjg = syjg;
    }

    public String getCpsjzzq() {
        return this.cpsjzzq;
    }

    public void setCpsjzzq(String cpsjzzq) {
        this.cpsjzzq = cpsjzzq;
    }

    public String getSjfjzzq() {
        return this.sjfjzzq;
    }

    public void setSjfjzzq(String sjfjzzq) {
        this.sjfjzzq = sjfjzzq;
    }

    public Integer getSjxh() {
        return this.sjxh;
    }

    public void setSjxh(Integer sjxh) {
        this.sjxh = sjxh;
    }

    public String getOrgsname() {
        return this.orgsname;
    }

    public void setOrgsname(String orgsname) {
        this.orgsname = orgsname;
    }

    public String getJgtxbh() {
        return this.jgtxbh;
    }

    public void setJgtxbh(String jgtxbh) {
        this.jgtxbh = jgtxbh;
    }

    public String getSf24yy() {
        return this.sf24yy;
    }

    public void setSf24yy(String sf24yy) {
        this.sf24yy = sf24yy;
    }

    public String getRotationpic1() {
        return rotationpic1;
    }

    public void setRotationpic1(String rotationpic1) {
        this.rotationpic1 = rotationpic1;
    }

    public String getRotationpic2() {
        return rotationpic2;
    }

    public void setRotationpic2(String rotationpic2) {
        this.rotationpic2 = rotationpic2;
    }


    public String getRotationpic3() {
        return rotationpic3;
    }

    public void setRotationpic3(String rotationpic3) {
        this.rotationpic3 = rotationpic3;
    }

    public String getRotationpic4() {
        return rotationpic4;
    }

    public void setRotationpic4(String rotationpic4) {
        this.rotationpic4 = rotationpic4;
    }

    public String getRotationpic5() {
        return rotationpic5;
    }

    public void setRotationpic5(String rotationpic5) {
        this.rotationpic5 = rotationpic5;
    }

    public String getRotationpic6() {
        return rotationpic6;
    }

    public void setRotationpic6(String rotationpic6) {
        this.rotationpic6 = rotationpic6;
    }

    public String getRotationpic7() {
        return rotationpic7;
    }

    public void setRotationpic7(String rotationpic7) {
        this.rotationpic7 = rotationpic7;
    }

    public Integer getMiniappshopid() {
        return miniappshopid;
    }

    public void setMiniappshopid(Integer miniappshopid) {
        this.miniappshopid = miniappshopid;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getAcewillshopid() {
        return acewillshopid;
    }

    public void setAcewillshopid(String acewillshopid) {
        this.acewillshopid = acewillshopid;
    }

    public String getOmpid() {
        return ompid;
    }

    public void setOmpid(String ompid) {
        this.ompid = ompid;
    }
}