package com.tzx.receiver.entity.msg;

import javax.persistence.*;

/**
 * OMP BOH ID关联配置实体
 */

public class OmpBohIdRef {


    /**
     * id
     */
    private String id;
    /**
     * 机构序号
     */
    private Long jgxh;
    /**
     * omp品牌id
     */
    private String ompBrandId;

    /**
     * 品牌名称
     */
    private String name;

    /**
     * 品牌ID
     */
    private Long brandId;

    /**
     * BOH ID
     */
    private Long bohId;

    /**
     * OMP ID
     */
    private Long ompId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getJgxh() {
        return jgxh;
    }

    public void setJgxh(Long jgxh) {
        this.jgxh = jgxh;
    }

    public String getOmpBrandId() {
        return ompBrandId;
    }

    public void setOmpBrandId(String ompBrandId) {
        this.ompBrandId = ompBrandId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getBrandId() {
        return brandId;
    }

    public void setBrandId(Long brandId) {
        this.brandId = brandId;
    }

    public Long getBohId() {
        return bohId;
    }

    public void setBohId(Long bohId) {
        this.bohId = bohId;
    }

    public Long getOmpId() {
        return ompId;
    }

    public void setOmpId(Long ompId) {
        this.ompId = ompId;
    }
}
