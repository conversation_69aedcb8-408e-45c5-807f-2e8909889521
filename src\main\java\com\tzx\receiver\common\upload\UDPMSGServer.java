package com.tzx.receiver.common.upload;

import com.tzx.receiver.common.utils.SendXML2RifMQ;
import com.tzx.receiver.common.utils.SpringContextHolder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetAddress;
import java.util.ResourceBundle;

public class UDPMSGServer extends Thread {
	private  ResourceBundle tzxresource = null;
	private DatagramSocket server = null;
	private byte[] recvBuf;
	private static int udpPort = 0;
	private  UpLoadTaskList upLoadTaskList;

	private static UDPMSGServer listener = null;

	private volatile boolean isRun = true;

	protected Logger	logger	= LoggerFactory.getLogger(getClass());

	static{
		listener = new UDPMSGServer();

	}

	private UDPMSGServer(){
		
	}
	
//	public static void init(UpLoadTaskList aupLoadTaskList,int audpPort){
//		udpPort = audpPort;
//		upLoadTaskList = aupLoadTaskList;
//	}
	
	private void close(){
		isRun = false;
	}
	
	public static void listenStart(){


		listener.start();
	}
	
	public static void listenStop(){
		listener.close();
	}
	


	public void run() {
		logger.info("UDP服务启动");
		if (upLoadTaskList==null){
			upLoadTaskList = SpringContextHolder.getBean("upLoadTaskList");
		}
		tzxresource = ResourceBundle.getBundle("tzxmqconfig");
		if(udpPort==0){
			String s= tzxresource.getString("msgudpport");
			udpPort = Integer.parseInt(s);
		}

		while (isRun) {
			try {
//				InetAddress desIp = InetAddress.getLocalHost();
				server = new DatagramSocket(udpPort);
			} catch (Exception e) {
				logger.info(udpPort+"冲突");
//			e.printStackTrace();
				return;
			}
			try{
				recvBuf = new byte[400];
				String recvStr = "";
				try {
					DatagramPacket recvPacket = new DatagramPacket(recvBuf,	recvBuf.length);
					server.receive(recvPacket);
					recvStr = new String(recvPacket.getData(), 0, recvPacket.getLength(), "gb2312");
					logger.info("接收到消息:" + recvStr);
					//接收到消息后，发送至扫描的队列
					upLoadTaskList.addTask(recvStr);

				} catch (Exception e) {

					logger.info("接受消息发生异常！");
				}
			}finally {
				try {
					server.close();
				} catch (Exception e) {
					e.printStackTrace();
				}
			}

		}
		logger.info("UDP服务关闭");
	}
}