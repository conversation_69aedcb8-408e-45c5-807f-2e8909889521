package com.tzx.commapi.rest.vo;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.math.BigDecimal;
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PMSCondiment {
   private String order_item_id	;//	订单明细ID	是
   private String condiment_id	;//	菜品口味ID	是	可重复，必须大于 0，不确定就传 1
   private String condiment_code	;//	口味编码	否
   private String condiment_name	;//	口味中文名称	是
   private String en_condiment_name	;//	口味英文名称	是	可以传空字符串
   private BigDecimal price	;//	口味价格	否
   private BigDecimal condimen_total	;//	口味总金额	否
   private Integer sequence	;//	口味顺序	否
   private BigDecimal qty	;//	数量	是	必须大于或等于 0

    public PMSCondiment() {
    }

    public PMSCondiment(String order_item_id, String condiment_id, String condiment_name) {
        this.order_item_id = order_item_id;
        this.condiment_id = condiment_id;
        this.condiment_name = condiment_name;

        this.price = BigDecimal.ZERO;//价格写死0
        this.condimen_total = BigDecimal.ZERO;//总价写死0
        this.sequence = Integer.parseInt(order_item_id);//序号写死ID
        this.condiment_code = "00"+condiment_id; //编码写死ID加前缀
        this.en_condiment_name = "";//英文名称写死空值
        this.qty = BigDecimal.ONE;//数量写死1

    }

    public String getOrder_item_id() {
        return order_item_id;
    }

    public void setOrder_item_id(String order_item_id) {
        this.order_item_id = order_item_id;
    }

    public String getCondiment_id() {
        return condiment_id;
    }

    public void setCondiment_id(String condiment_id) {
        this.condiment_id = condiment_id;
    }

    public String getCondiment_code() {
        return condiment_code;
    }

    public void setCondiment_code(String condiment_code) {
        this.condiment_code = condiment_code;
    }

    public String getCondiment_name() {
        return condiment_name;
    }

    public void setCondiment_name(String condiment_name) {
        this.condiment_name = condiment_name;
    }

    public String getEn_condiment_name() {
        return en_condiment_name;
    }

    public void setEn_condiment_name(String en_condiment_name) {
        this.en_condiment_name = en_condiment_name;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getCondimen_total() {
        return condimen_total;
    }

    public void setCondimen_total(BigDecimal condimen_total) {
        this.condimen_total = condimen_total;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    public BigDecimal getQty() {
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }
}
