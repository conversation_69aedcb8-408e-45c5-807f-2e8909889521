package com.tzx.receiver.common.utils;

import com.tzx.dyndatasource.DSType;
import com.tzx.dyndatasource.DynamicDataSource;
import com.tzx.receiver.common.upload.UploadTask;
import com.tzx.receiver.entity.UploadLog;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019-04-20
 * @Descption
 **/
public class DBUtils {


    //    private static
    private static JdbcTemplate jdbcTemplate;

    public static JdbcTemplate getJdbcTemplate() {
        return jdbcTemplate;
    }

    public static void setJdbcTemplate(JdbcTemplate jdbcTemplate) {
        DBUtils.jdbcTemplate = jdbcTemplate;
    }

    public static String getGGCSK(String sdbt) {
        //因为static方法aop切不到，所以这里手工设置一下
        DynamicDataSource.setDataSource(DSType.PROD.getValue());
        try {
            //应该以下发库为准，否则更换地址后，要做一次同步菜品才能生效
            String sql = "select sdnr from ts_ggcsk where sdbt = ?";
            SqlRowSet sqlRowSet = jdbcTemplate.queryForRowSet(sql, sdbt);
            if (sqlRowSet.next()) {
                return sqlRowSet.getString(1) == null ? "" : sqlRowSet.getString(1);
            } else {
                return "";
            }

        } catch (Exception e) {
            return "";
        }
    }

    public static String insertUploadDBLog(String msg) {
        //因为static方法aop切不到，所以这里手工设置一下
        DynamicDataSource.setDataSource(DSType.PROD.getValue());
        String uuid = UUIDUtils.UUIdGenerator();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String sql = "delete from tq_uploadlog where starttime <= '" + sdf.format(DateUtils.addDays(new Date(), -2)) + "'";
        jdbcTemplate.update(sql);

        sql = "delete from tq_uploadlog  \n" +
                "where id not in (select id from tq_uploadlog ORDER BY id desc limit 500);";
        jdbcTemplate.update(sql);

        sql = "insert into tq_uploadlog(sourcemsg,guid,starttime)values(?,?,?)";
        jdbcTemplate.update(sql, new Object[]{msg, uuid, sdf.format(new Date())});
        return uuid;
    }

    public static void deleteUploadDBLog(String guid){
        String sql = "delete from  tq_uploadlog  where guid = ?";
        jdbcTemplate.update(sql, new Object[]{ guid});
    }

    public static String updateUploadDBLog(String field, String value, String guid) {
        //因为static方法aop切不到，所以这里手工设置一下
        DynamicDataSource.setDataSource(DSType.PROD.getValue());
        String uuid = UUIDUtils.UUIdGenerator();
        String sql = "update tq_uploadlog set " + field + " = ? where guid = ?";
        jdbcTemplate.update(sql, new Object[]{value, guid});
        return uuid;
    }

    public static SqlRowSet getDataByTable(String tableName) {
        //因为static方法aop切不到，所以这里手工设置一下
        DynamicDataSource.setDataSource(DSType.PROD.getValue());
        SqlRowSet sqlRowSet = null;
        try {
            String sql = "select * from " + tableName;
            sqlRowSet = jdbcTemplate.queryForRowSet(sql);
        } catch (DataAccessException e) {

        }
        return sqlRowSet;
    }

    public static void writeReportTransProgres(UploadTask uploadTask) {
        //因为static方法aop切不到，所以这里手工设置一下
        DynamicDataSource.setDataSource(DSType.PROD.getValue());
        String sTableName = "tq_bbscjdk";

        try {
			String cSQL = "";
			if (uploadTask.getParam().getCommand().equalsIgnoreCase("DAYEND")) {
				cSQL = " delete from " + sTableName + " where mlmc = 'DAYEND' and bbrq = '" + uploadTask.getReportDate() + "'";
				jdbcTemplate.update(cSQL);
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	
				cSQL = " Insert into " + sTableName + "  (MLMC,  BBRQ, curprogres, maxprogres, starttime,curcode,curmsg) " + " Values(?, ?, ?, ?, ?,'0','解析命令参数成功') ; ";
				jdbcTemplate.update(cSQL, new Object[]{uploadTask.getParam().getCommand(), DateUtils.parseDate(uploadTask.getReportDate(), "yyyy-MM-dd"), 0, 3, new Date()});
			}
        } catch (ParseException e) {
            e.printStackTrace();
        }

    }

    public static void updateReportTransProgres(UploadTask uploadTask, int aCurProgres, int aMaxProgres, int acode, String amsg) {
        //因为static方法aop切不到，所以这里手工设置一下
        DynamicDataSource.setDataSource(DSType.PROD.getValue());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        
        String cSQL = "";
        		
        if(uploadTask.getParam().getCommand().equalsIgnoreCase("DAYEND")){
        	cSQL = " update tq_bbscjdk set curprogres = ? ,maxprogres = ?,stoptime = ? ,curcode=?," + "curmsg=? where mlmc = 'DAYEND' and bbrq = '" + uploadTask.getReportDate() + "'" ;
        	jdbcTemplate.update(cSQL, new Object[]{aCurProgres, aMaxProgres, new Date(), acode, amsg});
        }
    }

    public static List<UploadLog> getUploadLogList() {
        //因为static方法aop切不到，所以这里手工设置一下
        DynamicDataSource.setDataSource(DSType.PROD.getValue());
        String sql = "select * from tq_uploadlog where runresult is not null  and coalesce(command,'') <> ''" +
                " order by id desc limit 100";
        return jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(UploadLog.class));
    }

    public static void insertBBSCRZK(UploadTask uploadTask) {
        //因为static方法aop切不到，所以这里手工设置一下
        DynamicDataSource.setDataSource(DSType.PROD.getValue());
        String cSQL = " delete from tq_bbscrzk where BBRQ = ? and FDJGXH= ? and MLMC=?; ";
        cSQL = cSQL + " Insert into tq_bbscrzk (MLMC, FDJGXH, BBRQ, BBBC, WJM, CZSJ, CCSJ, CCCS, CLBZ, CZRYBH, CZRYMC, SKJH, BZ) " +
                "  Values(?, ?, ?, ?, ?, ?, ?, 0, '-2', ?, ?, ?, ?) ; ";
        try {
            jdbcTemplate.update(cSQL, new Object[]{DateUtils.parseDate(uploadTask.getReportDate(), "yyyy-MM-dd"),
                          uploadTask.getOrganizeId(),uploadTask.getParam().getCommand(),uploadTask.getParam().getCommand(),
                          uploadTask.getOrganizeId(), DateUtils.parseDate(uploadTask.getReportDate(), "yyyy-MM-dd"),
                    uploadTask.getShift(),uploadTask.getFileName(),new Date(),new Date(),uploadTask.getOpId(),"","",""});
        } catch (ParseException e) {
            e.printStackTrace();
        }
    }

    public static void execSQL(String sql) {
        //因为static方法aop切不到，所以这里手工设置一下
        DynamicDataSource.setDataSource(DSType.PROD.getValue());
        jdbcTemplate.update(sql);

    }
    public static String getStringBySQL(String sql){
        DynamicDataSource.setDataSource(DSType.PROD.getValue());
        SqlRowSet sqlRowSet = jdbcTemplate.queryForRowSet(sql);
        String result = "";
        if(sqlRowSet.next()){
            result = sqlRowSet.getString(1);
        }
        return result;
    }

    /**
     * 保存日志
     */
    public static void insertLog(String url, String state) {
        //因为static方法aop切不到，所以这里手工设置一下
        DynamicDataSource.setDataSource(DSType.PROD.getValue());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String sql = "insert into tq_downloadlog (downloadurl, state, creattime) values (?, ?, ?)";
        jdbcTemplate.update(sql, new Object[]{url, state, sdf.format(new Date())});
    }
}
