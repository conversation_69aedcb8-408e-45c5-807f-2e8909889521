package com.tzx.test;


import com.tzx.publics.util.DateUtil;
import com.tzx.publics.util.MD5Util;
import com.tzx.publics.util.StringUtil;
import com.tzx.receiver.common.upload.UploadGloVar;
import com.tzx.receiver.common.utils.DBUtils;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.OutputFormat;
import org.dom4j.io.XMLWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.jdbc.support.rowset.SqlRowSetMetaData;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.security.GeneralSecurityException;
import java.security.MessageDigest;
import java.sql.Types;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2019-04-23
 * @Descption
 **/
public class Test {

    @Autowired
    JdbcTemplate jdbcTemplate;
    /**
     * MD5 加密数据
     * @param data
     * @return
     * @throws IOException
     */
    public static byte[] encryptMD5(String data) throws IOException {
        byte[] bytes = null;
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            bytes = md.digest(data.getBytes("UTF-8"));
        } catch (GeneralSecurityException gse) {
            throw new IOException(gse);
        }
        return bytes;
    }

    /**
     * 把二进制转化为大写的十六进制
     * @param bytes
     * @return
     */
    private static String byte2hex(byte[] bytes) {
        StringBuilder sign = new StringBuilder();
        for (int i = 0; i < bytes.length; i++) {
            String hex = Integer.toHexString(bytes[i] & 0xFF);
            if (hex.length() == 1) {
                sign.append("0");
            }
            sign.append(hex.toUpperCase());
        }
        return sign.toString();
    }
    /**
     * 字符串转unicode
     *
     * @param str
     * @return
     */
    public static String stringToUnicode(String str) {
        StringBuffer sb = new StringBuffer();
        char[] c = str.toCharArray();
        for (int i = 0; i < c.length; i++) {
            sb.append("\\u" + Integer.toHexString(c[i]));
        }
        return sb.toString();
    }

    /**
     * unicode转字符串
     *
     * @param unicode
     * @return
     */
    public static String unicodeToString(String unicode) {
        StringBuffer sb = new StringBuffer();
        String[] hex = unicode.split("\\\\u");
        for (int i = 1; i < hex.length; i++) {
            int index = Integer.parseInt(hex[i], 16);
            sb.append((char) index);
        }
        return sb.toString();
    }
    public static void main(String[] args) {
        JSONObject orderInfo = new JSONObject();
        double l = orderInfo.optDouble("orde_dis_amount");
        System.out.println(l);
        System.out.println(l + 1);
        String url = null;
        String retundApi = "/api/order/refundOrder";
        System.out.println(url + retundApi);

        JSONObject param = new JSONObject();
        String callbackShopName = null;
        param.put("callbackShopName", callbackShopName);
        param.put("out_order_id", "11");
        param.put("sign", "22");
        double m = param.optDouble("callbackShopName");
        System.out.println(m);
        System.out.println(param.toString());
        try {
//            Test test = new Test();
//            DBUtils.setJdbcTemplate(test.jdbcTemplate);

            System.out.println(DBUtils.getGGCSK("FDJGXH"));
            String zfbStr = "实付:7.00\r\n优惠:0.00\r\n付款账户:2088602019987424\r\nAPPID:null";
            String[] zfbstrs = zfbStr.split("\r\n");
            for(String str : zfbstrs){
                System.out.println(str);
            }
            System.out.println("付款账户=" + zfbstrs[2].split(":")[1]);
            System.out.println("APPID=" + zfbstrs[3].split(":")[1]);


            //测试sign
            Map<String, String> map = new HashMap<String, String>();
            map.put("sign_method", "md5");
            map.put("v", "1.0");
            map.put("timestamp", "20110115010101");
            map.put("app_key", "test");
            map.put("password", "11111hhhhhhxxx");
            map.put("method", "th.card.cardinfo.get");
            map.put("format", "json");
            map.put("card_id", "8686860211000000516");

            String rawStr = "微风";
            System.out.println(rawStr);
            byte[] data = (rawStr).getBytes("UTF-8");
            String utf8Str = new String(data,"UTF-8");
            System.out.println(utf8Str);
            System.out.println(URLEncoder.encode(utf8Str,"UTF-8"));
            System.out.println(stringToUnicode(utf8Str));



            //这里将map.entrySet()转换成list
            List<Map.Entry<String,String>> list = new ArrayList<Map.Entry<String,String>>(map.entrySet());
            //然后通过比较器来实现排序
            Collections.sort(list, new Comparator<Map.Entry<String, String>>() {
                @Override
                public int compare(Map.Entry<String, String> o1, Map.Entry<String, String> o2) {
                    return o1.getKey().compareTo(o2.getKey());
                }
            });
            StringBuilder sbParam = new StringBuilder();
            sbParam.append("test");
            for(Map.Entry<String,String> mapping:list){
                System.out.println(mapping.getKey()+":"+mapping.getValue());
                sbParam.append(mapping.getKey()).append(mapping.getValue());
            }
            sbParam.append("test");
            System.out.println(sbParam.toString());
            String md5Param = MD5Util.md5(sbParam.toString()).toUpperCase();
            System.out.println(md5Param);
            System.out.println(byte2hex(encryptMD5(sbParam.toString())));




//            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
//            SimpleDateFormat sdf2 = new SimpleDateFormat("yyyyMMddHHmmss");
////            String basePath = "../UploadCaches/" ;
////            String path = "../UploadCaches/" + sdf.format(new Date());
//            String basePath = new File(System.getProperty("user.dir")).getParent()  + File.separator + "UploadCaches";
//            File baseFile = new File(basePath);
//            String path = basePath + File.separator + sdf.format(new Date());
//            File file = new File(path);
//            //文件夹不存在，则创建
//            if(!file.exists()){
//                file.mkdirs();
//            }
//            //删除以前数据
//            File[] files = baseFile.listFiles();
//            if (files != null){
//                String firstFile = basePath + sdf.format(DateUtils.addDays(new Date(),-UploadGloVar.KEEY_FILE_DAY));
//                for (File file1 : files) {
//                    //比较名称比他小，就删除
//                    if( file1.isDirectory()&&file1.getAbsolutePath().compareTo(firstFile)<0){
//                        file1.delete();
//                    }
//                }
//            }
//
//
//            //文件名称
//            String fileName = "Test" + sdf2.format(new Date()) + ".xml";
//
//            //开始编辑XML的内容
//            Document document= DocumentHelper.createDocument();
////            document.setXMLEncoding("GB2312");
//            Element root = document.addElement("DATAPACKET");
//            //XML头内容
//            Element head = root.addElement("HEAD");
//            Element Version = head.addElement("Version");
//            Version.addText("1");
//            Element SRC = head.addElement("SRC");
//            Element DEVID = head.addElement("DEVID");
//            DEVID.addText("");
//            Element APP = head.addElement("APP");
//            APP.addText("TZX-StoreBusinessSystem");
//            Element TID = head.addElement("TID");
//            TID.addText("TXDTASHUOCHW35DG39SG0LHHAW04YSDFGH");
//            Element MSGID = head.addElement("MSGID");
//            MSGID.addText("{" + UUID.randomUUID().toString().toUpperCase() + "}");
//            Element CORID = head.addElement("CORID");
//            CORID.addText("黄金饹面");
//
//            // 美化格式,并输出
//            OutputFormat format = OutputFormat.createPrettyPrint();
//            format.setEncoding("GB2312");
////            format.setEncoding("GBK");
//            XMLWriter writer = new XMLWriter( new FileOutputStream( path + "/" + fileName ),format );
//            writer.write( document );
//            writer.close();

        } catch (Exception e) {

        }
    }
}
