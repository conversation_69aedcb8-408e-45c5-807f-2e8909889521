package com.tzx.miniapp.rest.mapper;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.tzx.miniapp.rest.model.TqFklslsk;
import com.tzx.miniapp.rest.model.TqJtztk;
import com.tzx.miniapp.rest.model.TqWdkCouponTemp;
import com.tzx.miniapp.rest.model.TqZdk;
import com.tzx.miniapp.rest.model.TsBmkzk;
import com.tzx.miniapp.rest.model.TsFkfssdk;
import com.tzx.miniapp.rest.model.TsGgcsk;
import com.tzx.miniapp.rest.model.TsYhfssdk;
import com.tzx.miniapp.rest.vo.AccountsOrder;
import com.tzx.miniapp.rest.vo.BtPayments;
import com.tzx.miniapp.rest.vo.BtYdd;
import com.tzx.miniapp.rest.vo.BtYdxm2;
import com.tzx.miniapp.rest.vo.DishVo;
import com.tzx.miniapp.rest.vo.TqYyddylsk;
import com.tzx.publics.base.MyMapper;
import com.tzx.receiver.entity.msg.EcoTypeDic;

public interface MiniAppFirstPayMapper extends MyMapper<BtYdd> {

    public int getBillStatus(@Param("zdbh") String zdbh);

    public BtYdd getYdd(@Param("zdbh") String zdbh);

    public int calcYddPayActive(@Param("zdbh") String zdbh);

    public void insertBtYddPayActive(@Param("yddh") String yddh, @Param("pay_name") String pay_name, @Param("pay_money") double pay_money, @Param("couponcodes") String couponcodes, @Param("active_type") String active_type, @Param("active_id") int active_id);

    public double selectMemberPrice(@Param("zdbh") String zdbh);

    public TqJtztk getJtZtk(@Param("bbrq") Date bbrq);

    public int yddToZd(@Param("syydh") String syydh, @Param("abbrq") Date abbrq, @Param("aczry") String aczry, @Param("aygdlcs") int aygdlcs);

    /**
     * 根据微生活付款类型查询系统内对应付款方式
     *
     * @param fkfsmc2
     * @return
     */
    public TsFkfssdk getFkfs(@Param("fkfsmc2") String fkfsmc2);

    /**
     * 根据编码查询系统内对应付款方式
     *
     * @param fkfsbh
     * @return
     */
    public TsFkfssdk getFkfsByCode(@Param("fkfsbh") String fkfsbh);

    public AccountsOrder accountsOrder(@Param("szdbh") String szdbh, @Param("ijzid") int ijzid, @Param("ifkje") BigDecimal ifkje, @Param("ifksl") int ifksl, @Param("sfkhm") String sfkhm, @Param("ssfzhm") String ssfzhm, @Param("slxdh") String slxdh, @Param("sfkbz") String sfkbz, @Param("sskjh") String sskjh, @Param("sskyh") String sskyh);

    public TqZdk getZdbhByYdd(@Param("yddbh") String yddbh);

    public int updateZdkNoQch(@Param("kdzdbh") String kdzdbh, @Param("ksjzsj") Date ksjzsj, @Param("jzjssj") Date jzjssj, @Param("source") String source, @Param("jzsx") String jzsx, @Param("zwbh") String zwbh, @Param("bcid") int bcid, @Param("cwlxbh") String cwlxbh);

    public int updateZdk(@Param("kdzdbh") String kdzdbh, @Param("ksjzsj") Date ksjzsj, @Param("jzjssj") Date jzjssj, @Param("source") String source, @Param("qch") String qch, @Param("jzsx") String jzsx, @Param("zwbh") String zwbh, @Param("bcid") int bcid, @Param("cwlxbh") String cwlxbh);

    public int updateFklslsk(@Param("kdzdbh") String kdzdbh, @Param("jzzdbh") String jzzdbh, @Param("bcid") int bcid);

    public void insertBtPayments(@Param("btpay") BtPayments btpay);

    public String getKdzdbh(@Param("yddh") String yddh);

    public int updateBtYdd(@Param("yddh") String yddh, @Param("bill_num") String bill_num, @Param("ddzt") String ddzt, @Param("ddsj") String ddsj, @Param("shrbh") String shrbh, @Param("zlbh") String zlbh);

    public void insertTqYyddylsk(@Param("yyddyls") TqYyddylsk yyddyls);

    public int updateBtYddToCancel(@Param("yddh") String yddh, @Param("ddzt") String ddzt);

    public TsBmkzk getBh(@Param("bmc") String bmc, @Param("zdmc") String zdmc);

    public TsGgcsk getGgcsToWs(@Param("cdzd") String cdzd);

    public TsGgcsk getGgcsToCsh(@Param("cdzd") String cdzd);

    public int updateBh(@Param("nr") String nr, @Param("bmc") String bmc, @Param("zdmc") String zdmc);

    public String callP_SendKVSData(@Param("szdbh") String szdbh);

    public String getZdbhByYddh(@Param("yddh") String yddh);

    public int addYhfs(@Param("szdbh") String szdbh, @Param("aitemid") int aitemid, @Param("sskjh") String sskjh, @Param("ixmsl") int ixmsl);

    public List<Map<String, Integer>> findDiscounts(@Param("yddh") String yddh);

    public int calcmoney(@Param("szdbh") String szdbh);

    //更新卧单库是否加价菜
    public int updateJjcrwid(@Param("kdzdbh") String kdzdbh, @Param("bcid") int bcid);

    public int getBcid(@Param("bbrq") Date bbrq);

    public TqZdk getZdk(@Param("yddh") String yddh);

    public TqZdk getZdbhByZdbh(@Param("zdbh") String zdbh);

    public int calcAceWillActivity(@Param("zdbh") String zdbh);

    public void updateYddActiveCouponInfo(@Param("yddh") String yddh, @Param("activeID") int activeID, @Param("buyDish") String buyDish, @Param("giftDish") String giftDish, @Param("couponMoney") double couponMoney, @Param("couponCode") String couponCode);

    public int updateZdkZs(@Param("zzbz") String zzbz, @Param("kdzdbh") String kdzdbh, @Param("jzzdbh") String jzzdbh, @Param("jzbbrq") Date jzbbrq, @Param("jzsj") Date jzsj, @Param("jzcs") int jzcs, @Param("jzskjh") String jzskjh, @Param("jzczry") String jzczry, @Param("jzsx") String jzsx, @Param("ksjzsj") Date ksjzsj, @Param("jzjssj") Date jzjssj, @Param("jzbcid") int jzbcid, @Param("xfks") int xfks, @Param("cwlxbh") String cwlxbh);

    public int updateWdkZs(@Param("kdzdbh") String kdzdbh, @Param("jzzdbh") String jzzdbh, @Param("jzskjh") String jzskjh, @Param("jzbbrq") Date jzbbrq, @Param("jzbcid") int jzbcid);

    public int updateFklslskZs(@Param("kdzdbh") String kdzdbh, @Param("jzzdbh") String jzzdbh, @Param("jzbcid") int jzbcid);

    public void zRtr(@Param("bill_num") String bill_num);

    public TsFkfssdk getFkfsByZs(@Param("fkfsmc2") String fkfsmc2, @Param("yl3") String yl3);

    public String getbillid(@Param("skjh") String skjh, @Param("tname") String tname, @Param("fname") String fname);

    public DishVo getDsfyh(@Param("yhsx") String yhsx);

    public void insertTqWdkCouponTemp(List<TqWdkCouponTemp> twctList);

    public void insertDyrwk(@Param("jtbh") String jtbh, @Param("qqlx") int qqlx, @Param("djlx") int djlx, @Param("qdsj") String qdsj, @Param("clbz") int clbz);

    public TsYhfssdk getBjyhYh(@Param("yhfsbh") String yhfsbh);

    public List<TqFklslsk> getFklslsk(@Param("kdzdbh") String kdzdbh);

    public List<BtYdxm2> getJjcidList(@Param("tsyddbh") String tsyddbh);

    public int updateYdJjcrwid(@Param("kdzdbh") String kdzdbh, @Param("ydxm2id") int ydxm2id, @Param("jjcydxm2id") int jjcydxm2id);

    public List<TqFklslsk> findBalanceList(@Param("zdbh") String zdbh, @Param("fkfsbh") String fkfsbh);

    public double findBalanceAmount(@Param("zdbh") String zdbh, @Param("fkfsbh") String fkfsbh);

    public int yddToZdXcx(@Param("yydh") String yydh, @Param("lsdh") String lsdh, @Param("kdzdbh") String kdzdbh, @Param("jzzdbh") String jzzdbh, @Param("bbbc") int bbbc, @Param("bbrq") Date bbrq, @Param("czry") String czry, @Param("ygdlcs") int ygdlcs);

    public EcoTypeDic findEcotypedic(@Param("fkfsbh") String fkfsbh, @Param("thirdcode") String thirdcode);

    public int updateYddToTotalprice(@Param("yddh") String yddh, @Param("payGive") double payGive);

    public EcoTypeDic findEcotypedicByCode(@Param("thirdcode") String thirdcode, @Param("tzxcode") String tzxcode);

    public DishVo getDsfyhByCode(@Param("yhfsbh") String yhfsbh);

    public List<BtYdxm2> getQmJjcidList(@Param("tsyddbh") String tsyddbh);

    TsYhfssdk getDsfyhByYhfsmc2(@Param("yhfsmc2") String yhfsmc2);

    int updateTcid(@Param("kdzdbh") String kdzdbh);
}