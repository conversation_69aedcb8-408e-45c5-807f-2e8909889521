package com.tzx.receiver.entity.msg;

import java.io.Serializable;
import java.util.Date;

public class CpysszOne
  implements Serializable
{
  private Integer id;
  private String cpysbh;
  private String cpysmc1;
  private String cpysmc2;
  private Date ksrq;
  private Date jsrq;
  private String kssj;
  private String jssj;
  private String bz;
  private String yl1;
  private String yl2;
  private String yl3;
  private Integer jgxh;
  private Integer yl4;
  private Double yl5;

  public Integer getId()
  {
    return this.id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public String getCpysbh() {
    return this.cpysbh;
  }

  public void setCpysbh(String cpysbh) {
    this.cpysbh = cpysbh;
  }

  public String getCpysmc1() {
    return this.cpysmc1;
  }

  public void setCpysmc1(String cpysmc1) {
    this.cpysmc1 = cpysmc1;
  }

  public String getCpysmc2() {
    return this.cpysmc2;
  }

  public void setCpysmc2(String cpysmc2) {
    this.cpysmc2 = cpysmc2;
  }

  public Date getKsrq() {
    return this.ksrq;
  }

  public void setKsrq(Date ksrq) {
    this.ksrq = ksrq;
  }

  public Date getJsrq() {
    return this.jsrq;
  }

  public void setJsrq(Date jsrq) {
    this.jsrq = jsrq;
  }

  public String getKssj() {
    return this.kssj;
  }

  public void setKssj(String kssj) {
    this.kssj = kssj;
  }

  public String getJssj() {
    return this.jssj;
  }

  public void setJssj(String jssj) {
    this.jssj = jssj;
  }

  public String getBz() {
    return this.bz;
  }

  public void setBz(String bz) {
    this.bz = bz;
  }

  public String getYl1() {
    return this.yl1;
  }

  public void setYl1(String yl1) {
    this.yl1 = yl1;
  }

  public String getYl2() {
    return this.yl2;
  }

  public void setYl2(String yl2) {
    this.yl2 = yl2;
  }

  public String getYl3() {
    return this.yl3;
  }

  public void setYl3(String yl3) {
    this.yl3 = yl3;
  }

  public Integer getJgxh() {
    return this.jgxh;
  }

  public void setJgxh(Integer jgxh) {
    this.jgxh = jgxh;
  }

  public Integer getYl4() {
    return this.yl4;
  }

  public void setYl4(Integer yl4) {
    this.yl4 = yl4;
  }

  public Double getYl5() {
    return this.yl5;
  }

  public void setYl5(Double yl5) {
    this.yl5 = yl5;
  }
}