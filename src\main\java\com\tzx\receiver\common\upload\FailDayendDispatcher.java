package com.tzx.receiver.common.upload;

import com.tzx.publics.util.DateUtil;
import com.tzx.receiver.common.utils.DBUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2019-04-26
 * @Descption
 **/
@Component
public class FailDayendDispatcher {
    @Value("${msg.upload.reuploaddayend}")
    private String reuploaddayend ;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private UpLoadTaskList upLoadTaskList;
    public void doFailDayend(){
        if(!reuploaddayend.equals("1")){
            return;
        }
        String sql =" SELECT DISTINCT A.bbrq,c.clbz,b.czsj ,c.id  FROM tq_jtztk A" +
                " INNER JOIN tq_jtztk B ON A.BBRQ = B.BBRQ AND B.cznr = 'JSSY' AND B.JHID = '99' and A.cznr = 'KSSY'" +
                " LEFT JOIN tq_bbscrzk C ON A.bbRQ = C.bbrq AND C.mlmc = 'DAYEND' " +
                " WHERE A.BBRQ >= current_date - 15 " +
                " ORDER BY bbrq" ;
        SqlRowSet sqlRowSet = jdbcTemplate.queryForRowSet(sql);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        while (sqlRowSet.next()){
            if(StringUtils.isEmpty(sqlRowSet.getString("id"))//没有关联到日志的
                    ||(!StringUtils.isEmpty(sqlRowSet.getString("clbz"))&&
                         !sqlRowSet.getString("clbz").equals("0")&&
                         !sqlRowSet.getString("clbz").equals("3"))//clbz不是0的，或者clbz不是3的
                    ||(!StringUtils.isEmpty(sqlRowSet.getString("clbz"))&&
                       sqlRowSet.getString("clbz").equals("3")&&
                       !StringUtils.isEmpty(sqlRowSet.getString("czsj"))&&
                        DateUtil.parseDateAll(sqlRowSet.getString("czsj"))!=null&&
                       (new Date()).getTime() - DateUtil.parseDateAll(sqlRowSet.getString("czsj")).getTime()
                               > 1000*60*5&&  //clbz=3的，并且超过3分钟总部还没有处理完的
                       (new Date()).getTime() - DateUtil.parseDateAll(sqlRowSet.getString("czsj")).getTime()
                              < 1000*60*60*24) //如果传了一天还传不完那么，就不再传了
                    ){
                String vMsg = "Action=DayEnd|FDJGXH="+UploadGloVar.getOrganizeID() +"|BBRQ="+ simpleDateFormat.format(sqlRowSet.getDate(1)) +
                        "|BBBC=0|SendSKJH=99|OptBH=|OptName=|CZSJ=";
                upLoadTaskList.addTask(vMsg);
            }

        }

    }
}
