package com.tzx.receiver.common.upload;

import com.tzx.publics.listener.InitDataListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019-04-20
 * @Descption
 **/
@Component
@Lazy(true)
public class UploadKCrealTimeDispatcher {
    @Autowired
    UpLoadParamsList upLoadParamsList;
    @Autowired
    UpLoadTaskList upLoadTaskList;

    public void doUploadTimerDispatcher(){

        List<UploadParam>  timerUploadParam =  upLoadParamsList.getParamsByTimer();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        for (UploadParam uploadParam : timerUploadParam) {

            if(uploadParam.getCommand().toUpperCase().equals("KCREALTIMEBILL")){
                //实时上传 网络状态没有恢复，并且uploadTaskList 没有数据，不生成新的数据
                if((!InitDataListener.mqStatus) && upLoadTaskList.size() > 0){
                    continue;
                }
            }
            String msg = "ACTION=" + uploadParam.getCommand() + "|BBRQ="+sdf.format(new Date())+
                    "|OptBH=9999|OptName=9999|FDJGXH=" + UploadGloVar.getOrganizeID();
            upLoadTaskList.addTask(msg);
        }
    }
}
