package com.tzx.miniapp.rest.service.impl;

import com.tzx.miniapp.common.Data;
import com.tzx.miniapp.common.SysDictionary;
import com.tzx.miniapp.rest.mapper.MiniAppTqJtztkMapper;
import com.tzx.miniapp.rest.mapper.MiniAppTqZdkMapper;
import com.tzx.miniapp.rest.mapper.MiniAppTsCzykMapper;
import com.tzx.miniapp.rest.mapper.MiniAppTsJtsdkMapper;
import com.tzx.miniapp.rest.model.TqJtztk;
import com.tzx.miniapp.rest.model.TqZdk;
import com.tzx.miniapp.rest.model.TsBmkzk;
import com.tzx.miniapp.rest.model.TsCzyk;
import com.tzx.miniapp.rest.service.IMiniAppLoginService;
import com.tzx.miniapp.rest.vo.AccountsOrder;
import com.tzx.publics.util.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

@Service
public class MiniAppLoginServiceImpl implements IMiniAppLoginService {
	private final static Logger LOGGER = LoggerFactory.getLogger(MiniAppLoginServiceImpl.class);

	@Autowired
	private MiniAppTsCzykMapper tsCzykMapper;
	@Autowired
	private MiniAppTsJtsdkMapper tsJtsdkMapper;
	@Autowired
	private MiniAppTqJtztkMapper tqJtztkMapper;
	@Autowired
	private MiniAppTqZdkMapper tqZdkMapper;

	/**
	 * 登录
	 */
	@Transactional
	public void login(Data data, Data result) {
//		Map<String, Object> map = ReqDataUtil.getDataMap(data);

//		String czybh = ParamUtil.getStringValue(map, "czybh", false, null);// 操作员编号
//		String czymm = ParamUtil.getStringValue(map, "czymm", false, null);// 密码
//		String jtbm = ParamUtil.getStringValue(map, "jtbm", false, null);// 机台编码
//
//		// 根据用户名密码查询
//		TsCzyk czyk = tsCzykMapper.findLogin(czybh, czymm);
//		TsJtsdk jtsdk = tsJtsdkMapper.findByIpdz(jtbm);
//		
//		Map<String, String> bbrqMap = tqJtztkMapper.findBbrq();
//		String bbrq = DateUtil.getNowDateYYDDMM();
//		if (null != bbrqMap && bbrqMap.size() != 0) {
//			bbrq = bbrqMap.get("bbrq");
//		}

//		result.setCode(-1);
//		result.setMsg(SysDictionary.LOGIN_FAILURE_01);
//		result.setSuccess(false);
		
//		LoginCheck loginCheck = tqJtztkMapper.loginCheck(DateUtil.parseDate(bbrq), jtsdk.getJtbh());
//		if (czyk != null && jtsdk != null) {
//			if (null != loginCheck && !czybh.equals(loginCheck.getRybh())) {
//				result.setCode(-1);
//				result.setMsg("员工" + loginCheck.getRybh() + "未退出！");
//				result.setSuccess(false);
//			} else if (null != loginCheck && czybh.equals(loginCheck.getRybh())) {
				// if (czyk != null && jtsdk != null) {
//				result.setCode(SysDictionary.SUCCESS);
				result.setMsg(SysDictionary.LOGIN_SUCCESS);
//				result.setSuccess(true);
				// }
//			} else {
				// if (czyk != null && jtsdk != null) {
//				if (checkOpenState(bbrq)) {
//					int maxcs = tqJtztkMapper.getMaxYgdlcs(DateUtil.parseDate(bbrq), jtsdk.getJtbh()) + 1;
//					String cznr = "YYDL";
//					insertTqJtztk(cznr, czyk, jtsdk.getJtbh(), bbrq, maxcs);// 写入tq_jtztk机台状态库
//
////					result.setCode(SysDictionary.SUCCESS);
//					result.setMsg(SysDictionary.LOGIN_SUCCESS);
////					result.setSuccess(true);
//				} else {
//					result.setCode(-1);
//					result.setMsg("门店已打烊，请开店后登录！");
//					result.setSuccess(false);
//				}
				// }
//			}
//		}
	}
	
	
	/**
	 * 登出
	 */
	@Transactional
	public void logout(Data data, Data result) {
//		Map<String, Object> map = ReqDataUtil.getDataMap(data);
//
//		String czybh = ParamUtil.getStringValue(map, "czybh", false, null);// 操作员编号
//		String jtbm = ParamUtil.getStringValue(map, "jtbm", false, null);// 机台编码
//
//		// 根据用户名密码查询
//		TsCzyk czyk = tsCzykMapper.findByCzybh(czybh);
//		TsJtsdk jtsdk = tsJtsdkMapper.findByIpdz(jtbm);
//		
//		Map<String, String> bbrqMap = tqJtztkMapper.findBbrq();
//		String bbrq = DateUtil.getNowDateYYDDMM();
//		if (null != bbrqMap && bbrqMap.size() != 0) {
//			bbrq = bbrqMap.get("bbrq");
//		}
//
////		result.setCode(-1);
//		result.setMsg(SysDictionary.LOGOUT_FAILURE_01);
////		result.setSuccess(false);
//
//		if (czyk != null && jtsdk != null) {
//			String cznr = "YYTC";
//			int logincs = tqJtztkMapper.getYgdlcs(DateUtil.parseDate(bbrq), jtsdk.getJtbh(), czybh);
//			if(logincs != 0){
//				//未结帐单处理
//				openOrderDispose(jtsdk.getJtbh(), czybh);
//				insertTqJtztk(cznr, czyk, jtsdk.getJtbh(), bbrq, logincs);// 写入tq_jtztk机台状态库
//				tsJtsdkMapper.pTjskjqj(DateUtil.parseDate(bbrq), jtsdk.getJtbh());
//
////				result.setCode(SysDictionary.SUCCESS);
//				result.setMsg(SysDictionary.LOGOUT_SUCCESS);
////				result.setSuccess(true);
//			} else {
//				result.setMsg("未检测到登录，请强制退出！");
//			}
//			
//		}
	}
	private void openOrderDispose(String jtbh, String skyh){
		TqZdk tqZdks = tqZdkMapper.getZdCount(jtbh);
		
		if (null != tqZdks) {
			int ktbcid = tqZdkMapper.getBcid();
			String kdzdbh = tqZdks.getKdzdbh();
			tqZdkMapper.initZdk(kdzdbh + "", ktbcid);
			tqZdkMapper.delWdk(kdzdbh + "");
			AccountsOrder ao = tqZdkMapper.accountsOrder(kdzdbh, 0, new BigDecimal(0), 1, "", "", "", "", jtbh, skyh);
			long jzzdbhl = 0L;
			if (ao != null && "0".equals(ao.getA())) {
				int jzbcid = tqZdkMapper.getBcid();
				TsBmkzk tsBmkzJzzdbh = tqZdkMapper.getBh("TQ_ZDK", "JZZDBH");
				String jzzdbhOld = jtbh + tsBmkzJzzdbh.getNr();
				jzzdbhl = Long.parseLong(jzzdbhOld) + 1;
				tqZdkMapper.updateBh(subBh(jtbh, jzzdbhl), "TQ_ZDK", "JZZDBH");
				String jzzdbh = jzzdbhl + "";
				
				//更新 tq_zdk tq_wdk tq_fklslsk
				Map<String, String> bbrqMap = tqJtztkMapper.findBbrq();
				String jzbbrq = DateUtil.getNowDateYYDDMM();
				if (null != bbrqMap && bbrqMap.size() != 0) {
					jzbbrq = bbrqMap.get("bbrq");
				}
				Date jzjssj = new Date();
				tqZdkMapper.updateZdk(kdzdbh, jzzdbh, DateUtil.parseDate(jzbbrq), jzjssj, 1, jtbh, skyh, "ZDSX_YJ", jzjssj, jzjssj, jzbcid);
				tqZdkMapper.updateWdk(kdzdbh, jzzdbh, jtbh, DateUtil.parseDate(jzbbrq), jzbcid);
				tqZdkMapper.updateFklslsk(kdzdbh, jzzdbh, jzbcid);
			}
		}
		
	}
	private void insertTqJtztk(String cznr, TsCzyk czyk, String jtbh, String bbrq, int cs) {

		TqJtztk tqJtztk = new TqJtztk();
		
//		TqJtztk tqJtztks = tqJtztkMapper.findState(DateUtil.parseDate(bbrq), czyk.getCzybh(), cznr, jtbh);

//		if (null == tqJtztks) {
			tqJtztk.setJhid(jtbh);
			tqJtztk.setCznr(cznr);
			tqJtztk.setRybh(czyk.getCzybh());
			tqJtztk.setRyxm(czyk.getCzymc1());
			tqJtztk.setCzsj(new Date());
			tqJtztk.setBbrq(DateUtil.parseDate(bbrq));
			tqJtztk.setClbz("0");
			tqJtztk.setYgdlcs(cs + "");

			tqJtztkMapper.insert(tqJtztk);
//		} else {
//			if ("YYTC".equals(cznr)) {
//				tqJtztkMapper.updataCzsj(new Date(), tqJtztks.getId());
//			}
//			if ("YYDL".equals(cznr)) {
//				tqJtztkMapper.delJtztk(DateUtil.parseDate(bbrq), jtbh, czyk.getCzybh());
//			}
//		}
	}
	
	private boolean checkOpenState(String bbrq) {
		TqJtztk tqJtztks = tqJtztkMapper.checkOpenState(DateUtil.parseDate(bbrq));
		boolean b = true;
		if (null == tqJtztks) {
			b = true;
		} else {
			b = false;
		}
		return b;
	}
	
	public String subBh(String jtbh, long newBh) {

		String bhStr = newBh + "";

		return bhStr.substring(jtbh.length());
	}

}
