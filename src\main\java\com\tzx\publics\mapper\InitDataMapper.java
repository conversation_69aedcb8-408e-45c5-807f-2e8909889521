package com.tzx.publics.mapper;

import java.util.List;
import java.util.Map;

import com.tzx.publics.base.MyMapper;
import com.tzx.publics.vo.DeviceVo;
import com.tzx.publics.vo.GgcskVo;
import com.tzx.publics.vo.OrganVo;
import com.tzx.publics.vo.ThirdInfoVO;
import com.tzx.receiver.entity.msg.EcoTypeDic;
import org.apache.ibatis.annotations.Param;

public interface InitDataMapper extends MyMapper<GgcskVo> {

	public List<GgcskVo> getGgcs();
	
	public List<ThirdInfoVO> getThird();
	
	public OrganVo getOrgan();

	public String getbillid(@Param("skjh") String skjh, @Param("tname") String tname, @Param("fname") String fname);

	public int updateByInvoice(@Param("kdzdbh") String kdzdbh, @Param("kfpsqm") String kfpsqm, @Param("kfpje") double kfpje, @Param("isselprinteinvoice") String isselprinteinvoice);

	public double getKpje(@Param("kdzdbh") String kdzdbh);

	public List<EcoTypeDic> getEcoTypeDic();
	
	public GgcskVo getGgcsBySdbt(@Param("sdbt") String sdbt);
	
	public int insertGgcs(@Param("ggcs") GgcskVo ggcs);
	
	public List<DeviceVo> getDevice();

	public int updateGgcsBySdnr(@Param("sdbt") String sdbt, @Param("sdnr") String sdnr);

	public int updateGgcs(@Param("ggcs") GgcskVo ggcs);

}