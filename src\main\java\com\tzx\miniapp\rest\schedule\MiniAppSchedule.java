package com.tzx.miniapp.rest.schedule;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.tzx.miniapp.rest.service.IMiniAppOrderPrecheckService;

@Component
@EnableAsync
public class MiniAppSchedule {
	
	private final static Logger LOGGER = LoggerFactory.getLogger(MiniAppSchedule.class);
	
	@Autowired
	private IMiniAppOrderPrecheckService orderPrecheckService;
	/**
	 * 启动后延迟5分钟，以后每10分钟执行一次
	 */
	@Scheduled(initialDelay = 5 * 60 * 1000, fixedRate = 3 * 60 * 1000)
	@Async
	public void addSaleOutSchedule() {
		LOGGER.info("-检测小程序未结帐单沽清数量。");
		orderPrecheckService.scheduleAddSaleOut();
	}
}
