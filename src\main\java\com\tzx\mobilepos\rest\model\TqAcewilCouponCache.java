package com.tzx.mobilepos.rest.model;

import javax.persistence.Table;

/**
 * <AUTHOR>
 * @Date 2019-05-07
 * @Descption
 **/
@Table(name = "TQ_ACEWIL_COUPON_CACHE")
public class TqAcewilCouponCache {
    private String zdbh; // 账单编号
    private String scancode; // 券码
    private String datatype; // 目前仅对接微生活券，固定值 ： ACEWILL
    private int useok; // 状态 0：预核销成功，1：消费成功，2：查询成功
    private int yhfsid; // 优惠方式id
    private String coupontype; // 券类型 DISHCOUPON：菜品券；CASHCOUPON：代金券；DISHDISCOUNTCOUPON：折扣券
    private String couponcode; // 券id
    private double couponprice; // 券面值
    private String coupondishcode; // 菜品券关联菜品编码
    private int fkfs; // 付款方式id
    private String remark; // 微生活返回json
    private int is_usable; // 前端展示时，用于判断是否可用 0：可用，1：不可用
    private String cardcode; // 会员卡号
    private String templateid; // 券模板id
    private String coupondishcodes; // 菜品券关联菜品编码
    private String useablemsg; // 不可用原因
    private String couponname; // 券名称
    private double couponsale = 0; // 券购买价格
    private double coupon_totalmoney; // 折扣券优惠金额/最大优惠限额
    private String coupondishcodesext; // 折扣券，例外菜品编号

    public String getZdbh() {
        return zdbh;
    }

    public void setZdbh(String zdbh) {
        this.zdbh = zdbh;
    }

    public String getScancode() {
        return scancode;
    }

    public void setScancode(String scancode) {
        this.scancode = scancode;
    }

    public String getDatatype() {
        return datatype;
    }

    public void setDatatype(String datatype) {
        this.datatype = datatype;
    }

    public int getUseok() {
        return useok;
    }

    public void setUseok(int useok) {
        this.useok = useok;
    }

    public int getYhfsid() {
        return yhfsid;
    }

    public void setYhfsid(int yhfsid) {
        this.yhfsid = yhfsid;
    }

    public String getCoupontype() {
        return coupontype;
    }

    public void setCoupontype(String coupontype) {
        this.coupontype = coupontype;
    }

    public String getCouponcode() {
        return couponcode;
    }

    public void setCouponcode(String couponcode) {
        this.couponcode = couponcode;
    }

    public double getCouponprice() {
        return couponprice;
    }

    public void setCouponprice(double couponprice) {
        this.couponprice = couponprice;
    }

    public String getCoupondishcode() {
        return coupondishcode;
    }

    public void setCoupondishcode(String coupondishcode) {
        this.coupondishcode = coupondishcode;
    }

    public int getFkfs() {
        return fkfs;
    }

    public void setFkfs(int fkfs) {
        this.fkfs = fkfs;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public int getIs_usable() {
        return is_usable;
    }

    public void setIs_usable(int is_usable) {
        this.is_usable = is_usable;
    }

    public String getCardcode() {
        return cardcode;
    }

    public void setCardcode(String cardcode) {
        this.cardcode = cardcode;
    }

    public String getTemplateid() {
        return templateid;
    }

    public void setTemplateid(String templateid) {
        this.templateid = templateid;
    }

    public String getCoupondishcodes() {
        return coupondishcodes;
    }

    public void setCoupondishcodes(String coupondishcodes) {
        this.coupondishcodes = coupondishcodes;
    }

    public String getUseablemsg() {
        return useablemsg;
    }

    public void setUseablemsg(String useablemsg) {
        this.useablemsg = useablemsg;
    }

    public String getCouponname() {
        return couponname;
    }

    public void setCouponname(String couponname) {
        this.couponname = couponname;
    }

	public double getCouponsale() {
		return couponsale;
	}

	public void setCouponsale(double couponsale) {
		this.couponsale = couponsale;
	}

	public double getCoupon_totalmoney() {
		return coupon_totalmoney;
	}

	public void setCoupon_totalmoney(double coupon_totalmoney) {
		this.coupon_totalmoney = coupon_totalmoney;
	}

	public String getCoupondishcodesext() {
		return coupondishcodesext;
	}

	public void setCoupondishcodesext(String coupondishcodesext) {
		this.coupondishcodesext = coupondishcodesext;
	}

}
