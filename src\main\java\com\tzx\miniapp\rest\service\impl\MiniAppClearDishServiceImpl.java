package com.tzx.miniapp.rest.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.tzx.miniapp.common.Data;
import com.tzx.miniapp.rest.mapper.MiniAppClearDishMapper;
import com.tzx.miniapp.rest.service.IMiniAppClearDishService;
import com.tzx.miniapp.rest.vo.ClearDish;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
public class MiniAppClearDishServiceImpl implements IMiniAppClearDishService {
	private final static Logger LOGGER = LoggerFactory.getLogger(MiniAppShopStatusServiceImpl.class);

	@Autowired
	private MiniAppClearDishMapper clearDishMapper;

	/**
	 * 沽清信息缓存
	 */
	private static final Map<String, String> clearDishMap = new ConcurrentHashMap<>();
	/**
	 * 沽清信息缓存KEY
	 */
	public static final String clearDishMapKey = "clearDishInfo";

	@Transactional
	public String clearDish() {
		Data data = new Data();
		try {
			if(clearDishMap.containsKey(clearDishMapKey)) {
				LOGGER.info("沽清数据数据(cache)...");
				return clearDishMap.get(clearDishMapKey);
			}
			// 重置缓存
			initClearDishCache();
			return clearDishMap.get(clearDishMapKey);
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			data.setSuccess(0);
			data.setMsg("系统错误");
			data.setData(new HashMap<String, Object>());
			return JSONObject.toJSONString(data);
		}
	}

	@Override
	public void initClearDishCache() {
		Data data = new Data();
		List<ClearDish> cleardishlist = clearDishMapper.findClearDish();
		data.setSuccess(1);
		data.setMsg("操作成功");
		data.setData(cleardishlist);
		String clearDishData = JSONObject.toJSONString(data);
		clearDishMap.put(clearDishMapKey, clearDishData);
	}


}
