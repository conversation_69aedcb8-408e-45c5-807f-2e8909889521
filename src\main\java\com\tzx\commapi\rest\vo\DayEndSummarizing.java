package com.tzx.commapi.rest.vo;

public class DayEndSummarizing {
    private String endaccountstartdate;
    private String endaccountenddate;
    private String endaccountuniquecode;
    private String cashiername;
    private Integer ticketcount;
    private double totalcount;
    private double totalsellamount;
    private double totaloffamount;

    private double cashpay;
    private double vreditcardpay;
    private double wechatpay;
    private double alipay;
    private double vipcardpay;
    private double costbillpay;
    private double otherpay;
    private double differentamount;
    private String endaccountstate;

    public String getEndaccountstartdate() {
        return endaccountstartdate;
    }

    public void setEndaccountstartdate(String endaccountstartdate) {
        this.endaccountstartdate = endaccountstartdate;
    }

    public String getEndaccountenddate() {
        return endaccountenddate;
    }

    public void setEndaccountenddate(String endaccountenddate) {
        this.endaccountenddate = endaccountenddate;
    }

    public String getEndaccountuniquecode() {
        return endaccountuniquecode;
    }

    public void setEndaccountuniquecode(String endaccountuniquecode) {
        this.endaccountuniquecode = endaccountuniquecode;
    }

    public String getCashiername() {
        return cashiername;
    }

    public void setCashiername(String cashiername) {
        this.cashiername = cashiername;
    }

    public Integer getTicketcount() {
        return ticketcount;
    }

    public void setTicketcount(Integer ticketcount) {
        this.ticketcount = ticketcount;
    }

    public double getTotalcount() {
        return totalcount;
    }

    public void setTotalcount(double totalcount) {
        this.totalcount = totalcount;
    }

    public double getTotalsellamount() {
        return totalsellamount;
    }

    public void setTotalsellamount(double totalsellamount) {
        this.totalsellamount = totalsellamount;
    }

    public double getTotaloffamount() {
        return totaloffamount;
    }

    public void setTotaloffamount(double totaloffamount) {
        this.totaloffamount = totaloffamount;
    }

    public double getCashpay() {
        return cashpay;
    }

    public void setCashpay(double cashpay) {
        this.cashpay = cashpay;
    }

    public double getVreditcardpay() {
        return vreditcardpay;
    }

    public void setVreditcardpay(double vreditcardpay) {
        this.vreditcardpay = vreditcardpay;
    }

    public double getWechatpay() {
        return wechatpay;
    }

    public void setWechatpay(double wechatpay) {
        this.wechatpay = wechatpay;
    }

    public double getAlipay() {
        return alipay;
    }

    public void setAlipay(double alipay) {
        this.alipay = alipay;
    }

    public double getVipcardpay() {
        return vipcardpay;
    }

    public void setVipcardpay(double vipcardpay) {
        this.vipcardpay = vipcardpay;
    }

    public double getCostbillpay() {
        return costbillpay;
    }

    public void setCostbillpay(double costbillpay) {
        this.costbillpay = costbillpay;
    }

    public double getOtherpay() {
        return otherpay;
    }

    public void setOtherpay(double otherpay) {
        this.otherpay = otherpay;
    }

    public double getDifferentamount() {
        return differentamount;
    }

    public void setDifferentamount(double differentamount) {
        this.differentamount = differentamount;
    }

    public String getEndaccountstate() {
        return endaccountstate;
    }

    public void setEndaccountstate(String endaccountstate) {
        this.endaccountstate = endaccountstate;
    }
}
