package com.tzx.miniapp.rest.vo;

import java.util.List;

public class ItemVo {

	private int item_id;// 餐类明细id（item_id皆为餐类明细id，套餐也是）
	private String item_name;// 菜品/套餐名称
	private int item_count;// 数量
	private String item_price;// 价格？？（没用）
	private String amount_price;// 总价
	private String is_combo;// 是否套餐CMSX_TC,CMSX_DP
	private String item_serial;// 点餐序号
	private String item_taste;// 口味编号
	private String item_unit_name;// 单位（份，个，杯等等）（没用）
	private String taste_name;// 口味名称（没用）
	private int discount_id;// 优惠方式id
	public List<ComboDetails> comboDetails;// 套餐明细集合

	@Override
	public String toString() {
		return "ItemVo [item_id=" + item_id + ", item_name=" + item_name + ", item_count=" + item_count
				+ ", item_price=" + item_price + ", is_combo=" + is_combo + ", item_serial=" + item_serial
				+ ", item_taste=" + item_taste + ", item_unit_name=" + item_unit_name + ", taste_name=" + taste_name
				+ ", discount_id=" + discount_id + "]";
	}

	public int getItem_id() {
		return item_id;
	}

	public void setItem_id(int item_id) {
		this.item_id = item_id;
	}

	public int getItem_count() {
		return item_count;
	}

	public void setItem_count(int item_count) {
		this.item_count = item_count;
	}

	public String getItem_name() {
		return item_name;
	}

	public void setItem_name(String item_name) {
		this.item_name = item_name;
	}

	public String getItem_price() {
		return item_price;
	}

	public void setItem_price(String item_price) {
		this.item_price = item_price;
	}

	public String getIs_combo() {
		return is_combo;
	}

	public void setIs_combo(String is_combo) {
		this.is_combo = is_combo;
	}

	public String getItem_serial() {
		return item_serial;
	}

	public void setItem_serial(String item_serial) {
		this.item_serial = item_serial;
	}

	public String getItem_taste() {
		return item_taste;
	}

	public void setItem_taste(String item_taste) {
		this.item_taste = item_taste;
	}

	public String getItem_unit_name() {
		return item_unit_name;
	}

	public void setItem_unit_name(String item_unit_name) {
		this.item_unit_name = item_unit_name;
	}

	public String getTaste_name() {
		return taste_name;
	}

	public void setTaste_name(String taste_name) {
		this.taste_name = taste_name;
	}

	public int getDiscount_id() {
		return discount_id;
	}

	public void setDiscount_id(int discount_id) {
		this.discount_id = discount_id;
	}

	public List<ComboDetails> getComboDetails() {
		return comboDetails;
	}

	public void setComboDetails(List<ComboDetails> comboDetails) {
		this.comboDetails = comboDetails;
	}

	public String getAmount_price() {
		return amount_price;
	}

	public void setAmount_price(String amount_price) {
		this.amount_price = amount_price;
	}

}
