package com.tzx.miniapp.rest.service;

import com.tzx.publics.common.BillNoData;
import com.tzx.miniapp.common.Data;

import net.sf.json.JSONObject;

/**
 * 账单服务 ，老娘舅企迈定制
 * 此入口包含所有对账单的处理业务
 * 
 * <AUTHOR>
 */
public interface IMiniAppQmOrderPrecheckNew {
	/**
	 * 落单，清台， 老娘舅企迈定制
	 */
	public Data orderPrecheckCode(JSONObject json, BillNoData billNoData);
	
	/**
	 * 落单，清台数据校验， 老娘舅企迈定制
	 */
	public Data orderPrecheckBefore(JSONObject json);

	void jointDealdetails(String kdzdbh, JSONObject member, String bbrq, String skjh);
}
