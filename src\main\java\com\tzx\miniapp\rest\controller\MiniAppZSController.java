package com.tzx.miniapp.rest.controller;

import com.tzx.publics.common.BillNoData;
import com.tzx.ecoserver.common.LockerByIdent;
import com.tzx.miniapp.common.Data;
import com.tzx.miniapp.rest.service.*;
import com.tzx.publics.base.BaseController;
import com.tzx.publics.listener.InitDataListener;
import com.tzx.publics.util.DateUtil;

import com.tzx.publics.util.GlobalLockGetDataUtil;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.concurrent.locks.ReentrantLock;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 *
 * <AUTHOR>
 * @2019-06-13
 */
@RestController
@RequestMapping("/posapi2")
public class MiniAppZSController extends BaseController {
	// 定义生成日志对象
	private final static Logger LOGGER = LoggerFactory.getLogger(MiniAppZSController.class);

	@Autowired
	private IMiniAppZsSyncData zsSynchroData;
	
	@Autowired
	private IMiniAppZsOrderPrecheck zsOrderPrecheck;
	
	@Autowired
	private IMiniAppFirstPayService firstPayService;

	//通用 锁接口
    @Autowired
    private LockerByIdent ecolocker;

	@Autowired
	private GlobalLockGetDataUtil globalLockGetDataUtil;
	@Autowired
	InitDataListener initDataListener;

    private ReentrantLock billLock = new ReentrantLock();

	/**
	 * 给众赏推送菜品数据，POS调用
	 *
	 * @param request
	 * @param response
	 * @param json
	 * @return
	 */
	@RequestMapping(value = "/shop/synchrodataDish", method = RequestMethod.POST)
	public String synchrodataDish(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) {
		initDataListener.loadGlovarParamsAndOrgan();//重新加载全局数据
		response.setContentType("text/html;charset=utf-8");
		LOGGER.info("request(synchrodataDish)：" + json);
		Data data = zsSynchroData.synchrodata(JSONObject.fromObject(json));
		LOGGER.info("response(synchrodataDish)：" + JSONObject.fromObject(data).toString());
		return JSONObject.fromObject(data).toString();
	}

	/**
	 * 给众赏推送门店营业状态，POS开店，数据同步，打样时调用
	 *	0 停止营业  1 开始营业
	 * @param request
	 * @param response
	 * @param json
	 * @return
	 */
	@RequestMapping(value = "/shop/syncStoreIsOpen", method = RequestMethod.POST)
	public String syncStoreIsOpen(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) {
		response.setContentType("text/html;charset=utf-8");
		LOGGER.info("request(syncStoreIsOpen)：" + json);
		Data data = zsSynchroData.syncStoreIsOpen(JSONObject.fromObject(json));
		LOGGER.info("response(syncStoreIsOpen)：" + JSONObject.fromObject(data).toString());
		return JSONObject.fromObject(data).toString();
	}
	
	/**
	 * 众赏调用，心跳接口
	 *
	 * @param request
	 * @param response
	 * @param
	 * @return
	 */
	
	@RequestMapping(value = "/genericRest/getOrganStatus", method = RequestMethod.GET)
	public String getOrganStatus(HttpServletRequest request, HttpServletResponse response) {
		response.setContentType("text/html;charset=utf-8");
		LOGGER.info("request(getOrganStatus)：----心跳检测开始！");
		JSONObject data = new JSONObject();
		JSONObject result = new JSONObject();
		result.put("msg", "操作成功");
		result.put("code", "0000");
		result.put("status", 1);
		result.put("st", DateUtil.getNowDateYYDDMMHHMMSS());
		
		data.put("result", result);
		// 同步开店状态
		zsSynchroData.syncStoreIsOpen(result);
		LOGGER.info("response(getOrganStatus)：----心跳检测结束！");
		return JSONObject.fromObject(data).toString();
	}
	
	
	/**
	 * 众赏   落单，清台接口
	 * 众赏版本，取消了预落单，购物车确认后，直接到付款界面，付款完成调用此接口
	 * @param request
	 * @param response
	 * @param json
	 * @return
	 */
	@RequestMapping(value = "/shop/orderFirstPay", method = RequestMethod.POST)
	public String orderPrecheck(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) {
		response.setContentType("text/html;charset=utf-8");
		LOGGER.info("request(orderFirstPay)：" + json);

		String outOrderId = JSONObject.fromObject(json).optString("out_order_id");
		Boolean IsLock = false;
		Data data = new Data();
		try {
			IsLock = ecolocker.Lock(outOrderId, 1);
			LOGGER.info("outorderid： {} ecolocker.Lock：{}", outOrderId, IsLock);
			if (IsLock) {
				BillNoData billNoData = null;
				billNoData  = globalLockGetDataUtil.getBillNoData("99", "9");
				data = zsOrderPrecheck.checkClmx(JSONObject.fromObject(json));
				if (data.getSuccess() != 0) {
					data = zsOrderPrecheck.orderPrecheck(JSONObject.fromObject(json), billNoData);
					if (data.getSuccess() != 0 && data.getCode() != 2) {
						firstPayService.sendPrintDetailed(JSONObject.fromObject(json));
						firstPayService.sendPrint(JSONObject.fromObject(json), "jzd");
						firstPayService.sendKVS(JSONObject.fromObject(json));
						firstPayService.sendPrintKichen(JSONObject.fromObject(json));
					}	
				}
			} else {
				data.setSuccess(0);
				data.setMsg("账单处理中，请勿重复提交:" + outOrderId);
				data.setData(new HashMap<String, Object>());
			}
		} catch (Exception e) {
			e.printStackTrace();
			LOGGER.error("Ignore this exception", e);
			data.setSuccess(0);
			data.setMsg("系统错误:" + e);
			data.setData(new HashMap<String, Object>());
		} finally {
			if (IsLock) {
				ecolocker.UnLock(outOrderId);
				LOGGER.info("outorderid： {} ecolocker.unlock解锁成功", outOrderId);
			}
		}
		LOGGER.info("response(orderFirstPay)：" + JSONObject.fromObject(data).toString());
		return JSONObject.fromObject(data).toString();
	}
	
	/**
	 * 账单状态查询
	 * 
	 * @param request
	 * @param response
	 * @param json
	 * @return
	 */
	@RequestMapping(value = "/shop/orderStatus", method = RequestMethod.POST)
	public String orderStatus(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) {
		response.setContentType("text/html;charset=utf-8");
		LOGGER.info("request(orderStatus)：" + json);
		Data data = zsOrderPrecheck.orderStatus(JSONObject.fromObject(json));
		LOGGER.info("response(orderStatus)：" + JSONObject.fromObject(data).toString());
		return JSONObject.fromObject(data).toString();
	}
	
	/**
	 * 同步菜品通知，众赏调用
	 *
	 * @param request
	 * @param response
	 * @param json
	 * @return
	 */
	@RequestMapping(value = "/shop/synchrodataInform", method = RequestMethod.POST)
	public String synchrodataInform(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) {
		response.setContentType("text/html;charset=utf-8");
		LOGGER.info("request(synchrodataInform)：" + json);
		Data data = new Data();
		// 众赏同步
		data = zsSynchroData.synchrodata(JSONObject.fromObject(json));
		if (data.getSuccess() == 0) {
			data.setSuccess(1);
		} else {
			data.setSuccess(0);
		}
		LOGGER.info("response(synchrodataInform)：" + JSONObject.fromObject(data).toString());
		return JSONObject.fromObject(data).toString();
	}

}


