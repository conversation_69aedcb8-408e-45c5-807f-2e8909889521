package com.tzx.miniapp.rest.vo;

import java.io.Serializable;
import java.math.BigDecimal;

public class ZsDishUnitEntity implements Serializable {

	private static final long serialVersionUID = 1L;

	private String unitId;// 单位id
	private String merUnitId;// 集团单位id
	private String unitName;// 单位名称
	private Integer isDefault;// 是否默认 0 否 1 是
	private Long standardPrice;// 价格 (分)
	private Integer validState;// 是否有效 0 否 1 是
	private BigDecimal countRate;// 默认规格计数比率

	public String getUnitId() {
		return unitId;
	}

	public void setUnitId(String unitId) {
		this.unitId = unitId;
	}

	public String getMerUnitId() {
		return merUnitId;
	}

	public void setMerUnitId(String merUnitId) {
		this.merUnitId = merUnitId;
	}

	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}

	public Integer getIsDefault() {
		return isDefault;
	}

	public void setIsDefault(Integer isDefault) {
		this.isDefault = isDefault;
	}

	public Long getStandardPrice() {
		return standardPrice;
	}

	public void setStandardPrice(Long standardPrice) {
		this.standardPrice = standardPrice;
	}

	public Integer getValidState() {
		return validState;
	}

	public void setValidState(Integer validState) {
		this.validState = validState;
	}

	public BigDecimal getCountRate() {
		return countRate;
	}

	public void setCountRate(BigDecimal countRate) {
		this.countRate = countRate;
	}

}
