package com.tzx.commapi.rest.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 传递至PMS的账单项实体类。标注为否的，不是必传。
 * 先全部从文档中粘过来，根据实际情况很多字段实际用不到。
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PMSOrder {
    private Integer staffmealflag;//	是否员工餐	是	是否员工餐订单 0是 1否【默认否1】
    private String order_id;//	订单ID	是	唯一键，期望是UUID
    private String order_id_short;//	订单ID短值	否
    private String order_id_eco;//	ECO订单ID	否
    private String third_party_id;//	第三方订单ID	否
    private String dinning_table_id;//	用餐桌台ID	否
    private String order_status;//	订单状态	是	1下单待支付 2已支付 3接单 4配送中 5完成 6取消
    private String waiter_user_id;    //服务员用户编码	否
    private String order_user_id;//	点单用户编码	否
    private String cashier_user_id;//	收银员用户编码	否
    private String workstation_id;//	工作站ID（桌台区域ID）	否
    private String workstation_name;//	工作站名称（桌台区域名称）	否
    private String sub_store_id;//	子门店编号（POS机编号）	否
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone ="GMT+8")
    private Date order_time;//	用户点单时间	是
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone ="GMT+8")
    private Date checkopen_time;//	开台时间	是	POS单必填
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone ="GMT+8")
    private Date checkout_time;//	结账时间	否
    private String order_channel;//	订单来源	是	见下面注意事项
    private Integer people_num;//	订单人数	否
    private String order_type;//	订单类型	否	FOR_HERE：堂 TAKE_OUT：外卖 TO_GO：外带
    private String order_type_id;//	订单类型ID	否	见下面注意事项
    private String take_style;//	取餐方式	否	TABLE-桌号、NO-取餐号、NO_BOOK-取餐号-预约、TABLE_BOOK-预约桌号’
    private String take_no;//	取餐码	是	取餐号码，示例：桌号：04，取餐号：5100’
    private BigDecimal order_total;//	订单合计金额	否	【优惠前金额】包含茶位费、服务费、小费、超时费
    private BigDecimal order_cost;//	订单应收金额	否	【优惠后金额】
    private BigDecimal service_total;//	服务费应收	否
    private BigDecimal service_cost;//	服务费实收	否
    private BigDecimal service_rate;//	服务费折扣率	否
    private BigDecimal products_total;//	商品总应收	否
    private BigDecimal products_cost;//	商品总实收	否
    private BigDecimal rounding_total;//	订单抹零金额	否
    private BigDecimal discount_total;//	订单折扣金额	否
    private String marketing_activitie_code;//	营销活动编码	否
    private BigDecimal order_payment;//	付款金额	否
    private String payment_method;//	付款方式	否
    private BigDecimal invoice_total;//	订单发票金额	否	例如100.34
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone ="GMT+8")
    private Date invoice_time;//	订单开发票时间	否
    private String invoice_url;//	电子发票连接	否
    private String invoice_status;//	订单发票状态	否	0不开发票，1开发票，2已经开发票
    private Integer order_closed;//	订单是否关闭	否
    private Integer order_print;//	订单是否打印	否
    private Integer order_locked;//	订单是否锁定	否
    private Integer order_pending;//	是否挂单	否
    private Integer order_cancelled;//	订单是否取消	否
    private String vip_code;//	Vip客户编号	否	会员卡号
    private String order_memo;//	订单备注信息	否
    private String third_party_index;//	第三方平台账单序号	否
    private BigDecimal package_fee;//	打包盒金额	否
    private BigDecimal package_discount;//	打包盒折扣金额	否
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone ="GMT+8")
    private Date accept_date;//	外卖接单日期	是	线上单必填
    private String secret_signal;//	取餐暗号	否
    private String consignee_address;//	送餐地址	否
    private String address_detail;//	详细地址	否
    private String consignee_name;//	收餐人姓名	否
    private String first_name;//	收餐人名字	否
    private String last_name;//	收餐人姓氏	否
    private String consignee_phone;//	收餐人电话	否
    private BigDecimal delivery_fee;//	配送费	否
    private BigDecimal delivery_discount;//	配送费折扣金额	否
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone ="GMT+8")
    private Date delivery_time;//	客户预约送达时间	否
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone ="GMT+8")
    private Date order_estimation;//	商品预计制作时间	否
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone ="GMT+8")
    private Date make_finish_time;//	预计订单完成时间	否
    private Integer is_package;//	是否打包	否
    private String cancel_reason;//	订单取消原因	否
    private String store_id;//	门店编码	否
    private String store_name;//	门店名称	否
    private String store_address;//	门店地址	否
    private String pos_id;//	POS机ID	否
    private String ext_info;//	订单扩展信息	否
    private String billid;//	billid结账id	否
    private Integer checkprint;//	是否打印结账单	否	0-打（默认） 1-不打
    //	会员信息	否	json_encode编码的会员信息。字段包括：cno:卡号、name:姓名、grade:等级编号、grade_name:等级名称、balance:卡余额、credit:积分余额、receive_credit:本次奖励积分、receive_coupons:本次奖励券。
    private String member_info;
    private String table_code;

    public Integer getStaffmealflag() {
        return staffmealflag;
    }

    public void setStaffmealflag(Integer staffmealflag) {
        this.staffmealflag = staffmealflag;
    }

    public String getOrder_id() {
        return order_id;
    }

    public void setOrder_id(String order_id) {
        this.order_id = order_id;
    }

    public String getOrder_id_short() {
        return order_id_short;
    }

    public void setOrder_id_short(String order_id_short) {
        this.order_id_short = order_id_short;
    }

    public String getOrder_id_eco() {
        return order_id_eco;
    }

    public void setOrder_id_eco(String order_id_eco) {
        this.order_id_eco = order_id_eco;
    }

    public String getThird_party_id() {
        return third_party_id;
    }

    public void setThird_party_id(String third_party_id) {
        this.third_party_id = third_party_id;
    }

    public String getDinning_table_id() {
        return dinning_table_id;
    }

    public void setDinning_table_id(String dinning_table_id) {
        this.dinning_table_id = dinning_table_id;
    }

    public String getOrder_status() {
        return order_status;
    }

    public void setOrder_status(String order_status) {
        this.order_status = order_status;
    }

    public String getWaiter_user_id() {
        return waiter_user_id;
    }

    public void setWaiter_user_id(String waiter_user_id) {
        this.waiter_user_id = waiter_user_id;
    }

    public String getOrder_user_id() {
        return order_user_id;
    }

    public void setOrder_user_id(String order_user_id) {
        this.order_user_id = order_user_id;
    }

    public String getCashier_user_id() {
        return cashier_user_id;
    }

    public void setCashier_user_id(String cashier_user_id) {
        this.cashier_user_id = cashier_user_id;
    }

    public String getWorkstation_id() {
        return workstation_id;
    }

    public void setWorkstation_id(String workstation_id) {
        this.workstation_id = workstation_id;
    }

    public String getWorkstation_name() {
        return workstation_name;
    }

    public void setWorkstation_name(String workstation_name) {
        this.workstation_name = workstation_name;
    }

    public String getSub_store_id() {
        return sub_store_id;
    }

    public void setSub_store_id(String sub_store_id) {
        this.sub_store_id = sub_store_id;
    }

    public Date getOrder_time() {
        return order_time;
    }

    public void setOrder_time(Date order_time) {
        this.order_time = order_time;
    }

    public Date getCheckopen_time() {
        return checkopen_time;
    }

    public void setCheckopen_time(Date checkopen_time) {
        this.checkopen_time = checkopen_time;
    }

    public Date getCheckout_time() {
        return checkout_time;
    }

    public void setCheckout_time(Date checkout_time) {
        this.checkout_time = checkout_time;
    }

    public String getOrder_channel() {
        return order_channel;
    }

    public void setOrder_channel(String order_channel) {
        this.order_channel = order_channel;
    }

    public Integer getPeople_num() {
        return people_num;
    }

    public void setPeople_num(Integer people_num) {
        this.people_num = people_num;
    }

    public String getOrder_type() {
        return order_type;
    }

    public void setOrder_type(String order_type) {
        this.order_type = order_type;
    }

    public String getOrder_type_id() {
        return order_type_id;
    }

    public void setOrder_type_id(String order_type_id) {
        this.order_type_id = order_type_id;
    }

    public String getTake_style() {
        return take_style;
    }

    public void setTake_style(String take_style) {
        this.take_style = take_style;
    }

    public String getTake_no() {
        return take_no;
    }

    public void setTake_no(String take_no) {
        this.take_no = take_no;
    }

    public BigDecimal getOrder_total() {
        return order_total;
    }

    public void setOrder_total(BigDecimal order_total) {
        this.order_total = order_total;
    }

    public BigDecimal getOrder_cost() {
        return order_cost;
    }

    public void setOrder_cost(BigDecimal order_cost) {
        this.order_cost = order_cost;
    }

    public BigDecimal getService_total() {
        return service_total;
    }

    public void setService_total(BigDecimal service_total) {
        this.service_total = service_total;
    }

    public BigDecimal getService_cost() {
        return service_cost;
    }

    public void setService_cost(BigDecimal service_cost) {
        this.service_cost = service_cost;
    }

    public BigDecimal getService_rate() {
        return service_rate;
    }

    public void setService_rate(BigDecimal service_rate) {
        this.service_rate = service_rate;
    }

    public BigDecimal getProducts_total() {
        return products_total;
    }

    public void setProducts_total(BigDecimal products_total) {
        this.products_total = products_total;
    }

    public BigDecimal getProducts_cost() {
        return products_cost;
    }

    public void setProducts_cost(BigDecimal products_cost) {
        this.products_cost = products_cost;
    }

    public BigDecimal getRounding_total() {
        return rounding_total;
    }

    public void setRounding_total(BigDecimal rounding_total) {
        this.rounding_total = rounding_total;
    }

    public BigDecimal getDiscount_total() {
        return discount_total;
    }

    public void setDiscount_total(BigDecimal discount_total) {
        this.discount_total = discount_total;
    }

    public String getMarketing_activitie_code() {
        return marketing_activitie_code;
    }

    public void setMarketing_activitie_code(String marketing_activitie_code) {
        this.marketing_activitie_code = marketing_activitie_code;
    }

    public BigDecimal getOrder_payment() {
        return order_payment;
    }

    public void setOrder_payment(BigDecimal order_payment) {
        this.order_payment = order_payment;
    }

    public String getPayment_method() {
        return payment_method;
    }

    public void setPayment_method(String payment_method) {
        this.payment_method = payment_method;
    }

    public BigDecimal getInvoice_total() {
        return invoice_total;
    }

    public void setInvoice_total(BigDecimal invoice_total) {
        this.invoice_total = invoice_total;
    }

    public Date getInvoice_time() {
        return invoice_time;
    }

    public void setInvoice_time(Date invoice_time) {
        this.invoice_time = invoice_time;
    }

    public String getInvoice_url() {
        return invoice_url;
    }

    public void setInvoice_url(String invoice_url) {
        this.invoice_url = invoice_url;
    }

    public String getInvoice_status() {
        return invoice_status;
    }

    public void setInvoice_status(String invoice_status) {
        this.invoice_status = invoice_status;
    }

    public Integer getOrder_closed() {
        return order_closed;
    }

    public void setOrder_closed(Integer order_closed) {
        this.order_closed = order_closed;
    }

    public Integer getOrder_print() {
        return order_print;
    }

    public void setOrder_print(Integer order_print) {
        this.order_print = order_print;
    }

    public Integer getOrder_locked() {
        return order_locked;
    }

    public void setOrder_locked(Integer order_locked) {
        this.order_locked = order_locked;
    }

    public Integer getOrder_pending() {
        return order_pending;
    }

    public void setOrder_pending(Integer order_pending) {
        this.order_pending = order_pending;
    }

    public Integer getOrder_cancelled() {
        return order_cancelled;
    }

    public void setOrder_cancelled(Integer order_cancelled) {
        this.order_cancelled = order_cancelled;
    }

    public String getVip_code() {
        return vip_code;
    }

    public void setVip_code(String vip_code) {
        this.vip_code = vip_code;
    }

    public String getOrder_memo() {
        return order_memo;
    }

    public void setOrder_memo(String order_memo) {
        this.order_memo = order_memo;
    }

    public String getThird_party_index() {
        return third_party_index;
    }

    public void setThird_party_index(String third_party_index) {
        this.third_party_index = third_party_index;
    }

    public BigDecimal getPackage_fee() {
        return package_fee;
    }

    public void setPackage_fee(BigDecimal package_fee) {
        this.package_fee = package_fee;
    }

    public BigDecimal getPackage_discount() {
        return package_discount;
    }

    public void setPackage_discount(BigDecimal package_discount) {
        this.package_discount = package_discount;
    }

    public Date getAccept_date() {
        return accept_date;
    }

    public void setAccept_date(Date accept_date) {
        this.accept_date = accept_date;
    }

    public String getSecret_signal() {
        return secret_signal;
    }

    public void setSecret_signal(String secret_signal) {
        this.secret_signal = secret_signal;
    }

    public String getConsignee_address() {
        return consignee_address;
    }

    public void setConsignee_address(String consignee_address) {
        this.consignee_address = consignee_address;
    }

    public String getAddress_detail() {
        return address_detail;
    }

    public void setAddress_detail(String address_detail) {
        this.address_detail = address_detail;
    }

    public String getConsignee_name() {
        return consignee_name;
    }

    public void setConsignee_name(String consignee_name) {
        this.consignee_name = consignee_name;
    }

    public String getFirst_name() {
        return first_name;
    }

    public void setFirst_name(String first_name) {
        this.first_name = first_name;
    }

    public String getLast_name() {
        return last_name;
    }

    public void setLast_name(String last_name) {
        this.last_name = last_name;
    }

    public String getConsignee_phone() {
        return consignee_phone;
    }

    public void setConsignee_phone(String consignee_phone) {
        this.consignee_phone = consignee_phone;
    }

    public BigDecimal getDelivery_fee() {
        return delivery_fee;
    }

    public void setDelivery_fee(BigDecimal delivery_fee) {
        this.delivery_fee = delivery_fee;
    }

    public BigDecimal getDelivery_discount() {
        return delivery_discount;
    }

    public void setDelivery_discount(BigDecimal delivery_discount) {
        this.delivery_discount = delivery_discount;
    }

    public Date getDelivery_time() {
        return delivery_time;
    }

    public void setDelivery_time(Date delivery_time) {
        this.delivery_time = delivery_time;
    }

    public Date getOrder_estimation() {
        return order_estimation;
    }

    public void setOrder_estimation(Date order_estimation) {
        this.order_estimation = order_estimation;
    }

    public Date getMake_finish_time() {
        return make_finish_time;
    }

    public void setMake_finish_time(Date make_finish_time) {
        this.make_finish_time = make_finish_time;
    }

    public Integer getIs_package() {
        return is_package;
    }

    public void setIs_package(Integer is_package) {
        this.is_package = is_package;
    }

    public String getCancel_reason() {
        return cancel_reason;
    }

    public void setCancel_reason(String cancel_reason) {
        this.cancel_reason = cancel_reason;
    }

    public String getStore_id() {
        return store_id;
    }

    public void setStore_id(String store_id) {
        this.store_id = store_id;
    }

    public String getStore_name() {
        return store_name;
    }

    public void setStore_name(String store_name) {
        this.store_name = store_name;
    }

    public String getStore_address() {
        return store_address;
    }

    public void setStore_address(String store_address) {
        this.store_address = store_address;
    }

    public String getPos_id() {
        return pos_id;
    }

    public void setPos_id(String pos_id) {
        this.pos_id = pos_id;
    }

    public String getExt_info() {
        return ext_info;
    }

    public void setExt_info(String ext_info) {
        this.ext_info = ext_info;
    }

    public String getBillid() {
        return billid;
    }

    public void setBillid(String billid) {
        this.billid = billid;
    }

    public Integer getCheckprint() {
        return checkprint;
    }

    public void setCheckprint(Integer checkprint) {
        this.checkprint = checkprint;
    }

    public String getMember_info() {
        return member_info;
    }

    public void setMember_info(String member_info) {
        this.member_info = member_info;
    }

    public String getTable_code() {
        return table_code;
    }

    public void setTable_code(String table_code) {
        this.table_code = table_code;
    }
}
