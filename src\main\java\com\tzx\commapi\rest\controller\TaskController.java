package com.tzx.commapi.rest.controller;

import com.tzx.commapi.rest.service.ITaskApiService;
import com.tzx.publics.mapper.InitDataMapper;
import com.tzx.publics.vo.GgcskVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/task")
public class TaskController {

    @Autowired
    private InitDataMapper initDataMapper;

    @Autowired
    private ITaskApiService taskApiService;

    @GetMapping("/getConfig")
    public Map<String, String> getConfig() {
        Map<String, String> result = new HashMap<>();
        
        // 获取报表日期
        String bbrq = System.getProperty("WYStartBbrq");
        result.put("bbrq", bbrq);

        // 获取更新时间
        GgcskVo ggcss = initDataMapper.getGgcsBySdbt("HUARUNWUYESTARDATE");
        String lastUpdatetime = ggcss.getSdnr();
        result.put("lastUpdatetime", lastUpdatetime);

        return result;
    }

    @PostMapping("/updateBbrq")
    public Map<String, Object> updateBbrq(@RequestParam String bbrq) {
        Map<String, Object> result = new HashMap<>();
        try {
            System.setProperty("WYStartBbrq", bbrq);
            result.put("success", true);
            result.put("message", "报表日期更新成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "报表日期更新失败：" + e.getMessage());
        }
        return result;
    }

    @PostMapping("/updateLastUpdatetime")
    public Map<String, Object> updateLastUpdatetime(@RequestParam String lastUpdatetime) {
        Map<String, Object> result = new HashMap<>();
        try {
            initDataMapper.updateGgcsBySdnr("HUARUNWUYESTARDATE", lastUpdatetime);
            result.put("success", true);
            result.put("message", "更新时间修改成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "更新时间修改失败：" + e.getMessage());
        }
        return result;
    }

    @PostMapping("/start")
    public Map<String, Object> start() {
        Map<String, Object> result = new HashMap<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        int executionCount = 0;
        
        try {
            while (true) {
                if (executionCount > 500) {
                    break;
                }
                // 获取当前时间减去1小时
                Date oneHourAgo = new Date(System.currentTimeMillis() - 3600 * 1000);
                
                // 获取当前更新时间
                GgcskVo ggcss = initDataMapper.getGgcsBySdbt("HUARUNWUYESTARDATE");
                String lastUpdatetime = ggcss.getSdnr();
                Date lastUpdateDate = sdf.parse(lastUpdatetime);

                // 如果最后更新时间大于一小时前，退出循环
                if (lastUpdateDate.compareTo(oneHourAgo) > 0) {
                    result.put("success", true);
                    result.put("message", String.format("任务执行完成，共执行%d次，最后更新时间：%s", 
                        executionCount, lastUpdatetime));
                    break;
                }

                // 执行任务
                taskApiService.bohDataEstateTasks();
                executionCount++;

                // 为了避免过于频繁的执行，添加短暂延时
                Thread.sleep(1000);
            }
        } catch (ParseException e) {
            result.put("success", false);
            result.put("message", "日期解析失败：" + e.getMessage());
        } catch (InterruptedException e) {
            result.put("success", false);
            result.put("message", "任务执行被中断：" + e.getMessage());
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "任务执行失败：" + e.getMessage());
        }
        return result;
    }
}
