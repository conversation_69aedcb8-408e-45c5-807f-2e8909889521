<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <!-- 继承Spring Boot 的配置-->
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>1.5.6.RELEASE</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <!--包架构和描述-->
    <groupId>com.tzx</groupId>
    <artifactId>tzxserver</artifactId>
    <version>3.0</version>
    <name>tzxserver</name>
    <description>天子星门店服务程序</description>

    <properties>
        <activemq.version>5.8.0</activemq.version>
        <!-- Spring boot 版本号-->
        <spring.boot.version>1.5.6.RELEASE</spring.boot.version>
        <!-- 编码-->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <!-- JDK版本 -->
        <java.version>1.7</java.version>
        <!-- 一些依赖 的版本号 -->
        <httpclient.version>4.3.5</httpclient.version>
        <jsp-api.version>2.0</jsp-api.version>
        <joda-time.version>2.5</joda-time.version>
        <commons-lang3.version>3.3.2</commons-lang3.version>
        <commons-io.version>1.3.2</commons-io.version>
        <mybatisplus-spring-boot-starter.version>1.0.2</mybatisplus-spring-boot-starter.version>
        <commons-lang3.version>3.1</commons-lang3.version>
        <commons-io.version>2.4</commons-io.version>
        <commons-codec.version>1.6</commons-codec.version>
        <commons-fileupload.version>1.3</commons-fileupload.version>
        <commons-collections.version>3.2.1</commons-collections.version>
        <commons-beanutils.version>1.8.3</commons-beanutils.version>
        <commons-httpclient.version>3.1</commons-httpclient.version>
        <mybatis-generator.version>1.3.5</mybatis-generator.version>
    </properties>

    <packaging>jar</packaging>
    <repositories>
    	<repository>
		<id>alimaven</id>
		<url>https://maven.aliyun.com/nexus/content/groups/public/</url>
    	</repository>

		<repository>
		<id>mvnrepository</id>
		<url>https://mvnrepository.com/</url>
		</repository>
    </repositories>


    <dependencies>
        <!-- 父项目springboot 框架继承过来的依赖 -->
        <!--  Spring boot  整合mybatis 环境-->
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>1.2.0</version>
        </dependency>
        <!--  Spring boot web 环境-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <!-- 移除 原有的log,使用log4j-->
            <!--<exclusions>-->
            <!--<exclusion>-->
            <!--<groupId>org.springframework.boot</groupId>-->
            <!--<artifactId>spring-boot-starter-logging</artifactId>-->
            <!--</exclusion>-->
            <!--</exclusions>-->
        </dependency>
        <!--  Spring boot activemq-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-activemq</artifactId>
        </dependency>
        <!--  单元测试环境-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>


        <!-- 切面变成只吃   -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>


        <!-- 引用这依赖，然后在配置文件配置好数据库连接参数后，能够自动注入jdbcTemplate Bean-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>

        <!--  数据库驱动-->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>9.2-1002-jdbc4</version>
        </dependency>

        <!-- 编码解码环境-->
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
        </dependency>
        <!-- 集合工具扩展类 -->
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
        </dependency>
        <!--httpclient 相关的依赖-->
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.7</version>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
            <version>4.5.7</version>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore</artifactId>
        </dependency>

        <!-- javabean 的 扩展工具类 -->
        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <version>3.1.0</version>
            <scope>provided</scope>
        </dependency>

        <!--<dependency>-->
        <!--<groupId>org.apache.tomcat.embed</groupId>-->
        <!--<artifactId>tomcat-embed-jasper</artifactId>-->
        <!--</dependency>-->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-cache</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.8.2</version>
        </dependency>
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
        </dependency>
        <!-- 集成ehcache需要的依赖 -->
        <dependency>
            <groupId>net.sf.ehcache</groupId>
            <artifactId>ehcache</artifactId>
        </dependency>
        <!--<dependency>-->
            <!--<groupId>org.springframework.boot</groupId>-->
            <!--<artifactId>spring-boot-starter-thymeleaf</artifactId>-->
        <!--</dependency>-->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <optional>true</optional>
            <scope>runtime</scope>
        </dependency>

        <!-- spring boot 之外的 第三方依赖 -->
        <dependency>
            <groupId>org.mybatis.generator</groupId>
            <artifactId>mybatis-generator-core</artifactId>
            <version>${mybatis-generator.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
            <version>1.1.0</version>
        </dependency>
        <!--mapper -->
        <dependency>
            <groupId>tk.mybatis</groupId>
            <artifactId>mapper-spring-boot-starter</artifactId>
            <version>1.1.0</version>
        </dependency>
        <!-- java lang 包的扩展类 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>${commons-lang3.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-compress</artifactId>
            <version>1.12</version>
        </dependency>
        <!-- io操作的扩展类 -->
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>${commons-io.version}</version>
        </dependency>
        <!-- 文件上传下载的扩展类 -->
        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
            <version>${commons-fileupload.version}</version>
        </dependency>
        <!-- velocity模板引擎 -->
        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity</artifactId>
            <version>1.7</version>
        </dependency>
        <dependency>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-resources-plugin</artifactId>
            <version>2.5</version>
        </dependency>
        <dependency>
            <groupId>net.sf.json-lib</groupId>
            <artifactId>json-lib</artifactId>
            <version>2.4</version>
            <classifier>jdk15</classifier>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>18.0</version>
        </dependency>
        <dependency>
            <groupId>org.jdom</groupId>
            <artifactId>jdom</artifactId>
            <version>2.0.2</version>
        </dependency>
        <dependency>
            <groupId>net.java.dev.jna</groupId>
            <artifactId>jna</artifactId>
            <version>4.4.0</version>
        </dependency>

        <!-- http client 类-->
        <dependency>
            <groupId>commons-httpclient</groupId>
            <artifactId>commons-httpclient</artifactId>
            <version>${commons-httpclient.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient-cache</artifactId>
            <version>4.2.5</version>
        </dependency>

        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
            <version>3.3</version>
        </dependency>
        <!-- 分布式事务jar ???  在这个工程里是否用到？？？ -->
        <!--<dependency>-->
            <!--<groupId>org.ow2.jotm</groupId>-->
            <!--<artifactId>jotm-core</artifactId>-->
            <!--<version>2.2.2</version>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>org.ow2.jotm</groupId>-->
            <!--<artifactId>jotm</artifactId>-->
            <!--<version>2.2.2</version>-->
            <!--<type>pom</type>-->
        <!--</dependency>-->
        <!-- 用于处理多数据源事务-->
        <!--<dependency>-->
            <!--<groupId>org.springframework.boot</groupId>-->
            <!--<artifactId>spring-boot-starter-jta-atomikos</artifactId>-->
        <!--</dependency>-->

        <!-- xml 操作类-->

        <!-- https://mvnrepository.com/artifact/org.dom4j/com.springsource.org.dom4j -->
        <dependency>
            <groupId>org.dom4j</groupId>
            <artifactId>com.springsource.org.dom4j</artifactId>
            <version>1.6.1</version>
        </dependency>


        <!-- 阿里巴巴数据库连接池 -->
        <!--<dependency>-->
        <!--<groupId>com.alibaba</groupId>-->
        <!--<artifactId>druid</artifactId>-->
        <!--<version>1.0.1</version>-->
        <!--</dependency>-->
        <!-- 优化为使用整合到Spring Boot 的阿里巴巴连接池依赖，这样可以不用再去
           通过 @configuration去@bean注入了-->
		   
		<dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
            <version>1.8.0</version>
		</dependency>   
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>1.1.0</version>
        </dependency>
        <!-- 阿里巴巴JSON -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>

		<dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-aspects</artifactId>
      <version>4.3.9.RELEASE</version>
</dependency>

		
		

        <!--<dependency>-->
        <!--<groupId>org.mybatis</groupId>-->
        <!--<artifactId>mybatis</artifactId>-->
        <!--<version>3.2.3</version>-->
        <!--</dependency>-->

        <!--<dependency>-->
        <!--<groupId>org.mybatis</groupId>-->
        <!--<artifactId>mybatis-spring</artifactId>-->
        <!--<version>1.2.2</version>-->
        <!--</dependency>-->


        <!-- jackson  -->
        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-jaxb-annotations</artifactId>
            <version>2.8.9</version>
        </dependency>

        <!-- JavaBean映射工具库 -->
        <dependency>
            <groupId>net.sf.dozer</groupId>
            <artifactId>dozer</artifactId>
            <version>5.4.0</version>
        </dependency>

        <dependency>
            <groupId>com.mashape.unirest</groupId>
            <artifactId>unirest-java</artifactId>
            <version>1.4.9</version>
        </dependency>

        <dependency>
            <groupId>org.codehaus.xfire</groupId>
            <artifactId>xfire-all</artifactId>
            <version>1.2.6</version>
            <exclusions>
                <exclusion>
                    <artifactId>xfire-spring</artifactId>
                    <groupId>org.codehaus.xfire</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>xbean</artifactId>
                    <groupId>xmlbeans</groupId>
                </exclusion>
            </exclusions>
        </dependency>

 <!--       <dependency>
            <groupId>com.huaweicloud</groupId>
            <artifactId>esdk-obs-java-bundle</artifactId>
            <version>3.23.3</version>
        </dependency>-->



        <!-- 一个jdbc代理类，通过它可以实现记录SQL日志 -->
        <!--<dependency>-->
        <!--<groupId>org.lazyluke</groupId>-->
        <!--<artifactId>log4jdbc-remix</artifactId>-->
        <!--<version>0.2.7</version>-->
        <!--</dependency>-->


        <!-- 日志相关-->
        <!--<dependency>-->
        <!--<groupId>org.slf4j</groupId>-->
        <!--<artifactId>slf4j-api</artifactId>-->
        <!--<version>1.7.21</version>-->
        <!--</dependency>-->

        <!--<dependency>-->
        <!--<groupId>org.slf4j</groupId>-->
        <!--<artifactId>slf4j-log4j12</artifactId>-->
        <!--<version>1.7.21</version>-->
        <!--</dependency>-->
        <!--<dependency>-->
        <!--<groupId>log4j</groupId>-->
        <!--<artifactId>log4j</artifactId>-->
        <!--<version>1.2.17</version>-->
        <!--</dependency>-->


    </dependencies>

    <build>
        <finalName>tzxserver3.0</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
					
					<fork>true</fork>
					

                </configuration>
                <version>3.1</version>
            </plugin>
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>1.3.2</version>

                <configuration>
                    <!--配置文件的路径 -->
                    <configurationFile>${basedir}/src/main/resources/generatorConfig.xml</configurationFile>
                    <overwrite>true</overwrite>
                </configuration>
            </plugin>

            <!-- 完整打包模式 start -->
            <!--          <plugin>
                          <groupId>org.springframework.boot</groupId>
                          <artifactId>spring-boot-maven-plugin</artifactId>
                          <configuration>
                              <fork>true</fork>
                          </configuration>
                      </plugin>-->
            <!-- 完整打包模式 end -->
            <!-- 分离lib打包模式 start -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <configuration>
                    <appendAssemblyId>false</appendAssemblyId>
                    <descriptors>
                        <descriptor>src/main/resources/assembly.xml</descriptor>
                    </descriptors>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifest>
                            <addClasspath>true</addClasspath>
                            <classpathPrefix>../../tzxLib/</classpathPrefix>
                            <mainClass>com.tzx.TzxApplication</mainClass>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-lib</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/lib</outputDirectory>
                            <overWriteReleases>false</overWriteReleases>
                            <overWriteSnapshots>false</overWriteSnapshots>
                            <overWriteIfNewer>true</overWriteIfNewer>
                            <includeScope>compile</includeScope>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!-- 分离lib打包模式end -->
        </plugins>
        <!-- 用maven管理需要设置一下导出源码目录下的配置文件，因为有一个文件配置到源码目录下了 -->
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <filtering>false</filtering>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                    <include>**/*.tld</include>
                </includes>
            </resource>
        </resources>


    </build>

</project>
