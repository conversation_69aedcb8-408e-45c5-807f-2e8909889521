package com.tzx.publics.util;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Created by <PERSON>x<PERSON> on 2020-01-15.
 *  * 用于根据list数组 通用标识锁
 */
public class LockerByList {
    private Map<String, Integer> identList = new ConcurrentHashMap<String, Integer>();

    public  boolean IsLock(ArrayList<String> arrayList){
        boolean tempBool = false;
        for (String item:arrayList) {
            tempBool =  identList.containsKey(item);
            if(tempBool){
                break;
            }
        }
        return  tempBool;
    }
    public Boolean Lock(ArrayList<String> arrayList, Integer status){
        if(IsLock(arrayList)){
            return  false;
        };
        boolean hasGetLock = false;
        synchronized (this){
            if(IsLock(arrayList)){
                return  false;
            };
            for (String item:arrayList) {
                identList.put(item, status);
            }
            hasGetLock = true;
        }
        if(!hasGetLock){
            return  false;
        }
        return IsLock(arrayList);
    }
    public Boolean UnLock(ArrayList<String> arrayList){
        if(!IsLock(arrayList)){
            return  true;
        };
        boolean hasGetLock = false;
        synchronized (this){
            if(!IsLock(arrayList)){
                return  true;
            };
            for (String item:arrayList) {
                identList.remove(item);
            }
            hasGetLock = true;
        }
        return !IsLock(arrayList);
    }

    public static void main(String[] args){
        Map<String, Integer> id = new ConcurrentHashMap<String, Integer>();
        id.put("123",1);
        id.put("123",1);
        System.out.println(id.toString());

        LockerByList lockerByList = new LockerByList();
        ArrayList<String> arrayList = new ArrayList<String>();
        arrayList.add("a");
        arrayList.add("b");
        System.out.println(StringUtils.join(arrayList,","));

        ArrayList<String> arrayList2 = new ArrayList<String>();
        arrayList2.add("c");
        arrayList2.add("d");

        ArrayList<String> arrayList3 = new ArrayList<String>();
        arrayList3.add("a");
        arrayList3.add("e");

        Boolean isLock = lockerByList.Lock(arrayList,1);
        System.out.println("arr1" + isLock);

        isLock = lockerByList.Lock(arrayList2,1);
        System.out.println("arr2" + isLock);

        isLock = lockerByList.Lock(arrayList3,1);
        System.out.println("arr3" + isLock);

        lockerByList.UnLock(arrayList);
        isLock = lockerByList.IsLock(arrayList);
        System.out.println("arr1" + isLock);
        isLock = lockerByList.IsLock(arrayList2);
        System.out.println("arr2" + isLock);
        isLock = lockerByList.IsLock(arrayList3);
        System.out.println("arr3" + isLock);



    }

}
