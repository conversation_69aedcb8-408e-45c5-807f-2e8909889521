package com.tzx.publics.util;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import net.sf.json.JSONObject;
import net.sf.json.JsonConfig;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON> on 2020-12-05.
 */
public class JsonUtils {
    public static JsonConfig gxhkJsonConfig	= new JsonConfig();
    public static List<JSONObject> mapToJsonList(List<Map<String, Object>> listMap )
    {
        List<JSONObject> list = new ArrayList<JSONObject>();

        try {
            for (Map<String, Object> map : listMap) {
                JSONObject json = JSONObject.fromObject(map, JsonUtils.gxhkJsonConfig);
                list.add(json);
            }
        }catch(Exception e){
           e.printStackTrace();
         }
        return list;
    }
    private static Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
    public static String ObjToJson(Object object){

        return gson.toJson(object);
    }
    public static <T> T jsonToObj(String str,Class<T> t){
        return gson.fromJson(str,t);
    }
}
