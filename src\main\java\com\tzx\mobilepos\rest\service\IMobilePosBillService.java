package com.tzx.mobilepos.rest.service;

import com.tzx.mobilepos.common.Data;
import com.tzx.publics.common.BillNoData;

public interface IMobilePosBillService {
	/**
	 * 创建账单
	 * 
	 * @param param
	 * @param result
	 */
	public void createBill(Data param, Data result, BillNoData billNoData);

	/**
	 * 下单
	 * 
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void ordering(Data param, Data result) throws Exception;


	/**
	 * 优惠校验
	 * 
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void checkDiscount(Data param, Data result) throws Exception;

	/**
	 * 账单优惠
	 * 
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void discountOrder(Data param, Data result) throws Exception;

	/**
	 * 结账
	 * 
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void accountsOrder(Data param, Data result) throws Exception;

	/**
	 * 支付
	 * 
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void payment(Data param, Data result) throws Exception;
	
//	public void payment1(Data param, Data result) throws Exception;

	/**
	 * 
	 * @Description: 查询账单
	 * @param @param data
	 * @param @param result
	 * @return void
	 * @throws
	 * <AUTHOR>
	 * @email  <EMAIL>
	 * @date 2018-10-11
	 */
	public void queryOrder(Data data, Data result) throws Exception;

	/**
	 * 
	 * @Description: 退单
	 * @param @param data
	 * @param @param result
	 * @param @throws Exception
   	 * @return void
	 * @throws
	 * <AUTHOR>
	 * @email  <EMAIL>
	 * @date 2018-10-11
	 */
	public void refundOrder(Data data, Data result) throws Exception;

	
	/**
	* @Description: 查询账单详情
	* @param @param data
	* @param @param result
	* @param @throws Exception
	* @return void
	* @throws
	* <AUTHOR>
	* @email  <EMAIL>
	* @date 2018-10-15
	*/
	public void queryBillDetail(Data data, Data result) throws Exception;

	/**
	* @Description: 查询优惠活动
	* @param @param data
	* @param @param result
	* @param @throws Exception
	* @return void
	* @throws
	* <AUTHOR>
	* @email  <EMAIL>
	* @date 2018-10-17
	*/
	public void queryDiscount(Data data, Data result) throws Exception;
	
	/**
	* @Description: 打印，目前只有打印结帐单
	* @param @param data
	* @param @param result
	* @param @throws Exception
	* @return void
	* @throws
	* <AUTHOR>
	* @email  <EMAIL>
	* @date 2018-11-06
	*/
	public void posPrintJna(Data data, Data result) throws Exception;

	/**
	 * 校验交现金，班结信息
	 * 
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void checkLimit(Data param, Data result) throws Exception;
	
	/**
	 * 添加口味备注
	 * 
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void addTaste(Data param, Data result) throws Exception;

	/**
	 * 查询异常账单
	 *
	 * @param data
	 * @param result
	 * @throws Exception
	 */
	public void queryExceptionBill(Data data, Data result) throws Exception;

	/**
	 * 异常账单处理
	 *
	 * @param data
	 * @param result
	 * @throws Exception
	 */
	public void exceptionBillHandler(Data data, Data result) throws Exception;

	/**
	 * 
	 * @param number
	 * @param operType
	 * @return
	 * @throws Exception
	 */
	public Data billSynchronized(String number, int operType) throws Exception;


}
