<?xml version="1.0" encoding="UTF-8"?>
<ehcache xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:noNamespaceSchemaLocation="ehcache.xsd">
	<diskStore path="java.io.tmpdir/Tmp_EhCache" />
	
	<defaultCache eternal="false" maxElementsInMemory="1000"
		overflowToDisk="false" diskPersistent="false" timeToIdleSeconds="0"
		timeToLiveSeconds="3600" memoryStoreEvictionPolicy="LRU" />
		
	<cache name="httpCache" maxEntriesLocalHeap="200"	timeToLiveSeconds="120">
	</cache>
</ehcache>  