package com.tzx.ecoserver.common;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019-11-08.
 */
public class EcoONQueueManage {

    // 定义消费者线程池
    private static volatile ThreadPoolExecutor memberUpThreadPool = null;

    /**
     * @Description 获取线程池对象（通过双重检查锁实现）。
     * @param
     * @return ThreadPoolExecutor 线程池对象
     * @see
     */
    public static ThreadPoolExecutor getMemberUpThreadPool() {
        if (memberUpThreadPool == null) {
            synchronized (ThreadPoolExecutor.class) {
                if (memberUpThreadPool == null) {
                    //核心线程数1，最大线程数1，使用LinkedBlockingQueue阻塞队列，队列深度1000
                    //线程数超过队列大小时的策略为重试。
                    memberUpThreadPool = new ThreadPoolExecutor(1, 1, 3, TimeUnit.MINUTES, new LinkedBlockingQueue<Runnable>(1000), new ThreadPoolExecutor.CallerRunsPolicy());
                }
            }
        }
        return memberUpThreadPool;
    }

    public static void runEcoQueue(HashMap<String, String> data,PlayNewOrderThread playNewOrderThread) {
        try {
            getMemberUpThreadPool().execute(new RunONProcessor(data,playNewOrderThread));
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}
