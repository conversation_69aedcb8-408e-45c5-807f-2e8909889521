package com.tzx;

import com.tzx.receiver.common.msg.MessageHandler;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;

/**
 * ObsClientServiceTest
 *
 * <AUTHOR>
 * @version 0.0.1
 * @since 2023-06-21
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class ObsClientServiceTest {


    @Test
    public void test() {

        String data = "<DATAPACKET>\n" +
                "        <MSG>\n" +
                "                <SUBSYSTEM>BASIC</SUBSYSTEM>\n" +
                "                <DATATYPE>FULL</DATATYPE>\n" +
                "                <ZLBH>3</ZLBH>\n" +
                "                <JGXH>27</JGXH>\n" +
                "                <VERSIONSTIME>1668833755866</VERSIONSTIME>\n" +
                "                <CSHXFBZ>TZX</CSHXFBZ>\n" +
                "                <SERVERDATA>VST_ORGSORTS_DL</SERVERDATA>\n" +
                "                <CHILDDATA>VST_ORGSORTS_DL</CHILDDATA>\n" +
                "                <PRIMARY>ID</PRIMARY>\n" +
                "                <DOWNMSGURL>http://*************:9081/TzxCommunicate/IssueLogUpdate</DOWNMSGURL>\n" +
                "                <DELIVERYMETHOD>4</DELIVERYMETHOD>\n" +
                "                <DOWNLOADURL>https://test-162a.obs.cn-north-4.myhuaweicloud.com/config/202211191255-ad399462fc844ebd9ab49c3542800069.zip</DOWNLOADURL>\n" +
                "        </MSG>\n" +
                "</DATAPACKET>";

        MessageHandler handler = new MessageHandler(data);


        try {
            Thread thread = new Thread(handler);
            thread.start();
            thread.join();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}
