package com.tzx.receiver.common.upload;

import com.tzx.publics.listener.InitDataListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**doUploadTimerDispatcher
 * <AUTHOR>
 * @Date 2019-04-20
 * @Descption  上传相关定时任务，用于扫描未打烊上传营业日期
 *               未成功上传至总部MQ文件，到时间点的定时上传
 *               ，上传队列等内容
 **/
@Component
@DependsOn("newTzxMsgListener")
@EnableAsync  //多任务并发执行的注解
public class UploadSchedule {
    /***
     *以下定时任务之间关系为
     * 1、首先定义一个上传任务UpLoadTaskList，用于保存需要上传的任务列表
     * 2、scanUploadTaskSchedule负责扫描UpLoadTaskList，
     *   取到任务后做统计、生成XML等操作，然后将XML发送至总部MQ
     * 3、scanKCrealTime扫描定时上传的任务列表，到了规定时间点后，将任务插入至UpLoadTaskList
     * 4、scanFailDayendSchedule 负责扫描未成功做打烊上传的报表日期，然后生成DAYEND任务插入值UpLoadTaskList
     * 5、scanFailSendMQSchedule  扫描定时上传任务中已经成功生成XML，但是为上传至总部的文件,然后将此文件关联任务
     *    插入到upLoadTaskList,此时应该给该任务做标识。上传处理器对该任务只做重新上传MQ的操作
     * 6、另外，在UDPServre和Controller中接收到POS的上传通知后，也会生成相应的任务插入值 UpLoadTaskList中
     ***/

    //控制用于防止多个任务并发执行的变量
    private boolean isDoingTaskSchedule = false;
    private boolean isDoingKCrealtimeSchedule = false;
    private boolean isDoingFailDayendSchedule = false;
    private boolean isDoingFailFailMQSchedule = false;
    private boolean isDoingOverUpdateToServSchedule = false;
    protected Logger logger	= LoggerFactory.getLogger(getClass());
    //处理真实上传任务的控制器
    @Autowired
    private UploadTaskDispacher uploadTaskDispatcher;
    @Autowired
    private UploadKCrealTimeDispatcher uploadKCrealTimeDispatcher;
    @Autowired
    private  FailDayendDispatcher failDayendDispatcher;
    @Autowired
    private FailMQDispatcher failMQDispatcher;
    @Autowired
    private UploadOverUpdateDispatcher uploadOverUpdateDispatcher;
    @Autowired
    private UploadTaskTableDispatcher uploadTaskTableDispatcher;
    private boolean isDoingScanTaskTable = false;
    //扫描任务表，找到任务后，将任务表中的内容，转换为上传任务uploadtaskList
    //注意，无特殊请求请问更改下面两个时间。因为上传至PMS命令也在次定时任务重，服务启动时优先加载
    //上次意外退出时未完成的任务，延迟30秒目的是让这种加载可以充分完成。时间间隔2秒是为了保证PMS的时效性。
    //TODO 此定时任务可能会引发数据新能问题，后续需要优化
    @Scheduled(initialDelay = 30*1000,fixedRate = 2*1000)
    @Async
    public void scanTaskTable(){
        if(isDoingScanTaskTable){
            return;
        }
        isDoingScanTaskTable = true;
        try{
            uploadTaskTableDispatcher.doScanTaskTable();
        }finally {
            isDoingScanTaskTable = false;
        }

    }
    //启动后延迟2分钟，以后每15秒执行一次
    @Scheduled(initialDelay=2*60*1000,fixedRate = 60*60*1000)
    @Async
    public void scanFailDayendSchedule(){
        if(isDoingFailDayendSchedule){
            return;
        }
        isDoingFailDayendSchedule = true;
        try{
            failDayendDispatcher.doFailDayend();
        }finally {
            isDoingFailDayendSchedule = false;
        }
    }
    //启动后延迟5分钟，以后每30秒执行一次
    @Scheduled(initialDelay=5*60*1000,fixedRate = 30*1000)
    @Async
    public void scanFailSendMQSchedule(){
        if(isDoingFailFailMQSchedule){
            return;
        }
        isDoingFailFailMQSchedule = true;
        try{
            failMQDispatcher.doFailMQ();
        }finally {
            isDoingFailFailMQSchedule = false;
        }
    }
    //启动后延迟30秒钟，以后每2秒执行一次
    @Scheduled(initialDelay=30*1000,fixedRate = 2*1000)
    @Async
    public void scanKCrealTimeSchedule(){
        if(isDoingKCrealtimeSchedule){
            return;
        }
        isDoingKCrealtimeSchedule = true;
        try{
            uploadKCrealTimeDispatcher.doUploadTimerDispatcher();
        }finally {
            isDoingKCrealtimeSchedule = false;
        }

    }
    @Scheduled(initialDelay=10*1000,fixedRate = 1000) //启动后延迟10秒，以后每一秒执行一次
    @Async
    public void scanUploadTaskSchedule(){
        if(isDoingTaskSchedule){
            return;
        }
        isDoingTaskSchedule = true;
        try{
            uploadTaskDispatcher.doUploadDispatcher();
        }finally {
            isDoingTaskSchedule = false;
        }


    }

    //启动后延迟30秒钟，以后每30秒执行一次
    @Scheduled(initialDelay=30*1000,fixedRate = 30*1000)
    @Async
    public void scanOverUpdateToServSchedule(){
        if(isDoingOverUpdateToServSchedule){
            return;
        }
        isDoingOverUpdateToServSchedule = true;
        try{
            String POS_UPDATEMENU_UPLOAD = InitDataListener.ggcsMap.get("POS_UPDATEMENU_UPLOAD");
            if(null!=POS_UPDATEMENU_UPLOAD && POS_UPDATEMENU_UPLOAD.equals("1")){
                uploadOverUpdateDispatcher.excute();
            }

        }finally {
            isDoingOverUpdateToServSchedule = false;
        }
    }

}
