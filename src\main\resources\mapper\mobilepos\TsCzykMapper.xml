<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.mobilepos.rest.mapper.MobilePosTsCzykMapper">
	<select id="findLogin" resultType="com.tzx.mobilepos.rest.model.TsCzyk">
		select zb.zbbh as group_id,  czy.id as czy_id, czy.* from ts_czyk czy left join ts_czyzbk zb on czy.czyzbid = zb.id 
		where czy.czybh = #{czybh} and czy.czymm = #{czymm} and czy.czyzt &lt;&gt; 'DISABLE'
	</select>
	<select id="findByCzybh" resultType="com.tzx.mobilepos.rest.model.TsCzyk">
		select * from ts_czyk where czybh = #{czybh}
	</select>
	
	<update id="updatePassword">
		update ts_czyk set czymm = #{xczymm} where czybh = #{czybh}
	</update>
	<update id="updatePasswordXf">
		update ts_czyk_xf set czymm = #{xczymm} where czybh = #{czybh}
	</update>
</mapper>
