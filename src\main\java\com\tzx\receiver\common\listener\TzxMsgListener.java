package com.tzx.receiver.common.listener;

import com.tzx.receiver.common.msg.MessageDispatcher;
import com.tzx.receiver.common.upload.UDPMSGServer;
import com.tzx.receiver.common.upload.UDPServer;
import com.tzx.receiver.common.upload.UpLoadTaskList;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import javax.servlet.ServletContext;
import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;

public class TzxMsgListener implements ServletContextListener{

	private UpLoadTaskList upLoadTaskList;
	protected Logger	logger	= LoggerFactory.getLogger(getClass());
	// 应用监听器的销毁方法     
	public void contextDestroyed(ServletContextEvent servletContextEvent) {     
		ServletContext servletContext = servletContextEvent.getServletContext(); 
		MessageDispatcher.listenStop();
		UDPServer.listenStop();
		logger.info("销毁工作完成...");    
	} 
	
    // 应用监听器的初始化方法     
	public void contextInitialized(ServletContextEvent servletContextEvent) {     
	    // 通过这个事件可以获取整个应用的空间     
		// 在整个web应用下面启动的时候做一些初始化的内容添加工作     
	    ServletContext servletContext = servletContextEvent.getServletContext();    
	    
	    MessageDispatcher.listenStart();
	    logger.info("消息监听器初始化工作完成...");    
	    
	      UDPServer.listenStart();
	//    UdpService updThread = new UdpService("3005");
	//    updThread.start();
	    logger.info("UDPServer初始化工作完成...");

//		try {
//			UDPMSGServer.listenStart();
//		} catch (Exception e) {
//			logger.info("UDPMSGServer初始化工作失败");
//		}
//
//		logger.info("UDPMSGServer初始化工作完成...");

	}
	
}
