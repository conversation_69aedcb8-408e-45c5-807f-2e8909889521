<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.kvs.rest.mapper.KvsInfoMapper">
	<select id="getLastUpdateTime" resultType="java.lang.String">
		select to_char(Last_Update_Time,'yyyy-mm-dd hh24:mi:ss.us') as Last_Update_Time from tq_kvs_change_time limit 1
    </select>
    
    <select id="getKvsInfoOnlyQch" resultType="com.tzx.kvs.rest.vo.KvsInfoOnlyQchVo">
		select a.* from (select distinct mxk.kdzdbh, zd.source, zd.ksdcsj, zd.jzsj, zd.xsms, zd.qch, COALESCE(fzk.kvshcbz, 0) as kvshcbz, zd.jzsx 
		from tq_kvsmxk mxk left join tq_zdk zd on zd.kdzdbh = mxk.kdzdbh left join tq_kvsfzk fzk ON mxk.kdzdbh = fzk.kdzdbh) a 
		where a.kvshcbz &lt;&gt; 1 and jzsx = 'ZDSX_YJ' 
		<if test='inF == "t"'>
			and source in 
			<foreach item="source" index="index" collection="showSource" open="(" separator="," close=")">
				#{source}
			</foreach>
			<if test='notInF == "t"'>
				or source not in ('POS','APP','XCX','SERVICE_SELF')
			</if>
		</if>
		order by a.jzsj 
    </select>
    
    <insert id="insertKvsfzk">
<!-- 		insert into tq_kvsfzk (kdzdbh, groupid, rwid, cmbh, cmsl, kdshcsl, kdshcbz, kvshcsl, kvshcbz, czlx) -->
<!-- 		values(#{kdzdbh}, #{groupid}, 0, '', 0.00, 0.00, 0, 0.00, 1, #{type}); -->
		insert into tq_kvsfzk (kdzdbh, groupid, rwid, cmbh, cmsl, kdshcsl, kdshcbz, kvshcsl, kvshcbz, czlx)
		select kdzdbh, #{groupid}, w.rwid, w.cmbh, 0.00, 0.00, 0, 0.00, 1, #{type} from tq_wdk w where w.kdzdbh = #{kdzdbh}
	</insert>
	
    <update id="updateKvsChangeTime">
		update tq_kvs_change_time set Last_Update_Time = CURRENT_TIMESTAMP 
	</update>
	
	<select id="getRecoverKvsInfoOnlyQch" resultType="com.tzx.kvs.rest.vo.KvsInfoOnlyQchVo">
		select a.* from (select distinct fzk.kdzdbh, zd.source, fzk.create_time as ksdcsj, zd.jzsj, zd.xsms, zd.qch, COALESCE(fzk.kvshcbz, 0) as kvshcbz, zd.jzsx 
		from tq_kvsfzk fzk left join tq_zdk zd on zd.kdzdbh = fzk.kdzdbh where fzk.czlx = #{type} and fzk.kvshcbz = 1) a  
		where jzsx = 'ZDSX_YJ' and a.ksdcsj &gt;= now()- interval '${timeLimit} minute' order by a.ksdcsj desc limit #{showCount}
    </select>
    
    <delete id="delKvsfzk">
		delete from tq_kvsfzk where groupid = #{groupid} and kdzdbh = #{kdzdbh} and czlx = #{type};
	</delete>
    
</mapper>
