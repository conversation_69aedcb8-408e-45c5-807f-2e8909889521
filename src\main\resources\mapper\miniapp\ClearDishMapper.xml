<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.miniapp.rest.mapper.MiniAppClearDishMapper">
	<select id="findClearDish" resultType="com.tzx.miniapp.rest.vo.ClearDish">
		select cm.omp_dishesid as dishid,
			   cm.omp_dnid as dnid,
			   0       as meanid,
			   1       as type,
			   1       as status,
			   gqsl    as count,
			   cm.cmbh as dishsno
		from ts_gqk gq
		left join vs_cmk cm on cm.cmid = gq.cmid
	</select>
	
	<select id="findClearDishQm" resultType="com.tzx.miniapp.rest.vo.ClearDishQm">
		select cmid as dishsid, cmbh as dishsno from ts_gqk
	</select>
</mapper>
