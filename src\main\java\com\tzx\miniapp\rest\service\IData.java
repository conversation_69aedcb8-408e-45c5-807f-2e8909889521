package com.tzx.miniapp.rest.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tzx.miniapp.common.ApiSyncVo;
import com.tzx.miniapp.common.Body;
import com.tzx.miniapp.rest.mapper.MiniAppShopBaseInfoMapper;
import com.tzx.publics.util.GsonUtil;
import com.tzx.publics.util.Util;

public abstract class IData<T extends Body> {

	// @Autowired
	// private MiniAppFirstPayMapper firstPayMapper;
	private final static Logger LOGGER = LoggerFactory.getLogger(IData.class);

	public abstract String getParams() throws JsonProcessingException;

	protected String buildReturn(T body, int type, MiniAppShopBaseInfoMapper shopBaseInfoMapper) throws JsonProcessingException {

		
		// TsGgcsk appId = firstPayMapper.getGgcsToWs("appId");
		
//		String dd_hehegu = PropertiesUtil.readValueForClasses(propertiesPath, "zhongshang_appId");
//		String appKey = PropertiesUtil.readValueForClasses(propertiesPath, "zhongshang_appKey");

		String appId = "";
		String appKey = "";
		
		appId = shopBaseInfoMapper.getTthird("ZHONGS", "APPID");
		if(type == 1){
			appKey = shopBaseInfoMapper.getTthird("ZHONGS", "APPKEY");
		} else {
			appKey = shopBaseInfoMapper.getTthird("ZHONGS", "STOREKEY");
		}
		
		long ts = System.currentTimeMillis();
		ApiSyncVo<T> vo = new ApiSyncVo<>();
		vo.setAppId(appId);
		vo.setBody(body);
		vo.setTimestamp(ts + "");
		vo.setSign(getSign(vo, appKey));

		String str = GsonUtil.GsonString(vo);
		return str;
	}

	protected String getSign(ApiSyncVo<?> arv, String appKey) {
		String bodyStr = GsonUtil.GsonString(arv.getBody());
		String text = arv.getAppId() + arv.getTimestamp() + bodyStr + appKey;
//		LOGGER.info(text);
		return Util.ZSSign(text, "UTF-8");
	}
	
	public String getUrl() {
		String url = this.getClass().getName();
		url = url.substring(url.lastIndexOf(".")+1,url.length());
		return url.replace("DataS", "s");
	}

}
