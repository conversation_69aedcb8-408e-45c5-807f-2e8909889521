<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.miniapp.rest.mapper.MiniAppTsCmkMapper">
	<select id="findDishBasicData" resultType="com.tzx.miniapp.rest.vo.Dish">
        select cl.clid as item_menu_id,cl.id as details_id, cl.xmid as item_id ,cl.xmbh as item_no,
        cl.xmmc1 as item_name,cm.dwbh as unit_name,COALESCE(tx.cmjg, cl.xmdj) as price,
        cm.pydm as phonetic_code, (cl.ksrq||' '||cl.kssj) as start_time,(cl.jsrq||' '||cl.jssj) as end_time,
		cm.cmsx as is_combo,cl.yhfsid,cl.yhfsbh,cm.sfxsmx 
		from tq_clmxk cl 
		left join ts_cmk cm on cm.cmid = cl.xmid 
		left join ts_ggcsk gg on sdbt = 'FDJGBH' and bzsm = '分店机构编号' 
		left join ts_psjgsdk jg on jg.jgbh = gg.sdnr 
		left join ts_cmjgtxk tx on tx.jgtxbh = jg.jgtxbh and tx.cmid = cl.xmid
		where cl.clmxlb = #{clmxlb}
    </select>
	<select id="findTcmxByXmid" resultType="com.tzx.miniapp.rest.model.TsTcmxk">
		select * from ts_tcmxk where xmid = #{xmid}
	</select>
</mapper>
