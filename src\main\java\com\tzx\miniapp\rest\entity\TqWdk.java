package com.tzx.miniapp.rest.entity;

import java.lang.Double;
import java.util.Date;

/**
 * TqWdk
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-15
 */
/**
 * 优惠方式：(只有产生那条辅助的记录才会有此赋值，正常菜品是空值)
                  01负记录的情况 (yhfsmxk中那条负记录才会记录)
                  02买赠记录的情况 例:满多少元赠送X元 满X元送X产生的送的那条记录为02
                  03点的项目为优惠项目并且属于折扣类时产生的记录（退的时候会用到此记录)
                      例全单8折，TQ_WDK会产生一条这样的记录
                  04点的项目为优惠项目并且属于折让类时产生的记录（退的时候会用到此记录)
                      例折让8元，TQ_WDK会产生一条这样的记录
                 05)优惠票券产生的记录
此字段只是程序内部处理标识用到，没有必要按照wdk_XX的方式来赋值。

卧单备注(wdbz):wdbz_TC(退菜)  WDBZ_FS(奉送)  WDBZ_MD(免单)  WDBZ_QX(取消) WDBZ_CJ(冲减)

菜目属性(cmsx)：内容有'CMSX_DP'（默认单品）、CMSX_TC（套菜）、CMSX_MX（明细)

辅助点菜号:用来记录优惠方式是一正一负产生项目时的编号，保存为主菜的rwid,删除的时候查找比较方便。套菜也可能会产生这样的情况，所以不能用tcdch.


使用优惠付款方式ID：付款按钮也可以挂优惠方式，所以产生的优惠方式在wdk中就要标识此优惠方式是点付款按钮时产生的，取消此优惠方式的时候也要把产生的那笔付款方式取消掉。

冲减沽清标记: 0或空或null，未冲减沽清     1 已冲减沽清     2 已冲减过沽清并且被删除了



 */
public class TqWdk {
    private Integer rwid;

    private Date tmbj;

    private String fsskjh;

    private Integer yrwid;

    private String kdzdbh;

    private String jzzdbh;

    private Integer clmxid;

    private Integer cmid;

    private String cmbh;

    private String cmmc1;

    private String cmmc2;

    private String dwbh;

    private Integer cmggid;

    private String cmggbh;

    private String yzwbh;

    private String zwbh;

    private String tcfs;

    private Double tcbl;

    private Double tcje;

    private Double fzsl;

    private Double fzje;

    private Integer zdsj;

    private String xdh;

    private String xdhshry;

    private String fwyh;

    private Double cbdj;

    private Double cmdj;

    private Double cmsl;

    private Double cmje;

    private Double sjje;

    private Double yhje;

    private Double dpzkje;

    private Double zrje;

    private String zkzt;

    private String xlzkzt;

    private Integer zkl;

    private Integer yhfsid;

    private String yhfsbh;

    private String yhfs;

    private Integer xlid;

    private String xlbh;

    private String cmsx;

    private String wdbz;

    private String cdbj;

    private String tszt;

    private String qczt;

    private Integer tcid;

    private Integer tcdch;

    private Integer fzdch;

    private String tcsfgh;

    private Integer dcxh;

    private Date fsbbrq;

    private Date jzbbrq;

    private Integer fsbcid;

    private Integer jzbcid;

    private String jzskjh;

    private String kwbz;

    private String cpbh;

    private String dcbz;

    private String zwh;

    private String csbh;

    private String pqhm;

    private Integer syyhfkfsid;

    private Integer scbj;

    private String xsms;

    private Integer cjgqbj;

    private Integer cpbj;

    private String yhyybh;

    private String sfxsmx;

    private String memo;

    private Integer scqrbj;

    private Integer jjcrwid;

    private Double yongjje;

    private Integer yongjzkl;

    private Integer ydxmid;

    private Integer yhxh;

    private Double ecodiscountFee;

    private Integer distype;

    private Integer packid;

    private Double yhqjsje;

    public Integer getRwid() {
        return rwid;
    }

    public void setRwid(Integer rwid) {
        this.rwid = rwid;
    }

    public Date getTmbj() {
        return tmbj;
    }

    public void setTmbj(Date tmbj) {
        this.tmbj = tmbj;
    }

    public String getFsskjh() {
        return fsskjh;
    }

    public void setFsskjh(String fsskjh) {
        this.fsskjh = fsskjh;
    }

    public Integer getYrwid() {
        return yrwid;
    }

    public void setYrwid(Integer yrwid) {
        this.yrwid = yrwid;
    }

    public String getKdzdbh() {
        return kdzdbh;
    }

    public void setKdzdbh(String kdzdbh) {
        this.kdzdbh = kdzdbh;
    }

    public String getJzzdbh() {
        return jzzdbh;
    }

    public void setJzzdbh(String jzzdbh) {
        this.jzzdbh = jzzdbh;
    }

    public Integer getClmxid() {
        return clmxid;
    }

    public void setClmxid(Integer clmxid) {
        this.clmxid = clmxid;
    }

    public Integer getCmid() {
        return cmid;
    }

    public void setCmid(Integer cmid) {
        this.cmid = cmid;
    }

    public String getCmbh() {
        return cmbh;
    }

    public void setCmbh(String cmbh) {
        this.cmbh = cmbh;
    }

    public String getCmmc1() {
        return cmmc1;
    }

    public void setCmmc1(String cmmc1) {
        this.cmmc1 = cmmc1;
    }

    public String getCmmc2() {
        return cmmc2;
    }

    public void setCmmc2(String cmmc2) {
        this.cmmc2 = cmmc2;
    }

    public String getDwbh() {
        return dwbh;
    }

    public void setDwbh(String dwbh) {
        this.dwbh = dwbh;
    }

    public Integer getCmggid() {
        return cmggid;
    }

    public void setCmggid(Integer cmggid) {
        this.cmggid = cmggid;
    }

    public String getCmggbh() {
        return cmggbh;
    }

    public void setCmggbh(String cmggbh) {
        this.cmggbh = cmggbh;
    }

    public String getYzwbh() {
        return yzwbh;
    }

    public void setYzwbh(String yzwbh) {
        this.yzwbh = yzwbh;
    }

    public String getZwbh() {
        return zwbh;
    }

    public void setZwbh(String zwbh) {
        this.zwbh = zwbh;
    }

    public String getTcfs() {
        return tcfs;
    }

    public void setTcfs(String tcfs) {
        this.tcfs = tcfs;
    }

    public Double getTcbl() {
        return tcbl;
    }

    public void setTcbl(Double tcbl) {
        this.tcbl = tcbl;
    }

    public Double getTcje() {
        return tcje;
    }

    public void setTcje(Double tcje) {
        this.tcje = tcje;
    }

    public Double getFzsl() {
        return fzsl;
    }

    public void setFzsl(Double fzsl) {
        this.fzsl = fzsl;
    }

    public Double getFzje() {
        return fzje;
    }

    public void setFzje(Double fzje) {
        this.fzje = fzje;
    }

    public Integer getZdsj() {
        return zdsj;
    }

    public void setZdsj(Integer zdsj) {
        this.zdsj = zdsj;
    }

    public String getXdh() {
        return xdh;
    }

    public void setXdh(String xdh) {
        this.xdh = xdh;
    }

    public String getXdhshry() {
        return xdhshry;
    }

    public void setXdhshry(String xdhshry) {
        this.xdhshry = xdhshry;
    }

    public String getFwyh() {
        return fwyh;
    }

    public void setFwyh(String fwyh) {
        this.fwyh = fwyh;
    }

    public Double getCbdj() {
        return cbdj;
    }

    public void setCbdj(Double cbdj) {
        this.cbdj = cbdj;
    }

    public Double getCmdj() {
        return cmdj;
    }

    public void setCmdj(Double cmdj) {
        this.cmdj = cmdj;
    }

    public Double getCmsl() {
        return cmsl;
    }

    public void setCmsl(Double cmsl) {
        this.cmsl = cmsl;
    }

    public Double getCmje() {
        return cmje;
    }

    public void setCmje(Double cmje) {
        this.cmje = cmje;
    }

    public Double getSjje() {
        return sjje;
    }

    public void setSjje(Double sjje) {
        this.sjje = sjje;
    }

    public Double getYhje() {
        return yhje;
    }

    public void setYhje(Double yhje) {
        this.yhje = yhje;
    }

    public Double getDpzkje() {
        return dpzkje;
    }

    public void setDpzkje(Double dpzkje) {
        this.dpzkje = dpzkje;
    }

    public Double getZrje() {
        return zrje;
    }

    public void setZrje(Double zrje) {
        this.zrje = zrje;
    }

    public String getZkzt() {
        return zkzt;
    }

    public void setZkzt(String zkzt) {
        this.zkzt = zkzt;
    }

    public String getXlzkzt() {
        return xlzkzt;
    }

    public void setXlzkzt(String xlzkzt) {
        this.xlzkzt = xlzkzt;
    }

    public Integer getZkl() {
        return zkl;
    }

    public void setZkl(Integer zkl) {
        this.zkl = zkl;
    }

    public Integer getYhfsid() {
        return yhfsid;
    }

    public void setYhfsid(Integer yhfsid) {
        this.yhfsid = yhfsid;
    }

    public String getYhfsbh() {
        return yhfsbh;
    }

    public void setYhfsbh(String yhfsbh) {
        this.yhfsbh = yhfsbh;
    }

    public String getYhfs() {
        return yhfs;
    }

    public void setYhfs(String yhfs) {
        this.yhfs = yhfs;
    }

    public Integer getXlid() {
        return xlid;
    }

    public void setXlid(Integer xlid) {
        this.xlid = xlid;
    }

    public String getXlbh() {
        return xlbh;
    }

    public void setXlbh(String xlbh) {
        this.xlbh = xlbh;
    }

    public String getCmsx() {
        return cmsx;
    }

    public void setCmsx(String cmsx) {
        this.cmsx = cmsx;
    }

    public String getWdbz() {
        return wdbz;
    }

    public void setWdbz(String wdbz) {
        this.wdbz = wdbz;
    }

    public String getCdbj() {
        return cdbj;
    }

    public void setCdbj(String cdbj) {
        this.cdbj = cdbj;
    }

    public String getTszt() {
        return tszt;
    }

    public void setTszt(String tszt) {
        this.tszt = tszt;
    }

    public String getQczt() {
        return qczt;
    }

    public void setQczt(String qczt) {
        this.qczt = qczt;
    }

    public Integer getTcid() {
        return tcid;
    }

    public void setTcid(Integer tcid) {
        this.tcid = tcid;
    }

    public Integer getTcdch() {
        return tcdch;
    }

    public void setTcdch(Integer tcdch) {
        this.tcdch = tcdch;
    }

    public Integer getFzdch() {
        return fzdch;
    }

    public void setFzdch(Integer fzdch) {
        this.fzdch = fzdch;
    }

    public String getTcsfgh() {
        return tcsfgh;
    }

    public void setTcsfgh(String tcsfgh) {
        this.tcsfgh = tcsfgh;
    }

    public Integer getDcxh() {
        return dcxh;
    }

    public void setDcxh(Integer dcxh) {
        this.dcxh = dcxh;
    }

    public Date getFsbbrq() {
        return fsbbrq;
    }

    public void setFsbbrq(Date fsbbrq) {
        this.fsbbrq = fsbbrq;
    }

    public Date getJzbbrq() {
        return jzbbrq;
    }

    public void setJzbbrq(Date jzbbrq) {
        this.jzbbrq = jzbbrq;
    }

    public Integer getFsbcid() {
        return fsbcid;
    }

    public void setFsbcid(Integer fsbcid) {
        this.fsbcid = fsbcid;
    }

    public Integer getJzbcid() {
        return jzbcid;
    }

    public void setJzbcid(Integer jzbcid) {
        this.jzbcid = jzbcid;
    }

    public String getJzskjh() {
        return jzskjh;
    }

    public void setJzskjh(String jzskjh) {
        this.jzskjh = jzskjh;
    }

    public String getKwbz() {
        return kwbz;
    }

    public void setKwbz(String kwbz) {
        this.kwbz = kwbz;
    }

    public String getCpbh() {
        return cpbh;
    }

    public void setCpbh(String cpbh) {
        this.cpbh = cpbh;
    }

    public String getDcbz() {
        return dcbz;
    }

    public void setDcbz(String dcbz) {
        this.dcbz = dcbz;
    }

    public String getZwh() {
        return zwh;
    }

    public void setZwh(String zwh) {
        this.zwh = zwh;
    }

    public String getCsbh() {
        return csbh;
    }

    public void setCsbh(String csbh) {
        this.csbh = csbh;
    }

    public String getPqhm() {
        return pqhm;
    }

    public void setPqhm(String pqhm) {
        this.pqhm = pqhm;
    }

    public Integer getSyyhfkfsid() {
        return syyhfkfsid;
    }

    public void setSyyhfkfsid(Integer syyhfkfsid) {
        this.syyhfkfsid = syyhfkfsid;
    }

    public Integer getScbj() {
        return scbj;
    }

    public void setScbj(Integer scbj) {
        this.scbj = scbj;
    }

    public String getXsms() {
        return xsms;
    }

    public void setXsms(String xsms) {
        this.xsms = xsms;
    }

    public Integer getCjgqbj() {
        return cjgqbj;
    }

    public void setCjgqbj(Integer cjgqbj) {
        this.cjgqbj = cjgqbj;
    }

    public Integer getCpbj() {
        return cpbj;
    }

    public void setCpbj(Integer cpbj) {
        this.cpbj = cpbj;
    }

    public String getYhyybh() {
        return yhyybh;
    }

    public void setYhyybh(String yhyybh) {
        this.yhyybh = yhyybh;
    }

    public String getSfxsmx() {
        return sfxsmx;
    }

    public void setSfxsmx(String sfxsmx) {
        this.sfxsmx = sfxsmx;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getScqrbj() {
        return scqrbj;
    }

    public void setScqrbj(Integer scqrbj) {
        this.scqrbj = scqrbj;
    }

    public Integer getJjcrwid() {
        return jjcrwid;
    }

    public void setJjcrwid(Integer jjcrwid) {
        this.jjcrwid = jjcrwid;
    }

    public Double getYongjje() {
        return yongjje;
    }

    public void setYongjje(Double yongjje) {
        this.yongjje = yongjje;
    }

    public Integer getYongjzkl() {
        return yongjzkl;
    }

    public void setYongjzkl(Integer yongjzkl) {
        this.yongjzkl = yongjzkl;
    }

    public Integer getYdxmid() {
        return ydxmid;
    }

    public void setYdxmid(Integer ydxmid) {
        this.ydxmid = ydxmid;
    }

    public Integer getYhxh() {
        return yhxh;
    }

    public void setYhxh(Integer yhxh) {
        this.yhxh = yhxh;
    }

    public Double getEcodiscountFee() {
        return ecodiscountFee;
    }

    public void setEcodiscountFee(Double ecodiscountFee) {
        this.ecodiscountFee = ecodiscountFee;
    }

    public Integer getDistype() {
        return distype;
    }

    public void setDistype(Integer distype) {
        this.distype = distype;
    }

    public Integer getPackid() {
        return packid;
    }

    public void setPackid(Integer packid) {
        this.packid = packid;
    }

    public Double getYhqjsje() {
        return yhqjsje;
    }

    public void setYhqjsje(Double yhqjsje) {
        this.yhqjsje = yhqjsje;
    }
}