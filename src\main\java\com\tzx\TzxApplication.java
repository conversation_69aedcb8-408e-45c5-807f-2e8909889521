package com.tzx;

import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.Banner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Import;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import com.tzx.config.EmbeddedTomcatConfiguration;
import com.tzx.config.MatualDbUtil;
import com.tzx.publics.common.Version;
import com.tzx.receiver.common.listener.NewTzxMsgListener;
import com.tzx.receiver.common.upload.UDPMSGServer;
import com.tzx.receiver.common.utils.DBUtils;
import com.tzx.receiver.common.utils.SpringContextHolder;

//禁用自动配置数据源，代码里要使用动态切换
@SpringBootApplication(exclude = {DruidDataSourceAutoConfigure.class,DataSourceAutoConfiguration.class})
@MapperScan(basePackages = "com.tzx.**.mapper")
@EnableTransactionManagement
@EnableWebMvc
@Import(EmbeddedTomcatConfiguration.class)
@EnableScheduling
public class TzxApplication  {
    public static boolean loadFinish = false;
    private static Logger LOGGER = LoggerFactory.getLogger(TzxApplication.class);
    @Autowired
    JdbcTemplate jdbcTemplate;
    /**
     * 注册listener 最好在这里通过代码注册吧，别用注解形式了。
     * 因为注解其他人读代码时，不太好定位都有哪些listener
     * 全都写到这里一目了然
     */
//    @Bean("tzxMsgListener")
//    这里依赖一下jdbcTemplate，再然后再将它注入到DBUtils中。否则
//    有可能会出现ServletListenerRegistrationBean在jdbcTemplate前
//    被引入，然后再使用它就出错了
//    @DependsOn({"jdbcTemplate","upLoadTaskList"})
//    public ServletListenerRegistrationBean<TzxMsgListener> tzxMsgListener(){
//        DBUtils.setJdbcTemplate(jdbcTemplate);
//        return  new ServletListenerRegistrationBean<TzxMsgListener>(new TzxMsgListener());
//    }
    @Bean("newTzxMsgListener")
  //  这里依赖一下jdbcTemplate，再然后再将它注入到DBUtils中。否则
 //   有可能会出现ServletListenerRegistrationBean在jdbcTemplate前
 //   被引入，然后再使用它就出错了
    @DependsOn({"jdbcTemplate","upLoadTaskList"})
    public NewTzxMsgListener newTzxMsgListener(){
        NewTzxMsgListener newTzxMsgListener = new NewTzxMsgListener();
        DBUtils.setJdbcTemplate(jdbcTemplate);
        return newTzxMsgListener;
    }

    /**
     * 程序入口点
     * @param args
     */
    public static void main(String[] args) {
        //程序入口
//        SpringApplication.run(TzxApplication.class, args);
        MatualDbUtil.checkDBConnect();
        SpringApplication application = new SpringApplication(TzxApplication.class);
        application.setBannerMode(Banner.Mode.OFF);

        ApplicationContext applicationContext =  application.run(args);
        SpringContextHolder springContextHolder = new SpringContextHolder();
        springContextHolder.setApplicationContext(applicationContext);
        
        LOGGER.info("**************************************************");
        LOGGER.info("小程序当前版本编号：" + Version.MINIAPP_VERSION);
        LOGGER.info("**************************************************");
        LOGGER.info("版本历史信息：");
        LOGGER.info(Version.MINIAPP_VERSION_LOG);
        LOGGER.info("**************************************************");
        LOGGER.info("App服务当前版本编号：" + Version.MOBILEPOS_VERSION);
        LOGGER.info("**************************************************");
        LOGGER.info("版本历史信息：");
        LOGGER.info(Version.MOBILEPOS_VERSION_LOG);
        LOGGER.info("**************************************************");
        LOGGER.info("监听服务当前版本编号：" + Version.RECEVIER_VERSION);
        LOGGER.info("**************************************************");
        LOGGER.info("版本历史信息：");
        LOGGER.info(Version.RECEVIER_VERSION_LOG);
        LOGGER.info("**************************************************");
        LOGGER.info("ECO服务当前版本编号：" + Version.ECO_VERSION);
        LOGGER.info("**************************************************");
        LOGGER.info("ECO服务版本历史信息：");
        LOGGER.info(Version.ECO_VERSION_LOG);
        LOGGER.info("**************************************************");
        LOGGER.info("凯景服务当前版本编号：" + Version.FMCGBI_VERSION);
        LOGGER.info("**************************************************");
        LOGGER.info("凯景服务版本历史信息：");
        LOGGER.info(Version.FMCGBI_VERSION_LOG);
        LOGGER.info("**************************************************");
        
        try {
            UDPMSGServer.listenStart();
        } catch (Exception e) {
            LOGGER.info("UDPMSGServer初始化工作失败");
        }

        LOGGER.info("UDPMSGServer初始化工作完成...");
        loadFinish = true;
    }


}
