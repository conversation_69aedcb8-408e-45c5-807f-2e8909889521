package com.tzx.miniapp.rest.service.impl;

import com.tzx.commapi.rest.service.IUseYhfsApiService;
import com.tzx.commapi.rest.vo.UseYhfsParam;
import com.tzx.miniapp.common.Constant;
import com.tzx.miniapp.common.Data;
import com.tzx.miniapp.rest.mapper.MiniAppCancelBillMapper;
import com.tzx.miniapp.rest.mapper.MiniAppFirstPayMapper;
import com.tzx.miniapp.rest.mapper.MiniAppOrderPrecheckMapper;
import com.tzx.miniapp.rest.mapper.MiniAppShopBaseInfoMapper;
import com.tzx.miniapp.rest.mapper.MiniAppShopStatusMapper;
import com.tzx.miniapp.rest.mapper.MiniAppTsPsjgsdkMapper;
import com.tzx.miniapp.rest.model.TqFklslsk;
import com.tzx.miniapp.rest.model.TqJtztk;
import com.tzx.miniapp.rest.model.TqWdkCouponTemp;
import com.tzx.miniapp.rest.model.TqZdk;
import com.tzx.miniapp.rest.model.TsBmkzk;
import com.tzx.miniapp.rest.model.TsCmk;
import com.tzx.miniapp.rest.model.TsFkfssdk;
import com.tzx.miniapp.rest.model.TsGgcsk;
import com.tzx.miniapp.rest.model.TsPsjgsdk;
import com.tzx.miniapp.rest.model.TsYhfssdk;
import com.tzx.miniapp.rest.service.IMiniAppFirstPayService;
import com.tzx.miniapp.rest.service.IMiniAppOrderPrecheckService;
import com.tzx.miniapp.rest.vo.AccountsOrder;
import com.tzx.miniapp.rest.vo.BtPayments;
import com.tzx.miniapp.rest.vo.BtYdd;
import com.tzx.miniapp.rest.vo.BtYdxm2;
import com.tzx.miniapp.rest.vo.DishVo;
import com.tzx.miniapp.rest.vo.Shops;
import com.tzx.publics.common.BillNoData;
import com.tzx.publics.listener.InitDataListener;
import com.tzx.publics.util.ArithUtil;
import com.tzx.publics.util.Const;
import com.tzx.publics.util.DatagramUtil;
import com.tzx.publics.util.DateUtil;
import com.tzx.publics.util.PosPrintJna;
import com.tzx.publics.util.PosPrintKichenJna;
import com.tzx.publics.util.PropertiesUtil;
import com.tzx.publics.util.StringUtil;
import com.tzx.publics.util.Util;
import com.tzx.receiver.common.utils.DBUtils;
import net.sf.json.JSONArray;
import net.sf.json.JSONException;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
public class MiniAppFirstPayServiceImpl implements IMiniAppFirstPayService {
	private final static Logger LOGGER = LoggerFactory.getLogger(MiniAppFirstPayServiceImpl.class);

	private static Map<String, Integer> BillList = new ConcurrentHashMap<String, Integer>();

	// 结账清台接口注入对象
	@Autowired
	private MiniAppFirstPayMapper firstPayMapper;

	// 门店状态查询接口注入对象
	@Autowired
	private MiniAppShopStatusMapper shopStatusMapper;

	// 同步基础资料接口注入对象
	@Autowired
	private MiniAppShopBaseInfoMapper shopBaseInfoMapper;

	// 退单接口注入对象
	@Autowired
	private MiniAppCancelBillMapper cancelBillMapper;

	// 机构配置信息查询接口注入对象
	@Autowired
	private MiniAppTsPsjgsdkMapper tsPsjgsdkMapper;
	
	// 预定单接口注入对象
	@Autowired
	private MiniAppOrderPrecheckMapper orderPrecheckMapper;
	
	// 不计收入拆分
	@Autowired
    private IUseYhfsApiService useYhfsApiService;

    @Autowired
    private IMiniAppOrderPrecheckService orderPrecheckService;

	@Transactional
	public synchronized Data getBillStatus(JSONObject payData, int operType) {




		// **************************************************
		// 读取预订单号、桌位编号、
		String yddbh = payData.optString("out_order_id");
		// 转成内部编号
		String tsyddbh = Constant.BILL_PREFIX + yddbh;

		// 创建返回数据对象
		Data data = new Data();
		// 设置付款失败标志，真正付款完成之后再置标志
		data.setYddbh(tsyddbh);
		data.setSuccess(0);
		data.setMsg("检查订单是否正在处理或者已经结账或者已经退单失败！");

		// operType=1：表示申请锁定处理；非1：表示处理完成，有了结果，需要解锁
		if (operType == 1) {
			// 检查订单是否正在处理，通过一个缓存列表处理
			if (BillList.containsKey(tsyddbh)) {
				// 正在处理，返回2
				data.setSuccess(2);
				return data;
			}
			;
			int BillStatus = firstPayMapper.getBillStatus(tsyddbh);
			if (BillStatus == 0) {
				// 没有预落订单数据，返回4
				data.setMsg("没有找到预落订单数据！");
				data.setSuccess(4);
				return data;
			}
			// 1：表示结账完成；2：表示退单完成；3：表示预落单完成
			if (BillStatus == 1) {
				// 表示订单已经结账完成，返回1
				data.setMsg("订单已经结账！");
				data.setSuccess(2);
				return data;
			}
			if (BillStatus == 2) {
				// 表示订单已经退单，返回3
				data.setMsg("订单已经退单！");
				data.setSuccess(3);
				return data;
			}
			// 3：表示预落单正常完成，可以正常结账
			BillList.put(tsyddbh, 1);
			data.setSuccess(1);
			return data;
		} else {
			// 删除已经处理完成账单，清除列表中的对应订单数据
			BillList.remove(tsyddbh);
			data.setSuccess(1);
			return data;
		}
	}

	@Transactional
	public Data firstPay(JSONObject payData, BillNoData billNoData) {
		// **************************************************
		// 读取预订单号、桌位编号、
		// **************************************************
		String yddbh = payData.optString("out_order_id");
		String tablesno = payData.optString("tableName"); // 取小程序的桌台名称
		String tableorno = payData.optString("tableorno");
		// 转成内部编号
		String tsyddbh = Constant.BILL_PREFIX + yddbh;

		// **************************************************
		// 检查门店状态
		// **************************************************
		Data dataStatus = shopStatus();
		dataStatus.setYddbh(yddbh);
		if (dataStatus.getSuccess() != 1) {
			return dataStatus;
		}

		BtYdd btYdd = firstPayMapper.getYdd(tsyddbh);
		if (btYdd.getDdzt() == "5") {
			dataStatus.setSuccess(3);
			dataStatus.setMsg("订单已经处理完成！不要重复提交！");
		}

		// **************************************************
		// 创建返回数据对象
		// **************************************************
		Data data = new Data();
		// 设置付款失败标志，真正付款完成之后再置标志
		data.setYddbh(tsyddbh);
		data.setSuccess(0);
		data.setMsg("订单处理失败！");
		// 班次编号
		int bcid = 0;
		LOGGER.info("门店开始结账，平台订单编号：" + yddbh);
		// 开始结账时间
		Date ksjzsj = new Date();
		// 报表日期
		Map<String, String> bbrqMap = shopStatusMapper.findBbrq();
		// 生成默认报表日期
		String bbrq = DateUtil.getNowDateYYDDMM();
		if (null != bbrqMap && bbrqMap.size() != 0) {
			bbrq = bbrqMap.get("bbrq");
		}
		data.setBbrq(bbrq);
		// if ("2".equals(tableorno)) { // 该字段只是在小程序后台展示数据时使用的.
		// 	tablesno = "999";
		// }
		// 读取机台状态库
		LOGGER.info("查询机台状态数据……");
		TqJtztk jtzt = firstPayMapper.getJtZtk(DateUtil.parseDate(bbrq));
		data.setMsg(jtzt.getRybh());

		// 第一步：预定账单转成账单
		// 第二步：计算多收票券金额，生成多收记录，没有其它处理
		// 第三步：重新处理菜品优惠活动，我们系统内部活动（这个过程已经没用）
		// 第四步：处理微生活优惠券面值金额信息，生成活动数据，下步调用存储过程处理微生活活动产生优惠券数据（这个过程已经失效）
		// 第五步：调用微生活活动优惠券处理存储过程（这个过程已经失效）
		// 第六步：处理支付信息，微生活菜品券和代金券需要算成优惠，不能算成付款
		// 第七步：调用微生活优惠券产生金额处理存储过程
		// 第八步：计算账单金额

		try {
			// **************************************************
			// 第一步：预定单转账单
			// **************************************************
			// 预定单号、报表日期、当前SERVER登录人员编号、员工登录次数
			LOGGER.info("预定账单转成正式账单……");
			int ytzR = firstPayMapper.yddToZd(tsyddbh, DateUtil.parseDate(bbrq), jtzt.getRybh(), Integer.parseInt(jtzt.getYgdlcs()));

			if (ytzR != 0) {
				LOGGER.info("预订单转订单失败,平台订单编号：" + yddbh + "，错误代码：" + ytzR);
				data.setMsg("转订单失败，错误代码：" + ytzR);
				data.setData(new HashMap<String, Object>());
				return data;
			}

			// **************************************************
			// 获取预定账单
			// **************************************************
			JSONObject orderInfo = payData.optJSONObject("order_info");

			// **************************************************
			// 读取微生活使用买一送一活动信息列表，包含活动ID、券ID、赠送菜品编号
			// **************************************************
			// {"activity":[{"aid":"25","couponid":"1628689186707463298","gift":"02040002"}]}
			LOGGER.info("保留活动列表……");
			JSONArray activityList;
			if (payData.has("activity")) {
				activityList = payData.optJSONArray("activity");
			} else {
				activityList = new JSONArray();
			}
			LOGGER.info(activityList.toString());
			// 单品
			JSONArray normalitems = orderInfo.optJSONArray("normalitems");
			// 套餐
			JSONArray setmeal = orderInfo.optJSONArray("setmeal");
			// 新大众营销
			JSONArray activityDishes = orderInfo.optJSONArray("activityDishes");
			
//			List<ReqParam> reqParams = new ArrayList<ReqParam>();
			// **************************************************
			// 生成赠送菜品列表
			// **************************************************
			// 首先生成一个赠送菜品列表，后面拆分主菜需要使用这个列表
			// 赠送菜品可能重复，所以使用顺序序号做为标识，按照顺序匹配处理，处理一个删除一个，使用点餐序号排除赠送主菜，防止重复关联，重复处理赠送主菜
			// 另外赠送主菜肯定只有一份，如果赠送主菜存在多份，需要拆成N+1份，N保留，1表示赠送主菜
			// 单品
			LOGGER.info("生成赠送菜品列表");
			Map<Integer, JSONObject> activeDishList = new HashMap<Integer, JSONObject>();
			for (int i = 0; i < normalitems.size(); i++) {
				JSONObject jo = normalitems.getJSONObject(i);
				// 只加赠送菜品，正常菜品不做处理
				if (jo.has("activity")) {
					JSONObject activityAndCouponInfo = jo.optJSONObject("activity");
					if (activityAndCouponInfo.optInt("type", 0) != 1) {
						data.setMsg("结账清台失败：目前只支持买一送一优惠活动！");
						return data;
					}
					// 保存主菜菜品
					activeDishList.put(activeDishList.size() + 1, jo);
				}
				
//	            ReqParam reqParam = new ReqParam();
//	            reqParam.setItem_id(jo.optString("dishsno"));
//	            reqParam.setItem_name(jo.optString("name"));
//	            reqParam.setCount(new BigDecimal(jo.optInt("number")));
//	            reqParams.add(reqParam);
			}
			// 套餐
			for (int i = 0; i < setmeal.size(); i++) {
				JSONObject jo = setmeal.getJSONObject(i);
				// 只加赠送菜品，正常菜品不做处理
				if (jo.has("activity")) {
					JSONObject activityAndCouponInfo = jo.optJSONObject("activity");
					if (activityAndCouponInfo.optInt("type", 0) != 1) {
						data.setMsg("结账清台失败：目前只支持买一送一优惠活动！");
						return data;
					}
					// 保存主菜菜品
					activeDishList.put(activeDishList.size() + 1, jo);
				}
				
//				ReqParam reqParam = new ReqParam();
//				reqParam.setItem_id(jo.optString("dishsno"));
//				reqParam.setItem_name(jo.optString("name"));
//				reqParam.setCount(new BigDecimal(jo.optInt("number")));
//				reqParams.add(reqParam);
				
				// 处理固定明细
//				JSONArray maindish = jo.optJSONArray("maindish");
//				for (int j = 0; j < maindish.size(); j++) {
//					JSONObject jo1 = maindish.getJSONObject(j);
//					ReqParam reqParam1 = new ReqParam();
//					reqParam1.setItem_id(jo1.optString("dishsno"));
//					reqParam1.setItem_name(jo1.optString("name"));
//					reqParam1.setCount(new BigDecimal(jo1.optInt("number")));
//					reqParams.add(reqParam1);
//				}
				// 处理可选明细
				JSONArray mandatory = jo.optJSONArray("mandatory");
				for (int j = 0; j < mandatory.size(); j++) {
					JSONObject jo1 = mandatory.getJSONObject(j);

					String dishNo = jo1.optString("dishsno", "");
					TsCmk cmk = orderPrecheckMapper.getDishByCode(dishNo);
					int cmId = null == cmk ? jo1.optInt("id") : cmk.getCmid();
					
					double mxsl = 1;
					double fzsl = orderPrecheckMapper.getFzsl(jo1.optInt("rpdid"), cmId);
					if (0.5 == fzsl) {
						mxsl = ArithUtil.mul(0.5, jo1.optInt("number"));
					} else {
						mxsl = jo1.optInt("number");
					}
					
//					ReqParam reqParam1 = new ReqParam();
//					reqParam1.setItem_id(jo1.optString("dishsno"));
//					reqParam1.setItem_name(jo1.optString("name"));
//					reqParam1.setCount(new BigDecimal(mxsl));
//					reqParams.add(reqParam1);
				}
			}
			LOGGER.info("    " + activeDishList.toString());
			
			// 实时沽清
//			DecSaleOut(reqParams);

			Map<Integer, JSONObject> newActiveDishList = new HashMap<Integer, JSONObject>();
			if(null == activityDishes) {
				activityDishes = new JSONArray();
			}
			double discountAmount = 0;
			for (int i = 0; i < activityDishes.size(); i++) {
				JSONObject jo = activityDishes.getJSONObject(i);
				newActiveDishList.put(newActiveDishList.size() + 1, jo);
				
				JSONObject buyDish = jo.optJSONObject("buyDish");
				JSONObject giftDish = jo.optJSONObject("giftDish");
				JSONObject activity = jo.optJSONObject("activity");
				if (!"4".equals(activity.optString("type"))) {
					double giftTotal = ArithUtil.mul(giftDish.optDouble("price"), giftDish.optInt("number"));
					double activityGiftTotal = ArithUtil.mul(ArithUtil.div(activity.optDouble("giftDishPrice"), 100, 2), activity.optInt("gitDishCount"));
					discountAmount = ArithUtil.add(discountAmount, ArithUtil.sub(giftTotal, activityGiftTotal));
				} else {
					double buyTotal = ArithUtil.mul(buyDish.optDouble("aprice") + buyDish.optDouble("price"), buyDish.optInt("number"));
					double activityGiftTotal = ArithUtil.mul(ArithUtil.div(activity.optDouble("buyDishPrice"), 100, 2), activity.optInt("buyDishCount"));
					discountAmount = ArithUtil.add(discountAmount, ArithUtil.sub(buyTotal, activityGiftTotal));
				}
				
			}
			
			// **************************************************
			// 门店营业模式
			// **************************************************
			TsGgcsk yyms = firstPayMapper.getGgcsToWs("MDYYMS");
			if (null != yyms && "3".equals(yyms.getSdnr())) {
				bcid = firstPayMapper.getBcid(DateUtil.parseDate(bbrq));
			}

			// **************************************************
			// 第二步：计算多收票券金额
			// **************************************************
			// 如果多收优惠券金额大于结账金额，多出部分算做多收礼券金额
			double payAmount = 0.00;
			JSONObject pay_info = payData.optJSONObject("pay_info");
			JSONArray pay_info_array = pay_info.optJSONArray("pay_info");
			int isMemberPrice = payData.optInt("isMemberPrice", 1);
			for (int i = 0; i < pay_info_array.size(); i++) {
				JSONObject pay_info_obj = pay_info_array.getJSONObject(i);
				String source = pay_info_obj.optString("source");
//				if ("balance".equals(source)) {
//					isBalance = true;
//				}
				// 忽略微生活内部支付方式
				if ("wlife".equals(source)) {
					continue;
				}
				payAmount = payAmount + pay_info_obj.optDouble("amount");
			}
			
			String chargeMode = orderInfo.optString("charge_mode", "1");
			if (!"1".equals(chargeMode) && !"2".equals(chargeMode) && isMemberPrice != 1) {
				orderPrecheckMapper.clearYddPayActiveByType(tsyddbh);
			}
//			if (payAmount > btYdd.getTotalprice()) {
//				firstPayMapper.insertBtYddPayActive(tsyddbh, "多收", (payAmount - btYdd.getTotalprice() + firstPayMapper.selectMemberPrice(tsyddbh)) * -1, "0000000000", "COUPON");
//			}
			
			double total = ArithUtil.sub(ArithUtil.sub(btYdd.getTotalprice(), discountAmount), firstPayMapper.selectMemberPrice(tsyddbh));
			if (payAmount > total) {
				firstPayMapper.insertBtYddPayActive(tsyddbh, "多收", ArithUtil.mul(ArithUtil.sub(payAmount, total), -1), "0000000000", "COUPON", 0);
			}

			// **************************************************
			// 第三步：重新处理菜品优惠活动
			// **************************************************
			List<Map<String, Integer>> dList = firstPayMapper.findDiscounts(tsyddbh);
			// 查询账单
			TqZdk zdk = firstPayMapper.getZdbhByYdd(tsyddbh);
			// 注意：更新账单状态变成未结状态，然后才能使用优惠
			firstPayMapper.updateZdkNoQch(zdk.getKdzdbh(), ksjzsj, ksjzsj, "XCX", "ZDSX_WJ", tablesno, bcid, "1");
			if (null != dList && dList.size() > 0) {
				for (Map<String, Integer> map : dList) {
					// 使用优惠
					if (firstPayMapper.addYhfs(zdk.getKdzdbh(), map.get("itemid"), "99", 1) != 0) {
						data.setMsg("处理优惠活动失败！");
						return data;
					}
				}
			}

			// **************************************************
			// 第四步：处理微生活优惠券面值金额信息
			// **************************************************
			// 注意需要区分会员普通优惠券和微生活活动优惠券的区别，会员普通优惠券计入付款，而活动优惠券计入优惠
			for (int i = 0; i < pay_info_array.size(); i++) {
				JSONObject pay_info_obj = pay_info_array.getJSONObject(i);
				String source = pay_info_obj.optString("source");
				// 不是优惠券付款方式直接继续循环
				if (!"product".equals(source)) {
					continue;
				}
				// 微生活优惠活动生成的菜品券付款方式在我们系统内部转成优惠，不能按照付付款方式处理
				// 如果是会员券还是按照付款方式处理
				// 微生活把这两种券都合并到一起了，所以这里要拆分金额
				JSONArray couponMoneyList;
				if (payData.has("deno_gift_couponMoney")) {
					couponMoneyList = payData.optJSONArray("deno_gift_couponMoney");
				} else {
					couponMoneyList = new JSONArray();
				}
				// 直接把优惠券面值放在优惠活动中，方便后面拆分金额
				for (int j = 0; j < activityList.size(); j++) {
					JSONObject activeInfo = activityList.getJSONObject(j);
					for (int k = 0; k < couponMoneyList.size(); k++) {
						JSONObject couponMoney = couponMoneyList.getJSONObject(k);
						if (activeInfo.optString("couponid").equals(couponMoney.optString("id"))) {
							activeInfo.put("money", couponMoney.optDouble("money") / 100);
						}
					}
				}
				// 定义一个赠送主菜、赠送菜品对应列表，匹配活动中的赠送菜品使用
				// 赠送主菜肯定不会重复，但是赠送菜品则会出现重复
				Map<String, String> buyGiftDishList = new HashMap<String, String>();
				// 循环付款列表中的优惠券关联的优惠券ID列表
				// 注意：这个列表中的优惠券包含两部分来源：会员普通优惠券、微生活活动优惠券，微生活把这两种优惠券都算做
				// 付款，但是我们是只把会员普通优惠券算做付款，而活动优惠券算做优惠，处理方式不同
				JSONArray couponDishsInfo = pay_info_obj.getJSONArray("coupon_dishs_info");
				// 第一层循环券ID列表
				for (int j = couponDishsInfo.size() - 1; j >= 0; j--) {
					String couponID = couponDishsInfo.getString(j);
					// 菜品券ID是否存在活动列表中，存在表示这个优惠券是活动生产的，不是会员正常优惠券
					// 第二层循环活动列表
					for (int k = 0; k < activityList.size(); k++) {
						JSONObject activeInfo = activityList.getJSONObject(k);
						// 券ID匹配表示找到活动对应数据
						if (activeInfo.getString("couponid").equals(couponID)) {
							// 然后根据活动ID、赠送菜品编号，查询赠送菜品信息
							String activeID = activeInfo.getString("aid");
							int intActiveID = (10000 + activeInfo.optInt("aid", 0)) * -1;
							String dishNo = activeInfo.getString("gift");
							// 循环赠菜列表，找到对应赠菜，取出金额
							for (Map.Entry<Integer, JSONObject> activeDishInfo : activeDishList.entrySet()) {
								if (activeDishInfo.getValue().has("activity")) {
									// 根据活动ID、菜品编码找到赠送菜品记录，之后还要判断这个赠送菜品是否已经关联主菜已经处理，因为赠菜可能重复，但是主菜不会重复
									if (activeDishInfo.getValue().optJSONObject("activity").optString("id", "").equals(activeID) && activeDishInfo.getValue().optJSONObject("activity").optString("gift", "").equals(dishNo)) {
										// 判断赠菜是否已经处理
										if (buyGiftDishList.containsKey(activeDishInfo.getValue().optJSONObject("activity").optString("buy"))) {
											continue;
										}
										// 保存优惠券面值和优惠券ID
										firstPayMapper.updateYddActiveCouponInfo(tsyddbh, intActiveID, activeDishInfo.getValue().optJSONObject("activity").optString("buy", ""), activeDishInfo.getValue().optJSONObject("activity").optString("gift", ""), activeInfo.optDouble("money", 0.00), couponID);
										// 加入赠送主菜、赠送菜品关联列表，表示这个赠送菜品关联主菜已经处理
										buyGiftDishList.put(activeDishInfo.getValue().optJSONObject("activity").optString("buy"), activeDishInfo.getValue().optJSONObject("activity").optString("gift"));
										break;
									}
								}
							}
						}
					}
				}
			}

			// **************************************************
			// 第五步：处理微生活优惠券
			// **************************************************
			if (activeDishList.size() > 0 || newActiveDishList.size() > 0) {	
				if (firstPayMapper.calcAceWillActivity(tsyddbh) != 0) {
					data.setMsg("处理微生活优惠活动失败！");
					return data;
				}
			}

			// **************************************************
			LOGGER.info("处理付款数据……");
			// ==============================
			// 说明：微生活代金券和菜品券改成优惠，不再计入付款
			// ==============================
			// 是否存在微生活优惠券付款数据
			boolean isExistAceCoupon = false;

			// 是否存在付款记录
			boolean isPayRecord = false;

			// **************************************************
			// 第六步：处理支付信息
			// **************************************************
			List<Integer> fkfsidList = new ArrayList<Integer>();
			List<BigDecimal> actualmoneyList = new ArrayList<BigDecimal>();
			boolean aoflag = true;
			
			double denoAmountS = 0;
			double saledenoMoneyS = 0;
			JSONArray couponDenoInfoS = new JSONArray();

			double dishsAmountS = 0;
			double saleDishMoneyS = 0;
			JSONArray couponDishsInfoS = new JSONArray();
			
			for (int i = 0; i < pay_info_array.size(); i++) {
				JSONObject pay_info_obj = pay_info_array.getJSONObject(i);
				String source = pay_info_obj.optString("source");
				// 忽略微生活内部支付方式
				if ("wlife".equals(source)) {
					continue;
				}
				TsFkfssdk fkfs = firstPayMapper.getFkfs(source);
				// 已有对应关系：
				// "微储值卡";"balance"，会员储值卡
				// "微生活优惠券";"product"，菜品券
				// "微生活优惠券";"coupon"，代金券
				if (fkfs == null) {
					// 如果没有对应付款方式，全部计处默认的付款方式：小程序付款
					fkfs = firstPayMapper.getFkfsByCode("2020");
				}
				BigDecimal actualmoney = new BigDecimal("0.00");
				actualmoney = BigDecimal.valueOf(pay_info_obj.optDouble("amount"));
				String sfkbz = "";
				// 也是微生活内部一种支付方式
				if ("product".equals(source)) {
					// 微生活优惠活动生成的菜品券付款方式在我们系统内部转成优惠，不能按照付付款方式处理
					// 如果是会员券还是按照付款方式处理
					// 微生活把这两种券都合并到一起了，所以这里要拆分金额
					sfkbz = "0";
					JSONArray couponMoneyList;
					if (payData.has("deno_gift_couponMoney")) {
						couponMoneyList = payData.optJSONArray("deno_gift_couponMoney");
					} else {
						couponMoneyList = new JSONArray();
					}
					for (int j = 0; j < activityList.size(); j++) {
						JSONObject activeInfo = activityList.getJSONObject(j);
						for (int k = 0; k < couponMoneyList.size(); k++) {
							JSONObject couponMoney = couponMoneyList.getJSONObject(k);
							if (activeInfo.optString("couponid").equals(couponMoney.optString("id"))) {
								activeInfo.put("money", couponMoney.optDouble("money") / 100);
							}
						}
					}
					// 循环付款列表中的优惠券关联的优惠券ID列表
					JSONArray couponDishsInfo = pay_info_obj.getJSONArray("coupon_dishs_info");
					// 循环券列表
					for (int j = 0; j < couponDishsInfo.size(); j++) {
						String couponID = couponDishsInfo.getString(j);
						// 菜品券ID是否存在活动列表中，存在表示这个优惠券是活动生产的，不是会员正常优惠券
						for (int k = 0; k < activityList.size(); k++) {
							JSONObject activeInfo = activityList.getJSONObject(k);
							if (activeInfo.getString("couponid").equals(couponID)) {
								// 根据活动ID、赠送菜品编号，查询赠送菜品信息
								String activeID = activeInfo.getString("aid");
								int intActiveID = (10000 + activeInfo.optInt("aid", 0)) * -1;
								String dishNo = activeInfo.getString("gift");
								// 循环赠菜列表，找到对应账菜，取出金额
								for (Map.Entry<Integer, JSONObject> activeDishInfo : activeDishList.entrySet()) {
									if (activeDishInfo.getValue().has("activity")) {
										if (activeDishInfo.getValue().optJSONObject("activity").optString("id", "").equals(activeID) && activeDishInfo.getValue().optJSONObject("activity").optString("gift", "").equals(dishNo)) {
											// 判断是否已经做过关联
											BigDecimal dishMoney = new BigDecimal("0.00");
											// 如果菜品金额大于优惠券的金额，使用优惠券金额计算付款，券不足金额需要支付
											// 如果菜品金额小于优惠券的金额，使用菜品金额计算付款，券多出金额不算，直接抛弃，算做多收票券
											if (activeDishInfo.getValue().optDouble("price", 0.00) > activeInfo.optDouble("money", 0.00)) {
												dishMoney = BigDecimal.valueOf(activeInfo.optDouble("money", 0.00));
											} else {
												dishMoney = BigDecimal.valueOf(activeDishInfo.getValue().optDouble("price", 0.00));
											}
											// 保存优惠券面值和优惠券码
											firstPayMapper.updateYddActiveCouponInfo(tsyddbh, intActiveID, activeDishInfo.getValue().optJSONObject("activity").optString("buy", ""), activeDishInfo.getValue().optJSONObject("activity").optString("gift", ""), activeInfo.optDouble("money", 0.00), couponID);
											// 付款减去微生活优惠券金额
											actualmoney = actualmoney.subtract(dishMoney);
											break;
										}
									}
								}
							}
						}
					}
				}
				if (actualmoney.compareTo(new BigDecimal("0.00")) == 0) {
					// 表示当前付款方式无效
					// 可能都是微生活活动优惠券产生金额，没有会员优惠券金额，所以这条付款直接忽略
					continue;
				}
				// 20190428，微生活菜品券不再记入付款
				if ("product".equals(source)) {
					isExistAceCoupon = true;
					double dishMoney = pay_info_obj.optDouble("amount");
					double saleDishMoney = ArithUtil.div(pay_info_obj.optDouble("sale_dish_money"), 100);
					// 单独记录微生活活动优惠券
					firstPayMapper.insertBtYddPayActive(tsyddbh, pay_info_obj.optString("source", "微生活菜品券"), ArithUtil.sub(dishMoney, saleDishMoney), pay_info_obj.optString("serilNo", "序列号"), "COUPON", 0);
					// 2019-10-21 现在又要记录实收了。。。。。。
					actualmoney = BigDecimal.valueOf(saleDishMoney);
					
					dishsAmountS = dishMoney;
					saleDishMoneyS = saleDishMoney;
					couponDishsInfoS = pay_info_obj.getJSONArray("coupon_dishs_info");
				}
				// 20190428，微生活菜品券不再记入付款
				if ("coupon".equals(source)) {
					isExistAceCoupon = true;
					double denoMoney = pay_info_obj.optDouble("amount");
					double saleDenoMoney = ArithUtil.div(pay_info_obj.optDouble("sale_deno_money"), 100);
					// 单独记录微生活代金券
					firstPayMapper.insertBtYddPayActive(tsyddbh, pay_info_obj.optString("source", "微生活代金券"), ArithUtil.sub(denoMoney, saleDenoMoney), pay_info_obj.optString("serilNo", "序列号"), "COUPON", 0);
					// 2019-10-21 现在又要记录实收了。。。。。。
					actualmoney = BigDecimal.valueOf(saleDenoMoney);
					
					denoAmountS = denoMoney;
					saledenoMoneyS = saleDenoMoney;
					couponDenoInfoS = pay_info_obj.getJSONArray("coupons_info");
				}
				// 20190530，处理微生活储值卡中赠送金额转优惠
				// 拆储值如使用新方法，改动比较大，暂时
				if ("balance".equals(source)) {
					BigDecimal cardMoney = new BigDecimal("0.00");
					DishVo dsfyhB = null;
					
					if ("1".equals(InitDataListener.ggcsMap.get("POS_MEMBALANCE_USEDIS"))) {
						cardMoney = BigDecimal.valueOf(ArithUtil.sub(actualmoney.doubleValue(), ArithUtil.div(pay_info_obj.optDouble("storepay"), 100, 2)));
						dsfyhB = firstPayMapper.getDsfyh("63");
					}
					if ("2".equals(InitDataListener.ggcsMap.get("POS_MEMBALANCE_USEDIS"))) {
						String acewillDiscount = InitDataListener.ggcsMap.get("ACEWILLDISCOUNT");
						if (null != acewillDiscount) {
							if (isNumeric(acewillDiscount) && Integer.parseInt(acewillDiscount) > 0 && Integer.parseInt(acewillDiscount) <=100) {
								dsfyhB = firstPayMapper.getDsfyh("73");
								double scale = ArithUtil.div(Double.parseDouble(acewillDiscount), 100);
								double cardMoneyD = ArithUtil.sub(actualmoney.doubleValue(), ArithUtil.mul(actualmoney.doubleValue(), scale));
								cardMoney = new BigDecimal(cardMoneyD).setScale(2, RoundingMode.HALF_UP);
								
							} else {
								LOGGER.error("微生活储值未拆分:拆分比例=" + acewillDiscount);
							}
						} else {
							LOGGER.error("微生活储值未拆分:未设置拆分比例");
						}
					}
					
//					if (null != dsfyhB && !actualmoney.equals(cardMoney)) {
					if (null != dsfyhB && cardMoney.doubleValue() != 0) {
						// 实际付款金额使用储值金额替换
						firstPayMapper.insertBtYddPayActive(tsyddbh, pay_info_obj.optString("source", "微生活储值卡"), cardMoney.doubleValue(), orderInfo.getJSONObject("member").optString("cno", "会员编号"), "GIFT", 0);
						actualmoney = new BigDecimal(ArithUtil.sub(actualmoney.doubleValue(), cardMoney.doubleValue()));
					}
				}
				if ("credit".equals(source)) {
					if ("1".equals(InitDataListener.ggcsMap.get("POS_MEMCREDIT_USEDIS"))) {
						DishVo dsfyhC = firstPayMapper.getDsfyh("67");
						if (null != dsfyhC) {
							firstPayMapper.insertBtYddPayActive(tsyddbh, pay_info_obj.optString("source", "微生活积分消费"), actualmoney.doubleValue(), orderInfo.getJSONObject("member").optString("cno", "会员编号"), "CREDIT", 0);
							actualmoney = new BigDecimal("0.00");
						}
					}
				}
				// 调用付款存储过程，生成账单付款记录
				fkfsidList.add(fkfs.getId());
				actualmoneyList.add(actualmoney);
//				AccountsOrder ao = firstPayMapper.accountsOrder(zdk.getKdzdbh(), fkfs.getId(), actualmoney, 1, "", "", "", sfkbz, "99", jtzt.getRybh());
//				if (ao != null && "0".equals(ao.getA()) && aoflag) {
//					// 付款成功
//					aoflag = true;
//				} else {
//					// 付款失败
//					aoflag = false;
//					break;
//				}
				// 生成付款记录
				BtPayments btpay = new BtPayments();
				btpay.setYddh(tsyddbh);
				btpay.setPay_channel("微生活小程序点餐");
				btpay.setPay_name(fkfs.getFkfsmc1());
				btpay.setPay_no(fkfs.getFkfsbh());
				// 当付款方式为微生活优惠券时，此字段用作菜品券标志 0是菜品券 ""是代金券
				btpay.setVcardid(sfkbz);
				btpay.setVtele("");
				btpay.setPay_count(actualmoney.doubleValue());
				btpay.setPay_bb(0);
				btpay.setVocount(0);
				btpay.setFzzhje(0);
				btpay.setFlhl(0);
				btpay.setPay_memo("");
				btpay.setOpttime(payData.optString("addtime"));
				// 生成预定付款记录
				firstPayMapper.insertBtPayments(btpay);
				isPayRecord = true;
			}
			int activeResult = firstPayMapper.calcYddPayActive(tsyddbh);
			if (activeResult != 0) {
				data.setMsg("处理微生活优惠活动失败！");
				return data;
			}
			
			firstPayMapper.calcmoney(zdk.getKdzdbh());
			
			if (fkfsidList.size() == actualmoneyList.size()) {
				for (int i = 0; i < fkfsidList.size(); i++) {
					AccountsOrder ao = firstPayMapper.accountsOrder(zdk.getKdzdbh(), fkfsidList.get(i), actualmoneyList.get(i), 1, "", "", "", "", "99", jtzt.getRybh());
					if (ao != null && "0".equals(ao.getA()) && aoflag) {
						// 付款成功
						aoflag = true;
					} else {
						// 付款失败
						aoflag = false;
						break;
					}
				}
			}
			// 检查是否付款失败
			if (!aoflag) {
				// 付款失败
				JSONObject datajob = new JSONObject();
				datajob.put("identify", yddbh);
				datajob.put("meal_number", "0");
				datajob.put("ordermode", 2);
				datajob.put("tableorno", tableorno);
				data.setMsg("处理付款数据失败！");
				data.setData(datajob);
				return data;
			}

			// **************************************************
			// 第七步：处理微生活优惠券产生的优惠金额，现在只记成优惠，不再记成付款，********
			// **************************************************
			// 微生活会员价处理也放在这个过程中处理
			// if (isExistAceCoupon) {
//			if (firstPayMapper.calcYddPayActive(tsyddbh) != 0) {
//				data.setMsg("处理微生活优惠活动失败！");
//				return data;
//			}
			// }

			// 20190627，杨文彦，如果没有付款记录需要生成一条零金额的小程序付款记录
			if (!isPayRecord) {
				TsFkfssdk fkfs = firstPayMapper.getFkfsByCode("2020");
				// 生成付款记录
				BtPayments btpay = new BtPayments();
				btpay.setYddh(tsyddbh);
				btpay.setPay_channel("微生活小程序点餐");
				btpay.setPay_name(fkfs.getFkfsmc1());
				btpay.setPay_no(fkfs.getFkfsbh());
				btpay.setVcardid("");
				btpay.setVtele("");
				btpay.setPay_count(0.000);
				btpay.setPay_bb(0);
				btpay.setVocount(0);
				btpay.setFzzhje(0);
				btpay.setFlhl(0);
				btpay.setPay_memo("");
				btpay.setOpttime(DateUtil.getNowDateYYDDMMHHMMSS());
				// 生成预定付款记录
				firstPayMapper.insertBtPayments(btpay);
				AccountsOrder ao = firstPayMapper.accountsOrder(zdk.getKdzdbh(), fkfs.getId(), new BigDecimal("0.00"), 1, "", "", "", "", "99", jtzt.getRybh());
				if (ao != null && "0".equals(ao.getA()) && aoflag) {
					// 付款成功
					aoflag = true;
				} else {
					// 付款失败
					aoflag = false;
				}
			}
			
			// 检查是否付款失败
			if (!aoflag) {
				// 付款失败
				JSONObject datajob = new JSONObject();
				datajob.put("identify", yddbh);
				datajob.put("meal_number", "0");
				datajob.put("ordermode", 2);
				datajob.put("tableorno", tableorno);
				data.setMsg("处理付款数据失败！");
				data.setData(datajob);
				return data;
			}

			// **************************************************
			// 第八步：计算账单金额
			// **************************************************
			// 重新计算金额、生成取餐号、结账账单编号、班次、加价菜RWID
			firstPayMapper.calcmoney(zdk.getKdzdbh());

			String qch = "0";
			String kdzdbh = zdk.getKdzdbh();
			String jzzdbh = zdk.getJzzdbh();
			Date jzjssj = new Date();
			// 生成取餐号
			if(null == billNoData){
				qch = createBh("TQ_ZDK", "QCH");
			} else {
				qch = billNoData.getQch();
			}
			
			// 拆分不记收入
			if ("1".equals(InitDataListener.ggcsMap.get("POS_PAYWITHOUT_TODIS"))) {
				List<TqFklslsk> fklslskList = firstPayMapper.getFklslsk(kdzdbh);
				commUseYhfs(zdk, fklslskList, "99", bbrq);
			}
			
			if ("-1".equals(qch)) {
				// 此判断是为了兼容老版本zdk没有qch字段问题
				qch = zdk.getLsdh();
				firstPayMapper.updateZdkNoQch(kdzdbh, ksjzsj, jzjssj, "XCX", "ZDSX_YJ", tablesno, bcid, "1");
			} else {
				firstPayMapper.updateZdk(kdzdbh, ksjzsj, jzjssj, "XCX", qch, "ZDSX_YJ", tablesno, bcid, "1");
			}
			// 更新付款流水，增加结账账单编号、班次
			firstPayMapper.updateFklslsk(kdzdbh, jzzdbh, bcid);
			// 更新预定账单状态
			String zdbh = firstPayMapper.getKdzdbh(tsyddbh);
			firstPayMapper.updateBtYdd(tsyddbh, zdbh, "5", DateUtil.getNowDateYYDDMMHHMMSS(), qch, tablesno);
			// 处理加价菜品RWID，使用RWID更新。
			firstPayMapper.updateJjcrwid(zdbh, bcid);
			
			// 关联加价菜
			List<BtYdxm2> jjcList = firstPayMapper.getJjcidList(tsyddbh);
			for (BtYdxm2 jjc : jjcList) {
				firstPayMapper.updateYdJjcrwid(kdzdbh, jjc.getYdxm2id(), jjc.getJjcydxm2id());
			}
						
			JSONObject datajob = new JSONObject();
			datajob.put("identify", yddbh);
			datajob.put("meal_number", qch);
			datajob.put("ordermode", 2);
			datajob.put("tableorno", tableorno);
			datajob.put("pos_order_id", kdzdbh);
			data.setSuccess(1);
			data.setMsg("付款成功");
			data.setData(datajob);

			List<TqWdkCouponTemp> twctList = new ArrayList<TqWdkCouponTemp>();
			for (int i = 0; i < couponDenoInfoS.size(); i++) {
				TqWdkCouponTemp twct = new TqWdkCouponTemp();
				twct.setZdbh(zdbh);
				twct.setDatatype("ACEWILL");
				twct.setUseok(1);
				twct.setCoupontype("CASHCOUPON");
				twct.setCouponcode(couponDenoInfoS.getString(i));
				twct.setCoupondishcode("");
				twct.setIs_usable(0);
				twct.setUseablemsg("OK");
				twct.setCoupon_salemoney(ArithUtil.div(saledenoMoneyS, couponDenoInfoS.size(), 2));
				twct.setCoupon_totalmoney(ArithUtil.div(denoAmountS, couponDenoInfoS.size(), 2));
				twctList.add(twct);
			}
			for (int i = 0; i < couponDishsInfoS.size(); i++) {
				TqWdkCouponTemp twct = new TqWdkCouponTemp();
				twct.setZdbh(zdbh);
				twct.setDatatype("ACEWILL");
				twct.setUseok(1);
				twct.setCoupontype("DISHCOUPON");
				twct.setCouponcode(couponDishsInfoS.getString(i));
				twct.setIs_usable(0);
				twct.setUseablemsg("OK");
				twct.setCoupon_salemoney(ArithUtil.div(saleDishMoneyS, couponDishsInfoS.size(), 2));
				twct.setCoupon_totalmoney(ArithUtil.div(dishsAmountS, couponDishsInfoS.size(), 2));
				twctList.add(twct);
			}
			if (twctList.size() > 0) {
				firstPayMapper.insertTqWdkCouponTemp(twctList);
			}
			
			orderPrecheckMapper.updateSaleoutStep(tsyddbh, 0);

			return data;
		} catch (Exception e) {
			BillList.remove(tsyddbh);
			LOGGER.info("预订单转订单异常，平台订单编号：" + yddbh);
			LOGGER.error("Ignore this exception", e);
			data.setMsg("系统错误！");
			data.setData(new HashMap<String, Object>());
			return data;
		}
	}

	@Transactional
	public void sendPrint(JSONObject payData, String ptype) {
		try {
			String isPrint = "1";
			TsGgcsk ggcsp = firstPayMapper.getGgcsToWs("POSISPRINTXCXBILL");
			if (null != ggcsp && !"".equals(ggcsp.getSdnr())) {
				isPrint = ggcsp.getSdnr();
			}
			if ("0".equals(isPrint)) {
				Map<String, String> bbrqMap = shopStatusMapper.findBbrq();
				String jzbbrq = DateUtil.getNowDateYYDDMM();
				if (null != bbrqMap && bbrqMap.size() != 0) {
					jzbbrq = bbrqMap.get("bbrq");
				}
				String yddbh = Constant.BILL_PREFIX + payData.optString("out_order_id");
				//10158
				int printId = 70120;
				switch (ptype) {
				case "jzd":
					printId = 70120;
					break;
				case "zdqx":
					yddbh = payData.optString("out_order_id");
					printId = 70121;
					break;
				}
				
				LOGGER.info("发送打印信息,预订单编号：" + yddbh);
				String printStr = "桌位名称='';预定单号='" + yddbh + "';操作员='99';报表日期='" + jzbbrq + "';查询类型='1';渠道='XCX'";
				
				PosPrintJna print = new PosPrintJna();
				print.tzxReportlib(printId, printStr, "99");
				LOGGER.info("结帐单打印完成" + yddbh);
			} else {
				LOGGER.info("小程序打印已关闭；isPrint：" + isPrint);
			}

		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
		}
	}

	@Transactional
	public void sendPrintByYddh(String yddbh) {
		try {
			String isPrint = "1";
			TsGgcsk ggcsp = firstPayMapper.getGgcsToWs("POSISPRINTXCXBILL");
			if (null != ggcsp && !"".equals(ggcsp.getSdnr())) {
				isPrint = ggcsp.getSdnr();
			}
			if ("0".equals(isPrint)) {
				LOGGER.info("发送打印信息,预订单编号：" + yddbh);
				Shops shops = shopBaseInfoMapper.findShopsData();
				DatagramUtil.sendUdpMsg("ACTION=TZXYDDPRINT|FDJGXH=" + shops.getSid() + "|YDDH=" + yddbh, Integer.valueOf("8007").intValue(),
						PropertiesUtil.readValueForClasses("/application.properties", "xcxprintip"));
			} else {
				LOGGER.info("小程序打印已关闭；isPrint：" + isPrint);
			}

		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
		}
	}

	/**
	 * 
	 * 订单完成后，账单信息送KVS数据表，并发送通知
	 * 
	 * @param payData
	 */
	@Transactional
	public void sendKVS(JSONObject payData) {
		try {
//			String isSendKVS = PropertiesUtil.readValueForClasses("/application.properties", "isSendKVS");
			
//			if (StringUtil.getBoolean(isSendKVS, false)) {
			if ("Y".equals(InitDataListener.ggcsMap.get("SFQYKVS"))) {
				String yddbh = Constant.BILL_PREFIX + payData.optString("out_order_id");
				LOGGER.info("向KVS数据表中写入数据,预订单编号：" + yddbh);
				TqZdk zdk = firstPayMapper.getZdk(yddbh);
				String kvs = firstPayMapper.callP_SendKVSData(zdk.getKdzdbh());
				if (!kvs.equals("0")) {
					LOGGER.info("插入kvs表成功:" + kvs);
				} else {
					LOGGER.info("插入kvs表成功:" + kvs);
				}
				LOGGER.info("发送kvs信息,预订单编号：" + yddbh);
				Shops shops = shopBaseInfoMapper.findShopsData();
				DatagramUtil.sendUdpMsg("SRC=KDS|ACTION=REFRESHKDSKVS|FDJGXH=" + shops.getSid() + "|GUID=" +String.valueOf(System.currentTimeMillis()), Integer.valueOf("7999").intValue(), PropertiesUtil.readValueForClasses("/application.properties", "kvsip"));
				DatagramUtil.sendUdpMsg("SRC=KVS|ACTION=REFRESHKDSKVS|FDJGXH=" + shops.getSid() + "|GUID=" +String.valueOf(System.currentTimeMillis()), Integer.valueOf("7999").intValue(), PropertiesUtil.readValueForClasses("/application.properties", "kvsip"));

			} else {
				LOGGER.info("小程序未使用KVS；isSendKVS：" + InitDataListener.ggcsMap.get("SFQYKVS"));
			}

		} catch (Exception e) {
			LOGGER.error("送KVS系统错误！", e);
		}
	}

	/**
	 * 
	 * 微生活退款接口
	 * 
	 * @param data
	 * @param yddbh
	 */
	public void welcrmApiRefund(Data data, String yddbh) {
		String propertiesPath = "/application.properties";
		String retundApi = "/api/Order/refund";
        String appKey=DBUtils.getGGCSK("WELCRM_APPKEY");
        if(StringUtils.isEmpty(appKey)){
            appKey = PropertiesUtil.readValueForClasses(propertiesPath, "welcrm_appkey");
        }

        if (StringUtils.isEmpty(appKey)) {
            data.setMsg("应用密钥未配置！退款失败！");
            return;
        }

        TsPsjgsdk jg = tsPsjgsdkMapper.findLocalShopConfig();
        String shopkey = jg == null ? null : jg.getJydd();

        if (StringUtils.isEmpty(shopkey)) {
            data.setMsg("门店密码未配置！退款失败！");
            return;
        }


        String welcrm_api=DBUtils.getGGCSK("WELCRM_API");
        if(StringUtils.isEmpty(welcrm_api)){
            welcrm_api = PropertiesUtil.readValueForClasses(propertiesPath, "welcrm_api_refund");
        }
        if (StringUtils.isEmpty(welcrm_api)) {
            data.setMsg("微生活退款接口地址未配置！退款失败！");
            return ;
        }

        String apiRefund = welcrm_api+retundApi;
        LOGGER.info(String.format("应用密钥：%s 门店密码: %s 退款接口地址: %s", appKey, shopkey, apiRefund));
        String shopUUID=InitDataListener.organVo.getUuid();
		String sign = Util.miniappSign(appKey, yddbh);
		String result = Util.refund(apiRefund, shopkey, shopUUID,yddbh, sign);
		if (Const.CONNECT_ERROR.equals(result)) {
			data.setMsg("连接错误，退款失败！");
		} else {
			LOGGER.info("微生活自动退款返回：" + result);
			JSONObject resultJson = JSONObject.fromObject(result);
			int code = resultJson.getInt("errcode");
			if (code == 0) {
				data.setMsg("结账失败，已退款！");
			} else {
				data.setMsg("结账失败，自动退款失败！");
			}
		}
	}

	public String createBh(String bmc, String zdmc) {
		TsBmkzk tsBmkzKdzdbh = firstPayMapper.getBh(bmc, zdmc);
		if (null == tsBmkzKdzdbh) {
			return "-1";
		}
		String oldBh = tsBmkzKdzdbh.getNr();
		TsGgcsk ggcsQch = firstPayMapper.getGgcsToWs("QCHCSH");
		if (oldBh.equals(ggcsQch.getSdnr())) {
			oldBh = "0";
		}
		String newBh = (Long.parseLong(oldBh) + 1) + "";

		int bhLength = 3;
		if (null != tsBmkzKdzdbh.getCdzd() && !"".equals(tsBmkzKdzdbh.getCdzd())) {
			TsGgcsk ggcs = firstPayMapper.getGgcsToWs(tsBmkzKdzdbh.getCdzd());
			if (null != ggcs.getSdnr() && !"".equals(ggcs.getSdnr())) {
				bhLength = Integer.parseInt(ggcs.getSdnr());
			}
		}

		NumberFormat nf = NumberFormat.getInstance();
		nf.setGroupingUsed(false);
		nf.setMaximumIntegerDigits(bhLength);
		nf.setMinimumIntegerDigits(bhLength);
		newBh = nf.format(Integer.parseInt(newBh));

		firstPayMapper.updateBh(newBh, bmc, zdmc);
		return newBh;
	}

	/**
	 * 
	 * 订单完成后，送厨打
	 * 
	 * @param json
	 */
	@Transactional
	public void sendPrintKichen(JSONObject json) {
		try {
			String isKichenPrint = "N";
			TsGgcsk ggcs = firstPayMapper.getGgcsToWs("SFSCD");
			if (null != ggcs.getSdnr() && !"".equals(ggcs.getSdnr())) {
				isKichenPrint = ggcs.getSdnr();
			}
			if ("Y".equals(isKichenPrint)) {

				String yddbh = Constant.BILL_PREFIX + json.optString("out_order_id");
				String szdbh = firstPayMapper.getZdbhByYddh(yddbh);
				LOGGER.info("开启厨打,预订单编号：" + yddbh + ",订单编号：" + szdbh);

				String printKichenStr = "账单号='" + szdbh + "';渠道='XCX'";
				PosPrintKichenJna printKichen = new PosPrintKichenJna();
				printKichen.tzxReportlib("99", printKichenStr);

				LOGGER.info("厨打完成" + yddbh);
			} else {
				LOGGER.info("小程序未开启厨打；");
			}
		} catch (Exception e) {
			LOGGER.error("送厨打系统错误！", e);
		}
	}
	
	/**
	 * 
	 * 订单完成后，送打清单
	 * 
	 * @param json
	 */
	@Transactional
	public void sendPrintDetailed(JSONObject json) {
		try {
			String isDetailedPrint = "N";
			TsGgcsk ggcs = firstPayMapper.getGgcsToWs("JZSFDYQD");
			if (null != ggcs && null != ggcs.getSdnr() && !"".equals(ggcs.getSdnr())) {
				isDetailedPrint = ggcs.getSdnr();
			}
			if ("Y".equals(isDetailedPrint)) {

				String yddbh = Constant.BILL_PREFIX + json.optString("out_order_id");
				String szdbh = firstPayMapper.getZdbhByYddh(yddbh);
				LOGGER.info("开启清单打印,预订单编号：" + yddbh + ",订单编号：" + szdbh);

				String printStr = "账单编号='" + szdbh + "';渠道='XCX'";
				int printId = 10158;
				
				PosPrintJna print = new PosPrintJna();
				print.tzxReportlib(printId, printStr, "99");

				LOGGER.info("清单打印完成" + yddbh);
			} else {
				LOGGER.info("未开启打印清单；");
			}
		} catch (Exception e) {
			LOGGER.error("打印清单系统错误！", e);
		}
	}
	

	@Transactional
	public void exceptionCancelBill(Data data) {
		String tsyddbh = data.getYddbh();
		String yddh = tsyddbh.replaceFirst("TS", "");
		cancelBillMapper.cancelBill(tsyddbh, DateUtil.parseDate(data.getBbrq()), "99", 0, data.getRybh());
		firstPayMapper.updateBtYddToCancel(tsyddbh, "7");
		// 订单转账单失败调用微生活的退款接口
		// isWelcrm 是否微生活对接
		String isUseWelcrmStr = PropertiesUtil.readValueForClasses("/application.properties", "isUseWelcrm");
		if (StringUtil.getBoolean(isUseWelcrmStr, false)) {
			welcrmApiRefund(data, yddh);
		}
	}

	/**
	 * 下单前检查门店状态
	 * 
	 * @return
	 */
	public Data shopStatus() {
		Data data = new Data();

		Map<String, String> bbrqMap = shopStatusMapper.findBbrq();
		String bbrq = DateUtil.getNowDateYYDDMM();
		if (null != bbrqMap && bbrqMap.size() != 0) {
			bbrq = bbrqMap.get("bbrq");
		}

		int start = shopStatusMapper.checkOpenStart(DateUtil.parseDate(bbrq));
		int end = shopStatusMapper.checkOpenEnd(DateUtil.parseDate(bbrq));
		int ld = shopStatusMapper.loginDlcs(DateUtil.parseDate(bbrq));
		int lt = shopStatusMapper.loginTccs(DateUtil.parseDate(bbrq));
		TsGgcsk il = firstPayMapper.getGgcsToWs("ISLOGINOUTING");
		if (null != il && "Y".equals(il.getSdnr())) {
			data.setSuccess(0);
			data.setMsg("门店正在盘点，请稍后再试");
		} else if (0 == start) {
			data.setSuccess(0);
			data.setMsg("门店未营业");
		} else if (0 < end) {
			data.setSuccess(0);
			data.setMsg("门店已打烊");
		} else if (ld <= lt) {
			data.setSuccess(0);
			data.setMsg("员工未登录");
		} else if (checkDysj(bbrq) > 0) {
			data.setSuccess(0);
			data.setMsg("系统日期已经大于营业日期，请做打烊后再进行当前操作!");
		} else {
			data.setSuccess(1);
			data.setMsg("正常营业");
		}
		return data;
	}

	/**
	 * 下单前检查是否打烊
	 * 
	 * @param bbrq
	 * @return
	 */
	public int checkDysj(String bbrq) {
		String sys24yy = shopStatusMapper.getSys24yy();
		String dysjsz = shopStatusMapper.getDyxzsjd();
		Date bbrqD = DateUtil.parseDateAll(bbrq + " 00:00:00");
		if ("Y".equals(sys24yy)) {
			int day = DateUtil.daysBetween(bbrqD, DateUtil.parseDateAll(DateUtil.getNowDateYYDDMM() + " 00:00:00"));
			return day;
		} else {
			if ("".equals(dysjsz) || null == dysjsz) {
				dysjsz = "00:00:00";
			}
			Date bbrqDy = DateUtil.parseDateAll(bbrq + " " + dysjsz);
			Date dysj = DateUtil.parseDateAll(DateUtil.getNowDateYYDDMMHHMMSS());
			int day = DateUtil.daysBetween(DateUtil.getPlusDay(bbrqDy, 1), dysj);
			return day;
		}
	}
	
	public boolean isNumeric(String str) {
		for (int i = str.length(); --i >= 0;) {
			if (!Character.isDigit(str.charAt(i))) {
				return false;
			}
		}
		return true;
	}
	
	public void commUseYhfs(TqZdk zdk, List<TqFklslsk> fklslskList, String skjh, String jzbbrq) {
		for (int i = 0; i < fklslskList.size(); i++) {
			TqFklslsk fklslsk = fklslskList.get(i);
			if ("N".equals(fklslsk.getSfsr())) {
				TsYhfssdk yhfssdk = firstPayMapper.getBjyhYh("BJSR" + fklslsk.getFkfsbh());
				if (null != yhfssdk) {
					UseYhfsParam useYhfsParam = new UseYhfsParam();
					useYhfsParam.setBillid(zdk.getKdzdbh());
					useYhfsParam.setOpType(91003);
					useYhfsParam.setYhfsId(yhfssdk.getId());
					useYhfsParam.setFklsid(fklslsk.getId());
					useYhfsParam.setSkjh(skjh);// 机号
					useYhfsParam.setJgtxbh(InitDataListener.organVo.getJgtxbh());// 价格体系编号
					useYhfsParam.setDisAmount(new BigDecimal(fklslsk.getFkje()));
					useYhfsParam.setBbrq(DateUtil.parseDate(jzbbrq));
					if ((i + 1) == fklslskList.size() && 0 != zdk.getDslj()) {
						useYhfsParam.setDslj(new BigDecimal(zdk.getDslj()));
						useYhfsParam.setHasDslj(true);
					}
					useYhfsParam.setInnerCalMoney(true);// 由接口内部使用均摊及账单金额重计算

					useYhfsApiService.CommUseYhfs(useYhfsParam);
				}
			}
		}
	}

	@Transactional
	public Data orderPrecheckBefore(JSONObject orderData) {
		// 创建返回数据对象
		Data data = new Data();
		data.setSuccess(1);
		data.setMsg("验证通过！");
		JSONObject orderInfo = orderData.optJSONObject("order_info");
		JSONObject payInfos = orderData.optJSONObject("pay_info");

		try {
			double orderAmount = orderInfo.optDouble("total", 0); // 账单金额  orderAmount = realAmount + ordeDisAmount
			double realAmount = orderInfo.optDouble("cost", 0); // 实结金额payAmount
			double ordeDisAmount = 0d; // 优惠金额
			double boxTotal= ArithUtil.div(orderInfo.optDouble("box_price",0),100); //餐盒费
			List<JSONObject> boxItems=new ArrayList<>();//餐盒菜品

			double payAmount = 0;
			double dishRealAmount = 0;

			double promotionCouponsAmount=0d;

			JSONArray payInfoArr = payInfos.optJSONArray("pay_info");
			JSONArray promotions = orderInfo.optJSONArray("promotions");
			JSONArray discounts = orderInfo.optJSONArray("discounts");

			String diningWay = orderInfo.optString("diningWay","1"); //1为堂食,2为外带

			if(CollectionUtils.isNotEmpty(promotions)){
				for(int i = 0; i < promotions.size(); i++){
					ordeDisAmount = ArithUtil.add(ordeDisAmount, promotions.getJSONObject(i).optDouble("money",0d));
				}
			}

			if(CollectionUtils.isNotEmpty(discounts)){
				for(int i = 0; i < discounts.size(); i++){
					ordeDisAmount = ArithUtil.add(ordeDisAmount, discounts.getJSONObject(i).optDouble("money",0d));
				}
			}

            /*if("1".equals(orderInfo.optString("isUsePromotion"))&&CollectionUtils.isNotEmpty(promotionCoupons)){
                for(int i=0;i<promotionCoupons.size();i++){
                    promotionCouponsAmount = ArithUtil.add(promotionCouponsAmount,
                            ArithUtil.sub(promotionCoupons.getJSONObject(i).optDouble("promotion_discount_amount",0d), promotionCoupons.getJSONObject(i).optDouble("promotion_sale_money",0d)));
                }
                promotionCouponsAmount=ArithUtil.div(promotionCouponsAmount,100);
                ordeDisAmount=ArithUtil.add(ordeDisAmount,promotionCouponsAmount);
                realAmount=ArithUtil.sub(realAmount,promotionCouponsAmount);
            }*/

			String welifePayInfoStr=orderData.optString("welife_pay_info");
			if(org.apache.commons.lang3.StringUtils.isNotEmpty(welifePayInfoStr)){
				try {
					JSONObject welifePayInfo = JSONObject.fromObject(welifePayInfoStr);
					promotionCouponsAmount = ArithUtil.sub(0,ArithUtil.div(welifePayInfo.optDouble("coupon_sale_money",0d),100));
					ordeDisAmount = ArithUtil.add(ordeDisAmount,promotionCouponsAmount);
					realAmount = ArithUtil.sub(realAmount,promotionCouponsAmount);
				}catch (JSONException e){
					LOGGER.error("welife_pay_info不是有效的JSON",welifePayInfoStr);
				}

			}

			for (int i = 0; i < payInfoArr.size(); i++) {
				JSONObject payInfo = payInfoArr.getJSONObject(i);
				double pAmount = payInfo.optDouble("amount");
				if(pAmount>realAmount&&"source".equals(payInfo.optString("coupon"))) {
					payAmount=realAmount;
					payInfo.put("amount",payAmount);
				}else {
					payAmount = ArithUtil.add(payAmount, pAmount);
				}
			}
//            for (int i = 0; i < payInfoArr.size(); i++) {
//                JSONObject payInfo = payInfoArr.getJSONObject(i);
//                double pAmount = payInfo.optDouble("amount");
//                if("coupon".equals(payInfo.optString("source")) || "product".equals(payInfo.optString("source"))) {
//                    double saleDishMoney = payInfo.optDouble("sale_dish_money");
//                    double dishMoney = ArithUtil.sub(pAmount, saleDishMoney);
//                    payAmount = ArithUtil.add(payAmount, saleDishMoney);
//                    ordeDisAmount = ArithUtil.add(ordeDisAmount, dishMoney);
//                    realAmount = ArithUtil.sub(realAmount, dishMoney);
//                }else {
//                    payAmount = ArithUtil.add(payAmount, pAmount);
//                }
//            }

			orderInfo.put("order_amount", orderAmount); // 账单金额
			orderInfo.put("real_amount", realAmount); // 实结金额
			orderInfo.put("orde_dis_amount", ordeDisAmount); // 优惠金额

			if (orderAmount != ArithUtil.add(realAmount, ordeDisAmount)) {
				data.setSuccess(0);
				data.setMsg("“账单金额”不等于“实结金额”+“优惠金额”，请联系管理员！");
				data.setData(new HashMap<String, Object>());
				return data;
			}

			if (payAmount < realAmount) {
				data.setSuccess(0);
				data.setMsg("“支付金额”小于“应付金额”，请联系管理员！");
				data.setData(new HashMap<String, Object>());
				return data;
			}

			if (orderInfo.has("normalitems")) {
				JSONArray normalitems = new JSONArray();
				normalitems = orderInfo.getJSONArray("normalitems");
				for (int i = 0; i < normalitems.size(); i++) {
					JSONObject item = normalitems.getJSONObject(i);
					TsCmk cmk = orderPrecheckMapper.getDishByCode(getItemCodeBySpeccode(item));
					if (null == cmk) {
						data.setSuccess(0);
						data.setMsg("菜品“" + item.optString("name") + "”不存在，请联系管理员！");
						data.setData(new HashMap<String, Object>());
						return data;
					}
					if (!"CMSX_DP".equals(cmk.getCmsx())) {


						data.setSuccess(0);
						data.setMsg("菜品“" + item.optString("name") + "”不是单品菜品，请联系管理员！");
						data.setData(new HashMap<String, Object>());
						return data;
					}

					item.put("foodboxset",cmk.getFoodboxset());
					Map<String, String> resultMap = new HashMap<String, String>();
					JSONObject boxItem = setItemRealPrice(resultMap, item, diningWay);
					if("-1".equals(resultMap.get("code"))){
						data.setSuccess(0);
						data.setMsg("菜品“" + item.optString("name") + "”未绑定餐盒或无餐盒数据，请联系管理员！");
						data.setData(new HashMap<String, Object>());
						return data;
					}
					if(boxItem.size()>0){
						boxItems.add(boxItem);
					}
					dishRealAmount = ArithUtil.add(dishRealAmount, item.optDouble("realprice", 0));
				}
			}

			if (orderInfo.has("setmeal")) {
				JSONArray setmeal = new JSONArray();
				setmeal = orderInfo.getJSONArray("setmeal");
				for (int i = 0; i < setmeal.size(); i++) {
					JSONObject item = setmeal.getJSONObject(i);
					TsCmk cmk = orderPrecheckMapper.getDishByCode(getItemCodeBySpeccode(item));
					if (null == cmk) {
						data.setSuccess(0);
						data.setMsg("菜品“" + item.optString("name") + "”不存在，请联系管理员！");
						data.setData(new HashMap<String, Object>());
						return data;
					}
					if (!"CMSX_TC".equals(cmk.getCmsx())) {
						data.setSuccess(0);
						data.setMsg("菜品“" + item.optString("name") + "”不是套餐菜品，请联系管理员！");
						data.setData(new HashMap<String, Object>());
						return data;
					}

					item.put("foodboxset",cmk.getFoodboxset());
					Map<String, String> resultMap = new HashMap<String, String>();
					JSONObject boxItem = setItemRealPrice(resultMap, item,diningWay);
					if("-1".equals(resultMap.get("code"))){
						data.setSuccess(0);
						data.setMsg("菜品“" + item.optString("name") + "”未绑定餐盒或无餐盒数据，请联系管理员！");
						data.setData(new HashMap<String, Object>());
						return data;
					}
					if(boxItem.size()>0){
						boxItems.add(boxItem);
					}

					double setmealRealprice = item.optDouble("realprice", 0); //套餐主项实收
					double mmRealprice = 0; //明细实收之和
					JSONObject maxPriceItem=new JSONObject(); //最大价格单品

					if (item.has("maindish")) {
						JSONArray mainDish = new JSONArray();
						mainDish = item.optJSONArray("maindish");
						for (int j = 0; j < mainDish.size(); j++) {
							JSONObject md = mainDish.getJSONObject(j);
							Map<String, String> resultMap1 = new HashMap<String, String>();
							setItemRealPrice(resultMap1, md, diningWay);
							mmRealprice = ArithUtil.add(mmRealprice, md.optDouble("realprice", 0));
							if(maxPriceItem.optDouble("realprice",0d)<md.optDouble("realprice", 0)){
								maxPriceItem=md;
							}
						}
					}

					if (item.has("mandatory")) {
						JSONArray mandatory = new JSONArray();
						mandatory = item.optJSONArray("mandatory");
						for (int j = 0; j < mandatory.size(); j++) {
							JSONObject md = mandatory.getJSONObject(j);
							Map<String, String> resultMap2 = new HashMap<String, String>();
							setItemRealPrice(resultMap2, md, diningWay);
							mmRealprice = ArithUtil.add(mmRealprice, md.optDouble("realprice", 0));
							if(maxPriceItem.optDouble("realprice",0d)<md.optDouble("realprice", 0)){
								maxPriceItem=md;
							}
						}
					}

					if (item.has("optional")) {
						JSONArray optional =item.optJSONArray("optional");
						for (int j = 0; j < optional.size(); j++) {
							JSONObject md = optional.getJSONObject(j);
							Map<String, String> resultMap3 = new HashMap<String, String>();
							setItemRealPrice(resultMap3,md,diningWay);
							mmRealprice = ArithUtil.add(mmRealprice, md.optDouble("realprice", 0));
							if(maxPriceItem.optDouble("realprice",0d)<md.optDouble("realprice", 0)){
								maxPriceItem=md;
							}
						}
					}

						/*if (setmealRealprice != mmRealprice) {

						maxPriceItem.put("realprice",ArithUtil.sub(maxPriceItem.optDouble("realprice"),
								ArithUtil.sub(mmRealprice,setmealRealprice)));

						data.setSuccess(0);
						data.setMsg("套餐菜品“" + item.optString("name") + "”金额与明细金额不符，请联系管理员！");
						data.setData(new HashMap<String, Object>());
						return data;
					}
					*/
					dishRealAmount = ArithUtil.add(dishRealAmount, setmealRealprice);
				}
			}

			if(CollectionUtils.isNotEmpty(boxItems)){
				JSONArray normalitems=null;
				if (orderInfo.has("normalitems")) {
					normalitems = orderInfo.getJSONArray("normalitems");
					normalitems.addAll(boxItems);
				}else {
					normalitems=new JSONArray();
					normalitems.add(boxItems);
					orderInfo.put("normalitems",normalitems);
				}
			}

			if (ArithUtil.add(dishRealAmount,boxTotal) != orderAmount) {
				data.setSuccess(0);
				data.setMsg("“账单金额”不等于“明细金额”，请联系管理员！");
				data.setData(new HashMap<String, Object>());
				return data;
			}

			return data;
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			data.setSuccess(0);
			data.setMsg("系统错误:" + e);
			data.setData(new HashMap<String, Object>());
			return data;
		}
	}

	/***
	 * 保存口味
	 */
	private JSONObject setItemRealPrice(Map<String, String> resultMap, JSONObject item,String diningWay){
		resultMap.put("code", "0");
		resultMap.put("msg", "");
		JSONObject boxItem = new JSONObject();
		//计算单品或套餐主项realprice = price*number+aprice


		Double realPrice = ArithUtil.add(item.optDouble("aprice",0d),
				ArithUtil.mul(item.optDouble("price",0d), item.optDouble("number",0d)));

		//口味加价
		realPrice = ArithUtil.add(realPrice, ArithUtil.div( item.optDouble("toppingTotal",0d),100d));

		//口味
		JSONArray toppings = item.optJSONArray("toppings");
		if(CollectionUtils.isNotEmpty(toppings)){
			String remark = "";
			for(int i = 0; i < toppings.size(); i++){
				JSONObject top=toppings.getJSONObject(i);
				double addprice = ArithUtil.div(top.optDouble("addprice", 0d),100d);
				remark+=" " + top.optString("tpname","") + "(+" +  addprice + "元) ";
			}
			item.put("remark",remark);
		}

		//餐盒费
		if("2".equals(diningWay)){
			LOGGER.info("外带处理餐盒");
			JSONObject norms= item.optJSONObject("norms");
			if(null!=norms&&!norms.isEmpty()){
				double boxNum = ArithUtil.mul(norms.optDouble("box_num", 0d),item.optDouble("number", 0d)); //菜品餐盒总数=规格餐盒数x菜品数量
				double boxPrice=ArithUtil.div(norms.optDouble("box_price",0d),100);
				double boxTotal=ArithUtil.mul(boxNum,boxPrice);
				if(0 < boxTotal){
					//查询餐盒信息
					TsCmk cmk = orderPrecheckMapper.getTsCmk(item.optInt("foodboxset",-1));
					if (null == cmk) {
						cmk = orderPrecheckMapper.getSendAmount("餐盒");
					}
					if (null != cmk) {
						resultMap.put("code", "0");
						resultMap.put("msg", "");
						boxItem.put("did",cmk.getDlid());
						boxItem.put("dishsno",cmk.getCmbh());
						boxItem.put("name",cmk.getCmmc1());
						boxItem.put("localName",cmk.getCmmc1());
						boxItem.put("price", boxPrice);
						boxItem.put("number",boxNum);
						boxItem.put("realprice", boxTotal);
						boxItem.put("dishunit", cmk.getDwbh());
					} else {
						resultMap.put("code", "-1");
						resultMap.put("msg", "菜品未绑定餐盒或无餐盒数据！");
						return boxItem;
					}
				}
//                realPrice = ArithUtil.add(realPrice,boxTotalPrice);
			}
		}
		item.put("realprice",realPrice);
		return boxItem;
	}

	private String getItemCodeBySpeccode(JSONObject item) {
		String dishno = item.optString("dishsno");

		String speccode = item.optString("speccode","");
		if(org.apache.commons.lang3.StringUtils.isEmpty(speccode)){
			JSONObject norms = item.optJSONObject("norms");
			if(null != norms && !norms.isEmpty()){
				speccode = norms.optString("speccode");
			}
		}

		LOGGER.info("dishsno=" + dishno+",speccode=" + speccode);

		if(org.apache.commons.lang3.StringUtils.isNotEmpty(speccode)  && !"null".equalsIgnoreCase(speccode)){
			return speccode;
		}
		return dishno;

	}


	
}
