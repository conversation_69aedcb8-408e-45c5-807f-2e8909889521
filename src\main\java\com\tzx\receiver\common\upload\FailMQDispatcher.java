package com.tzx.receiver.common.upload;

import org.apache.commons.lang.StringUtils;
import org.junit.experimental.categories.Categories;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2019-04-26
 * @Descption
 **/
@Component
public class FailMQDispatcher {
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private UpLoadTaskList upLoadTaskList;
    //网络发送失败是需要重新上传的命令类型
    private String repeatTrans = "";
    public void doFailMQ(){

        //判断一下是否已经打烊了，如果打烊了，也没必要传了
        String checkSql = "select 1 from tq_jtztk where jhid = '99' and bbrq = '" + UploadGloVar.getReportDate() + "" +
                "' and cznr = 'JSSY' ";
        SqlRowSet checkSqlRowSet = jdbcTemplate.queryForRowSet(checkSql);
        if(checkSqlRowSet.next()){
            return;
        }

        if(StringUtils.isEmpty(repeatTrans)){
            repeatTrans = "('NONE'";
            String sql1 =" SELECT mlmc from ts_scbblxk where sfcc = '1'  ";
            SqlRowSet sqlRowSet1 = jdbcTemplate.queryForRowSet(sql1);
            while (sqlRowSet1.next()){
                repeatTrans = repeatTrans + ",'" + sqlRowSet1.getString("mlmc") + "'";
            }
            repeatTrans = repeatTrans + ")";
        }

        String sql =" SELECT * from tq_uploadlog where runresult = '-1' and command in  "+ repeatTrans +
                " and ( sourcemsg LIKE '%BBRQ=" +  UploadGloVar.getReportDate() + "%'  or sourcemsg LIKE '%bbrq[:]" +  UploadGloVar.getReportDate() + "%)' ) ";
        SqlRowSet sqlRowSet = jdbcTemplate.queryForRowSet(sql);

        while (sqlRowSet.next()){
            String vMsg = sqlRowSet.getString("sourcemsg") ;
            vMsg = vMsg + "|ONLYSENDMQ=1|FILENAME=" + sqlRowSet.getString("uploadfilename")
              +"|GUID=" + sqlRowSet.getString("GUID");
            upLoadTaskList.addTask(vMsg);
        }

    }
}
