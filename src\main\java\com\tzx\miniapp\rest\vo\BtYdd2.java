package com.tzx.miniapp.rest.vo;

import javax.persistence.Entity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Entity
public class BtYdd2 implements Serializable {

	private String yddh;
	private String merchant_id;
	private String shops_id;
	private String hybh;
	private String lxr;
	private String lxrdh;
	private String mobile;
	private String ydrq;
	private String ydbc;
	private int ydrs;
	private int men;
	private int women;
	private int eldernum;
	private int childnum;
	private String qsbh;
	private String ddsj;
	private String blrq;
	private String blsj;
	private BigDecimal bzjfs;
	private String ddzt;
	private String zlbh;
	private int ydzs;
	private String jk;
	private String czybh;
	private String czymc;
	private String khjlbh;
	private String khjlmc;
	private String destinestate;
	private String recieptdept;
	private String paychannel;
	private String paystatus;
	private String hardtomove;
	private BigDecimal totalprice;
	private String scqy;
	private String csbz;
	private String before_order_source;
	private String before_order_type;
	private String horseman_id;
	private String member_address;
	private String needreciept;
	private String sfyd;
	private int parkingnum;
	private int zkfa;
	private String ydlx;
	private String kwxh;
	private String khtmtz;
	private String sfyyf;
	private String sfxyq;
	private int cancel_type;
	private String warning;
	private int shippingmethod;
	private String before_source;
	private String qdsj;
	private String yshsj;
	private String qdsj1;
	private String sysj;
	private String pdsj;
	private String wcsj;
	private String sbsqsj;
	private String xsqxsj;
	private String qdrbh;
	private String qdrmc;
	private String shrbh;
	private String shrmc;
	private String qdrbh1;
	private String qdrmc1;
	private String syrbh;
	private String syrmc;
	private String pdrbh;
	private String pdrmc;
	private String sbsqrbh;
	private String sbsqrmc;
	private String xsqxrbh;
	private String xsqxrmc;
	private BigDecimal ysje;
	private BigDecimal zlje;
	private String ynkpi;
	private String cancel_type_online;
	private String fail_type;
	private String horseman_name;
	private int scbj;
	private String bz;
	private BigDecimal yl1;
	private BigDecimal yl2;
	private BigDecimal yl3;
	private String yl4;
	private String yl5;
	private String ynkpi2;
	private String cancel_type_online2;
	private String fail_type2;
	private String is_update;
	private String is_handwork;
	private String handwork_yddh;
	private String invalid_desc;
	private String is_commission;
	private String bill_num;
	private Date report_date;
	private BigDecimal commission_amount;
	private BigDecimal shop_real_amount;
	private int refund_state;
	private String rider_phone;
	private String delivery_is_open;
	private BigDecimal platform_rate;
	private BigDecimal shop_rate;
	private BigDecimal package_box_fee;
	private String is_verification;
	private String taxpayerid;

	public String getYddh() {
		return yddh;
	}

	public void setYddh(String yddh) {
		this.yddh = yddh;
	}

	public String getMerchant_id() {
		return merchant_id;
	}

	public void setMerchant_id(String merchant_id) {
		this.merchant_id = merchant_id;
	}

	public String getShops_id() {
		return shops_id;
	}

	public void setShops_id(String shops_id) {
		this.shops_id = shops_id;
	}

	public String getHybh() {
		return hybh;
	}

	public void setHybh(String hybh) {
		this.hybh = hybh;
	}

	public String getLxr() {
		return lxr;
	}

	public void setLxr(String lxr) {
		this.lxr = lxr;
	}

	public String getLxrdh() {
		return lxrdh;
	}

	public void setLxrdh(String lxrdh) {
		this.lxrdh = lxrdh;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getYdrq() {
		return ydrq;
	}

	public void setYdrq(String ydrq) {
		this.ydrq = ydrq;
	}

	public String getYdbc() {
		return ydbc;
	}

	public void setYdbc(String ydbc) {
		this.ydbc = ydbc;
	}

	public int getYdrs() {
		return ydrs;
	}

	public void setYdrs(int ydrs) {
		this.ydrs = ydrs;
	}

	public int getMen() {
		return men;
	}

	public void setMen(int men) {
		this.men = men;
	}

	public int getWomen() {
		return women;
	}

	public void setWomen(int women) {
		this.women = women;
	}

	public int getEldernum() {
		return eldernum;
	}

	public void setEldernum(int eldernum) {
		this.eldernum = eldernum;
	}

	public int getChildnum() {
		return childnum;
	}

	public void setChildnum(int childnum) {
		this.childnum = childnum;
	}

	public String getQsbh() {
		return qsbh;
	}

	public void setQsbh(String qsbh) {
		this.qsbh = qsbh;
	}

	public String getDdsj() {
		return ddsj;
	}

	public void setDdsj(String ddsj) {
		this.ddsj = ddsj;
	}

	public String getBlrq() {
		return blrq;
	}

	public void setBlrq(String blrq) {
		this.blrq = blrq;
	}

	public String getBlsj() {
		return blsj;
	}

	public void setBlsj(String blsj) {
		this.blsj = blsj;
	}

	public BigDecimal getBzjfs() {
		return bzjfs;
	}

	public void setBzjfs(BigDecimal bzjfs) {
		this.bzjfs = bzjfs;
	}

	public String getDdzt() {
		return ddzt;
	}

	public void setDdzt(String ddzt) {
		this.ddzt = ddzt;
	}

	public String getZlbh() {
		return zlbh;
	}

	public void setZlbh(String zlbh) {
		this.zlbh = zlbh;
	}

	public int getYdzs() {
		return ydzs;
	}

	public void setYdzs(int ydzs) {
		this.ydzs = ydzs;
	}

	public String getJk() {
		return jk;
	}

	public void setJk(String jk) {
		this.jk = jk;
	}

	public String getCzybh() {
		return czybh;
	}

	public void setCzybh(String czybh) {
		this.czybh = czybh;
	}

	public String getCzymc() {
		return czymc;
	}

	public void setCzymc(String czymc) {
		this.czymc = czymc;
	}

	public String getKhjlbh() {
		return khjlbh;
	}

	public void setKhjlbh(String khjlbh) {
		this.khjlbh = khjlbh;
	}

	public String getKhjlmc() {
		return khjlmc;
	}

	public void setKhjlmc(String khjlmc) {
		this.khjlmc = khjlmc;
	}

	public String getDestinestate() {
		return destinestate;
	}

	public void setDestinestate(String destinestate) {
		this.destinestate = destinestate;
	}

	public String getRecieptdept() {
		return recieptdept;
	}

	public void setRecieptdept(String recieptdept) {
		this.recieptdept = recieptdept;
	}

	public String getPaychannel() {
		return paychannel;
	}

	public void setPaychannel(String paychannel) {
		this.paychannel = paychannel;
	}

	public String getPaystatus() {
		return paystatus;
	}

	public void setPaystatus(String paystatus) {
		this.paystatus = paystatus;
	}

	public String getHardtomove() {
		return hardtomove;
	}

	public void setHardtomove(String hardtomove) {
		this.hardtomove = hardtomove;
	}

	public BigDecimal getTotalprice() {
		return totalprice;
	}

	public void setTotalprice(BigDecimal totalprice) {
		this.totalprice = totalprice;
	}

	public String getScqy() {
		return scqy;
	}

	public void setScqy(String scqy) {
		this.scqy = scqy;
	}

	public String getCsbz() {
		return csbz;
	}

	public void setCsbz(String csbz) {
		this.csbz = csbz;
	}

	public String getBefore_order_source() {
		return before_order_source;
	}

	public void setBefore_order_source(String before_order_source) {
		this.before_order_source = before_order_source;
	}

	public String getBefore_order_type() {
		return before_order_type;
	}

	public void setBefore_order_type(String before_order_type) {
		this.before_order_type = before_order_type;
	}

	public String getHorseman_id() {
		return horseman_id;
	}

	public void setHorseman_id(String horseman_id) {
		this.horseman_id = horseman_id;
	}

	public String getMember_address() {
		return member_address;
	}

	public void setMember_address(String member_address) {
		this.member_address = member_address;
	}

	public String getNeedreciept() {
		return needreciept;
	}

	public void setNeedreciept(String needreciept) {
		this.needreciept = needreciept;
	}

	public String getSfyd() {
		return sfyd;
	}

	public void setSfyd(String sfyd) {
		this.sfyd = sfyd;
	}

	public int getParkingnum() {
		return parkingnum;
	}

	public void setParkingnum(int parkingnum) {
		this.parkingnum = parkingnum;
	}

	public int getZkfa() {
		return zkfa;
	}

	public void setZkfa(int zkfa) {
		this.zkfa = zkfa;
	}

	public String getYdlx() {
		return ydlx;
	}

	public void setYdlx(String ydlx) {
		this.ydlx = ydlx;
	}

	public String getKwxh() {
		return kwxh;
	}

	public void setKwxh(String kwxh) {
		this.kwxh = kwxh;
	}

	public String getKhtmtz() {
		return khtmtz;
	}

	public void setKhtmtz(String khtmtz) {
		this.khtmtz = khtmtz;
	}

	public String getSfyyf() {
		return sfyyf;
	}

	public void setSfyyf(String sfyyf) {
		this.sfyyf = sfyyf;
	}

	public String getSfxyq() {
		return sfxyq;
	}

	public void setSfxyq(String sfxyq) {
		this.sfxyq = sfxyq;
	}

	public int getCancel_type() {
		return cancel_type;
	}

	public void setCancel_type(int cancel_type) {
		this.cancel_type = cancel_type;
	}

	public String getWarning() {
		return warning;
	}

	public void setWarning(String warning) {
		this.warning = warning;
	}

	public int getShippingmethod() {
		return shippingmethod;
	}

	public void setShippingmethod(int shippingmethod) {
		this.shippingmethod = shippingmethod;
	}

	public String getBefore_source() {
		return before_source;
	}

	public void setBefore_source(String before_source) {
		this.before_source = before_source;
	}

	public String getQdsj() {
		return qdsj;
	}

	public void setQdsj(String qdsj) {
		this.qdsj = qdsj;
	}

	public String getYshsj() {
		return yshsj;
	}

	public void setYshsj(String yshsj) {
		this.yshsj = yshsj;
	}

	public String getQdsj1() {
		return qdsj1;
	}

	public void setQdsj1(String qdsj1) {
		this.qdsj1 = qdsj1;
	}

	public String getSysj() {
		return sysj;
	}

	public void setSysj(String sysj) {
		this.sysj = sysj;
	}

	public String getPdsj() {
		return pdsj;
	}

	public void setPdsj(String pdsj) {
		this.pdsj = pdsj;
	}

	public String getWcsj() {
		return wcsj;
	}

	public void setWcsj(String wcsj) {
		this.wcsj = wcsj;
	}

	public String getSbsqsj() {
		return sbsqsj;
	}

	public void setSbsqsj(String sbsqsj) {
		this.sbsqsj = sbsqsj;
	}

	public String getXsqxsj() {
		return xsqxsj;
	}

	public void setXsqxsj(String xsqxsj) {
		this.xsqxsj = xsqxsj;
	}

	public String getQdrbh() {
		return qdrbh;
	}

	public void setQdrbh(String qdrbh) {
		this.qdrbh = qdrbh;
	}

	public String getQdrmc() {
		return qdrmc;
	}

	public void setQdrmc(String qdrmc) {
		this.qdrmc = qdrmc;
	}

	public String getShrbh() {
		return shrbh;
	}

	public void setShrbh(String shrbh) {
		this.shrbh = shrbh;
	}

	public String getShrmc() {
		return shrmc;
	}

	public void setShrmc(String shrmc) {
		this.shrmc = shrmc;
	}

	public String getQdrbh1() {
		return qdrbh1;
	}

	public void setQdrbh1(String qdrbh1) {
		this.qdrbh1 = qdrbh1;
	}

	public String getQdrmc1() {
		return qdrmc1;
	}

	public void setQdrmc1(String qdrmc1) {
		this.qdrmc1 = qdrmc1;
	}

	public String getSyrbh() {
		return syrbh;
	}

	public void setSyrbh(String syrbh) {
		this.syrbh = syrbh;
	}

	public String getSyrmc() {
		return syrmc;
	}

	public void setSyrmc(String syrmc) {
		this.syrmc = syrmc;
	}

	public String getPdrbh() {
		return pdrbh;
	}

	public void setPdrbh(String pdrbh) {
		this.pdrbh = pdrbh;
	}

	public String getPdrmc() {
		return pdrmc;
	}

	public void setPdrmc(String pdrmc) {
		this.pdrmc = pdrmc;
	}

	public String getSbsqrbh() {
		return sbsqrbh;
	}

	public void setSbsqrbh(String sbsqrbh) {
		this.sbsqrbh = sbsqrbh;
	}

	public String getSbsqrmc() {
		return sbsqrmc;
	}

	public void setSbsqrmc(String sbsqrmc) {
		this.sbsqrmc = sbsqrmc;
	}

	public String getXsqxrbh() {
		return xsqxrbh;
	}

	public void setXsqxrbh(String xsqxrbh) {
		this.xsqxrbh = xsqxrbh;
	}

	public String getXsqxrmc() {
		return xsqxrmc;
	}

	public void setXsqxrmc(String xsqxrmc) {
		this.xsqxrmc = xsqxrmc;
	}

	public BigDecimal getYsje() {
		return ysje;
	}

	public void setYsje(BigDecimal ysje) {
		this.ysje = ysje;
	}

	public BigDecimal getZlje() {
		return zlje;
	}

	public void setZlje(BigDecimal zlje) {
		this.zlje = zlje;
	}

	public String getYnkpi() {
		return ynkpi;
	}

	public void setYnkpi(String ynkpi) {
		this.ynkpi = ynkpi;
	}

	public String getCancel_type_online() {
		return cancel_type_online;
	}

	public void setCancel_type_online(String cancel_type_online) {
		this.cancel_type_online = cancel_type_online;
	}

	public String getFail_type() {
		return fail_type;
	}

	public void setFail_type(String fail_type) {
		this.fail_type = fail_type;
	}

	public String getHorseman_name() {
		return horseman_name;
	}

	public void setHorseman_name(String horseman_name) {
		this.horseman_name = horseman_name;
	}

	public int getScbj() {
		return scbj;
	}

	public void setScbj(int scbj) {
		this.scbj = scbj;
	}

	public String getBz() {
		return bz;
	}

	public void setBz(String bz) {
		this.bz = bz;
	}

	public BigDecimal getYl1() {
		return yl1;
	}

	public void setYl1(BigDecimal yl1) {
		this.yl1 = yl1;
	}

	public BigDecimal getYl2() {
		return yl2;
	}

	public void setYl2(BigDecimal yl2) {
		this.yl2 = yl2;
	}

	public BigDecimal getYl3() {
		return yl3;
	}

	public void setYl3(BigDecimal yl3) {
		this.yl3 = yl3;
	}

	public String getYl4() {
		return yl4;
	}

	public void setYl4(String yl4) {
		this.yl4 = yl4;
	}

	public String getYl5() {
		return yl5;
	}

	public void setYl5(String yl5) {
		this.yl5 = yl5;
	}

	public String getYnkpi2() {
		return ynkpi2;
	}

	public void setYnkpi2(String ynkpi2) {
		this.ynkpi2 = ynkpi2;
	}

	public String getCancel_type_online2() {
		return cancel_type_online2;
	}

	public void setCancel_type_online2(String cancel_type_online2) {
		this.cancel_type_online2 = cancel_type_online2;
	}

	public String getFail_type2() {
		return fail_type2;
	}

	public void setFail_type2(String fail_type2) {
		this.fail_type2 = fail_type2;
	}

	public String getIs_update() {
		return is_update;
	}

	public void setIs_update(String is_update) {
		this.is_update = is_update;
	}

	public String getIs_handwork() {
		return is_handwork;
	}

	public void setIs_handwork(String is_handwork) {
		this.is_handwork = is_handwork;
	}

	public String getHandwork_yddh() {
		return handwork_yddh;
	}

	public void setHandwork_yddh(String handwork_yddh) {
		this.handwork_yddh = handwork_yddh;
	}

	public String getInvalid_desc() {
		return invalid_desc;
	}

	public void setInvalid_desc(String invalid_desc) {
		this.invalid_desc = invalid_desc;
	}

	public String getIs_commission() {
		return is_commission;
	}

	public void setIs_commission(String is_commission) {
		this.is_commission = is_commission;
	}

	public String getBill_num() {
		return bill_num;
	}

	public void setBill_num(String bill_num) {
		this.bill_num = bill_num;
	}

	public Date getReport_date() {
		return report_date;
	}

	public void setReport_date(Date report_date) {
		this.report_date = report_date;
	}

	public BigDecimal getCommission_amount() {
		return commission_amount;
	}

	public void setCommission_amount(BigDecimal commission_amount) {
		this.commission_amount = commission_amount;
	}

	public BigDecimal getShop_real_amount() {
		return shop_real_amount;
	}

	public void setShop_real_amount(BigDecimal shop_real_amount) {
		this.shop_real_amount = shop_real_amount;
	}

	public int getRefund_state() {
		return refund_state;
	}

	public void setRefund_state(int refund_state) {
		this.refund_state = refund_state;
	}

	public String getRider_phone() {
		return rider_phone;
	}

	public void setRider_phone(String rider_phone) {
		this.rider_phone = rider_phone;
	}

	public String getDelivery_is_open() {
		return delivery_is_open;
	}

	public void setDelivery_is_open(String delivery_is_open) {
		this.delivery_is_open = delivery_is_open;
	}

	public BigDecimal getPlatform_rate() {
		return platform_rate;
	}

	public void setPlatform_rate(BigDecimal platform_rate) {
		this.platform_rate = platform_rate;
	}

	public BigDecimal getShop_rate() {
		return shop_rate;
	}

	public void setShop_rate(BigDecimal shop_rate) {
		this.shop_rate = shop_rate;
	}

	public BigDecimal getPackage_box_fee() {
		return package_box_fee;
	}

	public void setPackage_box_fee(BigDecimal package_box_fee) {
		this.package_box_fee = package_box_fee;
	}

	public String getIs_verification() {
		return is_verification;
	}

	public void setIs_verification(String is_verification) {
		this.is_verification = is_verification;
	}

	public String getTaxpayerid() {
		return taxpayerid;
	}

	public void setTaxpayerid(String taxpayerid) {
		this.taxpayerid = taxpayerid;
	}

}
