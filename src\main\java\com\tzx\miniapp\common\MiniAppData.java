package com.tzx.miniapp.common;

import com.tzx.publics.common.BaseData;

public class MiniAppData extends BaseData {
	private long t;
	private boolean success;

	public MiniAppData() {
		t = System.currentTimeMillis();
		this.setSource("XCX");
		this.setMsg("");
	}

	public long getT() {
		return t;
	}

	public void setT(long t) {
		this.t = t;
	}

	public boolean isSuccess() {
		return success;
	}

	public void setSuccess(boolean success) {
		this.success = success;
	}

}
