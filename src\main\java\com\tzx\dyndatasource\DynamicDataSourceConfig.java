package com.tzx.dyndatasource;

/**
 * <AUTHOR>
 * @Date 2020-03-13
 * @Descption
 **/

import com.alibaba.druid.pool.xa.DruidXADataSource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jta.atomikos.AtomikosDataSourceBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2020-03-13
 * @Descption 读取动态数据源配置
 **/
@Configuration
public class DynamicDataSourceConfig {
    public static DSType dsType = DSType.PROD;//默认状态就是正式环境
    @Value("${jdbc.url}")
    String jdbcUrl;
    @Bean
    public DataSource prodDataSource(){
        DruidXADataSource xaDataSource = DruidXADataSourceBuilder.create("application",
                "jdbc");
//        AtomikosDataSourceBean atomikosDataSourceBean = new AtomikosDataSourceBean();
//        atomikosDataSourceBean.setXaDataSource(xaDataSource);
//        atomikosDataSourceBean.setUniqueResourceName("prodDataSource");
        return xaDataSource;
    }

    @Bean
    public DataSource uatDataSource(){
        DruidXADataSource xaDataSource = DruidXADataSourceBuilder.create("application",
                "jdbc");
        //这里写死，自动替换成tzxserverlx。实际应该是写在配置文件里面两个配置比较好。但是
        //有可能升级配置文件后，影响现实项目已有的配置
        String url = jdbcUrl;
        url = url.substring(0,url.lastIndexOf("/")+1);
        url = url + "tzxserverlx";
        xaDataSource.setUrl(url);
//        AtomikosDataSourceBean atomikosDataSourceBean = new AtomikosDataSourceBean();
//        atomikosDataSourceBean.setXaDataSource(xaDataSource);
//        atomikosDataSourceBean.setUniqueResourceName("uatDataSource");
        return xaDataSource;
    }


    @Bean
    @Primary
    public DynamicDataSource dataSource(DataSource prodDataSource, DataSource uatDataSource) {
        Map<Object, Object> targetDataSources = new HashMap<>();
        targetDataSources.put(DSType.PROD.getValue(), prodDataSource);
        targetDataSources.put(DSType.UAT.getValue(), uatDataSource);
        return new DynamicDataSource( targetDataSources);
    }
}
