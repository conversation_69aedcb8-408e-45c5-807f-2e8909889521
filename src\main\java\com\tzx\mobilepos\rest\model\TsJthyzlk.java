package com.tzx.mobilepos.rest.model;

import com.tzx.publics.base.BaseEntity;

import javax.persistence.Table;
import java.io.Serializable;

/**
 * <AUTHOR> @since 2019-02-17
 */
@Table(name = "TS_JTHYZLK")
public class TsJthyzlk extends BaseEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	private String id;
	private String cono;
	private String name;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getCono() {
		return cono;
	}

	public void setCono(String cono) {
		this.cono = cono;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

}
