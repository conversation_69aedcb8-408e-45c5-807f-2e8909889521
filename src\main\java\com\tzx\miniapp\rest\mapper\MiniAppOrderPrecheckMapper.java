package com.tzx.miniapp.rest.mapper;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.tzx.commapi.rest.vo.ReqParam;
import com.tzx.miniapp.rest.model.TqWdk;
import com.tzx.miniapp.rest.model.TqZdk;
import com.tzx.miniapp.rest.model.TsCmk;
import com.tzx.miniapp.rest.model.TsYhfssdk;
import com.tzx.miniapp.rest.vo.BtActivity;
import com.tzx.miniapp.rest.vo.BtSaleoutdishType;
import com.tzx.miniapp.rest.vo.BtTcselectmx;
import com.tzx.miniapp.rest.vo.BtYdd;
import com.tzx.miniapp.rest.vo.BtYdxm1;
import com.tzx.miniapp.rest.vo.BtYdxm2;
import com.tzx.miniapp.rest.vo.Dish;
import com.tzx.miniapp.rest.vo.GroupDetails;
import com.tzx.publics.base.MyMapper;

public interface MiniAppOrderPrecheckMapper extends MyMapper<Dish> {

    public void insertBtYddActive(@Param("yddh") String yddh, @Param("pay_name") String pay_name, @Param("pay_money") double pay_money, @Param("couponcodes") String couponcodes, @Param("active_type") String active_type, @Param("active_id") int active_id);

    public void clearYdd(@Param("yddh") String yddh);

    public void clearYdxm1(@Param("yddh") String yddh);

    public void clearYdxm2(@Param("yddh") String yddh);

    public void clearYddTcSelectMx(@Param("yddh") String yddh);

    public void clearYddActive(@Param("yddh") String yddh);

    public void clearYddPayActive(@Param("yddh") String yddh);

    public int insertBtYdd(@Param("by") BtYdd by);

    public int insertBtYdxm1(BtYdxm1 ydxm1);

    public int insertBtYdxm2(List<BtYdxm2> byxm2List);

    public int insertBtTcselectmx(List<BtTcselectmx> tsmxList);

    public int insertBtActivity(List<BtActivity> btActivity);

    public void updateYddActiveGiftYdxmid(@Param("yddh") String yddh, @Param("dcxh") int dcxh);

    public void updateYddActiveBuyYdxmid(@Param("yddh") String yddh, @Param("dcxh") int dcxh, @Param("maindcxh") int maindcxh);

    public int selectYhfssdk(@Param("id") int id);

    public int insertYhfssdk(@Param("id") int id, @Param("yhfsbh") String yhfsbh, @Param("yhfsmc1") String yhfsmc1, @Param("yhsx") String yhsx);

    public int selectYhfssdk_xf(@Param("id") int id);

    public int insertYhfssdk_xf(@Param("id") int id, @Param("yhfsbh") String yhfsbh, @Param("yhfsmc1") String yhfsmc1, @Param("yhsx") String yhsx);

    public int getBcid();

    public BtYdxm1 getDiscount(@Param("total") double total);

    public double getFzsl(@Param("fzid") int fzid, @Param("mxid") int mxid);

    public String getIsVipPrice(@Param("grade") int grade);

    public int addCmByZs(@Param("szdbh") String szdbh, @Param("aitemid") int aitemid, @Param("ixmsl") int ixmsl, @Param("sskjh") String sskjh, @Param("sxsyh") String sxsyh, @Param("skwbh") String skwbh, @Param("sggbh") String sggbh, @Param("skwbz") String skwbz, @Param("atype") int atype);

    public String getClmxidByXmbh(@Param("xmbh") String xmbh);

    public int addTcByZs(@Param("szdbh") String szdbh, @Param("aitemid") int aitemid, @Param("ixmsl") int ixmsl);

    public void deletetcdcxzlsk(@Param("bill_num") String bill_num, @Param("tcid") Integer tcid, @Param("tcxh") Integer tcxh, @Param("item_id") Integer item_id);

    public int getItmeIdByXmbh(@Param("xmbh") String xmbh);

    public GroupDetails getTcxh(@Param("tcid") int tcid, @Param("fzid") int fzid);

    public int getFzmxid(@Param("fzid") int fzid, @Param("fzje") double fzje, @Param("mxid") int mxid);

    public void addTcdcxzlsk(@Param("kdzdbh") String kdzdbh, @Param("tcid") Integer tcid,
                             @Param("tcxh") Integer tcxh, @Param("item_id") Integer item_id,
                             @Param("item_count") Double item_count, @Param("xcsl") Integer xcsl,
                             @Param("cmdj") Double cmdj, @Param("cmje") Double cmje, @Param("fzje") Double fzje,
                             @Param("mxlxid") Integer mxlxid);

    public TqZdk getZdk(@Param("yddh") String yddh);

    public TqZdk getZdlsk(@Param("yddh") String yddh);

    public String getClmxidByXmid(@Param("xmid") int xmid);

    public TqWdk getNewWdk(@Param("kdzdbh") String kdzdbh);

    public TsYhfssdk getYhfs(@Param("yhsx") String yhsx);

    public int addCmNew(@Param("szdbh") String szdbh, @Param("aitemid") int aitemid, @Param("ixmsl") int ixmsl, @Param("sskjh") String sskjh, @Param("sxsyh") String sxsyh, @Param("skwbh") String skwbh, @Param("sggbh") String sggbh, @Param("skwbz") String skwbz, @Param("atype") int atype);

    public int insertBtYddByZs(@Param("by") BtYdd by);

    public int addCmNewByZs(@Param("szdbh") String szdbh, @Param("aitemid") int aitemid, @Param("ixmsl") int ixmsl, @Param("sskjh") String sskjh, @Param("sxsyh") String sxsyh, @Param("skwbh") String skwbh, @Param("sggbh") String sggbh, @Param("ssysdc") String ssysdc, @Param("sjgxh") String sjgxh, @Param("sjgtxbh") String sjgtxbh, @Param("skwbz") String skwbz, @Param("atype") int atype);

    public String getFoodBoxSetByCmid(@Param("cmid") int cmid);

    public TsCmk getTsCmk(@Param("cmid") int cmid);

    public List<String> checkClmx(@Param("itemIdList") List<Integer> itemIdList);

    public List<Dish> checkPrice(@Param("itemIdList") List<Integer> itemIdList);

    public TsCmk getSendAmount(@Param("cmmc1") String cmmc1);

    public void clearYddPayActiveByType(@Param("yddh") String yddh);

    public int insertBtSaleoutdish(List<BtSaleoutdishType> saleoutdishList);

    public void updateSaleoutStep(@Param("yddh") String yddh, @Param("step") int step);

    public List<ReqParam> getBtSaleoutdish(@Param("nowDate") Date nowDate);

    public void updateAddSaleOut(@Param("step") int step, @Param("nowDate") Date nowDate);

    public void delBtSaleoutdish(@Param("yddh") String yddh);

    public TsCmk getAddDish(@Param("kwid") int kwid);

    public Integer getIdByCode(@Param("dishsno") String dishsno);

    public TsCmk getFoodBoxInfo(@Param("cmmc") String cmmc);

    public void clearYddPayments(@Param("yddh") String yddh);

    public TsCmk getDishByCode(@Param("cmbh") String cmbh);

    public TsCmk getDishById(@Param("cmid") Integer cmid);

    TsCmk getDishByIngredientId(@Param("ingredientId") Integer ingredientId);

    TsCmk getDishByName(String cmmc);

    List<TsCmk> getAllDishes();

    TsCmk getDishJgtxByCode(@Param("cmbh") String cmbh);
}