package com.tzx.miniapp.rest.mapper;

import com.tzx.publics.base.MyMapper;
import com.tzx.miniapp.rest.model.TsCzyk;
import org.apache.ibatis.annotations.Param;

/**
 *
 * <AUTHOR>
 * @since 2018-05-15
 */

public interface MiniAppTsCzykMapper extends MyMapper<TsCzyk> {

	public TsCzyk findLogin(@Param("czybh") String czybh, @Param("czymm") String czymm);
	
	public TsCzyk findByCzybh(@Param("czybh") String czybh);

}