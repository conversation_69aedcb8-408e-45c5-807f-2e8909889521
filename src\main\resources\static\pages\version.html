<!DOCTYPE html>
<html lang="zh-CN" xmlns:align="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <!-- Bootstrap -->
    <link href="../bootstrap-3.3.7-dist/css/bootstrap.css" rel="stylesheet">
    <link href="../css/popbox.css" rel="stylesheet">

    <!-- HTML5 shim 和 Respond.js 是为了让 IE8 支持 HTML5 元素和媒体查询（media queries）功能 -->
    <!-- 警告：通过 file:// 协议（就是直接将 html 页面拖拽到浏览器中）访问页面时 Respond.js 不起作用 -->
    <!--[if lt IE 9]>
    <script src="../js/common/html5shiv.min.js"></script>
    <script src="../js/common/respond.min.js"></script>
    <![endif]-->
    <script type="text/javascript" src="../js/common/jquery.min.js"></script>
    <script type="text/javascript" src="../bootstrap-3.3.7-dist/js/bootstrap.min.js"></script>
    <script type="text/javascript" src="../js/pages/version.js"></script>
</head>
<style>

    .table th {

        text-align: center;
        height: 38px;
        background-color: #f0f4f4;

    }

</style>
<body>
<div class="container-fluid">
    <div class="alert alert-danger alert-dismissible" role="alert" id = "versionmsg" style="display: none">
        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        查询版本号发生异常
    </div>
    <div align="center">
        <div align="left">
            <p >主版本号:<a href="#" id="baseversion"></a></p>
            <p>小程序服务版本号:<a href="#" id="miniappversion"></a></p>
            <p>App服务版本号:<a href="#" id="mobileposversion"></a></p>
            <p>监听程序版本号:<a href="#" id="recevierversion"></a></p>
            <br/>
            <h5>版权所有:奥琦玮信息科技（北京）有限公司</h5>
        </div>
    </div>
    <div id="background" class="back">
        <div id="div1" class="content">
            <div id="close">
                <span id="close-button">×</span>
                <h4 id="popcaption"></h4>
            </div>
            <div id="div2">
                <h4 id="poptitle"></h4>
                <p id="popdetail"></p>
            </div>
            <h3 id="foot"></h3>
        </div>
    </div>

</div>
</body>
</html>