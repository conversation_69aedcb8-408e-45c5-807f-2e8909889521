package com.tzx.miniapp.rest.service.impl;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.tzx.commapi.rest.service.IUseYhfsApiService;
import com.tzx.commapi.rest.vo.UseYhfsParam;
import com.tzx.miniapp.common.Constant;
import com.tzx.miniapp.common.Data;
import com.tzx.publics.common.BillNoData;
import com.tzx.publics.listener.InitDataListener;
import com.tzx.miniapp.rest.mapper.MiniAppFirstPayMapper;
import com.tzx.miniapp.rest.mapper.MiniAppOrderPrecheckMapper;
import com.tzx.miniapp.rest.mapper.MiniAppShopBaseInfoMapper;
import com.tzx.miniapp.rest.mapper.MiniAppShopStatusMapper;
import com.tzx.miniapp.rest.mapper.MiniAppTqWdkMapper;
import com.tzx.miniapp.rest.mapper.MiniAppTqZdkMapper;
import com.tzx.miniapp.rest.mapper.MiniAppTsPsjgsdkMapper;
import com.tzx.miniapp.rest.model.TqFklslsk;
import com.tzx.miniapp.rest.model.TqJtztk;
import com.tzx.miniapp.rest.model.TqWdk;
import com.tzx.miniapp.rest.model.TqZdk;
import com.tzx.miniapp.rest.model.TsBmkzk;
import com.tzx.miniapp.rest.model.TsCmk;
import com.tzx.miniapp.rest.model.TsFkfssdk;
import com.tzx.miniapp.rest.model.TsGgcsk;
import com.tzx.miniapp.rest.model.TsPsjgsdk;
import com.tzx.miniapp.rest.model.TsYhfssdk;
import com.tzx.miniapp.rest.service.IData;
import com.tzx.miniapp.rest.service.IMiniAppZsOrderPrecheck;
import com.tzx.miniapp.rest.vo.AccountsOrder;
import com.tzx.miniapp.rest.vo.BtPayments;
import com.tzx.miniapp.rest.vo.BtTcselectmx;
import com.tzx.miniapp.rest.vo.BtYdd;
import com.tzx.miniapp.rest.vo.BtYdxm2;
import com.tzx.miniapp.rest.vo.Dish;
import com.tzx.miniapp.rest.vo.DishVo;
import com.tzx.miniapp.rest.vo.GroupDetails;
import com.tzx.miniapp.rest.vo.Shops;
import com.tzx.publics.util.ArithUtil;
import com.tzx.publics.util.DateUtil;
import com.tzx.publics.util.SendRequest;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

@Service
public class MiniAppZsOrderPrecheckImpl implements IMiniAppZsOrderPrecheck {

	private final static Logger LOGGER = LoggerFactory.getLogger(MiniAppZsOrderPrecheckImpl.class);

	@Autowired
	private MiniAppOrderPrecheckMapper orderPrecheckMapper;
	@Autowired
	private MiniAppFirstPayMapper firstPayMapper;
	@Autowired
	private MiniAppShopStatusMapper shopStatusMapper;
	@Autowired
	private MiniAppTqZdkMapper tqZdkMapper;
	@Autowired
	private MiniAppTqWdkMapper tqWdk;
	@Autowired
	private MiniAppShopBaseInfoMapper shopBaseInfoMapper;
	// 机构配置信息查询接口注入对象
	@Autowired
	private MiniAppTsPsjgsdkMapper tsPsjgsdkMapper;
	// 不计收入拆分
	@Autowired
	private IUseYhfsApiService useYhfsApiService;

	@Transactional
	public Data orderPrecheck(JSONObject orderData, BillNoData billNoData) {
		// 创建返回数据对象
		Data data = new Data();
		// 小程序订单号
		String outOrderId = orderData.optString("out_order_id");
		// 持久化使用第预订单号字段，为了快速区分，小程序增加了  TS 前缀
		String outOrderIdInDB = Constant.BILL_PREFIX + outOrderId;
		
		// 报表日期
		Map<String, String> bbrqMap = shopStatusMapper.findBbrq();
		// 生成默认报表日期
		String bbrq = DateUtil.getNowDateYYDDMM();
		if (null != bbrqMap && bbrqMap.size() != 0) {
			bbrq = bbrqMap.get("bbrq");
		}
		// 判断营业状态，因为众赏推送异常账单时不判断营业状态，所以这里控制一下
		TqJtztk jtztk = shopStatusMapper.checkShopOpenStart(DateUtil.parseDate(bbrq));
		if (null == jtztk || "JSSY".equals(jtztk.getCznr())) {
			data.setSuccess(0);
			data.setMsg("门店已打烊，请开店后重试！");
			data.setData(new HashMap<String, Object>());
			return data;
		}
		Data dataShopStatus = shopStatus();
		if (dataShopStatus.getSuccess() != 1) {
			data.setMsg("下单失败：" + dataShopStatus.getMsg());
			return data;
		}
		
		TqZdk tqzdk = orderPrecheckMapper.getZdk(outOrderIdInDB);
		TqZdk tqzdlsk = orderPrecheckMapper.getZdlsk(outOrderIdInDB);
		
		if ((null != tqzdk && "ZDSX_YJ".equals(tqzdk.getJzsx())) || (null != tqzdlsk && "ZDSX_YJ".equals(tqzdlsk.getJzsx()))) {
			JSONObject joData = new JSONObject();
			joData.put("identify", outOrderId);
			if(null != tqzdk){
				joData.put("meal_number", tqzdk.getQch());
			}
			if(null != tqzdlsk){
				joData.put("meal_number", tqzdlsk.getQch());
			}
			joData.put("business_date", bbrq);
			data.setData(joData);
			data.setSuccess(1);
			data.setCode(2);
			data.setMsg("订单已同步");
			return data;
		}
		
		JSONObject orderInfo = orderData.optJSONObject("order_info");
		JSONObject payInfo = orderData.optJSONObject("pay_info");
		
		JSONArray payInfoArr = payInfo.optJSONArray("pay_info");
		double cAmount = 0;
		for (int i = 0; i < payInfoArr.size(); i++) {
			JSONObject payObj = payInfoArr.getJSONObject(i);
			cAmount = ArithUtil.add(cAmount, payObj.optDouble("amount", 0));
		}
		if(orderInfo.optDouble("order_amount", 0) != cAmount){
			data.setSuccess(0);
			data.setMsg("支付金额(" + cAmount + ")与应付金额（" + orderInfo.optDouble("order_amount", 0) + "）不匹配！");
			data.setData(new HashMap<String, Object>());
			return data;
		}

		double totalAmount = ArithUtil.add(orderInfo.optDouble("order_amount", 0),  orderInfo.optDouble("orde_dis_amount", 0));
		double mxAmount = 0;

		JSONArray normalitemsMx = new JSONArray();
		if (orderInfo.has("normalitems")) {
			normalitemsMx = orderInfo.optJSONArray("normalitems");
		}
		JSONArray setmealMx = new JSONArray();
		if (orderInfo.has("setmeal")) {
			setmealMx = orderInfo.optJSONArray("setmeal");
		}
		for (int i = 0; i < normalitemsMx.size(); i++) {
			JSONObject normalitemObj = normalitemsMx.getJSONObject(i);
			double itemAmount = ArithUtil.mul(normalitemObj.optDouble("number", 0), normalitemObj.optDouble("price", 0));
			double foodboxAmount = ArithUtil.mul(normalitemObj.optDouble("number", 0), normalitemObj.optDouble("foodbox_amount", 0));
			itemAmount = ArithUtil.add(itemAmount, foodboxAmount);
			mxAmount = ArithUtil.add(mxAmount, itemAmount);
		}

		for (int i = 0; i < setmealMx.size(); i++) {
			JSONObject setmealObj = setmealMx.getJSONObject(i);
			JSONArray mainDishMx = new JSONArray();
			double number = setmealObj.optDouble("number", 0);
			mxAmount = ArithUtil.add(mxAmount, ArithUtil.mul(setmealObj.optDouble("price", 0), number));
			if (setmealObj.has("maindish")) {
				mainDishMx = setmealObj.optJSONArray("maindish");
			}
			for (int j = 0; j < mainDishMx.size(); j++) {
				JSONObject mainDishMxObj = mainDishMx.getJSONObject(j);
				double foodboxAmount = ArithUtil.mul(mainDishMxObj.optDouble("number", 0), mainDishMxObj.optDouble("foodbox_amount", 0));
				foodboxAmount = ArithUtil.mul(foodboxAmount, number);
				mxAmount = ArithUtil.add(mxAmount, foodboxAmount);
			}
			JSONArray mandatoryMx = new JSONArray();
			if (setmealObj.has("mandatory")) {
				mandatoryMx = setmealObj.optJSONArray("mandatory");
			}
			for (int j = 0; j < mandatoryMx.size(); j++) {
				JSONObject mandatoryMxObj = mandatoryMx.getJSONObject(j);
				double foodboxAmount = ArithUtil.mul(mandatoryMxObj.optDouble("number", 0), mandatoryMxObj.optDouble("foodbox_amount", 0));
				foodboxAmount = ArithUtil.mul(foodboxAmount, number);
				double apriceAmount = ArithUtil.mul(mandatoryMxObj.optDouble("number", 0), mandatoryMxObj.optDouble("aprice", 0));
				apriceAmount = ArithUtil.mul(apriceAmount, number);
				mxAmount = ArithUtil.add(mxAmount, foodboxAmount);
				mxAmount = ArithUtil.add(mxAmount, apriceAmount);
			}
		}

		double sendAmount = 0;
		if (orderInfo.has("send_amount")) {
			sendAmount = orderInfo.optDouble("send_amount", 0);
		}
		mxAmount = ArithUtil.add(mxAmount, sendAmount);

		if(totalAmount != mxAmount){
			data.setSuccess(0);
			data.setMsg("账单金额(" + totalAmount + ")与明细金额（" + mxAmount + "）不匹配！");
			data.setData(new HashMap<String, Object>());
			return data;
		}

		// cardr_type 会员卡类型（1：众赏会员，2：百福会员）  ， pos数据库  cwlxbh 1：微生活，2：众赏，3：百福
		String cwlxbh = "2";
		JSONObject member = new JSONObject();
		if (orderData.has("member")) {
			member = orderData.optJSONObject("member");
			if (member.has("cardr_type")) {
				String cardr_type = member.optString("cardr_type");
				if ("1".equals(cardr_type)) {
					cwlxbh = "2";
				}
				if ("2".equals(cardr_type)) {
					cwlxbh = "3";
				}
			}
		}
		
		// 默认失败
		data.setSuccess(0);
		// 保存平台预定账单编号
		data.setYddbh(outOrderId);
		try {
//			String kdzdbh = createBh("99", "TQ_ZDK", "KDZDBH");
			String kdzdbh = billNoData.getKdzdbh();
			Map<String, String> rMap = createBill(orderInfo, kdzdbh, outOrderIdInDB, bbrq, billNoData);
			//落单
//			boolean resultFlag = ordering(orderInfo, kdzdbh, outOrderIdInDB, rMap);
			Map<String, String> orderingR = ordering(orderInfo, kdzdbh, outOrderIdInDB, rMap);
			if ("true".equals(orderingR.get("resultFlag"))) {
				double yhje = orderInfo.optDouble("orde_dis_amount", 0);
				double settleAmount=orderInfo.optDouble("settle_amount",0);
				if (yhje != 0) {
					TsYhfssdk yhfs = orderPrecheckMapper.getYhfs("65");
					insertDiscount(kdzdbh, yhje, bbrq, rMap.get("bcid"), rMap.get("jtbh"), yhfs,settleAmount);
					tqZdkMapper.updateZrje(kdzdbh, yhje);
				}

				firstPayMapper.calcmoney(kdzdbh);
				firstPayMapper.zRtr(kdzdbh);
				
				boolean aoflag = firstPay(outOrderIdInDB, payInfo, kdzdbh, bbrq, yhje, rMap, cwlxbh);
				if (aoflag) {
					// 拆分不记收入
					if ("1".equals(InitDataListener.ggcsMap.get("POS_PAYWITHOUT_TODIS"))) {
						TqZdk zdk = firstPayMapper.getZdbhByYdd(outOrderIdInDB);
						List<TqFklslsk> fklslskList = firstPayMapper.getFklslsk(kdzdbh);
						commUseYhfs(zdk, fklslskList, "99", bbrq);
					}
					
					accountsOrder(orderInfo, kdzdbh, bbrq, rMap.get("bcid"), rMap.get("czybh"), billNoData, cwlxbh);
					jointYdd(orderInfo, outOrderIdInDB, rMap.get("bcid"), rMap.get("qch"));
					JSONObject joData = new JSONObject();
					joData.put("identify", outOrderId);
					joData.put("meal_number", rMap.get("qch"));
					joData.put("business_date", bbrq);
					data.setData(joData);
					data.setSuccess(1);
					data.setMsg("付款成功");
				} else {
					tqZdkMapper.delWdkByYddh(outOrderIdInDB);
					tqZdkMapper.delFklslskByYddh(outOrderIdInDB);
					tqZdkMapper.delZdkByYddh(outOrderIdInDB);
					orderPrecheckMapper.clearYdd(outOrderIdInDB);
					orderPrecheckMapper.clearYdxm1(outOrderIdInDB);
					orderPrecheckMapper.clearYdxm2(outOrderIdInDB);
					orderPrecheckMapper.clearYddTcSelectMx(outOrderIdInDB);
					data.setSuccess(0);
					data.setMsg("下单失败");
					data.setData(new HashMap<String, Object>());
				}
			} else {
				tqZdkMapper.delWdkByYddh(outOrderIdInDB);
				tqZdkMapper.delFklslskByYddh(outOrderIdInDB);
				tqZdkMapper.delZdkByYddh(outOrderIdInDB);
				orderPrecheckMapper.clearYdd(outOrderIdInDB);
				orderPrecheckMapper.clearYdxm1(outOrderIdInDB);
				orderPrecheckMapper.clearYdxm2(outOrderIdInDB);
				orderPrecheckMapper.clearYddTcSelectMx(outOrderIdInDB);
				data.setSuccess(0);
				data.setMsg("下单失败:" + orderingR.get("resultMsg"));
				data.setData(new HashMap<String, Object>());
			}
			return data;
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			data.setSuccess(0);
			data.setMsg("系统错误:" + e);
			data.setData(new HashMap<String, Object>());
			return data;
		}
	}
	
	public Map<String, String> ordering(JSONObject orderInfo, String kdzdbh, String outOrderIdInDB, Map<String, String> rMap) {
		String resultFlag = "true";
		String resultMsg = "落单成功；";
		Map<String, String> resultMap = new HashMap<String, String>();
		int result_ = 0;
		JSONArray normalitems = new JSONArray();
		if (orderInfo.has("normalitems")) {
			normalitems = orderInfo.getJSONArray("normalitems");
		}
				
		JSONArray setmeal = new JSONArray();
		if (orderInfo.has("setmeal")) {
			setmeal = orderInfo.getJSONArray("setmeal");
		}
		List<BtYdxm2> bymx = new ArrayList<BtYdxm2>();
		List<BtTcselectmx> tsmx = new ArrayList<BtTcselectmx>();
		int yddcxh = 0;
//		TsPsjgsdk psjgsdk = tsPsjgsdkMapper.findLocalShopConfig();
		
		for (int i = 0; i < normalitems.size(); i++) {
			JSONObject item = normalitems.getJSONObject(i);
			int clmxidi = getClmxid(item);
			if(clmxidi == 0){
				result_ = -3;
				resultMsg = "菜品：《" + item.optString("name") + "》不在餐谱内，请重新同步基础数据;";
				break;
			}
			result_ = orderPrecheckMapper.addCmByZs(kdzdbh, clmxidi, item.optInt("number"), "99", "", "", Math.random() * 10000 + "", item.optString("remark"), 0);
//			result_ = orderPrecheckMapper.addCmNewByZs(kdzdbh, item.optInt("did"), item.optInt("number"), "99", "", "", Math.random() * 10000 + "", "", psjgsdk.getJgxh() + "", psjgsdk.getJgtxbh(), item.optString("remark"));
//			if (orderInfo.optInt("diningWay", 1) != 1 && 0 != item.optDouble("foodbox_amount", 0)) {
			if (0 != item.optDouble("foodbox_amount", 0)) {
				boolean tpkF = takeoutPackagingFee(item, kdzdbh, item.optInt("number"), rMap.get("czybh"));
				if (!tpkF) {
					result_ = -2;
				}
			}
			
			if (result_ != 0) {
				break;
			}
			
			if (item.optDouble("", 0) != 0) {
				
			}
			yddcxh = yddcxh + 1;
			BtYdxm2 ydxm2 = jointYdmx(item, outOrderIdInDB, "单品", yddcxh);
			bymx.add(ydxm2);
		}
		
		for (int i = 0; i < setmeal.size(); i++) {
			if (result_ != 0) {
				break;
			}
			JSONObject item = setmeal.getJSONObject(i);
			int clmxidi = getClmxid(item);
			if(clmxidi == 0){
				result_ = -3;
				resultMsg = "菜品：《" + item.optString("name") + "》不在餐谱内，请重新同步基础数据;";
				break;
			}
			for (int j = 0; j < item.optInt("number"); j++) {
				yddcxh = yddcxh + 1;
				result_ = orderPrecheckMapper.addTcByZs(kdzdbh, clmxidi, 1);
				orderPrecheckMapper.deletetcdcxzlsk(kdzdbh, null, null, null);
				if (result_ == 1) {
					result_ = 0;
				}
				
				BtYdxm2 ydxm2 = jointYdmx(item, outOrderIdInDB, "套餐", yddcxh);
				bymx.add(ydxm2);
				
				// 20190929，杨文彦，开始
				// 主菜菜品
				JSONArray mainDish = new JSONArray();
				if (item.has("maindish")) {
					mainDish = item.optJSONArray("maindish");
				}
				for (int k = 0; k < mainDish.size(); k++) {
					JSONObject gro = mainDish.getJSONObject(k);
					// 20190929，杨文彦，餐盒处理不再使用套餐主项关联餐盒设置，改成使用明细关联设置
//					if (orderInfo.optInt("diningWay", 1) != 1 && 0 != gro.optDouble("foodbox_amount", 0)) {
					if (0 != gro.optDouble("foodbox_amount", 0)) {
						boolean tpkF = takeoutPackagingFee(gro, kdzdbh, gro.optInt("number"), rMap.get("czybh"));
						if (!tpkF) {
							result_ = -2;
							break;
						}
					}
				}
				// 20190929，杨文彦，结束
				
				// 辅助可选菜品
				JSONArray mandatory = new JSONArray();
				if (item.has("mandatory")) {
					mandatory = item.optJSONArray("mandatory");
				}
				Integer tcid = item.optInt("did");
				for (int k = 0; k < mandatory.size(); k++) {
					if (result_ != 0) {
						break;
					}
					JSONObject gro = mandatory.getJSONObject(k);
					int rpdid = Integer.parseInt(gro.optString("rpdid"));
					GroupDetails gd = orderPrecheckMapper.getTcxh(tcid, rpdid);
					double aprice = 0;
					if (gro.has("aprice")) {
						aprice = gro.optDouble("aprice", 0);
					}
					orderPrecheckMapper.addTcdcxzlsk(kdzdbh, tcid, gd.getId(), gro.optInt("did"), gro.optDouble("number"),  1, Double.parseDouble(gd.getSjjg()), Double.parseDouble(gd.getSjjg()), ArithUtil.mul(aprice, gro.optDouble("number")),  rpdid);
					for (int n = 0; n < gro.optInt("number"); n++) {
						BtTcselectmx btsmx = new BtTcselectmx();
						double fzsl = orderPrecheckMapper.getFzsl(rpdid, gro.optInt("did"));
						btsmx.setYddh(outOrderIdInDB);
						if (0.5 == fzsl) {
							btsmx.setFzsl(0.5);
						} else {
							btsmx.setFzsl(1);
						}
						btsmx.setFzje(new BigDecimal(aprice));
						btsmx.setDcxh(yddcxh);
						btsmx.setFzid(rpdid);
						btsmx.setMxid(gro.optInt("did"));
						tsmx.add(btsmx);
					}
					// 20190929，杨文彦，餐盒处理不再使用套餐主项关联餐盒设置，改成使用明细关联设置
//					if (orderInfo.optInt("diningWay", 1) != 1 && 0 != gro.optDouble("foodbox_amount", 0)) {
					if (0 != gro.optDouble("foodbox_amount", 0)) {
						boolean tpkF = takeoutPackagingFee(gro, kdzdbh, gro.optInt("number"), rMap.get("czybh"));
						if (!tpkF) {
							result_ = -2;
							break;
						}
					}
				}
//				
				if (result_ != 0) {
					break;
				}
				result_ = orderPrecheckMapper.addCmByZs(kdzdbh, clmxidi, 1, "99", "", "", Math.random() * 10000 + "", item.optString("remark"), 0);
//				result_ = orderPrecheckMapper.addCmNewByZs(kdzdbh, item.optInt("did"), 1, "99", "", "", Math.random() * 10000 + "", "", psjgsdk.getJgxh() + "", psjgsdk.getJgtxbh(), item.optString("remark"));
				if (result_ != 0) {
					break;
				}
			}
//			// 20190929，杨文彦，餐盒处理不再使用套餐主项关联餐盒设置，改成使用明细关联设置
//			if (orderInfo.optInt("diningWay", 1) != 1 && 0 != item.optDouble("foodbox_amount", 0)) {
//				takeoutPackagingFee(item, kdzdbh, item.optInt("number"), rMap.get("czybh"));
//			}
		}
		
		if(orderInfo.optInt("diningWay", 1) == 3){
			result_ = insertSendAmount(kdzdbh, orderInfo.optDouble("send_amount", 0));
			if(result_ == -1){
				resultMsg = "未添加小程序配送费，请添加后重新下单！";
			}
		}
		
		// 保存菜品数据
		if (bymx.size() > 0) {
			orderPrecheckMapper.insertBtYdxm2(bymx);
		}
		// 保存可选套餐明细数据
		if (tsmx.size() > 0) {
			orderPrecheckMapper.insertBtTcselectmx(tsmx);
		}
					
		if (result_ == 0 || result_ == 1) {
			resultFlag = "true";
		} else if (result_ == -502 || result_== -500) {
			resultFlag = "false";
			resultMsg = "有菜品被沽清，请门店确认";
		} else if (result_ == -1) {
			resultFlag = "false";
		} else if (result_ == -2) {
			resultFlag = "false";
			resultMsg = "餐盒数据不匹配，请重新同步后再下单；";
		} else if (result_ == -3) {
			resultFlag = "false";
		} else {
			resultFlag = "false";
			resultMsg = "未知错误[code="+result_+"]";
		}
		resultMap.put("resultFlag", resultFlag);
		resultMap.put("resultMsg", resultMsg);
		return resultMap;
	}
	
	/**
	 * 创建账单
	 * @return
	 */
	public Map<String, String> createBill(JSONObject orderInfo, String kdzdbh, String yddh, String bbrq, BillNoData billNoData) {
		Map<String, String> rMap = new HashMap<String, String>();
		
		String jtbh = "99";// 机台号

		int bcid = 0; // 班次id
		TqJtztk jtzt = firstPayMapper.getJtZtk(DateUtil.parseDate(bbrq));
		String ygdlcs = jtzt.getYgdlcs();
		String czybh = jtzt.getRybh();// 操作员编号

		TsGgcsk yyms = firstPayMapper.getGgcsToWs("MDYYMS");
		String zwbh = orderInfo.optString("tableno", "");
		String qch = "";
		if (null != yyms && "3".equals(yyms.getSdnr())) {
			bcid = firstPayMapper.getBcid(DateUtil.parseDate(bbrq));
		} else {
			bcid = Integer.parseInt(jtzt.getYl1());
		}
		
		if(null == zwbh || "".equals(zwbh)){
//			qch = firstPayMapper.getbillid("", "TQ_ZDK", "QCH");// 取餐号
			qch = billNoData.getQch();
			zwbh = "";
		}

//		String lsdh = createBh(jtbh, "TQ_ZDK", "LSDH");
		String lsdh = billNoData.getLsdh();

		int ktbcid = bcid;

		TqZdk tqZdk = new TqZdk();
		tqZdk.setKdzdbh(kdzdbh);
		tqZdk.setLsdh(lsdh);
		tqZdk.setJzcs(0);
		tqZdk.setKtskjh(jtbh);
		tqZdk.setFwyh(czybh);
		tqZdk.setKtczry(czybh);
		tqZdk.setKtsj(new Date());
		tqZdk.setKdbbrq(DateUtil.parseDate(bbrq));
		tqZdk.setJzsx("ZDSX_WJ");
		tqZdk.setSource("XCX");
		tqZdk.setCbid(-1);
		tqZdk.setXfks(1);
		tqZdk.setDyzdcs(1);
		tqZdk.setYddh(yddh);
		// 20191012，杨文彦，处理是否预约标记
		if (orderInfo.optString("isDelayedMeal", "0").equals("1")) {
			tqZdk.setYl1("1");
			String yysj = orderInfo.optString("mealtime", "201901011200");
			yysj = yysj.substring(0, 4) + "-" + yysj.substring(4, 6) + "-" + yysj.substring(6, 8) + " " + yysj.substring(8, 10) + ":"  + yysj.substring(10, 12) + ":00";
			tqZdk.setYl2(yysj);
		} else {
			tqZdk.setYl1("0");
			tqZdk.setYl2("2019-01-01 12:00:00");
		}
		if (orderInfo.optInt("diningWay", 1) == 2) {
			tqZdk.setXsms("XSMS_WM");
		} else {
			tqZdk.setXsms("XSMS_TS");
		}

		tqZdk.setYgdlcs(ygdlcs + "");
		tqZdk.setZkl(100);
		tqZdk.setYhfsbh(null);
		tqZdk.setKtbcid(ktbcid);
		tqZdk.setZdzt("");
		tqZdk.setQch(qch);
		tqZdk.setZwbh(zwbh);

		tqZdkMapper.insert(tqZdk);

		rMap.put("qch", qch);
		rMap.put("bcid", bcid + "");
		rMap.put("czybh", czybh);
		rMap.put("jtbh", jtbh);
		
		return rMap;
	}
	
	
	public String createBh(String jtbh, String bmc, String zdmc) {
		jtbh = jtbh + "9";
		TsBmkzk tsBmkzKdzdbh = firstPayMapper.getBh(bmc, zdmc); // 获取当前编码对象
		String oldBh = tsBmkzKdzdbh.getNr(); // 当前编码
		int bhLength = 10; // 默认编码长度
		if ("LSDH".equals(zdmc)) {
			bhLength = 4; // 流水单号默认长度4
		}
		if ("KDZDBH".equals(zdmc) || "JZZDBH".equals(zdmc)) {
			bhLength = 10; // 账单号，结帐单号默认长度10
		}
		if (null != tsBmkzKdzdbh.getCdzd() && !"".equals(tsBmkzKdzdbh.getCdzd())) {
			TsGgcsk ggcsws = firstPayMapper.getGgcsToWs(tsBmkzKdzdbh.getCdzd());
			if (null != ggcsws.getSdnr() && !"".equals(ggcsws.getSdnr())) {
				bhLength = Integer.parseInt(ggcsws.getSdnr()); // 实际编码长度
			}
		}
		String newBh = (Long.parseLong(oldBh) + 1) + ""; // 新编码
		firstPayMapper.updateBh(newBh, bmc, zdmc); // 更新编码
		// 补全编码
		NumberFormat nf = NumberFormat.getInstance();
		nf.setGroupingUsed(false);
		bhLength = bhLength - 1;
		
		nf.setMaximumIntegerDigits(bhLength);
		nf.setMinimumIntegerDigits(bhLength);

		newBh = jtbh + nf.format(Integer.parseInt(newBh));
		
		return newBh;
	}
	
	
	
	@Transactional
	public Data orderStatus(JSONObject orderData) {
		Data data = new Data();
		String outOrderId = orderData.optString("out_order_id");
		String yddh = Constant.BILL_PREFIX + outOrderId;
		TqZdk zdk = orderPrecheckMapper.getZdk(yddh);
		
		data.setSuccess(1);
		data.setMsg("未生成账单");
		
		if (null == zdk) {
			data.setSuccess(0);
			data.setMsg("未生成账单");
		} else if ("ZDSX_WJ".equals(zdk.getJzsx())) {
			data.setSuccess(0);
			data.setMsg("账单未关闭");
		} else if ("ZDSX_YJ".equals(zdk.getJzsx())) {
			JSONObject jo = new JSONObject();
			jo.put("identify", outOrderId);
			jo.put("meal_number", zdk.getQch());
			jo.put("tableno", zdk.getZwbh());
			
			data.setData(data);
			data.setSuccess(1);
			data.setMsg("账单已完成");
		} else {
			data.setSuccess(0);
			data.setMsg("未生成账单");
		}
		return data;
	}
	
	
	public boolean firstPay(String outOrderIdInDB, JSONObject payInfos, String kdzdbh, String bbrq, double yhje, Map<String, String> rMap, String cwlxbh) {
		JSONArray payInfoArr = payInfos.optJSONArray("pay_info");
		boolean aoflag = true;
		TqJtztk jtzt = firstPayMapper.getJtZtk(DateUtil.parseDate(bbrq));
		double storedGivePay = 0;

		boolean isEmployee = false;

		for (int i = 0; i < payInfoArr.size(); i++) {
			JSONObject payInfo = payInfoArr.getJSONObject(i);
			String source = payInfo.optString("source");
			String fkfsmcs2 = "";
			String yl3 = "";
			
			if (source.equals("promotion")) {
				String promotion_source = payInfo.optString("promotion_source");
				TsYhfssdk yhfs = null;
				if ("weixin".equals(promotion_source)) {
					yhfs = orderPrecheckMapper.getYhfs("62");
				} else if ("alipay".equals(promotion_source)) {
					yhfs = orderPrecheckMapper.getYhfs("61");
				} else if ("pay_coupon".equals(promotion_source)) {
					yhfs = orderPrecheckMapper.getYhfs("65");
				} else {
					LOGGER.info("不识别的券来源：" + promotion_source);
					return false;
				}
				
				if (null != yhfs) {
					JSONArray promotionList = payInfo.optJSONArray("promotion_list");
					if (null != promotionList && promotionList.size() > 0) {
						for (int j = 0; j < promotionList.size(); j++) {
							JSONObject pl = promotionList.getJSONObject(j);
							double disAmount = ArithUtil.div(pl.optDouble("amount", 0), 100, 2);
							//"settle_amount"  //结算优惠//50优惠券, 优惠券结算10
							double settleAmount=pl.optDouble("settle_amount",0);
							insertDiscount(kdzdbh, disAmount, bbrq, rMap.get("bcid"), rMap.get("jtbh"), yhfs,settleAmount);
							yhje = ArithUtil.add(yhje, disAmount);
						}
					} else {
						double disAmount = payInfo.optDouble("amount", 0);
						double settleAmount=payInfo.optDouble("settle_amount",0);
						insertDiscount(kdzdbh, disAmount, bbrq, rMap.get("bcid"), rMap.get("jtbh"), yhfs,settleAmount);
						yhje = ArithUtil.add(yhje, disAmount);
					}
					tqZdkMapper.updateZrje(kdzdbh, yhje);
					firstPayMapper.calcmoney(kdzdbh);
					firstPayMapper.zRtr(kdzdbh);
				} else {
					LOGGER.info("未添加“支付宝商家优惠”或“微信商家优惠”");
					return false;
				}
			} else {
				if (source.equals("balance")
						||"balance_emp".equals(source)) { //员工会员储值
					yl3 = "ERP_FKFS_PUBAPP";
					storedGivePay = ArithUtil.add(storedGivePay, ArithUtil.sub(payInfo.optDouble("amount"), payInfo.optDouble("storepay")));

					if("balance_emp".equals(source)){
						isEmployee=true;
					}


				} else if (source.equals("credit")) {
					yl3 = "ERP_FKFS_PUBAPPCRE";
				} else if (source.equals("pay_coupon")) {
					yl3 = "ERP_FKFS_PUB_COUPON";
				} else {
					fkfsmcs2 = source;
				}

				TsFkfssdk fkfs = firstPayMapper.getFkfsByZs(fkfsmcs2, yl3);
				if (fkfs == null) {
					fkfs = firstPayMapper.getFkfsByCode("2020");
				}
				BigDecimal actualmoney = new BigDecimal("0.00");
				actualmoney = BigDecimal.valueOf(payInfo.optDouble("amount"));
				// 调用付款存储过程，生成账单付款记录wwq
				String fkbz = "";
				if (payInfo.containsKey("outtradeno")) {
					fkbz = payInfo.optString("outtradeno");
				}
				AccountsOrder ao = firstPayMapper.accountsOrder(kdzdbh, fkfs.getId(), actualmoney, 1, "", "", "", fkbz, "99", jtzt.getRybh());
				insertPayments(outOrderIdInDB, fkfs, actualmoney.doubleValue(), DateUtil.getNowDateYYDDMMHHMMSS());
				if (ao != null && "0".equals(ao.getA()) && aoflag) {
					aoflag = true;
				} else {
					aoflag = false;
					break;
				}
			}
		}
		if (payInfoArr.size() == 0) {
			TsFkfssdk fkfs = firstPayMapper.getFkfsByCode("2020");
			firstPayMapper.accountsOrder(kdzdbh, fkfs.getId(), new BigDecimal(0), 1, "", "", "", "", "99", jtzt.getRybh());
			insertPayments(outOrderIdInDB, fkfs, 0, DateUtil.getNowDateYYDDMMHHMMSS());
			aoflag = true;
		}
		
//		POS_MEMBALANCE_USEDIS 会员储值付款记录优惠方式	0 不记录优惠，1按照会员系统传过来的优惠拆分记录优惠，2李先生按比例拆分		
		if ("1".equals(InitDataListener.ggcsMap.get("POS_MEMBALANCE_USEDIS"))) {
			List<TqFklslsk> balanceList = firstPayMapper.findBalanceList(kdzdbh, "3001");
//			2：众赏，3：百福
			DishVo dsfyhB = null;
			if ("2".equals(cwlxbh)) {
				if(isEmployee){
					dsfyhB = firstPayMapper.getDsfyh("119"); //新增 和合谷 众赏员工储值卡优惠 119
				}else {
					dsfyhB = firstPayMapper.getDsfyh("75");
				}
			}
			if ("3".equals(cwlxbh)) {
				 dsfyhB = firstPayMapper.getDsfyh("74");
			}
			if (null != dsfyhB && balanceList.size() > 0 && storedGivePay != 0) {
				acewillDiscount(kdzdbh, dsfyhB, balanceList, storedGivePay);
			}
		}
		
		return aoflag;
	}
	
	public void accountsOrder(JSONObject orderInfo, String kdzdbh, String bbrq, String bcid, String czybh, BillNoData billNoData, String cwlxbh) {
//		String jzzdbh = createBh("99", "TQ_ZDK", "JZZDBH");
		String jzzdbh = billNoData.getJzzdbh();
		Date jzjssj = new Date();
		JSONObject ordermemo = orderInfo.optJSONObject("ordermemo");
		String zdbz = "";
		if(null != ordermemo){
			zdbz = ordermemo.optString("text");
		}
		firstPayMapper.updateZdkZs(zdbz, kdzdbh, jzzdbh, DateUtil.parseDate(bbrq), jzjssj, 1, "99", czybh, "ZDSX_YJ", jzjssj, jzjssj, Integer.parseInt(bcid), orderInfo.optInt("people"), cwlxbh);
		firstPayMapper.updateWdkZs(kdzdbh, jzzdbh, "99", DateUtil.parseDate(bbrq), Integer.parseInt(bcid));
		firstPayMapper.updateFklslskZs(kdzdbh, jzzdbh, Integer.parseInt(bcid));
	}

	public void insertDiscount(String kdzdbh, double yhje, String bbrq, String bcid, String skjh, TsYhfssdk yhfs,Double yhqjsje) {
		TqWdk tqwdk = orderPrecheckMapper.getNewWdk(kdzdbh);
		TqWdk tqwdkIn = new TqWdk();
		tqwdkIn.setKdzdbh(kdzdbh);
		tqwdkIn.setClmxid(-1);
		tqwdkIn.setCmbh("YH" + yhfs.getYhfsbh());
		tqwdkIn.setCmmc1(yhfs.getYhfsmc1());
		tqwdkIn.setCmmc2(yhfs.getYhfsmc2());
		tqwdkIn.setDwbh("");
		tqwdkIn.setYzwbh("");
		tqwdkIn.setZwbh("");
		tqwdkIn.setTcfs("");
		tqwdkIn.setTcbl(0);
		tqwdkIn.setTcje(0);
		tqwdkIn.setFzsl(0);
		tqwdkIn.setFzje(0);
		tqwdkIn.setZdsj(0);
		tqwdkIn.setXdh("");
		tqwdkIn.setXdhshry("");
		tqwdkIn.setFwyh("");
		tqwdkIn.setCbdj(new BigDecimal(0));
		tqwdkIn.setCmdj(new BigDecimal(0));
		tqwdkIn.setCmsl(1);
		tqwdkIn.setCmje(new BigDecimal(ArithUtil.mul(yhje, -1)));
		tqwdkIn.setSjje(new BigDecimal(0));
		tqwdkIn.setYhje(new BigDecimal(0));
		tqwdkIn.setDpzkje(new BigDecimal(0));
		tqwdkIn.setZrje(new BigDecimal(yhje));
		tqwdkIn.setZkzt("N");
		tqwdkIn.setXlzkzt("N");
		tqwdkIn.setZkl(100);
		tqwdkIn.setYhfsid(yhfs.getId());
		tqwdkIn.setYhfsbh(yhfs.getYhfsbh());
		tqwdkIn.setYhfs(yhfs.getYhsx());
		tqwdkIn.setTszt("N");
		tqwdkIn.setQczt("N");
		tqwdkIn.setTcdch(0);
		tqwdkIn.setTmbj(new Date());
		tqwdkIn.setDcxh(tqwdk.getDcxh() + 1);
		tqwdkIn.setFsbbrq(DateUtil.parseDate(bbrq));
		tqwdkIn.setFsbcid(Integer.parseInt(bcid));
		tqwdkIn.setFsskjh(skjh);
		tqwdkIn.setSyyhfkfsid(tqwdk.getSyyhfkfsid() + 1);
		tqwdkIn.setScbj(0);
		tqwdkIn.setYongjje(0);
		tqwdkIn.setYongjzkl(100);
		tqwdkIn.setYhxh(0);
		tqwdkIn.setWdbz("");
		tqwdkIn.setKwbz("");
		tqwdkIn.setYhqjsje(yhqjsje);
		tqWdk.insert(tqwdkIn);
	}
	
	public void insertDiscount(String kdzdbh, double yhje, String bbrq, String bcid, String skjh, TsYhfssdk yhfs) {
		insertDiscount(kdzdbh,yhje,bbrq,bcid,skjh,yhfs,0d);
	}
	
	@Override
	public Data posCancelBill(JSONObject json) {
		Data data = new Data();
//		String propertiesPath = "/application.properties";
//		String zsApi = PropertiesUtil.readValueForClasses(propertiesPath, "zhongshang_api");
		String tsyddbh = json.optString("yddbh");
		String yddh = tsyddbh.replaceFirst("TS", "");

		String zsApi = shopBaseInfoMapper.getTthird("ZHONGS", "URL");
		// 组装数据，发送
		String r = "erro";
		if(null != zsApi && !"".equals(zsApi)){
			r = sync(new MiniAppZsSyncRefundOrder(shopBaseInfoMapper, yddh, 4), zsApi);
		}

		if ("erro".equals(r) || null == r || "n++".equals(r)) {
			data.setSuccess(1);
			data.setMsg("链接失败，请检查网络状态！");
			return data;
		} else {
			JSONObject jo = JSONObject.fromObject(r);
			JSONObject result = jo.optJSONObject("result");
			String status = result.optString("status");
			String code = result.optString("code");
			String msg = result.optString("msg");
			if ("1".equals(status) || ("0".equals(status) && "SMS005".equals(code))) {
				data.setSuccess(0);
				data.setMsg("退单成功！");
				return data;
			} else {
				data.setSuccess(1);
				data.setMsg(msg);
				return data;
			}
		}
	}
	
	public static String sync(IData<?> data, String zsApi) {
		String rest = "erro";	
		try {
			// 拼接域名+接口地址
			String url = zsApi + data.getUrl();
			LOGGER.debug(url);
			// 发
			rest = SendRequest.zsPost(url,data.getParams());
			LOGGER.info("rest：" + rest);
		} catch (Exception e) {
			LOGGER.error(e.getMessage(),e);
		}
		LOGGER.debug(rest);
		
		return rest;
	}
	
	public int getClmxid(JSONObject item) {
		String clmxid = orderPrecheckMapper.getClmxidByXmid(item.optInt("did"));
		int clmxidi = 0;
		if (null != clmxid) {
			clmxidi = Integer.parseInt(clmxid);
		}
		return clmxidi;
	}
	
	
	
	public void jointYdd(JSONObject orderInfo, String outOrderIdInDB, String ydbcid, String qch) {
		JSONObject ordermemo = orderInfo.optJSONObject("ordermemo");
		String tableno = orderInfo.optString("tableno");
		BtYdd by = new BtYdd();
		Shops shops = shopBaseInfoMapper.findShopsData();

		by.setYddh(outOrderIdInDB);
		by.setYdrs(orderInfo.optInt("people"));
		by.setMen(0);
		by.setWomen(0);
		by.setEldernum(0);
		by.setChildnum(0);
		by.setShops_id(shops.getSid() + "");
		by.setTotalprice(orderInfo.optDouble("order_amount"));
		by.setYdrq(DateUtil.getNowDateYYDDMM());
		by.setQdsj(DateUtil.getNowDateYYDDMMHHMMSS());
		by.setYdbc(ydbcid);
		by.setDdzt("5");
		if (null != ordermemo) {
			by.setKwxh(ordermemo.optString("text"));
		}
		by.setZlbh(tableno);
		// 就餐时间
		if (orderInfo.containsKey("mealtime")) {
			String mealtime = orderInfo.optString("mealtime", ""); // 可以为空的值默认为""
			by.setMealtime(mealtime);
		}
		// 下单来源 “0”为门店下单；“1”为线上下单；默认为“0”。V1.1新增
		if (orderInfo.containsKey("channel")) {
			int channel = orderInfo.optInt("channel", 0); // 默认为0
			by.setChannel(channel);
		}
		// 销售模式
		if (orderInfo.containsKey("diningWay")) {
			int diningWay = orderInfo.optInt("diningWay", 1); // 默认为"1"堂食,2外带，3外卖
			by.setDiningway("XSMS_TS");
			by.setYl5("1");
			if (diningWay == 2) {
				by.setDiningway("XSMS_WM"); // "XSMS_WM"是外带，不是外卖
				by.setYl5("2");
			}
			if (diningWay == 3) {
				by.setYl5("3");
				if (orderInfo.containsKey("mealtime")) {
					String mealtime = orderInfo.optString("mealtime", "201901011200"); // 可以为空的值默认为""
					mealtime = mealtime.substring(0, 4) + "-" + mealtime.substring(4, 6) + "-" + mealtime.substring(6, 8) + " " + mealtime.substring(8, 10) + ":"  + mealtime.substring(10, 12) + ":00";
					by.setMealtime(mealtime);
				} else {
					by.setMealtime("立即配送");
				}
			}
		}
		// 外卖联系人
		if (orderInfo.containsKey("lxr")) {
			by.setLxr(orderInfo.optString("lxr", ""));
		}
		// 外卖联系电话
		if (orderInfo.containsKey("lxrdh")) {
			String mobile = orderInfo.optString("lxrdh", "***********");
			by.setLxrdh(mobile.replaceAll("(\\d{3})\\d{4}(\\d{4})","$1****$2"));
			by.setMobile(mobile);
		}
		// 外卖配送地址
		if (orderInfo.containsKey("member_address")) {
			by.setMember_address(orderInfo.optString("member_address", ""));
		}
				
		by.setShrbh(qch);
		orderPrecheckMapper.insertBtYddByZs(by);
	}
	
	
	public BtYdxm2 jointYdmx(JSONObject jo, String outOrderIdInDB, String xmsx, int dcxh) {
		BtYdxm2 ydmx2 = new BtYdxm2();
		int number = 1;
		if("单品".equals(xmsx)){
			number = jo.optInt("number");
		}
		ydmx2.setIsactive(0);
		ydmx2.setYddh(outOrderIdInDB);
		ydmx2.setXmid(jo.optInt("did"));
//		ydmx2.setXmbh(jo.optString("dishsno"));
		ydmx2.setXmmc(jo.optString("name"));
		ydmx2.setXmsx(xmsx);
		// 单价还是菜品原价
		ydmx2.setXmdj(new BigDecimal(jo.optString("price")));
		ydmx2.setXmsl(number);
		ydmx2.setZkl(100);
		// 菜品实结金额
		ydmx2.setTotalprice(new BigDecimal((jo.optDouble("price") + jo.optDouble("aprice")) * number));
		ydmx2.setDwbh(jo.optString("dishunit"));
		ydmx2.setKwbh(jo.optString("remark"));
		// 菜品金额使用原价*数量
		ydmx2.setCmje(new BigDecimal((jo.optDouble("price") + jo.optDouble("aprice")) * number));
		ydmx2.setTcbh("");
		ydmx2.setTcdch(0);
		ydmx2.setFzsl(0);
		ydmx2.setDcxh(dcxh);
		ydmx2.setFzje(new BigDecimal(0));
		BigDecimal decimal = new BigDecimal((jo.optDouble("price") + jo.optDouble("aprice") + jo.optDouble("discountsprice")) * number);
		BigDecimal setScale = decimal.setScale(2, BigDecimal.ROUND_HALF_UP);
		ydmx2.setYl3(setScale + "");
		
		return ydmx2;
	}

	public void insertPayments(String outOrderIdInDB, TsFkfssdk fkfs, double actualmoney, String addtime) {
		BtPayments btpay = new BtPayments();
		btpay.setYddh(outOrderIdInDB);
		btpay.setPay_channel("众赏小程序点餐");
		btpay.setPay_name(fkfs.getFkfsmc1());
		btpay.setPay_no(fkfs.getFkfsbh());
		btpay.setVcardid("");
		btpay.setVtele("");
		btpay.setPay_count(actualmoney);
		btpay.setPay_bb(0);
		btpay.setVocount(0);
		btpay.setFzzhje(0);
		btpay.setFlhl(0);
		btpay.setPay_memo("");
		btpay.setOpttime(addtime);
		// 生成预定付款记录
		firstPayMapper.insertBtPayments(btpay);
	}
	
	public boolean takeoutPackagingFee(JSONObject item, String kdzdbh, int number, String xsyh) {
		TsPsjgsdk psjgsdk = tsPsjgsdkMapper.findLocalShopConfig();
		String cmid = orderPrecheckMapper.getFoodBoxSetByCmid(item.optInt("did"));
		if (null != cmid && !"".equals(cmid)) {
			orderPrecheckMapper.addCmNewByZs(kdzdbh, Integer.parseInt(cmid), number, "99", xsyh, "", Math.random() * 10000 + "", "", psjgsdk.getJgxh() + "", psjgsdk.getJgtxbh(), "", 6);
			return true;
		} else {
			return false;
		}
	}
	
//	@Transactional
//    public BillNoData getBillNoData(){
//        BillNoData billNoData = new BillNoData();
//        LOGGER.info("获取新账单号开始"+ this);
//        StringBuffer kdzdbh = new StringBuffer(firstPayMapper.getbillid("99","TQ_ZDK","KDZDBH"));
//        StringBuffer jzzdbh = new StringBuffer(firstPayMapper.getbillid("99","TQ_ZDK","JZZDBH"));
//        StringBuffer lsdh = new StringBuffer(firstPayMapper.getbillid("99","TQ_ZDK","LSDH"));
//        StringBuffer qch = new StringBuffer(firstPayMapper.getbillid("99","TQ_ZDK","QCH"));
//        kdzdbh.replace(2,3,"9");
//        jzzdbh.replace(2,3,"9");
//        lsdh.replace(2,3,"9");
//        billNoData.setKdzdbh(kdzdbh.toString());
//        billNoData.setJzzdbh(jzzdbh.toString());
//        billNoData.setLsdh(lsdh.toString());
//        billNoData.setQch(qch.toString());
//        LOGGER.info("获取新账单号成功" + billNoData.toString());
//        return  billNoData;
//    }
	@Transactional
	public Data checkClmx(JSONObject orderData) {
		// 创建返回数据对象
		Data data = new Data();
		// 小程序订单号
		String outOrderId = orderData.optString("out_order_id");
		JSONObject orderInfo = orderData.optJSONObject("order_info");		
		// 保存平台预定账单编号
		data.setYddbh(outOrderId);
		Map<Integer, Double> dishPrice = new HashMap<Integer, Double>();
		try {
			List<Integer> itemIdList = new ArrayList<Integer>();
			JSONArray normalitems = new JSONArray();
			if (orderInfo.has("normalitems")) {
				normalitems = orderInfo.getJSONArray("normalitems");
			}
			JSONArray setmeal = new JSONArray();
			if (orderInfo.has("setmeal")) {
				setmeal = orderInfo.getJSONArray("setmeal");
			}
			for (int i = 0; i < normalitems.size(); i++) {
				JSONObject item = normalitems.getJSONObject(i);
				itemIdList.add(item.optInt("did"));
				dishPrice.put(item.optInt("did"), item.optDouble("price"));
				
			}
			for (int i = 0; i < setmeal.size(); i++) {
				JSONObject item = setmeal.getJSONObject(i);
				itemIdList.add(item.optInt("did"));
				dishPrice.put(item.optInt("did"), item.optDouble("price"));
			}
			List<String> cmmcList = orderPrecheckMapper.checkClmx(itemIdList);
			if (cmmcList.size() > 0) {
				String itemName = "";
				for(String cmmc : cmmcList){
					itemName = itemName + "《" + cmmc + "》";
				}
				data.setSuccess(0);
				data.setMsg("菜品：" + itemName + "不在餐谱内，请重新同步基础数据;");
				data.setData(new HashMap<String, Object>());
			} else {
				List<Dish> dishList = orderPrecheckMapper.checkPrice(itemIdList);
				String priceMsg = "";
				for(Dish dish : dishList){
					double a = dishPrice.get(dish.getXmid());
					float  b = dish.getPrice();
					if ((float)a != b) {
						priceMsg = priceMsg + "《" + dish.getName() + "->pos:" + dish.getPrice() + ",xcx:" + dishPrice.get(dish.getXmid()) + "》";
					}
				}
				if (priceMsg.length() > 0) {
					data.setSuccess(0);
					data.setMsg("小程序菜品价格与pos不一致，请重新同步：" + priceMsg);
					data.setData(new HashMap<String, Object>());
				} else {
					data.setSuccess(1);
					data.setMsg("菜品无误");
					data.setData(new HashMap<String, Object>());
				}
			}
			
			return data;
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			data.setSuccess(0);
			data.setMsg("系统错误:" + e);
			data.setData(new HashMap<String, Object>());
			return data;
		}
	}
	
	
	public int insertSendAmount(String kdzdbh, double sendAmount) {
		TqWdk tqwdk = orderPrecheckMapper.getNewWdk(kdzdbh);
		TsCmk saCmk = orderPrecheckMapper.getSendAmount("小程序外卖配送费");
		if (null != saCmk && null != tqwdk) {
			TqWdk tqwdkIn = new TqWdk();
			tqwdkIn.setKdzdbh(kdzdbh);
			tqwdkIn.setClmxid(-1);
			tqwdkIn.setCmid(saCmk.getCmid());
			tqwdkIn.setCmbh(saCmk.getCmbh());
			tqwdkIn.setCmmc1("小程序外卖配送费");
			tqwdkIn.setDwbh(saCmk.getDwbh());
			tqwdkIn.setYzwbh("");
			tqwdkIn.setZwbh("");
			tqwdkIn.setTcfs("");
			tqwdkIn.setTcbl(0);
			tqwdkIn.setTcje(0);
			tqwdkIn.setFzsl(0);
			tqwdkIn.setFzje(0);
			tqwdkIn.setZdsj(0);
			tqwdkIn.setXdh("");
			tqwdkIn.setXdhshry("");
			tqwdkIn.setFwyh("");
			tqwdkIn.setCbdj(new BigDecimal(0));
			tqwdkIn.setCmdj(new BigDecimal(sendAmount));
			tqwdkIn.setCmsl(1);
			tqwdkIn.setCmje(new BigDecimal(sendAmount));
			tqwdkIn.setSjje(new BigDecimal(sendAmount));
			tqwdkIn.setYhje(new BigDecimal(0));
			tqwdkIn.setDpzkje(new BigDecimal(0));
			tqwdkIn.setZrje(new BigDecimal(0));
			tqwdkIn.setYhfs("");
			tqwdkIn.setZkzt(saCmk.getSfzk());
			tqwdkIn.setXlzkzt(saCmk.getSfzk());
			tqwdkIn.setZkl(100);
			tqwdkIn.setTszt("N");
			tqwdkIn.setQczt("N");
			tqwdkIn.setCmsx("CMSX_DP");
			tqwdkIn.setCdbj("*");
			tqwdkIn.setXlid(saCmk.getXlid());
			tqwdkIn.setXlbh(saCmk.getXlbh());
			tqwdkIn.setTcdch(0);
			tqwdkIn.setTmbj(new Date());
			tqwdkIn.setDcxh(tqwdk.getDcxh() + 1);
			tqwdkIn.setFsbbrq(tqwdk.getFsbbrq());
			tqwdkIn.setFsbcid(tqwdk.getFsbcid());
			tqwdkIn.setFsskjh(tqwdk.getFsskjh());
			tqwdkIn.setScbj(1);
			tqwdkIn.setYongjje(0);
			tqwdkIn.setYongjzkl(100);
			tqwdkIn.setSyyhfkfsid(tqwdk.getSyyhfkfsid() + 1);
			tqwdkIn.setWdbz("");
			tqwdkIn.setKwbz("");
			tqWdk.insert(tqwdkIn);
			return 0;
		} else {
			return -1;
		}
	}
	
	public void commUseYhfs(TqZdk zdk, List<TqFklslsk> fklslskList, String skjh, String jzbbrq) {
		for (int i = 0; i < fklslskList.size(); i++) {
			TqFklslsk fklslsk = fklslskList.get(i);
			if ("N".equals(fklslsk.getSfsr())) {
				TsYhfssdk yhfssdk = firstPayMapper.getBjyhYh("BJSR" + fklslsk.getFkfsbh());
				if (null != yhfssdk) {
					UseYhfsParam useYhfsParam = new UseYhfsParam();
					useYhfsParam.setBillid(zdk.getKdzdbh());
					useYhfsParam.setOpType(91003);
					useYhfsParam.setYhfsId(yhfssdk.getId());
					useYhfsParam.setFklsid(fklslsk.getId());
					useYhfsParam.setSkjh(skjh);// 机号
					useYhfsParam.setJgtxbh(InitDataListener.organVo.getJgtxbh());// 价格体系编号
					useYhfsParam.setDisAmount(new BigDecimal(fklslsk.getFkje()));
					useYhfsParam.setBbrq(DateUtil.parseDate(jzbbrq));
					if ((i + 1) == fklslskList.size() && 0 != zdk.getDslj()) {
						useYhfsParam.setDslj(new BigDecimal(zdk.getDslj()));
						useYhfsParam.setHasDslj(true);
					}
					useYhfsParam.setInnerCalMoney(true);// 由接口内部使用均摊及账单金额重计算

					useYhfsApiService.CommUseYhfs(useYhfsParam);	
				}
			}
		}
	}
	
	
	public void acewillDiscount(String zdbh, DishVo dsfyh, List<TqFklslsk> fklsList, double storedGivePays) {
		// 拆分部分
		for (TqFklslsk fkls : fklsList) {
			UseYhfsParam useYhfsParam = new UseYhfsParam();
			useYhfsParam.setBillid(zdbh);
			useYhfsParam.setOpType(91002);
			useYhfsParam.setYhfsId(Integer.parseInt(dsfyh.getYhfsid()));
			useYhfsParam.setJzid(fkls.getJzid());
			useYhfsParam.setSkjh(fkls.getSkjh());// 机号
			useYhfsParam.setJgtxbh(InitDataListener.organVo.getJgtxbh());// 价格体系编号
			useYhfsParam.setDisAmount(new BigDecimal(storedGivePays));
			useYhfsParam.setBbrq(fkls.getJzbbrq());
			useYhfsParam.setInnerCalMoney(true);// 由接口内部使用均摊及账单金额重计算
			useYhfsApiService.CommUseYhfs(useYhfsParam);
		}
	}

	public Data shopStatus() {
		Data data = new Data();

		Map<String, String> bbrqMap = shopStatusMapper.findBbrq();
		String bbrq = DateUtil.getNowDateYYDDMM();
		if (null != bbrqMap && bbrqMap.size() != 0) {
			bbrq = bbrqMap.get("bbrq");
		}

		int start = shopStatusMapper.checkOpenStart(DateUtil.parseDate(bbrq));
		int end = shopStatusMapper.checkOpenEnd(DateUtil.parseDate(bbrq));
		int ld = shopStatusMapper.loginDlcs(DateUtil.parseDate(bbrq));
		int lt = shopStatusMapper.loginTccs(DateUtil.parseDate(bbrq));
		TsGgcsk il = firstPayMapper.getGgcsToWs("ISLOGINOUTING");
		if (null != il && "Y".equals(il.getSdnr())) {
			data.setSuccess(0);
			data.setMsg("门店正在盘点，请稍后再试");
		} else if (0 == start) {
			data.setSuccess(0);
			data.setMsg("门店未营业");
		} else if (0 < end) {
			data.setSuccess(0);
			data.setMsg("门店已打烊");
		} else if (ld <= lt) {
			data.setSuccess(0);
			data.setMsg("员工未登录");
		} else if (checkDysj(bbrq) > 0) {
			data.setSuccess(0);
			data.setMsg("系统日期已经大于营业日期，请做打烊后再进行当前操作！");
		} else {
			data.setSuccess(1);
			data.setMsg("正常营业");
		}
		return data;
	}

	/**
	 * 下单前检查是否打烊
	 *
	 * @param bbrq
	 * @return
	 */
	public int checkDysj(String bbrq) {
		String sys24yy = shopStatusMapper.getSys24yy();
		String dysjsz = shopStatusMapper.getDyxzsjd();
		Date bbrqD = DateUtil.parseDateAll(bbrq + " 00:00:00");
		if ("Y".equals(sys24yy)) {
			int day = DateUtil.daysBetween(bbrqD, DateUtil.parseDateAll(DateUtil.getNowDateYYDDMM() + " 00:00:00"));
			return day;
		} else {
			if ("".equals(dysjsz) || null == dysjsz) {
				dysjsz = "00:00:00";
			}
			Date bbrqDy = DateUtil.parseDateAll(bbrq + " " + dysjsz);
			Date dysj = DateUtil.parseDateAll(DateUtil.getNowDateYYDDMMHHMMSS());
			int day = DateUtil.daysBetween(DateUtil.getPlusDay(bbrqDy, 1), dysj);
			return day;
		}
	}

}
