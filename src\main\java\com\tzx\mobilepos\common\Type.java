package com.tzx.mobilepos.common;

public enum Type {
	REGISTER, // 注册
	LOGIN, // 登录
	LOGOUT, // 登出
	BASIC_DATA, // 数据同步
	CREATE_BILL, // 创建账单
	ORDERING, // 点菜下单
	CHECK_DISCOUNT, // 验证优惠是否可用
	BILL_DISCOUNT, // 账单优惠
	BILL_QUERY, // 账单查询
	BILL_ACCOUNTS, // 结账
	BILL_PAYMENT, // 付款
	REFUND_BILL, // 退单
	BILL_DETAIL_QUERY, // 查询账单明细
	ORDERING_ONE, // 每加一次菜品下一次单
	DISCOUNT_QUERY, // 查询优惠活动
	REFUND_ORDER, // 整单退单
	POS_PRINT_JNA, // jna调用pos动态库打印
	MEITUAN_VERIFY, // 美团验券
	LOGIN_SHIFT, // 更新登录班次
	CHECK_LIMIT, // 校验班结状态
	ADD_TASTE,// 添加口味
	UPDATE_PASSWORD,//更新密码
	MTQ_RETURN,//测试
	FIND_ACEWILL_MEMBER,//查询微生活会员信息
	REFUND_ACEWILL_MEMBER,//清除微生活会员信息
	CACHE_ACEWILL_MEMBER,//查询缓存的微生活会员信息
	ACEWILL_MEMBER_PREVIEW,//微生活会员，预消费
	ACEWILL_MEMBER_COMMIT,//微生活会员，提交消费
	EXCEPTION_BILL_QUERY,//异常账单查询
	EXCEPTION_BILL_HANDLER//异常账单处理
}
