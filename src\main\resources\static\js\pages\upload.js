$(function(){
    loaduploadloglist();
});
function loaduploadloglist() {
    //首先清空
    $("#uploadtbbody").empty();
    $.ajax({
        url : "/TzxMsg/TzxMsg/uploadloglist",
        type : "POST",
        async:false,
        cache:false,
        success : function(data) {
            //填充表格
            if (data.success){
                if (data.data.length == 0 ){
                    showmessage($("#uploademptymsg"));
                }else{
                    //开始加载数据
                    var tableStr = "";
                    var len = data.data.length;
                    for (var i = 0; i < len; i++) {
                        var runresult = "";
                        if (data.data[i].runresult=="0"){
                            runresult = "成功";
                        }else if (data.data[i].runresult=="1") {
                            runresult = "忽略";
                        }else{
                            runresult = "错误";
                        }
                        if (data.data[i].upprocess==""){
                            data.data[i].upprocess = "100%";
                        }
                        var processhtml = "<div class=\"progress-bar\" role=\"progressbar\" aria-valuenow=\"100" +
                            "\" aria-valuemin=\"0\" aria-valuemax=\"100\" style=\"width:"+ data.data[i].upprocess+  "\">"
                            +data.data[i].upprocess+
                            "</div>";
                        var filename = typeof(data.data[i].uploadfilename) == "undefined"?"":data.data[i].uploadfilename;

                        tableStr = tableStr + "<tr><td >" + data.data[i].command  + "</td>" +
                            "<td >" + data.data[i].bbrq  + "</td>" +
                            "<td >" + data.data[i].starttime  + "</td>" +
                            "<td >" + data.data[i].endtime  + "</td>" +
                            // "<td >" + runresult  + "</td>" +
                            "<td >" + data.data[i].exceptionmsg  + "</td>" +
                            "<td >" + filename  + "</td>" +
                            + "</tr>";
                    }
                    $("#uploadtbbody").html(tableStr);

                }
            }else{
                showmessage($("#uploadmsg"));
            }
        },
        error : function() {
            showmessage($("#uploadmsg"));
        }
    });
}
function showmessage(Element) {
    Element.css("display","block");

}