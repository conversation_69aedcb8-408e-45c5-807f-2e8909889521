package com.tzx.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.util.Utils;
import com.tzx.TzxApplication;
import com.tzx.receiver.common.utils.DBUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.support.rowset.SqlRowSet;
//import sun.security.ssl.HandshakeInStream;

import java.io.InputStream;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Properties;

/**
 * Created by <PERSON>x<PERSON> on 2019-11-24.
 */
public class MatualDbUtil {
    private static DruidDataSource dataSource=null;
    private static JdbcTemplate jdbcTemplate =null;
    private static Logger LOGGER = LoggerFactory.getLogger(MatualDbUtil.class);

    public static HashMap<String, String> getMatualParams() {
        return MatualParams;
    }

    private static HashMap<String,String> MatualParams = new HashMap<String, String>();
    public static JdbcTemplate getJdbcTemplate() throws Exception {
        if(null == jdbcTemplate){
            jdbcTemplate = new JdbcTemplate();
            MatualDbUtil.GetDbConnect();
            jdbcTemplate.setDataSource(dataSource);
        }
        return jdbcTemplate;
    }
    /**
     * 构造函数完成数据库的连接和连接对象的生成
     * @throws Exception
     */
    public MatualDbUtil(){

    }
    public static void configFromPropety(Properties properties,DruidDataSource ds) {
        String property = properties.getProperty("jdbc.url");
        if(property != null) {
            ds.setUrl(property);
        }

        property = properties.getProperty("jdbc.username");
        if(property != null) {
            ds.setUsername(property);
        }

        property = properties.getProperty("jdbc.password");
        if(property != null) {
            ds.setPassword(property);
        }
        property = properties.getProperty("jdbc.driver-class-name");
        if(property != null) {
            ds.setDriverClassName(property);
        }

    }
    public static void GetDbConnect() throws Exception  {
        try{
            if(dataSource==null){
                InputStream in = MatualDbUtil.class.getClassLoader().getResourceAsStream("application.properties");
                Properties prop = new Properties();
                prop.load(in);
                dataSource=new DruidDataSource();
                configFromPropety(prop,dataSource);

//                //配置初始化大小、最小、最大
                dataSource.setInitialSize(1);
                dataSource.setMinIdle(1);
                dataSource.setMaxActive(1);
//                //连接泄漏监测
                dataSource.setRemoveAbandoned(true);
                dataSource.setRemoveAbandonedTimeout(30);
//                //配置获取连接等待超时的时间
                dataSource.setMaxWait(20000);
//                //配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
                dataSource.setTimeBetweenEvictionRunsMillis(20000);
//                //防止过期
                dataSource.setValidationQuery("SELECT 'x'");
                dataSource.setTestWhileIdle(true);
                dataSource.setTestOnBorrow(true);
            }
        }catch(Exception e){
            throw e;
        }
    }
    /**
     * 取得已经构造生成的数据库连接
     * @return 返回数据库连接对象
     * @throws Exception
     */
    public static Connection getConnect() throws Exception{
        Connection con=null;
        try {
            GetDbConnect();
            con=dataSource.getConnection();
        } catch (Exception e) {
            throw e;
        }
        return con;
    }
    public static void main(String[] args) throws Exception {
        MatualDbUtil dbConnect = new MatualDbUtil();
        Connection connection = dbConnect.getConnect();
        String sql = "SELECT 1+1 from dual";
        PreparedStatement ps = connection.prepareStatement(sql);
        ResultSet resultSet = ps.executeQuery();
        if(resultSet.next()){
            System.out.println(resultSet.getInt(1));
        }
    }
    public static String getGGCSK(String sdbt) {
        try {
            //应该以下发库为准，否则更换地址后，要做一次同步菜品才能生效
            String sql = "select sdnr from ts_ggcsk where sdbt = ?";
            SqlRowSet sqlRowSet = MatualDbUtil.getJdbcTemplate().queryForRowSet(sql, sdbt);
            if (sqlRowSet.next()) {
                return sqlRowSet.getString(1) == null ? "" : sqlRowSet.getString(1);
            } else {
                return "";
            }

        } catch (Exception e) {
            return "SYS_CONERR_999";
        }
    }
    public static String getTempSql() {
        try {
            //应该以下发库为准，否则更换地址后，要做一次同步菜品才能生效
            String sql = "select 1";
            SqlRowSet sqlRowSet = MatualDbUtil.getJdbcTemplate().queryForRowSet(sql);
            if (sqlRowSet.next()) {
                return sqlRowSet.getString(1) == null ? "" : sqlRowSet.getString(1);
            } else {
                return "";
            }

        } catch (Exception e) {
            return "SYS_CONERR_999";
        }
    }
    public static void initMatualParams() throws Exception{
       String rtStr = getGGCSK("SHOWTAKEOUT");
       if(rtStr.equals("SYS_CONERR_999")){
           throw new Exception("SYS_CONERR_999");
       }else{
           MatualParams.put("SHOWTAKEOUT",rtStr);
       }
    }
    public static void checkDBConnect(){
        int retryCount = 0;
        LOGGER.info("正在检测数据库是否连接");
        while(true)
        {
            if(getTempSql().equals("SYS_CONERR_999")){
                try {
                    Thread.sleep(3000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                LOGGER.info("正在检测数据库是否连接，重试次数" +retryCount );
                retryCount++;
            }
            else {
                try {
                    initMatualParams();
                    break;
                } catch (Exception e) {
                    e.printStackTrace();
                }
                LOGGER.info("读取参数异常 正在检测数据库是否连接，重试次数" +retryCount );
                retryCount++;
            }
        }
        LOGGER.info("检测数据库是否连接，检测完成");
    }
}
