package com.tzx.receiver.common.upload;

import com.sun.org.apache.xpath.internal.operations.Bool;
import com.tzx.receiver.common.utils.DBUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Lazy;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 * @Date 2019-04-20
 * @Descption 用于存储所有的上传参数的LIST
 **/
@Component
@Lazy(true)
@DependsOn("newTzxMsgListener")
public class UpLoadParamsList implements InitializingBean {
    @Value("${msg.upload.minwaittime}")
    private String minWaiteTime;
    @Value("${msg.upload.maxwaittime}")
    private String maxWaiteTime;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    private final Logger logger	= LoggerFactory.getLogger(getClass());

    private ArrayList<UploadParam> params = new ArrayList<UploadParam>();

//    params.
    private ReentrantLock lock = new ReentrantLock();
    //将数据库中的配置初始化到list中去
    private void init(){
        if(this.params.size()>0){
            return;
        }
        lock.lock();
        try{
            String uploadType = DBUtils.getGGCSK("POS_ISUSEREALTIME");
            //没有设置过，或者设置了非0 非1 的值，都设置成默认的0
            if(StringUtils.isEmpty(uploadType)||(!uploadType.equals("1"))){
                uploadType = "0";
            }
//            params.set(new ArrayList<UploadParam>());
            String sql = "select lxbh ,sclxmc ,zbmlmc ,mlmc ,pzmc ,sfcc ,tjml ,wcml ,sjjg ,\n" +
                    //msgType表示传输数据格式是XML还是Json, 0表示XML，1表示JSON
                    "ccjgsj ,cccs ,yl1 ,yl2 ,posturl ,sendempty ,coalesce (msgtype,'0','1') as msgtype,signkeys,msgtype,\n" +
                    "array_to_string(array_agg(b.talias||'='||b.tcode||" +
                    //当为Json格式传输是，有时候接口要求属性是JsonObject,有时候要求是JsonArray。通过#后面数字表示
                    "(case when coalesce (a.msgtype,'0') = '1' then '#'||coalesce (b.childtype,'0') || " +
                    "'#'||coalesce (b.notempty,'0') else ''  end)),',') as scbb from \n" +
                    "ts_scbblxk a inner join ts_scbbk b on a.lxbh = b.bblxid\n" +
                    //任务类型分为三类：1、http实时上传uploadtype=1，总部参数设为1时，该类开启。2、老的MQ定时上传uploadtype=0，总部设为0开启
                    //3、打烊或考勤上传uploadtype=-1，打烊操作时通过UDP激活。不受总部参数控制
                    " where (a.uploadtype = -1 or a.uploadtype = " + uploadType + ") \n" +
                    "group by lxbh ,sclxmc ,zbmlmc ,mlmc ,pzmc ,sfcc ,tjml ,wcml ," +
                    "sjjg ,ccjgsj ,cccs ,yl1 ,yl2 ,posturl ,sendempty ;";
            SqlRowSet sqlRowSet = jdbcTemplate.queryForRowSet(sql);
            while (sqlRowSet.next()){
                UploadParam uploadParam = new UploadParam();
                uploadParam.setCommand(sqlRowSet.getString("MLMC"));
                //如果数据库里设置的时间间隔为-1，那么就从系统配置的最大时间
                //和最小时间中随机取一个值
                if (sqlRowSet.getString("SJJG").equals("-1")){
                    try {
                        uploadParam.setMinWaitSecond(Integer.parseInt(minWaiteTime));
                        uploadParam.setMaxWaiteSecond(Integer.parseInt(maxWaiteTime));
                    } catch (NumberFormatException e) {
                        uploadParam.setMinWaitSecond(0);
                        uploadParam.setMaxWaiteSecond(0);
                    }
                }else if (Integer.parseInt(sqlRowSet.getString("SJJG")) > 0 ){
                    uploadParam.setWaitSecond(Integer.parseInt(sqlRowSet.getString("SJJG")));
                }else{
                    uploadParam.setMinWaitSecond(0);
                    uploadParam.setMaxWaiteSecond(0);
                    uploadParam.setWaitSecond(0);
                }

                uploadParam.setActionName(sqlRowSet.getString("zbmlmc"));
                try {
                    uploadParam.setTransTypeId(Integer.parseInt(sqlRowSet.getString("lxbh")));
                } catch (Exception e) {
                    uploadParam.setTransTypeId(0);
                }
                uploadParam.setTransTypeName(sqlRowSet.getString("sclxmc"));
                uploadParam.setConfigName(sqlRowSet.getString("pzmc"));
                uploadParam.setTotalCommand(sqlRowSet.getString("tjml"));
                uploadParam.setIsTotal(StringUtils.isBlank(uploadParam.getTotalCommand()));
                uploadParam.setFinalCommand(sqlRowSet.getString("wcml"));
                uploadParam.setIsRunFinal(StringUtils.isBlank(uploadParam.getFinalCommand()));
                uploadParam.setSendUrl(sqlRowSet.getString("posturl"));
                try {
                    uploadParam.setIsSendEmpty(sqlRowSet.getString("SendEmpty").equals("1"));
                } catch (Exception e) {
                    uploadParam.setIsSendEmpty(false);
                }
                uploadParam.setMsgType(sqlRowSet.getString("msgtype").equals("1")?UploadDataType.JSON:
                        UploadDataType.XML);
                uploadParam.setSignKeys(sqlRowSet.getString("signkeys"));
                uploadParam.setTables(sqlRowSet.getString("scbb"));
                uploadParam.restNextRunTime();  //重置一下下次上传的时间
                params.add(uploadParam);
            }
        }catch (Exception E){
            logger.error("加载全局上传任务出现异常");
        }
        finally {
            lock.unlock();
        }
    }

    public UpLoadParamsList() {
        super();
    }

    /**
     * 获取定时已经到了定时上传时间点的任务
     * @return
     */
    public List<UploadParam> getParamsByTimer(){
        init();
        List<UploadParam> result = new ArrayList<UploadParam>();
        Date now = new Date();
        for(UploadParam uploadParam:this.params){
            if(uploadParam.getNextRunTime().getTime() <= now.getTime() ){
                uploadParam.restNextRunTime();
                UploadParam temp = new UploadParam();
                BeanUtils.copyProperties(uploadParam,temp);
                result.add(temp);
            }
        }
        return result;

    }

    /**
     * 根据命令名称找到命令参数
     * @param commond
     * @return
     */
    public UploadParam findUploadParamByCommand(String commond){
        if(! isHasDoneInit()){
            init();
        }

        UploadParam uploadParam = null;
        for (UploadParam param : this.params) {
            if(param.getCommand().equals(commond)){
                uploadParam = param;
            }
        }
        return  uploadParam;
    }
    public Boolean isHasDoneInit(){
        lock.lock();
        try
        {
            return  this.params.size()>0;
        }
        finally {
            lock.unlock();
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        init();
    }
    
	public void clearParams() {
		lock.lock();
		try {
			this.params.clear();
		} finally {
			lock.unlock();
		}
	}
}
