package com.tzx.miniapp.rest.vo;

import javax.persistence.Entity;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.List;

@Entity
public class Norms implements Serializable {

	private String name;
	private int duid;
	private float price;
	private float orgprice;
	private int bargainprice;
	private float memberprice;;
	private int min_unit;
	private int limitCount;

	/**
	 * 菜品配料id
	 */
	private int dishToppingId;

	/**
	 * 餐盒费
	 */
	private Double box_price;
	/**
	 * 餐盒数量
	 */
	private Integer box_num;

	public Double getBox_price() {
		return box_price;
	}

	public void setBox_price(Double box_price) {
		this.box_price = box_price;
	}

	public Integer getBox_num() {
		return box_num;
	}

	public void setBox_num(Integer box_num) {
		this.box_num = box_num;
	}

	@Transient
	private List<Integer> membergid;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public int getDuid() {
		return duid;
	}

	public void setDuid(int duid) {
		this.duid = duid;
	}

	public float getPrice() {
		return price;
	}

	public void setPrice(float price) {
		this.price = price;
	}

	public float getOrgprice() {
		return orgprice;
	}

	public void setOrgprice(float orgprice) {
		this.orgprice = orgprice;
	}

	public int getBargainprice() {
		return bargainprice;
	}

	public void setBargainprice(int bargainprice) {
		this.bargainprice = bargainprice;
	}

	public float getMemberprice() {
		return memberprice;
	}

	public void setMemberprice(float memberprice) {
		this.memberprice = memberprice;
	}

	public int getMin_unit() {
		return min_unit;
	}

	public void setMin_unit(int min_unit) {
		this.min_unit = min_unit;
	}

	public int getLimitCount() {
		return limitCount;
	}

	public void setLimitCount(int limitCount) {
		this.limitCount = limitCount;
	}

	public List<Integer> getMembergid() {
		return membergid;
	}

	public void setMembergid(List<Integer> membergid) {
		this.membergid = membergid;
	}

	public int getDishToppingId() {
		return dishToppingId;
	}

	public void setDishToppingId(int dishToppingId) {
		this.dishToppingId = dishToppingId;
	}
}
