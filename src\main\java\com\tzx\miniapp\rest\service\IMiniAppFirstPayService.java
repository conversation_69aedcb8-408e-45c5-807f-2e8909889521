package com.tzx.miniapp.rest.service;

import com.tzx.miniapp.common.Data;
import com.tzx.publics.common.BillNoData;

import net.sf.json.JSONObject;

public interface IMiniAppFirstPayService {
	/**
	 * 结账
	 */
	public Data getBillStatus(JSONObject payData, int operType);

	public Data firstPay(JSONObject json, BillNoData billNoData);

	public void sendPrint(JSONObject json, String ptype);

	public void sendPrintByYddh(String json);

	public void sendKVS(JSONObject json);

	public void sendPrintKichen(JSONObject json);

	public void exceptionCancelBill(Data data);
	
	public void sendPrintDetailed(JSONObject json);

	 Data orderPrecheckBefore(JSONObject orderData) ;
}
