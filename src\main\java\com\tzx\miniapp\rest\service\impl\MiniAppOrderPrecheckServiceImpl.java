package com.tzx.miniapp.rest.service.impl;

import com.tzx.commapi.rest.service.ISaleOutApiService;
import com.tzx.commapi.rest.vo.CommApiData;
import com.tzx.commapi.rest.vo.ReqParam;
import com.tzx.miniapp.common.Constant;
import com.tzx.miniapp.common.Data;
import com.tzx.miniapp.rest.mapper.MiniAppFirstPayMapper;
import com.tzx.miniapp.rest.mapper.MiniAppOrderPrecheckMapper;
import com.tzx.miniapp.rest.mapper.MiniAppShopBaseInfoMapper;
import com.tzx.miniapp.rest.mapper.MiniAppShopStatusMapper;
import com.tzx.miniapp.rest.model.TsCmk;
import com.tzx.miniapp.rest.model.TsGgcsk;
import com.tzx.miniapp.rest.service.IMiniAppOrderPrecheckService;
import com.tzx.miniapp.rest.vo.BtActivity;
import com.tzx.miniapp.rest.vo.BtSaleoutdishType;
import com.tzx.miniapp.rest.vo.BtTcselectmx;
import com.tzx.miniapp.rest.vo.BtYdd;
import com.tzx.miniapp.rest.vo.BtYdxm1;
import com.tzx.miniapp.rest.vo.BtYdxm2;
import com.tzx.miniapp.rest.vo.Dish;
import com.tzx.miniapp.rest.vo.Shops;
import com.tzx.publics.listener.InitDataListener;
import com.tzx.publics.util.ArithUtil;
import com.tzx.publics.util.DateUtil;
import com.tzx.publics.util.GsonUtil;
import com.tzx.publics.util.PropertiesUtil;
import com.tzx.publics.util.StringUtil;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class MiniAppOrderPrecheckServiceImpl implements IMiniAppOrderPrecheckService {
	private final static Logger LOGGER = LoggerFactory.getLogger(MiniAppOrderPrecheckServiceImpl.class);

	// 微生活的买一送一活动对应我们系统内部的买一送一优惠活动常量,对应微生活1
	private static String BUY_ONE_GIVE_ONE_YHSX = "11";
	// 微生活的加价购活动对应我们系统内部的活动常量,对应微生活2
	private static String ADDITIONAL_YHSX = "21";
	// 微生活的第二份半价活动对应我们系统内部的活动常量,对应微生活3
	private static String BUY_TWO_HALF_PRICE_YHSX = "19";
	// 微生活的特价菜活动对应我们系统内部的活动常量,对应微生活4
	private static String BUY_SPECIAL_OFFER_ITEM_YHSX = "60";
	// 微生活新大众营销，只能整单
	private static String ORDER_PROMOTION = "80";

	// 预定单接口注入对象
	@Autowired
	private MiniAppOrderPrecheckMapper orderPrecheckMapper;

	// 同步基础资料接口注入对象
	@Autowired
	private MiniAppShopBaseInfoMapper shopBaseInfoMapper;

	// 门店状态查询接口注入对象
	@Autowired
	private MiniAppShopStatusMapper shopStatusMapper;

	// 结账清台接口注入对象
	@Autowired
	private MiniAppFirstPayMapper firstPayMapper;

	// 实时沽清
	@Autowired
	private ISaleOutApiService saleOutApiService;

	@Transactional
	public Data orderPrecheck(JSONObject orderData) {
		// 微生活预定账单编号
		String outOrderId = orderData.optString("out_order_id");
		// 我们系统内部预定账单编号，规则：增加一个前缀
		String outOrderIdInDB = Constant.BILL_PREFIX + outOrderId;
		// 创建返回数据对象
		Data data = new Data();
		// 沽清数据列表
		List<ReqParam> reqParams = new ArrayList<ReqParam>();
		// 默认失败
		data.setSuccess(0);
		// 保存平台预定账单编号
		data.setYddbh(outOrderId);

		try {
			LOGGER.info("门店开始接单，平台预定账单编号：" + outOrderId);
			// **************************************************
			// 第一步：检查门店状态
			// **************************************************
			LOGGER.info("第一步：检查门店状态……");
			// 可能没有开店、没有开班、已经打烊等等
			Data dataShopStatus = shopStatus();
			if (dataShopStatus.getSuccess() != 1) {
				data.setMsg("下单失败：" + dataShopStatus.getMsg());
				return data;
			}

			// **************************************************
			// 第二步：清除可能存在遗留预定账单数据
			// 微生活可能重复调用预落单接口，但是最终没有结账清台，所以每次预落单前需要把以前的数据删除
			// **************************************************
			LOGGER.info("第二步：清除可能存在遗留预定账单数据……");
			// 清理预定账单数据
			orderPrecheckMapper.clearYdd(outOrderIdInDB);
			// 清理我们系统活动数据
			orderPrecheckMapper.clearYdxm1(outOrderIdInDB);
			// 清理菜品明细数据
			orderPrecheckMapper.clearYdxm2(outOrderIdInDB);
			// 清除套餐可选明细数据
			orderPrecheckMapper.clearYddTcSelectMx(outOrderIdInDB);
			// 清除微生活活动
			orderPrecheckMapper.clearYddActive(outOrderIdInDB);
			// 清除新版微生活活动及付款优惠信息
			orderPrecheckMapper.clearYddPayActive(outOrderIdInDB);

			// **************************************************
			// 读取传入账单数据
			// **************************************************
			// 账单
			JSONObject orderInfo = orderData.optJSONObject("order_info");
			JSONObject member = orderInfo.optJSONObject("member");
			// 获取会员等级id
			String grade = "0";
			if (null != member) {
				member.optString("grade", "0");
			}
			// 读取总部配置是否使用会员价设置，Y：使用会员价；N：不使用会员价
			String isVipPrice = orderPrecheckMapper.getIsVipPrice(Integer.parseInt(grade));
			if (isVipPrice == null) {
				isVipPrice = "N";
			}
			// 菜品
			JSONArray normalitems = orderInfo.optJSONArray("normalitems");
			// 套餐
			JSONArray setmeal = orderInfo.optJSONArray("setmeal");
			// 大众营销
			JSONArray activityDishes = orderInfo.optJSONArray("activityDishes");
			// 新大众营销
			JSONArray orderPromotion = orderInfo.optJSONArray("order_promotion");
			// 账单金额，菜品总计金额，包含赚送菜品金额
			double total = 0;
			double memberTotal = 0;
			double discountAmount = 0;
			double memberDiscountAmount = 0;
			// 是否存在微生活优惠活动
			// 微生活优惠活动与我们系统内部的优惠活动只能同时使用一个，不能共存
			// 一个微生活优惠活动最多可以赠送三个菜品，同一时间只会存在一个微生活优惠活动
			boolean existsActivity = false;
			// 点餐序号，套餐主菜明细点餐序号一致
			int dcxh = 0;
			// 菜品列表
			List<BtYdxm2> bymx = new ArrayList<BtYdxm2>();
			// 可选套餐明细列表
			List<BtTcselectmx> tsmx = new ArrayList<BtTcselectmx>();
			// 微生活优惠活动
			List<BtActivity> activityList = new ArrayList<BtActivity>();

			// **************************************************
			// 第3.1步：处理大众营销
			// **************************************************
			if (null != activityDishes) {
				for (int i = 0; i < activityDishes.size(); i++) {
					JSONObject buyDish = activityDishes.getJSONObject(i).optJSONObject("buyDish");
					JSONObject giftDish = activityDishes.getJSONObject(i).optJSONObject("giftDish");
					JSONObject activity = activityDishes.getJSONObject(i).optJSONObject("activity");

//					buyDish.put("price", ArithUtil.div(activity.optDouble("buyDishPrice"), 100, 2));
//					if (!"4".equals(activity.optString("type"))) {
//						giftDish.put("price", ArithUtil.div(activity.optDouble("giftDishPrice"), 100, 2));
//					}
					total = ArithUtil.add(total, ArithUtil.mul(buyDish.optDouble("aprice") + buyDish.optDouble("price"), buyDish.optInt("number")));

					// 拆分之后最后一份套餐对象，关联微生活优惠活动使用
//					BtYdxm2 lastSetmeal = null;

					String remark = "";
					if (!"1".equals(InitDataListener.ggcsMap.get("SFSYJJCGN"))) {
						JSONArray memos = buyDish.optJSONArray("memo");
						if (null != memos && memos.size() > 0) {
							remark = getItemeTaste(memos);
						} else {
							remark = buyDish.optString("remark");
						}
					}

					int forCount = 1;
					int itemNumber = buyDish.optInt("number");
					String xmsx = "单品";
					if(buyDish.optInt("type") == 2){
						forCount = buyDish.optInt("number");
						itemNumber = 1;
						xmsx = "套餐";
					}
					// 插入活动主菜，拆分套餐，按照下单套餐数量分成多条记录
					for (int j = 0; j < forCount; j++) {
						dcxh += 1;
						List<BtYdxm2> bymx1 = new ArrayList<BtYdxm2>();
						if ("1".equals(InitDataListener.ggcsMap.get("SFSYJJCGN"))) {
							remark = getItemeTaste1(outOrderIdInDB, buyDish, dcxh, bymx1, buyDish.optInt("type"));
						}
						BtYdxm2 ydmx2 = new BtYdxm2();
						ydmx2.setIsactive(0);
						ydmx2.setYddh(outOrderIdInDB);

						String dishNo = buyDish.optString("dishsno");
						TsCmk tsCmk = orderPrecheckMapper.getDishByCode(dishNo);

						ydmx2.setXmid(null == tsCmk ? buyDish.optInt("did") : tsCmk.getCmid());
						ydmx2.setXmbh(dishNo);
						ydmx2.setXmmc(buyDish.optString("name"));
						ydmx2.setXmsx(xmsx);
						// 单价还是菜品原价
						ydmx2.setXmdj(new BigDecimal(buyDish.optString("price")));
						ydmx2.setXmsl(itemNumber);
						ydmx2.setZkl(100);
						// 菜品实结金额
						ydmx2.setTotalprice(new BigDecimal((buyDish.optDouble("price") + buyDish.optDouble("aprice")) * itemNumber));
						ydmx2.setDwbh(buyDish.optString("dishunit"));
						ydmx2.setKwbh(remark);
						// 菜品金额使用原价*数量
						ydmx2.setCmje(new BigDecimal((buyDish.optDouble("price") + buyDish.optDouble("aprice")) * itemNumber));

						ydmx2.setTcbh("");
						ydmx2.setTcdch(0);
						ydmx2.setFzsl(0);
						ydmx2.setFzje(new BigDecimal(0));
						ydmx2.setDcxh(dcxh);
						BigDecimal decimal = new BigDecimal((buyDish.optDouble("price") + buyDish.optDouble("aprice")) * itemNumber);
						BigDecimal setScale = decimal.setScale(2, BigDecimal.ROUND_HALF_UP);
						ydmx2.setYl3(setScale + "");
//						lastSetmeal = ydmx2;
						bymx.add(ydmx2);
						bymx.addAll(bymx1);

						// 处理可选明细
						JSONArray mandatory = buyDish.optJSONArray("mandatory");
						if (null == mandatory) {
							mandatory = new JSONArray();
						}
						for (int k = 0; k < mandatory.size(); k++) {
							JSONObject jo1 = mandatory.getJSONObject(k);
							for (int n = 0; n < jo1.optInt("number"); n++) {
								BtTcselectmx btsmx = new BtTcselectmx();
								double fzsl = orderPrecheckMapper.getFzsl(jo1.optInt("rpdid"), jo1.optInt("id"));
								btsmx.setYddh(outOrderIdInDB);
								if (0.5 == fzsl) {
									btsmx.setFzsl(0.5);
								} else {
									btsmx.setFzsl(1);
								}
								btsmx.setFzje(new BigDecimal(jo1.optDouble("aprice")));
								btsmx.setDcxh(dcxh);
								btsmx.setFzid(jo1.optInt("rpdid"));
								btsmx.setMxid(jo1.optInt("id"));
								tsmx.add(btsmx);
							}
						}
					}

					// 插入活动菜品activeYdxm2，4为特价菜品，无活动菜品
					if (!"4".equals(activity.optString("type"))) {
						dcxh += 1;
						List<BtYdxm2> bymx1 = new ArrayList<BtYdxm2>();
						if ("1".equals(InitDataListener.ggcsMap.get("SFSYJJCGN"))) {
							remark = getItemeTaste1(outOrderIdInDB, buyDish, dcxh, bymx1, buyDish.optInt("type"));
						}
						total = ArithUtil.add(total, ArithUtil.mul(giftDish.optDouble("price"), giftDish.optInt("number")));
						BtYdxm2 aYdxm2 = new BtYdxm2();
						aYdxm2.setIsactive(0);
						aYdxm2.setYddh(outOrderIdInDB);

						String dishNo = giftDish.optString("dishsno");
						TsCmk tsCmk = orderPrecheckMapper.getDishByCode(dishNo);

						aYdxm2.setXmid(null == tsCmk ? giftDish.optInt("did") : tsCmk.getCmid());
						aYdxm2.setXmbh(dishNo);
						aYdxm2.setXmmc(giftDish.optString("name"));
						aYdxm2.setXmsx("单品");
						// 单价还是菜品原价
						aYdxm2.setXmdj(new BigDecimal(giftDish.optString("price")));
						aYdxm2.setXmsl(giftDish.optInt("number"));
						aYdxm2.setZkl(100);
						// 菜品实结金额
						aYdxm2.setTotalprice(new BigDecimal(giftDish.optDouble("price") * giftDish.optInt("number")));
						aYdxm2.setDwbh(giftDish.optString("dishunit"));
						aYdxm2.setKwbh(remark);
						// 菜品金额使用原价*数量
						aYdxm2.setCmje(new BigDecimal(giftDish.optDouble("price") * giftDish.optInt("number")));

						aYdxm2.setTcbh("");
						aYdxm2.setTcdch(0);
						aYdxm2.setFzsl(0);
						aYdxm2.setFzje(new BigDecimal(0));
						aYdxm2.setDcxh(dcxh);
						aYdxm2.setIsactive(1);
						BigDecimal decimal = new BigDecimal(giftDish.optDouble("price") * giftDish.optInt("number"));
						BigDecimal setScale = decimal.setScale(2, BigDecimal.ROUND_HALF_UP);
						aYdxm2.setYl3(setScale + "");
//						lastSetmeal = ydmx2;
						bymx.add(aYdxm2);
						bymx.addAll(bymx1);

						// 处理活动关联
						BtActivity btActivity = new BtActivity();
						btActivity.setYddh(outOrderIdInDB);
						// 与赠送菜品序号一致，更新优惠数据使用
						// 这个序号是上面拆分菜品最后一条菜品产生，十分重要，在后面处理活动表中的ydxmid和buy_ydxmid时关联更新使用
						btActivity.setDcxh(dcxh);
						// 微生活优惠活动ID需要加上10000，并且转成负数处理，为了防止与我们系统内部优惠活动ID冲突
						int active_id = (10000 + activity.optInt("aid", 0)) * -1;
						btActivity.setActive_id(active_id);
						// 本次活动名称，交易期间动态生成
						btActivity.setActive_current_name(activity.optString("name", ""));
						// 买一送一优惠活动名称，不是本次菜品上的实际产生优惠活动名称，类似模板名称
						btActivity.setActive_name(activity.optString("name", ""));
						// 买一送一优惠活动类型
						btActivity.setActive_type(activity.optInt("type", 1));
						// 此时暂未生成赠送菜品预定数据，后续需要处理此值，特别重要
						// 赠送菜品预定明细ID
						btActivity.setYdxmid(-1);
						// 此时暂未生成赠送主菜预定数据，后续需要处理此值，特别重要
						// 赠送主菜预定明细ID
						btActivity.setBuy_ydxmid(-1);
						// 赠送主菜编号
						btActivity.setBuy(activity.optString("buyDishno", ""));
						// 赠送菜品编号
						btActivity.setGift(activity.optString("giftDishno", ""));

						double giftTotal = ArithUtil.mul(giftDish.optDouble("price"), giftDish.optInt("number"));
						double activityGiftTotal = ArithUtil.mul(ArithUtil.div(activity.optDouble("giftDishPrice"), 100, 2), activity.optInt("gitDishCount"));
						btActivity.setCoupon_money(ArithUtil.sub(giftTotal, activityGiftTotal));
						discountAmount = ArithUtil.add(discountAmount, ArithUtil.sub(giftTotal, activityGiftTotal));

						activityList.add(btActivity);
					} else {
						String yhsx = BUY_SPECIAL_OFFER_ITEM_YHSX;
						int active_id = (10000 + activity.optInt("aid", 0)) * -1;
						if (orderPrecheckMapper.selectYhfssdk(active_id) == 0) {
							LOGGER.info("    处理本地优惠活动方式设定数据，可能优惠活动未从微生活同步下发过来……");
							orderPrecheckMapper.insertYhfssdk(active_id, "W" + activity.optInt("aid", 0), activity.optString("name", ""), yhsx);
						}
						if (orderPrecheckMapper.selectYhfssdk_xf(active_id) == 0) {
							LOGGER.info("    处理本地优惠活动方式设定临时下发数据，可能优惠活动未从微生活同步下发过来……");
							orderPrecheckMapper.insertYhfssdk_xf(active_id, "W" + activity.optInt("aid", 0), activity.optString("name", ""), yhsx);
						}

//						total = ArithUtil.add(total, ArithUtil.mul(ArithUtil.add(buyDish.optDouble("aprice"), buyDish.optDouble("price")), buyDish.optInt("number")));
						double buyTotal = ArithUtil.mul(buyDish.optDouble("aprice") + buyDish.optDouble("price"), buyDish.optInt("number"));
						double activityGiftTotal = ArithUtil.mul(ArithUtil.div(activity.optDouble("buyDishPrice"), 100, 2), activity.optInt("buyDishCount"));
//						total = buyTotal;
						discountAmount = ArithUtil.add(discountAmount, ArithUtil.sub(buyTotal, activityGiftTotal));
						// 特价菜活动，与会 员价流程契合，暂时走会员价，晓辉抽出时间改过程再微调
						orderPrecheckMapper.insertBtYddActive(outOrderIdInDB, String.valueOf(dcxh),
								ArithUtil.sub(buyTotal, activityGiftTotal), activity.optString("buyDishno", ""),
								"SPECIAL", active_id);
					}
					existsActivity = true;
				}
			}
			// **************************************************
			// 第3.2步：处理新版大众营销，整单型
			// **************************************************
			if (null != orderPromotion) {
				for (int i = 0; i < orderPromotion.size(); i++) {
					// 如果使用大众营销活动，则关闭会员价入口
					isVipPrice = "N";
					JSONObject promotion = orderPromotion.getJSONObject(i);

					String yhsx = ORDER_PROMOTION;
					int active_id = (10000 + promotion.optInt("promotion_id", 0)) * -1;
					if (orderPrecheckMapper.selectYhfssdk(active_id) == 0) {
						LOGGER.info("    处理本地优惠活动方式设定数据，可能优惠活动未从微生活同步下发过来……");
						orderPrecheckMapper.insertYhfssdk(active_id, "W" + promotion.optInt("promotion_id", 0), promotion.optString("promotion_name", ""), yhsx);
					}
					if (orderPrecheckMapper.selectYhfssdk_xf(active_id) == 0) {
						LOGGER.info("    处理本地优惠活动方式设定临时下发数据，可能优惠活动未从微生活同步下发过来……");
						orderPrecheckMapper.insertYhfssdk_xf(active_id, "W" + promotion.optInt("promotion_id", 0), promotion.optString("promotion_name", ""), yhsx);
					}

					double promotionDiscountAmount = ArithUtil.div(promotion.optDouble("promotion_discount_amount"), 100, 2);
					discountAmount = ArithUtil.add(discountAmount, promotionDiscountAmount);
					firstPayMapper.insertBtYddPayActive(outOrderIdInDB, "微生活通用营销", promotionDiscountAmount, "", "ACTIVITY", active_id);
					existsActivity = true;
				}
			}
			// **************************************************
			// 第3.3步：处理新版大众营销，加价购
			// 因为需要预先处理会员价控制字段，所以在处理菜品前单独循环一次所有菜品，拿出加价购优惠数据
			// **************************************************
			double pdAmount = ArithUtil.add(dishPromotion(outOrderIdInDB, normalitems), dishPromotion(outOrderIdInDB, setmeal));
			if(pdAmount !=0){
				isVipPrice = "N";
				existsActivity = true;
				discountAmount = ArithUtil.add(discountAmount, pdAmount);
			}
			// **************************************************
			// 第三步：生成赠送主菜编号列表
			// **************************************************
			LOGGER.info("第三步：生成赠送主菜编号列表……");
			Map<Integer, String> buyDishList = new HashMap<Integer, String>();
			if (!generateBuyDishList(normalitems, setmeal, buyDishList, data)) {
				return data;
			}

			// **************************************************
			// 第四步：处理微生活打包盒，如果菜品属于外带模式，并且存在关联打包盒设置，需要自动附加打包盒
			// **************************************************
			LOGGER.info("第四步：处理微生活打包盒……");
			generateDishPackage(orderInfo, normalitems, setmeal);

			// **************************************************
			// 第五步：处理单品类型菜品
			// **************************************************
			LOGGER.info("第五步：处理单品类型菜品……");
			for (int i = 0; i < normalitems.size(); i++) {
				JSONObject jo = normalitems.getJSONObject(i);

				String dishNo = jo.optString("dishsno", "");
				TsCmk tsCmk = orderPrecheckMapper.getDishByCode(dishNo);

				// 如果是多规格菜品则替换菜品id
				if (jo.optInt("did", 0) != jo.optInt("duid", 0)) {
					jo.put("did", jo.optInt("duid", 0));
//					TsCmk tsCmk = orderPrecheckMapper.getTsCmk(jo.optInt("duid", 0));
					if (null != tsCmk) {
						jo.put("dishsno", tsCmk.getCmbh());
						jo.put("name", tsCmk.getCmmc1());
						jo.put("dishunit",  tsCmk.getDwbh());
					}
				}
				// 菜品总计金额
				// (加价金额+菜品金额) * 数量
				total = ArithUtil.add(total, ArithUtil.mul(jo.optDouble("aprice") + jo.optDouble("price"), jo.optInt("number")));
				double total1 = ArithUtil.mul(jo.optDouble("aprice") + jo.optDouble("price"), jo.optInt("number"));
				double memberTotal1 = 0;
				if ( jo.optDouble("price") >   jo.optDouble("memberprice") ) {
					memberTotal = ArithUtil.add(memberTotal, ArithUtil.mul(jo.optDouble("aprice") + jo.optDouble("memberprice"), jo.optInt("number")));
					memberTotal1 = ArithUtil.mul(jo.optDouble("aprice") + jo.optDouble("memberprice"), jo.optInt("number"));
				} else {
					memberTotal = ArithUtil.add(memberTotal, ArithUtil.mul(jo.optDouble("aprice") + jo.optDouble("price"), jo.optInt("number")));
					memberTotal1 = ArithUtil.mul(jo.optDouble("aprice") + jo.optDouble("price"), jo.optInt("number"));
				}
				memberDiscountAmount = ArithUtil.add(memberDiscountAmount, ArithUtil.sub(total1, memberTotal1));
				// 判断是否赠送主菜
				boolean isMainDish = false;
				// 判断正常菜品是否关联微生活优惠活动
				if (!jo.has("activity")) {
					for (Map.Entry<Integer, String> entry : buyDishList.entrySet()) {
						if (jo.optString("dishsno").equals(entry.getValue())) {
							LOGGER.info("    当前菜品属于赠送主菜类型……");
							LOGGER.info("    " + JSONObject.fromObject(jo).toString());
							// 当前菜品属于优惠活动关联赠送主菜
							isMainDish = true;
							// 如果匹配，需要删除赠送主菜列表中的主菜，表示已经处理
							buyDishList.remove(entry.getKey());
							break;
						}
					}
				}
				String remark = "";
				if (!"1".equals(InitDataListener.ggcsMap.get("SFSYJJCGN"))) {
					JSONArray memos = jo.optJSONArray("memo");
					if (null != memos && memos.size() > 0) {
						remark = getItemeTaste(memos);
					} else {
						remark = jo.optString("remark");
					}
				}
				if (isMainDish && (jo.optInt("number") > 1) && !jo.has("activity")) {
					LOGGER.info("    赠送主菜拆分……");
					LOGGER.info("    " + JSONObject.fromObject(jo).toString());
					// 如果存在微生活买一送一优惠活动，并且菜品数量多份，需要单独分离出来一份以供我们系统内部优惠关联处理使用
					// 点菜序号
					dcxh += 1;
					// 复用微生活活动表，优惠券编号couponcodes记录菜品编号dishsno
					if (isVipPrice.equals("Y") && (jo.optDouble("price") > jo.optDouble("memberprice"))) {
						// 记录会员价方式下每个可以使用会员价的菜品的优惠金额，按照正数记录
						orderPrecheckMapper.insertBtYddActive(outOrderIdInDB, String.valueOf(dcxh), ArithUtil.mul(jo.optDouble("aprice") + jo.optDouble("price"), jo.optInt("number") - 1) - ArithUtil.mul(jo.optDouble("aprice") + jo.optDouble("memberprice"), jo.optInt("number") - 1), jo.optString("dishsno"), "MEMBER", 0);
					}
					List<BtYdxm2> bymx1 = new ArrayList<BtYdxm2>();
					if ("1".equals(InitDataListener.ggcsMap.get("SFSYJJCGN"))) {
						remark = getItemeTaste1(outOrderIdInDB, jo, dcxh, bymx1, 1);
					}
					BtYdxm2 ydxm2 = new BtYdxm2();
					ydxm2.setIsactive(0);
					ydxm2.setYddh(outOrderIdInDB);

					ydxm2.setXmid(null == tsCmk ? jo.optInt("did") : tsCmk.getCmid());
					ydxm2.setXmbh(dishNo);
					ydxm2.setXmmc(jo.optString("name"));
					ydxm2.setXmsx("单品");
					// 单价还是菜品原价
					ydxm2.setXmdj(new BigDecimal(jo.optString("price")));
					ydxm2.setXmsl(jo.optInt("number") - 1);
					ydxm2.setZkl(100);
					// 菜品实结金额
					ydxm2.setTotalprice(new BigDecimal(jo.optDouble("price") * (jo.optInt("number") - 1)));
					ydxm2.setDwbh(jo.optString("dishunit"));
					ydxm2.setKwbh(remark);
					// 菜品金额使用原价*数量
					ydxm2.setCmje(new BigDecimal(jo.optDouble("price") * (jo.optInt("number") - 1)));
					ydxm2.setTcbh("");
					ydxm2.setTcdch(0);
					ydxm2.setFzsl(0);
					ydxm2.setFzje(new BigDecimal(0));
					ydxm2.setDcxh(dcxh);
					BigDecimal decimal = new BigDecimal(jo.optDouble("price") * (jo.optInt("number") - 1));
					BigDecimal setScale = decimal.setScale(2, BigDecimal.ROUND_HALF_UP);
					ydxm2.setYl3(setScale + "");
					bymx.add(ydxm2);
					bymx.addAll(bymx1);
					// 点菜序号
					dcxh += 1;
					// 复用微生活活动表，优惠券编号couponcodes记录菜品编号dishsno
					if (isVipPrice.equals("Y") && (jo.optDouble("price") > jo.optDouble("memberprice"))) {
						// 记录会员价方式下每个可以使用会员价的菜品的优惠金额，按照正数记录
						orderPrecheckMapper.insertBtYddActive(outOrderIdInDB, String.valueOf(dcxh), ArithUtil.mul(jo.optDouble("aprice") + jo.optDouble("price"), 1) - ArithUtil.mul(jo.optDouble("aprice") + jo.optDouble("memberprice"), 1), jo.optString("dishsno"), "MEMBER", 0);
					}
					BtYdxm2 activeYdxm2 = new BtYdxm2();
					activeYdxm2.setIsactive(0);
					activeYdxm2.setYddh(outOrderIdInDB);
					activeYdxm2.setXmid(null == tsCmk ? jo.optInt("did") : tsCmk.getCmid());
					activeYdxm2.setXmbh(dishNo);
					activeYdxm2.setXmmc(jo.optString("name"));
					activeYdxm2.setXmsx("单品");
					// 单价还是菜品原价
					activeYdxm2.setXmdj(new BigDecimal(jo.optString("price")));
					activeYdxm2.setXmsl(1);
					activeYdxm2.setZkl(100);
					// 菜品实结金额
					activeYdxm2.setTotalprice(new BigDecimal(jo.optDouble("price")));
					activeYdxm2.setDwbh(jo.optString("dishunit"));
					activeYdxm2.setKwbh(remark);
					// 菜品金额使用原价*数量
					activeYdxm2.setCmje(new BigDecimal(jo.optDouble("price")));
					activeYdxm2.setTcbh("");
					activeYdxm2.setTcdch(0);
					activeYdxm2.setFzsl(0);
					activeYdxm2.setFzje(new BigDecimal(0));
					activeYdxm2.setDcxh(dcxh);
					BigDecimal decimal1 = new BigDecimal(jo.optDouble("price"));
					BigDecimal setScale1 = decimal1.setScale(2, BigDecimal.ROUND_HALF_UP);
					activeYdxm2.setYl3(setScale1 + "");
					bymx.add(activeYdxm2);
					bymx.addAll(bymx1);

				} else {
					LOGGER.info("    可能当前菜品属于普通菜品、赠送菜品、单份赠送主菜……");
					LOGGER.info("    " + JSONObject.fromObject(jo).toString());
					// 点菜序号
					dcxh += 1;
					// 复用微生活活动表，优惠券编号couponcodes记录菜品编号dishsno
					if (isVipPrice.equals("Y") && (jo.optDouble("price") > jo.optDouble("memberprice"))) {
						// 记录会员价方式下每个可以使用会员价的菜品的优惠金额，按照正数记录
						orderPrecheckMapper.insertBtYddActive(outOrderIdInDB, String.valueOf(dcxh), ArithUtil.mul(jo.optDouble("aprice") + jo.optDouble("price"), jo.optInt("number")) - ArithUtil.mul(jo.optDouble("aprice") + jo.optDouble("memberprice"), jo.optInt("number")), jo.optString("dishsno"), "MEMBER", 0);
					}
					List<BtYdxm2> bymx1 = new ArrayList<BtYdxm2>();
					if ("1".equals(InitDataListener.ggcsMap.get("SFSYJJCGN"))) {
						remark = getItemeTaste1(outOrderIdInDB, jo, dcxh, bymx1, 1);
					}
					BtYdxm2 ydmx2 = new BtYdxm2();
					// 判断是否赚送菜品
					if (jo.has("activity")) {
						ydmx2.setIsactive(1);
					} else {
						ydmx2.setIsactive(0);
					}
					ydmx2.setYddh(outOrderIdInDB);
					ydmx2.setXmid(null == tsCmk ? jo.optInt("did") : tsCmk.getCmid());
					ydmx2.setXmbh(dishNo);
					ydmx2.setXmmc(jo.optString("name"));
					ydmx2.setXmsx("单品");
					// 单价还是菜品原价
					ydmx2.setXmdj(new BigDecimal(jo.optString("price")));
					ydmx2.setXmsl(jo.optInt("number"));
					ydmx2.setZkl(100);
					// 菜品实结金额
					ydmx2.setTotalprice(new BigDecimal(jo.optDouble("price") * jo.optInt("number")));
					ydmx2.setDwbh(jo.optString("dishunit"));
					ydmx2.setKwbh(remark);
					// 菜品金额使用原价*数量
					ydmx2.setCmje(new BigDecimal(jo.optDouble("price") * jo.optInt("number")));
					ydmx2.setTcbh("");
					ydmx2.setTcdch(0);
					ydmx2.setFzsl(0);
					ydmx2.setFzje(new BigDecimal(0));
					ydmx2.setDcxh(dcxh);
					BigDecimal decimal = new BigDecimal(jo.optDouble("price") * jo.optInt("number"));
					BigDecimal setScale = decimal.setScale(2, BigDecimal.ROUND_HALF_UP);
					ydmx2.setYl3(setScale + "");
					bymx.add(ydmx2);
					bymx.addAll(bymx1);
				}
				// 检查是否存在微生活买一送一优惠活动数据
				// 优惠活动数据存在被赠菜品之上，但是可能属于多份菜品，但是最多只有一份菜品发生买一赠一优惠活动，上面过程已经针对多份菜品做了处理
				// "activity":{"money":1000,"buy":"02010007","gift":"02030029","buyName":"清炒油麦菜盖饭","giftName":"测试粥4",
				// "templateid":"8892314","templateName":"测试粥4送劵","title":"满10元买清炒油麦菜盖饭赠测试粥4","name":"买一赠一活动","id":"14",
				// "limit":{"kinds":[],"dishsno":[]},"status":1}
				// 处理被送单品上的优惠活动
				if (jo.has("activity")) {
					LOGGER.info("    开始处理单品赠送菜品上面活动……");
					existsActivity = true;
					JSONObject aceActivity = jo.optJSONObject("activity");
					LOGGER.info("    " + JSONObject.fromObject(aceActivity).toString());
					BtActivity btActivity = new BtActivity();
					btActivity.setYddh(outOrderIdInDB);
					// 与赠送菜品序号一致，更新优惠数据使用
					// 这个序号是上面拆分菜品最后一条菜品产生，十分重要，在后面处理活动表中的ydxmid和buy_ydxmid时关联更新使用
					btActivity.setDcxh(dcxh);
					// 微生活优惠活动ID需要加上10000，并且转成负数处理，为了防止与我们系统内部优惠活动ID冲突
					int active_id = (10000 + aceActivity.optInt("id", 0)) * -1;
					btActivity.setActive_id(active_id);
					// 本次活动名称，交易期间动态生成
					btActivity.setActive_current_name(aceActivity.optString("title", ""));
					// 买一送一优惠活动名称，不是本次菜品上的实际产生优惠活动名称，类似模板名称
					btActivity.setActive_name(aceActivity.optString("name", ""));
					// 买一送一优惠活动类型
					btActivity.setActive_type(aceActivity.optInt("type", 1));
					// 此时暂未生成赠送菜品预定数据，后续需要处理此值，特别重要
					// 赠送菜品预定明细ID
					btActivity.setYdxmid(-1);
					// 此时暂未生成赠送主菜预定数据，后续需要处理此值，特别重要
					// 赠送主菜预定明细ID
					btActivity.setBuy_ydxmid(-1);
					// 赠送主菜编号
					btActivity.setBuy(aceActivity.optString("buy", ""));
					// 赠送菜品编号
					btActivity.setGift(aceActivity.optString("gift", ""));
					activityList.add(btActivity);
				}

				ReqParam reqParam = new ReqParam();
				reqParam.setItem_id(jo.optString("dishsno"));
				reqParam.setItem_name(jo.optString("name"));
				reqParam.setCount(new BigDecimal(jo.optInt("number")));
				reqParams.add(reqParam);
			}

			// **************************************************
			// 第六步：处理套餐类型菜品
			// **************************************************
			LOGGER.info("第六步：处理套餐类型菜品……");
			// 不存明细
			for (int i = 0; i < setmeal.size(); i++) {
				JSONObject jo = setmeal.getJSONObject(i);
				total = ArithUtil.add(total, ArithUtil.mul(jo.optDouble("aprice") + jo.optDouble("price"), jo.optInt("number")));
				double total1 = ArithUtil.mul(jo.optDouble("aprice") + jo.optDouble("price"), jo.optInt("number"));
				double memberTotal1 = 0;
				if (  jo.optDouble("price")  >   jo.optDouble("memberprice") ) {
					memberTotal = ArithUtil.add(memberTotal, ArithUtil.mul(jo.optDouble("aprice") + jo.optDouble("memberprice"), jo.optInt("number")));
					memberTotal1 = ArithUtil.mul(jo.optDouble("aprice") + jo.optDouble("memberprice"), jo.optInt("number"));
				} else {
					memberTotal = ArithUtil.add(memberTotal, ArithUtil.mul(jo.optDouble("aprice") + jo.optDouble("price"), jo.optInt("number")));
					memberTotal1 = ArithUtil.mul(jo.optDouble("aprice") + jo.optDouble("price"), jo.optInt("number"));
				}
				memberDiscountAmount = ArithUtil.add(memberDiscountAmount, ArithUtil.sub(total1, memberTotal1));

				// 拆分之后最后一份套餐对象，关联微生活优惠活动使用
				BtYdxm2 lastSetmeal = null;
				String remark = "";
				if (!"1".equals(InitDataListener.ggcsMap.get("SFSYJJCGN"))) {
					JSONArray memos = jo.optJSONArray("memo");
					if (null != memos && memos.size() > 0) {
						remark = getItemeTaste(memos);
					} else {
						remark = jo.optString("remark");
					}
				} else { // 套餐需要把总加价金额中的加价菜金额减掉
					JSONArray memos = jo.optJSONArray("memo");
					if (null != memos && memos.size() > 0) {
						double aprice = jo.optDouble("aprice", 0);
						for (int j = 0; j < memos.size(); j++) {
							JSONObject memo = memos.getJSONObject(j);
							JSONArray items = memo.optJSONArray("items");
							for (int k = 0; k < items.size(); k++) {
								JSONObject item = items.getJSONObject(k);
								double xmdj = ArithUtil.div(item.optDouble("aprice", 0), 100);
								aprice = ArithUtil.sub(aprice, xmdj);
							}
						}
						jo.put("aprice", aprice);
					}
				}

				String setDishNo = jo.optString("dishsno");

				TsCmk setTsCmk = orderPrecheckMapper.getDishByCode(setDishNo);

				ReqParam reqParam = new ReqParam();
				reqParam.setItem_id(setDishNo);
				reqParam.setItem_name(jo.optString("name"));
				reqParam.setCount(new BigDecimal(jo.optInt("number")));
				reqParams.add(reqParam);

				// 拆分套餐，按照下单套餐数量分成多条记录
				for (int j = 0; j < jo.optInt("number"); j++) {
					dcxh += 1;
					// 复用微生活活动表，优惠券编号couponcodes记录菜品编号dishsno
					if (isVipPrice.equals("Y")
							&& (jo.optDouble("price") > jo.optDouble("memberprice"))) {
						// 记录会员价方式下每个可以使用会员价的菜品的优惠金额，按照正数记录
						orderPrecheckMapper.insertBtYddActive(outOrderIdInDB, String.valueOf(dcxh),
								ArithUtil.mul(jo.optDouble("aprice") + jo.optDouble("price"), 1) - ArithUtil.mul(jo.optDouble("aprice") + jo.optDouble("memberprice"), 1), jo.optString("dishsno"),
								"MEMBER", 0);
					}
					List<BtYdxm2> bymx1 = new ArrayList<BtYdxm2>();
					if ("1".equals(InitDataListener.ggcsMap.get("SFSYJJCGN"))) {
						remark = getItemeTaste1(outOrderIdInDB, jo, dcxh, bymx1, 2);
					}
					BtYdxm2 ydmx2 = new BtYdxm2();
					ydmx2.setIsactive(0);
					ydmx2.setYddh(outOrderIdInDB);
					ydmx2.setXmid(null == setTsCmk ? jo.optInt("did") : setTsCmk.getCmid());
					ydmx2.setXmbh(setDishNo);
					ydmx2.setXmmc(jo.optString("name"));
					ydmx2.setXmsx("套餐");
					// 单价还是菜品原价
					ydmx2.setXmdj(new BigDecimal(jo.optString("price")));
					ydmx2.setXmsl(1);
					ydmx2.setZkl(100);
					// 菜品实结金额
					ydmx2.setTotalprice(new BigDecimal(jo.optDouble("price") + jo.optDouble("aprice")));
					ydmx2.setDwbh(jo.optString("dishunit"));
					ydmx2.setKwbh(remark);
					// 菜品金额使用原价*数量
					ydmx2.setCmje(new BigDecimal(jo.optDouble("price") + jo.optDouble("aprice")));

					ydmx2.setTcbh("");
					ydmx2.setTcdch(0);
					ydmx2.setFzsl(0);
					ydmx2.setFzje(new BigDecimal(0));
					ydmx2.setDcxh(dcxh);
					BigDecimal decimal = new BigDecimal(jo.optDouble("price") + jo.optDouble("aprice"));
					BigDecimal setScale = decimal.setScale(2, BigDecimal.ROUND_HALF_UP);
					ydmx2.setYl3(setScale + "");
					lastSetmeal = ydmx2;
					bymx.add(ydmx2);
					bymx.addAll(bymx1);

					// 处理固定明细
					JSONArray maindish = jo.optJSONArray("maindish");
					for (int k = 0; k < maindish.size(); k++) {
						JSONObject jo1 = maindish.getJSONObject(k);
						ReqParam reqParam1 = new ReqParam();
						reqParam1.setItem_id(jo1.optString("dishsno"));
						reqParam1.setItem_name(jo1.optString("name"));
						reqParam1.setCount(new BigDecimal(jo1.optInt("number")));
						reqParams.add(reqParam1);

                        /*if ("1".equals(InitDataListener.ggcsMap.get("SFSYJJCGN"))) {
							remark = getItemeTaste1(outOrderIdInDB, jo1, dcxh, bymx, 2);
						}*/
					}

					// 处理可选明细
					JSONArray mandatory = jo.optJSONArray("mandatory");
					for (int k = 0; k < mandatory.size(); k++) {
						JSONObject jo1 = mandatory.getJSONObject(k);
						String jo1DishNo = jo1.optString("dishsno");
						TsCmk jo1TsCmk = orderPrecheckMapper.getDishByCode(jo1DishNo);
						for (int n = 0; n < jo1.optInt("number"); n++) {
							BtTcselectmx btsmx = new BtTcselectmx();
							int jo1DishId = null == jo1TsCmk ? jo1.optInt("id") : jo1TsCmk.getCmid();
							double fzsl = orderPrecheckMapper.getFzsl(jo1.optInt("rpdid"), jo1DishId);
							btsmx.setYddh(outOrderIdInDB);
							if (0.5 == fzsl) {
								btsmx.setFzsl(0.5);
							} else {
								btsmx.setFzsl(1);
							}
							btsmx.setFzje(new BigDecimal(jo1.optDouble("aprice")));
							btsmx.setDcxh(dcxh);
							btsmx.setFzid(jo1.optInt("rpdid"));
							btsmx.setMxid(jo1DishId);
							tsmx.add(btsmx);

							ReqParam reqParam1 = new ReqParam();
							reqParam1.setItem_id(jo1.optString("dishsno"));
							reqParam1.setItem_name(jo1.optString("name"));
							reqParam1.setCount(new BigDecimal(btsmx.getFzsl()));
							reqParams.add(reqParam1);

                            /*if ("1".equals(InitDataListener.ggcsMap.get("SFSYJJCGN"))) {
								remark = getItemeTaste1(outOrderIdInDB, jo1, dcxh, bymx, 2);
							}*/
						}
					}

//					// 非必选
//					JSONArray optional = new JSONArray();
//					if (jo.has("optional")) {
//						JSONArray optionalArray = jo.optJSONArray("optional");
//						for (int k = 0; k < optionalArray.size(); k++) {
//							JSONObject gro = optionalArray.getJSONObject(k);
////                    Integer gdid = orderPrecheckMapper.getIdByCode(gro.optString("dishsno"));
//							TsCmk gdCmk = orderPrecheckMapper.getDishJgtxByCode(getItemCodeBySpeccode(gro));
//
//							gro.put("did", gdCmk.getCmid());
//							gro.put("localName", gdCmk.getCmmc1());
//							gro.put("dishunit",gdCmk.getDwbh());
//							// gro.put("price",gdCmk.getCmdj());
//
//							BtYdxm2 ydxm3 = jointYdmx(jo, outOrderIdInDB, "CMSX_MX", dcxh, gro, setTsCmk.getCmid(), 0, "", "ERP_MXLX_GROUP");
//							bymx.add(ydxm3);
//						}
//					}


					// 非必选
					JSONArray optionalArray = jo.optJSONArray("optional");
					for (int k = 0; k < optionalArray.size(); k++) {
						JSONObject jo1 = optionalArray.getJSONObject(k);
						String jo1DishNo = jo1.optString("dishsno");
						TsCmk jo1TsCmk = orderPrecheckMapper.getDishByCode(jo1DishNo);
						for (int n = 0; n < jo1.optInt("number"); n++) {
							BtTcselectmx btsmx = new BtTcselectmx();
							int jo1DishId = null == jo1TsCmk ? jo1.optInt("id") : jo1TsCmk.getCmid();
							double fzsl = orderPrecheckMapper.getFzsl(jo1.optInt("rpdid"), jo1DishId);
							btsmx.setYddh(outOrderIdInDB);
							if (0.5 == fzsl) {
								btsmx.setFzsl(0.5);
							} else {
								btsmx.setFzsl(1);
							}
							btsmx.setFzje(new BigDecimal(jo1.optDouble("aprice")));
							btsmx.setDcxh(dcxh);
							btsmx.setFzid(jo1.optInt("rpdid"));
							btsmx.setMxid(jo1DishId);
							tsmx.add(btsmx);

							ReqParam reqParam1 = new ReqParam();
							reqParam1.setItem_id(jo1.optString("dishsno"));
							reqParam1.setItem_name(jo1.optString("name"));
							reqParam1.setCount(new BigDecimal(btsmx.getFzsl()));
							reqParams.add(reqParam1);

                            /*if ("1".equals(InitDataListener.ggcsMap.get("SFSYJJCGN"))) {
								remark = getItemeTaste1(outOrderIdInDB, jo1, dcxh, bymx, 2);
							}*/
						}
					}

				}

				// 处理被赠套餐主菜上的买一送一优惠活动
				// 只会处理最后一份
				if (jo.has("activity")) {
					LOGGER.info("开始处理套餐主菜赠送菜品上面活动");
					LOGGER.info("    " + JSONObject.fromObject(jo).toString());
					// 最后一份套餐关联买一送一优惠活动
					lastSetmeal.setIsactive(1);
					existsActivity = true;
					JSONObject aceActivity = jo.optJSONObject("activity");
					BtActivity btActivity = new BtActivity();
					btActivity.setYddh(outOrderIdInDB);
					// 与赠送菜品序号一致，更新优惠数据使用
					// 这个序号是上面拆分菜品最后一条菜品产生，十分重要，在后面处理活动表中的ydxmid和buy_ydxmid时关联更新使用
					btActivity.setDcxh(dcxh);
					// 微生活优惠活动ID需要加上10000，并且转成负数处理，为了防止与我们系统内部优惠活动ID冲突
					int active_id = (10000 + aceActivity.optInt("id", 0)) * -1;
					btActivity.setActive_id(active_id);
					// 本次活动名称，交易期间动态生成
					btActivity.setActive_current_name(aceActivity.optString("title", ""));
					// 买一送一优惠活动名称，不是本次菜品上的实际产生优惠活动名称，类似模板名称
					btActivity.setActive_name(aceActivity.optString("name", ""));
					// 买一送一优惠活动类型
					btActivity.setActive_type(aceActivity.optInt("type", 1));
					// 此时暂未生成赠送菜品预定数据，后续需要处理此值，特别重要
					// 赠送菜品预定明细ID
					btActivity.setYdxmid(-1);
					// 此时暂未生成赠送主菜预定数据，后续需要处理此值，特别重要
					// 赠送主菜预定明细ID
					btActivity.setBuy_ydxmid(-1);
					// 赠送主菜编号
					btActivity.setBuy(aceActivity.optString("buy", ""));
					// 赠送菜品编号
					btActivity.setGift(aceActivity.optString("gift", ""));
					activityList.add(btActivity);
				}
			}

			// 实时沽清
			Data dataDSO = new Data();
			DecSaleOut(dataDSO, reqParams);
			if (dataDSO.getSuccess() == 0) {
				LOGGER.info(" 下单失败:" + dataDSO.getMsg());
				data.setSuccess(0);
				data.setMsg("下单失败:" + dataDSO.getMsg());
				data.setData(new HashMap<String, Object>());
				return data;
			} else {
				int payTime = 10; // 默认十分钟
				if (orderInfo.has("payTime") && orderInfo.optInt("payTime", 0) != 0) {
					payTime = orderInfo.optInt("payTime", 0);
				}
				List<BtSaleoutdishType> saleoutdishList = new ArrayList<BtSaleoutdishType>();
				Date create_time = new Date();
				Date restrict_time = new Date(create_time.getTime() + 1000 * 60 * payTime);
				for (ReqParam reqParam : reqParams) {
					BtSaleoutdishType saleout = new BtSaleoutdishType();
					saleout.setYddh(outOrderIdInDB);
					saleout.setXmid(0);
					saleout.setXmbh(reqParam.getItem_id());
					saleout.setXmmc(reqParam.getItem_name());
					saleout.setXmsl(reqParam.getCount().doubleValue());
					saleout.setCreate_time(create_time);
					saleout.setRestrict_time(restrict_time);
					saleout.setStep(1);
					saleoutdishList.add(saleout);
				}
				orderPrecheckMapper.insertBtSaleoutdish(saleoutdishList);
			}

			// **************************************************
			// 第六步：如果微生活已经使用了活动，不能再使用我们的活动
			// **************************************************
			LOGGER.info(" 第六步：如果微生活已经使用了活动，不能再使用我们的活动……");
			double cost = total;
			double memberCost = memberTotal;
			BtYdxm1 ydxm1 = null;
			if (!existsActivity) {
				// 是否可以使用我们系统内部满减活动，特指，不是其它活动
				String isDiscountStr = PropertiesUtil.readValueForClasses("/application.properties", "isDiscount");
				if (StringUtil.getBoolean(isDiscountStr, false)) {
					ydxm1 = orderPrecheckMapper.getDiscount(total);
				}
				if (null != ydxm1) {
					cost = Double.valueOf(ydxm1.getYl5());
					ydxm1.setYddh(outOrderIdInDB);
					ydxm1.setMemo("活动");
					orderPrecheckMapper.insertBtYdxm1(ydxm1);
				}
			}
			// **************************************************
			// 第七步：保存预定账单数据
			// **************************************************
			LOGGER.info("第七步：保存预定账单数据……");
			// 处理账单主表数据
			BtYdd by = jointYdd(orderData, total, cost);
			// 保存预定账单数据
			orderPrecheckMapper.insertBtYdd(by);
			// 保存菜品数据
			orderPrecheckMapper.insertBtYdxm2(bymx);
			// 保存可选套餐明细数据
			if (tsmx.size() > 0) {
				orderPrecheckMapper.insertBtTcselectmx(tsmx);
			}
			// **************************************************
			// 第八步：处理微生活活动关联字段
			// 菜品明细数据已经生成，需要处理优惠活动表中记录赠送主菜、赠送菜品在预定明细中的ID，方便存储过程处理
			// **************************************************
			LOGGER.info(" 第八步：处理微生活活动关联字段……");
			if (activityList.size() > 0) {
				orderPrecheckMapper.insertBtActivity(activityList);
				LOGGER.info("处理微生活优惠活动赠送主菜、赠送菜品关联字段(ydxmid、buy_ydxmid)……");
				Map<Integer, Integer> buyList = new HashMap<Integer, Integer>();
				Map<Integer, Integer> giftList = new HashMap<Integer, Integer>();
				for (BtActivity activity : activityList) {
					// 如果优惠方式设定表和下发表中没有数据，默认插入一条，为了门店数据完整，总部从微生活总部同步过来数据后会下发冲掉这个优惠数据
					// 优惠方式ID=(微生活活动ID+10000)*-1，优惠方式编号=W+微生活活动ID
					String yhsx = BUY_ONE_GIVE_ONE_YHSX;
					if(activity.getActive_type() == 1){
						yhsx = BUY_ONE_GIVE_ONE_YHSX;
					}
					if(activity.getActive_type() == 2){
						yhsx = ADDITIONAL_YHSX;
					}
					if(activity.getActive_type() == 3){
						yhsx = BUY_TWO_HALF_PRICE_YHSX;
					}
					if(activity.getActive_type() == 4){
						yhsx = BUY_SPECIAL_OFFER_ITEM_YHSX;
					}
					if (orderPrecheckMapper.selectYhfssdk(activity.getActive_id()) == 0) {
						LOGGER.info("    处理本地优惠活动方式设定数据，可能优惠活动未从微生活同步下发过来……");
						orderPrecheckMapper.insertYhfssdk(activity.getActive_id(), "W" + String.valueOf((activity.getActive_id() + 10000) * -1), activity.getActive_name(), yhsx);
					}
					if (orderPrecheckMapper.selectYhfssdk_xf(activity.getActive_id()) == 0) {
						LOGGER.info("    处理本地优惠活动方式设定临时下发数据，可能优惠活动未从微生活同步下发过来……");
						orderPrecheckMapper.insertYhfssdk_xf(activity.getActive_id(), "W" + String.valueOf((activity.getActive_id() + 10000) * -1), activity.getActive_name(), yhsx);
					}
					// 定义一个已经处理菜品列表，防止一个主菜关联两个赠送菜品
//					Map<Integer, Integer> dcxhList = new HashMap<Integer, Integer>();
					for (BtYdxm2 btYdxm2 : bymx) {
						// 只能查找单品、套餐类型菜品，明细类型忽略
						if (btYdxm2.getXmsx().equals("单品") || btYdxm2.getXmsx().equals("套餐")) {
							// 根据菜品编码匹配主菜，赠送菜品忽略
							if ((btYdxm2.getIsactivity() == 0) && btYdxm2.getXmbh().equals(activity.getBuy())) {
								// 菜品编码匹配，还要检查是否已经关联其它赠送菜品
								if (!giftList.containsKey(btYdxm2.getDcxh()) && !buyList.containsKey(activity.getDcxh())) {
									// 上面已经折分赠送主菜拆成为单份，所以关联单份菜品
									LOGGER.info("    关联优惠活动对应赠送主菜、赠送菜品所在预定账单明细表中主键……");
									// 找到赠送主菜
									// 记录赠送菜品预定明细ID
									orderPrecheckMapper.updateYddActiveGiftYdxmid(activity.getYddh(), activity.getDcxh());
									// 记录赠送主菜预定明细ID
									orderPrecheckMapper.updateYddActiveBuyYdxmid(activity.getYddh(), activity.getDcxh(), btYdxm2.getDcxh());
									// 保存已经处理过的菜品点菜序号
									giftList.put(btYdxm2.getDcxh(), btYdxm2.getDcxh());
									buyList.put(activity.getDcxh(), activity.getDcxh());
									// 跳出循环过程，继续处理下个活动
									break;

								}
							}
						}
					}
				}
			}

			// **************************************************
			// 第九步：包含活动
			// **************************************************
			LOGGER.info(" 第九步：包含活动……");
			if ((isVipPrice != null) && (isVipPrice.equals("Y"))) {
				// 回传会员价格
				for (int i = 0; i < setmeal.size(); i++) {
					JSONObject jo = setmeal.getJSONObject(i);
					if (jo.optDouble("price") > jo.optDouble("memberprice")) {
						jo.put("price", jo.optDouble("memberprice"));
						jo.put("bmemberprice", 1);
					}
				}
				for (int i = 0; i < normalitems.size(); i++) {
					JSONObject jo = normalitems.getJSONObject(i);
					if (jo.optDouble("price") > jo.optDouble("memberprice")) {
						jo.put("price", jo.optDouble("memberprice"));
						jo.put("bmemberprice", 1);
					}
				}
//				data = returnData(orderData, memberTotal, memberCost, ydxm1);
				data = returnData1(orderData, total, cost, ydxm1, discountAmount, memberDiscountAmount);
			} else {
//				data = returnData(orderData, total, cost, ydxm1);
				data = returnData1(orderData, total, cost, ydxm1, discountAmount, 0);
			}

			return data;
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			data.setSuccess(0);
			data.setMsg("系统错误");
			data.setData(new HashMap<String, Object>());
			return data;
		}
	}

	/**
	 * 处理打包盒
	 *
	 * @param orderInfo
	 * @param normalitems
	 * @param setmeal
	 */
	public void generateDishPackage(JSONObject orderInfo, JSONArray normalitems, JSONArray setmeal) {
		// 检查菜品是否关联打包盒
		// 销售模式
		if (!orderInfo.containsKey("diningWay")) {
			return;
		}
		// 默认为"1"堂食
		int diningWay = orderInfo.optInt("diningWay", 1);
		// "XSMS_WM"是外带，不是外卖
		if (diningWay != 2) {
			return;
		}
		int dishCount = normalitems.size();
		// 检查单品类型菜品
		for (int i = 0; i < dishCount; i++) {
			JSONObject jo = normalitems.getJSONObject(i);
			// Dish dish = shopBaseInfoMapper.findDishInfo(jo.optInt("did", 0));
			Dish dish = shopBaseInfoMapper.findDishInfoByDishNo(jo.optString("dishsno", "0"));
			addNormalitems(normalitems, dish, jo, 1);
		}
		dishCount = setmeal.size();
		// 检查套餐类型菜品
		for (int i = 0; i < dishCount; i++) {
			JSONObject jo = setmeal.getJSONObject(i);

			Dish setDish = shopBaseInfoMapper.findDishInfoByDishNo(jo.optString("dishsno", "0"));
			addNormalitems(normalitems, setDish, jo, jo.optInt("number", 0));

            /* 小程序性能版, 套餐的餐盒配置, 只取套餐头的餐盒配置
			JSONArray maindishs = jo.optJSONArray("maindish"); // 套餐固定项
			JSONArray mandatorys = jo.optJSONArray("mandatory"); // 套餐可选项
			
			for (int j = 0; j < maindishs.size(); j++) {
				JSONObject maindish = maindishs.getJSONObject(j);
				// Dish dish = shopBaseInfoMapper.findDishInfo(maindish.optInt("id", 0));
				Dish dish = shopBaseInfoMapper.findDishInfoByDishNo(maindish.optString("dishsno", "0"));
				addNormalitems(normalitems, dish, maindish, jo.optInt("number", 0));
			}

			Map<String, Object> moietyFoodBox = new HashMap<String, Object>();
			Set<String> mfbSet = new HashSet<String>();
			
			for (int j = 0; j < mandatorys.size(); j++) {
				JSONObject mandatory = mandatorys.getJSONObject(j);
				// Dish dish = shopBaseInfoMapper.findDishInfo(mandatory.optInt("id", 0));
				Dish dish = shopBaseInfoMapper.findDishInfoByDishNo(mandatory.optString("dishsno", "0"));
				double fzsl = orderPrecheckMapper.getFzsl(mandatory.optInt("rpdid"), mandatory.optInt("id"));
				if (0.5 == fzsl) {
					if (null != dish) {
						if (!dish.getIffoodbox().equals("Y")) {
							if (dish.getFoodboxset() != -1) {
								Dish p = shopBaseInfoMapper.findDishInfo(dish.getFoodboxset());
								if (null != p) {
									String did = p.getId() + "";
									double num = ArithUtil.mul(fzsl, mandatory.optInt("number", 0));
									if (null != moietyFoodBox.get(did)) {
										moietyFoodBox.put(did, ArithUtil.add((double)moietyFoodBox.get(did), num));
									} else {
										moietyFoodBox.put(did, num);
									}
									moietyFoodBox.put("p_" + did, p);
									mfbSet.add(did);
								}
							}
						}
					}
				} else {
					addNormalitems(normalitems, dish, mandatory, jo.optInt("number", 0));
				}
			}
			List<String> mfblist = new ArrayList<String>(mfbSet);
			if (mfblist.size() > 0) {
				addNormalitemsMoiety(normalitems, mfblist, moietyFoodBox, jo.optInt("number", 0));
			}

			 */
		}
	}

//	public void addNormalitems(JSONArray normalitems, Dish dish, JSONObject jo) {
//		if (null != dish) {
//			if (!dish.getIffoodbox().equals("Y")) {
//				if (dish.getFoodboxset() != -1) {
//					Dish p = shopBaseInfoMapper.findDishInfo(dish.getFoodboxset());
//					if (null != p) {
//						JSONObject packageDish = new JSONObject();
//						packageDish.put("did", p.getDid());
//						packageDish.put("duid", p.getDid());
//						packageDish.put("dishsno", p.getDishsno());
//						packageDish.put("name", p.getName());
//						packageDish.put("dishunit", p.getDishunit());
//						packageDish.put("count", jo.optInt("number", 0));
//						packageDish.put("number", jo.optInt("number", 0));
//						packageDish.put("price", String.valueOf(p.getPrice()));
//						packageDish.put("orgprice", String.valueOf(p.getPrice()));
//						packageDish.put("memberprice", String.valueOf(p.getVipPrice()));
//						packageDish.put("dishimg", p.getDishimg());
//						packageDish.put("pic_b", p.getDishimg());
//						JSONObject cooks = new JSONObject();
//						packageDish.put("cooks", cooks);
//						cooks.put("aprice", "");
//						cooks.put("id", "");
//						cooks.put("cook", "");
//						packageDish.put("type", 1);
//						packageDish.put("membergid", "");
//						packageDish.put("bgift", 0);
//						packageDish.put("isWeigh", 0);
//						packageDish.put("bmemberprice", 0);
//						packageDish.put("bargainprice", 0);
//						packageDish.put("aprice", 0);
//						packageDish.put("pkid", p.getPkid());
//						packageDish.put("remark", "");
//						packageDish.put("omids", "");
//						normalitems.add(packageDish);
//					}
//				}
//			}
//		}
//	}

	public void addNormalitems(JSONArray normalitems, Dish dish, JSONObject jo, int setmealNumber) {
		if (null != dish) {
			if (!dish.getIffoodbox().equals("Y")) {
				if (dish.getFoodboxset() != -1) {
					Dish p = shopBaseInfoMapper.findDishInfo(dish.getFoodboxset());
					if (null != p) {
						JSONObject packageDish = new JSONObject();
						packageDish.put("did", p.getId());
						packageDish.put("duid", p.getId());
						packageDish.put("dishsno", p.getDishsno());
						packageDish.put("name", p.getName());
//						packageDish.put("dishunit", p.getDishunit());
						packageDish.put("dishunit", p.getDwname());
						packageDish.put("count", ArithUtil.mul(jo.optInt("number", 0), setmealNumber));
						packageDish.put("number", ArithUtil.mul(jo.optInt("number", 0), setmealNumber));
						packageDish.put("price", String.valueOf(p.getPrice()));
						packageDish.put("orgprice", String.valueOf(p.getPrice()));
						packageDish.put("memberprice", String.valueOf(p.getVipPrice()));
						packageDish.put("dishimg", p.getDishimg());
						packageDish.put("pic_b", p.getDishimg());
						JSONObject cooks = new JSONObject();
						cooks.put("aprice", "");
						cooks.put("id", "");
						cooks.put("cook", "");
						packageDish.put("cooks", cooks);
						packageDish.put("type", 1);
						packageDish.put("membergid", "");
						packageDish.put("bgift", 0);
						packageDish.put("isWeigh", 0);
						packageDish.put("bmemberprice", 0);
						packageDish.put("bargainprice", 0);
						packageDish.put("aprice", 0);
						packageDish.put("pkid", p.getPkid());
						packageDish.put("remark", "");
						packageDish.put("omids", "");
						normalitems.add(packageDish);
					}
				}
			}
		}
	}

	public void addNormalitemsMoiety(JSONArray normalitems, List<String> mfblist, Map<String, Object> moietyFoodBox, int setmealNumber) {
		for (int i = 0; i < mfblist.size(); i++) {
			Dish p = (Dish) moietyFoodBox.get("p_" + mfblist.get(i));
			double number = (double) moietyFoodBox.get(mfblist.get(i));
			number = Math.ceil(number);
			JSONObject packageDish = new JSONObject();
			packageDish.put("did", p.getId());
			packageDish.put("duid", p.getId());
			packageDish.put("dishsno", p.getDishsno());
			packageDish.put("name", p.getName());
//			packageDish.put("dishunit", p.getDishunit());
			packageDish.put("dishunit", p.getDwname());
			packageDish.put("count", ArithUtil.mul(number, setmealNumber));
			packageDish.put("number", ArithUtil.mul(number, setmealNumber));
			packageDish.put("price", String.valueOf(p.getPrice()));
			packageDish.put("orgprice", String.valueOf(p.getPrice()));
			packageDish.put("memberprice", String.valueOf(p.getVipPrice()));
			packageDish.put("dishimg", p.getDishimg());
			packageDish.put("pic_b", p.getDishimg());
			JSONObject cooks = new JSONObject();
			cooks.put("aprice", "");
			cooks.put("id", "");
			cooks.put("cook", "");
			packageDish.put("cooks", cooks);
			packageDish.put("type", 1);
			packageDish.put("membergid", "");
			packageDish.put("bgift", 0);
			packageDish.put("isWeigh", 0);
			packageDish.put("bmemberprice", 0);
			packageDish.put("bargainprice", 0);
			packageDish.put("aprice", 0);
			packageDish.put("pkid", p.getPkid());
			packageDish.put("remark", "");
			packageDish.put("omids", "");
			normalitems.add(packageDish);
		}
	}

	/**
	 * 生成赠送主菜列表
	 *
	 * @param normalitems
	 * @param setmeal
	 * @param buyDishList
	 * @param data
	 * @return
	 */
	public boolean generateBuyDishList(JSONArray normalitems, JSONArray setmeal, Map<Integer, String> buyDishList, Data data) {
		// 首先生成一个赠送主菜编号列表，后面拆分主菜需要使用这个列表
		// 赠送主菜可能重复，所以使用顺序序号做为标识，按照顺序匹配处理
		// 目前微生活赠送主菜不会重复，赠送菜品可能重复，只是采用通用设计结构
		// 处理一个关联主菜之后，需要删除列表之中对应赠送主菜
		// 使用点餐序号排除赠送主菜，防止重复关联，重复处理赠送主菜
		// 另外赠送主菜肯定只有一份，如果赠送主菜存在多份，需要拆成N+1，N保留，1表示赠送主菜
		// 单品
		for (int i = 0; i < normalitems.size(); i++) {
			JSONObject jo = normalitems.getJSONObject(i);
			// 只加赠送菜品，正常菜品不做处理
			if (jo.has("activity")) {
				JSONObject activity = jo.optJSONObject("activity");
				if (activity.optInt("type", 0) != 1) {
					data.setMsg("下单失败：目前只能支持买一送一优惠活动！");
					return false;
				}
				// 保存主菜菜品编号
				buyDishList.put(buyDishList.size() + 1, activity.optString("buy", ""));
			}
		}
		// 套餐
		for (int i = 0; i < setmeal.size(); i++) {
			JSONObject jo = setmeal.getJSONObject(i);
			// 只加赠送菜品，正常菜品不做处理
			if (jo.has("activity")) {
				JSONObject aceActivity = jo.optJSONObject("activity");
				if (aceActivity.optInt("type", 0) != 1) {
					data.setMsg("下单失败：目前只能支持买一送一优惠活动！");
					return false;
				}
				// 保存主菜菜品编号
				buyDishList.put(buyDishList.size() + 1, aceActivity.optString("buy", ""));
			}
		}
		return true;
	}

	/**
	 * 处理预定账单数据
	 *
	 * @param obj
	 * @param total
	 * @param cost
	 * @return
	 */
	public BtYdd jointYdd(JSONObject obj, double total, double cost) {
		JSONObject orderInfo = obj.optJSONObject("order_info");
		JSONObject ordermemo = orderInfo.optJSONObject("ordermemo");
		String tableno = orderInfo.optString("tableno");
		BtYdd by = new BtYdd();
		Shops shops = shopBaseInfoMapper.findShopsData();
		JSONObject member = orderInfo.optJSONObject("member");
		int ydbc = orderPrecheckMapper.getBcid();
		// 添加预订单号加上TS前缀
		by.setYddh(Constant.BILL_PREFIX + obj.optString("out_order_id"));
		if (null != member) {
			// 添加会员信息
			by.setHybh(member.optString("cno", ""));
			by.setLxr(member.optString("name", ""));
			by.setLxrdh(member.optString("phone", ""));
		}
		by.setYdrs(orderInfo.optInt("people"));
		by.setMen(0);
		by.setWomen(0);
		by.setEldernum(0);
		by.setChildnum(0);
		by.setShops_id(shops.getSid() + "");
		by.setTotalprice(total);
		by.setYdrq(DateUtil.getNowDateYYDDMM());
		by.setQdsj(DateUtil.getNowDateYYDDMMHHMMSS());
		by.setYdbc(ydbc + "");
		by.setDdzt("7"); // 默认预落单状态为退单状态，其实应该另标志一种状态
		by.setKwxh(ordermemo.optString("text"));
		by.setZlbh(tableno);
		by.setYl4("XCX");
		// 就餐时间
		if (orderInfo.containsKey("mealtime")) {
			String mealtime = orderInfo.optString("mealtime", ""); // 可以为空的值默认为""
			by.setMealtime(mealtime);
		}
		// 到店时间
		if (orderInfo.containsKey("timeToShop")) {
			String mealtime = orderInfo.optString("timeToShop", "");// 可以为空的值默认为""
			by.setMealtime(mealtime);
		}
		// 下单来源 “0”为门店下单；“1”为线上下单；默认为“0”。V1.1新增
		if (orderInfo.containsKey("channel")) {
			int channel = orderInfo.optInt("channel", 0); // 默认为0
			by.setChannel(channel);
		}
		// 销售模式
		if (orderInfo.containsKey("diningWay")) {
			int diningWay = orderInfo.optInt("diningWay", 1); // 默认为"1"堂食
			by.setDiningway("XSMS_TS");
			if (diningWay == 2) {
				by.setDiningway("XSMS_WM"); // "XSMS_WM"是外带，不是外卖
			}
		}
		return by;
	}

	/**
	 * 返回数据增加我们活动内容
	 *
	 * @param obj
	 *            账单数据
	 * @param total
	 *            账单金额
	 * @param cost
	 *            实结金额
	 * @param ydxm1
	 *            活动明细
	 * @return
	 */
	public Data returnData(JSONObject obj, double total, double cost, BtYdxm1 ydxm1) {
		// 回传数据对象
		Data data = new Data();
		// 账单对象
		JSONObject orderInfo = obj.optJSONObject("order_info");
		// 活动列表
		JSONArray discounts = new JSONArray();
		// 活动信息
		JSONObject discount_info = new JSONObject();
		// 查询门店信息
		Shops shops = shopBaseInfoMapper.findShopsData();
		orderInfo.put("shop_name", shops.getShopname());
		orderInfo.put("total", total);
		orderInfo.put("cost", cost);
		orderInfo.put("djqflag", true);
		orderInfo.put("cpqflag", true);
		orderInfo.put("mealfee", 0);
		orderInfo.put("weiXinPay", null);
		orderInfo.put("balance", null);
		orderInfo.put("credit", null);
		orderInfo.put("update_time", new Date());
		orderInfo.put("saulPayPull", null);
		orderInfo.put("id", null);
		orderInfo.put("msgId", null);
		// 返回账单号不需要加TS前缀
		orderInfo.put("out_order_id", obj.optString("out_order_id"));
		if (null != ydxm1) {
			discount_info.put("dsid", ydxm1.getYl2());
			discount_info.put("title", ydxm1.getXmmc());
			// 活动金额
			discount_info.put("money", ydxm1.getYl4());
			// 活动类型
			discount_info.put("type", ydxm1.getYl1());
			discount_info.put("rules", null);
			discount_info.put("memberPriceType", 0);
			discounts.add(discount_info);
			orderInfo.put("discounts", discounts);
		}
		data.setData(orderInfo);
		data.setSuccess(1);
		data.setMsg("操作成功");
		return data;
	}

	public Data returnData1(JSONObject obj, double total, double cost, BtYdxm1 ydxm1, double discountAmount, double memberDiscountAmount) {
		// 回传数据对象
		Data data = new Data();
		// 账单对象
		JSONObject orderInfo = obj.optJSONObject("order_info");
		// 活动列表
		JSONArray discounts = new JSONArray();
		// 活动信息
		JSONObject discount_info = new JSONObject();
		// 查询门店信息
		Shops shops = shopBaseInfoMapper.findShopsData();
		// 会员充值模式， 1：开启，2：关闭，3：智能推荐订单金额倍数，4：智能推荐储值规则
		String chargeMode = orderInfo.optString("charge_mode", "1");

		orderInfo.put("shop_name", shops.getShopname());
		orderInfo.put("total", total);

		// charge_mode 为1，2时正常返回，其他情况cost返回不包含会员价部分，vipcost为包含会员价的应收
		if ("1".equals(chargeMode) || "2".equals(chargeMode)) {
			orderInfo.put("cost", ArithUtil.sub(ArithUtil.sub(cost, discountAmount), memberDiscountAmount));
		} else {
			orderInfo.put("cost", ArithUtil.sub(cost, discountAmount));
			orderInfo.put("vipcost", ArithUtil.sub(ArithUtil.sub(cost, discountAmount), memberDiscountAmount));
		}

		orderInfo.put("djqflag", true);
		orderInfo.put("cpqflag", true);
		orderInfo.put("mealfee", 0);
		orderInfo.put("weiXinPay", null);
		orderInfo.put("balance", null);
		orderInfo.put("credit", null);
		orderInfo.put("update_time", new Date());
		orderInfo.put("saulPayPull", null);
		orderInfo.put("id", null);
		orderInfo.put("msgId", null);
		// 返回账单号不需要加TS前缀
		orderInfo.put("out_order_id", obj.optString("out_order_id"));
		if (null != ydxm1) {
			discount_info.put("dsid", ydxm1.getYl2());
			discount_info.put("title", ydxm1.getXmmc());
			// 活动金额
			discount_info.put("money", ydxm1.getYl4());
			// 活动类型
			discount_info.put("type", ydxm1.getYl1());
			discount_info.put("rules", null);
			discount_info.put("memberPriceType", 0);
			discounts.add(discount_info);
			orderInfo.put("discounts", discounts);
		}
		data.setData(orderInfo);
		data.setSuccess(1);
		data.setMsg("操作成功");
		return data;
	}

	/**
	 * 下单前检查门店状态
	 *
	 * @return
	 */
	public Data shopStatus() {
		Data data = new Data();

		Map<String, String> bbrqMap = shopStatusMapper.findBbrq();
		String bbrq = DateUtil.getNowDateYYDDMM();
		if (null != bbrqMap && bbrqMap.size() != 0) {
			bbrq = bbrqMap.get("bbrq");
		}

		int start = shopStatusMapper.checkOpenStart(DateUtil.parseDate(bbrq));
		int end = shopStatusMapper.checkOpenEnd(DateUtil.parseDate(bbrq));
		int ld = shopStatusMapper.loginDlcs(DateUtil.parseDate(bbrq));
		int lt = shopStatusMapper.loginTccs(DateUtil.parseDate(bbrq));
		TsGgcsk il = firstPayMapper.getGgcsToWs("ISLOGINOUTING");
		if (null != il && "Y".equals(il.getSdnr())) {
			data.setSuccess(0);
			data.setMsg("门店正在盘点，请稍后再试");
		} else if (0 == start) {
			data.setSuccess(0);
			data.setMsg("门店未营业");
		} else if (0 < end) {
			data.setSuccess(0);
			data.setMsg("门店已打烊");
		} else if (ld <= lt) {
			data.setSuccess(0);
			data.setMsg("员工未登录");
		} else if (checkDysj(bbrq) > 0) {
			data.setSuccess(0);
			data.setMsg("系统日期已经大于营业日期，请做打烊后再进行当前操作！");
		} else {
			data.setSuccess(1);
			data.setMsg("正常营业");
		}
		return data;
	}

	/**
	 * 下单前检查是否打烊
	 *
	 * @param bbrq
	 * @return
	 */
	public int checkDysj(String bbrq) {
		String sys24yy = shopStatusMapper.getSys24yy();
		String dysjsz = shopStatusMapper.getDyxzsjd();
		Date bbrqD = DateUtil.parseDateAll(bbrq + " 00:00:00");
		if ("Y".equals(sys24yy)) {
			int day = DateUtil.daysBetween(bbrqD, DateUtil.parseDateAll(DateUtil.getNowDateYYDDMM() + " 00:00:00"));
			return day;
		} else {
			if ("".equals(dysjsz) || null == dysjsz) {
				dysjsz = "00:00:00";
			}
			Date bbrqDy = DateUtil.parseDateAll(bbrq + " " + dysjsz);
			Date dysj = DateUtil.parseDateAll(DateUtil.getNowDateYYDDMMHHMMSS());
			int day = DateUtil.daysBetween(DateUtil.getPlusDay(bbrqDy, 1), dysj);
			return day;
		}
	}

	public String getItemeTaste(JSONArray memos) {
		String itemTaste = "";
		for (int i = 0; i < memos.size(); i++) {
			JSONObject memo = memos.getJSONObject(i);
			JSONArray items = memo.optJSONArray("items");
			for (int j = 0; j < items.size(); j++) {
				JSONObject item = items.getJSONObject(j);
				itemTaste += "," + item.optString("ordermemo");
			}
		}
		if (!"".equals(itemTaste)) {
			itemTaste = itemTaste.substring(1, itemTaste.length());
		}
		return itemTaste;
	}

	public String getItemeTaste1(String yddh, JSONObject jo,  int dcxh, List<BtYdxm2> bymx, int setmeal) {

		JSONArray toppings=jo.optJSONArray("toppings");

		if(CollectionUtils.isNotEmpty(toppings)){
			for(int i=0;i<toppings.size();i++){
				JSONObject topping=toppings.getJSONObject(i);
				Integer ingredientId=topping.optInt("tpid");
				double xmdj = ArithUtil.div(topping.optDouble("addprice", 0), 100);
				int number=topping.optInt("count");
				if(setmeal==2){
					number=1;
				}

				TsCmk cmk=orderPrecheckMapper.getDishByIngredientId(ingredientId);

				BtYdxm2 ydxm2 = initJjcToYdxm2(yddh, cmk, xmdj, number, dcxh);
				bymx.add(ydxm2);

			}
		}


		String itemTaste = "";
		JSONArray memos = jo.optJSONArray("memo");
//		double aprice = jo.optDouble("aprice", 0);
		int number = jo.optInt("number");
		if (setmeal == 2) {
			number= 1;
		}
		if (null != memos && memos.size() > 0) {
			for (int i = 0; i < memos.size(); i++) {
				JSONObject memo = memos.getJSONObject(i);
				JSONArray items = memo.optJSONArray("items");
				for (int j = 0; j < items.size(); j++) {
					JSONObject item = items.getJSONObject(j);
					int omid = item.optInt("omid", 0);
					double xmdj = ArithUtil.div(item.optDouble("aprice", 0), 100);
//					aprice = ArithUtil.sub(aprice, xmdj);
					TsCmk cmk = orderPrecheckMapper.getAddDish(omid);
					if (null == cmk) {
						itemTaste += "," + item.optString("ordermemo");
					} else {
						BtYdxm2 ydxm2 = initJjcToYdxm2(yddh, cmk, xmdj, number, dcxh);
						bymx.add(ydxm2);
					}
				}
			}
			if (!"".equals(itemTaste)) {
				itemTaste = itemTaste.substring(1, itemTaste.length());
			}
//			if (setmeal == 2) {
//				jo.put("aprice", aprice);
//			}
		} else {
			itemTaste = jo.optString("remark");
		}
		return itemTaste;
	}

	/**
	 * 处理大众营销优惠菜品数据
	 * 将被分离的优惠组装到主菜品数据中
	 *
	 * @param orderInfo
	 * @param normalitems
	 * @param setmeal
	 */
	//此处有个疑问，活动中赠送菜品是否需要增加餐盒  generateDishPackage
	public void discountItemJoint(JSONObject orderInfo, JSONArray normalitems, JSONArray setmeal) {
		JSONArray activityDishes = orderInfo.optJSONArray("activityDishes");
		for (int i = 0; i < activityDishes.size(); i++) {
			JSONObject buyDish = activityDishes.getJSONObject(i).optJSONObject("buyDish");
			JSONObject giftDish = activityDishes.getJSONObject(i).optJSONObject("giftDish");
			JSONObject activity = activityDishes.getJSONObject(i).optJSONObject("activity");

			buyDish.put("price", ArithUtil.div(activity.optDouble("buyDishPrice"), 100, 2));
			if (!"4".equals(activity.optString("type"))) {
				giftDish.put("price", ArithUtil.div(activity.optDouble("giftDishPrice"), 100, 2));
			}

			buyDish.put("dcxh", i + 1);
			// 根据菜品类型，加入到不通集合中方便后续统一处理。1：单品，2：套餐
			if (buyDish.optString("type").equals("1")) {
				normalitems.add(buyDish);
			}
			if (buyDish.optString("type").equals("2")) {
				setmeal.add(buyDish);
			}
			// 处理活动数据，与之前活动数据稍有不通，单独处理吧

		}
	}

	public double dishPromotion(String outOrderIdInDB, JSONArray joa) {
		double discountAmount = 0;
		for (int i = 0; i < joa.size(); i++) {
			JSONObject items = joa.getJSONObject(i);
			if (items.containsKey("dishPromotion")) {
				JSONObject promotion = items.optJSONObject("dishPromotion");
				String yhsx = ORDER_PROMOTION;
				int active_id = (10000 + promotion.optInt("promotion_id", 0)) * -1;
				if (orderPrecheckMapper.selectYhfssdk(active_id) == 0) {
					LOGGER.info("    处理本地优惠活动方式设定数据，可能优惠活动未从微生活同步下发过来……");
					orderPrecheckMapper.insertYhfssdk(active_id, "W" + promotion.optInt("promotion_id", 0), promotion.optString("promotion_name", ""), yhsx);
				}
				if (orderPrecheckMapper.selectYhfssdk_xf(active_id) == 0) {
					LOGGER.info("    处理本地优惠活动方式设定临时下发数据，可能优惠活动未从微生活同步下发过来……");
					orderPrecheckMapper.insertYhfssdk_xf(active_id, "W" + promotion.optInt("promotion_id", 0), promotion.optString("promotion_name", ""), yhsx);
				}
				double promotionDiscountAmount = ArithUtil.div(promotion.optDouble("promotion_discount_amount", 0), 100, 2);
				discountAmount = ArithUtil.add(discountAmount, promotionDiscountAmount);
				firstPayMapper.insertBtYddPayActive(outOrderIdInDB, "微生活通用营销", promotionDiscountAmount, "", "ACTIVITY", active_id);
			}
		}
		return discountAmount;
	}

	// 实时沽清
	private void DecSaleOut(List<ReqParam> reqParams) {
		if (!(null != InitDataListener.ggcsMap.get("ISREALTIMEDISHCOUNT") && InitDataListener.ggcsMap.get("ISREALTIMEDISHCOUNT").equals("Y"))) {
			LOGGER.info("未启用实时沽清参数ISREALTIMEDISHCOUNT");
			return;
		}
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("dishinfo", GsonUtil.GsonString(reqParams));
		CommApiData commApiData = new CommApiData();
		saleOutApiService.SaleOutDec(commApiData, jsonObject);
	}

	// 实时沽清
	private Data DecSaleOut(Data data, List<ReqParam> reqParams) {
		if (!(null != InitDataListener.ggcsMap.get("ISREALTIMEDISHCOUNT") && InitDataListener.ggcsMap.get("ISREALTIMEDISHCOUNT").equals("Y"))) {
			LOGGER.info("未启用实时沽清参数ISREALTIMEDISHCOUNT");
			data.setSuccess(1);
			return data;
		}
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("dishinfo", GsonUtil.GsonString(reqParams));
		CommApiData commApiData = new CommApiData();
		saleOutApiService.SaleOutDec(commApiData, jsonObject);

		if (commApiData.getCode().equals(0)) {
			data.setSuccess(1);
		} else {
			data.setSuccess(0);
			data.setMsg(commApiData.getMsg());
		}
		return data;
	}

	@Override
	public void scheduleAddSaleOut() {
		Date nowDate = new Date();
		List<ReqParam> saleoutdishList = orderPrecheckMapper.getBtSaleoutdish(nowDate);
		AddSaleOut(saleoutdishList);
		orderPrecheckMapper.updateAddSaleOut(-1, nowDate);
	}

	private void AddSaleOut(List<ReqParam> reqParams) {
		if (!(null != InitDataListener.ggcsMap.get("ISREALTIMEDISHCOUNT") && InitDataListener.ggcsMap.get("ISREALTIMEDISHCOUNT").equals("Y"))) {
			LOGGER.info("未启用实时沽清参数ISREALTIMEDISHCOUNT");
			return;
		}
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("dishinfo", GsonUtil.GsonString(reqParams));
		CommApiData commApiData = new CommApiData();
		saleOutApiService.SaleOutAdd(commApiData, jsonObject);
	}

	private BtYdxm2 initJjcToYdxm2(String yddh, TsCmk cmk, double xmdj, int xmsl, int dcxh){
		BtYdxm2 ydmx2 = new BtYdxm2();
		ydmx2.setIsactive(0);
		ydmx2.setYddh(yddh);
		ydmx2.setXmid(cmk.getCmid());
		ydmx2.setXmbh(cmk.getCmbh());
		ydmx2.setXmmc(cmk.getCmmc1());
		ydmx2.setXmsx("加价菜");
		// 单价还是菜品原价
		ydmx2.setXmdj(new BigDecimal(xmdj));
		ydmx2.setXmsl(xmsl);
		ydmx2.setZkl(100);
		// 菜品实结金额
		ydmx2.setTotalprice(new BigDecimal(ArithUtil.mul(xmdj, xmsl)));
		ydmx2.setDwbh(cmk.getDwbh());
		ydmx2.setKwbh("");
		// 菜品金额使用原价*数量
		ydmx2.setCmje(new BigDecimal(ArithUtil.mul(xmdj, xmsl)));
		ydmx2.setTcbh("");
		ydmx2.setTcdch(0);
		ydmx2.setFzsl(0);
		ydmx2.setFzje(new BigDecimal(0));
		ydmx2.setDcxh(dcxh);
		BigDecimal decimal = new BigDecimal(ArithUtil.mul(xmdj, xmsl));
		BigDecimal setScale = decimal.setScale(2, BigDecimal.ROUND_HALF_UP);
		ydmx2.setYl3(setScale + "");

		return ydmx2;
	}

}
