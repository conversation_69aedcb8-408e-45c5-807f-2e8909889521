package com.tzx.publics.common;

/**
 * <AUTHOR>
 * @Date 2019-05-05
 * @Descption
 **/
public class Version {
	//通用API版本号
	public static final String COMMAPI_VERSION = "3.0.3";
	//凯景版本号
	public static final String FMCGBI_VERSION = "1.0.1";
	//ECO版本号
	public static final String ECO_VERSION = "3.1.1";
    //APP服务版本号
    public static final String MOBILEPOS_VERSION = "2.1.6";
    //小程序服务版本号
    public static final String MINIAPP_VERSION = "2.8.9";
    //监听服务版本号
    public static final String RECEVIER_VERSION = "3.0.5";
    //主版本号
    public static final String BASE_VERSION = "3.8.57";

	//通用api服务  沽清服务3.0.1版本
	public static final String COMMAPI_VERSION_LOG  = "20200119 3.0.1 通用沽清功能。\n" +
			"20200119 3.0.2 沽清支持小数的沽清。\n";
	public static final String ECO_VERSION_LOG  = "正式版本发布。\n" +
			"20191111，3.0.0 ECO深度对接接口开发完成\n" +
			"20191113，3.0.2 消息提示改为明确提示\n" +
			"20191113，3.0.3 请求包后面带=格式化处理\n"  +
			"20191118，3.0.4 退单消息提示改为明确提示\n" +
			"20191119，3.0.5 优化打印，改为拒单模式；优化账单号获取事务限制；优化通用锁\n" +
			"20191122，3.0.6 账单号获取锁优化\n" +
			"20191129，3.0.7 增加订单明细跟订单总金额一致性验证\n"+
			"20191130，3.0.8 double计算精度错误出现一长串小数的bug修复\n"+
			"20191202，3.0.9 eco接单取消两边菜目属性的校验，以POS的为准\n"+
	        "20191212，3.0.10 eco打印清单功能 \n"+
			"20191216，3.0.11 eco增加小程序跟eco外卖账单号获取同步锁 \n"+
			"20191219，3.0.12 1、eco退单增加取消发票功能， \n"+
			"2、eco重复退单返回状态改为成功 \n"+
			"3、兼容eco退单报文outid 和outorderid两种订单字段模式\n"+
			"20191220，3.0.13 1、 取消发票改为只要可开的都执行退\n"+
					"2、eco接单及退单业务跟 打印分离 \n"+
			"20191220，3.0.14 1、 eco取消单增加是否打印清单控制\n"+
			"20191225，3.0.15 1、 eco部分退单功能支持\n"+
			        "2、外卖几号篮子功能支持\n"+
			"20200106，3.0.16 1、 eco清单模版放到结账单模版之前打印\n"+
					"2、优化声音提醒模块，每一笔新订单至少响一次\n"+
			"20200106，3.0.17 1、 优化声音提醒模块，同一次轮询数量新订单响过，状态新订单就不响了\n"+
	        "20200109，3.0.18 1、 部分退单新逻辑实现\n"+
			"2、历史退单支持\n"+
			"20200110，3.0.19 1、 退单通知优化\n"+
			"2、补打账单通过参数控制是否补打清单\n"+
			"20200110，3.0.20 1、 补打账单清单打印错误修复\n"+
			"2、退单失败返回明确信息\n"+
			"20200113，3.0.21 1、 历史订单提示信息显示错误修复\n"+
			"20200117，3.0.22 1、通用沽清接口开发 \n"+
			" 2、 eco沽清功能开发\n"+
			"20200119，3.0.23 1、沽清接口增加轮询锁 \n"+
			" 2、 沽清接口返回剩余数量\n"+
			"20200204，3.0.24 1、eco套餐实时沽清功能包括接单、部分退款、全部退款时的沽清。\n"+
			"20200303，3.0.25 1、eco微信自接单如果kdishno为空就读itemid。\n"+
			"20200305，3.0.26 1、eco菜品数量只能保存整数bug修复。\n"+
			"20200417，3.0.27 1、pos兼容eco和saaas订单\n"+
			"20200417，3.0.28 1、订单来源错误bug修复\n"+
			"20200910，3.0.29 1、增加外卖异常订单对接功能，同时验证处理几个对接新版打印渠道处理错误问题\n";

	//凯景服务升级日志
    public static final String FMCGBI_VERSION_LOG  = "20200117 1.0.1 凯景服务第一版。\n";

	//APP服务升级日志
    public static final String MOBILEPOS_VERSION_LOG  = "20190510 2.0.1 合并老代码。\n" +
    		"20190528，1.18，修改支付流程，不自动退款，增加异常账单处理。\n" +
    		"20190529，1.19，增加储值支付赠送金额计入优惠操作。\n" +
    		"20190529，1.20，暂时屏蔽异常账单处理，修改自动退款，改为只删账单不退款。\n" +
    		"20190531，2.0.2，1.18，1.19，1.20，版本迁移。\n" +
    		"20190612，2.0.3，新增活动 买减，买折， 第二份打折，第二份半价，多明细打折,满加价购。\n" +
    		"20190612，2.0.3，新增限制停用账号登录。\n" +
    		"20190702，2.0.4，新增美团支核一体。\n" +
    		"20190703，2.0.5，支持柜台点餐模式。\n" +
    		"20191031，2.0.6，修改微生活券使用规则，购买金额计入付款。\n" +
    		"20191118，2.0.7，增加app本地打印。\n" +
    		"20191205，2.0.8，修复乡村基app云闪付支付bug。\n" +
    		"20200309，2.0.9，微生活储值，积分支付改为根据公共参数判断是否拆分优惠；修改生成微信支付宝商户订单号规则。\n" +
    		"20200319，2.1.1，1.兼容2.0版本，增加实时沽清。\n" +
    		"20200421，2.1.2，1.对接微生活折扣券；2.抹零。\n" +
    		"20200507，2.1.3，1.新版app微生活折扣券。\n";
    //小程序服务升级日志
    public static final String MINIAPP_VERSION_LOG = "20190419，2.5，自动附加餐盒需要处理图片数据。\n" +
            "20190421，2.5.1，优惠券转成优惠，不再记成付款。\n" +
            "20190421，2.5.2，优惠券转成优惠，不再记成付款。\n" +
            "20190421，2.5.3，优惠券转成优惠，不再记成付款。\n" +
            "20190422，2.5.4，付款金额比账单金额多的部分记成微生活多收。\n" +
            "20190427，2.5.5，优化拼装套餐固定、可选、可选明细过程。\n" +
            "20190428，2.5.6，结账增加并发处理机制，防止同一时间同时结账。\n" +
            // 合并代码之前2.0修改或者增加功能内容
		    "20190516，2.5.7，增加微生活会员价支持。\n" +
		    "20190516，2.5.8，增加可选套餐默认选中功能，优化查询会员等级sql。\n" +
		    "20190516，2.5.9，增加微生活会员价格支持，预落单回传微生活会员价金额，套餐明细无须处理。\n" +
		    "20190516，2.5.9.1，解决套餐会员价计算根据数量翻倍问题。\n" +
		    "20190516，2.5.9.2，微生活会员价，会员价大于标准价，则使用标准价。\n" +
		    "20190530，2.6.0，微生活储值金额中赠送金额部分需要转优惠。\n" +
			"20190530，2.6.1，解决微生活储值金额中赠送金额部分需要转优惠BUG。\n" +
			"20190530，2.6.2，如果微生活代金券金额大于账单金额，需要生成一条0金额小程序付款记录。\n" +
			"20190530，2.6.3，修改会员价和券多收场景下的计算优惠的实现方式，改成预落定单过程计算多收。\n" +
			"20190704，2.6.4，修改自动退款失败问题。\n" +
			"20190704，2.7.1，增加众赏小程序点餐。\n" +
			"20190916，2.7.2，众赏小程序增加餐盒处理，增加结账清单打印。\n" +
			"20191009，2.7.3，套餐餐盒不再关联主项，改成关联明细。\n" +
			"20191012，2.7.4，增加是否预约订单处理。\n" +
			"20191031，2.7.5，增加券结算金额拆分，微生活套餐明细餐盒费。\n" +
			"20191204，2.7.6，众赏小程序，解决获取账单号死锁问题。\n" +
			"20191205，2.7.7，微生活小程序，增加多规格支持。\n" +
			"20191205，2.7.8，众赏小程序，关单增加前置验证，菜品是否售卖。\n" +
			"20200210，2.7.9，众赏小程序，修复判断菜品价格是否一致精度问题。\n" +
			"20200214，2.8.1，众赏小程序，支持外卖。\n" +
			"20200309，2.8.2，1、微生活小程序，储值，积分支付改为根据公共参数判断是否拆分优惠；"
						+ "2、对接微生活小程序，新版大众营销；"
						+ "3、更改微生活小程序，获取取餐号规则。\n" +
			"20200319，2.8.3，1.兼容2.0版本，增加实时沽清；2.支持微信券记录优惠。\n" +
			"20200402，2.8.5，1.修改实时沽清逻辑；2.顺溜余额不足不可使用会员价。\n" +
			"20200421，2.8.6，1.微生活小程序，使用会员价优惠，优惠被四舍五入问题。\n" +
			"20200508，2.8.7，1.众赏落单增加打样校验，防止众赏在打烊后推送异常账单，造成数据丢失。\n";
    		// 合并结束
    //监听升级日志
    public static final String RECEVIER_VERSION_LOG  = "20190510 3.0.1 合并老代码 \n" +
    		"20190510，3.0.1,实现数据上传日志查看功能。\n"+
    		"20190820，3.0.3,增加 TS_THIRD_INFO_XF 表下发。\n"+
    		"20191122，3.0.4,支持通过fastDFS下载数据。\n"+
			"20200225，3.0.5,POS同步更新状态回传总部。\n";
    
    //主版本升级日志
    public static final String BASE_VERSION_LOG  = "正式版本发布。\n" +
    		"20190528，3.0.2 增加上传监控日志和查看版本号的界面。\n" +
    		"20190531，3.0.3 2.0APP合并代码。\n" +
    		"20190611，3.0.4 2.0小程序合并代码。\n" +
    		"20190611，3.0.5 增加银杏岛定位系统接口。\n" +
    		"20190611，3.0.6 修改会员价和券多收场景下的计算优惠的实现方式，改成预落定单过程计算多收。\n" +
    		"20190611，3.0.7 修改自动退款失败问题。\n" +
    		"20190820，3.0.8 包含MINIAPP_VERSION 2.7.1，RECEVIER_VERSION 3.0.3。\n" +
    		"20190916，3.0.9 修改打印动态库路径，直接用pos根目录。。\n"+
    		"20191009，3.0.10 小程序有修改。\n"+
    		"20191012，3.0.11 小程序有修改。\n"+
    		"20191122，3.0.14  监听服务3.0.4。\n"+
    		"20191204，3.0.15 小程序2.7.6。\n"+
    		"20191205，3.0.16 小程序2.7.7；app2.0.8。\n"+
    		"20200117，3.0.17 凯景1.0.1。\n"+
    		"20200210，3.0.18 小程序2.7.9。\n"+
    		"20200214，3.0.19 小程序2.8.1。\n"+
    		"20200309，3.1.0  小程序2.8.2；APP2.0.9。\n"+
    		"20200319，3.1.1  小程序2.8.3；APP2.1.1。\n"+
			"20200330，3.1.2  增加eco音量单独控制功能。\n"+
			"20200402，3.1.3 小程序2.8.5。\n"+
			"20200417，3.1.5  eco3.0.27。\n"+
			"20200421，3.1.6  小程序 2.8.6，app 2.1.2。\n"+
			"20200426，3.1.7  eco外卖动态渠道支持\n"+
			"20200506，3.1.8  eco部分退款退款菜品获取失败bug修复\n"+
			"20200507，3.1.9 app微生活折扣券新版；众赏小程序，落单增加打样校验，防止众赏在打烊后推送异常账单，造成数据丢失\n"+
			"20200518，3.2.0 1.app，微生活小程序，众赏小程序，增加不计收入付款拆分;2.所有折扣券默认不可与其他券同享；3.修复折扣券，可优惠菜品金额为 0元，还能用券bug；\n"+
			"20200525，3.2.1 1.POS加价菜功能总部新增字段下发支持\n"+
			"20200525，3.2.2 1.app支持多规格（对应apk 1.5.21）；2.小程序支持加价菜；3.增加读取mq配置文件，把相关数据写入数据库（临时方案，待李先生全部门店升级后取消改功能）；\n"+
			"20200609，3.2.3 1.app微生活会员清台未接收到返回，pos退单后，还可以继续支付bug修复（对应apk 1.5.22）；2.eco话务点餐无法接单提示菜品编码为null的bug修复；\n"+
			"20200707，3.2.4 1、eco话务点餐无法接单 2、挂账单位增加有效日期下发\n"+
			"20200707，3.2.5 1、挂账单位增加有效日期下发去掉字段下划线\n"+
			"20200708，3.2.6 1、挂账单位下发修改\n"+
			"20200715，3.2.7 1、微信支付宝支付，增加 客户序号 88（李先生）判断，非88  菜品金额传 单价实结金额； 2、微生活小程序，口味分组增加判断，如果min>0 ,是否必选字段传 必选，如果 min=max，是否单选传单选；3、众赏小程序，拆分储值支付赠送金额到优惠；\n"+
			"20200910，3.2.9 1、晶品定时上传物业接口\n"+
			"20200916，3.3.1 1、外卖增加功能，对应版本：3.0.29。2、app增加企迈会员对接。\n"+
			"20201020，3.3.3 1、增加华为短信接口。\n"+
			"20201022，3.3.5 1、增加异常部分退单功能;部分退单之后还能整单退单,但是不能继续进行二次部分退单。\n"+
			"20201026，3.3.6 1、新增企迈会员，积分消费支付方式。\n"+
			"20201104，3.3.7 1、修改企迈上送订单金额匹配问题，支付方式写死现金；2、修改企迈相关接口菜品编号改为id；。\n"+
			"20201112，3.3.8 1、app，小程序，支持非必选项目组；。\n"+
			"20201118，3.3.9 1、新增给李先生企迈小程序推送沽清信息；2、新增老娘舅企迈以菜品编号作为落单菜品标识，修改餐盒逻辑；。\n"+
			"20201121，3.3.10 1、新增老娘舅企迈小程序落单接口；。\n"+
			"20201125，3.3.11 1、修改企迈账单上送接口，兼容全部账单上送；2、李先生企迈小程序修改餐盒逻辑；。\n"+
			"20201208，3.3.12 1、app支付接口新增操作员编号判断，没有编号不能支付；2、app前端修改息屏自动清进程时间，由30分钟改为10分钟；3、修改app前端获取操作员编号方式，由本地获取改为从登录返回数据获取；。\n"+
			"20201212，3.3.13 1、app先用美团券再用聚合支付扫描美团券导致 tq_yhmtcouponsorders 清除bug修复\n"+
			"20201214，3.3.14 1、乡村基企迈会员上传功能\n"+
			"20201215，3.3.15 1、乡村基企迈会员上传功能\n"+
			"20201231，3.3.16 1、李先生企迈小程序，增加沽清提示，修复沽清操作\n"+
			"20210105，3.3.18 1、自运营外卖支持可选套餐\n"+
			"20210107，3.3.19 1、** 外卖异常订单补打接口receiveError 增加自运营外卖可选套餐及付款流水的支持;2、** 乡村基企迈上传过滤掉pos取消的菜品;\n"+
			"20210107，3.3.20 1、** 老娘舅企迈增加预约单;\n"+
			"20210112，3.3.21 1、** 乡村基增加科传物业接口上传;\n"+
			"20210112，3.3.22 1、处理按比例拆分储值，精度问题；2、企迈小程序退款，增加超时时间；\n"+
			"20210121，3.3.23 1、 老娘舅企迈小程序区分套餐 固定项，可选项；2、 李先生企迈小程序，自动上传日始，打样状态；3、 李先生服务区方兴物业上传接口修改；\n"+
			"20210203，3.5.1 1、 物业自动上传改版；2、李先生新增金鹿物业新版实时上传；\n"+
			"20210218，3.5.2 1、 李先生物业支持金鹿打样上传；\n"+
			"20210304，3.5.3 1、增加app是否显示菜品字段控制；2、ta_clmxk增加下发字段yl3；3、 增加app，小程序增加是否开启kvs判断；4、 eco深度对接支持 kvs；\n"+
			"20210316，3.5.5 1、 深度对接外卖发送kvs通过 单独参数控制POS_ECO_SFQYKVS；2、 调用eco 转发接口；\n"+
			"20210329，3.5.6 1、 众赏隐藏手机号；2、方形物业修改上传逻辑，退单原单也重新上传，并且增加判断，原单失败退单也不上传；3、企迈会员消费提交，超时时间缩短为15秒，重复提交机制增加成功状态提交;4、 李先生企迈提交消费增加机构编号；\n"+
			"20210329，3.5.6 1、 上传队列增加上传任务对应数据库guid的去重，对于已经存在的进行更新处理，解决上传队列不停增大的问题；2、mq实时上传增加全局网络状态功能，网络状态不正常并且当前上传队列大于0的情况不生成新的上传数据；3、打烊上传和数据上传增加http接口模式;4、 李先生胶东机场物业上传；\n"+
			"20210316，3.5.9 1、 监控提示信息VST_POS_EXCEPTION_INFO下发支持；\n"+
			"20210521，3.6.1 1、 乡村基企迈上送订单增加储值支付 bizid；2、 乡村基企迈上送订单更改orderno获取方式；3、乡村基企迈上送订单更改券明细列表券码获取方式 ；4、 和合谷众赏小程序，增加实付应付金额校验；\n"+
			"20210608，3.6.2 1、 老娘舅企迈小程序，增加储值拆分；\n"+
			"20210608，3.6.3 1、 老娘舅企迈小程序接口支持跨域；2、 老娘舅企迈小程序接口支持企迈自定义拆分；3、 pos沽清同步企迈小程序接口异步改为同步；\n"+
			"20210709，3.6.5 1、 ECO外卖一维码支持；2、李先生企迈升级支持新版会员，小程序;\n"+
			"20210714，3.6.7 1、 ts_psjgsdk，新增了2个下发字段：rotationpic6（视频，长度255），rotationpic7（锁屏长度100）;\n"+
			"20210719，3.6.8 1、 李先生企迈小程序增加一个公共参数XCXITEMNAMETYPE，用来控制落单菜品名称来源;\n"+
			"20210723，3.7.1 1、 乡村基，app兼容乡村基模式，屏蔽会员入口;\n"+
			"20210803，3.7.2 1、 李先生企迈小程序，增加微信支付宝支付商户订单号转换;\n"+
			"20210818，3.7.3 1、 李先生新增哈尔滨太平机场物业接口;\n"+
			"20210906，3.7.5 1、 大米先生新增协信星光物业接口，华润五彩城物业接口,修改科传物业;\n"+
			"20210913，3.7.6 1、 李先生大兴机场物业接口对接;\n"+
			"20211013，3.7.7 1、 和合谷重赏小程序明细金额与账单金额校验;2、 和合谷重赏小程序支持部分打包；\n"+
			"20211020，3.7.8 1、 李先生手持app，新增核销抖音券功能；\n"+
			"20211025，3.7.9 1、 乡村基，TS_FKFSSDK_XF新增字段 THIRDSOURCE，用来绑定付款方式对应的挂账外卖渠道 ；\n"+
			"20211110，3.7.10 1、 鼎友，eco外卖增加虚拟菜品落单模式 ；\n"+
			"20211116，3.7.11 1、 李先生抖音券增加代金券 ；\n"+
			"20211213，3.7.12 1、 李先生第二份打折，第二份半价活动修复 ；2、和合谷众赏小程序，落单返回数据增加报表日期字段。\n"+
			"20211220，3.7.13 1、 李先生增加渠道表下发。\n"+
            "20211230，3.7.15 1、 ECO_RECEIVE_PATTERN 增加模式 3 ， eco外卖，单个菜品不存在时也可以正常下单，需要增加异常菜品绑定。\n"+
			"20211230，3.7.16 1、 新增大米先生绿地潮方物业接口对接。2、 ts_cmk新增下发字段IFCHANGEABLEPRICE。\n"+
			"20220110，3.7.17 1、 新增蒸小皖 南陵服务区驿达物业接口对接 。\n"+
			"20220119，3.7.18 1、 新增ECO有退单需要确认时的语音提示；2、 ECO外卖新增平台店名称存储，用于打印 。\n"+
			"20220304，3.7.19 1、 新增蒸小皖合肥长桥机场物业 。\n"+
			"20220318，3.7.20 1、 乡村基企迈账单上送，储值赠送 计入实收；2、订单上送 有小数送小数，不取整了；2、支核一体增加bizid字段 。\n"+
			"20220325，3.7.21 1、 顺旺基，企迈上送增加 bizid，券类型，合并储值优惠，修改菜品数量为double型，优惠券加券码，菜品金额用原价单价 。\n"+
			"20220325，3.7.22 1、 鼎友，解决企迈请求 无法获取证书问题 。\n"+
			"20220516，3.7.24 1、 对接OMP，支持微生活小程序先付直接通过结账（清台）接口下单；2、 对接OMP，支持微生活小程序菜品多规格speccode参数；3、微生活小程序对接参数配置迁移到公共参数配置表；4、 新增PMS订单同步接口功能。\n"+
			"20220516，3.7.25 1、 增加参数控制是window版服务还是Android版服务；2、 去掉多余的data属性。\n"+
			"20220516，3.7.26 1、 新增表下发 TQ_YHFKFSSZ_XF。\n"+
			"20220516，3.8.1 1、 omp小程序，支持优惠券，储值优惠拆分。2、 omp小程序，修复口味bug。\n"+
			"20220516，3.8.2 1、 取消李先生mq信息从xml转移到数据库操作，该功能已全部执行完成，以后不需要了。2、 新增下发成功后通知pos功能。\n"+
			"20220802，3.8.2 1、 乡村基，企迈上传，修改bizid，截掉后缀。\n"+
			"20220901，3.8.5 1、 新增满N免M优惠活动新增字段下发。\n"+
			"20220913，3.8.6 1、 沫切企迈小程序增加配料功能。\n"+
			"20221011，3.8.8 1、 泰希家汕头万象城华润物业对接。2、 ECO外卖 ，增加重复单返回 账单编号。\n"+
			"20221019，3.8.9 1、 增加口味排序字段下发。2、 微生活小程序，修改口味排序。\n"+
			"20221027，3.8.10 1、 修改app美团券 表 tq_yhmtcouponsorders 中券面金额存储值。\n"+
            "20221110，3.8.12 1、 大米先生，新增   徐汇日月光，无限极荟，金虹桥国金中心物业接口。2、 新增监控新增两个表的下发。3、 泰希家汕头万象城物业修改不上传不及收入付款金额。\n"+
			"20221117，3.8.13 1、 ECO增加饿了么骑手取餐条形码。\n"+
			"20221221，3.8.15 1、 三合一增加心跳接口。2、 新增大米先生 无锡万象城物业接口。3、 新增大米先生 上海金融街物业接口。\n"+
            "20230110，3.8.16 1、 新增李先生 武铁传媒物业接口。\n"+
            "20230109，3.8.17 1、 新增李先生支核一体功能。\n"+
			"20230203，3.8.18 1、 新增大米先生，长沙星沙万象汇物业接口。\n"+
			"20230216，3.8.19 1、 下发表Yhfssz新增字段IFZDYZK、MAXZKL。\n"+
			"20230306，3.8.20 1、 和合谷众赏小程序，落单增加门店状态校验。\n"+
			"20230329，3.8.21 1、 微生活小程序新增配料功能对接。2、 新增大米先生上海嘉里不夜城店物业接口。\n"+
			"20230403，3.8.22 1、 一品多码下发接收。\n"+
			"20230410，3.8.23 1、 ECO外卖对接支持抖音外卖。\n"+
			"20230428，3.8.25 1、 增加热门菜品表下发；2、取消企迈小程序同步基础数据通知接口。\n"+
			"20230517，3.8.26 1、 大米先生上海星扬餐厅物业接口。\n"+
			"20230522，3.8.27 1、 ECO外卖对接饿了么配料功能。 2、ECO外卖支持按名称匹配菜品（需要启用系统参数“ECO外卖菜品是否按名称匹配”）。\n"+
			"20230601，3.8.28 1、 李先生太原南站物业接口。2、 修改app支核一体使用逻辑，只要查询了会员，不允许使用支核一体。\n"+
			"20230630，3.8.29 1、 拉取总部数据新增lsdh特殊标记，jjcrwid。\n"+
			"20230704，3.8.30 1、 新增通过华为OBS下发。 \n"+
			"20230707，3.8.31 1、 手持增加微生活券，按月判断条件。 2、 北京大兴机场物业支付方式优化。\n"+
			"20230719，3.8.32 1、 修复ECO外卖订单配料菜品编码无效导致无法接单的问题。\n"+
			"20230724，3.8.33 1、 新增接口，拉取ECO全部订单，处理收银未收到的部分退订单。 \n"+
			"20230731，3.8.34 1、 ECO外卖对接美团可选套餐。 \n"+
			"20230808，3.8.35 1、 新增李先生成都东站物业接口。 \n"+
			"20230817，3.8.36 1、 新增蒸小皖 新上铁物业接口。 \n"+
			"20231026，3.8.50 1、 乡村基合并eco 的企迈 支付与优惠的拆分合并。 \n"+
			"20231026，3.8.51 1、 鼎友长沙星华润万象城物业接口。 \n"+
			"20231107，3.8.52 1、 蒸小皖新上铁物业，套餐有加价项目组，单价*数量不等于菜目金额问题修复；2、退单优惠计算错误问题修复。 \n"+
			"20231109，3.8.53 1、 乡村基，变更eco打印为老版本打印；2、乡村基，替换菜品名字中的 空格为加号。 \n"+
			"20231124，3.8.55 1、 和合谷 小程序增加套餐非必选组支持。 \n"+
			"20231225，3.8.56 1、 大米先生 成都万象城物业。 \n"+
			"20240219，3.8.57 1、 大米先生 物业优化。 \n"+
			"20240416，3.8.58 1、 鼎友企迈小程序团购券实付和优惠拆分；" +
			"				2、鼎友企迈小程序支持套餐加价菜；" +
			"				3、和合谷POS核销会员付费券及小程序核销付费券落单；" +
			"				4、乡村基北京长楹天街店物业接口对接\n"+
			"20240416，3.8.59 1、ECO外卖订单保存付款方式渠道编码，解决电子发票二维码无法正确打印的问题;\n"+
			"				2、	ECO堂食小程序渠道的订单菜品如果门店不存在，直接返回接单失败不转单以防止转成异常账单。\n" +
			"20240613，3.8.61 1、ECO平台外卖N选N套餐对接;\n"

			;
}
