<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.mobilepos.rest.mapper.MobilePosTqJtztkMapper">
	<select id="findBbrq" resultType="Map">
		SELECT nr as bbrq FROM TS_BMKZK WHERE ZDMC ='BBRQ'
	</select>
	<select id="findState" resultType="com.tzx.mobilepos.rest.model.TqJtztk">
		select * from tq_jtztk where bbrq = #{bbrq} and rybh = #{rybh} and cznr = #{cznr} and jhid = #{jhid} limit 1
	</select>
	<update id="updataCzsj">
		update tq_jtztk set czsj = #{czsj} where id = #{id}
	</update>
<!-- 	<select id="checkOpenState" resultType="com.tzx.pos_service.rest.model.TqJtztk"> -->
<!-- 		select * from tq_jtztk where bbrq = #{bbrq} and cznr = 'JSSY' and jhid = '99' limit 1 -->
<!-- 	</select> -->
	<select id="loginCheck" resultType="com.tzx.mobilepos.rest.vo.LoginCheck">
<!-- 		select rybh,ygdlcs,count(rybh) from tq_jtztk where BBRQ=#{bbrq} and jhid=#{jhid} GROUP BY rybh,ygdlcs HAVING count(rybh)&lt;2 limit 1 -->
		select rybh,ygdlcs,cznr from tq_jtztk where bbrq = #{bbrq} and jhid=#{jhid} and cznr in ('YYDL','YYTC','APPTC') order by cast(ygdlcs as int) desc, czsj desc  limit 1
	</select>
	<select id="getMaxYgdlcs" resultType="java.lang.Integer">
		select COALESCE(max(to_number(ygdlcs,'99G999D9S')),'0') ygdlcs from tq_jtztk where BBRQ=#{bbrq} and cznr='YYDL' and jhid=#{jhid};
	</select>
	<select id="getYgdlcs" resultType="java.lang.Integer">
		select COALESCE(max(to_number(ygdlcs,'99G999D9S')),'0') ygdlcs from tq_jtztk where BBRQ=#{bbrq} and cznr=#{appLogin} and jhid=#{jhid} and rybh =#{rybh};
	</select>
	<delete id="delJtztk">
		delete from tq_jtztk where BBRQ=#{bbrq} and cznr='YYTC' and jhid=#{jhid} and rybh =#{rybh};
	</delete>

	<delete id="deletetcdcxzlsk">
		delete from tq_tcdcxzlsk where kdzdbh = #{bill_num} and mxlxid is not null<!-- and tcid = #{tcid} and tcxh = #{tcxh}  -->
		<if test="item_id != null and item_id != ''">
			and mxxmid = #{item_id}
		</if>
	</delete>
	<select id="checkOpenStart" resultType="java.lang.Integer">
		select count(id) from tq_jtztk where bbrq = #{bbrq} and cznr = 'KSSY' and jhid = '99'
	</select>
	<select id="checkOpenEnd" resultType="java.lang.Integer">
		select count(id) from tq_jtztk where bbrq = #{bbrq} and cznr = 'JSSY' and jhid = '99'
	</select>
	<select id="checkOpen" resultType="java.lang.Integer">
		select count(id) from tq_jtztk where bbrq = #{bbrq} and cznr = #{cznr} and jhid = '99'
	</select>
	
	<select id="getJtzt" resultType="com.tzx.mobilepos.rest.model.TqJtztk">
<!-- 		SELECT * FROM TQ_JTZTK WHERE BBRQ = #{bbrq} AND RYBH = #{czybh} AND CZNR IN ('YYDL', 'YYTC','APPTC') AND JHID &lt;&gt; #{jtbh} ORDER BY CZSJ DESC LIMIT 1 -->
		select * from tq_jtztk where bbrq = #{bbrq} and rybh = #{czybh} and cznr in (#{appLogin}, #{appLogout}) and jhid &lt;&gt; #{jtbh} order by czsj desc limit 1
	</select>
	<select id="getCurrentJtzt" resultType="com.tzx.mobilepos.rest.model.TqJtztk">
		SELECT * FROM TQ_JTZTK WHERE BBRQ = #{bbrq} AND RYBH = #{czybh} AND CZNR IN ('YYDL') AND JHID = #{jtbh} ORDER BY CZSJ DESC LIMIT 1
	</select>

	<update id="updataLoginShiftState">
		update tq_jtztk set yl1 = #{bcid}, yl2 = #{bcmc} where jhid = #{jtbh} and rybh = #{czybh} and ygdlcs = #{dlcs} and cznr = #{cznr} 
	</update>

	<select id="getJtztJh" resultType="com.tzx.mobilepos.rest.model.TqJtztk">
		select * from tq_jtztk where bbrq = #{bbrq} and jhid=#{jtbh} and cznr in ('YYDL','YYTC')
		<if test="dlcs != null and dlcs != ''">
			and ygdlcs = #{dlcs}
		</if>
		order by cast(ygdlcs as int) desc, czsj desc  limit 1
	</select>
	<select id="getJtztNew" resultType="com.tzx.mobilepos.rest.model.TqJtztk">
		select * from tq_jtztk where bbrq = #{bbrq} and jhid=#{jhid} and cznr in (#{appLogin}, #{appLogout}) order by czsj desc limit 1
	</select>
</mapper>
