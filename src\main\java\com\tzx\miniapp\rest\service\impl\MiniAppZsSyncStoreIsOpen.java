package com.tzx.miniapp.rest.service.impl;

import java.util.Map;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tzx.miniapp.rest.mapper.MiniAppShopBaseInfoMapper;
import com.tzx.miniapp.rest.mapper.MiniAppShopStatusMapper;
import com.tzx.miniapp.rest.model.TqJtztk;
import com.tzx.miniapp.rest.service.IData;
import com.tzx.miniapp.rest.vo.Shops;
import com.tzx.miniapp.rest.vo.ZsStoreIsOpenVo;
import com.tzx.publics.util.DateUtil;

/**
 * MiniAppZsSyncDish
 * 
 * <AUTHOR> 2019年06月17日
 */
public class MiniAppZsSyncStoreIsOpen extends IData<ZsStoreIsOpenVo> {

	MiniAppShopBaseInfoMapper shopBaseInfoMapper;
	MiniAppShopStatusMapper shopStatusMapper;
	
	public MiniAppZsSyncStoreIsOpen(MiniAppShopBaseInfoMapper shopBaseInfoMapper, MiniAppShopStatusMapper shopStatusMapper) {
		this.shopBaseInfoMapper = shopBaseInfoMapper;
		this.shopStatusMapper = shopStatusMapper;
		
	}
	
	@Override
	public String getParams() throws JsonProcessingException {
		ZsStoreIsOpenVo io = new ZsStoreIsOpenVo();
		int isOpenShop = 0;
		Shops shops = shopBaseInfoMapper.findShopsData();
		// 报表日期
		Map<String, String> bbrqMap = shopStatusMapper.findBbrq();
		// 生成默认报表日期
		String bbrq = DateUtil.getNowDateYYDDMM();
		if (null != bbrqMap && bbrqMap.size() != 0) {
			bbrq = bbrqMap.get("bbrq");
		}
		TqJtztk jtztk = shopStatusMapper.checkShopOpenStart(DateUtil.parseDate(bbrq));
		if(null == jtztk || "JSSY".equals(jtztk.getCznr())){
			TqJtztk jtztk1 = shopStatusMapper.checkShopClassesStart(DateUtil.parseDate(bbrq));
			if(null == jtztk1 || "YYTC".equals(jtztk1.getCznr())){
				isOpenShop = 0;
			} else {
				isOpenShop = 1;
			}
		} else {
			isOpenShop = 1;
		}
		
		io.setDataVersion(System.currentTimeMillis() + "");
		io.setStoreId(shops.getSid() + "");
		io.setIsOpenShop(isOpenShop);

		return buildReturn(io, 2, shopBaseInfoMapper);

	}



	public String getUrl() {
		return "store/syncStoreIsOpen";
	}

}
