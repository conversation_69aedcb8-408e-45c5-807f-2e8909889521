package com.tzx.mobilepos.rest.mapper;

import com.tzx.publics.base.MyMapper;
import com.tzx.mobilepos.rest.model.TsFkfssdk;
import com.tzx.mobilepos.rest.vo.PaymentWay;

import java.util.List;

import org.apache.ibatis.annotations.Param;

/**
*
* <AUTHOR>
* @since 2018-05-21
*/
public interface MobilePosTsFkfssdkMapper extends MyMapper<TsFkfssdk> {
	public List<PaymentWay> findTsFkfssdkBasicData(@Param("posMemberType") String posMemberType);
	
	public PaymentWay getFkfssdkByType(@Param("fklxsx") String fklxsx, @Param("yl3") String yl3);
}
