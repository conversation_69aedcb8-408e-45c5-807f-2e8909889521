package com.tzx.miniapp.rest.service;

import com.tzx.publics.common.BillNoData;
import com.tzx.miniapp.common.Data;

import net.sf.json.JSONObject;

/**
 * 账单服务 
 * 此入口包含所有对账单的处理业务
 * 
 * <AUTHOR>
 */
public interface IMiniAppZsOrderPrecheck {
	/**
	 * 落单，清台
	 */
	public Data orderPrecheck(JSONObject json, BillNoData billNoData);
	
	/**
	 * 账单查询
	 */
	public Data orderStatus(JSONObject json);
	
	/**
	 * 退单
	 */
	public Data posCancelBill(JSONObject json);
	
	/**
	 * 获取账单号
	 */
//	public BillNoData getBillNoData();
	
	/**
	 * 验证菜品是否存在与餐谱
	 */
	public Data checkClmx(JSONObject json);

}
