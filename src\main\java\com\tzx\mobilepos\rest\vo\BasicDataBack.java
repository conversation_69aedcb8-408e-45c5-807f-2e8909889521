package com.tzx.mobilepos.rest.vo;

import com.tzx.mobilepos.rest.model.TsGgcsk;
import com.tzx.mobilepos.rest.model.TsJthyzlk;

import java.util.List;

public class BasicDataBack {

	private List<ItemClass> ITEM_CLASS;

	private List<Dish> DISH;

	private List<ComboDetails> COMBO_DETAILS;

	private List<ComboGroup> COMBO_GROUP;

	private List<PaymentWay> PAYMENT_WAY;

	private List<Dish> DISCOUNT;

	private List<Taste> TASTE;
	
	private List<ClearDish> CLEAR_DISH;
	
	private List<TsGgcsk> COMMON_PARAM;
	
	private List<TsJthyzlk> PARTY_MEMBER;
	
	private List<Dish> SPEC_DISH;

	public List<ItemClass> getITEM_CLASS() {
		return ITEM_CLASS;
	}

	public void setITEM_CLASS(List<ItemClass> iTEM_CLASS) {
		ITEM_CLASS = iTEM_CLASS;
	}

	public List<Dish> getDISH() {
		return DISH;
	}

	public void setDISH(List<Dish> dISH) {
		DISH = dISH;
	}

	public List<ComboDetails> getCOMBO_DETAILS() {
		return COMBO_DETAILS;
	}

	public void setCOMBO_DETAILS(List<ComboDetails> cOMBO_DETAILS) {
		COMBO_DETAILS = cOMBO_DETAILS;
	}

	public List<ComboGroup> getCOMBO_GROUP() {
		return COMBO_GROUP;
	}

	public void setCOMBO_GROUP(List<ComboGroup> cOMBO_GROUP) {
		COMBO_GROUP = cOMBO_GROUP;
	}

	public List<PaymentWay> getPAYMENT_WAY() {
		return PAYMENT_WAY;
	}

	public void setPAYMENT_WAY(List<PaymentWay> pAYMENT_WAY) {
		PAYMENT_WAY = pAYMENT_WAY;
	}

	public List<Dish> getDISCOUNT() {
		return DISCOUNT;
	}

	public void setDISCOUNT(List<Dish> dISCOUNT) {
		DISCOUNT = dISCOUNT;
	}

	public List<Taste> getTASTE() {
		return TASTE;
	}

	public void setTASTE(List<Taste> tASTE) {
		TASTE = tASTE;
	}

	public List<ClearDish> getCLEAR_DISH() {
		return CLEAR_DISH;
	}

	public void setCLEAR_DISH(List<ClearDish> cLEAR_DISH) {
		CLEAR_DISH = cLEAR_DISH;
	}

	public List<TsGgcsk> getCOMMON_PARAM() {
		return COMMON_PARAM;
	}

	public void setCOMMON_PARAM(List<TsGgcsk> cOMMON_PARAM) {
		COMMON_PARAM = cOMMON_PARAM;
	}

	public List<TsJthyzlk> getPARTY_MEMBER() {
		return PARTY_MEMBER;
	}

	public void setPARTY_MEMBER(List<TsJthyzlk> pARTY_MEMBER) {
		PARTY_MEMBER = pARTY_MEMBER;
	}

	public List<Dish> getSPEC_DISH() {
		return SPEC_DISH;
	}

	public void setSPEC_DISH(List<Dish> sPEC_DISH) {
		SPEC_DISH = sPEC_DISH;
	}

}
