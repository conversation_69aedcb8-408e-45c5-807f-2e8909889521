package com.tzx.receiver.entity.msg;

import java.io.Serializable;

public class Yhfstjsz
  implements Serializable
{
  private Integer id;
  private Integer yhfsid;
  private String yhfsbh;
  private String xzfs;
  private String pdzt;
  private Integer lbid;
  private Integer xmid;
  private String memo;
  private String yl1;
  private String yl2;
  private String yl3;
  private String pdlx;
  private Double zdxe;
  private Double zgxe;
  private Integer zdxl;
  private Integer zgxl;

  public Integer getId()
  {
    return this.id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public Integer getYhfsid() {
    return this.yhfsid;
  }

  public void setYhfsid(Integer yhfsid) {
    this.yhfsid = yhfsid;
  }

  public String getYhfsbh() {
    return this.yhfsbh;
  }

  public void setYhfsbh(String yhfsbh) {
    this.yhfsbh = yhfsbh;
  }

  public String getPdzt() {
    return this.pdzt;
  }

  public void setPdzt(String pdzt) {
    this.pdzt = pdzt;
  }

  public Integer getLbid() {
    return this.lbid;
  }

  public void setLbid(Integer lbid) {
    this.lbid = lbid;
  }

  public Integer getXmid() {
    return this.xmid;
  }

  public void setXmid(Integer xmid) {
    this.xmid = xmid;
  }

  public String getMemo() {
    return this.memo;
  }

  public void setMemo(String memo) {
    this.memo = memo;
  }

  public String getYl1() {
    return this.yl1;
  }

  public void setYl1(String yl1) {
    this.yl1 = yl1;
  }

  public String getYl2() {
    return this.yl2;
  }

  public void setYl2(String yl2) {
    this.yl2 = yl2;
  }

  public String getYl3() {
    return this.yl3;
  }

  public void setYl3(String yl3) {
    this.yl3 = yl3;
  }

  public String getPdlx() {
    return this.pdlx;
  }

  public void setPdlx(String pdlx) {
    this.pdlx = pdlx;
  }

  public Double getZdxe() {
    return this.zdxe;
  }

  public void setZdxe(Double zdxe) {
    this.zdxe = zdxe;
  }

  public Double getZgxe() {
    return this.zgxe;
  }

  public void setZgxe(Double zgxe) {
    this.zgxe = zgxe;
  }

  public Integer getZdxl() {
    return this.zdxl;
  }

  public void setZdxl(Integer zdxl) {
    this.zdxl = zdxl;
  }

  public Integer getZgxl() {
    return this.zgxl;
  }

  public void setZgxl(Integer zgxl) {
    this.zgxl = zgxl;
  }

  public String getXzfs() {
    return this.xzfs;
  }

  public void setXzfs(String xzfs) {
    this.xzfs = xzfs;
  }
}