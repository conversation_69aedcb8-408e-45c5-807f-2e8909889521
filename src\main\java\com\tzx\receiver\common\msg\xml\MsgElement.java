package com.tzx.receiver.common.msg.xml;

import org.dom4j.Element;


/**
 * 
 * 数据包元素的消息体元素类。
 * 
 * <AUTHOR>
 * 
 */
public class MsgElement extends BaseDPElement {

	protected MsgDatasElement datas;

	/**
	 * 构造一个 MsgElement 对象。
	 * 
	 */
	public MsgElement(DataPacketBuilder builder, Element currentNode) {
		super(builder, currentNode);
	}

	/**
	 * 返回此元素对象所在的文档路径。这里返回的是<code>MSG</code>
	 * 
	 */
	public static String getCurrentPath() {
		return DPC.SMsgNodeStr;
	}

	/**
	 * 返回此元素对象所在的文档路径。这里返回的是<code>MSG</code>
	 * 
	 */
	public static String getHeadPath() {
		return DPC.SHeadNodeStr;
	}	
	/**
	 * 
	 * 获取指向DATAS元素的<code>MsgDatasElement</code>对象。如果XML文档中不存在DATAS元素，则自动创建此元素。
	 * 
	 * @return 指向DATAS元素的{@link MsgDatasElement}对象。
	 */

	public MsgDatasElement getDatas() {
		Element childNode;
		if (datas == null) {
			childNode = builder.getNode(currentNode, DPC.SMsgDatasNodeStr);
			datas = new MsgDatasElement(builder, childNode);
		}
		return datas;
	}

	public String getPackageName() {
		Element childNode = builder.findNode(currentNode, DPC.SMsgSubSystemNodeStr);
		if (childNode != null) {
			return builder.readString(childNode, "");
		} else {
			return "";
		}
	}

	public String getActionName() {
		Element childNode = builder.findNode(currentNode, DPC.SMsgActionNodeStr);
		if (childNode != null) {
			return builder.readString(childNode, "");
		} else {
			return "";
		}
	}
	
	public String getPersonnelBh() {
		Element childNode = builder.findNode(currentNode, DPC.SMsgPersonnelNOdeStr);
		if (childNode != null) {
			return builder.readString(childNode, "");
		} else {
			return "";
		}
	}	
	public String getVer() {
		Element childNode = builder.findNode(currentNode, DPC.SMsgVerNOdeStr);
		if (childNode != null) {
			return builder.readString(childNode, "");
		} else {
			return "";
		}
	}	
	public String getSrc() {
		Element childNode = builder.findNode(currentNode, DPC.SMsgSrcNOdeStr);
		if (childNode != null) {
			return builder.readString(childNode, "");
		} else {
			return "";
		}
	}	
	public String getDes() {
		Element childNode = builder.findNode(currentNode, DPC.SMsgDesNOdeStr);
		if (childNode != null) {
			return builder.readString(childNode, "");
		} else {
			return "";
		}
	}	
	public String getDevid() {
		Element childNode = builder.findNode(currentNode, DPC.SMsgDevidNOdeStr);
		if (childNode != null) {
			return builder.readString(childNode, "");
		} else {
			return "";
		}
	}
	public String getApp() {
		Element childNode = builder.findNode(currentNode, DPC.SMsgAppNOdeStr);
		if (childNode != null) {
			return builder.readString(childNode, "");
		} else {
			return "";
		}
	}
	public String getTid() {
		Element childNode = builder.findNode(currentNode, DPC.SMsgTidNOdeStr);
		if (childNode != null) {
			return builder.readString(childNode, "");
		} else {
			return "";
		}
	}
	public String getMsgid() {
		Element childNode = builder.findNode(currentNode, DPC.SMsgMsgidNOdeStr);
		if (childNode != null) {
			return builder.readString(childNode, "");
		} else {
			return "";
		}
	}
	public String getCorid() {
		Element childNode = builder.findNode(currentNode, DPC.SMsgCoridNOdeStr);
		if (childNode != null) {
			return builder.readString(childNode, "");
		} else {
			return "";
		}
	}
	public String getWorkdate() {
		Element childNode = builder.findNode(currentNode, DPC.SMsgWorkdateNOdeStr);
		if (childNode != null) {
			return builder.readString(childNode, "");
		} else {
			return "";
		}
	}
	public String getReserve() {
		Element childNode = builder.findNode(currentNode, DPC.SMsgReserveNOdeStr);
		if (childNode != null) {
			return builder.readString(childNode, "");
		} else {
			return "";
		}
	}
}
