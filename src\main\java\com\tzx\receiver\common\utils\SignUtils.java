package com.tzx.receiver.common.utils;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Map;

import org.apache.commons.codec.digest.DigestUtils;

public class SignUtils {
	public static String KEY = "TZXBOH2020-06-02";
	public static String getSign(Map<String, Object> map, String frKey)
	{
		ArrayList<String> list = new ArrayList<String>();
		for (Map.Entry<String, Object> entry : map.entrySet())
		{
			if (entry.getValue() != null && !"".equals(entry.getValue()))
			{
				list.add(entry.getKey() + "=" + entry.getValue() + "&");
			}
		}
		int size = list.size();
		String[] arrayToSort = list.toArray(new String[size]);
		Arrays.sort(arrayToSort, String.CASE_INSENSITIVE_ORDER);
		StringBuilder sb = new StringBuilder();
		for (int i = 0; i < size; i++)
		{
			sb.append(arrayToSort[i]);
		}
		String result = sb.toString();
		result += "key=" + frKey;
		System.out.println("Sign Before MD5:" + result);
		result = DigestUtils.md5Hex(getContentBytes(result, "UTF-8")).toUpperCase();
		System.out.println("Sign After MD5:" + result);
		return result;
	}
	public static byte[] getContentBytes(String content, String charset)
	{
		if ((charset == null) || ("".equals(charset)))
		{
			return content.getBytes();
		}
		try
		{
			return content.getBytes(charset);
		}
		catch (UnsupportedEncodingException e)
		{
			throw new RuntimeException("MD5签名过程中出现错误,指定的编码集不对,您目前指定的编码集是:" + charset);
		}
	}

}
