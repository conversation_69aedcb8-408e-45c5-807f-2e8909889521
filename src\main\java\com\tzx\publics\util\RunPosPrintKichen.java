package com.tzx.publics.util;

import com.sun.jna.Native;
import com.tzx.miniapp.rest.mapper.MiniAppFirstPayMapper;
import com.tzx.publics.common.ApplicationContextUtils;
import com.tzx.publics.listener.InitDataListener;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.*;

public class RunPosPrintKichen implements Runnable {

	private final static Logger LOGGER = LoggerFactory.getLogger(RunPosPrintKichen.class);

	private static TzxReportLibIntKichen libKichen = null;
	private static String dllPath = "";
	private String jtbh;
	private String printStr;

	public RunPosPrintKichen(String jtbh, String printStr) {
		this.jtbh = jtbh;
		this.printStr = printStr;
	}

	public static void initDll() {
		if (libKichen == null) {
			synchronized (RunPosPrintKichen.class) {
//				// 系统 Windows 或者 Linux
//				String osName = System.getProperties().getProperty("os.name").toLowerCase();
//				// //架构 x86 或者 amd64
//				String osArch = System.getProperties().getProperty("os.arch").toLowerCase();
//				String path = System.getProperty("user.dir");
//				path = path + File.separator + "bin" + File.separator;
//				LOGGER.info("This OS is: " + osName + ";" + osArch + ";path:" + path);
//				String fileName = null;
//				if (osArch.indexOf("64") != -1) {// 64位
//					dllPath = path + "PrintBin" + File.separator;
//					fileName = dllPath + "tzxReportLibkichen64.dll";
//				} else if (osArch.indexOf("86") != -1) {// 32位
//					dllPath = path + "PrintBin" + File.separator;
//					fileName = dllPath + "tzxReportLibkichen.dll";
//				} else {// 不支持的
//					LOGGER.info("This OS is not support！");
//				}
				
				String path = System.getProperty("user.dir");
				File file = new File(path);
				
				LOGGER.info("path:" + path);
				String fileName = null;
				dllPath = file.getParent() + File.separator;
				fileName = dllPath + "tzxReportLibkichen.dll";
				
				LOGGER.info("PrintKichenDllFileName: " + fileName);
				libKichen = (TzxReportLibIntKichen) Native.loadLibrary(fileName, TzxReportLibIntKichen.class);
			}
		}
	}
	
	@Override
	public void run() {
		try {
			String useNewPrint = InitDataListener.ggcsMap.get("USENEWPRINT");
			if ((null != useNewPrint) && useNewPrint.equals("Y")) {
				// 新版打印
				MiniAppFirstPayMapper firstPayMapper =  ApplicationContextUtils.applicationContext.getBean(MiniAppFirstPayMapper.class);
				firstPayMapper.insertDyrwk("99", 0, 0, printStr, 0);
			} else {
				// 原来打印方式
				this.initDll();
				jtbh = "1" + jtbh;
				File file = new File(dllPath + "ReportParamCD");
				if (!file.exists()) {
					file.mkdir();
				}
				savePrintParamFile(dllPath + "ReportParamCD" + File.separator + jtbh + "_1" + ".txt", printStr);
				this.libKichen.PrintReportCDByPosNo(1, Integer.parseInt(jtbh));
				LOGGER.info("完成打印...");
			};
		} catch (Exception e) {
			e.printStackTrace();
		}

	}

	public void savePrintParamFile(String fileName, String printStr) {
		File file = new File(fileName);
		Writer out = null;
		try {
			out = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(file), "GBK"));
			out.write(printStr);
			out.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

}
