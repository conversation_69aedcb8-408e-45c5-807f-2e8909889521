package com.tzx.receiver.common.upload;

import java.io.IOException;
import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetAddress;
import java.net.SocketException;
import java.text.ParseException;

public class UDPClient {
	
	 public static void udpVoid() {   
	        try {   
	            DatagramSocket socket = new DatagramSocket();   
	            String s = "C:/Users/<USER>/Desktop/boh-sql/text.txt";   
	            byte[] buffer = s.getBytes();   
	            /*ResourceBundle rb = ResourceBundle.getBundle("UDPClient");	
				String UDPServer = rb.getString("UDPServer");
				String UDPport = rb.getString("UDPport");*/
	            String UDPServer = "127.0.0.1";
				String UDPport = "3005";
	            DatagramPacket packet = new DatagramPacket(buffer, buffer.length,InetAddress.getByName(UDPServer),Integer.parseInt(UDPport));   
	            socket.send(packet);   
	            socket.close();   
	        } catch (SocketException e) {   
	            e.printStackTrace();   
	        } catch (IOException e) {   
	            e.printStackTrace();   
	        }   
	           
	    }   

	 public static void main(String[] args) throws ParseException {
		 udpVoid();
	 }
}
