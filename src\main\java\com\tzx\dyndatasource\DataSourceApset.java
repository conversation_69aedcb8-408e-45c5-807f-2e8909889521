package com.tzx.dyndatasource;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Method;


/**
 * <AUTHOR>
 * @Date 2020-03-16
 * @Descption
 **/
@Component
@Aspect
@Order(1) //优先级别设置为1
public class DataSourceApset {
    private Logger logger = LoggerFactory.getLogger(DataSourceApset.class);
    //只切下发的，下发的强制切换为正式库。其他数据通过更改默认数据源实现。
    //TODO 此切面可能会引发数据性能问题，后续需要优化
    private final  String POINT_CUT_RECEIVER = "execution(* com.tzx.receiver..*.*(..))";
    @Pointcut(POINT_CUT_RECEIVER)
    public void pointCutReceier(){}
    @Around("pointCutReceier()")
    public Object busiAround(ProceedingJoinPoint point) throws  Throwable {
        DynamicDataSource.setDataSource(DSType.PROD.getValue());
        try {
            return point.proceed();
        } finally {
            DynamicDataSource.clearDataSource();
        }
    }
}
