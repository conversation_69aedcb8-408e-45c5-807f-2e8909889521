package com.tzx.receiver.entity.msg;

import java.io.Serializable;
import java.util.Date;

public class Advertisement implements Serializable {

    private Long id;
    /**
     * 名称
     */
    private String styleName;
    /**
     * LOGO
     */
    private String styleLogo;
    /**
     * 广告语1
     */
    private String advertisement1;
    /**
     * 广告语2
     */
    private String advertisement2;
    /**
     * 广告语3
     */
    private String advertisement3;
    /**
     * 广告语4
     */
    private String advertisement4;
    /**
     * 广告语5
     */
    private String advertisement5;
    /**
     * 二维码
     */
    private String qrCode;
    /**
     * 是否启用
     */
    private String states;
    /**
     * 创建人
     */
    private String operatePerson;
    /**
     * 创建时间
     */
    private Date operateTime;
    /**
     * 上次修改人
     */
    private String lastModifyPerson;
    /**
     * 上次修改时间
     */
    private Date lastModifyTime;

    /**
     * 机构序号
     */
    private Integer jgxh;
    /**
     * 广告对应渠道
     */
    private String channel;

    public String getChannel() { return channel;
    }

    public void setChannel(String channel) {    this.channel = channel;    }

    public Integer getJgxh() {
        return jgxh;
    }

    public void setJgxh(Integer jgxh) {
        this.jgxh = jgxh;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getStyleName() {
        return styleName;
    }

    public void setStyleName(String styleName) {
        this.styleName = styleName;
    }

    public String getStyleLogo() {
        return styleLogo;
    }

    public void setStyleLogo(String styleLogo) {
        this.styleLogo = styleLogo;
    }

    public String getAdvertisement1() {
        return advertisement1;
    }

    public void setAdvertisement1(String advertisement1) {
        this.advertisement1 = advertisement1;
    }

    public String getAdvertisement2() {
        return advertisement2;
    }

    public void setAdvertisement2(String advertisement2) {
        this.advertisement2 = advertisement2;
    }

    public String getAdvertisement3() {
        return advertisement3;
    }

    public void setAdvertisement3(String advertisement3) {
        this.advertisement3 = advertisement3;
    }

    public String getAdvertisement4() {
        return advertisement4;
    }

    public void setAdvertisement4(String advertisement4) {
        this.advertisement4 = advertisement4;
    }

    public String getAdvertisement5() {
        return advertisement5;
    }

    public void setAdvertisement5(String advertisement5) {
        this.advertisement5 = advertisement5;
    }

    public String getQrCode() {
        return qrCode;
    }

    public void setQrCode(String qrCode) {
        this.qrCode = qrCode;
    }

    public String getStates() {
        return states;
    }

    public void setStates(String states) {
        this.states = states;
    }

    public String getOperatePerson() {
        return operatePerson;
    }

    public void setOperatePerson(String operatePerson) {
        this.operatePerson = operatePerson;
    }

    public Date getOperateTime() {
        return operateTime;
    }

    public void setOperateTime(Date operateTime) {
        this.operateTime = operateTime;
    }

    public String getLastModifyPerson() {
        return lastModifyPerson;
    }

    public void setLastModifyPerson(String lastModifyPerson) {
        this.lastModifyPerson = lastModifyPerson;
    }

    public Date getLastModifyTime() {
        return lastModifyTime;
    }

    public void setLastModifyTime(Date lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }

}
