package com.tzx.commapi.rest.vo;

import java.util.Date;
import java.util.UUID;

/**
 * Created by <PERSON><PERSON><PERSON> on 2020-09-08.
 */
public class TqTask {
    private String tasktype;
    private String secondtype;
    private String objid;
    private String objparams;
    private Date createtime;
    private String cwlxbh;
    private String zdzt;
    private Date jzbbrq;
    private String source;
    private Integer wmtype;
    private String uuid;
    private Date jzsj;


    public  TqTask(String tasktype, String secondtype, String objid, String objparams, Date createtime
    ,String cwlxbh, String zdzt, Date jzbbrq, String source,Integer wmtype, Date jzsj){
        this.tasktype = tasktype;
        this.secondtype = secondtype;
        this.objid = objid;
        this.objparams = objparams;
        this.createtime = createtime;
        this.cwlxbh =cwlxbh;
        this.zdzt = zdzt;
        this.jzbbrq = jzbbrq;
        this.source = source;
        this.wmtype = wmtype;
        this.uuid = UUID.randomUUID().toString();
        this.jzsj = jzsj;
    }

    public String getTasktype() {
        return tasktype;
    }

    public void setTasktype(String tasktype) {
        this.tasktype = tasktype;
    }

    public String getSecondtype() {
        return secondtype;
    }

    public void setSecondtype(String secondtype) {
        this.secondtype = secondtype;
    }

    public String getObjid() {
        return objid;
    }

    public void setObjid(String objid) {
        this.objid = objid;
    }

    public String getObjparams() {
        return objparams;
    }

    public void setObjparams(String objparams) {
        this.objparams = objparams;
    }

    public Date getCreatetime() {
        return createtime;
    }

    public void setCreatetime(Date createtime) {
        this.createtime = createtime;
    }

    public String getCwlxbh() {
        return cwlxbh;
    }

    public void setCwlxbh(String cwlxbh) {
        this.cwlxbh = cwlxbh;
    }

    public String getZdzt() {
        return zdzt;
    }

    public void setZdzt(String zdzt) {
        this.zdzt = zdzt;
    }

    public Date getJzbbrq() {
        return jzbbrq;
    }

    public void setJzbbrq(Date jzbbrq) {
        this.jzbbrq = jzbbrq;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Integer getWmtype() {
        return wmtype;
    }

    public void setWmtype(Integer wmtype) {
        this.wmtype = wmtype;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public Date getJzsj() {
        return jzsj;
    }

    public void setJzsj(Date jzsj) {
        this.jzsj = jzsj;
    }

    @Override
    public String toString() {
        return "TqTask{" +
                "tasktype='" + tasktype + '\'' +
                ", secondtype='" + secondtype + '\'' +
                ", objid='" + objid + '\'' +
                ", objparams='" + objparams + '\'' +
                ", createtime=" + createtime +
                ", cwlxbh='" + cwlxbh + '\'' +
                ", zdzt='" + zdzt + '\'' +
                ", jzbbrq=" + jzbbrq +
                ", source='" + source + '\'' +
                ", wmtype=" + wmtype +
                ", uuid='" + uuid + '\'' +
                '}';
    }
}
