package com.tzx.receiver.common.msg.datatype;


/**
 * 数据类型的公共常量、过程、函数、枚举类型的定义
 * 
 * <AUTHOR>
 * 
 */
public class DataTypeUnit {

	public static final int DefaultPrecision = 15;

	public static final int DefaultScale = 0;

	public static final int DefaultSize = 32;

	public static Class DataTypeAttributeClasses[] = { DataTypeAttribute.class,
			DataTypeAttribute.class, NumberAttribute.class, StringAttribute.class,
			DateTimeAttribute.class, BlobAttribute.class, DataTypeAttribute.class,
			DataTypeAttribute.class };

	/**
	 * 数据类型
	 * 
	 * <AUTHOR>
	 * 
	 */
	public enum DataType {
		dtInteger, dtFloat, dtNumber, dtString, dtDateTime, dtBlob, dtText, dtUnknown
	}

	/**
	 * 日期时间数据类型的子类型
	 * 
	 * <AUTHOR>
	 * 
	 */

	public enum DateTimeSubType {
		stDateTime, stDate, stTime
	}
}
