package com.tzx.receiver.entity;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2019-05-10
 * @Descption
 **/
public class UploadLog {
    private int id;
    private String guid;
    private String sourcemsg;
    private String command;
    private String uploadfilename;
    private String upprocess;
    private String upstate;
    private String starttime;
    private String endtime;
    private String exceptionmsg;
    private String runresult;
    private String bbrq;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getSourcemsg() {
        return sourcemsg;
    }

    public void setSourcemsg(String sourcemsg) {
        this.sourcemsg = sourcemsg;
    }

    public String getCommand() {
        return command;
    }

    public void setCommand(String command) {
        this.command = command;
    }

    public String getUploadfilename() {
        return uploadfilename;
    }

    public void setUploadfilename(String uploadfilename) {
        this.uploadfilename = uploadfilename;
    }

    public String getUpprocess() {
        return upprocess;
    }

    public void setUpprocess(String upprocess) {
        this.upprocess = upprocess;
    }

    public String getUpstate() {
        return upstate;
    }

    public void setUpstate(String upstate) {
        this.upstate = upstate;
    }

    public String getStarttime() {
        return starttime;
    }

    public void setStarttime(String starttime) {
        this.starttime = starttime;
    }

    public String getEndtime() {
        return endtime;
    }

    public void setEndtime(String endtime) {
        this.endtime = endtime;
    }

    public String getExceptionmsg() {
        return exceptionmsg;
    }

    public void setExceptionmsg(String exceptionmsg) {
        this.exceptionmsg = exceptionmsg;
    }

    public String getRunresult() {
        return runresult;
    }

    public void setRunresult(String runresult) {
        this.runresult = runresult;
    }

    public String getBbrq() {
        //从命理中解析出爆吧日期
        String[] array = sourcemsg.split("\\|");
        String key,value;
        //数组转换到map
        for (String str : array) {
            String[] kv = str.split("=");
            if (kv.length==2){
                key = kv[0];
                value = kv[1];
                if (key.equalsIgnoreCase("BBRQ")){
                    return value;

                }
            }
        }
        return "";
    }

    public void setBbrq(String bbrq) {
        this.bbrq = bbrq;
    }
}
