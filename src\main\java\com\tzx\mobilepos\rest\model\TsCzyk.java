package com.tzx.mobilepos.rest.model;

import com.tzx.publics.base.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2018-05-15
 */
@Table(name = "Ts_CZYK")
public class TsCzyk extends BaseEntity implements Serializable {

	private static final long serialVersionUID = 1L;
	@Id
	@Column(name = "ID")
	private int id;
	@Column(name = "CZYZBID")
	private int czyzbid;
	@Column(name = "CZYBH")
	private String czybh;
	@Column(name = "CZYMC1")
	private String czymc1;
	@Column(name = "CZYMC2")
	private String czymc2;
	@Column(name = "CZYXB")
	private String czyxb;
	@Column(name = "CZYMM")
	private String czymm;
	@Column(name = "QXKBH")
	private String qxkbh;
	@Column(name = "CZYZT")
	private String czyzt;
	@Column(name = "YXRQ")
	private Date yxrq;
	@Column(name = "XGMMSJ")
	private Date xgmmsj;
	@Column(name = "CZYKH")
	private String czykh;
	@Column(name = "YL1")
	private String yl1;
	@Column(name = "YL2")
	private String yl2;
	@Column(name = "YL3")
	private String yl3;
	
	private int group_id;
	private int czy_id;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public int getCzyzbid() {
		return czyzbid;
	}

	public void setCzyzbid(int czyzbid) {
		this.czyzbid = czyzbid;
	}

	public String getCzybh() {
		return czybh;
	}

	public void setCzybh(String czybh) {
		this.czybh = czybh;
	}

	public String getCzymc1() {
		return czymc1;
	}

	public void setCzymc1(String czymc1) {
		this.czymc1 = czymc1;
	}

	public String getCzymc2() {
		return czymc2;
	}

	public void setCzymc2(String czymc2) {
		this.czymc2 = czymc2;
	}

	public String getCzyxb() {
		return czyxb;
	}

	public void setCzyxb(String czyxb) {
		this.czyxb = czyxb;
	}

	public String getCzymm() {
		return czymm;
	}

	public void setCzymm(String czymm) {
		this.czymm = czymm;
	}

	public String getQxkbh() {
		return qxkbh;
	}

	public void setQxkbh(String qxkbh) {
		this.qxkbh = qxkbh;
	}

	public String getCzyzt() {
		return czyzt;
	}

	public void setCzyzt(String czyzt) {
		this.czyzt = czyzt;
	}

	public Date getYxrq() {
		return yxrq;
	}

	public void setYxrq(Date yxrq) {
		this.yxrq = yxrq;
	}

	public Date getXgmmsj() {
		return xgmmsj;
	}

	public void setXgmmsj(Date xgmmsj) {
		this.xgmmsj = xgmmsj;
	}

	public String getCzykh() {
		return czykh;
	}

	public void setCzykh(String czykh) {
		this.czykh = czykh;
	}

	public String getYl1() {
		return yl1;
	}

	public void setYl1(String yl1) {
		this.yl1 = yl1;
	}

	public String getYl2() {
		return yl2;
	}

	public void setYl2(String yl2) {
		this.yl2 = yl2;
	}

	public String getYl3() {
		return yl3;
	}

	public void setYl3(String yl3) {
		this.yl3 = yl3;
	}

	public int getGroup_id() {
		return group_id;
	}

	public void setGroup_id(int group_id) {
		this.group_id = group_id;
	}

	public int getCzy_id() {
		return czy_id;
	}

	public void setCzy_id(int czy_id) {
		this.czy_id = czy_id;
	}

}
