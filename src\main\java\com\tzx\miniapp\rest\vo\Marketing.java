package com.tzx.miniapp.rest.vo;

import java.io.Serializable;
import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 *         <p>
 *         营销活动
 *         </p>
 *         <p>
 *         <li>dsid 营销活动id 46
 *         <li>programtype 1
 *         <li>money 满额度 100
 *         <li>submoney 优惠额度 50
 *         <li>type 类型（1-满减，2-每减，3买赠，4-特价） 1
 *         <li>max 最大优惠额度 50
 *         <li>timeflag 3
 *         <li>name 活动名称 满100-50
 *         <li>validtime “2017-08-03—2017-09-30”
 *         <li>applytimes “00:00—23:59”
 *         <li>applytime null
 *         <li>not_include_dish_kinds 不参与满减或每满末端菜类id [‘123’,’321’]
 *         <li>cpqflag 满减与菜品券共用 true
 *         <li>djqflag 满减与代金券共用 true
 *         <li>buy 买赠原菜品
 *         <li>gift 赠送菜品
 *         </p>
 */
public class Marketing implements Serializable {
	private int dsid;
	private int programtype;
	private int money;
	private int submoney;
	private int type;
	private int max;
	private int timeflag;
	private String name;
	private String validtime;
	private String applytimes;
	private String applytime;
	private String not_include_dish_kinds;
	private boolean cpqflag;
	private boolean djqflag;
	private List<DishLite> buy;
	private List<DishLite> gift;
	public int getDsid() {
		return dsid;
	}
	public void setDsid(int dsid) {
		this.dsid = dsid;
	}
	public int getProgramtype() {
		return programtype;
	}
	public void setProgramtype(int programtype) {
		this.programtype = programtype;
	}
	public int getMoney() {
		return money;
	}
	public void setMoney(int money) {
		this.money = money;
	}
	public int getSubmoney() {
		return submoney;
	}
	public void setSubmoney(int submoney) {
		this.submoney = submoney;
	}
	public int getType() {
		return type;
	}
	public void setType(int type) {
		this.type = type;
	}
	public int getMax() {
		return max;
	}
	public void setMax(int max) {
		this.max = max;
	}
	public int getTimeflag() {
		return timeflag;
	}
	public void setTimeflag(int timeflag) {
		this.timeflag = timeflag;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getValidtime() {
		return validtime;
	}
	public void setValidtime(String validtime) {
		this.validtime = validtime;
	}
	public String getApplytimes() {
		return applytimes;
	}
	public void setApplytimes(String applytimes) {
		this.applytimes = applytimes;
	}
	public String getApplytime() {
		return applytime;
	}
	public void setApplytime(String applytime) {
		this.applytime = applytime;
	}
	public String getNot_include_dish_kinds() {
		return not_include_dish_kinds;
	}
	public void setNot_include_dish_kinds(String not_include_dish_kinds) {
		this.not_include_dish_kinds = not_include_dish_kinds;
	}
	public boolean isCpqflag() {
		return cpqflag;
	}
	public void setCpqflag(boolean cpqflag) {
		this.cpqflag = cpqflag;
	}
	public boolean isDjqflag() {
		return djqflag;
	}
	public void setDjqflag(boolean djqflag) {
		this.djqflag = djqflag;
	}
	public List<DishLite> getBuy() {
		return buy;
	}
	public void setBuy(List<DishLite> buy) {
		this.buy = buy;
	}
	public List<DishLite> getGift() {
		return gift;
	}
	public void setGift(List<DishLite> gift) {
		this.gift = gift;
	}
	
}
