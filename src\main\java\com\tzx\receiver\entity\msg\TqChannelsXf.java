package com.tzx.receiver.entity.msg;

/**
 * Created by Wangwq on 2021-12-20.
 */
public class TqChannelsXf {
    private Integer id;
    private String yl3;
    private String cpysmc1;
    private String sfxs;
    private Integer xssx;
    private String fkfsbm;
    private String yhbm;
    private String qdbm;
    private String ggcsbm;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getYl3() {
        return yl3;
    }

    public void setYl3(String yl3) {
        this.yl3 = yl3;
    }

    public String getCpysmc1() {
        return cpysmc1;
    }

    public void setCpysmc1(String cpysmc1) {
        this.cpysmc1 = cpysmc1;
    }

    public String getSfxs() {
        return sfxs;
    }

    public void setSfxs(String sfxs) {
        this.sfxs = sfxs;
    }

    public Integer getXssx() {
        return xssx;
    }

    public void setXssx(Integer xssx) {
        this.xssx = xssx;
    }

    public String getFkfsbm() {
        return fkfsbm;
    }

    public void setFkfsbm(String fkfsbm) {
        this.fkfsbm = fkfsbm;
    }

    public String getYhbm() {
        return yhbm;
    }

    public void setYhbm(String yhbm) {
        this.yhbm = yhbm;
    }

    public String getQdbm() {
        return qdbm;
    }

    public void setQdbm(String qdbm) {
        this.qdbm = qdbm;
    }

    public String getGgcsbm() {
        return ggcsbm;
    }

    public void setGgcsbm(String ggcsbm) {
        this.ggcsbm = ggcsbm;
    }
}
