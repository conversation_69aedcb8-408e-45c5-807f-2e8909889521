package com.tzx.receiver.entity.msg;

import java.io.Serializable;
import java.util.Date;

public class Paymentbuttons
  implements Serializable
{
  private Integer id;
  private Integer cpysid;
  private String funbh;
  private String funname1;
  private String funname2;
  private Integer fkfsid;
  private Integer fkfzid;
  private String butcolor;
  private Integer funcid;
  private String sfxs;
  private Integer xssx;
  private Date ksrq;
  private Date jsrq;
  private String kssj;
  private String jssj;
  private String memo;
  private String ylzd1;
  private String ylzd2;
  private String ylzd3;
  private Integer jgxh;
  private String fzbh;
  private Integer yhfsid;
  private String yhfsbh;
  private Integer yl4;
  private Double yl5;

  public Integer getId()
  {
    return this.id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public Integer getCpysid() {
    return this.cpysid;
  }

  public void setCpysid(Integer cpysid) {
    this.cpysid = cpysid;
  }

  public String getFunbh() {
    return this.funbh;
  }

  public void setFunbh(String funbh) {
    this.funbh = funbh;
  }

  public String getFunname1() {
    return this.funname1;
  }

  public void setFunname1(String funname1) {
    this.funname1 = funname1;
  }

  public String getFunname2() {
    return this.funname2;
  }

  public void setFunname2(String funname2) {
    this.funname2 = funname2;
  }

  public Integer getFkfsid() {
    return this.fkfsid;
  }

  public void setFkfsid(Integer fkfsid) {
    this.fkfsid = fkfsid;
  }

  public Integer getFkfzid() {
    return this.fkfzid;
  }

  public void setFkfzid(Integer fkfzid) {
    this.fkfzid = fkfzid;
  }

  public String getButcolor() {
    return this.butcolor;
  }

  public void setButcolor(String butcolor) {
    this.butcolor = butcolor;
  }

  public Integer getFuncid() {
    return this.funcid;
  }

  public void setFuncid(Integer funcid) {
    this.funcid = funcid;
  }

  public String getSfxs() {
    return this.sfxs;
  }

  public void setSfxs(String sfxs) {
    this.sfxs = sfxs;
  }

  public Integer getXssx() {
    return this.xssx;
  }

  public void setXssx(Integer xssx) {
    this.xssx = xssx;
  }

  public Date getKsrq() {
    return this.ksrq;
  }

  public void setKsrq(Date ksrq) {
    this.ksrq = ksrq;
  }

  public Date getJsrq() {
    return this.jsrq;
  }

  public void setJsrq(Date jsrq) {
    this.jsrq = jsrq;
  }

  public String getKssj() {
    return this.kssj;
  }

  public void setKssj(String kssj) {
    this.kssj = kssj;
  }

  public String getJssj() {
    return this.jssj;
  }

  public void setJssj(String jssj) {
    this.jssj = jssj;
  }

  public String getMemo() {
    return this.memo;
  }

  public void setMemo(String memo) {
    this.memo = memo;
  }

  public String getYlzd1() {
    return this.ylzd1;
  }

  public void setYlzd1(String ylzd1) {
    this.ylzd1 = ylzd1;
  }

  public String getYlzd2() {
    return this.ylzd2;
  }

  public void setYlzd2(String ylzd2) {
    this.ylzd2 = ylzd2;
  }

  public String getYlzd3() {
    return this.ylzd3;
  }

  public void setYlzd3(String ylzd3) {
    this.ylzd3 = ylzd3;
  }

  public Integer getJgxh() {
    return this.jgxh;
  }

  public void setJgxh(Integer jgxh) {
    this.jgxh = jgxh;
  }

  public String getFzbh() {
    return this.fzbh;
  }

  public void setFzbh(String fzbh) {
    this.fzbh = fzbh;
  }

  public Integer getYhfsid() {
    return this.yhfsid;
  }

  public void setYhfsid(Integer yhfsid) {
    this.yhfsid = yhfsid;
  }

  public String getYhfsbh() {
    return this.yhfsbh;
  }

  public void setYhfsbh(String yhfsbh) {
    this.yhfsbh = yhfsbh;
  }

  public Integer getYl4() {
    return this.yl4;
  }

  public void setYl4(Integer yl4) {
    this.yl4 = yl4;
  }

  public Double getYl5() {
    return this.yl5;
  }

  public void setYl5(Double yl5) {
    this.yl5 = yl5;
  }
}