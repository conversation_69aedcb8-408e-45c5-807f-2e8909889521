<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.mobilepos.rest.mapper.MobilePosTqZdkMapper">
    <select id="getBh" resultType="com.tzx.mobilepos.rest.model.TsBmkzk" >
        SELECT * FROM TS_BMKZK WHERE BMC = #{bmc} and ZDMC = #{zdmc}
    </select>
    <select id="getZdCount" resultType="com.tzx.mobilepos.rest.model.TqZdk" >
        select * from tq_zdk where ktskjh = #{ktskjh} and jzcs = 0
    </select>
    <update id="initZdk">
        update tq_zdk set yjje = '0',xmxjje = '0',zdje = '0',fkje = '0',zrje = '0', zkl = '100', fkce = '0', mlje = '0', zdzkl = null, yhfsid = null, yhfsbh = null, ktbcid = #{ktbcid}, fwyh = #{czybh}, ktczry = #{czybh}, dslj = '0', yhje = '0', zkje = '0', ygdlcs = #{ygdlcs} where kdzdbh = #{kdzdbh}
    </update>
    <delete id="delWdk">
        delete from tq_wdk where kdzdbh = #{kdzdbh}
    </delete>
    <delete id="delFkls">
        delete from tq_fklslsk where kdzdbh = #{kdzdbh}
    </delete>
    <update id="updateBh">
        update TS_BMKZK set nr = #{nr} where BMC = #{bmc} and ZDMC = #{zdmc}
    </update>
    <select id="addTc" resultType="java.lang.Integer" >
        select * from P_ADDTC(#{szdbh}, #{aitemid}, #{ixmsl});
    </select>
    <select id="addCm" useCache="false" resultType="java.lang.Integer" >
        select * from P_AddCM_APP (#{szdbh}, #{aitemid}, #{ixmsl}, #{sskjh}, #{sxsyh}, #{skwbh}, #{sggbh}, #{skwbz}, #{atype});
    </select>
    <select id="getYhsx" resultType="java.lang.String" >
        select yhsx from ts_yhfssdk where id = #{id}
    </select>
    <select id="yhfstj" resultType="java.lang.Integer" >
        select * from p_yhfstj(#{iyhfsid}, #{szdbh}, #{isl}, #{syhsx});
    </select>
    <select id="addYhfs" resultType="java.lang.Integer" >
        select * from P_ADDYHFS(#{aitemid}, #{iyhfsid}, #{szdbh},#{sskjh},#{ixmsl});
    </select>

    <select id="findBillMoney" resultType="com.tzx.mobilepos.rest.vo.BillMoney" >
        select kdzdbh, sum(cmje) as cope_with_money, sum(sjje) as actual_money ,
               sum(yhje) as discount_money from tq_wdk where kdzdbh = #{zdbh} and wdbz &lt;&gt; 'WDBZ_FS'
                                                         and wdbz &lt;&gt; 'WDBZ_MD' and wdbz &lt;&gt; 'WDBZ_SS' and wdbz &lt;&gt; 'WDBZ_TC'
                                                         and wdbz &lt;&gt; 'WDBZ_QX' and (cmsx isnull or cmsx &lt;&gt; 'CMSX_MX')
                                                         and yhfs = '' group by kdzdbh ;
    </select>
    <select id="findCalcMoney" resultType="com.tzx.mobilepos.rest.vo.CalcMoney" >
        select * from p_calcmoney(#{zdbh});
    </select>
    <select id="accountsOrder" resultType="com.tzx.mobilepos.rest.vo.AccountsOrder" >
        select * from p_payment(#{szdbh}, #{ijzid}, #{ifkje}, #{ifksl}, #{sfkhm}, #{ssfzhm}, #{slxdh}, #{sfkbz}, #{sskjh}, #{sskyh});
    </select>
    <update id="updateZdk">
        update tq_zdk set jzzdbh = #{jzzdbh}, jzbbrq = #{jzbbrq}, jzsj = #{jzsj}, jzcs = #{jzcs}, jzskjh = #{jzskjh}, jzczry = #{jzczry}, jzsx = #{jzsx}, ksjzsj = #{ksjzsj}, jzjssj = #{jzjssj}, jzbcid = #{jzbcid}, xfks = #{xfks}
        <if test="zwbh != null and zwbh != ''">
            ,zwbh = #{zwbh}
        </if>
        <if test="qch != null and qch != ''">
            ,qch = #{qch}
        </if>
        <if test="zzbz != null and zzbz != ''">
            ,zzbz = #{zzbz}
        </if>
        where kdzdbh = #{kdzdbh}
    </update>
    <update id="updateWdk">
        update tq_wdk set jzzdbh = #{jzzdbh}, jzbbrq = #{jzbbrq}, jzskjh = #{jzskjh}, jzbcid = #{jzbcid} where kdzdbh = #{kdzdbh}
    </update>
    <update id="updateFklslsk">
        update tq_fklslsk set jzzdbh = #{jzzdbh}, jzbcid = #{jzbcid} where kdzdbh = #{kdzdbh}
    </update>
    <select id="getFkfsid" resultType="com.tzx.mobilepos.rest.vo.PaymentWay" >
        select id, fkfsmc1 as payment_name1, yl3 as payment_mark from ts_fkfssdk where yl3 = #{yl3} order by id limit 1
        <!-- 		select b.id, b.fkfsmc1 as payment_name1 from tq_paymentbutton a inner join ts_fkfssdk b on a.fkfsid = b.id where b.yl3 = #{yl3}  -->
    </select>
    <select id="getJg" resultType="com.tzx.mobilepos.rest.model.TsPsjgsdk" >
        select jg.* from ts_psjgsdk jg ,(select sdnr from ts_ggcsk where sdbt = 'FDJGBH') gg where jg.jgbh = gg.sdnr
    </select>
    <select id="getWdDish" resultType="com.tzx.mobilepos.rest.vo.WdDishVo" >
        select rwid as wdrwid, clmxid,cmmc1,cmsl,sjje,cmbh,cmdj,cmid,cmsl as icmsl  from tq_wdk
        where kdzdbh = #{kdzdbh} and wdbz &lt;&gt; 'WDBZ_FS'
          and wdbz &lt;&gt; 'WDBZ_MD' and wdbz &lt;&gt; 'WDBZ_SS' and wdbz &lt;&gt; 'WDBZ_TC'
          and wdbz &lt;&gt; 'WDBZ_QX' and (cmsx isnull or cmsx &lt;&gt; 'CMSX_MX') and yhfs = ''
    </select>
    <select id="getWdDishNew" resultType="com.tzx.mobilepos.rest.vo.WdDishVo" >
        select rwid as wdrwid, clmxid,cmmc1,cmsl,round(sjje/cmsl::numeric ,2) as sjje,cmbh,cmdj,cmid,cmsl as icmsl  from tq_wdk
        where kdzdbh = #{kdzdbh} and wdbz &lt;&gt; 'WDBZ_FS'
          and wdbz &lt;&gt; 'WDBZ_MD' and wdbz &lt;&gt; 'WDBZ_SS' and wdbz &lt;&gt; 'WDBZ_TC'
          and wdbz &lt;&gt; 'WDBZ_QX' and (cmsx isnull or cmsx &lt;&gt; 'CMSX_MX') and yhfs = ''
    </select>
    <select id="getRifUrl" resultType="java.lang.String" >
        select sdnr from ts_ggcsk where sdbt = 'TZXMQWEBADD'
    </select>
    <select id="getDiscountR" resultType="com.tzx.mobilepos.rest.vo.ItemVo" >
        select clmxid as item_id, cmmc1 as item_name, cmsl as item_count, cmje as item_price,
               dcxh as item_serial, yhfsid as discount_id from tq_wdk where kdzdbh = #{kdzdbh}
                                                                        and wdbz &lt;&gt; 'WDBZ_FS' and wdbz &lt;&gt; 'WDBZ_MD' and wdbz &lt;&gt; 'WDBZ_SS' and wdbz &lt;&gt; 'WDBZ_TC'
                                                                        and wdbz &lt;&gt; 'WDBZ_QX' and (cmsx isnull or CMSX &lt;&gt; 'CMSX_MX') and yhfs &lt;&gt; ''
    </select>
    <select id="getItemR" resultType="com.tzx.mobilepos.rest.vo.ItemVo" >
        select wd.clmxid as item_id, wd.cmmc1 as item_name, wd.cmsl as item_count,
               wd.dwbh as item_unit_name, wd.cmdj as item_price, wd.cmje as amount_price, wd.dcxh as item_serial,
               wd.kwbz as item_taste, wd.kwbz as taste_name, wd.cmsx as is_combo from tq_wdk wd
        where wd.kdzdbh = #{kdzdbh} and wd.wdbz &lt;&gt; 'WDBZ_FS' and wd.wdbz &lt;&gt; 'WDBZ_MD'
          and wd.wdbz &lt;&gt; 'WDBZ_SS' and wd.wdbz &lt;&gt; 'WDBZ_TC' and wd.wdbz &lt;&gt; 'WDBZ_QX'
          and (wd.cmsx isnull or wd.cmsx &lt;&gt; 'CMSX_MX') and wd.yhfs = '' order by wd.dcxh
    </select>
    <select id="getComboDetailsR" resultType="com.tzx.mobilepos.rest.vo.ComboDetails" >
        select clmxid as item_id, cmid as details_id, cmmc1 as details_name, cmje as price,
               round(cast(cmsl as numeric), 1) as item_count, dwbh as item_unit_name, kwbz as taste_name from tq_wdk where kdzdbh = #{kdzdbh}
                                                                                                                       and clmxid = #{clmxid} and cmsx = 'CMSX_MX'
    </select>
    <select id="getTasteNames" resultType="java.lang.String" >
        select array_to_string(array(select kwnr1 from ts_kwsdk where kwbh in (${kwbhs})),',')
    </select>
    <select id="getBcid" resultType="com.tzx.mobilepos.rest.model.TsBck" >
        <!-- 		select id from ts_bck where to_char(kssj, 'HH24:MI:SS') &lt; to_char(now(), 'HH24:MI:SS') and to_char(jssj, 'HH24:MI:SS') &gt; to_char(now(), 'HH24:MI:SS') -->
        <!-- 		select * from ts_bck where bcbh = (select * from p_getbcbh(CAST(now() AS DATETIME))) -->
        select * from ts_bck where id = (select * from p_getbcmc(CAST(now() AS DATETIME)))
    </select>
    <update id="updateKtczry">
        update tq_zdk set fwyh = #{ktczry}, ktczry = #{ktczry}, ygdlcs = #{ygdlcs} where kdzdbh = #{kdzdbh}
    </update>
    <select id="getGgcs" resultType="com.tzx.mobilepos.rest.model.TsGgcsk" >
        select * from ts_ggcsk where sdbt = #{cdzd} limit 1
    </select>

    <!-- 根据开台收款记号查询订单 -->
    <select id="queryOrderByKtskjh" resultType="com.tzx.mobilepos.rest.vo.BilledOrderVo">
        select kdzdbh as orderNumber, fkje as actualAmount, lsdh as flowNumber,
        case xsms when 'XSMS_TS' then '堂食' when 'XSMS_WM' then '外带' else '其他' end as salesModel,
        zdzt , jzsx ,case  when jzsj ISNULL then ktsj  else jzsj end as orderTime,
        case when zwbh is null or zwbh = '' then qch else zwbh end as qch,
        case when zwbh is null or zwbh = '' then '取餐号' else '桌台号' end as qchTitle
        from TQ_ZDK where jzsx = #{jzsx}
        <if test="jzczry != null and jzczry != ''">
            and jzczry = #{jzczry} order by jzsj desc
        </if>
        <if test="ktczry != null and ktczry != ''">
            and ktczry = #{ktczry} order by ktsj desc
        </if>
    </select>

    <select id="queryOrderDetailByKdzdbh" resultType="com.tzx.mobilepos.rest.model.TqWdk">
        SELECT
            kdzdbh,
            zdje,
            lsdh,
            case xsms when 'XSMS_TS' then '堂食' else '其他' end as xsms,
            ksdcsj,
            qch
        FROM TQ_WDK WHERE KDZDBH = #{kdzdbh}
    </select>

    <select id="queryBillInfo" resultType="com.tzx.mobilepos.rest.vo.BilledOrderVo">
        select zzbz, kdzdbh as orderNumber, lsdh as flowNumber, yhje as zkje, fkje as actualAmount,
        (select sum(cmsl) from tq_wdk where kdzdbh = #{kdzdbh})as sum,
        case when zwbh is null or zwbh = '' then qch else zwbh end as qch,
        case when zwbh is null or zwbh = '' then '取餐号' else '桌台号' end as qchTitle
        from tq_zdk
        where kdzdbh = #{kdzdbh}
        <if test="ktskjh != null and ktskjh !=''">
            and ktskjh = #{ktskjh}
        </if>
    </select>

    <select id="queryBillTcInfoByKdzdbh" resultType="com.tzx.mobilepos.rest.vo.BillItemVo">
        select c.cmid,w.clmxid,w.cmmc1 as itemName,w.cmsl, w.cmje,case w.sfxsmx when 'Y' then  w.sfxsmx else 'N' end as sftc from tq_wdk w left join ts_cmk c on w.cmid = c.cmid where  w.kdzdbh = #{kdzdbh} and w.sfxsmx = 'Y' and w.cmid is not null order by rwid
    </select>

    <select id="queryBillDetailByKdzdbh" resultType="com.tzx.mobilepos.rest.vo.BillItemVo">
        select w.rwid,c.cmid,w.clmxid,w.cmmc1 as itemName,cmsl, cmje ,case w.sfxsmx when 'Y' then  w.sfxsmx else '' end as sfxsmx, w.dcxh, w.kwbz as item_taste, w.syyhfkfsid, w.yhfs from tq_wdk w left join ts_cmk c on w.cmid = c.cmid where  w.kdzdbh = #{kdzdbh}
        <if test="sftc != null and sftc !=''">
            and w.sfxsmx = #{sftc}
        </if>
        <if test="sftc == null or sftc ==''">
            and w.sfxsmx is null
        </if>
        <if test="clmxid != null and clmxid !=''">
            and w.clmxid = #{clmxid}
        </if>
        <if test="dcxh != null and dcxh !=0">
            and w.dcxh = #{dcxh}
        </if>
        <if test="syyhfkfsid != null and syyhfkfsid !=0">
            and w.syyhfkfsid = #{syyhfkfsid}
        </if>
        order by rwid
    </select>

    <select id="findIcibd" resultType="Integer">
        select id from tq_cbsdk where yl1 = 'TS'
    </select>

    <select id="useAppCanUseYhfs" resultType="Integer">
        SELECT *
        FROM
            p_useappcanuseyhfs(#{bill_num}, #{icbid}, #{sjgtxbh},#{sskjh},#{ifuntype})
    </select>

    <select id="findJgtxbh" resultType="String">
        select p.jgtxbh from ts_psjgsdk p left join ts_ggcsk g on g.sdnr = p.jgbh where g.sdbt = 'FDJGBH'
    </select>
    <select id="queryDiscountzZr" resultType="com.tzx.mobilepos.rest.vo.DiscountVo">
        select cl.qxid, cl.showxh, cl.yhfsid as dishno, cl.id as dishid, cl.buttoncolor as color, cl.clid, xmmc1 as dishname, yhfsid as discounttypeid
        from tq_clmxk cl left join ts_yhfssdk yh on cl.yhfsid=yh.id  where cl.yhfsid = -110
        <!-- 		select cl.qxid, cl.showxh, cl.yhfsid as dishno, cl.id as dishid, cl.buttoncolor as color,  -->
        <!-- 		cl.clid, cl.xmmc1 as dishname, cl.yhfsid as discounttypeid -->
        <!-- 		from tq_yhfsqxk qx  -->
        <!-- 		left join tq_clmxk cl on cl.yhfsid = qx.yhid  -->
        <!-- 		left join ts_yhfssdk yh on cl.yhfsid = yh.id  -->
        <!-- 		where qx.groupid = #{groupid} and cl.yhfsid = -110 -->
    </select>
    <select id="queryDiscount" resultType="com.tzx.mobilepos.rest.vo.DiscountVo">
        SELECT *
        FROM
        p_getappcanuseyhfs(#{kdzdbh},#{icbid},#{sjgtxbh},0) as dishk(clid int4,DishId int, DishNo varchar, DishName varchar, Color varchar, DiscountTypeId int4,
        showxh int4,qxid varchar, DishPrice NUMERIC(12,2), xmid int4, PqlxId int4,DishUnit varchar,
        DishType  varchar,YHSX varchar, YL3 varchar, ISSALEOUT varchar, SALEOUTCOUNT int4,DishPrice2 NUMERIC(12,2),ZKFS  Varchar)
        ;

        <!-- select * from p_getcanuseyhfs(#{kdzdbh},#{icbid},'',#{sjgtxbh})as dishk(clid int4,dishid int, dishno varchar, dishname varchar, color varchar, discounttypeid int4,
     showxh int4,qxid varchar, dishprice numeric(12,2), xmid int4, pqlxid int4,dishunit varchar,
 dishtype  varchar,yhsx varchar, yl3 varchar, issaleout varchar, saleoutcount int4,dishprice2 numeric(12,2),zkfs  varchar) -->
    </select>

    <insert id="addTcdcxzlsk">
        insert into tq_tcdcxzlsk(kdzdbh,tcid,tcxh,mxxmid,cmsl,xcsl,cmdj,cmje,fzje,mxlxid)
        values(#{kdzdbh},#{tcid},#{tcxh},#{item_id},#{item_count},'1',#{cmdj},#{cmje},#{fzje},#{mxlxid});
    </insert>

    <select id="findTcInfo" resultType="java.lang.Integer">
        <!-- 		select c.id as tcxh, d.tcid  -->
        <!-- 		from ts_tcmxk c  -->
        <!-- 		left join (select a.xmid as tcid from tq_clmxk a where a.id = #{item_id}) d on d.tcid = c .xmid -->
        <!-- 		where c.xmid = (select a.xmid as tcid from tq_clmxk a where a.id = #{item_id}) and c.mxxmid = #{cmid} and c.mxlx = 'ERP_MXLX_GROUP' -->
        select xmid from tq_clmxk where id = #{item_id}
    </select>

    <select id="findItem" resultType="com.tzx.mobilepos.rest.vo.GroupDetails">
        select e.fzid as mxlxid,e.fzje,f.cmdj, tc.sjjg
        from ts_tcfzmxk e
        left join ts_cmk f on e.mxid = f.cmid
        left join ts_tcmxk tc on tc.xmid = #{tcid} and tc.id = #{tcxh} and e.fzid = tc.mxxmid
        where e.id = #{id}
        <!-- 		and e.mxid = #{mxid} and e.fzid = #{fzid}   -->

    </select>

    <select id="cancelBill" resultType="java.lang.Integer" >
        select * from p_cancelbill_app(#{azdbh}, #{azdrq}, #{askjh}, #{sczry});
    </select>
    <select id="changeMone" resultType="java.lang.String" >
        select COALESCE(sum(FKJE),0) FKJE from TQ_FKLSLSK where KDZDBH = #{kdzdbh} and SFZL='Y';
    </select>

    <select id="zRtr" resultType="java.lang.Integer">
        select * from P_ZRTR(#{bill_num});
    </select>
    <select id="getPayMent" resultType="com.tzx.mobilepos.rest.vo.PayMentVo" >
        select ls.kdzdbh, ls.jzzdbh, ls.fklxsx, ls.jzid, sd.fkfsmc1, coalesce(sum(fkje),0) fkje
        from tq_fklslsk ls
                 left join ts_fkfssdk sd on ls.jzid = sd.id
        where ls.kdzdbh=#{kdzdbh}
        group by ls.kdzdbh, ls.jzzdbh, ls.fklxsx, ls.jzid, sd.fkfsmc1
    </select>

    <update id="updateXsms">
        update tq_zdk set xsms = #{xsms}, zwbh = #{qch}
        <if test="qch != null and qch !=''">
            , qch = #{qch}
        </if>
        where kdzdbh = #{kdzdbh}
    </update>
    <select id="getGqplk" resultType="java.lang.String" >
        select array_to_string(ARRAY(SELECT unnest(array_agg(cmmc1))),',') as cmmc
        from ts_gqplk a left join ts_cmk b on (a.cmid=b.cmid) where a.kdzdbh=#{kdzdbh}
    </select>
    <select id="cancelJd" useCache="false" resultType="java.lang.Integer" >
        select * from p_canceljdnew(#{szdbh}, #{irwid}, #{isl}, #{sskjh}, #{sczrh}, #{deltype});
    </select>
    <update id="updateJjcrwid">
        update tq_wdk set jjcrwid = rwid where kdzdbh = #{kdzdbh}
    </update>
    <select id="getFullMoney" resultType="java.lang.String" >
        select coalesce(sum(fkje),0) from tq_fklslsk
        where fklxsx = 'FKSX_XJ' and skjh = #{jtbh} and jzbbrq = #{bbrq} and jzbcid = #{bcid} and skyh = #{czybh}
    </select>
    <select id="getCrossedMoney" resultType="java.lang.String" >
        select coalesce(sum(skje),0) from tq_zbjlsklsk where bbrq=#{bbrq} and bbbc=#{bcid} and skybh=#{czybh}
    </select>
    <select id="getFkje" resultType="java.lang.Double" >
        select coalesce(sum(fkje),0) from tq_fklslsk where kdzdbh = #{zdbh}
    </select>
    <select id="getZdje" resultType="java.lang.Double" >
        select (coalesce(sum(fkje),0) + coalesce(sum(dslj),0)) as fkje from tq_zdk where kdzdbh = #{zdbh}
    </select>
    <select id="getZdjeAndDsf1" resultType="java.lang.Double" >
        select (coalesce(sum(fkje),0)) as fkje from tq_zdk where kdzdbh =  #{zdbh}
    </select>
    <select id="getZdjeAndDsf2" resultType="java.lang.Double" >
        select (coalesce(sum(yhje),0)) as yhje from tq_thirdpaydiscount where kdzdbh = #{zdbh}
    </select>

    <select id="getFklsList" resultType="com.tzx.mobilepos.rest.vo.PaymentRunningWater" >
        select fkls.id as fklsid, fkls.fkfsmc1 as fkfsmc, fkls.fkje, fkls.jzid, fksd.fkfsbh, fkls.fkhm as yzm,
               case fksd.yl3 when 'ERP_FKFS_WX' then 'FKSX_SMZF' when 'ERP_FKFS_ZFB' then 'FKSX_SMZF' else fkls.fklxsx end as fklxsx,
               case fksd.yl3 when 'ERP_FKFS_WX' then 'FKSX_SMZF' when 'ERP_FKFS_ZFB' then 'FKSX_SMZF' else fksd.yl3 end as payment_mark
        from tq_fklslsk fkls left join ts_fkfssdk fksd on fkls.jzid = fksd.id where fkls.kdzdbh = #{zdbh}
    </select>
    <select id="getCouponlsList" resultType="com.tzx.mobilepos.rest.vo.CouponRunningWater" >
        select tpd.*, case  when tpd.yhje <![CDATA[ < ]]> 0 then concat_ws('',wct.couponname,'(多收)')  else wct.couponname end as couponname
        from tq_thirdpaydiscount tpd
                 left join tq_acewil_coupon_cache wct on wct.zdbh = tpd.kdzdbh and wct.couponcode = tpd.yzm
        where tpd.kdzdbh = #{zdbh} and tpd.optype = #{optype}
    </select>

    <delete id="delFkje">
        delete from tq_fklslsk where kdzdbh = #{kdzdbh}
        <choose>
            <when test="fklsid !=0">
                and id = #{fklsid}
            </when>
            <when test="fkhm !=0">
                and fkhm = #{fkhm}
            </when>
            <when test="fkbz != ''">
                and fkbz = #{fkbz}
            </when>
            <otherwise>
                and fklxsx = #{fklxsx}
            </otherwise>
        </choose>

    </delete>
    <delete id="delFkjeByFkhm">
        delete from tq_fklslsk where kdzdbh = #{kdzdbh} and fkhm = #{fkhm}
    </delete>
    <insert id="insertMantualZrAmount">
        insert into tq_mantualzramount(kdzdbh,zrje)
        values(#{kdzdbh},#{zrje});
    </insert>
    <delete id="delMantualZrAmount">
        delete from tq_mantualzramount where kdzdbh = #{kdzdbh}
    </delete>
    <select id="getPaymentWay" resultType="com.tzx.mobilepos.rest.vo.PaymentWay" >
        select id,fkfsmc1 as payment_name1,fkfsmc2 as payment_name2,fklxsx as payment_class,fkfsbh as payno  from ts_fkfssdk where id = #{fkfsid}
    </select>
    <update id="updateTaste">
        update tq_wdk set kwbz = #{kwbz} where kdzdbh = #{kdzdbh} and rwid = #{rwid}
    </update>

    <select id="findYhfsid" resultType="String">
        select distinct t.yhfsid from tq_clmxk t left join ts_yhfssdk s on s.id = t.yhfsid where s.yhsx = #{yhsx}
    </select>

    <select id="queryDiscountsRoot" resultType="com.tzx.mobilepos.rest.vo.DiscountsRoot">
        select * from tq_yhfsqxk where yhid = #{yhfsid}
    </select>
    <select id="getZdWj" resultType="com.tzx.mobilepos.rest.model.TqZdk" >
        select * from tq_zdk where ktskjh = #{jtbh} and jzsx = 'ZDSX_WJ'
    </select>
    <select id="getPayMentToJtbh" resultType="com.tzx.mobilepos.rest.vo.PayMentVo" >
        select ls.kdzdbh, ls.fklxsx, ls.jzid from tq_fklslsk ls
                                                      inner join tq_zdk zd on zd.jzsx = 'ZDSX_WJ' and zd.ktskjh = #{jtbh} and ls.kdzdbh = zd.kdzdbh limit 1;
    </select>

    <select id="findCmje" resultType="BigDecimal">
        select ROUND(sjje/cmsl, 2) from tq_zdk where zdbh = #{zdbh} and cmid = #{xmid}
    </select>

    <select id="updatePqhm">
        update tq_wdk set pqhm = #{yzm} where kdzdbh = #{zdbh} and clmxid = #{xmid}
    </select>

    <select id="getNoCrossedMoney" resultType="java.lang.String" >
        <!-- 		select coalesce(sum(ls.fkje),0) from tq_fklslsk ls  -->
        <!-- 		where ls.fklxsx = 'FKSX_XJ' and ls.skjh = #{jtbh} and ls.jzbbrq = #{bbrq} and ls.jzbcid = #{bcid}  -->
        <!-- 		and ls.skyh = #{czybh} and kdzdbh not in (select skzd.kdzdbh from tq_skhzddyk skzd where skzd.bbrq = #{bbrq})  -->
        select coalesce(sum(ls.fkje),0) from tq_fklslsk ls
        where ls.jzid = '0' and ls.jzbbrq = #{bbrq} and ls.jzbcid = #{bcid}
        and ls.skyh = #{czybh} and kdzdbh not in (select skzd.kdzdbh from tq_skhzddyk skzd where skzd.bbrq = #{sbbrq})
    </select>
    <select id="countDsfFkls" resultType="java.lang.Integer">
        select count(id) from tq_fklslsk ls where kdzdbh = #{kdzdbh} and jzid = #{jzid}
        <choose>
            <when test="fkhm != ''">
                and fkhm = #{fkhm}
            </when>
            <otherwise>
                and fkhm &lt;&gt; '第三方优惠'
            </otherwise>
        </choose>

    </select>

    <select id="getZdmx" resultType="java.lang.Integer" >
        select count(kdzdbh) from tq_wdk where kdzdbh = #{zdbh}
    </select>

    <select id="getWdMtDish" resultType="com.tzx.mobilepos.rest.vo.WdDishVo">
        select
        <!--a.rwid as wdrwid,a.clmxid,a.cmmc1,a.sjje,(round(a.sjje / a.cmsl,4)) price,a.cmbh,a.cmdj,( a.cmsl - COALESCE(b.cmsl2,0) )as cmsl from tq_wdk a left join  -->
        a.rwid as wdrwid,a.clmxid,a.cmmc1,a.sjje,cmdj as price,a.cmbh,a.cmdj,( a.cmsl - COALESCE(b.cmsl2,0) )as cmsl from tq_wdk a left join
        (
        select
        COALESCE(count(*),0) as cmsl2
        ,kdzdbh
        ,wdrwid
        from
        tq_yhmtcouponsorders
        where
        kdzdbh = #{zdbh} and status = '0' and isdone = 'y'
        GROUP BY kdzdbh,wdrwid
        ) b on b.wdrwid = a.rwid and a.kdzdbh = b.kdzdbh
        <![CDATA[
			where a.kdzdbh = #{zdbh} and a.WDBZ<>'WDBZ_FS'
			and a.WDBZ<>'WDBZ_MD' and a.WDBZ<>'WDBZ_SS' and a.WDBZ<>'WDBZ_TC'
			and a.WDBZ<>'WDBZ_QX' and (a.CMSX isnull or a.CMSX<>'CMSX_MX')
			and ( a.cmsl - COALESCE(b.cmsl2,0) ) > 0 and coalesce(a.yhfsid, -1) = -1
			order by a.cmbh asc, a.cmsl asc
		]]>
    </select>

    <select id="getZdk" resultType="com.tzx.mobilepos.rest.model.TqZdk" >
        select * from tq_zdk where kdzdbh = #{kdzdbh}
    </select>

    <select id="getOuttradeno" resultType="java.lang.String" >
        select fkbz from tq_fklslsk
    </select>

    <select id="getFkjeNoZl" resultType="java.lang.Double" >
        select coalesce(sum(fkje),0) from tq_fklslsk where kdzdbh = #{zdbh} and sfzl = 'N'
    </select>

    <select id="checkIsEnablePv" resultType="java.lang.Integer" >
        select count(1) from TS_FKFSSDK b LEFT JOIN ts_ggcsk c on  b.yl3 =  c.sdbt where c.syfw ='PV' and b.yl3=#{paymentMark}
    </select>

    <select id="callP_SendKVSData" resultType="java.lang.String" >
        select * from sendkvsdata(#{szdbh})
    </select>

    <select id="countYhtemp" resultType="java.lang.Integer">
        select count(yzm) from tq_yhmtcouponstemp where kdzdbh = #{kdzdbh} and jzid = #{jzid}
    </select>
    <delete id="delYhtemp">
        delete from tq_yhmtcouponstemp where kdzdbh = #{kdzdbh}
    </delete>
    <select id="getDsfyh" resultType="com.tzx.mobilepos.rest.vo.Dish" >
        <!-- 		select a.id as details_id,a.xmmc1 as item_name,a.yhfsid from tq_clmxk a left join tq_clsdk b on a.clid = b.id  -->
        <!-- 		left join tq_cbsdk c on b.cbid = c.id left join ts_yhfssdk d on a.yhfsid = d.id  -->
        <!-- 		where c.yl1 = 'TS' and d.yhsx = '61' and d.id is not null limit 1 -->
        select -1 as details_id, b.yhfsmc1 as item_name,b.id as yhfsid from ts_yhfssdk b where  yhsx =#{yhsx}
    </select>
    <delete id="delTpd">
        delete from tq_thirdpaydiscount where kdzdbh = #{kdzdbh}
        <if test="yhfsid !=0">
            and yhfsid = #{yhfsid}
        </if>
        <if test="yzm != null and yzm != ''">
            and yzm = #{yzm}
        </if>
    </delete>

    <select id="getBuywdandYhitems" resultType="com.tzx.mobilepos.rest.vo.BuywdandYhitems">
        select * from p_getbuywdandyhitems(#{szdbh},#{iyhfsid},#{syhsx},${icbid},#{ssellmode},#{sjgtxbh},1) as dishk
                          (clid int4,DishId int, DishNo varchar, DishName varchar, Color varchar,
                           DiscountTypeId int4, showxh int4,qxid varchar, DishPrice NUMERIC(12,2), xmid int4,
                           PqlxId int4,DishUnit varchar,DishType varchar,YHSX varchar, YL3 varchar, ISSALEOUT varchar,
                           SALEOUTCOUNT int4,DishPrice2 NUMERIC(12,2),is_gift varchar, yhfsmxPrice NUMERIC(12,2),
                           times NUMERIC,yhcmsl NUMERIC,needaddcmsl NUMERIC,usewdcmsl NUMERIC, yhtimes int4);
    </select>

    <select id="getBuydishrel" resultType="java.lang.Integer" >
        select buydishrel from ts_yhfssdk where id = #{id}
    </select>

    <select id="getYhneedChoitems" resultType="com.tzx.mobilepos.rest.vo.BuywdandYhitems">
        select * from p_getyhneedchoitems(#{iyhfsid},#{syhsx},${icbid},#{ssellmode},#{sjgtxbh}) as dishk
                          (clid int4,DishId int, DishNo varchar, DishName varchar, Color varchar, DiscountTypeId int4,
                           showxh int4,qxid varchar, DishPrice NUMERIC(12,2), xmid int4, PqlxId int4,DishUnit varchar,
                           DishType varchar,YHSX varchar, YL3 varchar, ISSALEOUT varchar, SALEOUTCOUNT
                               int4,DishPrice2 NUMERIC(12,2) ,is_gift varchar, yhfsmxPrice NUMERIC(12,2)) where is_gift = 'Y';
    </select>

    <insert id="insertWdandYhTempItem">
        insert into tq_wdandyhtempitem(yhfsid,clmxid, kdzdbh, isneedadd, cmsl, usetag,rwid)
        values(#{yhfsid},#{clmxid},#{kdzdbh},#{isneedadd},#{cmsl},#{usetag},#{rwid});
    </insert>
    <insert id="insertYhMainTempItem">
        insert into tq_yhmaintempitem(yhfsid,kdzdbh,yhtimes,paramtype)
        values(#{yhfsid},#{kdzdbh},#{yhtimes},#{paramtype});
    </insert>
    <insert id="insertYhTempItem">
        insert into tq_yhtempitem(yhfsid,clmxid, kdzdbh,isgift,cmsl)
        values(#{yhfsid},#{clmxid},#{kdzdbh},#{isgift},#{cmsl});
    </insert>
    <delete id="delWdandYhTempItem">
        delete from tq_wdandyhtempitem where kdzdbh = #{kdzdbh}
    </delete>
    <delete id="delYhMainTempItem">
        delete from tq_yhmaintempitem where kdzdbh = #{kdzdbh}
    </delete>
    <delete id="delYhTempItem">
        delete from tq_yhtempitem where kdzdbh = #{kdzdbh}
    </delete>

    <select id="queryWdkByCmid" resultType="com.tzx.mobilepos.rest.model.TqWdk">
        select * from tq_wdk where kdzdbh = #{kdzdbh} and cmid = #{clmxid} and (cmsx = 'CMSX_DP' or cmsx = 'CMSX_TC') order by cmsl desc
    </select>

    <select id="cancelThirdYhfs" useCache="false" resultType="java.lang.Integer" >
        select * from p_cancelthirdyhfsnew (#{zdbh}, #{yzm}, #{yhfsid});
    </select>

    <insert id="insertTqWdkCouponTemp">
        insert into tq_wdk_coupon_temp(zdbh,scancode,datatype,useok,yhfsid,coupontype,couponcode,couponprice,coupondishcode,fkfs,remark,is_usable,rwid,cardcode,templateid,coupondishcodes,useablemsg,couponname,coupon_salemoney,coupon_totalmoney)
        select zdbh,scancode,datatype,useok,yhfsid,coupontype,couponcode,#{couponprice},coupondishcode,fkfs,remark,is_usable,rwid,cardcode,templateid,coupondishcodes,useablemsg,couponname,couponsale,coupon_totalmoney from tq_acewil_coupon_cache
        where zdbh = #{zdbh} and couponcode = #{couponcode}
    </insert>

    <insert id="insertTqWdkCouponTempQm">
        insert into tq_wdk_coupon_temp(zdbh,scancode,datatype,useok,yhfsid,coupontype,couponcode,couponprice,coupondishcode,fkfs,remark,is_usable,rwid,cardcode,templateid,coupondishcodes,useablemsg,couponname,coupon_salemoney,coupon_totalmoney)
        select zdbh,scancode,datatype,useok,yhfsid,coupontype,couponcode,#{couponprice},coupondishcode,fkfs,remark,is_usable,rwid,cardcode,templateid,coupondishcodes,useablemsg,couponname,#{couponsale},#{couponprice} from tq_acewil_coupon_cache
        where zdbh = #{zdbh} and couponcode = #{couponcode}
    </insert>

    <delete id="delTqWdkCouponTemp">
        delete from tq_wdk_coupon_temp where zdbh = #{zdbh}
        <if test="couponcode != null and couponcode != ''">
            and couponcode = #{couponcode}
        </if>
    </delete>

    <delete id="delTqAcewilCouponCache">
        delete from tq_acewil_coupon_cache where zdbh = #{zdbh}
    </delete>
    <delete id="delZdk">
        delete from tq_zdk where kdzdbh = #{kdzdbh}
    </delete>


    <!-- 查询所有未结账单 -->
    <select id="queryOrderToWj" resultType="com.tzx.mobilepos.rest.vo.BilledOrderVo">
        select a.* from (select DISTINCT tteo.billid, zd.kdzdbh as orderNumber, zd.fkje as actualAmount, zd.lsdh as flowNumber,
                                         case zd.xsms when 'XSMS_TS' then '堂食' when 'XSMS_WM' then '外带' else '其他' end as salesModel,
                                         zd.zdzt , zd.jzsx ,case  when zd.jzsj ISNULL then zd.ktsj  else zd.jzsj end as orderTime, zd.zwbh as qch
                         from tq_zdk zd
                                  left join tq_third_except_order tteo on tteo.billid = zd.kdzdbh
                         where zd.jzsx = 'ZDSX_WJ' and zd.ktczry = #{ktczry}) a
        order by a.orderTime desc
    </select>

    <select id="findRetryTteoList" resultType="com.tzx.mobilepos.rest.model.TqThirdExceptOrder">
        select * from tq_third_except_order where billid = #{billid} order by pay_status asc, updatetime desc
    </select>

    <select id="getZdWjList" resultType="com.tzx.mobilepos.rest.model.TqZdk" >
        select * from tq_zdk where ktskjh = #{jtbh} and jzsx = 'ZDSX_WJ'
    </select>

    <select id="getWdDishMtZh" resultType="com.tzx.mobilepos.rest.vo.WdDishVo" >
        select rwid as wdrwid, clmxid,cmmc1,cmsl,round(sjje/cmsl::numeric ,2) as sjje,cmbh,cmdj,cmid,cmsl as icmsl
        from tq_wdk where kdzdbh = #{kdzdbh} and wdbz &lt;&gt; 'WDBZ_FS'
        and wdbz &lt;&gt; 'WDBZ_MD' and wdbz &lt;&gt; 'WDBZ_SS' and wdbz &lt;&gt; 'WDBZ_TC'
        and wdbz &lt;&gt; 'WDBZ_QX' and (cmsx isnull or cmsx &lt;&gt; 'CMSX_MX')
        <!-- 		and yhfs = ''  -->
    </select>

    <select id="gettWdrwid" resultType="com.tzx.mobilepos.rest.vo.WdDishVo" >
        select rwid as wdrwid, sjje, cmdj from tq_wdk where kdzdbh = #{zdbh} and cmid = #{cmid}
                                                        and wdbz &lt;&gt; 'WDBZ_FS' and wdbz &lt;&gt; 'WDBZ_MD' and wdbz &lt;&gt; 'WDBZ_SS' and wdbz &lt;&gt; 'WDBZ_TC'
                                                        and wdbz &lt;&gt; 'WDBZ_QX' and (cmsx isnull or cmsx &lt;&gt; 'CMSX_MX') order by cmsl limit 1
    </select>

    <select id="getXmidByXmbh" resultType="java.lang.Integer" >
        select cmid from ts_cmk where cmbh = #{cmbh}
    </select>

    <update id="updateByInvoice">
        update tq_zdk set kfpsqm = #{kfpsqm}, shortkfpsqm = '', kfpje=#{kfpje}, sfkfp='Y', isselprinteinvoice=#{isselprinteinvoice} where kdzdbh = #{kdzdbh}
    </update>

    <select id="getKpje" resultType="java.lang.Double">
        select coalesce(sum(ls.fkje), 0) from tq_fklslsk ls
                                                  left join ts_fkfssdk sd on sd.id = ls.jzid
        where ls.kdzdbh = #{kdzdbh} and sd.sfkfp = 'Y'
    </select>

    <select id="getAddSaleOutData" resultType="com.tzx.mobilepos.rest.vo.WdDishVo">
        select cmid, cmbh,  sum(cmsl) as cmsl  from tq_wdk where kdzdbh = #{kdzdbh} group by cmid, cmbh
    </select>

    <select id="findCancleMoling" resultType="com.tzx.mobilepos.rest.vo.CalcMoney" >
        select * from p_canclemoling(#{szdbh}, #{itype});
    </select>

    <select id="findMoling" resultType="com.tzx.mobilepos.rest.vo.CalcMoney" >
        select * from p_moling(#{szdbh}, #{itype});
    </select>

    <select id="queryWdkByRwid" resultType="com.tzx.mobilepos.rest.model.TqWdk">
        select * from tq_wdk where rwid = #{rwid}
    </select>

    <select id="getFklslsk" resultType="com.tzx.mobilepos.rest.model.TqFklslsk" >
        select *, sd.sfsr from tq_fklslsk ls left join ts_fkfssdk sd on sd.id = ls.jzid where kdzdbh = #{kdzdbh} order by ls.id
    </select>

    <select id="getBjyhYh" resultType="com.tzx.mobilepos.rest.model.TsYhfssdk" >
        select * from ts_yhfssdk where yhfsbh = #{yhfsbh}
    </select>

    <select id="getTqWdkCouponTemp" resultType="com.tzx.mobilepos.rest.model.TqWdkCouponTemp" >
        select * from tq_wdk_coupon_temp where zdbh = #{zdbh}
    </select>

    <select id="getWdQmDish" resultType="com.tzx.mobilepos.rest.vo.QmWdDishVo" >
        select  cmmc1 as name, cmsl as num,
        <if test='qmType == "2"'>
            cmbh as trade_mark,
        </if>
        <if test='qmType == "1"'>
            cmid as trade_mark,
        </if>
        round(sjje/cmsl::numeric ,2) as price
        from tq_wdk where kdzdbh = #{zdbh} and wdbz &lt;&gt; 'WDBZ_FS'
        and wdbz &lt;&gt; 'WDBZ_MD' and wdbz &lt;&gt; 'WDBZ_SS' and wdbz &lt;&gt; 'WDBZ_TC'
        and wdbz &lt;&gt; 'WDBZ_QX' and (cmsx isnull or cmsx &lt;&gt; 'CMSX_MX') and yhfs = ''
    </select>

    <select id="getWdQmDishUpload" resultType="com.tzx.mobilepos.rest.vo.QmUploadWdDishVo" >
        select cmsl as num, cmmc1 as name,
        <if test='qmType == "2"'>
            cmbh as trade_mark,
        </if>
        <if test='qmType == "1"'>
            cmid as trade_mark,
        </if>
        cmdj as price, round(sjje/cmsl::numeric ,2) as price1, abs(cmje) as amount, cmbh as id
        from vq_zdmxk where kdzdbh = #{zdbh} and jzbbrq = #{bbrq} and wdbz &lt;&gt; 'WDBZ_FS'
        and wdbz &lt;&gt; 'WDBZ_MD' and wdbz &lt;&gt; 'WDBZ_SS' and wdbz &lt;&gt; 'WDBZ_TC'
        and wdbz &lt;&gt; 'WDBZ_QX' and (cmsx isnull or cmsx &lt;&gt; 'CMSX_MX')
        <if test='yht == "2"'>
            and yhfs &lt;&gt; ''
        </if>
        <if test='yht == "1"'>
            and yhfs = ''
        </if>
    </select>

    <select id="getWdQmDishRefundUpload" resultType="com.tzx.mobilepos.rest.vo.QmWdDishVo" >
        select  cmmc1 as name, cmsl as num,
        <if test='qmType == "2"'>
            cmbh as trade_mark,
        </if>
        <if test='qmType == "1"'>
            cmid as trade_mark,
        </if>
        round(sjje/cmsl::numeric ,2) as price
        from vq_zdmxk where kdzdbh = #{zdbh} and jzbbrq = #{bbrq} and wdbz &lt;&gt; 'WDBZ_FS'
        and wdbz &lt;&gt; 'WDBZ_MD' and wdbz &lt;&gt; 'WDBZ_SS' and wdbz &lt;&gt; 'WDBZ_TC' and coalesce(memo, '') &lt;&gt; 'QX'
        and (cmsx isnull or cmsx &lt;&gt; 'CMSX_MX') and yhfs = ''
    </select>

    <select id="getQmZdk" resultType="com.tzx.mobilepos.rest.model.TqZdk" >
        select * from vq_zdk where kdzdbh = #{kdzdbh} and jzbbrq = #{bbrq}
    </select>

    <select id="getVqFklsList" resultType="com.tzx.mobilepos.rest.vo.FklsVo" >
        select sd.sfjf, sd.yl3 as paysource , ls.sfzhm, ls.* from vq_fklsk ls left join ts_fkfssdk sd on ls.jzid = sd.id
        where ls.kdzdbh = #{kdzdbh} and sd.fkfsbh &lt;&gt; '1973'
    </select>

    <select id="getVqFklsDsfCount" resultType="java.lang.Integer">
        select count(ls.*) from vq_fklsk ls left join ts_fkfssdk sd on ls.jzid = sd.id
        where sd.yl3 in ('ERP_FKFS_ZFB','ERP_FKFS_WX') and ls.kdzdbh = #{kdzdbh}
    </select>

    <select id="getBizidList" resultType="com.tzx.mobilepos.rest.vo.FklsVo" >
        select sd.sfjf, sd.yl3 as paysource , ls.sfzhm, ls.* from vq_fklsk ls left join ts_fkfssdk sd on ls.jzid = sd.id
        where ls.kdzdbh = #{kdzdbh} and sd.fkfsbh = '1973' limit 1
    </select>

    <select id="getWdQmDishUploadCoupons" resultType="com.tzx.mobilepos.rest.vo.QmUploadWdDishVo" >
        select cmsl as num, cmmc1 as name,
        <if test='qmType == "2"'>
            cmbh as trade_mark,
        </if>
        <if test='qmType == "1"'>
            cmid as trade_mark,
        </if>
        round(sjje/cmsl::numeric ,2) as price, abs(cmje) as amount,
        case when pqhm is null then cmbh when pqhm = '' then cmbh else pqhm end as id,
        case cmbh when '**********' then 'coupon' when '**********' then 'card' else 'offline_discount' end as method
        from vq_zdmxk where kdzdbh = #{zdbh} and jzbbrq = #{bbrq} and wdbz &lt;&gt; 'WDBZ_FS'
        and wdbz &lt;&gt; 'WDBZ_MD' and wdbz &lt;&gt; 'WDBZ_SS' and wdbz &lt;&gt; 'WDBZ_TC'
        and wdbz &lt;&gt; 'WDBZ_QX' and (cmsx isnull or cmsx &lt;&gt; 'CMSX_MX')
        <if test='yht == "2"'>
            and yhfs &lt;&gt; ''
        </if>
        <if test='yht == "1"'>
            and yhfs = ''
        </if>
    </select>

    <insert id="insertTqBillOtherInfo">
        INSERT INTO "public"."tq_bill_otherinfo"("billno", "userid", "bizid", "billtype", "ispv", "fwyh", "skjh", "bbrq", "create_time", "old_billno", "scbj")
        VALUES ( #{billno}, #{userid}, #{bizid}, '', 'Y', #{fwyh}, #{skjh}, #{bbrq}, now(), '', 0);
    </insert>

    <insert id="insertTqCommThirdOrder">
        INSERT INTO tq_commthirdorder( "billid", "createtime", "updatetime", "bbrq", "datatype", "dataname", "scan_code", "paytypeid", "orderno", "amount", "first_pay_status", "last_pay_status", "pay_status", "errcount", "updatecount")
        VALUES (#{billid}, now(), now(),  #{bbrq}, 'PV', 'HYK',  #{scan_code},  #{paytypeid}, #{orderno}, #{amount}, '0', '0', '0', 0, 0);
    </insert>

    <insert id="insertTqMemberInfo">
        INSERT INTO "public"."tq_memberinfo"( "billid", "scancode", "datatype", "remark", "canvipprice", "balance", "is_usable", "credit", "cardno", "phone", "membername","is_pv")
        VALUES ( #{billid}, #{scan_code}, 'QIMAI', '' , 'Y', NULL, 0, NULL, #{cardno}, #{phone}, NULL,'1');
    </insert>

</mapper>
