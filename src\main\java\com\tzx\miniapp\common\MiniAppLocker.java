package com.tzx.miniapp.common;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.stereotype.Service;

/**
 * 号码锁
 * 
 * <AUTHOR>
 *
 */
@Service
public class MiniAppLocker {
	private static Map<String, Integer> numberMap = new ConcurrentHashMap<String, Integer>();

	public synchronized MiniAppData lockerByNum(String number, int operType) {
		// 创建返回数据对象
		MiniAppData data = new MiniAppData();
		// operType=1：表示申请锁定；非1：表示处理完成，需要解锁
		if (operType == 1) {
			// 检查是否正在处理
			if (numberMap.containsKey(number)) {
				// 正在处理，返回2
				data.setSuccess(false);
				data.setMsg("处理中，请稍后再试！");
				return data;
			}
			// 加锁完成
			numberMap.put(number, 1);
			data.setMsg("锁定成功！");
			data.setSuccess(true);
			return data;
		} else {
			// 删除已经处理完成账单，清除列表中的对应订单数据
			numberMap.remove(number);
			data.setSuccess(true);
			data.setMsg("解锁成功！");
			return data;
		}
	}
}
