package com.tzx.miniapp.rest.vo;

import javax.persistence.Entity;
import java.io.Serializable;
import java.math.BigDecimal;

@Entity
public class BtYdxm2 implements Serializable {

	private String yddh;
	private int xmid;
	private String xmbh;
	private String xmsx;
	private BigDecimal xmdj;
	private int xmsl;
	private int zkl;
	private BigDecimal totalprice;
	private String dwbh;
	private String kwbh;
	private BigDecimal cmje;
	private String tcbh;
	private int tcdch;
	private int fzsl;
	private BigDecimal fzje;
	private int dcxh;
	private String xmmc;
	private String yl3;
	private int ydxm2id;
	private int jjcydxm2id;
	private int top_item_id;
	private String yl4;
	private String xm1mc;
	private String yl5;
	private int yl2;

	// 新增一个字段，表示是否微生活活动，0：正常菜品，1：优惠赠送菜品，默认：0
	private int isactivity;

	public int getIsactivity() {
		return isactivity;
	}

	public void setIsactive(int isactivity) {
		this.isactivity = isactivity;
	}

	public String getYddh() {
		return yddh;
	}

	public void setYddh(String yddh) {
		this.yddh = yddh;
	}

	public String getXmbh() {
		return xmbh;
	}

	public void setXmbh(String xmbh) {
		this.xmbh = xmbh;
	}

	public String getXmsx() {
		return xmsx;
	}

	public void setXmsx(String xmsx) {
		this.xmsx = xmsx;
	}

	public BigDecimal getXmdj() {
		return xmdj;
	}

	public void setXmdj(BigDecimal xmdj) {
		this.xmdj = xmdj;
	}

	public int getXmsl() {
		return xmsl;
	}

	public void setXmsl(int xmsl) {
		this.xmsl = xmsl;
	}

	public int getZkl() {
		return zkl;
	}

	public void setZkl(int zkl) {
		this.zkl = zkl;
	}

	public BigDecimal getTotalprice() {
		return totalprice;
	}

	public void setTotalprice(BigDecimal totalprice) {
		this.totalprice = totalprice;
	}

	public String getDwbh() {
		return dwbh;
	}

	public void setDwbh(String dwbh) {
		this.dwbh = dwbh;
	}

	public String getKwbh() {
		return kwbh;
	}

	public void setKwbh(String kwbh) {
		this.kwbh = kwbh;
	}

	public BigDecimal getCmje() {
		return cmje;
	}

	public void setCmje(BigDecimal cmje) {
		this.cmje = cmje;
	}

	public String getTcbh() {
		return tcbh;
	}

	public void setTcbh(String tcbh) {
		this.tcbh = tcbh;
	}

	public int getTcdch() {
		return tcdch;
	}

	public void setTcdch(int tcdch) {
		this.tcdch = tcdch;
	}

	public int getFzsl() {
		return fzsl;
	}

	public void setFzsl(int fzsl) {
		this.fzsl = fzsl;
	}

	public BigDecimal getFzje() {
		return fzje;
	}

	public void setFzje(BigDecimal fzje) {
		this.fzje = fzje;
	}

	public int getDcxh() {
		return dcxh;
	}

	public void setDcxh(int dcxh) {
		this.dcxh = dcxh;
	}

	public String getXmmc() {
		return xmmc;
	}

	public void setXmmc(String xmmc) {
		this.xmmc = xmmc;
	}

	public int getXmid() {
		return xmid;
	}

	public void setXmid(int xmid) {
		this.xmid = xmid;
	}

	public String getYl3() {
		return yl3;
	}

	public void setYl3(String yl3) {
		this.yl3 = yl3;
	}

	public int getYdxm2id() {
		return ydxm2id;
	}

	public void setYdxm2id(int ydxm2id) {
		this.ydxm2id = ydxm2id;
	}

	public int getJjcydxm2id() {
		return jjcydxm2id;
	}

	public void setJjcydxm2id(int jjcydxm2id) {
		this.jjcydxm2id = jjcydxm2id;
	}

	public int getTop_item_id() {
		return top_item_id;
	}

	public void setTop_item_id(int top_item_id) {
		this.top_item_id = top_item_id;
	}

	public void setIsactivity(int isactivity) {
		this.isactivity = isactivity;
	}

	public String getYl4() {
		return yl4;
	}

	public void setYl4(String yl4) {
		this.yl4 = yl4;
	}

	public String getXm1mc() {
		return xm1mc;
	}

	public void setXm1mc(String xm1mc) {
		this.xm1mc = xm1mc;
	}

	public String getYl5() {
		return yl5;
	}

	public void setYl5(String yl5) {
		this.yl5 = yl5;
	}

	public int getYl2() {
		return yl2;
	}

	public void setYl2(int yl2) {
		this.yl2 = yl2;
	}
}
