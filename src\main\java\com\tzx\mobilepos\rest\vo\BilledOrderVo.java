package com.tzx.mobilepos.rest.vo;

import java.math.BigDecimal;
import java.util.List;

/**
 * 
* @ClassName: BilledOrderVo
* <AUTHOR>
* @date 2018-10-12
* @email <EMAIL>
* @Description: 已结账单实体类
 */
public class BilledOrderVo {

	private String orderNumber;		//单号
	private String actualAmount;	//实结金额
	private String flowNumber;		//流水单号
	private String orderTime;		//下单时间
	private String salesModel;		//销售模式
	
	private String zzbz;			//整单备注
	private String qch;				//取餐号
	private String zkje;			//折扣金额
	private BigDecimal fkje;		//应结金额
	private Integer sum;			//菜品总计
	List<BillItemVo> billItemVo;	//账单详情
	private String zdzt;// 账单状态
	private String jzsx;// 结账属性
	private String billid;// 第三方支付表账单编号
	private String qchTitle;//取餐号或桌台号 标题
	
	public BilledOrderVo() {
		super();
	}


	public BilledOrderVo(String orderNumber, String actualAmount,
			String flowNumber, String orderTime, String salesModel,
			String zzbz, String qch, String zkje, BigDecimal fkje,
			Integer sum, List<BillItemVo> billItemVo, String zdzt, String jzsx) {
		super();
		this.orderNumber = orderNumber;
		this.actualAmount = actualAmount;
		this.flowNumber = flowNumber;
		this.orderTime = orderTime;
		this.salesModel = salesModel;
		this.zzbz = zzbz;
		this.qch = qch;
		this.zkje = zkje;
		this.fkje = fkje;
		this.sum = sum;
		this.billItemVo = billItemVo;
	}



	public String getOrderNumber() {
		return orderNumber;
	}

	public void setOrderNumber(String orderNumber) {
		this.orderNumber = orderNumber;
	}

	public String getActualAmount() {
		return actualAmount;
	}

	public void setActualAmount(String actualAmount) {
		this.actualAmount = actualAmount;
	}

	public String getFlowNumber() {
		return flowNumber;
	}

	public void setFlowNumber(String flowNumber) {
		this.flowNumber = flowNumber;
	}

	public String getOrderTime() {
		return orderTime;
	}

	public void setOrderTime(String orderTime) {
		this.orderTime = orderTime;
	}

	public String getSalesModel() {
		return salesModel;
	}

	public void setSalesModel(String salesModel) {
		this.salesModel = salesModel;
	}

	public String getZzbz() {
		return zzbz;
	}

	public void setZzbz(String zzbz) {
		this.zzbz = zzbz;
	}

	public String getQch() {
		return qch;
	}

	public void setQch(String qch) {
		this.qch = qch;
	}

	public String getZkje() {
		return zkje;
	}

	public void setZkje(String zkje) {
		this.zkje = zkje;
	}

	public BigDecimal getFkje() {
		return fkje;
	}

	public void setFkje(BigDecimal fkje) {
		this.fkje = fkje;
	}

	public Integer getSum() {
		return sum;
	}

	public void setSum(Integer sum) {
		this.sum = sum;
	}

	public List<BillItemVo> getBillItemVo() {
		return billItemVo;
	}

	public void setBillItemVo(List<BillItemVo> billItemVo) {
		this.billItemVo = billItemVo;
	}

	public String getZdzt() {
		return zdzt;
	}

	public void setZdzt(String zdzt) {
		this.zdzt = zdzt;
	}

	public String getJzsx() {
		return jzsx;
	}

	public void setJzsx(String jzsx) {
		this.jzsx = jzsx;
	}
	
	public String getBillid() {
		return billid;
	}

	public void setBillid(String billid) {
		this.billid = billid;
	}


	public String getQchTitle() {
		return qchTitle;
	}


	public void setQchTitle(String qchTitle) {
		this.qchTitle = qchTitle;
	}
	
}
