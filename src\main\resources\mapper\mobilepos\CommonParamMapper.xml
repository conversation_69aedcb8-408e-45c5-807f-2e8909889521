<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.mobilepos.rest.mapper.MobilePosCommonParamMapper">
	<select id="findCommonParam" resultType="com.tzx.mobilepos.rest.model.TsGgcsk">
		select * from ts_ggcsk where sdbt in ('POS_CPMXSDSFSM', 'JZSFXSZP', 'MDYYMS', 'POS_MEMBER_TYPE')
	</select>
</mapper>
