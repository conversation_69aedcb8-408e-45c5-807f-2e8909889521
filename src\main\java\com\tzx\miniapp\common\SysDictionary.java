package com.tzx.miniapp.common;



public class SysDictionary
{
	/** 员工登录状态 */
	public static final String				OPT_STATE_YYDL					= "YYDL";
	public static final int SUCCESS =0;
	public static final int FAILURE =-1;
	public static final String LOGIN_SUCCESS ="登录成功";
	public static final String LOGIN_FAILURE_01 ="登录失败,检查用户名和密码是否正确";
	public static final String LOGIN_FAILURE_02 ="登录失败,系统异常";
	public static final String SYSTEM_ERROR = "系统内部错误";

	public static final String LOGOUT_SUCCESS ="退出成功";
	public static final String LOGOUT_FAILURE_01 ="退出失败,系统异常";

	/** 桌位相关 */
	public static final String FREE_NAME         ="空闲";
	public static final String BUSY_NAME         ="占用";
	public static final String CLEAN_NAME        ="清扫";

	/** 微生活品牌id */
	public static final String GGCS_WELCRM_BRAND_ID = "WLIFE_ORDER_BRAND_ID";
    /**微生活商户ID*/
	public static final String GGCS_WELCRM_MERCHANT_ID = "WLIFE_ORDER_TENANCY_ID";
	
	/**众赏接口地址*/
	public static final String GGCS_ZS_API = "ZS_API";
	/**众赏appid*/
	public static final String GGCS_ZS_APPID = "ZS_APPID";
}
