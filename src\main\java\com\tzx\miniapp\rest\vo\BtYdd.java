package com.tzx.miniapp.rest.vo;

import javax.persistence.Entity;
import java.io.Serializable;

@Entity
public class BtYdd implements Serializable {

	private String yddh;
	private double totalprice; // 实结金额
	private double yl1; // 账单金额
	private double yl2; // 优惠金额
	private double shop_rate; // 优惠金额
	private int ydrs;
	private int men;
	private int women;
	private int eldernum;
	private int childnum;
	private String shops_id;
	private String hybh;
	private String ydrq;
	private String ydbc;
	private String ddzt;
	private String ddsj;
	private String qdsj;
	private int channel;// 下单来源 “0”为门店下单；“1”为线上下单；默认为“0”。V1.1新增 paychannel
	private String kwxh;

	private String diningway;// 销售模式
	private String zlbh;// 桌位编号
	private String shrbh;// 取餐号

	private String lxr;// 联系人
	private String lxrdh;// 联系人电话
	private String invalid_desc;// 是否预约单
	private String mealtime;// 就餐时间/送达时间
	private String member_address; // 配送地址
	private String yl4; // 来源，这里固定XCX
	private String yl5; // 众赏小程序外卖标识 1堂食，2外送，3外卖
	private String bz;

	private String fail_type2; // 餐饮类型
	private String bill_num; // 账单编号
	private String mobile;

	private String horseman_name; // 会员类型 1：微生活，2：众赏，3：百福，4：企迈，5：冥晨

	public String getInvalid_desc() {
		return invalid_desc;
	}

	public void setInvalid_desc(String invalid_desc) {
		this.invalid_desc = invalid_desc;
	}

	public String getMealtime() {
		return mealtime;
	}

	public void setMealtime(String mealtime) {
		this.mealtime = mealtime;
	}

	public int getChannel() {
		return channel;
	}

	public void setChannel(int channel) {
		this.channel = channel;
	}

	public String getYddh() {
		return yddh;
	}

	public void setYddh(String yddh) {
		this.yddh = yddh;
	}

	public double getTotalprice() {
		return totalprice;
	}

	public void setTotalprice(double totalprice) {
		this.totalprice = totalprice;
	}

	public int getYdrs() {
		return ydrs;
	}

	public void setYdrs(int ydrs) {
		this.ydrs = ydrs;
	}

	public int getMen() {
		return men;
	}

	public void setMen(int men) {
		this.men = men;
	}

	public int getWomen() {
		return women;
	}

	public void setWomen(int women) {
		this.women = women;
	}

	public int getEldernum() {
		return eldernum;
	}

	public void setEldernum(int eldernum) {
		this.eldernum = eldernum;
	}

	public int getChildnum() {
		return childnum;
	}

	public void setChildnum(int childnum) {
		this.childnum = childnum;
	}

	public String getShops_id() {
		return shops_id;
	}

	public void setShops_id(String shops_id) {
		this.shops_id = shops_id;
	}

	public String getHybh() {
		return hybh;
	}

	public void setHybh(String hybh) {
		this.hybh = hybh;
	}

	public String getYdrq() {
		return ydrq;
	}

	public void setYdrq(String ydrq) {
		this.ydrq = ydrq;
	}

	public String getYdbc() {
		return ydbc;
	}

	public void setYdbc(String ydbc) {
		this.ydbc = ydbc;
	}

	public String getDdzt() {
		return ddzt;
	}

	public void setDdzt(String ddzt) {
		this.ddzt = ddzt;
	}

	public String getDdsj() {
		return ddsj;
	}

	public void setDdsj(String ddsj) {
		this.ddsj = ddsj;
	}

	public String getQdsj() {
		return qdsj;
	}

	public void setQdsj(String qdsj) {
		this.qdsj = qdsj;
	}

	public String getKwxh() {
		return kwxh;
	}

	public void setKwxh(String kwxh) {
		this.kwxh = kwxh;
	}

	public String getLxr() {
		return lxr;
	}

	public void setLxr(String lxr) {
		this.lxr = lxr;
	}

	public String getLxrdh() {
		return lxrdh;
	}

	public void setLxrdh(String lxrdh) {
		this.lxrdh = lxrdh;
	}

	public String getDiningway() {
		return diningway;
	}

	public void setDiningway(String diningway) {
		this.diningway = diningway;
	}

	public String getZlbh() {
		return zlbh;
	}

	public void setZlbh(String zlbh) {
		this.zlbh = zlbh;
	}

	public String getShrbh() {
		return shrbh;
	}

	public void setShrbh(String shrbh) {
		this.shrbh = shrbh;
	}

	public String getMember_address() {
		return member_address;
	}

	public void setMember_address(String member_address) {
		this.member_address = member_address;
	}

	public String getYl4() {
		return yl4;
	}

	public void setYl4(String yl4) {
		this.yl4 = yl4;
	}

	public String getYl5() {
		return yl5;
	}

	public void setYl5(String yl5) {
		this.yl5 = yl5;
	}

	public String getBz() {
		return bz;
	}

	public void setBz(String bz) {
		this.bz = bz;
	}

	public double getYl1() {
		return yl1;
	}

	public void setYl1(double yl1) {
		this.yl1 = yl1;
	}

	public double getYl2() {
		return yl2;
	}

	public void setYl2(double yl2) {
		this.yl2 = yl2;
	}

	public double getShop_rate() {
		return shop_rate;
	}

	public void setShop_rate(double shop_rate) {
		this.shop_rate = shop_rate;
	}

	public String getFail_type2() {
		return fail_type2;
	}

	public void setFail_type2(String fail_type2) {
		this.fail_type2 = fail_type2;
	}

	public String getBill_num() {
		return bill_num;
	}

	public void setBill_num(String bill_num) {
		this.bill_num = bill_num;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getHorseman_name() {
		return horseman_name;
	}

	public void setHorseman_name(String horseman_name) {
		this.horseman_name = horseman_name;
	}
}
