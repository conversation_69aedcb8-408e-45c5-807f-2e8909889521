package com.tzx.mobilepos.common;

import com.tzx.publics.common.BaseData;
import net.sf.json.JSONObject;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019-04-18
 * @Descption
 **/
public class Data extends BaseData {
    private long t;
    private Oper oper;
    private Type type;
    private String tenancy_id;
    private String store_id;

    private String organ_code;
    private boolean success;



    public Data() {
        t = System.currentTimeMillis();
//        source = "APP";
//        msg = "";
        super.setSource("APP");
        super.setMsg("");
    }

    public String getOrgan_code() {
        return organ_code;
    }

    public void setOrgan_code(String organ_code) {
        this.organ_code = organ_code;
    }

    public long getT() {
        return t;
    }

    public void setT(long t) {
        this.t = t;
    }

    public Oper getOper() {
        return oper;
    }

    public void setOper(Oper oper) {
        this.oper = oper;
    }

    public Type getType() {
        return type;
    }

    public void setType(Type type) {
        this.type = type;
    }

    public String getTenancy_id() {
        return tenancy_id;
    }

    public void setTenancy_id(String tenancy_id) {
        this.tenancy_id = tenancy_id;
    }

    public String getStore_id() {
        return store_id;
    }

    public void setStore_id(String store_id) {
        this.store_id = store_id;
    }


    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }




}
