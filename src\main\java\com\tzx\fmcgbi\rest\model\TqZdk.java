package com.tzx.fmcgbi.rest.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @since 2020-01-09
 */
@Table(name = "TQ_ZDK")
public class TqZdk implements Serializable {
	private static final long serialVersionUID = 1L;

	@Column(name = "KDZDBH")
	private String kdzdbh;//开单账单编号
	@Column(name = "JZZDBH")
	private String jzzdbh;//结账账单编号
	@Column(name = "PCBH")
	private String pcbh;
	@Column(name = "KDLXBH")
	private String kdlxbh;
	@Column(name = "LSDH")
	private String lsdh;//流水单号
	@Column(name = "KDBBRQ")
	private Date kdbbrq;//开单报表日期
	@Column(name = "JZBBRQ")
	private Date jzbbrq;//结账报表日期
	@Column(name = "CBID")
	private int cbid;
	@Column(name = "ZWBH")
	private String zwbh;
	@Column(name = "XFKS")
	private int xfks;
	@Column(name = "KTSJ")
	private Date ktsj;//开台时间
	@Column(name = "JZSJ")
	private Date jzsj;//结账时间
	@Column(name = "JZCS")
	private int jzcs;//结账次数
	@Column(name = "KTSKJH")
	private String ktskjh;//开台收款机号
	@Column(name = "JZSKJH")
	private String jzskjh;//结账收款机号
	@Column(name = "FWYH")
	private String fwyh;//服务员号
	@Column(name = "KTCZRY")
	private String ktczry;//开台操作人员
	@Column(name = "JZCZRY")
	private String jzczry;//结账操作人员
	@Column(name = "KTBCID")
	private int ktbcid;//开台班次id
	@Column(name = "JZBCID")
	private int jzbcid;
	@Column(name = "NLD")
	private String nld;
	@Column(name = "FWFZID")
	private int fwfzid;
	@Column(name = "FWFBH")
	private String fwfbh;
	@Column(name = "FWFJE")
	private double fwfje;
	@Column(name = "FWFZKL")
	private int fwfzkl;
	@Column(name = "ZDXFZT")
	private String zdxfzt;
	@Column(name = "YDDH")
	private String yddh;
	@Column(name = "KDXDH")
	private String kdxdh;
	@Column(name = "YDSJ")
	private Date ydsj;
	@Column(name = "DYZDCS")
	private int dyzdcs;
	@Column(name = "YJJE")
	private double yjje;
	@Column(name = "XMXJJE")
	private double xmxjje;
	@Column(name = "ZDJE")
	private double zdje;
	@Column(name = "FKJE")
	private double fkje;
	@Column(name = "FKCE")
	private double fkce;
	@Column(name = "ZKJE")
	private double zkje;
	@Column(name = "ZRJE")
	private double zrje;
	@Column(name = "MLJE")
	private double mlje;
	@Column(name = "DPZKJE")
	private double dpzkje;
	@Column(name = "YHJE")
	private double yhje;
	@Column(name = "FSJE")
	private double fsje;
	@Column(name = "DSLJ")
	private double dslj;
	@Column(name = "MDJE")
	private double mdje;
	@Column(name = "YHCZRY")
	private String yhczry;
	@Column(name = "ZKL")
	private int zkl;
	@Column(name = "ZDZKL")
	private int zdzkl;
	@Column(name = "ZKFAID")
	private int zkfaid;
	@Column(name = "ZKFABH")
	private String zkfabh;
	@Column(name = "MDYYID")
	private int mdyyid;
	@Column(name = "MDYYBH")
	private String mdyybh;
	@Column(name = "YHFSID")
	private int yhfsid;
	@Column(name = "YHFSBH")
	private String yhfsbh;
	@Column(name = "ZZBZ")
	private String zzbz;
	@Column(name = "ZDBZ")
	private String zdbz;
	@Column(name = "XSMS")
	private String xsms;
	@Column(name = "CWLXBH")
	private String cwlxbh;
	@Column(name = "CWLXMC")
	private String cwlxmc;
	@Column(name = "ZDZT")
	private String zdzt;
	@Column(name = "YCLXID")
	private int yclxid;
	@Column(name = "GKQTID")
	private int gkqtid;
	@Column(name = "ZDYLXID")
	private int zdylxid;
	@Column(name = "LRRS")
	private int lrrs;
	@Column(name = "ETRS")
	private int etrs;
	@Column(name = "WOAMEN")
	private int woamen;
	@Column(name = "MEN")
	private int men;
	@Column(name = "JZSX")
	private String jzsx;
	@Column(name = "SCBJ")
	private int scbj;
	@Column(name = "TRANSFLAG")
	private String transflag;
	@Column(name = "ZCCS")
	private int zccs;
	@Column(name = "FZZDBH")
	private String fzzdbh;
	@Column(name = "KSJZSJ")
	private Date ksjzsj;
	@Column(name = "KSDCSJ")
	private Date ksdcsj;
	@Column(name = "JZJSSJ")
	private Date jzjssj;
	@Column(name = "BCQRSJ")
	private Date bcqrsj;
	@Column(name = "SOURCE")
	private String source;
	@Column(name = "YGDLCS")
	private String ygdlcs;
	@Column(name = "CPBJ")
	private int cpbj;
	@Column(name = "YL1")
	private String yl1;
	@Column(name = "YL2")
	private String yl2;
	@Column(name = "YL3")
	private String yl3;
	@Column(name = "SFKFP")
	private String sfkfp;
	@Column(name = "KFPJE")
	private double kfpje;
	@Column(name = "KFPSQM")
	private String kfpsqm;
	@Column(name = "ISSELPRINTEINVOICE")
	private String isselprinteinvoice;
	@Column(name = "SHORTKFPSQM")
	private String shortkfpsqm;
	@Column(name = "PSFJE")
	private double psfje;
	@Column(name = "YHYHJE")
	private double yhyhje;
	@Column(name = "PTYHJE")
	private double ptyhje;
	@Column(name = "PYYJJE")
	private double pyyjje;
	@Column(name = "YHFKJE")
	private double yhfkje;

	public String getKdzdbh() {
		return kdzdbh;
	}

	public void setKdzdbh(String kdzdbh) {
		this.kdzdbh = kdzdbh;
	}

	public String getJzzdbh() {
		return jzzdbh;
	}

	public void setJzzdbh(String jzzdbh) {
		this.jzzdbh = jzzdbh;
	}

	public String getPcbh() {
		return pcbh;
	}

	public void setPcbh(String pcbh) {
		this.pcbh = pcbh;
	}

	public String getKdlxbh() {
		return kdlxbh;
	}

	public void setKdlxbh(String kdlxbh) {
		this.kdlxbh = kdlxbh;
	}

	public String getLsdh() {
		return lsdh;
	}

	public void setLsdh(String lsdh) {
		this.lsdh = lsdh;
	}

	public Date getKdbbrq() {
		return kdbbrq;
	}

	public void setKdbbrq(Date kdbbrq) {
		this.kdbbrq = kdbbrq;
	}

	public Date getJzbbrq() {
		return jzbbrq;
	}

	public void setJzbbrq(Date jzbbrq) {
		this.jzbbrq = jzbbrq;
	}

	public int getCbid() {
		return cbid;
	}

	public void setCbid(int cbid) {
		this.cbid = cbid;
	}

	public String getZwbh() {
		return zwbh;
	}

	public void setZwbh(String zwbh) {
		this.zwbh = zwbh;
	}

	public int getXfks() {
		return xfks;
	}

	public void setXfks(int xfks) {
		this.xfks = xfks;
	}

	public Date getKtsj() {
		return ktsj;
	}

	public void setKtsj(Date ktsj) {
		this.ktsj = ktsj;
	}

	public Date getJzsj() {
		return jzsj;
	}

	public void setJzsj(Date jzsj) {
		this.jzsj = jzsj;
	}

	public int getJzcs() {
		return jzcs;
	}

	public void setJzcs(int jzcs) {
		this.jzcs = jzcs;
	}

	public String getKtskjh() {
		return ktskjh;
	}

	public void setKtskjh(String ktskjh) {
		this.ktskjh = ktskjh;
	}

	public String getJzskjh() {
		return jzskjh;
	}

	public void setJzskjh(String jzskjh) {
		this.jzskjh = jzskjh;
	}

	public String getFwyh() {
		return fwyh;
	}

	public void setFwyh(String fwyh) {
		this.fwyh = fwyh;
	}

	public String getKtczry() {
		return ktczry;
	}

	public void setKtczry(String ktczry) {
		this.ktczry = ktczry;
	}

	public String getJzczry() {
		return jzczry;
	}

	public void setJzczry(String jzczry) {
		this.jzczry = jzczry;
	}

	public int getKtbcid() {
		return ktbcid;
	}

	public void setKtbcid(int ktbcid) {
		this.ktbcid = ktbcid;
	}

	public int getJzbcid() {
		return jzbcid;
	}

	public void setJzbcid(int jzbcid) {
		this.jzbcid = jzbcid;
	}

	public String getNld() {
		return nld;
	}

	public void setNld(String nld) {
		this.nld = nld;
	}

	public int getFwfzid() {
		return fwfzid;
	}

	public void setFwfzid(int fwfzid) {
		this.fwfzid = fwfzid;
	}

	public String getFwfbh() {
		return fwfbh;
	}

	public void setFwfbh(String fwfbh) {
		this.fwfbh = fwfbh;
	}

	public double getFwfje() {
		return fwfje;
	}

	public void setFwfje(double fwfje) {
		this.fwfje = fwfje;
	}

	public int getFwfzkl() {
		return fwfzkl;
	}

	public void setFwfzkl(int fwfzkl) {
		this.fwfzkl = fwfzkl;
	}

	public String getZdxfzt() {
		return zdxfzt;
	}

	public void setZdxfzt(String zdxfzt) {
		this.zdxfzt = zdxfzt;
	}

	public String getYddh() {
		return yddh;
	}

	public void setYddh(String yddh) {
		this.yddh = yddh;
	}

	public String getKdxdh() {
		return kdxdh;
	}

	public void setKdxdh(String kdxdh) {
		this.kdxdh = kdxdh;
	}

	public Date getYdsj() {
		return ydsj;
	}

	public void setYdsj(Date ydsj) {
		this.ydsj = ydsj;
	}

	public int getDyzdcs() {
		return dyzdcs;
	}

	public void setDyzdcs(int dyzdcs) {
		this.dyzdcs = dyzdcs;
	}

	public double getYjje() {
		return yjje;
	}

	public void setYjje(double yjje) {
		this.yjje = yjje;
	}

	public double getXmxjje() {
		return xmxjje;
	}

	public void setXmxjje(double xmxjje) {
		this.xmxjje = xmxjje;
	}

	public double getZdje() {
		return zdje;
	}

	public void setZdje(double zdje) {
		this.zdje = zdje;
	}

	public double getFkje() {
		return fkje;
	}

	public void setFkje(double fkje) {
		this.fkje = fkje;
	}

	public double getFkce() {
		return fkce;
	}

	public void setFkce(double fkce) {
		this.fkce = fkce;
	}

	public double getZkje() {
		return zkje;
	}

	public void setZkje(double zkje) {
		this.zkje = zkje;
	}

	public double getZrje() {
		return zrje;
	}

	public void setZrje(double zrje) {
		this.zrje = zrje;
	}

	public double getMlje() {
		return mlje;
	}

	public void setMlje(double mlje) {
		this.mlje = mlje;
	}

	public double getDpzkje() {
		return dpzkje;
	}

	public void setDpzkje(double dpzkje) {
		this.dpzkje = dpzkje;
	}

	public double getYhje() {
		return yhje;
	}

	public void setYhje(double yhje) {
		this.yhje = yhje;
	}

	public double getFsje() {
		return fsje;
	}

	public void setFsje(double fsje) {
		this.fsje = fsje;
	}

	public double getDslj() {
		return dslj;
	}

	public void setDslj(double dslj) {
		this.dslj = dslj;
	}

	public double getMdje() {
		return mdje;
	}

	public void setMdje(double mdje) {
		this.mdje = mdje;
	}

	public String getYhczry() {
		return yhczry;
	}

	public void setYhczry(String yhczry) {
		this.yhczry = yhczry;
	}

	public int getZkl() {
		return zkl;
	}

	public void setZkl(int zkl) {
		this.zkl = zkl;
	}

	public int getZdzkl() {
		return zdzkl;
	}

	public void setZdzkl(int zdzkl) {
		this.zdzkl = zdzkl;
	}

	public int getZkfaid() {
		return zkfaid;
	}

	public void setZkfaid(int zkfaid) {
		this.zkfaid = zkfaid;
	}

	public String getZkfabh() {
		return zkfabh;
	}

	public void setZkfabh(String zkfabh) {
		this.zkfabh = zkfabh;
	}

	public int getMdyyid() {
		return mdyyid;
	}

	public void setMdyyid(int mdyyid) {
		this.mdyyid = mdyyid;
	}

	public String getMdyybh() {
		return mdyybh;
	}

	public void setMdyybh(String mdyybh) {
		this.mdyybh = mdyybh;
	}

	public int getYhfsid() {
		return yhfsid;
	}

	public void setYhfsid(int yhfsid) {
		this.yhfsid = yhfsid;
	}

	public String getYhfsbh() {
		return yhfsbh;
	}

	public void setYhfsbh(String yhfsbh) {
		this.yhfsbh = yhfsbh;
	}

	public String getZzbz() {
		return zzbz;
	}

	public void setZzbz(String zzbz) {
		this.zzbz = zzbz;
	}

	public String getZdbz() {
		return zdbz;
	}

	public void setZdbz(String zdbz) {
		this.zdbz = zdbz;
	}

	public String getXsms() {
		return xsms;
	}

	public void setXsms(String xsms) {
		this.xsms = xsms;
	}

	public String getCwlxbh() {
		return cwlxbh;
	}

	public void setCwlxbh(String cwlxbh) {
		this.cwlxbh = cwlxbh;
	}

	public String getCwlxmc() {
		return cwlxmc;
	}

	public void setCwlxmc(String cwlxmc) {
		this.cwlxmc = cwlxmc;
	}

	public String getZdzt() {
		return zdzt;
	}

	public void setZdzt(String zdzt) {
		this.zdzt = zdzt;
	}

	public int getYclxid() {
		return yclxid;
	}

	public void setYclxid(int yclxid) {
		this.yclxid = yclxid;
	}

	public int getGkqtid() {
		return gkqtid;
	}

	public void setGkqtid(int gkqtid) {
		this.gkqtid = gkqtid;
	}

	public int getZdylxid() {
		return zdylxid;
	}

	public void setZdylxid(int zdylxid) {
		this.zdylxid = zdylxid;
	}

	public int getLrrs() {
		return lrrs;
	}

	public void setLrrs(int lrrs) {
		this.lrrs = lrrs;
	}

	public int getEtrs() {
		return etrs;
	}

	public void setEtrs(int etrs) {
		this.etrs = etrs;
	}

	public int getWoamen() {
		return woamen;
	}

	public void setWoamen(int woamen) {
		this.woamen = woamen;
	}

	public int getMen() {
		return men;
	}

	public void setMen(int men) {
		this.men = men;
	}

	public String getJzsx() {
		return jzsx;
	}

	public void setJzsx(String jzsx) {
		this.jzsx = jzsx;
	}

	public int getScbj() {
		return scbj;
	}

	public void setScbj(int scbj) {
		this.scbj = scbj;
	}

	public String getTransflag() {
		return transflag;
	}

	public void setTransflag(String transflag) {
		this.transflag = transflag;
	}

	public int getZccs() {
		return zccs;
	}

	public void setZccs(int zccs) {
		this.zccs = zccs;
	}

	public String getFzzdbh() {
		return fzzdbh;
	}

	public void setFzzdbh(String fzzdbh) {
		this.fzzdbh = fzzdbh;
	}

	public Date getKsjzsj() {
		return ksjzsj;
	}

	public void setKsjzsj(Date ksjzsj) {
		this.ksjzsj = ksjzsj;
	}

	public Date getKsdcsj() {
		return ksdcsj;
	}

	public void setKsdcsj(Date ksdcsj) {
		this.ksdcsj = ksdcsj;
	}

	public Date getJzjssj() {
		return jzjssj;
	}

	public void setJzjssj(Date jzjssj) {
		this.jzjssj = jzjssj;
	}

	public Date getBcqrsj() {
		return bcqrsj;
	}

	public void setBcqrsj(Date bcqrsj) {
		this.bcqrsj = bcqrsj;
	}

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public String getYgdlcs() {
		return ygdlcs;
	}

	public void setYgdlcs(String ygdlcs) {
		this.ygdlcs = ygdlcs;
	}

	public int getCpbj() {
		return cpbj;
	}

	public void setCpbj(int cpbj) {
		this.cpbj = cpbj;
	}

	public String getYl1() {
		return yl1;
	}

	public void setYl1(String yl1) {
		this.yl1 = yl1;
	}

	public String getYl2() {
		return yl2;
	}

	public void setYl2(String yl2) {
		this.yl2 = yl2;
	}

	public String getYl3() {
		return yl3;
	}

	public void setYl3(String yl3) {
		this.yl3 = yl3;
	}

	public String getSfkfp() {
		return sfkfp;
	}

	public void setSfkfp(String sfkfp) {
		this.sfkfp = sfkfp;
	}

	public double getKfpje() {
		return kfpje;
	}

	public void setKfpje(double kfpje) {
		this.kfpje = kfpje;
	}

	public String getKfpsqm() {
		return kfpsqm;
	}

	public void setKfpsqm(String kfpsqm) {
		this.kfpsqm = kfpsqm;
	}

	public String getIsselprinteinvoice() {
		return isselprinteinvoice;
	}

	public void setIsselprinteinvoice(String isselprinteinvoice) {
		this.isselprinteinvoice = isselprinteinvoice;
	}

	public String getShortkfpsqm() {
		return shortkfpsqm;
	}

	public void setShortkfpsqm(String shortkfpsqm) {
		this.shortkfpsqm = shortkfpsqm;
	}

	public double getPsfje() {
		return psfje;
	}

	public void setPsfje(double psfje) {
		this.psfje = psfje;
	}

	public double getYhyhje() {
		return yhyhje;
	}

	public void setYhyhje(double yhyhje) {
		this.yhyhje = yhyhje;
	}

	public double getPtyhje() {
		return ptyhje;
	}

	public void setPtyhje(double ptyhje) {
		this.ptyhje = ptyhje;
	}

	public double getPyyjje() {
		return pyyjje;
	}

	public void setPyyjje(double pyyjje) {
		this.pyyjje = pyyjje;
	}

	public double getYhfkje() {
		return yhfkje;
	}

	public void setYhfkje(double yhfkje) {
		this.yhfkje = yhfkje;
	}

}
