package com.tzx.receiver.dao;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.jdbc.support.rowset.SqlRowSet;

import com.tzx.receiver.common.msg.MsgSqlUtil;

import java.util.Map;

@Repository
public class MsgBaseDao {
	@Autowired
	protected JdbcTemplate		jdbcTemplate;
	
	public JdbcTemplate getJdbcTemplate()
	{
		return jdbcTemplate;
	}
	
	public void truncat(String tbnames){
		String[] tbname = tbnames.split(",");
		for (int i = 0; i < tbname.length; i++){
			String sql = MsgSqlUtil.getTruncatSql(tbname[i]);
			jdbcTemplate.execute(sql);
			//System.out.println(sql);
		}
	}
	
	public boolean find(String tbnames, Object object, String where){
		boolean ret = false;
		String sql = "";
		SqlRowSet rs = null;
		String[] tbname = tbnames.split(",");
		for (int i = 0; i < tbname.length; i++){
			sql = MsgSqlUtil.getFindSql(tbname[i], object, where);
			rs = jdbcTemplate.queryForRowSet(sql);
			if (rs.next()){
				return true;
			}
		}
		return ret;
	}
	
	public void insert(String tbnames, Object object){
		String[] tabname = tbnames.split(",");
		for (int i = 0; i < tabname.length; i++) {
			String sql = MsgSqlUtil.getInsertSql(tabname[i], object);
			if (sql != null && sql.length() > 0) {
				jdbcTemplate.execute(sql);
				//System.out.println(sql);
			}
		}
	}
		
	public void update( String tbnames, Object object,String where){
		String[] tabname = tbnames.split(",");
		for (int i = 0; i < tabname.length; i++) {
			String sql = MsgSqlUtil.getUpdateSql(tabname[i], object, where);
			if (sql != null && sql.length() > 0) {
				jdbcTemplate.update(sql);
				//System.out.println(sql);
			}
		}
	}
	
	public void delete(String tbnames, Object object,String where){
		String[] tabname = tbnames.split(",");
		for (int i = 0; i < tabname.length; i++) {
			String sql = MsgSqlUtil.getDeleteSql(tabname[i], object, where);
			if (sql != null && sql.length() > 0) {
				jdbcTemplate.execute(sql);
				//System.out.println(sql);
			}
		}
	}
	
	public String queryOrganNo(){
		String organNo = null;
		SqlRowSet rs = null;
		String sql = " select sdnr from ts_ggcsk where sdbt = 'FDJGBH' ";
		
		rs = jdbcTemplate.queryForRowSet(sql);
		if (rs.next()){
			organNo = rs.getString("sdnr");
		}
		return organNo;
	}

	public void insertXfVerLog(Map<String,Object> params){
		String sql = "insert into tq_xfverlog(count,createtime,dbName,url,zlbh,jgxh,versionsTime,status)"+
					" values (?,now(),?, ?, ?,?, ?, ?)";
		jdbcTemplate.update(sql, new Object[]{params.get("count"),params.get("dbName"),params.get("url"),params.get("zlbh")
		,params.get("jgxh"),params.get("versionsTime"),"init"});
	}

	public void insertXfMsgLog(Map<String,Object> params){
		String sql = "insert into tq_xfmsglog(count,createtime,dbName,zlbh,jgxh,versionsTime,status)"+
				" values (?,now(),?, ?, ?,?, ?)";
		jdbcTemplate.update(sql, new Object[]{params.get("count"),params.get("dbName"),params.get("zlbh")
				,params.get("jgxh"),params.get("versionsTime"),"init"});
	}
	
}
