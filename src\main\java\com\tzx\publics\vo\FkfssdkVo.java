package com.tzx.publics.vo;

import com.tzx.publics.base.BaseEntity;

import javax.persistence.Table;
import java.io.Serializable;

/**
 * <AUTHOR> @since 2020-01-10
 */
@Table(name = "TS_FKFSSDK")
public class FkfssdkVo extends BaseEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	private int id;
	private int fkfzid;
	private String fkfsbh;
	private String fkfsmc1;
	private String fkfsmc2;
	private String fklxsx;
	private int pxbh;
	private String sfyx;
	private String sfsr;
	private String sfbb;
	private String sfkfp;
	private String sffq;
	private String sfxtfkfs;
	private String hl;
	private String sfjf;
	private double syye;
	private double fkme;
	private String cwkmbh;
	private String cwkmmc;
	private String memo;
	private String yl1;
	private String yl2;
	private String yl3;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public int getFkfzid() {
		return fkfzid;
	}

	public void setFkfzid(int fkfzid) {
		this.fkfzid = fkfzid;
	}

	public String getFkfsbh() {
		return fkfsbh;
	}

	public void setFkfsbh(String fkfsbh) {
		this.fkfsbh = fkfsbh;
	}

	public String getFkfsmc1() {
		return fkfsmc1;
	}

	public void setFkfsmc1(String fkfsmc1) {
		this.fkfsmc1 = fkfsmc1;
	}

	public String getFkfsmc2() {
		return fkfsmc2;
	}

	public void setFkfsmc2(String fkfsmc2) {
		this.fkfsmc2 = fkfsmc2;
	}

	public String getFklxsx() {
		return fklxsx;
	}

	public void setFklxsx(String fklxsx) {
		this.fklxsx = fklxsx;
	}

	public int getPxbh() {
		return pxbh;
	}

	public void setPxbh(int pxbh) {
		this.pxbh = pxbh;
	}

	public String getSfyx() {
		return sfyx;
	}

	public void setSfyx(String sfyx) {
		this.sfyx = sfyx;
	}

	public String getSfsr() {
		return sfsr;
	}

	public void setSfsr(String sfsr) {
		this.sfsr = sfsr;
	}

	public String getSfbb() {
		return sfbb;
	}

	public void setSfbb(String sfbb) {
		this.sfbb = sfbb;
	}

	public String getSfkfp() {
		return sfkfp;
	}

	public void setSfkfp(String sfkfp) {
		this.sfkfp = sfkfp;
	}

	public String getSffq() {
		return sffq;
	}

	public void setSffq(String sffq) {
		this.sffq = sffq;
	}

	public String getSfxtfkfs() {
		return sfxtfkfs;
	}

	public void setSfxtfkfs(String sfxtfkfs) {
		this.sfxtfkfs = sfxtfkfs;
	}

	public String getHl() {
		return hl;
	}

	public void setHl(String hl) {
		this.hl = hl;
	}

	public String getSfjf() {
		return sfjf;
	}

	public void setSfjf(String sfjf) {
		this.sfjf = sfjf;
	}

	public double getSyye() {
		return syye;
	}

	public void setSyye(double syye) {
		this.syye = syye;
	}

	public double getFkme() {
		return fkme;
	}

	public void setFkme(double fkme) {
		this.fkme = fkme;
	}

	public String getCwkmbh() {
		return cwkmbh;
	}

	public void setCwkmbh(String cwkmbh) {
		this.cwkmbh = cwkmbh;
	}

	public String getCwkmmc() {
		return cwkmmc;
	}

	public void setCwkmmc(String cwkmmc) {
		this.cwkmmc = cwkmmc;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public String getYl1() {
		return yl1;
	}

	public void setYl1(String yl1) {
		this.yl1 = yl1;
	}

	public String getYl2() {
		return yl2;
	}

	public void setYl2(String yl2) {
		this.yl2 = yl2;
	}

	public String getYl3() {
		return yl3;
	}

	public void setYl3(String yl3) {
		this.yl3 = yl3;
	}

}
