create or replace function p_zwyydtozd_xcx(syydh character varying, alsdh character varying, akdzdbh character varying, ajzzdbh character varying, abbbc integer, abbrq datetime, aczry character varying, askjh character varying, aygdlcs integer) returns integer
    language plpgsql
as
$$
DECLARE
    re_dd record; zdrecord record; re_ydd record;re_zdmx record;
    re_tcmxk record; tcfzmxk record; iCLMXID int; sZWZT varchar(10);
    sKDLXBH varchar(10);sXSMS varchar(10); sPCBH varchar(10); sNLD varchar(10);
    sKDXDH varchar(10); iYCLXID int; iGKQTID int; iZDYLXID int; sSource varchar(20);
    sJZZDBH varchar(20); iDCXH integer; re integer; icbid int; iCMID int; iwdcount int;
    irwid int; itcDCXH int; fTCJE numeric(19,4); fMXJE numeric(19,4);
    fYZZZT int;
    fXZZZT int;
    sCloudSource varchar(50);
    aMDYYMS varchar(50);
    sdnr varchar(200);
    sSourceBz varchar(1);
    inewsyyhfkfsid integer;
    iTotalYHJE numeric(19,4);
    ydsdkRec record;
    ifun integer;
    rfun rescalcmoney;
    res resPayment;
    fkfsRec record;
    sfkfsbh varchar(20);
    brt integer;
    re_discount record;
    iSingleYHJE numeric(19,4);
    iTotalYHJEM numeric(19,4);
    iFKJE numeric(19,4);
begin
    if not exists (select * from BT_YDD where YDDH = syydh) then
        return -100;
    end if;
    if exists (select kdzdbh from tq_zdk where yddh = syydh) then
        return -200;
    end if;
    if exists (select kdzdbh from tq_zdlsk where yddh = syydh) then
        return -200;
    end if;
    select a.sdnr into aMDYYMS from ts_ggcsk a where sdbt = 'MDYYMS';
    IF aMDYYMS <> '3' THEN
        abbbc := P_GetBCMC(CAST(now() as datetime));
    end if;
    if abbbc is null THEN
        abbbc = -1;
    end if;
    select COALESCE(max(to_number(ygdlcs, '99G999D9S')), '1') into aYGDLCS from tq_jtztk
    where BBRQ = abbrq and cznr = 'YYDL' and jhid = askjh;
    fYZZZT = 0;
    if exists (select ygdlcs from tq_jtztk where BBRQ = abbrq and cznr = 'YYTC'
                                             and jhid = askjh and to_number(ygdlcs, '99G999D9S') = aYGDLCS) then
        fYZZZT = 1;
    end if;
    if fYZZZT = 1 then
        return -800;
    end if;
    select COALESCE(ydrs, 0) ydrs, COALESCE(men, 0) men, COALESCE(WOMEN, 0)WOMEN,
           COALESCE(ELDERNUM, 0)ELDERNUM, COALESCE(CHILDNUM, 0)CHILDNUM, before_order_source,
           COALESCE(totalprice, 0) totalprice, 0 as yl3, COALESCE(shop_rate,0)  shop_rate,
           COALESCE(yl1,0) zdje,COALESCE(yl2,0) yl2,
           COALESCE(platform_rate,0) platform_rate,COALESCE(commission_amount,0) commission_amount,
           COALESCE(yl1,0)-COALESCE(yl2,0) yhfkje,yl4,kwxh,horseman_name,diningway,shrbh,zlbh,qdsj,fail_type2,invalid_desc,mealtime into re_ydd from BT_YDD where YDDH = syydh;
    sPCBH = ''; sKDLXBH = ''; sNLD = ''; sKDXDH = ''; iYCLXID = 0; iGKQTID = 0; iZDYLXID = 0; sXSMS = '';
    sSource = re_ydd.before_order_source;
    icbid = null;
    insert into tq_zdk(KDZDBH,LSDH,KDBBRQ,ZWBH,XFKS,KTSJ,JZCS,KTSKJH,FWYH,KTCZRY,KTBCID,NLD,DYZDCS,YJJE,XMXJJE,ZDJE,FKJE,FKCE,ZKJE,ZRJE,MLJE,DPZKJE,YHJE,FSJE,DSLJ,MDJE,ZKL,
                       XSMS,JZSX,SCBJ,CBID,PCBH,KDLXBH,KDXDH,YCLXID,GKQTID,ZDYLXID,MEN,WOAMEN,ETRS,LRRS,FWFZID,FWFBH,ZKFAID,ZKFABH,ZDZT,source,YDDH,qch,zdbz,cwlxbh,yl1,yl2)
    values(akdzdbh,alsdh,aBBRQ,re_ydd.zlbh, re_ydd.ydrs,now(),0,aSKJH,aczry,aczry,aBBBC,'',0,0,0,0,0,0,0,0,0,0,0,0,0,0,100,re_ydd.diningway,'ZDSX_WJ',0,
           null,'','','',0,0,0, re_ydd.men, re_ydd.WOMEN, re_ydd.CHILDNUM, re_ydd.ELDERNUM,
           null,'',null,'','',re_ydd.yl4,syydh,re_ydd.shrbh,re_ydd.kwxh,re_ydd.horseman_name,re_ydd.invalid_desc,re_ydd.mealtime);
    update tq_zdk set xmxjje=re_ydd.zdje,zdje=re_ydd.zdje,fkje=re_ydd.zdje,xsms=re_ydd.diningway,ygdlcs=aYGDLCS,
                      psfje=re_ydd.yl3 ,yhyhje=0,ptyhje=re_ydd.platform_rate,pyyjje=re_ydd.commission_amount,
                      yhfkje=re_ydd.yhfkje, ksdcsj=CAST(re_ydd.qdsj as datetime), mdyybh = re_ydd.fail_type2,
                      ksjzsj=CAST(re_ydd.qdsj as datetime), jzjssj=CAST(re_ydd.qdsj as datetime) where kdzdbh=akdzdbh;
    select ZKL, ZDZKL, zkfabh, KDBBRQ, KTBCID, ZWBH, KTSKJH, YHFSBH, ZDZT, JZSX into zdrecord
    FROM TQ_ZDK WHERE KDZDBH = akdzdbh;
    brt = 0;
    --校验菜品是否都在门店
    for re_dd in select b.cmid, b.cmsx, b.cmmc1, a.xmbh, a.xmsx, a.xmdj, a.xmsl, a.zkl, a.totalprice,
                        a.dwbh, a.kwbh, a.cmje, a.tcbh, a.tcdch, a.fzsl, a.fzje, a.dcxh, a.id,b.cmbh from bt_ydxm2 a
                                                                                                              left join ts_cmk b on a.xmbh = b.cmbh where yddh = sYYDH order by a.id asc
        loop
            if (re_dd.cmbh is null) or (re_dd.cmbh = '') then
                brt = 1;
                exit;
            END IF;
        end loop;
    if brt = 1 THEN
        RETURN -121;
    end if;
    select COALESCE(max(syyhfkfsid),0) into inewsyyhfkfsid from TQ_WDK where KDZDBH=akdzdbh;
    inewsyyhfkfsid := inewsyyhfkfsid + 1;
    for re_dd in select a.*,c.XLZKZT,b.XLID,b.XLBH,case COALESCE(isactivity,0) when  1 then 'Y' else 'N' end as sfzk ,b.cmmc2,b.CBJE from bt_ydxm2 a
                                                                                  left join ts_cmk b on a.xmbh = b.cmbh  left join TS_XLK c 	ON b.XLBH = c.XLBH where yddh = sYYDH order by a.id
        loop
            itcDCXH := iDCXH;
            insert into TQ_WDK(KDZDBH, CLMXID, CMID, CMBH, CMMC1,
                               CMMC2, DWBH, YZWBH, ZWBH, TCFS,
                               TCBL, TCJE, FZSL, FZJE, CMDJ,
                               ZDSJ, XDH, XDHSHRY, FWYH, CBDJ,
                               CMSL, CMJE, SJJE, YHJE, ZRJE,
                               DPZKJE, ZKZT, XLID, XLBH, XLZKZT,
                               ZKL, WDBZ, TMBJ, KWBZ, fsskjh,
                               YHFS, yhfsbh, YHFSID, CDBJ, TSZT,
                               QCZT, TCID, TCDCH, DCXH, CPBH,
                               DCBZ, ZWH, CSBH, FSBBRQ, FSBCID,
                               CMSX, CJGQBJ, YDXMID, SYYHFKFSID, SFXSMX,
                               YONGJJE, YONGJZKL)
            select akdzdbh, -1, re_dd.xmid, re_dd.xmbh, case when re_dd.xmsx ='CMSX_MX' then ' '||re_dd.xmmc else re_dd.xmmc end xmmc,
                   re_dd.CMMC2, re_dd.dwbh, '', '', re_dd.yl4,
                   0, 0, 0, 0, re_dd.xmdj,
                   0, '', '', aczry, re_dd.CBJE,
                   re_dd.xmsl, re_dd.cmje, re_dd.totalprice, 0, re_dd.yl3::numeric(19,4),
                   0, re_dd.sfzk, re_dd.XLID, re_dd.XLBH, re_dd.XLZKZT,
                   100, '', now(), re_dd.kwbh, askjh,
                   '', '', null, '*', 'N',
                   'N', re_dd.top_item_id, re_dd.tcdch, re_dd.dcxh, '',
                   '', '', '', zdrecord.KDBBRQ, zdrecord.KTBCID,
                   re_dd.xmsx, 0, re_dd.id,inewsyyhfkfsid,
                   'Y' ,0,100;
            inewsyyhfkfsid := inewsyyhfkfsid + 1;
        end loop;
    select COALESCE(max(DCXH), 0) into iDCXH from TQ_WDK where KDZDBH = akdzdbh;
    iDCXH := iDCXH + 1;
    select count(*) into iwdcount from tq_wdk where kdzdbh = akdzdbh;
    if iwdcount = 0 then
        delete from tq_zdk where kdzdbh = akdzdbh;
        return -400;
    end if;
    raise notice 're_ydd %',re_ydd;
    iTotalYHJE = re_ydd.shop_rate;
    if(iTotalYHJE > 0) then
        SELECT * INTO ydsdkRec FROM ts_yhfssdk WHERE yhsx = '6';
        IF NOT found THEN
            RETURN -104;
        END IF;
        iTotalYHJEM  = iTotalYHJE;
        iSingleYHJE  = iTotalYHJE;
        INSERT INTO TQ_WDK(KDZDBH, CLMXID, CMID, CMBH, CMMC1, CMMC2, DWBH, YZWBH, ZWBH, TCFS,
                           TCBL, TCJE, FZSL, FZJE, CMDJ, ZDSJ, XDH, XDHSHRY, FWYH, CBDJ,
                           CMSL, CMJE, SJJE, YHJE, ZRJE, DPZKJE, ZKZT, XLBH, XLZKZT, ZKL,
                           WDBZ, TMBJ, KWBZ, fsskjh, YHFS, yhfsbh, YHFSID, CDBJ, TSZT, QCZT,
                           TCID, TCDCH, DCXH, CPBH, DCBZ, ZWH, CSBH, FSBBRQ, FSBCID, fzdch,
                           syyhfkfsid, yongjzkl, yongjje, SCBJ, JZZDBH, JZBBRQ, JZBCID, JZSKJH,yhxh,ecodiscount_fee)
        SELECT akdzdbh, -1, NULL, 'YH' || ydsdkRec.YHFSBH, ydsdkRec.YHFSMC1, ydsdkRec.YHFSMC2, '', '', '', '',
               0, 0, 0, 0, 0, 0, '', '', '', 0,
               1, -iSingleYHJE, 0, 0, iSingleYHJE, 0, 'N', '', 'N', 100,
               '', now(), '', '99', ydsdkRec.yhsx, '1', ydsdkRec.id, '*', 'N', 'N',
               null, '0', iDCXH, '', '', '', '', aBBRQ, zdrecord.KTBCID, null,
               inewsyyhfkfsid, 100, 0, 0, null, aBBRQ, zdrecord.KTBCID, '99',0,iSingleYHJE;
        inewsyyhfkfsid = inewsyyhfkfsid + 1;
        UPDATE TQ_ZDK SET ZRJE = COALESCE(ZRJE, 0) + iTotalYHJEM
        WHERE KDZDBH = akdzdbh;
--         ifun = P_ZRTR(akdzdbh);
--         select * into rfun from  p_calcmoney(akdzdbh);
    end if;
    for re_discount in select * from bt_order_discount a where order_code = syydh order by a.id asc
        loop
            SELECT * INTO ydsdkRec FROM ts_yhfssdk WHERE yhfsbh = re_discount.activity_id;
            IF NOT found THEN
                RETURN -103;
            END IF;
            --插入优惠记录
            INSERT INTO TQ_WDK(KDZDBH, CLMXID, CMID, CMBH, CMMC1, CMMC2, DWBH, YZWBH, ZWBH, TCFS,
                               TCBL, TCJE, FZSL, FZJE, CMDJ, ZDSJ, XDH, XDHSHRY, FWYH, CBDJ,
                               CMSL, CMJE, SJJE, YHJE, ZRJE, DPZKJE, ZKZT, XLBH, XLZKZT, ZKL,
                               WDBZ, TMBJ, KWBZ, fsskjh, YHFS, yhfsbh, YHFSID, CDBJ, TSZT, QCZT,
                               TCID, TCDCH, DCXH, CPBH, DCBZ, ZWH, CSBH, FSBBRQ, FSBCID, fzdch,
                               syyhfkfsid, yongjzkl, yongjje, SCBJ, JZZDBH, JZBBRQ, JZBCID, JZSKJH,yhxh,ecodiscount_fee,rule_id)
            SELECT akdzdbh, -1, NULL, 'YH' || ydsdkRec.YHFSBH, re_discount.discount_desc, ydsdkRec.YHFSMC2, '', '', '', '',
                   0, 0, 0, 0, 0, 0, '', '', '', 0,
                   1, -re_discount.shop_rate, 0, 0, re_discount.shop_rate, 0, 'N', '', 'N', 100,
                   '', now(), '', '99', ydsdkRec.yhsx, '1', ydsdkRec.id, '*', 'N', 'N',
                   null, '0', iDCXH, '', '', '', '', aBBRQ, zdrecord.KTBCID, null,
                   inewsyyhfkfsid, 100, 0, 0, aJZZDBH, aBBRQ, zdrecord.KTBCID, '99',0,re_discount.discount_fee,re_discount.discount_type;
            inewsyyhfkfsid = inewsyyhfkfsid + 1;
            UPDATE TQ_ZDK SET ZRJE = COALESCE(ZRJE, 0) + re_discount.shop_rate WHERE KDZDBH = akdzdbh;
            --更新单品优惠金额
            UPDATE TQ_WDK SET DPZKJE = COALESCE(DPZKJE, 0) + re_discount.shop_rate,SJJE=COALESCE(SJJE, 0) - re_discount.shop_rate
            WHERE KDZDBH = akdzdbh and CMBH = re_discount.pay_no;

--             ifun = P_ZRTR(akdzdbh);
--             select * into rfun from  p_calcmoney(akdzdbh);
        end loop;
    select sum(cmje) cmje, sum(sjje)sjje into re_zdmx from tq_wdk
    where kdzdbh = akdzdbh and cmsx <> 'CMSX_MX';
--     if re_ydd.totalprice <> re_zdmx.sjje then
--         return -500;
--     end if;
--     select coalesce(sum(cmje), 0) into fTCJE from tq_wdk where kdzdbh = akdzdbh and cmsx = 'CMSX_TC';
--     select coalesce(sum(cmje), 0) into fMXJE from tq_wdk where kdzdbh = akdzdbh and cmsx = 'CMSX_MX';
--     if fTCJE <> fMXJE then
--         return -600;
--     end if;
    for fkfsRec in  SELECT a.*,b.pay_count,b.vtele,b.pay_memo FROM bt_payments b inner join ts_fkfssdk a
                                                                                            on a.fkfsbh = b.pay_no and b.yddh = syydh where  b.pay_no  is not null
        loop
            IF fkfsRec.fkfsbh is null THEN
                RETURN -105;
            END IF;
            select * into res from p_payment(akdzdbh, fkfsRec.id,fkfsRec.pay_count, 1,'', '', fkfsRec.vtele, fkfsRec.pay_memo, askjh, askjh);
            if res.a <> 0 THEN
                RETURN -106;
            end if;
        end loop;
--     select coalesce(sum(fkje), 0) into iFKJE from tq_fklslsk where kdzdbh = akdzdbh ;
--     if re_zdmx.sjje <> iFKJE then
--         return -700;
--     end if;
    sJZZDBH = ajzzdbh ;
    UPDATE TQ_ZDK set JZZDBH = sJZZDBH, JZBBRQ = abbrq, JZSKJH = aSKJH, JZCZRY = aCZRY, JZBCID = aBBBC,
                      JZSX = 'ZDSX_YJ', JZCS = JZCS + 1, JZSJ = now(), SCBJ = 0 where KDZDBH = akdzdbh;
    update tq_wdk set JZZDBH = sJZZDBH, JZBBRQ = aBBRQ, JZBCID = aBBBC, JZSKJH = aSKJH, scbj = 0 where KDZDBH = aKDZDBH;
    update tq_fklslsk set JZZDBH = sJZZDBH, JZBCID = aBBBC, scbj = 0 where KDZDBH = aKDZDBH;
    fXZZZT = 0;
    if exists (select ygdlcs from tq_jtztk where BBRQ = abbrq and cznr = 'YYTC'
                                             and jhid = askjh and to_number(ygdlcs, '99G999D9S') = aYGDLCS) then
        fXZZZT = 1;
    end if;
    if fYZZZT = 0 and fXZZZT = 1 THEN
        return -850;
    elseif not exists (select kdzdbh from tq_zdk where kdzdbh = akdzdbh) then
        return -875;
    end if;
    update tq_wdk set jjcrwid = rwid where kdzdbh = akdzdbh;
    return 0;
end $$;

alter function p_zwyydtozd_xcx(varchar, varchar, varchar, varchar, integer, datetime, varchar, varchar, integer) owner to postgres;

