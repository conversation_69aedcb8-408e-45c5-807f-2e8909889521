<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.miniapp.rest.mapper.MiniAppFirstPayMapper">
	
    <select id="getBillStatus" resultType="java.lang.Integer">
		SELECT COALESCE(SUM(BillStatus), 0) AS BillStatus FROM (SELECT CASE WHEN a.ddzt = '5' THEN 1 WHEN a.ddzt = '7' AND COALESCE(b.zdzt, 'NOBILL') = 'ZDZT_ZDQX' THEN 2 WHEN a.ddzt = '7' THEN 3 END AS BillStatus FROM bt_ydd a LEFT OUTER JOIN tq_zdk b ON a.bill_num = b.kdzdbh WHERE a.yddh = #{zdbh}) AS TempTable
	</select>

    <select id="getYdd" resultType="com.tzx.miniapp.rest.vo.BtYdd">
		select * from bt_ydd where yddh = #{zdbh};
	</select>

	<select id="calcYddPayActive" resultType="java.lang.Integer">
		select * from p_useyddpayactive (#{zdbh});
	</select>

    <insert id="insertBtYddPayActive">
		insert into bt_yddpayactive
		(yddh,pay_name,pay_money,couponcodes,active_type 
		<if test="active_id != null and active_id != 0">
			, active_id
		</if>
	    )
		values(#{yddh}, #{pay_name}, #{pay_money}, #{couponcodes}, #{active_type} 
		<if test="active_id != null and active_id != 0">
			, #{active_id}
		</if>
	    )
	</insert>

	<select id="selectMemberPrice" resultType="java.lang.Double">
		select coalesce(sum(pay_money), 0.00) from bt_yddpayactive where
		active_type = 'MEMBER' and yddh = #{zdbh};
	</select>

    <select id="getJtZtk" resultType="com.tzx.miniapp.rest.model.TqJtztk">
		select * from tq_jtztk where bbrq = #{bbrq} and jhid = '99' and cznr = 'YYDL' order by czsj desc limit 1
	</select>

    <select id="yddToZd" resultType="java.lang.Integer">
		select * from p_zwyydtozd(#{syydh},'', '', 0, #{abbrq}, #{aczry}, '99', #{aygdlcs})
	</select>
    <select id="getFkfs" resultType="com.tzx.miniapp.rest.model.TsFkfssdk">
		select a.* from (select a.*,case when a.FKFSMC2 is not null then regexp_split_to_table(a.FKFSMC2,',') end as fkfsmcs from TS_FKFSSDK a) a where a.fkfsmcs = #{fkfsmc2}
	</select>
    <select id="getFkfsByCode" resultType="com.tzx.miniapp.rest.model.TsFkfssdk">
		select * from TS_FKFSSDK where fkfsbh = #{fkfsbh}
	</select>
    <select id="accountsOrder" resultType="com.tzx.miniapp.rest.vo.AccountsOrder">
		select * from p_payment(#{szdbh}, #{ijzid}, #{ifkje}, #{ifksl}, #{sfkhm}, #{ssfzhm}, #{slxdh}, #{sfkbz}, #{sskjh}, #{sskyh});
	</select>

    <select id="getZdbhByYdd" resultType="com.tzx.miniapp.rest.model.TqZdk">
		select * from tq_zdk where yddh = #{yddbh} limit 1
	</select>
    <select id="getZdbhByZdbh" resultType="com.tzx.miniapp.rest.model.TqZdk">
		select * from tq_zdk where kdzdbh = #{zdbh}
	</select>
	
	<update id="updateZdk">
		update tq_zdk set ksjzsj = #{ksjzsj}, jzjssj = #{jzjssj}, source = #{source}, qch = #{qch}, jzsx = #{jzsx}, zwbh = #{zwbh} , cwlxbh = #{cwlxbh} 
		<if test="bcid != null and bcid != 0">
			, jzbcid = #{bcid}, ktbcid = #{bcid}
		</if>
		where kdzdbh = #{kdzdbh} 
	</update>
	
	<update id="updateZdkNoQch">
		update tq_zdk set ksjzsj = #{ksjzsj}, jzjssj = #{jzjssj}, source = #{source}, jzsx = #{jzsx}, zwbh = #{zwbh} , cwlxbh = #{cwlxbh} 
		<if test="bcid != null and bcid != 0">
			, jzbcid = #{bcid}, ktbcid = #{bcid} 
		</if>
		where kdzdbh = #{kdzdbh} 
	</update>

	<update id="updateFklslsk">
<!-- 		update tq_fklslsk set jzzdbh = #{jzzdbh} where kdzdbh = #{kdzdbh}  -->
		update tq_fklslsk set jzzdbh = #{jzzdbh} 
		<if test="bcid != null and bcid != 0">
			, jzbcid = #{bcid} 
		</if>
		where kdzdbh = #{kdzdbh}
	</update>
	
	<insert id="insertBtPayments" parameterType="com.tzx.miniapp.rest.vo.BtPayments">
		insert into bt_payments (
		yddh,pay_channel,pay_name,
		pay_no,vcardid,vtele,
		pay_count,pay_bb,vocount,
		fzzhje,flhl,pay_memo,opttime)
		values(
		#{btpay.yddh,jdbcType=VARCHAR},#{btpay.pay_channel,jdbcType=VARCHAR},#{btpay.pay_name,jdbcType=VARCHAR},
		#{btpay.pay_no,jdbcType=VARCHAR},#{btpay.vcardid,jdbcType=VARCHAR},#{btpay.vtele,jdbcType=VARCHAR},
		#{btpay.pay_count,jdbcType=NUMERIC},#{btpay.pay_bb,jdbcType=NUMERIC},#{btpay.vocount,jdbcType=NUMERIC},
		#{btpay.fzzhje,jdbcType=NUMERIC},#{btpay.flhl,jdbcType=NUMERIC},#{btpay.pay_memo,jdbcType=VARCHAR},#{btpay.opttime,jdbcType=VARCHAR})
	</insert>

    <select id="getKdzdbh" resultType="java.lang.String">
		select kdzdbh from tq_zdk where yddh = #{yddh}
	</select>

    <update id="updateBtYdd">
		update bt_ydd set bill_num = #{bill_num}, ddzt = #{ddzt}, 
		ddsj = #{ddsj}, shrbh = #{shrbh}, zlbh = #{zlbh}  where yddh = #{yddh}
	</update>

    <insert id="insertTqYyddylsk" parameterType="com.tzx.miniapp.rest.vo.TqYyddylsk">
		insert into tq_yyddylsk (fdjgxh,yddh,bbrq,dyzt,dyjmc,dylx,need_invoice,ddlx)
		values(
		#{yyddyls.fdjgxh,jdbcType=INTEGER},#{yyddyls.yddh,jdbcType=VARCHAR},#{yyddyls.bbrq,jdbcType=DATE},
		#{yyddyls.dyzt,jdbcType=INTEGER},#{yyddyls.dyjmc,jdbcType=VARCHAR},#{yyddyls.dylx,jdbcType=VARCHAR},
		#{yyddyls.need_invoice,jdbcType=VARCHAR},#{yyddyls.ddlx,jdbcType=VARCHAR})
	</insert>
    <update id="updateBtYddToCancel">
		update bt_ydd set ddzt = #{ddzt} where yddh = #{yddh}
	</update>
    <select id="getBh" resultType="com.tzx.miniapp.rest.model.TsBmkzk">
		SELECT * FROM TS_BMKZK WHERE BMC = #{bmc} and ZDMC = #{zdmc}
	</select>
    <select id="getGgcsToWs" resultType="com.tzx.miniapp.rest.model.TsGgcsk">
		select * from ts_ggcsk where sdbt = #{cdzd}
	</select>
    <select id="getGgcsToCsh" resultType="com.tzx.miniapp.rest.model.TsGgcsk">
		select * from ts_ggcsk where sdbt = #{cdzd}
	</select>
    <update id="updateBh">
		update TS_BMKZK set nr = #{nr} where BMC = #{bmc} and ZDMC = #{zdmc}
	</update>
    <select id="callP_SendKVSData" resultType="java.lang.String">
		select * from sendkvsdata(#{szdbh})
	</select>
    <select id="getZdbhByYddh" resultType="java.lang.String">
		select kdzdbh from tq_zdk where yddh = #{yddh}
	</select>
    <select id="addYhfs" resultType="java.lang.Integer">
		select * from p_addcm_app(#{szdbh}, #{aitemid}, #{ixmsl}, '99', '', '', '' ,'', 0)
	</select>
    <select id="calcmoney" resultType="java.lang.Integer">
		select * from p_calcmoney(#{szdbh})
	</select>
    <select id="findDiscounts" resultType="java.util.Map">
		select cl.id as itemid, cl.yhfsid from bt_ydxm1 yd left join tq_clmxk cl on yd.yl2 = cl.yhfsid where yd.yddh = #{yddh}
	</select>
    <update id="updateJjcrwid">
        update tq_wdk set jjcrwid = rwid
        <if test="bcid != null and bcid != 0">
            , fsbcid = #{bcid}, jzbcid = #{bcid}
        </if>
        where kdzdbh = #{kdzdbh}
    </update>
    <select id="getBcid" resultType="java.lang.Integer">
		select yl1 as bcid from tq_jtztk where BBRQ = #{bbrq} and jhid='99' and cznr = 'YYDL' ORDER BY cast(ygdlcs as int) desc LIMIT 1
	</select>
    <select id="getZdk" resultType="com.tzx.miniapp.rest.model.TqZdk">
		select * from tq_zdk where yddh = #{yddh}
	</select>
    <select id="calcAceWillActivity" resultType="java.lang.Integer">
    select * from p_useyddactive (#{zdbh});
  </select>
    <update id="updateYddActiveCouponInfo">
    update bt_yddactive set coupon_money = #{couponMoney}, coupon_code = #{couponCode}
    where yddh = #{yddh} and active_id = #{activeID} and buy = #{buyDish} and gift = #{giftDish}
  </update>
  
  <update id="updateZdkZs">
		update tq_zdk set jzzdbh = #{jzzdbh}, jzbbrq = #{jzbbrq}, jzsj = #{jzsj}, jzcs = #{jzcs}, jzskjh = #{jzskjh}, jzczry = #{jzczry}, jzsx = #{jzsx}, ksjzsj = #{ksjzsj}, jzjssj = #{jzjssj}, jzbcid = #{jzbcid}, xfks = #{xfks}, cwlxbh = #{cwlxbh} 
		<if test="zzbz != null and zzbz != ''">
			,zzbz = #{zzbz} 
		</if>
		where kdzdbh = #{kdzdbh} 
	</update>
	<update id="updateWdkZs">
		update tq_wdk set jzzdbh = #{jzzdbh}, jzbbrq = #{jzbbrq}, jzskjh = #{jzskjh}, jzbcid = #{jzbcid}, fsbcid = #{jzbcid}, jjcrwid = rwid where kdzdbh = #{kdzdbh} 
	</update>
	<update id="updateFklslskZs">
		update tq_fklslsk set jzzdbh = #{jzzdbh}, jzbcid = #{jzbcid} where kdzdbh = #{kdzdbh} 
	</update>
	
	<select id="zRtr" resultType="java.lang.Integer">
		select * from P_ZRTR(#{bill_num});
	</select>
	
	<select id="getFkfsByZs" resultType="com.tzx.miniapp.rest.model.TsFkfssdk">
		select a.* from (select a.*,case when a.FKFSMC2 is not null then regexp_split_to_table(a.FKFSMC2,',') end as fkfsmcs from TS_FKFSSDK a) a where 1 = 1
		<if test="fkfsmc2 != null and fkfsmc2 != ''">
			and a.fkfsmcs = #{fkfsmc2} 
		</if>
		<if test="yl3 != null and yl3 != ''">
			and a.yl3 = #{yl3} 
		</if>
		limit 1
	</select>
	
	<select id="getbillid" resultType="java.lang.String"  parameterType="map" statementType="CALLABLE"  useCache="false">
		select * from P_GetBillID(#{skjh},'0',#{tname},#{fname})
	</select>
	
	<select id="getDsfyh" resultType="com.tzx.miniapp.rest.vo.DishVo" >
		select -1 as details_id, b.yhfsmc1 as item_name,b.id as yhfsid,b.yhfsbh from ts_yhfssdk b where  yhsx =#{yhsx} 
	</select>

	<select id="getDsfyhByYhfsmc2" resultType="com.tzx.miniapp.rest.model.TsYhfssdk" >
		select * from ts_yhfssdk b where yhfsmc2 =#{yhfsmc2} limit 1;
	</select>

	<insert id="insertTqWdkCouponTemp" parameterType="java.util.List">
		insert into tq_wdk_coupon_temp(zdbh,datatype,coupontype,couponcode,coupondishcode,is_usable,useablemsg,coupon_salemoney,coupon_totalmoney,useok) values 
	    <foreach collection="list" item="twct" index="index"
	      separator=",">
	      (
	      #{twct.zdbh}, #{twct.datatype}, #{twct.coupontype}, #{twct.couponcode}, #{twct.coupondishcode}, #{twct.is_usable}, #{twct.useablemsg}, #{twct.coupon_salemoney}, #{twct.coupon_totalmoney}, #{twct.useok}
	      )
	    </foreach>
	</insert>
	<insert id="insertDyrwk">
		insert into tp_dyrwk(jtbh, qqlx, djlx, qdsj, clbz) values 
		(#{jtbh}, #{qqlx}, #{djlx} , #{qdsj}, #{clbz})
	</insert>
	
	<select id="getFklslsk" resultType="com.tzx.miniapp.rest.model.TqFklslsk" >
		select *, sd.sfsr from tq_fklslsk ls left join ts_fkfssdk sd on sd.id = ls.jzid where kdzdbh = #{kdzdbh} order by ls.id
	</select>
	
	<select id="getBjyhYh" resultType="com.tzx.miniapp.rest.model.TsYhfssdk" >
		select * from ts_yhfssdk where yhfsbh = #{yhfsbh}
	</select>
	
	<select id="getJjcidList" resultType="com.tzx.miniapp.rest.vo.BtYdxm2">
		select mx2.id as ydxm2id, mx1.id as jjcydxm2id from 
		(select a.* from bt_ydxm2 a where a.yddh = #{tsyddbh} and (a.xmsx = '加价菜' or a.xmsx = 'CMSX_YC_J')) mx1 left join
		(select b.* from bt_ydxm2 b where b.yddh = #{tsyddbh} and b.xmsx &lt;&gt; '加价菜' and b.xmsx &lt;&gt; 'CMSX_YC_J') mx2 on mx1.dcxh = mx2.dcxh
	</select>
	
	<update id="updateYdJjcrwid">
        update tq_wdk a set jjcrwid = b.rwid ,dcxh = b.dcxh, syyhfkfsid = b.syyhfkfsid, tcdch = b.tcdch, yhxh = b.yhxh
		from (select kdzdbh,rwid,dcxh,syyhfkfsid, tcdch, yhxh from tq_wdk where kdzdbh = #{kdzdbh} and ydxmid = #{ydxm2id}) b
		where a.kdzdbh = b.kdzdbh and a.ydxmid = #{jjcydxm2id}
    </update>

	<update id="updateTcid">
		UPDATE tq_wdk w
		SET tcid = t_tcid.cmid
		FROM
			( SELECT tcid, cmid FROM tq_wdk WHERE kdzdbh = #{kdzdbh} AND cmsx = 'CMSX_TC' ) t_tcid
		WHERE
			kdzdbh = #{kdzdbh}
		  AND w.tcid = t_tcid.tcid;
	</update>
    
    <select id="findBalanceList" resultType="com.tzx.miniapp.rest.model.TqFklslsk" >
		select fkls.* from tq_fklslsk fkls left join ts_fkfssdk fksd on fkls.jzid = fksd.id where fkls.kdzdbh = #{zdbh} and fksd.fkfsbh = #{fkfsbh} order by fkls.fkje desc
	</select>
	
	<select id="findBalanceAmount" resultType="java.lang.Double" >
		select coalesce(sum(fkls.fkje), 0) as balance_amount from tq_fklslsk fkls left join ts_fkfssdk fksd on fkls.jzid = fksd.id where fkls.kdzdbh = #{zdbh} and fksd.fkfsbh = #{fkfsbh}
	</select>
	
	<select id="yddToZdXcx" resultType="java.lang.Integer">
		select * from p_zwyydtozd_xcx(#{yydh}, #{lsdh}, #{kdzdbh}, #{jzzdbh}, #{bbbc}, #{bbrq}, #{czry}, '99', #{ygdlcs})
	</select>
	
	<select id="findEcotypedic" resultType="com.tzx.receiver.entity.msg.EcoTypeDic">
		select * from tq_ecotypedic where fkfsbh = #{fkfsbh} and thirdcode = #{thirdcode}
	</select>
	
	<update id="updateYddToTotalprice">
	update bt_ydd set totalprice = totalprice - #{payGive} where yddh = #{yddh}
	</update>

	<select id="findEcotypedicByCode" resultType="com.tzx.receiver.entity.msg.EcoTypeDic">
		select * from tq_ecotypedic where thirdcode = #{thirdcode} and tzxcode = #{tzxcode}
	</select>

	<select id="getDsfyhByCode" resultType="com.tzx.miniapp.rest.vo.DishVo" >
		select -1 as details_id, b.yhfsmc1 as item_name,b.id as yhfsid,b.yhfsbh from ts_yhfssdk b where  yhfsbh =#{yhfsbh}
	</select>

	<select id="getQmJjcidList" resultType="com.tzx.miniapp.rest.vo.BtYdxm2">
		select mx2.id as ydxm2id, mx1.id as jjcydxm2id from
		(select a.* from bt_ydxm2 a where a.yddh = #{tsyddbh} and a.yl5 = 'CMSX_JJC') mx1 left join
		(select b.* from bt_ydxm2 b where b.yddh = #{tsyddbh} and b.yl5 &lt;&gt; 'CMSX_JJC') mx2 on mx1.yl2 = mx2.yl2
	</select>

</mapper>

