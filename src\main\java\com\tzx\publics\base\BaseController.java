package com.tzx.publics.base;

import com.tzx.publics.annotations.RestMethod;
import com.tzx.publics.base.entity.MethodInfo;
import com.tzx.publics.common.BaseData;
import com.tzx.publics.util.SystemException;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * 控制器基类 jxy 2018/2/5.
 */
public abstract class BaseController {

	private final static Logger LOGGER = LoggerFactory.getLogger(BaseController.class);
	protected final static Map<String, MethodInfo> restMethodMap = new HashMap<>();

	/**
	 * 统一异常处理
	 * 
	 * @param request
	 * @param response
	 * @param exception
	 */
	@ExceptionHandler
	public BaseData exceptionHandler(HttpServletRequest request, HttpServletResponse response, SystemException exception) {
		LOGGER.error("统一异常处理：", exception);
		request.setAttribute("ex", exception);
		request.setAttribute("code", exception.getErrorCode().getCode());
		request.setAttribute("message", exception.getErrorCode().getMessage());
		BaseData data = new BaseData();
		data.setCode(exception.getErrorCode().getCode());
		data.setMsg(exception.getErrorCode().getMessage());
		LOGGER.error("有一条异常---" + exception.getErrorCode().getMessage());
		return data;
	}

	protected final void loadMethod() {
		try {
			Class<?> clazz = this.getClass();
			if (clazz.isAnnotationPresent(RestController.class)) {
				for (Method method : this.getClass().getMethods()) {
					if (method.isAnnotationPresent(RestMethod.class)) {
						RestMethod restMethod = method.getAnnotation(RestMethod.class);
						String type = restMethod.type();
						String oper = restMethod.oper();
						LOGGER.info(" 初始化方法 ：" + clazz.getSimpleName() + "." + method.getName());
						restMethodMap.put(type, new MethodInfo(oper, method));
					}
				}
			} else {
				LOGGER.debug(" 非加载类：  " + clazz.getSimpleName());
			}
		} catch (Exception e) {
			LOGGER.error(e.getMessage());
		}
	}

	protected final Method searchMethod(String type, String oper) {
		if (StringUtils.isNotBlank(type) && !restMethodMap.containsKey(type)) {
			loadMethod();
		}
		if (restMethodMap.containsKey(type)) {
			return restMethodMap.get(type).getMethod(oper);
		} else {
			LOGGER.info("没有找到type：{}的执行方法", type);
			return null;
		}
	}
}
