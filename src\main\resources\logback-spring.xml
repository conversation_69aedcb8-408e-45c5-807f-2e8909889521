<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="10 seconds"  debug="false">
<!--     <property name="log.path" value="./logs" /> -->
    <property name="log.path" value="/Program Files/TZXPOS/tzxserver3.0/logs"/>

    <!-- 控制台日志配置 -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36}:%line  - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 通用的日志文件控制 -->
    <appender name="rootfile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 文件路径 -->
        <file>${log.path}/base/base.log</file>
        <!--日志文档输出格式 -->
        <encoder>
            <pattern>%d{HH:mm:ss.SSS}[%thread] %-5level %logger{36} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- 是否追加 默认为true -->
        <append>true</append>
        <!-- 滚动策略 日期+大小 策略 -->
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/base/%d{yyyy-MM-dd,aux}/base-%d{yyyy-MM-dd}.%i.log.zip</fileNamePattern>
            <!-- 单个日志大小 -->
            <maxFileSize>100MB</maxFileSize>
            <!-- 日志保存周期 -->
            <maxHistory>30</maxHistory>
            <!-- 总大小 -->
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>info</level>
        </filter>
    </appender>

    <!-- 监听日志文件控制 -->
    <appender name="tzxreceiverfile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 文件路径 -->
        <file>${log.path}/tzxrif/tzxrif.log</file>
        <!--日志文档输出格式 -->
        <encoder>
            <pattern>%d{HH:mm:ss.SSS}[%thread] %-5level %logger{36} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- 是否追加 默认为true -->
        <append>true</append>
        <!-- 滚动策略 日期+大小 策略 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/tzxrif/%d{yyyy-MM-dd,aux}/tzxrif-%d{yyyy-MM-dd}.%i.log.zip</fileNamePattern>
            <!-- 单个日志大小 -->
            <maxFileSize>100MB</maxFileSize>
            <!-- 日志保存周期 -->
            <maxHistory>30</maxHistory>
            <!-- 总大小 -->
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>info</level>
        </filter>
    </appender>

    <appender name="tzxmobileposfile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 文件路径 -->
        <file>${log.path}/mobilepos/mobilepos.log</file>
        <!--日志文档输出格式 -->
        <encoder>
            <pattern>%d{HH:mm:ss.SSS}[%thread] %-5level %logger{36} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- 是否追加 默认为true -->
        <append>true</append>
        <!-- 滚动策略 日期+大小 策略 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/mobilepos/%d{yyyy-MM-dd,aux}/mobilepos-%d{yyyy-MM-dd}.%i.log.zip</fileNamePattern>
            <!-- 单个日志大小 -->
            <maxFileSize>100MB</maxFileSize>
            <!-- 日志保存周期 -->
            <maxHistory>30</maxHistory>
            <!-- 总大小 -->
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>info</level>
        </filter>
    </appender>


    <appender name="tzxmimiappfile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 文件路径 -->
        <file>${log.path}/miniApps/miniApps.log</file>
        <!--日志文档输出格式 -->
        <encoder>
            <pattern>%d{HH:mm:ss.SSS}[%thread] %-5level %logger{36} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- 是否追加 默认为true -->
        <append>true</append>
        <!-- 滚动策略 日期+大小 策略 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/miniApps/%d{yyyy-MM-dd,aux}/miniApps-%d{yyyy-MM-dd}.%i.log.zip</fileNamePattern>
            <!-- 单个日志大小 -->
            <maxFileSize>100MB</maxFileSize>
            <!-- 日志保存周期 -->
            <maxHistory>30</maxHistory>
            <!-- 总大小 -->
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>info</level>
        </filter>
    </appender>
    
    <appender name="tzxecoserverfile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 文件路径 -->
        <file>${log.path}/ecoServer/ecoServer.log</file>
        <!--日志文档输出格式 -->
        <encoder>
            <pattern>%d{HH:mm:ss.SSS}[%thread] %-5level %logger{36} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- 是否追加 默认为true -->
        <append>true</append>
        <!-- 滚动策略 日期+大小 策略 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/ecoServer/%d{yyyy-MM-dd,aux}/ecoServer-%d{yyyy-MM-dd}.%i.log.zip</fileNamePattern>
            <!-- 单个日志大小 -->
            <maxFileSize>100MB</maxFileSize>
            <!-- 日志保存周期 -->
            <maxHistory>30</maxHistory>
            <!-- 总大小 -->
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>info</level>
        </filter>
    </appender>
    
    <appender name="tzxfmcgbifile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 文件路径 -->
        <file>${log.path}/fmcgbi/fmcgbi.log</file>
        <!--日志文档输出格式 -->
        <encoder>
            <pattern>%d{HH:mm:ss.SSS}[%thread] %-5level %logger{36} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- 是否追加 默认为true -->
        <append>true</append>
        <!-- 滚动策略 日期+大小 策略 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/fmcgbi/%d{yyyy-MM-dd,aux}/fmcgbi-%d{yyyy-MM-dd}.%i.log.zip</fileNamePattern>
            <!-- 单个日志大小 -->
            <maxFileSize>100MB</maxFileSize>
            <!-- 日志保存周期 -->
            <maxHistory>30</maxHistory>
            <!-- 总大小 -->
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>info</level>
        </filter>
    </appender>
    
    <appender name="tzxkvsfile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 文件路径 -->
        <file>${log.path}/kvs/kvs.log</file>
        <!--日志文档输出格式 -->
        <encoder>
            <pattern>%d{HH:mm:ss.SSS}[%thread] %-5level %logger{36} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- 是否追加 默认为true -->
        <append>true</append>
        <!-- 滚动策略 日期+大小 策略 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/kvs/%d{yyyy-MM-dd,aux}/kvs-%d{yyyy-MM-dd}.%i.log.zip</fileNamePattern>
            <!-- 单个日志大小 -->
            <maxFileSize>100MB</maxFileSize>
            <!-- 日志保存周期 -->
            <maxHistory>30</maxHistory>
            <!-- 总大小 -->
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>info</level>
        </filter>
    </appender>

    <root level="info">
        <appender-ref ref="console" />
        <appender-ref ref="rootfile" />
    </root>

    <!-- 监听日志文件  -->
    <logger name="com.tzx.receiver" additivity="false" level="info" >
        <appender-ref ref="console" />
        <appender-ref ref="tzxreceiverfile" />
    </logger>

    <!-- 监听数据日志文件  -->
    <logger name="org.springframework.jdbc.core" additivity="false" level="error" >
        <appender-ref ref="console" />
        <appender-ref ref="tzxreceiverfile" />
    </logger>

    <!-- app服务的日志输入路径  -->

    <logger name="com.tzx.mobilepos" additivity="false" level="info" >
        <appender-ref ref="console" />
        <appender-ref ref="tzxmobileposfile" />
    </logger>


    <!-- 小程序服务的日志输入路径  -->

    <logger name="com.tzx.miniapp" additivity="false" level="info" >
        <appender-ref ref="console" />
        <appender-ref ref="tzxmimiappfile" />
    </logger>
    
    <!-- ECO服务的日志输入路径  -->
    <logger name="com.tzx.ecoserver" additivity="false" level="info" >
        <appender-ref ref="console" />
        <appender-ref ref="tzxecoserverfile" />
    </logger>
    
    <!-- 凯景服务的日志输入路径  -->
    <logger name="com.tzx.fmcgbi" additivity="false" level="info" >
        <appender-ref ref="console" />
        <appender-ref ref="tzxfmcgbifile" />
    </logger>
    
    <!-- kvs的日志输入路径  -->
    <logger name="com.tzx.kvs" additivity="false" level="info" >
        <appender-ref ref="console" />
        <appender-ref ref="tzxkvsfile" />
    </logger>
    
</configuration>