package com.tzx.receiver.common.utils;



import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import com.tzx.receiver.common.exception.SystemException;
import com.tzx.receiver.entity.MessageBody;
import com.tzx.receiver.entity.Module;

public class Global
{

	public static boolean isAjaxRequest(HttpServletRequest request)
	{
		return request.getHeader("x-requested-with") != null && request.getHeader("x-requested-with").equalsIgnoreCase("XMLHttpRequest");
	}

	/**
	 * 获得用户远程地址
	 */
	public static String getRemoteAddr(HttpServletRequest request)
	{
		String remoteAddr = request.getHeader("X-Real-IP");
		if (StringUtils.isNotBlank(remoteAddr))
		{
			remoteAddr = request.getHeader("X-Forwarded-For");
		}
		else if (StringUtils.isNotBlank(remoteAddr))
		{
			remoteAddr = request.getHeader("Proxy-Client-IP");
		}
		else if (StringUtils.isNotBlank(remoteAddr))
		{
			remoteAddr = request.getHeader("WL-Proxy-Client-IP");
		}
		return remoteAddr != null ? remoteAddr : request.getRemoteAddr();
	}

	/**
	 * 按级别得到模块
	 * 
	 * @param moduleList
	 * @return
	 * <AUTHOR>
	 */
	public static List<Module> getModuleByLevel(List<Module> moduleList, short level, String funccode)
	{
		List<Module> twoLevelModList = new ArrayList<Module>();
		Module form = null;
		for (int i = 0; i < moduleList.size(); i++)
		{
			form = moduleList.get(i);
			if (form.getLevel1() == level && form.getModuleCode().startsWith(funccode))
			{
				twoLevelModList.add(form);
			}
		}
		return twoLevelModList;
	}

	public static MessageBody getMessageBody(boolean success)
	{
		MessageBody body = new MessageBody();
		body.setSuccess(success);
		return body;
	}

	public static MessageBody getMessageBody(SystemException exception)
	{
		MessageBody body = new MessageBody();
		body.setSuccess(false);
		body.setErrorCode(exception.getErrorCode().getNumber());
		body.setErrorInfo(exception.getErrorInfo());
		body.setData(exception.getProperties());
		body.setStackTrace(exception.getStackTraceAsString()); // 设置异常信息
		return body;
	}

}
