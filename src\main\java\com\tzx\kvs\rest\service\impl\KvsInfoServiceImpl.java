package com.tzx.kvs.rest.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.tzx.kvs.common.KvsData;
import com.tzx.kvs.common.KvsException;
import com.tzx.kvs.rest.mapper.KvsInfoMapper;
import com.tzx.kvs.rest.service.IKvsInfoService;
import com.tzx.kvs.rest.vo.KvsInfoOnlyQchVo;

import net.sf.json.JSONObject;

@Service
public class KvsInfoServiceImpl implements IKvsInfoService {
	private final static Logger LOGGER = LoggerFactory.getLogger(KvsInfoServiceImpl.class);

	@Autowired
	private KvsInfoMapper infoMapper;

	@Transactional
	public void getKvsInfo(KvsData data, JSONObject requestJson) {
		JSONObject jsonData =  requestJson.optJSONObject("data");
		String lastUpdateTimeOld = jsonData.optString("lastUpdateTime", "");
		Boolean isSeparate = jsonData.optBoolean("isSeparate", true);
		List<String> showSource = jsonData.optJSONArray("showSource");
		
		JSONObject responseJson = new JSONObject();
		
		String lastUpdateTimeNew = infoMapper.getLastUpdateTime();
		if (lastUpdateTimeOld.equals(lastUpdateTimeNew)) {
			data.setCode(1);
			data.setData("");
			LOGGER.info("无新kvs数据！");
			throw new KvsException("无新kvs数据！");
		} else {
			responseJson.put("lastUpdateTime", lastUpdateTimeNew);
			
			String inF = "f";
			String notInF = "f";
			if(showSource.size() > 0){
				inF = "t";
				for (String source : showSource) {
					switch (source) {
					case "OTHER":
						notInF = "t";
						break;
					default:
						break;
					}
				}
			}
			
			List<KvsInfoOnlyQchVo> kvsInfoList = infoMapper.getKvsInfoOnlyQch(inF, notInF, showSource);
			if (isSeparate) {
				List<KvsInfoOnlyQchVo> posInfoList = new ArrayList<KvsInfoOnlyQchVo>();
				List<KvsInfoOnlyQchVo> xcxInfoList = new ArrayList<KvsInfoOnlyQchVo>();
				for (KvsInfoOnlyQchVo kvsInfo : kvsInfoList) {
					switch (kvsInfo.getSource()) {
					case "XCX":
						xcxInfoList.add(kvsInfo);
						break;
					default:
						posInfoList.add(kvsInfo);
						break;
					}
				}
				responseJson.put("posInfo", posInfoList);
				responseJson.put("xcxInfo", xcxInfoList);
			} else {
				responseJson.put("allInfo", kvsInfoList);
			}
		}
		
		data.setMsg("查询成功！");
		data.setData(responseJson);
	}

	@Transactional
	public void removeBill(KvsData data, JSONObject requestJson) {
		JSONObject jsonData = requestJson.optJSONObject("data");
		String kdzdbh = jsonData.optString("kdzdbh", "");

		JSONObject responseJson = new JSONObject();

		infoMapper.insertKvsfzk(kdzdbh, -1, "KVS");
		infoMapper.updateKvsChangeTime();

		data.setMsg("KVS消单成功！");
		data.setData(responseJson);
		
//		if (1 == 1) {
//			data.setCode(1);
//			data.setData("");
//			LOGGER.error("对方不想和你说话，并向你抛了一个异常！");
//			throw new KvsException("我有数据，我不查，我就报错，哎~我就是玩儿！");
//		}
	}
	
	
	@Transactional
	public void getRecoverKvsInfo(KvsData data, JSONObject requestJson) {
		JSONObject jsonData =  requestJson.optJSONObject("data");
		String lastUpdateTimeOld = jsonData.optString("lastUpdateTime", "");
		Boolean isSeparate = jsonData.optBoolean("isSeparate", true);
		int showCount = jsonData.optInt("showCount", 20);
		int timeLimit = jsonData.optInt("timeLimit", 120); // 查多久内的数据，单位分钟
		
		JSONObject responseJson = new JSONObject();
		
		String lastUpdateTimeNew = infoMapper.getLastUpdateTime();
		if (lastUpdateTimeOld.equals(lastUpdateTimeNew)) {
			data.setCode(1);
			data.setData("");
			LOGGER.info("无新kvs数据！");
			throw new KvsException("无新kvs数据！");
		} else {
			responseJson.put("lastUpdateTime", lastUpdateTimeNew);
			List<KvsInfoOnlyQchVo> kvsInfoList = infoMapper.getRecoverKvsInfoOnlyQch("KVS", showCount, timeLimit);
			if (isSeparate) {
				List<KvsInfoOnlyQchVo> posInfoList = new ArrayList<KvsInfoOnlyQchVo>();
				List<KvsInfoOnlyQchVo> xcxInfoList = new ArrayList<KvsInfoOnlyQchVo>();
				for (KvsInfoOnlyQchVo kvsInfo : kvsInfoList) {
					switch (kvsInfo.getSource()) {
					case "XCX":
						xcxInfoList.add(kvsInfo);
						break;
					default:
						posInfoList.add(kvsInfo);
						break;
					}
				}
				responseJson.put("posInfo", posInfoList);
				responseJson.put("xcxInfo", xcxInfoList);
			} else {
				responseJson.put("allInfo", kvsInfoList);
			}
		}
		
		data.setMsg("查询成功！");
		data.setData(responseJson);
	}
	
	@Transactional
	public void recoverRemoveBill(KvsData data, JSONObject requestJson) {
		JSONObject jsonData = requestJson.optJSONObject("data");
		String kdzdbh = jsonData.optString("kdzdbh", "");

		JSONObject responseJson = new JSONObject();

		infoMapper.delKvsfzk(kdzdbh, -1, "KVS");
		infoMapper.updateKvsChangeTime();

		data.setMsg("KVS恢复消单成功！");
		data.setData(responseJson);
	}
}
