package com.tzx.fmcgbi.common;

import com.google.common.collect.ImmutableMap;

import java.util.Map;

public class Constant {

	// code的值
	public static final int CODE_SUCCESS = 0; // 成功
	public static final int CODE_PARAM_FAILURE = 1; // 参数失败
	public static final int CODE_AUTH_FAILURE = 2; // 认证失败
	public static final int CODE_NULL_DATASET = 3; // 数据集为空
	public static final int CODE_CHANGE_DATASOURCE_FAILURE = 4;// 切换数据源失败
	public static final int CODE_INNER_EXCEPTION = 5; // 内部错误
	public static final int CODE_STORE_EXCEPTION = 6; // 门店状态异常
	public static final int CODE_CONN_EXCEPTION = 99; // 连接异常

	public static final String CODE_CONN_EXCEPTION_MSG = "网络请求连接超时"; // 连接异常
	public static final String TABLE_STATE_SUCCESS = "获取桌位成功";
	public static final String TABLE_STATE_ERROR = "获取桌位失败";

	public static final String BASICDATA_SUCCESS = "同步数据成功";
	public static final String BASICDATA_ERROR = "同步数据失败";
	// 折扣方案id
	public static final Double DISCOUNT_CASE_ID = 0d;

	public static final String NOT_EXISTS_TYPE = "未找到对应的type";
	public static final String NOT_EXISTS_OPER = "未找到对应的oper";

	public static final String QUERY_ORGAN_STATUS_FAILURE = "查询门店状态失败";

	public static final String NOT_EXISTS_HAVING_DISH = "没有已下单菜品，不能取消备注";
	public static final String HAVING_PRINT_CAN_NOT_CANC = "菜品已打印，不能取消备注";

	public static final String LOGIN_SUCCESS = "登录成功";
	public static final String LOGIN_FAILURE = "登录失败,用户名或密码错误~";
	public static final String LOGIN_ERROR = "登录失败";

	public static final String MODIFY_PASSWORD_SUCCESS = "修改密码成功";
	public static final String MODIFY_PASSWORD_FAILURE = "修改密码失败";

	public static final String UPLOAD_PRINTFORMAT_SUCCESS = "打印模板上传成功";
	public static final String UPLOAD_PRINTFORMAT_FAILURE = "打印模板上传失败";

	public static final String OPEN_TABLE_FAILURE = "开台失败";
	public static final String TABLE_OCCUPY = "桌位已被占用";
	public static final String OPEN_TABLE_SUCCESS = "开台成功";

	public static final String WHOLE_CANC_BILL_FAILURE = "整单取消失败";
	public static final String WHOLE_CANC_BILL_SUCCESS = "整单取消成功";

	public static final String SYNC_DATA_FAILURE = "同步数据失败";
	public static final String SYNC_DATA_SUCCESS = "同步数据成功";

	public static final String FIND_TABLE_NOT_EXISTS = "未查询到对应的桌位，请检查您输入的桌位号或联系管理员";
	public static final String FIND_TABLE_FAILURE = "查询桌位失败";
	public static final String FIND_TABLE_SUCCESS = "查询桌位成功";

	public static final String ORDER_DISH_FAILURE = "下单失败,未找到此账单对应的明细";
	public static final String ORDER_DISH_SUCCESS = "下单成功";

	public static final String PAUSE_ORDER_DISH_FAILURE = "暂存菜品失败";
	public static final String PAUSE_ORDER_DISH_SUCCESS = "暂存菜品成功";

	public static final String PAYMENT_FAILURE = "结账失败";
	public static final String PAYMENT_SUCCESS = "结账成功";
	public static final String PAYMENT_PAYING = "结账付款中";

	public static final String CLEAR_PAYMENT_FAILURE = "清除付款记录失败";
	public static final String CLEAR_PAYMENT_SUCCESS = "清除付款记录成功";

	public static final String CHANGE_TABLE_FAILURE = "转台失败";
	public static final String CHANGE_TABLE_SUCCESS = "转台成功";

	public static final String COMBINE_TABLE_FAILURE = "并台失败";
	public static final String COMBINE_TABLE_SUCCESS = "并台成功";

	public static final String FIND_BILL_FAILURE = "查询账单失败";
	public static final String FIND_BILL_SUCCESS = "查询账单成功";

	public static final Map<String, String> CMXG_ERROR_MAP = ImmutableMap.of("-1", "根据桌位名称没有找到桌位", "-2",
			"根据桌位名称和RWID在WDK没有找到菜品", "-3", "处理失败 ", "-4", "处理奉送失败", "-5", "处理失败1");

	public static final String REGISTER_SUCCESS = "注册成功";
	public static final String REGISTER_FAILURE = "注册失败,系统异常";
	public static final String REGISTER_EXISTING = "注册失败,机台编号或ip已存在";
	public static final String REGISTER_YES = "该设备已注册";
	public static final String REGISTER_NO = "未注册";
	public static final String DISCOUNT_SUCCESS = "优惠成功";
	public static final String DISCOUNT_QUERY_SUCCESS = "优惠活动查询成功";

	public static final String MEITUAN_VERIFY = "校验成功";
	public static final String MEITUAN_EXCEPTION = "调用验券接口异常";

}
