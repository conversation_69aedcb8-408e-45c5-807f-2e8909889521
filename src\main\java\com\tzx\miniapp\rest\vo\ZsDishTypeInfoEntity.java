package com.tzx.miniapp.rest.vo;

import java.io.Serializable;

public class ZsDishTypeInfoEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	private String categoryId;// 分类id
	private String merCategoryId;// 集团分类id
	private String parentCategoryId;// 上级分类编号
	private Integer status;// 状态：0：已停用，1：启用 2:删除
	private String name;// 分类名称
	private Integer sort;// 排序

	public String getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(String categoryId) {
		this.categoryId = categoryId;
	}

	public String getMerCategoryId() {
		return merCategoryId;
	}

	public void setMerCategoryId(String merCategoryId) {
		this.merCategoryId = merCategoryId;
	}

	public String getParentCategoryId() {
		return parentCategoryId;
	}

	public void setParentCategoryId(String parentCategoryId) {
		this.parentCategoryId = parentCategoryId;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Integer getSort() {
		return sort;
	}

	public void setSort(Integer sort) {
		this.sort = sort;
	}

}
