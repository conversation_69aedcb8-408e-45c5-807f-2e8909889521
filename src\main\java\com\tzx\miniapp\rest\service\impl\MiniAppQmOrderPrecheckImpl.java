package com.tzx.miniapp.rest.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.tzx.commapi.rest.service.IUseYhfsApiService;
import com.tzx.commapi.rest.vo.UseYhfsParam;
import com.tzx.miniapp.common.Constant;
import com.tzx.miniapp.common.Data;
import com.tzx.miniapp.rest.mapper.MiniAppFirstPayMapper;
import com.tzx.miniapp.rest.mapper.MiniAppOrderPrecheckMapper;
import com.tzx.miniapp.rest.mapper.MiniAppShopBaseInfoMapper;
import com.tzx.miniapp.rest.mapper.MiniAppShopStatusMapper;
import com.tzx.miniapp.rest.mapper.MiniAppTqWdkMapper;
import com.tzx.miniapp.rest.mapper.MiniAppTqZdkMapper;
import com.tzx.miniapp.rest.mapper.MiniAppTsPsjgsdkMapper;
import com.tzx.miniapp.rest.model.TqFklslsk;
import com.tzx.miniapp.rest.model.TqJtztk;
import com.tzx.miniapp.rest.model.TqWdk;
import com.tzx.miniapp.rest.model.TqZdk;
import com.tzx.miniapp.rest.model.TsCmk;
import com.tzx.miniapp.rest.model.TsFkfssdk;
import com.tzx.miniapp.rest.model.TsGgcsk;
import com.tzx.miniapp.rest.model.TsPsjgsdk;
import com.tzx.miniapp.rest.model.TsYhfssdk;
import com.tzx.miniapp.rest.service.IMiniAppQmOrderPrecheck;
import com.tzx.miniapp.rest.vo.AccountsOrder;
import com.tzx.miniapp.rest.vo.BtPayments;
import com.tzx.miniapp.rest.vo.BtTcselectmx;
import com.tzx.miniapp.rest.vo.BtYdd;
import com.tzx.miniapp.rest.vo.BtYdxm2;
import com.tzx.miniapp.rest.vo.Dish;
import com.tzx.miniapp.rest.vo.DishVo;
import com.tzx.miniapp.rest.vo.GroupDetails;
import com.tzx.miniapp.rest.vo.Shops;
import com.tzx.publics.common.BillNoData;
import com.tzx.publics.listener.InitDataListener;
import com.tzx.publics.util.ArithUtil;
import com.tzx.publics.util.DateUtil;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

@Service
public class MiniAppQmOrderPrecheckImpl implements IMiniAppQmOrderPrecheck {

	private final static Logger LOGGER = LoggerFactory.getLogger(MiniAppQmOrderPrecheckImpl.class);

	@Autowired
	private MiniAppOrderPrecheckMapper orderPrecheckMapper;
	@Autowired
	private MiniAppFirstPayMapper firstPayMapper;
	@Autowired
	private MiniAppShopStatusMapper shopStatusMapper;
	@Autowired
	private MiniAppTqZdkMapper tqZdkMapper;
	@Autowired
	private MiniAppTqWdkMapper tqWdk;
	@Autowired
	private MiniAppShopBaseInfoMapper shopBaseInfoMapper;
	// 机构配置信息查询接口注入对象
	@Autowired
	private MiniAppTsPsjgsdkMapper tsPsjgsdkMapper;
	// 不计收入拆分
	@Autowired
	private IUseYhfsApiService useYhfsApiService;
	
	@Transactional
	public Data orderPrecheck(JSONObject orderData, BillNoData billNoData) {
		// 创建返回数据对象
		Data data = new Data();
		// 小程序订单号
		String outOrderId = orderData.optString("out_order_id");
		// 持久化使用第预订单号字段，为了快速区分，小程序增加了  TS 前缀
		String outOrderIdInDB = Constant.BILL_PREFIX + outOrderId;
		
		// 报表日期
		Map<String, String> bbrqMap = shopStatusMapper.findBbrq();
		// 生成默认报表日期
		String bbrq = DateUtil.getNowDateYYDDMM();
		if (null != bbrqMap && bbrqMap.size() != 0) {
			bbrq = bbrqMap.get("bbrq");
		}
		// 判断营业状态，因为推送异常账单时不判断营业状态，所以这里控制一下
		TqJtztk jtztk = shopStatusMapper.checkShopOpenStart(DateUtil.parseDate(bbrq));
		if (null == jtztk || "JSSY".equals(jtztk.getCznr())) {
			data.setSuccess(0);
			data.setMsg("门店已打烊，请开店后重试！");
			data.setData(new HashMap<String, Object>());
			return data;
		}
		
		TqZdk tqzdk = orderPrecheckMapper.getZdk(outOrderIdInDB);
		TqZdk tqzdlsk = orderPrecheckMapper.getZdlsk(outOrderIdInDB);
		
		if ((null != tqzdk && "ZDSX_YJ".equals(tqzdk.getJzsx())) || (null != tqzdlsk && "ZDSX_YJ".equals(tqzdlsk.getJzsx()))) {
			JSONObject joData = new JSONObject();
			joData.put("identify", outOrderId);
			if(null != tqzdk){
				joData.put("meal_number", tqzdk.getQch());
			}
			if(null != tqzdlsk){
				joData.put("meal_number", tqzdlsk.getQch());
			}
			data.setData(joData);
			data.setSuccess(1);
			data.setCode(2);
			data.setMsg("订单已同步");
			return data;
		}
		
		JSONObject orderInfo = orderData.optJSONObject("order_info");
		JSONObject payInfo = orderData.optJSONObject("pay_info");
		// cardr_type 会员卡类型（1：众赏会员，2：百福会员）  ， pos数据库  cwlxbh 1：微生活，2：众赏，3：百福，4：企迈
		String cwlxbh = "4";
		// 默认失败
		data.setSuccess(0);
		// 保存平台预定账单编号
		data.setYddbh(outOrderId);
		try {
			String kdzdbh = billNoData.getKdzdbh();
			Map<String, String> rMap = createBill(orderInfo, kdzdbh, outOrderIdInDB, bbrq, billNoData);
			//落单
			Map<String, String> orderingR = ordering(orderInfo, kdzdbh, outOrderIdInDB, rMap);
			if ("true".equals(orderingR.get("resultFlag"))) {
				double yhje = orderInfo.optDouble("orde_dis_amount", 0);
				TsYhfssdk yhfs = orderPrecheckMapper.getYhfs("65");
				if (yhje != 0 && null != yhfs) {
					insertDiscount(kdzdbh, yhje, bbrq, rMap.get("bcid"), rMap.get("jtbh"), yhfs);
					tqZdkMapper.updateZrje(kdzdbh, yhje);
				}

				firstPayMapper.calcmoney(kdzdbh);
				firstPayMapper.zRtr(kdzdbh);
				
				double payAmount = 0;
				JSONArray payInfoArr = payInfo.optJSONArray("pay_info");
				for (int i = 0; i < payInfoArr.size(); i++) {
					JSONObject payInfo1 = payInfoArr.getJSONObject(i);
					double pAmount = payInfo1.optDouble("amount");
					payAmount = ArithUtil.add(payAmount, pAmount);
				}
				TqZdk zdkWJ = firstPayMapper.getZdbhByYdd(outOrderIdInDB);
				boolean aoflag = false;
				String aoflagMsg = "";
				if (zdkWJ.getFkje() == payAmount) {
					aoflag = firstPay(outOrderIdInDB, payInfo, kdzdbh, bbrq, yhje, rMap, cwlxbh, orderInfo);
				} else {
					aoflag = false;
					aoflagMsg = "：支付金额(" + payAmount + ")与门店账单金额(" + zdkWJ.getFkje() + ")不符，请检查菜品金额；";
				}
				if (aoflag) {
					// 拆分不记收入
					if ("1".equals(InitDataListener.ggcsMap.get("POS_PAYWITHOUT_TODIS"))) {
						List<TqFklslsk> fklslskList = firstPayMapper.getFklslsk(kdzdbh);
						commUseYhfs(zdkWJ, fklslskList, "99", bbrq);
					}
					
					accountsOrder(orderInfo, kdzdbh, bbrq, rMap.get("bcid"), rMap.get("czybh"), billNoData, cwlxbh);
					jointYdd(orderInfo, outOrderIdInDB, rMap.get("bcid"), rMap.get("qch"));
					JSONObject joData = new JSONObject();
					joData.put("identify", outOrderId);
					joData.put("meal_number", rMap.get("qch"));
					data.setData(joData);
					data.setSuccess(1);
					data.setMsg("付款成功");
				} else {
					tqZdkMapper.delWdkByYddh(outOrderIdInDB);
					tqZdkMapper.delFklslskByYddh(outOrderIdInDB);
					tqZdkMapper.delZdkByYddh(outOrderIdInDB);
					orderPrecheckMapper.clearYdd(outOrderIdInDB);
					orderPrecheckMapper.clearYdxm1(outOrderIdInDB);
					orderPrecheckMapper.clearYdxm2(outOrderIdInDB);
					orderPrecheckMapper.clearYddTcSelectMx(outOrderIdInDB);
					data.setSuccess(0);
					data.setMsg("下单失败" + aoflagMsg);
					data.setData(new HashMap<String, Object>());
				}
			} else {
				tqZdkMapper.delWdkByYddh(outOrderIdInDB);
				tqZdkMapper.delFklslskByYddh(outOrderIdInDB);
				tqZdkMapper.delZdkByYddh(outOrderIdInDB);
				orderPrecheckMapper.clearYdd(outOrderIdInDB);
				orderPrecheckMapper.clearYdxm1(outOrderIdInDB);
				orderPrecheckMapper.clearYdxm2(outOrderIdInDB);
				orderPrecheckMapper.clearYddTcSelectMx(outOrderIdInDB);
				data.setSuccess(0);
				data.setMsg("下单失败:" + orderingR.get("resultMsg"));
				data.setData(new HashMap<String, Object>());
			}
			return data;
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			data.setSuccess(0);
			data.setMsg("系统错误:" + e);
			data.setData(new HashMap<String, Object>());
			return data;
		}
	}
	
	public Map<String, String> ordering(JSONObject orderInfo, String kdzdbh, String outOrderIdInDB, Map<String, String> rMap) {
		String resultFlag = "true";
		String resultMsg = "";
		Map<String, String> resultMap = new HashMap<String, String>();
		int result_ = 0;
		JSONArray normalitems = new JSONArray();
		if (orderInfo.has("normalitems")) {
			normalitems = orderInfo.getJSONArray("normalitems");
		}
				
		JSONArray setmeal = new JSONArray();
		if (orderInfo.has("setmeal")) {
			setmeal = orderInfo.getJSONArray("setmeal");
		}
		List<BtYdxm2> bymx = new ArrayList<BtYdxm2>();
		List<BtTcselectmx> tsmx = new ArrayList<BtTcselectmx>();
		int yddcxh = 0;
		
		for (int i = 0; i < normalitems.size(); i++) {
			JSONObject item = normalitems.getJSONObject(i);
			result_ = orderPrecheckMapper.addCmByZs(kdzdbh, item.optInt("did"), item.optInt("number"), "99", "", "", Math.random() * 10000 + "", item.optString("remark"), 6);
//			if (orderInfo.optInt("diningWay", 1) != 1 && 0 != item.optDouble("foodbox_amount", 0)) {
//				boolean tpkF = takeoutPackagingFee(item, kdzdbh, item.optInt("number"), rMap.get("czybh"));
//				if (!tpkF) {
//					result_ = -4;
//				}
//			}
			
			if (result_ != 0) {
				if (result_ == -500) {
					resultMsg = "菜品《" + item.optString("name", "") + "》被沽清，请门店确认";
				}
				break;
			}
			
			if (item.optDouble("", 0) != 0) {
				
			}
			yddcxh = yddcxh + 1;
			BtYdxm2 ydxm2 = jointYdmx(item, outOrderIdInDB, "单品", yddcxh);
			bymx.add(ydxm2);
		}
		
		for (int i = 0; i < setmeal.size(); i++) {
			if (result_ != 0) {
				break;
			}
			JSONObject item = setmeal.getJSONObject(i);
			int clmxidi = getClmxid(item);
			if(clmxidi == 0){
				result_ = -3;
				resultMsg = "菜品：《" + item.optString("name") + "(" + item.optInt("did") + "》不在餐谱内，请重新同步基础数据;";
				break;
			}
			for (int j = 0; j < item.optInt("number"); j++) {
				yddcxh = yddcxh + 1;
				result_ = orderPrecheckMapper.addTcByZs(kdzdbh, clmxidi, 1);
				orderPrecheckMapper.deletetcdcxzlsk(kdzdbh, null, null, null);
				if (result_ == 1) {
					result_ = 0;
				}
				
				BtYdxm2 ydxm2 = jointYdmx(item, outOrderIdInDB, "套餐", yddcxh);
				bymx.add(ydxm2);
				
				// 主菜菜品
//				JSONArray mainDish = new JSONArray();
//				if (item.has("maindish")) {
//					mainDish = item.optJSONArray("maindish");
//				}
//				for (int k = 0; k < mainDish.size(); k++) {
//					JSONObject gro = mainDish.getJSONObject(k);
					// 餐盒处理不再使用套餐主项关联餐盒设置，改成使用明细关联设置
//					if (orderInfo.optInt("diningWay", 1) != 1 && 0 != gro.optDouble("foodbox_amount", 0)) {
//						boolean tpkF = takeoutPackagingFee(gro, kdzdbh, gro.optInt("number"), rMap.get("czybh"));
//						if (!tpkF) {
//							result_ = -4;
//							break;
//						}
//					}
//				}
				
				// 辅助可选菜品
				JSONArray mandatory = new JSONArray();
				if (item.has("mandatory")) {
					mandatory = item.optJSONArray("mandatory");
				}
				Integer tcid = item.optInt("did");
				String tcName = item.optString("name", "");
				for (int k = 0; k < mandatory.size(); k++) {
					if (result_ != 0) {
						break;
					}
					JSONObject gro = mandatory.getJSONObject(k);
					Integer rpdid = gro.optInt("rpdid");
					String mxName = gro.optString("name", "");
					if (0 == rpdid) {
						result_ = -5;
						resultMsg = "套餐《" + tcName + "(" + item.optInt("did") + ")》项目组明细《" + mxName + "(" + gro.optInt("did") + ")》项目组id为空 ，请确认数据后重新下单；";
						break;
					}
					GroupDetails gd = orderPrecheckMapper.getTcxh(tcid, rpdid);
					if (null == gd) {
						result_ = -5;
						resultMsg = "套餐《" + tcName + "(" + item.optInt("did") + ")》项目组明细《" + mxName + "(" + gro.optInt("did") + ")》组id(" + rpdid + ")不存在于本套餐 ，请确认数据后重新下单；";
						break;
					}
					double aprice = 0;
					if (gro.has("aprice")) {
						aprice = gro.optDouble("aprice", 0);
					}
					orderPrecheckMapper.addTcdcxzlsk(kdzdbh, tcid, gd.getId(), gro.optInt("did"), gro.optDouble("number"),  1, Double.parseDouble(gd.getSjjg()), Double.parseDouble(gd.getSjjg()), ArithUtil.mul(aprice, gro.optDouble("number")),  rpdid);
					for (int n = 0; n < gro.optInt("number"); n++) {
						BtTcselectmx btsmx = new BtTcselectmx();
						double fzsl = orderPrecheckMapper.getFzsl(rpdid, gro.optInt("did"));
						if (-1 == fzsl) {
							result_ = -5;
							resultMsg = "套餐《" + tcName + "(" + item.optInt("did") + ")》项目组明细《" + mxName + "(" + gro.optInt("did") + ")》不存在于本套餐 ，请确认数据后重新下单；";
							break;
						}
						btsmx.setYddh(outOrderIdInDB);
						if (0.5 == fzsl) {
							btsmx.setFzsl(0.5);
						} else {
							btsmx.setFzsl(1);
						}
						btsmx.setFzje(new BigDecimal(aprice));
						btsmx.setDcxh(yddcxh);
						btsmx.setFzid(rpdid);
						btsmx.setMxid(gro.optInt("did"));
						tsmx.add(btsmx);
					}
					// 餐盒处理不再使用套餐主项关联餐盒设置，改成使用明细关联设置
//					if (orderInfo.optInt("diningWay", 1) != 1 && 0 != gro.optDouble("foodbox_amount", 0)) {
//						boolean tpkF = takeoutPackagingFee(gro, kdzdbh, gro.optInt("number"), rMap.get("czybh"));
//						if (!tpkF) {
//							result_ = -4;
//							break;
//						}
//					}
				}
				
				if (result_ != 0) {
					break;
				}
				result_ = orderPrecheckMapper.addCmByZs(kdzdbh, clmxidi, 1, "99", "", "", Math.random() * 10000 + "", item.optString("remark"), 0);
//				if (result_ != 0) {
//					break;
//				}
				if (result_ != 0) {
					if (result_ == -500) {
						resultMsg = "菜品《" + item.optString("name", "") + "》被沽清，请门店确认";
					}
					break;
				}
			}
		}
		
		if (result_ == 0 && orderInfo.optInt("diningWay", 1) == 3) {
			TqWdk tqwdk = orderPrecheckMapper.getNewWdk(kdzdbh);
			TsCmk saCmk = orderPrecheckMapper.getSendAmount("小程序外卖配送费");
			result_ = insertSendAmount(kdzdbh, orderInfo.optDouble("send_amount", 0), saCmk, tqwdk, "小程序外卖配送费");
			if (result_ == -1) {
				resultMsg = "未添加小程序配送费，请添加后重新下单！";
			} else {
				JSONObject itemCH = new JSONObject();
				itemCH.put("number", 1);
				itemCH.put("did", saCmk.getCmid());
				itemCH.put("name", "小程序外卖配送费");
				itemCH.put("price", orderInfo.optDouble("send_amount", 0));
				itemCH.put("aprice", 0);
				itemCH.put("discountsprice", 0);
				itemCH.put("dishunit", "个");
				itemCH.put("remark", "");
				
				BtYdxm2 ydxm2 = jointYdmx(itemCH, outOrderIdInDB, "单品", tqwdk.getDcxh() + 1);
				bymx.add(ydxm2);
			}
		}
		if (result_ == 0 && orderInfo.optInt("diningWay", 1) == 2 && 0 != orderInfo.optDouble("foodbox_amount", 0)) {
			TqWdk tqwdk = orderPrecheckMapper.getNewWdk(kdzdbh);
			TsCmk saCmk = orderPrecheckMapper.getSendAmount("餐盒");
			result_ = insertSendAmount(kdzdbh, orderInfo.optDouble("foodbox_amount", 0), saCmk, tqwdk, "餐盒");
			if (result_ == -1) {
				result_ = -7;
				resultMsg = "未添加小程序餐盒费或账单明细不存在，请添加后重新下单！";
			} else {
				JSONObject itemCH = new JSONObject();
				itemCH.put("number", 1);
				itemCH.put("did", saCmk.getCmid());
				itemCH.put("name", "餐盒");
				itemCH.put("price", orderInfo.optDouble("foodbox_amount", 0));
				itemCH.put("aprice", 0);
				itemCH.put("discountsprice", 0);
				itemCH.put("dishunit", "个");
				itemCH.put("remark", "");

				BtYdxm2 ydxm2 = jointYdmx(itemCH, outOrderIdInDB, "单品", tqwdk.getDcxh() + 1);
				bymx.add(ydxm2);
			}
		}
		
		// 保存菜品数据
		if (bymx.size() > 0) {
			orderPrecheckMapper.insertBtYdxm2(bymx);
		}
		// 保存可选套餐明细数据
		if (tsmx.size() > 0) {
			orderPrecheckMapper.insertBtTcselectmx(tsmx);
		}
					
		if (result_ == 0 || result_ == 1) {
			resultFlag = "true";
		} else if (result_ == -500) {
			resultFlag = "false";
		} else if (result_ == -502) {
			resultFlag = "false";
			resultMsg = "有菜品被沽清，请门店确认";
		} else if (result_ == -2) {
			resultFlag = "false";
			resultMsg = "菜品数据不存在，请重新同步后再下单；";
		} else if (result_ == -4) {
			resultFlag = "false";
			resultMsg = "餐盒数据不匹配，请重新同步后再下单；";
		} else if (result_ == -3 || result_ == -5 || result_ == -6 || result_ == -7) {
			resultFlag = "false";
		} else {
			resultFlag = "false";
			resultMsg = "未知错误";
		}
		resultMap.put("resultFlag", resultFlag);
		resultMap.put("resultMsg", resultMsg);
		return resultMap;
	}

	public boolean firstPay(String outOrderIdInDB, JSONObject payInfos, String kdzdbh, String bbrq, double yhje, Map<String, String> rMap, String cwlxbh, JSONObject orderInfo) {
		JSONArray payInfoArr = payInfos.optJSONArray("pay_info");
		boolean aoflag = true;
		TqJtztk jtzt = firstPayMapper.getJtZtk(DateUtil.parseDate(bbrq));
		double storedGivePay = 0;
		double stored_pay = 0;
		for (int i = 0; i < payInfoArr.size(); i++) {
			JSONObject payInfo = payInfoArr.getJSONObject(i);
			String source = payInfo.optString("source");
			String fkfsmcs2 = "";
			String yl3 = "";
			
			if (source.equals("promotion")) {
				String promotion_source = payInfo.optString("promotion_source");
				TsYhfssdk yhfs = null;
				if ("weixin".equals(promotion_source)) {
					yhfs = orderPrecheckMapper.getYhfs("62");
				} else if ("alipay".equals(promotion_source)) {
					yhfs = orderPrecheckMapper.getYhfs("61");
				} else {
					LOGGER.info("不识别的券来源：" + promotion_source);
					return false;
				}
				
				if (null != yhfs) {
					JSONArray promotionList = payInfo.optJSONArray("promotion_list");
					for (int j = 0; j < promotionList.size(); j++) {
						JSONObject pl = promotionList.getJSONObject(j);
						double disAmount = ArithUtil.div(pl.optDouble("amount"), 100, 2);
						insertDiscount(kdzdbh, disAmount, bbrq, rMap.get("bcid"), rMap.get("jtbh"), yhfs);
						yhje = ArithUtil.add(yhje, disAmount);
					}
					tqZdkMapper.updateZrje(kdzdbh, yhje);
					firstPayMapper.calcmoney(kdzdbh);
					firstPayMapper.zRtr(kdzdbh);
				} else {
					LOGGER.info("未添加“支付宝商家优惠”或“微信商家优惠”");
					return false;
				}
			} else {
				if (source.equals("balance")) {
					yl3 = "ERP_FKFS_QMCZXF";
					storedGivePay = ArithUtil.add(storedGivePay, ArithUtil.sub(payInfo.optDouble("amount"), payInfo.optDouble("storepay")));
					stored_pay = payInfo.optDouble("amount");
				} else if (source.equals("credit")) {
					yl3 = "ERP_FKFS_QMJFXF";
				} else if (source.equals("coupon") || source.equals("product")) {
					yl3 = "ERP_FKFS_QMYHQ";
				} else {
					fkfsmcs2 = source;
				}

				TsFkfssdk fkfs = firstPayMapper.getFkfsByZs(fkfsmcs2, yl3);
				if (fkfs == null) {
					fkfs = firstPayMapper.getFkfsByCode("2020");
				}
				BigDecimal actualmoney = new BigDecimal("0.00");
				actualmoney = BigDecimal.valueOf(payInfo.optDouble("amount"));
				// 调用付款存储过程，生成账单付款记录wwq
				String fkbz = "";
				if (payInfo.containsKey("outtradeno")) {
					fkbz = payInfo.optString("outtradeno");
				}
				AccountsOrder ao = firstPayMapper.accountsOrder(kdzdbh, fkfs.getId(), actualmoney, 1, "", "", "", fkbz, "99", jtzt.getRybh());
				insertPayments(outOrderIdInDB, fkfs, actualmoney.doubleValue(), DateUtil.getNowDateYYDDMMHHMMSS());
				if (ao != null && "0".equals(ao.getA()) && aoflag) {
					aoflag = true;
				} else {
					aoflag = false;
					break;
				}
			}
		}
		if (payInfoArr.size() == 0) {
			TsFkfssdk fkfs = firstPayMapper.getFkfsByCode("2020");
			firstPayMapper.accountsOrder(kdzdbh, fkfs.getId(), new BigDecimal(0), 1, "", "", "", "", "99", jtzt.getRybh());
//			insertPayments(outOrderIdInDB, fkfs, 0, DateUtil.getNowDateYYDDMMHHMMSS());
			insertPayments(outOrderIdInDB, fkfs, 0, orderInfo.optString("addtime", DateUtil.getNowDateYYDDMMHHMMSS()));
			aoflag = true;
		}
		
//		POS_MEMBALANCE_USEDIS 会员储值付款记录优惠方式	0 不记录优惠，1按照会员系统传过来的优惠拆分记录优惠，2李先生按比例拆分		
		if ("1".equals(InitDataListener.ggcsMap.get("POS_MEMBALANCE_USEDIS"))) {
			List<TqFklslsk> balanceList = firstPayMapper.findBalanceList(kdzdbh, "1973");
			DishVo dsfyhB = firstPayMapper.getDsfyh("87");
			if (null != dsfyhB && balanceList.size() > 0 && storedGivePay != 0) {
				acewillDiscount(kdzdbh, dsfyhB, balanceList, storedGivePay);
			}
		}
		if ("2".equals(InitDataListener.ggcsMap.get("POS_MEMBALANCE_USEDIS"))) {
			String acewillDiscount = InitDataListener.ggcsMap.get("ACEWILLDISCOUNT");
			if (null != acewillDiscount) {
				if (isNumeric(acewillDiscount) && Integer.parseInt(acewillDiscount) > 0 && Integer.parseInt(acewillDiscount) <= 100) {
					List<TqFklslsk> balanceList = firstPayMapper.findBalanceList(kdzdbh, "1973");
					DishVo dsfyhB = firstPayMapper.getDsfyh("85");
					double scale = Integer.parseInt(acewillDiscount);
					double scale_pay = ArithUtil.sub(stored_pay, ArithUtil.mul(stored_pay, ArithUtil.div(scale, 100, 2)));
					scale_pay = ArithUtil.round(scale_pay, 2);
					if (null != dsfyhB && balanceList.size() > 0 && scale_pay != 0) {
						acewillDiscount(kdzdbh, dsfyhB, balanceList, scale_pay);
					}
				} else {
					LOGGER.error("企迈储值未拆分:拆分比例=" + acewillDiscount);
				}
			} else {
				LOGGER.error("企迈储值未拆分:未设置拆分比例");
			}
		}
		
		if ("1".equals(InitDataListener.ggcsMap.get("POS_MEMCREDIT_USEDIS"))) {
			List<TqFklslsk> creditList = firstPayMapper.findBalanceList(kdzdbh, "1972");
			DishVo dsfyhC = firstPayMapper.getDsfyh("86");
			if (null != dsfyhC && creditList.size() > 0) {
				double credit_give_pays = firstPayMapper.findBalanceAmount(kdzdbh, "1972");
				acewillDiscount(kdzdbh, dsfyhC, creditList, credit_give_pays);
			}
		}
		
		return aoflag;
	}
	
	public void accountsOrder(JSONObject orderInfo, String kdzdbh, String bbrq, String bcid, String czybh, BillNoData billNoData, String cwlxbh) {
		String jzzdbh = billNoData.getJzzdbh();
		Date jzjssj = new Date();
		JSONObject ordermemo = orderInfo.optJSONObject("ordermemo");
		String zdbz = "";
		if(null != ordermemo){
			zdbz = ordermemo.optString("text");
		}
		firstPayMapper.updateZdkZs(zdbz, kdzdbh, jzzdbh, DateUtil.parseDate(bbrq), jzjssj, 1, "99", czybh, "ZDSX_YJ", jzjssj, jzjssj, Integer.parseInt(bcid), orderInfo.optInt("people"), cwlxbh);
		firstPayMapper.updateWdkZs(kdzdbh, jzzdbh, "99", DateUtil.parseDate(bbrq), Integer.parseInt(bcid));
		firstPayMapper.updateFklslskZs(kdzdbh, jzzdbh, Integer.parseInt(bcid));
	}
	
	public void insertDiscount(String kdzdbh, double yhje, String bbrq, String bcid, String skjh, TsYhfssdk yhfs) {
		TqWdk tqwdk = orderPrecheckMapper.getNewWdk(kdzdbh);
		TqWdk tqwdkIn = new TqWdk();
		tqwdkIn.setKdzdbh(kdzdbh);
		tqwdkIn.setClmxid(-1);
		tqwdkIn.setCmbh("YH" + yhfs.getYhfsbh());
		tqwdkIn.setCmmc1(yhfs.getYhfsmc1());
		tqwdkIn.setCmmc2(yhfs.getYhfsmc2());
		tqwdkIn.setDwbh("");
		tqwdkIn.setYzwbh("");
		tqwdkIn.setZwbh("");
		tqwdkIn.setTcfs("");
		tqwdkIn.setTcbl(0);
		tqwdkIn.setTcje(0);
		tqwdkIn.setFzsl(0);
		tqwdkIn.setFzje(0);
		tqwdkIn.setZdsj(0);
		tqwdkIn.setXdh("");
		tqwdkIn.setXdhshry("");
		tqwdkIn.setFwyh("");
		tqwdkIn.setCbdj(new BigDecimal(0));
		tqwdkIn.setCmdj(new BigDecimal(0));
		tqwdkIn.setCmsl(1);
		tqwdkIn.setCmje(new BigDecimal(ArithUtil.mul(yhje, -1)));
		tqwdkIn.setSjje(new BigDecimal(0));
		tqwdkIn.setYhje(new BigDecimal(0));
		tqwdkIn.setDpzkje(new BigDecimal(0));
		tqwdkIn.setZrje(new BigDecimal(yhje));
		tqwdkIn.setZkzt("N");
		tqwdkIn.setXlzkzt("N");
		tqwdkIn.setZkl(100);
		tqwdkIn.setYhfsid(yhfs.getId());
		tqwdkIn.setYhfsbh(yhfs.getYhfsbh());
		tqwdkIn.setYhfs(yhfs.getYhsx());
		tqwdkIn.setTszt("N");
		tqwdkIn.setQczt("N");
		tqwdkIn.setTcdch(0);
		tqwdkIn.setTmbj(new Date());
		tqwdkIn.setDcxh(tqwdk.getDcxh() + 1);
		tqwdkIn.setFsbbrq(DateUtil.parseDate(bbrq));
		tqwdkIn.setFsbcid(Integer.parseInt(bcid));
		tqwdkIn.setFsskjh(skjh);
		tqwdkIn.setSyyhfkfsid(tqwdk.getSyyhfkfsid() + 1);
		tqwdkIn.setScbj(0);
		tqwdkIn.setYongjje(0);
		tqwdkIn.setYongjzkl(100);
		tqwdkIn.setYhxh(0);
		tqwdkIn.setWdbz("");
		tqwdkIn.setKwbz("");
		tqWdk.insert(tqwdkIn);
	}
	
	
	
	public int getClmxid(JSONObject item) {
		String clmxid = orderPrecheckMapper.getClmxidByXmid(item.optInt("did"));
		int clmxidi = 0;
		if (null != clmxid) {
			clmxidi = Integer.parseInt(clmxid);
		}
		return clmxidi;
	}
	
	
	
	public void jointYdd(JSONObject orderInfo, String outOrderIdInDB, String ydbcid, String qch) {
		JSONObject ordermemo = orderInfo.optJSONObject("ordermemo");
		String tableno = orderInfo.optString("tableno");
		BtYdd by = new BtYdd();
		Shops shops = shopBaseInfoMapper.findShopsData();

		by.setYddh(outOrderIdInDB);
		by.setYdrs(orderInfo.optInt("people"));
		by.setMen(0);
		by.setWomen(0);
		by.setEldernum(0);
		by.setChildnum(0);
		by.setShops_id(shops.getSid() + "");
		by.setTotalprice(orderInfo.optDouble("order_amount"));
		by.setYdrq(DateUtil.getNowDateYYDDMM());
		by.setQdsj(DateUtil.getNowDateYYDDMMHHMMSS());
		by.setYdbc(ydbcid);
		by.setDdzt("5");
		if (null != ordermemo) {
			by.setKwxh(ordermemo.optString("text"));
		}
		by.setZlbh(tableno);
		// 就餐时间
		if (orderInfo.containsKey("mealtime")) {
			String mealtime = orderInfo.optString("mealtime", ""); // 可以为空的值默认为""
			by.setMealtime(mealtime);
		}
		// 下单来源 “0”为门店下单；“1”为线上下单；默认为“0”。V1.1新增
		if (orderInfo.containsKey("channel")) {
			int channel = orderInfo.optInt("channel", 0); // 默认为0
			by.setChannel(channel);
		}
		// 销售模式
		if (orderInfo.containsKey("diningWay")) {
			int diningWay = orderInfo.optInt("diningWay", 1); // 默认为"1"堂食,2外带，3外卖
			by.setDiningway("XSMS_TS");
			by.setYl5("1");
			if (diningWay == 2) {
				by.setDiningway("XSMS_WM"); // "XSMS_WM"是外带，不是外卖
				by.setYl5("2");
			}
			if (diningWay == 3) {
				by.setYl5("3");
				if (orderInfo.containsKey("mealtime")) {
					String mealtime = orderInfo.optString("mealtime", "201901011200"); // 可以为空的值默认为""
					mealtime = mealtime.substring(0, 4) + "-" + mealtime.substring(4, 6) + "-" + mealtime.substring(6, 8) + " " + mealtime.substring(8, 10) + ":"  + mealtime.substring(10, 12) + ":00";
					by.setMealtime(mealtime);
				} else {
					by.setMealtime("立即配送");
				}
			}
		}
		// 外卖联系人
		if (orderInfo.containsKey("lxr")) {
			by.setLxr(orderInfo.optString("lxr", ""));
		}
		// 外卖联系电话
		if (orderInfo.containsKey("lxrdh")) {
			by.setLxrdh(orderInfo.optString("lxrdh", ""));
		}
		// 外卖配送地址
		if (orderInfo.containsKey("member_address")) {
			by.setMember_address(orderInfo.optString("member_address", ""));
		}
				
		by.setShrbh(qch);
		orderPrecheckMapper.insertBtYddByZs(by);
	}
	
	
	public BtYdxm2 jointYdmx(JSONObject jo, String outOrderIdInDB, String xmsx, int dcxh) {
		BtYdxm2 ydmx2 = new BtYdxm2();
		int number = 1;
		if("单品".equals(xmsx)){
			number = jo.optInt("number");
		}
		ydmx2.setIsactive(0);
		ydmx2.setYddh(outOrderIdInDB);
		ydmx2.setXmid(jo.optInt("did"));
//		ydmx2.setXmbh(jo.optString("dishsno"));
		ydmx2.setXmmc(jo.optString("name"));
		ydmx2.setXmsx(xmsx);
		// 单价还是菜品原价
		ydmx2.setXmdj(new BigDecimal(jo.optString("price", "0")));
		ydmx2.setXmsl(number);
		ydmx2.setZkl(100);
		// 菜品实结金额
		ydmx2.setTotalprice(new BigDecimal((jo.optDouble("price", 0) + jo.optDouble("aprice", 0)) * number));
		ydmx2.setDwbh(jo.optString("dishunit"));
		ydmx2.setKwbh(jo.optString("remark"));
		// 菜品金额使用原价*数量
		ydmx2.setCmje(new BigDecimal((jo.optDouble("price", 0) + jo.optDouble("aprice", 0)) * number));
		ydmx2.setTcbh("");
		ydmx2.setTcdch(0);
		ydmx2.setFzsl(0);
		ydmx2.setDcxh(dcxh);
		ydmx2.setFzje(new BigDecimal(0));
		BigDecimal decimal = new BigDecimal((jo.optDouble("price", 0) + jo.optDouble("aprice", 0) + jo.optDouble("discountsprice", 0)) * number);
		BigDecimal setScale = decimal.setScale(2, BigDecimal.ROUND_HALF_UP);
		ydmx2.setYl3(setScale + "");
		
		return ydmx2;
	}

	public void insertPayments(String outOrderIdInDB, TsFkfssdk fkfs, double actualmoney, String addtime) {
		BtPayments btpay = new BtPayments();
		btpay.setYddh(outOrderIdInDB);
		btpay.setPay_channel("企迈小程序点餐");
		btpay.setPay_name(fkfs.getFkfsmc1());
		btpay.setPay_no(fkfs.getFkfsbh());
		btpay.setVcardid("");
		btpay.setVtele("");
		btpay.setPay_count(actualmoney);
		btpay.setPay_bb(0);
		btpay.setVocount(0);
		btpay.setFzzhje(0);
		btpay.setFlhl(0);
		btpay.setPay_memo("");
		btpay.setOpttime(addtime);
		// 生成预定付款记录
		firstPayMapper.insertBtPayments(btpay);
	}
	
	public boolean takeoutPackagingFee(JSONObject item, String kdzdbh, int number, String xsyh) {
		TsPsjgsdk psjgsdk = tsPsjgsdkMapper.findLocalShopConfig();
		String cmid = orderPrecheckMapper.getFoodBoxSetByCmid(item.optInt("did"));
		if (null != cmid && !"".equals(cmid)) {
			orderPrecheckMapper.addCmNewByZs(kdzdbh, Integer.parseInt(cmid), number, "99", xsyh, "", Math.random() * 10000 + "", "", psjgsdk.getJgxh() + "", psjgsdk.getJgtxbh(), "", 6);
			return true;
		} else {
			return false;
		}
	}
	
	@Transactional
	public Data checkClmx(JSONObject orderData) {
		// 创建返回数据对象
		Data data = new Data();
		// 小程序订单号
		String outOrderId = orderData.optString("out_order_id");
		JSONObject orderInfo = orderData.optJSONObject("order_info");		
		// 保存平台预定账单编号
		data.setYddbh(outOrderId);
		Map<Integer, Double> dishPrice = new HashMap<Integer, Double>();
		try {
			List<Integer> itemIdList = new ArrayList<Integer>();
			JSONArray normalitems = new JSONArray();
			if (orderInfo.has("normalitems")) {
				normalitems = orderInfo.getJSONArray("normalitems");
			}
			JSONArray setmeal = new JSONArray();
			if (orderInfo.has("setmeal")) {
				setmeal = orderInfo.getJSONArray("setmeal");
			}
			for (int i = 0; i < normalitems.size(); i++) {
				JSONObject item = normalitems.getJSONObject(i);
				itemIdList.add(item.optInt("did"));
				dishPrice.put(item.optInt("did"), item.optDouble("price"));
				
			}
			for (int i = 0; i < setmeal.size(); i++) {
				JSONObject item = setmeal.getJSONObject(i);
				itemIdList.add(item.optInt("did"));
				dishPrice.put(item.optInt("did"), item.optDouble("price"));
			}
			List<String> cmmcList = orderPrecheckMapper.checkClmx(itemIdList);
			if (cmmcList.size() > 0) {
				String itemName = "";
				for(String cmmc : cmmcList){
					itemName = itemName + "《" + cmmc + "》";
				}
				data.setSuccess(0);
				data.setMsg("菜品：" + itemName + "不在餐谱内，请重新同步基础数据;");
				data.setData(new HashMap<String, Object>());
			} else {
				List<Dish> dishList = orderPrecheckMapper.checkPrice(itemIdList);
				String priceMsg = "";
				for(Dish dish : dishList){
					double a = dishPrice.get(dish.getXmid());
					float  b = dish.getPrice();
					if ((float)a != b) {
						priceMsg = priceMsg + "《" + dish.getName() + "->pos:" + dish.getPrice() + ",xcx:" + dishPrice.get(dish.getXmid()) + "》";
					}
				}
				if (priceMsg.length() > 0) {
					data.setSuccess(0);
					data.setMsg("小程序菜品价格与pos不一致，请重新同步：" + priceMsg);
					data.setData(new HashMap<String, Object>());
				} else {
					data.setSuccess(1);
					data.setMsg("菜品无误");
					data.setData(new HashMap<String, Object>());
				}
			}
			
			return data;
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			data.setSuccess(0);
			data.setMsg("系统错误:" + e);
			data.setData(new HashMap<String, Object>());
			return data;
		}
	}
	
	
	public int insertSendAmount(String kdzdbh, double sendAmount, TsCmk saCmk, TqWdk tqwdk, String name) {
		if (null != saCmk && null != tqwdk) {
			TqWdk tqwdkIn = new TqWdk();
			tqwdkIn.setKdzdbh(kdzdbh);
			tqwdkIn.setClmxid(-1);
			tqwdkIn.setCmid(saCmk.getCmid());
			tqwdkIn.setCmbh(saCmk.getCmbh());
			tqwdkIn.setCmmc1(name);
			tqwdkIn.setDwbh(saCmk.getDwbh());
			tqwdkIn.setYzwbh("");
			tqwdkIn.setZwbh("");
			tqwdkIn.setTcfs("");
			tqwdkIn.setTcbl(0);
			tqwdkIn.setTcje(0);
			tqwdkIn.setFzsl(0);
			tqwdkIn.setFzje(0);
			tqwdkIn.setZdsj(0);
			tqwdkIn.setXdh("");
			tqwdkIn.setXdhshry("");
			tqwdkIn.setFwyh("");
			tqwdkIn.setCbdj(new BigDecimal(0));
			tqwdkIn.setCmdj(new BigDecimal(sendAmount));
			tqwdkIn.setCmsl(1);
			tqwdkIn.setCmje(new BigDecimal(sendAmount));
			tqwdkIn.setSjje(new BigDecimal(sendAmount));
			tqwdkIn.setYhje(new BigDecimal(0));
			tqwdkIn.setDpzkje(new BigDecimal(0));
			tqwdkIn.setZrje(new BigDecimal(0));
			tqwdkIn.setYhfs("");
			tqwdkIn.setZkzt(saCmk.getSfzk());
			tqwdkIn.setXlzkzt(saCmk.getSfzk());
			tqwdkIn.setZkl(100);
			tqwdkIn.setTszt("N");
			tqwdkIn.setQczt("N");
			tqwdkIn.setCmsx("CMSX_DP");
			tqwdkIn.setCdbj("*");
			tqwdkIn.setXlid(saCmk.getXlid());
			tqwdkIn.setXlbh(saCmk.getXlbh());
			tqwdkIn.setTcdch(0);
			tqwdkIn.setTmbj(new Date());
			tqwdkIn.setDcxh(tqwdk.getDcxh() + 1);
			tqwdkIn.setFsbbrq(tqwdk.getFsbbrq());
			tqwdkIn.setFsbcid(tqwdk.getFsbcid());
			tqwdkIn.setFsskjh(tqwdk.getFsskjh());
			tqwdkIn.setScbj(1);
			tqwdkIn.setYongjje(0);
			tqwdkIn.setYongjzkl(100);
			tqwdkIn.setSyyhfkfsid(tqwdk.getSyyhfkfsid() + 1);
			tqwdkIn.setWdbz("");
			tqwdkIn.setKwbz("");
			tqWdk.insert(tqwdkIn);
			return 0;
		} else {
			return -1;
		}
	}
	
	public void commUseYhfs(TqZdk zdk, List<TqFklslsk> fklslskList, String skjh, String jzbbrq) {
		for (int i = 0; i < fklslskList.size(); i++) {
			TqFklslsk fklslsk = fklslskList.get(i);
			if ("N".equals(fklslsk.getSfsr())) {
				TsYhfssdk yhfssdk = firstPayMapper.getBjyhYh("BJSR" + fklslsk.getFkfsbh());
				if (null != yhfssdk) {
					UseYhfsParam useYhfsParam = new UseYhfsParam();
					useYhfsParam.setBillid(zdk.getKdzdbh());
					useYhfsParam.setOpType(91003);
					useYhfsParam.setYhfsId(yhfssdk.getId());
					useYhfsParam.setFklsid(fklslsk.getId());
					useYhfsParam.setSkjh(skjh);// 机号
					useYhfsParam.setJgtxbh(InitDataListener.organVo.getJgtxbh());// 价格体系编号
					useYhfsParam.setDisAmount(new BigDecimal(fklslsk.getFkje()));
					useYhfsParam.setBbrq(DateUtil.parseDate(jzbbrq));
					if ((i + 1) == fklslskList.size() && 0 != zdk.getDslj()) {
						useYhfsParam.setDslj(new BigDecimal(zdk.getDslj()));
						useYhfsParam.setHasDslj(true);
					}
					useYhfsParam.setInnerCalMoney(true);// 由接口内部使用均摊及账单金额重计算

					useYhfsApiService.CommUseYhfs(useYhfsParam);	
				}
			}
		}
	}
	
	
	public void acewillDiscount(String zdbh, DishVo dsfyh, List<TqFklslsk> fklsList, double storedGivePays) {
		// 拆分部分
		for (TqFklslsk fkls : fklsList) {
			UseYhfsParam useYhfsParam = new UseYhfsParam();
			useYhfsParam.setBillid(zdbh);
			useYhfsParam.setOpType(91002);
			useYhfsParam.setYhfsId(Integer.parseInt(dsfyh.getYhfsid()));
			useYhfsParam.setJzid(fkls.getJzid());
			useYhfsParam.setSkjh(fkls.getSkjh());// 机号
			useYhfsParam.setJgtxbh(InitDataListener.organVo.getJgtxbh());// 价格体系编号
			useYhfsParam.setDisAmount(new BigDecimal(storedGivePays));
			useYhfsParam.setBbrq(fkls.getJzbbrq());
			useYhfsParam.setInnerCalMoney(true);// 由接口内部使用均摊及账单金额重计算
			useYhfsApiService.CommUseYhfs(useYhfsParam);
		}
	}
	
	/**
	 * 创建账单
	 * @return
	 */
	public Map<String, String> createBill(JSONObject orderInfo, String kdzdbh, String yddh, String bbrq, BillNoData billNoData) {
		Map<String, String> rMap = new HashMap<String, String>();
		
		String jtbh = "99";// 机台号

		int bcid = 0; // 班次id
		TqJtztk jtzt = firstPayMapper.getJtZtk(DateUtil.parseDate(bbrq));
		String ygdlcs = jtzt.getYgdlcs();
		String czybh = jtzt.getRybh();// 操作员编号

		TsGgcsk yyms = firstPayMapper.getGgcsToWs("MDYYMS");
		String zwbh = orderInfo.optString("tableno", "");
//		String qch = "";
		String qch = orderInfo.optString("meal_number", "");
		if (null != yyms && "3".equals(yyms.getSdnr())) {
			bcid = firstPayMapper.getBcid(DateUtil.parseDate(bbrq));
		} else {
			bcid = Integer.parseInt(jtzt.getYl1());
		}
		
		if(null == zwbh || "".equals(zwbh)){
//			qch = billNoData.getQch();
			zwbh = "";
		}

		String lsdh = billNoData.getLsdh();

		int ktbcid = bcid;

		TqZdk tqZdk = new TqZdk();
		tqZdk.setKdzdbh(kdzdbh);
		tqZdk.setLsdh(lsdh);
		tqZdk.setJzcs(0);
		tqZdk.setKtskjh(jtbh);
		tqZdk.setFwyh(czybh);
		tqZdk.setKtczry(czybh);
		tqZdk.setKtsj(new Date());
		tqZdk.setKdbbrq(DateUtil.parseDate(bbrq));
		tqZdk.setJzsx("ZDSX_WJ");
		tqZdk.setSource("XCX");
		tqZdk.setCbid(-1);
		tqZdk.setXfks(1);
		tqZdk.setDyzdcs(1);
		tqZdk.setYddh(yddh);
		// 处理是否预约标记
		if (orderInfo.optString("isDelayedMeal", "0").equals("1")) {
			tqZdk.setYl1("1");
			String yysj = orderInfo.optString("mealtime", "1990-01-01 00:00:00");
//			yysj = yysj.substring(0, 4) + "-" + yysj.substring(4, 6) + "-" + yysj.substring(6, 8) + " " + yysj.substring(8, 10) + ":"  + yysj.substring(10, 12) + ":00";
			tqZdk.setYl2(yysj);
		} else {
			tqZdk.setYl1("0");
			tqZdk.setYl2("2019-01-01 12:00:00");
		}
		if (orderInfo.optInt("diningWay", 1) == 2) {
			tqZdk.setXsms("XSMS_WM");
		} else {
			tqZdk.setXsms("XSMS_TS");
		}

		tqZdk.setYgdlcs(ygdlcs + "");
		tqZdk.setZkl(100);
		tqZdk.setYhfsbh(null);
		tqZdk.setKtbcid(ktbcid);
		tqZdk.setZdzt("");
		tqZdk.setQch(qch);
		tqZdk.setZwbh(zwbh);

		tqZdkMapper.insert(tqZdk);

		rMap.put("qch", qch);
		rMap.put("bcid", bcid + "");
		rMap.put("czybh", czybh);
		rMap.put("jtbh", jtbh);
		
		return rMap;
	}
	
	public boolean isNumeric(String str) {
		for (int i = str.length(); --i >= 0;) {
			if (!Character.isDigit(str.charAt(i))) {
				return false;
			}
		}
		return true;
	}
	
	@Transactional
	public Data orderPrecheckBefore(JSONObject orderData) {
		// 创建返回数据对象
		Data data = new Data();
		data.setSuccess(1);
		data.setMsg("验证通过！");
		JSONObject orderInfo = orderData.optJSONObject("order_info");
		JSONObject payInfos = orderData.optJSONObject("pay_info");

		try {
			double orderAmount = orderInfo.optDouble("order_amount", 0);
			double payAmount = 0;
			JSONArray payInfoArr = payInfos.optJSONArray("pay_info");
			for (int i = 0; i < payInfoArr.size(); i++) {
				JSONObject payInfo = payInfoArr.getJSONObject(i);
				double pAmount = payInfo.optDouble("amount");
				payAmount = ArithUtil.add(payAmount, pAmount);
			}

			if (orderAmount != payAmount) {
				data.setSuccess(0);
				data.setMsg("“账单金额”不等于“支付金额”，请联系管理员！");
				data.setData(new HashMap<String, Object>());
				return data;
			}

			if (orderInfo.has("normalitems")) {
				JSONArray normalitems = new JSONArray();
				normalitems = orderInfo.getJSONArray("normalitems");
				for (int i = 0; i < normalitems.size(); i++) {
					JSONObject item = normalitems.getJSONObject(i);
					TsCmk cmk = orderPrecheckMapper.getDishById(item.optInt("did"));
					if (null == cmk) {
						data.setSuccess(0);
						data.setMsg("菜品“" + item.optString("name") + "(" + item.optString("did") + ")”不存在，请联系管理员！");
						data.setData(new HashMap<String, Object>());
						return data;
					}
					if (!"CMSX_DP".equals(cmk.getCmsx())) {
						data.setSuccess(0);
						data.setMsg("菜品“" + item.optString("name") + "(" + item.optString("did") + ")”不是单品菜品，请联系管理员！");
						data.setData(new HashMap<String, Object>());
						return data;
					}
				}
			}

			if (orderInfo.has("setmeal")) {
				JSONArray setmeal = new JSONArray();
				setmeal = orderInfo.getJSONArray("setmeal");
				for (int i = 0; i < setmeal.size(); i++) {
					JSONObject item = setmeal.getJSONObject(i);
					TsCmk cmk = orderPrecheckMapper.getDishById(item.optInt("did"));
					if (null == cmk) {
						data.setSuccess(0);
						data.setMsg("菜品“" + item.optString("name") + "(" + item.optString("did") + ")”不存在，请联系管理员！");
						data.setData(new HashMap<String, Object>());
						return data;
					}
					if (!"CMSX_TC".equals(cmk.getCmsx())) {
						data.setSuccess(0);
						data.setMsg("菜品“" + item.optString("name") + "(" + item.optString("did") + ")”不是套餐菜品，请联系管理员！");
						data.setData(new HashMap<String, Object>());
						return data;
					}

				}
			}
			return data;
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			data.setSuccess(0);
			data.setMsg("系统错误:" + e);
			data.setData(new HashMap<String, Object>());
			return data;
		}
	}
}
