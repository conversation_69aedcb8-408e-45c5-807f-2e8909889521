package com.tzx.mobilepos.rest.vo;

import java.util.List;

public class ComboDetails {

	private String id;// 套餐明细id
	private String item_id;// 套餐id
	private String item_no;// 套餐编号
	private String item_name;// 套餐名称
	private String details_id;// 明细菜品id
	private String details_name;// 明细菜品名称
	private String price;// 实际价格
	private String item_count;// 菜品数量
	private String details_type;// 明细类型 单品：ERP_MXLX_SINGLE，项目组：ERP_MXLX_GROUP
	private String item_unit_name;// 单位（份，个，杯等等）
	private List<GroupDetails> groupDetails; // 菜品可选项
	private String foodboxid = "0";// 餐盒id
	private String taste_name;// 口味备注

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getItem_id() {
		return item_id;
	}

	public void setItem_id(String item_id) {
		this.item_id = item_id;
	}

	public String getItem_no() {
		return item_no;
	}

	public void setItem_no(String item_no) {
		this.item_no = item_no;
	}

	public String getItem_name() {
		return item_name;
	}

	public void setItem_name(String item_name) {
		this.item_name = item_name;
	}

	public String getDetails_id() {
		return details_id;
	}

	public void setDetails_id(String details_id) {
		this.details_id = details_id;
	}

	public String getPrice() {
		return price;
	}

	public void setPrice(String price) {
		this.price = price;
	}

	public String getItem_count() {
		return item_count;
	}

	public void setItem_count(String item_count) {
		this.item_count = item_count;
	}

	public String getDetails_type() {
		return details_type;
	}

	public void setDetails_type(String details_type) {
		this.details_type = details_type;
	}

	public String getDetails_name() {
		return details_name;
	}

	public void setDetails_name(String details_name) {
		this.details_name = details_name;
	}

	public String getItem_unit_name() {
		return item_unit_name;
	}

	public void setItem_unit_name(String item_unit_name) {
		this.item_unit_name = item_unit_name;
	}

	public List<GroupDetails> getGroupDetails() {
		return groupDetails;
	}

	public void setGroupDetails(List<GroupDetails> groupDetails) {
		this.groupDetails = groupDetails;
	}

	public String getFoodboxid() {
		return foodboxid;
	}

	public void setFoodboxid(String foodboxid) {
		this.foodboxid = foodboxid;
	}

	public String getTaste_name() {
		return taste_name;
	}

	public void setTaste_name(String taste_name) {
		this.taste_name = taste_name;
	}

}
