package com.tzx.miniapp.rest.vo;

/**
 * Optional
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-02-21
 */
public class Optional implements java.io.Serializable {

    private Integer cmid;
    /**
     * name	菜品名称	String	是
     */
    private String name;
    /**
     * id	菜品id	Int	是
     */
    private Integer id;
    /**
     * duid	单位id	-	是
     */
    private Integer duid;
    /**
     * dishsno	速记码	String	是
     */
    private String dishsno;
    /**
     * maxnum	最大选购数量	Int	是
     */
    private Integer maxnum;
    /**
     * aprice	加价	Int	是
     */
    private Integer aprice;
    /**
     * rpdid	分组ID	Int	否
     */
    private Integer rpdid;
    /**
     * dishToppingId	菜品配料id	Int	是
     */
    private Integer dishToppingId;
    /**
     * pkid	？？待确认	Int	是
     */
    private Integer pkid;
    /**
     * title	可选分组分组名称	String	是
     */
    private String title;
    /**
     * optionalMinnum	可选组最小选择份数	Int	是
     */
    private Integer optionalMinnum;
    /**
     * optionalMaxnum	可选组最大选择份数	Int	是
     */
    private Integer optionalMaxnum;
    /**
     * optionalHasTip	可选菜份数限制 未选是否提示 0否 1是	Int	是
     */
    private Integer optionalHasTip;

    public Integer getCmid() {
        return cmid;
    }

    public void setCmid(Integer cmid) {
        this.cmid = cmid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getDuid() {
        return duid;
    }

    public void setDuid(Integer duid) {
        this.duid = duid;
    }

    public String getDishsno() {
        return dishsno;
    }

    public void setDishsno(String dishsno) {
        this.dishsno = dishsno;
    }

    public Integer getMaxnum() {
        return maxnum;
    }

    public void setMaxnum(Integer maxnum) {
        this.maxnum = maxnum;
    }

    public Integer getAprice() {
        return aprice;
    }

    public void setAprice(Integer aprice) {
        this.aprice = aprice;
    }

    public Integer getRpdid() {
        return rpdid;
    }

    public void setRpdid(Integer rpdid) {
        this.rpdid = rpdid;
    }

    public Integer getDishToppingId() {
        return dishToppingId;
    }

    public void setDishToppingId(Integer dishToppingId) {
        this.dishToppingId = dishToppingId;
    }

    public Integer getPkid() {
        return pkid;
    }

    public void setPkid(Integer pkid) {
        this.pkid = pkid;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getOptionalMinnum() {
        return optionalMinnum;
    }

    public void setOptionalMinnum(Integer optionalMinnum) {
        this.optionalMinnum = optionalMinnum;
    }

    public Integer getOptionalMaxnum() {
        return optionalMaxnum;
    }

    public void setOptionalMaxnum(Integer optionalMaxnum) {
        this.optionalMaxnum = optionalMaxnum;
    }

    public Integer getOptionalHasTip() {
        return optionalHasTip;
    }

    public void setOptionalHasTip(Integer optionalHasTip) {
        this.optionalHasTip = optionalHasTip;
    }
}
