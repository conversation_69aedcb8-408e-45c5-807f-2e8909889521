package com.tzx.commapi.rest.service.impl;

import com.tzx.commapi.common.CommApiException;
import com.tzx.commapi.rest.mapper.CommApiMapper;
import com.tzx.commapi.rest.service.IUseYhfsApiService;
import com.tzx.commapi.rest.vo.CommApiData;
import com.tzx.commapi.rest.vo.UseYhfsParam;
import com.tzx.commapi.rest.vo.YHMTCouponsTemp;
import com.tzx.commapi.rest.vo.YhfsParam;
import com.tzx.receiver.common.utils.UUIDUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

/**
 * Created by <PERSON>x<PERSON> on 2020-05-08.
 */
@Service
public class UseYhfsApiService implements IUseYhfsApiService {
    private final static Logger LOGGER = LoggerFactory.getLogger(UseYhfsApiService.class);
    @Autowired
    private CommApiMapper commApiMapper;

    @Override
    @Transactional
    public CommApiData CommUseYhfs(UseYhfsParam useYhfsParam) {
        CommApiData apiData = new CommApiData();
        LOGGER.info(useYhfsParam.toString());
        String newCode = useYhfsParam.getYzm();
        if(null==useYhfsParam.getBbrq()){
            throw new CommApiException("报表日期为空");
        }
        if(null==useYhfsParam.getOpType()){
            throw new CommApiException("操作类型为空");
        }
        if(useYhfsParam.getOpType().equals(91002)){
            commApiMapper.updateFklsByJzid(useYhfsParam.getBillid(),useYhfsParam.getJzid(),useYhfsParam.getDisAmount());
            LOGGER.info("更新优惠拆分完毕");
            useYhfsParam.setOpType(1002);
        }
        else{
            LOGGER.info("不需要更新优惠拆分");
        }
        if(useYhfsParam.getOpType().equals(91003)){
            commApiMapper.updateFklsByFklsid(useYhfsParam.getBillid(),useYhfsParam.getFklsid());
            LOGGER.info("更新付款流水成功");
            newCode = useYhfsParam.getFklsid().toString();
            useYhfsParam.setOpType(1002);
        }
        else{
            LOGGER.info("更新付款流水成功");
        }
        if(useYhfsParam.getOpType().equals(1002)){
            YhfsParam yhfsParam = new YhfsParam();
            yhfsParam.setZdbh(useYhfsParam.getBillid());
            yhfsParam.setYhfsid(useYhfsParam.getYhfsId());
            yhfsParam.setYhsx("");
            yhfsParam.setSguid(newCode);
            yhfsParam.setBbrq(useYhfsParam.getBbrq());
            yhfsParam.setOptype(useYhfsParam.getOpType());
            yhfsParam.setSparam2(newCode);
            yhfsParam.setNparam1(new BigDecimal(0));
            if(useYhfsParam.getHasDslj()){
                yhfsParam.setNparam1(useYhfsParam.getDslj());
                yhfsParam.setSparam5("dslj");
            }
            commApiMapper.deleteYhfsParam(yhfsParam);
            commApiMapper.saveYhfsParam(yhfsParam);
            LOGGER.info("保存优惠活动参数成功");
        }
        else{
            LOGGER.info("不需要保存优惠活动参数");
        }
        if(useYhfsParam.getHasDslj()){
            commApiMapper.UpdateDsljByZdbh(useYhfsParam.getBillid());
        }
        YHMTCouponsTemp yhmtCouponsTemp = new YHMTCouponsTemp();
        yhmtCouponsTemp.setClmxid(-1);
        yhmtCouponsTemp.setKdzdbh(useYhfsParam.getBillid());
        yhmtCouponsTemp.setYhfsid(useYhfsParam.getYhfsId());
        yhmtCouponsTemp.setPqlx("2");
        yhmtCouponsTemp.setOptype(1);
        yhmtCouponsTemp.setYzm(UUIDUtil.generateGUID());
        yhmtCouponsTemp.setBuyprice(useYhfsParam.getDisAmount());
        commApiMapper.SaveYhMTcouponsTempItems(yhmtCouponsTemp);
        LOGGER.info("保存mtcoupontem成功");

        commApiMapper.AddThirdYhfs(useYhfsParam.getBillid(),-1, useYhfsParam.getYhfsId(),newCode
        ,useYhfsParam.getSkjh(),useYhfsParam.getJgtxbh());
        LOGGER.info("使用活动成功");

        if(useYhfsParam.getInnerCalMoney()){
            commApiMapper.ExecBillDiscountShare(useYhfsParam.getBillid());
            commApiMapper.CalcMoney(useYhfsParam.getBillid());
            LOGGER.info("均摊和重新计算金额成功");
        }
        apiData.setCode(0);
        apiData.setMsg("成功");
        return apiData;
    }

}
