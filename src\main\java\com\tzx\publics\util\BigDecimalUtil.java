package com.tzx.publics.util;

import java.math.BigDecimal;

/**
 * BigDecimalUtil
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-10-31
 */
public class BigDecimalUtil {

    public static final int DEFAULT_SCALE = 2;

    /**
     * 除法运算
     *
     * @param v1    被除数
     * @param v2    除数
     * @param scale 保留小数位数
     * @return
     */
    public static double divide(double v1, double v2, int scale) {
        BigDecimal b1 = BigDecimal.valueOf(v1);
        BigDecimal b2 = BigDecimal.valueOf(v2);
        return b1.divide(b2, scale, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    /**
     * 乘法运算
     *
     * @param v1 被乘数
     * @param v2 乘数
     * @return
     */
    public static double multiply(double v1, double v2) {
        BigDecimal b1 = BigDecimal.valueOf(v1);
        BigDecimal b2 = BigDecimal.valueOf(v2);
        return b1.multiply(b2).doubleValue();
    }

    /**
     * 乘法运算
     *
     * @param v1    被乘数
     * @param v2    乘数
     * @param scale 保留小数位数
     * @return
     */
    public static double multiply(double v1, double v2, int scale) {
        BigDecimal b1 = BigDecimal.valueOf(v1);
        BigDecimal b2 = BigDecimal.valueOf(v2);
        return b1.multiply(b2).setScale(scale, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    /**
     * 乘法运算，舍弃小数部分
     *
     * @param v1 被乘数
     * @param v2 乘数
     * @return
     */
    public static double multiplyScaleRoundDown(double v1, double v2) {

        BigDecimal b1 = BigDecimal.valueOf(v1);
        BigDecimal b2 = BigDecimal.valueOf(v2);

        final BigDecimal value = b1.multiply(b2);

        return value.setScale(DEFAULT_SCALE, BigDecimal.ROUND_DOWN).doubleValue();
    }

}


