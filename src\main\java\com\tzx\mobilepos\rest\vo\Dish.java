package com.tzx.mobilepos.rest.vo;

public class Dish {

	private String item_menu_id;// 餐类id-餐谱id
	private String details_id;// 餐类详细id-餐谱详细id
	private String item_id;// 菜品id
	private String item_no;// 菜品编号
	private String item_name;// 菜品名称
	private String item_english;// 菜品英文简码，无
	private String phonetic_code;// 拼音简码
	private String five_code;// 五笔简码
	private String price;// 菜品单价
	private String unit_id;// 规格id，无
	private String unit_name;// 规格名称
	private String start_time;// 开始时间
	private String end_time;// 结束时间
	private String start_date;// 开始日期
	private String end_date;// 结束日期
	private String sales_model;
	private String valid_state;
	private String item_class_id;
	private String itemclass_code;
	private String item_barcode;
	private String is_discount;
	private String is_pushmoney;
	private String pushmoney_way;
	private String proportion;
	private String is_modifyquantity;
	private String is_modifyname;
	private String is_runningprice;
	private String is_staffmeal;
	private String is_throwaway;
	private String is_seafood;
	private String is_staplefood;
	private String is_combo;// 是否套餐，套餐：CMSX_TC，单品：CMSX_DP
	private String is_characteristic;
	private String is_recommendation;
	private String combo_type;
	private String nutrition;
	private String suitable_crowds;
	private String unsuitable_crowds;
	private String processing_technic;
	private String summary;
	private String spicy;
	private String is_points;
	private String photo1;
	private String photo2;
	private String photo3;
	private String xlbh;
	private String xlmc;
	private String yhfsid;// 优惠方式id
	private String yhfsbh;// 优惠方式编号
	private String sfxsmx;// 是否显示明细
	private String picpath;
	private String number;
	private String daysOfWeek; //周几可以售卖
	private String vip_price;// 菜品会员价
	private String foodboxid = "0";// 餐盒id
	private String ifspec;// 是否多规格 N Y

	public String getItem_menu_id() {
		return item_menu_id;
	}

	public void setItem_menu_id(String item_menu_id) {
		this.item_menu_id = item_menu_id;
	}

	public String getDetails_id() {
		return details_id;
	}

	public void setDetails_id(String details_id) {
		this.details_id = details_id;
	}

	public String getItem_id() {
		return item_id;
	}

	public void setItem_id(String item_id) {
		this.item_id = item_id;
	}

	public String getItem_no() {
		return item_no;
	}

	public void setItem_no(String item_no) {
		this.item_no = item_no;
	}

	public String getItem_name() {
		return item_name;
	}

	public void setItem_name(String item_name) {
		this.item_name = item_name;
	}

	public String getItem_english() {
		return item_english;
	}

	public void setItem_english(String item_english) {
		this.item_english = item_english;
	}

	public String getPhonetic_code() {
		return phonetic_code;
	}

	public void setPhonetic_code(String phonetic_code) {
		this.phonetic_code = phonetic_code;
	}

	public String getFive_code() {
		return five_code;
	}

	public void setFive_code(String five_code) {
		this.five_code = five_code;
	}

	public String getPrice() {
		return price;
	}

	public void setPrice(String price) {
		this.price = price;
	}

	public String getUnit_id() {
		return unit_id;
	}

	public void setUnit_id(String unit_id) {
		this.unit_id = unit_id;
	}

	public String getUnit_name() {
		return unit_name;
	}

	public void setUnit_name(String unit_name) {
		this.unit_name = unit_name;
	}

	public String getSales_model() {
		return sales_model;
	}

	public void setSales_model(String sales_model) {
		this.sales_model = sales_model;
	}

	public String getValid_state() {
		return valid_state;
	}

	public void setValid_state(String valid_state) {
		this.valid_state = valid_state;
	}

	public String getItem_class_id() {
		return item_class_id;
	}

	public void setItem_class_id(String item_class_id) {
		this.item_class_id = item_class_id;
	}

	public String getItemclass_code() {
		return itemclass_code;
	}

	public void setItemclass_code(String itemclass_code) {
		this.itemclass_code = itemclass_code;
	}

	public String getItem_barcode() {
		return item_barcode;
	}

	public void setItem_barcode(String item_barcode) {
		this.item_barcode = item_barcode;
	}

	public String getIs_discount() {
		return is_discount;
	}

	public void setIs_discount(String is_discount) {
		this.is_discount = is_discount;
	}

	public String getIs_pushmoney() {
		return is_pushmoney;
	}

	public void setIs_pushmoney(String is_pushmoney) {
		this.is_pushmoney = is_pushmoney;
	}

	public String getPushmoney_way() {
		return pushmoney_way;
	}

	public void setPushmoney_way(String pushmoney_way) {
		this.pushmoney_way = pushmoney_way;
	}

	public String getProportion() {
		return proportion;
	}

	public void setProportion(String proportion) {
		this.proportion = proportion;
	}

	public String getIs_modifyquantity() {
		return is_modifyquantity;
	}

	public void setIs_modifyquantity(String is_modifyquantity) {
		this.is_modifyquantity = is_modifyquantity;
	}

	public String getIs_modifyname() {
		return is_modifyname;
	}

	public void setIs_modifyname(String is_modifyname) {
		this.is_modifyname = is_modifyname;
	}

	public String getIs_runningprice() {
		return is_runningprice;
	}

	public void setIs_runningprice(String is_runningprice) {
		this.is_runningprice = is_runningprice;
	}

	public String getIs_staffmeal() {
		return is_staffmeal;
	}

	public void setIs_staffmeal(String is_staffmeal) {
		this.is_staffmeal = is_staffmeal;
	}

	public String getIs_throwaway() {
		return is_throwaway;
	}

	public void setIs_throwaway(String is_throwaway) {
		this.is_throwaway = is_throwaway;
	}

	public String getIs_seafood() {
		return is_seafood;
	}

	public void setIs_seafood(String is_seafood) {
		this.is_seafood = is_seafood;
	}

	public String getIs_staplefood() {
		return is_staplefood;
	}

	public void setIs_staplefood(String is_staplefood) {
		this.is_staplefood = is_staplefood;
	}

	public String getIs_combo() {
		return is_combo;
	}

	public void setIs_combo(String is_combo) {
		this.is_combo = is_combo;
	}

	public String getIs_characteristic() {
		return is_characteristic;
	}

	public void setIs_characteristic(String is_characteristic) {
		this.is_characteristic = is_characteristic;
	}

	public String getIs_recommendation() {
		return is_recommendation;
	}

	public void setIs_recommendation(String is_recommendation) {
		this.is_recommendation = is_recommendation;
	}

	public String getCombo_type() {
		return combo_type;
	}

	public void setCombo_type(String combo_type) {
		this.combo_type = combo_type;
	}

	public String getNutrition() {
		return nutrition;
	}

	public void setNutrition(String nutrition) {
		this.nutrition = nutrition;
	}

	public String getSuitable_crowds() {
		return suitable_crowds;
	}

	public void setSuitable_crowds(String suitable_crowds) {
		this.suitable_crowds = suitable_crowds;
	}

	public String getUnsuitable_crowds() {
		return unsuitable_crowds;
	}

	public void setUnsuitable_crowds(String unsuitable_crowds) {
		this.unsuitable_crowds = unsuitable_crowds;
	}

	public String getProcessing_technic() {
		return processing_technic;
	}

	public void setProcessing_technic(String processing_technic) {
		this.processing_technic = processing_technic;
	}

	public String getSummary() {
		return summary;
	}

	public void setSummary(String summary) {
		this.summary = summary;
	}

	public String getSpicy() {
		return spicy;
	}

	public void setSpicy(String spicy) {
		this.spicy = spicy;
	}

	public String getIs_points() {
		return is_points;
	}

	public void setIs_points(String is_points) {
		this.is_points = is_points;
	}

	public String getPhoto1() {
		return photo1;
	}

	public void setPhoto1(String photo1) {
		this.photo1 = photo1;
	}

	public String getPhoto2() {
		return photo2;
	}

	public void setPhoto2(String photo2) {
		this.photo2 = photo2;
	}

	public String getPhoto3() {
		return photo3;
	}

	public void setPhoto3(String photo3) {
		this.photo3 = photo3;
	}

	public String getXlbh() {
		return xlbh;
	}

	public void setXlbh(String xlbh) {
		this.xlbh = xlbh;
	}

	public String getXlmc() {
		return xlmc;
	}

	public void setXlmc(String xlmc) {
		this.xlmc = xlmc;
	}

	public String getYhfsid() {
		return yhfsid;
	}

	public void setYhfsid(String yhfsid) {
		this.yhfsid = yhfsid;
	}

	public String getYhfsbh() {
		return yhfsbh;
	}

	public void setYhfsbh(String yhfsbh) {
		this.yhfsbh = yhfsbh;
	}

	public String getSfxsmx() {
		return sfxsmx;
	}

	public void setSfxsmx(String sfxsmx) {
		this.sfxsmx = sfxsmx;
	}

	public String getPicpath() {
		return picpath;
	}

	public void setPicpath(String picpath) {
		this.picpath = picpath;
	}

	public String getStart_time() {
		return start_time;
	}

	public void setStart_time(String start_time) {
		this.start_time = start_time;
	}

	public String getEnd_time() {
		return end_time;
	}

	public void setEnd_time(String end_time) {
		this.end_time = end_time;
	}

	public String getStart_date() {
		return start_date;
	}

	public void setStart_date(String start_date) {
		this.start_date = start_date;
	}

	public String getEnd_date() {
		return end_date;
	}

	public void setEnd_date(String end_date) {
		this.end_date = end_date;
	}

	public String getNumber() {
		return number;
	}

	public void setNumber(String number) {
		this.number = number;
	}

	public String getDaysOfWeek() {
		return daysOfWeek;
	}

	public void setDaysOfWeek(String daysOfWeek) {
		this.daysOfWeek = daysOfWeek;
	}

	public String getVip_price() {
		return vip_price;
	}

	public void setVip_price(String vip_price) {
		this.vip_price = vip_price;
	}

	public String getFoodboxid() {
		return foodboxid;
	}

	public void setFoodboxid(String foodboxid) {
		this.foodboxid = foodboxid;
	}

	public String getIfspec() {
		return ifspec;
	}

	public void setIfspec(String ifspec) {
		this.ifspec = ifspec;
	}

}
