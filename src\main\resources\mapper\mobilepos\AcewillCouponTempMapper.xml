<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.mobilepos.rest.mapper.MobilePosAcewillCouponTempMapper">
	<select id="getFkfsid" resultType="java.lang.Integer">
		select id FROM ts_fkfssdk where fkfsbh = #{fkfsbh} order by id limit 1
	</select>

	<select id="getWdDish" resultType="com.tzx.mobilepos.rest.vo.WdDishVo" >
		select rwid as wdrwid,clmxid,cmmc1,cmsl,sjje,cmbh,cmdj from tq_wdk where kdzdbh = #{zdbh} and cmbh = #{cmbh} and wdbz &lt;&gt; 'WDBZ_FS'
		and wdbz &lt;&gt; 'WDBZ_MD' and wdbz &lt;&gt; 'WDBZ_SS' and wdbz &lt;&gt; 'WDBZ_TC'
		and wdbz &lt;&gt; 'WDBZ_QX' and (cmsx isnull or cmsx &lt;&gt; 'CMSX_MX') and yhfsid is null limit 1
	</select>

	<select id="getZdje" resultType="java.lang.Double" >
		select fkje from tq_zdk where kdzdbh = #{zdbh}
	</select>

	<delete id="delTwct">
		delete from tq_acewil_coupon_cache where zdbh = #{zdbh}
	</delete>

	<delete id="delTmi">
		delete from tq_memberinfo where billid = #{zdbh}
	</delete>

	<delete id="delFkls">
		delete from tq_fklslsk where kdzdbh = #{zdbh} and fklxsx = #{fklxsx}
	</delete>

	<select id="getCouponByCode" resultType="com.tzx.mobilepos.rest.model.TqAcewilCouponCache" >
		select * from tq_acewil_coupon_cache where zdbh = #{zdbh} and couponcode = #{couponcode} and is_usable = 0
	</select>

	<select id="getCouponUseCount" resultType="java.util.Map" >
		<!-- 		select count(pid) as counts from tq_acewil_coupon_cache  -->
		<!-- 		where zdbh = #{zdbh} and useok = 1 and is_usable = 0  -->
		<!-- 		<if test='t == "1"'> -->
		<!-- 			and templateid &lt;&gt; #{templateid} -->
		<!-- 		</if> -->
		<!-- 		<if test='t == "2"'> -->
		<!-- 			and templateid = #{templateid} -->
		<!-- 		</if> -->
		select count(pid) as counts, remark from tq_acewil_coupon_cache
		where datatype = #{datatype} and zdbh = #{zdbh} and useok = 1 and is_usable = 0
		<if test='t == "1"'>
			and templateid &lt;&gt; #{templateid}
		</if>
		<if test='t == "2"'>
			and templateid = #{templateid}
		</if>
		group by remark
	</select>

	<select id="getCouponPrice" resultType="java.lang.Double" >
		select coalesce(sum(couponprice),  0) as yhje from tq_acewil_coupon_cache 
		where zdbh = #{zdbh} and useok = 1 and is_usable = 0 and coupontype &lt;&gt; 'DISHDISCOUNTCOUPON'
	</select>
	
	<select id="getCountCouponPrice" resultType="java.lang.Double" >
		select coalesce(sum(coupon_totalmoney),  0) as yhje from tq_wdk_coupon_temp 
		where zdbh = #{zdbh} and useok = 1 and is_usable = 0 and coupontype = 'DISHDISCOUNTCOUPON'
	</select>

	<update id="updateUsaByCardcode">
		update tq_acewil_coupon_cache set is_usable = #{usable} where zdbh = #{zdbh} and cardcode &lt;&gt; #{cardcode}
	</update>

	<select id="getAcewillCoupons" resultType="com.tzx.mobilepos.rest.model.TqAcewilCouponCache" >
		select * from tq_acewil_coupon_cache where zdbh = #{zdbh} and cardcode = #{cardcode} order by pid
	</select>

	<update id="updateRwid">
		update tq_acewil_coupon_cache set rwid = #{rwid}, coupondishcode = #{cmbh} where zdbh = #{zdbh} and couponcode = #{couponcode}
	</update>

	<select id="getRefundRwid" resultType="java.lang.Integer" >
		select * from tq_wdk where kdzdbh = #{zdbh} and yhfsid = #{yhfsid} and cmsx = ''
		and wdbz &lt;&gt; 'WDBZ_FS' and wdbz &lt;&gt; 'WDBZ_MD' and wdbz &lt;&gt; 'WDBZ_SS'
		and wdbz &lt;&gt; 'WDBZ_TC' and wdbz &lt;&gt; 'WDBZ_QX' and (cmsx isnull or cmsx &lt;&gt; 'CMSX_MX')
	</select>

	<select id="getCouponByUseok" resultType="com.tzx.mobilepos.rest.model.TqAcewilCouponCache" >
<!-- 		select * from tq_acewil_coupon_cache where zdbh = #{zdbh} and  useok = #{useok} -->
		select * from tq_wdk_coupon_temp where zdbh = #{zdbh} and  useok = #{useok}
	</select>

	<select id="findZdAmount" resultType="java.lang.Double" >
		select coalesce(fkje, 0) from tq_zdk where kdzdbh = #{zdbh}
	</select>

	<select id="findPaymentAmount" resultType="java.lang.Double" >
		select coalesce(sum(fkls.fkje), 0) as payment_amount from tq_fklslsk fkls left join ts_fkfssdk fksd on fkls.jzid = fksd.id where fkls.kdzdbh = #{zdbh} and fksd.sfjf = 'Y'
	</select>

	<select id="findProducts" resultType="com.tzx.mobilepos.rest.vo.AcewillProductsVo" >
<!-- 		select wd.cmmc1 as name, wd.cmbh as no, sum (wd.cmsl) AS num, wt.couponprice * 100 as price, 1 as is_activity, wt.couponcode   -->
<!--         from tq_wdk wd left join tq_acewil_coupon_cache wt  on wd.rwid = wt.rwid and wt.coupontype = 'DISHCOUPON' and wt.useok = 1 -->
<!--         where wd.kdzdbh = #{zdbh} and (wd.cmsx = 'CMSX_DP' or wd.cmsx = 'CMSX_TC') -->
<!--         group by wd.cmmc1, wd.cmbh, (wt.couponprice * 100), wt.couponcode -->

<!--         with cpmx_ls as ( -->
<!-- 		select wd.cmbh as no, wd.cmmc1 as name, sum (wd.cmsl) as num, wt.couponprice * 100 as price, wt.couponcode -->
<!-- 		from tq_wdk wd left join tq_acewil_coupon_cache wt on wd.rwid = wt.rwid and wt.coupontype = 'DISHCOUPON' and wt.useok = 1 -->
<!-- 		where wd.kdzdbh = #{zdbh} and wd.cmsx in ('CMSX_DP', 'CMSX_TC') group by wd.cmmc1, wd.cmbh, (wt.couponprice * 100), wt.couponcode)  -->
<!-- 		select coalesce (a.no, b.no) as no, coalesce (a.name, b.name) as name, coalesce (a.num, 0) + coalesce (b.num, 0) as num, a.price, 1 as is_activity, a.couponcode  -->
<!-- 		from (select a.no, a.name, a.num, a.price, a.couponcode, row_number () over (partition by a.no order by a.num desc, a.couponcode) as xh from cpmx_ls a where a.couponcode is not null) a -->
<!-- 		full outer join (select a.no, a.name, a.num, a.price, a.couponcode from cpmx_ls a where a.couponcode is null) b on b.no = a.no and a .xh = 1 -->
		
		with cpmx_ls as (
		select wd.cmbh as no, wd.cmmc1 as name, sum (wd.cmsl) as num,coalesce (wt.couponprice * 100, wd.cmdj * 100) AS price, coalesce (wt.couponcode, wd.pqhm) as couponcode
		from tq_wdk wd left join tq_acewil_coupon_cache wt on wd.rwid = wt.rwid and wt.coupontype = 'DISHCOUPON' and wt.useok = 1
		where wd.kdzdbh = #{zdbh} and wd.cmsx in ('CMSX_DP', 'CMSX_TC') group by wd.cmmc1, wd.cmbh, (wt.couponprice * 100, wd.cmdj * 100), coalesce (wt.couponcode, wd.pqhm)) 
		select coalesce (a.no, b.no) as no, coalesce (a.name, b.name) as name, coalesce (a.num, 0) + coalesce (b.num, 0) as num, coalesce (a.price, b.price) as price, 1 as is_activity, a.couponcode 
		from (select a.no, a.name, a.num, a.price, a.couponcode, row_number () over (partition by a.no order by a.num desc, a.couponcode) as xh from cpmx_ls a where a.couponcode is not null) a
		full outer join (select a.no, a.name, a.num, a.price, a.couponcode from cpmx_ls a where a.couponcode is null) b on b.no = a.no and a .xh = 1
	</select>

	<delete id="delDsfls">
		delete from tq_zddsflsk where kdzdbh = #{zdbh} and yhfs = #{yhfs}
	</delete>

	<insert id="insertTqZddsflsk">
		insert into tq_zddsflsk(skjh,kdzdbh,kdbbrq,czsj,yhfs,qrcode,czlx,scbj,yl3,remark)
		values(#{skjh},#{kdzdbh},#{kdbbrq},#{czsj},#{yhfs},#{qrcode},#{czlx},#{scbj},#{yl3},#{remark});
	</insert>

	<select id="findZddsflsk" resultType="java.util.Map" >
		select qrcode,yl3,remark from tq_zddsflsk where kdzdbh = #{zdbh} and yhfs = #{yhfs} 
		<if test="yl3 != null and yl3 != ''">
			and yl3 = #{yl3} 
		</if>
		order by id desc limit 1
	</select>

	<update id="updateZddsflsk">
		update tq_zddsflsk set yl2 = #{yl2}, yl3 = #{yl3}
		<if test="remark != null and remark != ''">
			, remark = #{remark}
		</if>
		where kdzdbh = #{zdbh} and yhfs = #{yhfs}
	</update>

	<update id="updateUsaByUseok">
		update tq_acewil_coupon_cache set useok = #{useok} where zdbh = #{zdbh} 
		<if test="couponcode != null and couponcode != ''">
			and couponcode = #{couponcode} 
		</if>
	</update>	
	
	<select id="checkFkls" resultType="com.tzx.mobilepos.rest.vo.PayMentVo" >
		select ls.fklxsx, ls.jzid from tq_fklslsk ls left join ts_fkfssdk sd on ls.jzid = sd.id where kdzdbh = #{zdbh} and sd.fkfsbh = #{fkfsbh} limit 1 
	</select>

	<select id="getDiscountRwid" resultType="java.lang.String" >
		select wd.rwid from tq_fklslsk ls left join tq_wdk wd on wd.kdzdbh = ls.kdzdbh and wd.pqhm = ls.fkhm 
		where ls.kdzdbh = #{zdbh} and ls.id = #{fklsid} and wd.clmxid = -1 
	</select>
	
	<select id="getIsVipPriceCount" resultType="java.lang.Integer">
		select count(rwid) from tq_wdk where kdzdbh = #{kdzdbh} and yhfsid = -103
	</select>
	
	<select id="getWdDishDiscount" resultType="com.tzx.mobilepos.rest.vo.WdDishVo" >
		select rwid as wdrwid,clmxid,cmmc1,cmsl,sjje,cmbh,cmdj from tq_wdk where kdzdbh = #{zdbh} 
		<if test="products != null and products.size() > 0">
			and cmbh in
			<foreach item="item" index="index" collection="products" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="productsExt != null and productsExt.size() > 0">
			and cmbh not in
			<foreach item="item" index="index" collection="productsExt" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		and wdbz &lt;&gt; 'WDBZ_FS' and wdbz &lt;&gt; 'WDBZ_MD' and wdbz &lt;&gt; 'WDBZ_SS' and wdbz &lt;&gt; 'WDBZ_TC'
		and wdbz &lt;&gt; 'WDBZ_QX' and (cmsx isnull or cmsx &lt;&gt; 'CMSX_MX') and yhfsid is null
	</select>
	
	<insert id="insertWdAndYh">
		insert into tq_wdandyhtempitem(yhfsid,clmxid,kdzdbh,isneedadd,cmsl,usetag,rwid) 
		select #{yhfsid}, clmxid, kdzdbh, 1, cmsl, 1, rwid from tq_wdk where kdzdbh = #{zdbh} 
		<if test="products != null and products.size() > 0">
			and cmbh in
			<foreach item="item" index="index" collection="products" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="productsExt != null and productsExt.size() > 0">
			and cmbh not in
			<foreach item="item" index="index" collection="productsExt" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		and wdbz &lt;&gt; 'WDBZ_FS' and wdbz &lt;&gt; 'WDBZ_MD' and wdbz &lt;&gt; 'WDBZ_SS' and wdbz &lt;&gt; 'WDBZ_TC'
		and wdbz &lt;&gt; 'WDBZ_QX' and (cmsx isnull or cmsx &lt;&gt; 'CMSX_MX') and yhfsid is null
	</insert>
	
	<select id="getWdAndYhSize" resultType="java.lang.Integer">
		select count(kdzdbh) from tq_wdandyhtempitem where kdzdbh = #{zdbh} 
	</select>
	
	<insert id="insertAcewilDealdetails">
		insert into tq_acewil_dealdetails(cardno, billno, amount, balance, credit, bbrq, fwyh, skjh, create_time, canuse,membercontext)
		values(#{cardno}, #{billno}, #{amount}, #{balance}, #{credit}, #{bbrq}, #{fwyh}, #{skjh}, now(), #{canuse},#{dtl});
	</insert>
	
	<insert id="delAcewilDealdetails">
		delete from tq_acewil_dealdetails where billno = #{zdbh}
	</insert>

	<select id="getMemberPromotionAmount" resultType="double">
		select COALESCE(sum(zrje),0) from tq_wdk where kdzdbh=#{kdzdbh} and COALESCE(rule_id,'') ='' and COALESCE(yhfs,'') != ''
    </select>

	<update id="updateTqZdkAfterShare" >
		UPDATE tq_zdk
		SET yhje = w.zrje + w.dpzkje + w.yhje,
			zrje = w.zrje,
			dpzkje = w.dpzkje
		FROM
			(
				SELECT
					SUM ( zrje ) zrje,
					SUM ( dpzkje ) dpzkje,
					SUM ( yhje ) yhje
				FROM
					tq_wdk
				WHERE
					kdzdbh = #{kdzdbh}
				  AND COALESCE ( yhfs, '' ) != ''
			) w
		WHERE
			kdzdbh = #{kdzdbh};

		UPDATE tq_zdk
		SET fkje = f.fkje
		FROM
				( SELECT SUM ( fkje ) fkje FROM tq_fklslsk WHERE kdzdbh = #{kdzdbh} ) f
		WHERE
			kdzdbh = #{kdzdbh}
	</update>

	<select id="selectFkjeAndSjjeByKzdzbh" resultType="java.util.Map">
		SELECT
			z.fkje ,
			SUM(w.sjje) AS sjje
		FROM
			tq_zdk z
				LEFT JOIN
			tq_wdk w
			ON
				z.kdzdbh = w.kdzdbh
		WHERE
			z.kdzdbh =  #{kdzdbh}
		AND COALESCE(cmsx,'') != 'CMSX_MX'
		GROUP BY
			z.fkje
	</select>
</mapper>
