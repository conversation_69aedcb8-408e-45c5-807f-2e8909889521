<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.mobilepos.rest.mapper.MobilePosTsBckMapper">
	<select id="findUsefulShift" resultType="com.tzx.mobilepos.rest.vo.ShiftData">
<!-- 		select id,bcbh shiftCode,bcmc1 "name",kssj startTime,jssj endTime from ts_bck where bczt = 'Y' -->
		select bck.id, bck.bcbh as shiftcode, bck.bcmc1 as name, bck.kssj as starttime, bck.jssj as endtime
		from ts_bck bck where bck.id not in 
		(select to_number(ygdlcs, '9999999999999999999') from ts_ygsqlsk where bbrq=#{bbrq}  and czsx like &apos;%转桌确认%&apos;)
	</select>
</mapper>