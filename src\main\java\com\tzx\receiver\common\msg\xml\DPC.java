package com.tzx.receiver.common.msg.xml;

public class DPC {
	
	public static final String SUTF8Encoding = "UTF-8";

	public static final String SGb2312Encoding = "gb2312";
	public static final String SGbGBKEncoding = "GBK";

	public static final String SDocumentNodeName = "DATAPACKET";

	public static final String SHeadNodeStr = "HEAD";

	public static final String SMsgNodeStr = "MSG";

	public static final String SHeadVersionNodeStr = "Version";

	public static final String SHeadSenderIDNodeStr = "SenderID";

	public static final String SHeadReceiverIDNodeStr = "ReceiverID";

	public static final String SHeadOperatorIDNodeStr = "OperatorID";
	
	public static final String SMsgSubSystemNodeStr = "SUBSYSTEM";
	
	public static final String SMsgActionNodeStr = "ACTION";

	public static final String SMsgDatasNodeStr = "DATAS";

	public static final String SMsgDataNodeStr = "DATA";

	public static final String SMsgNameAttrStr = "Name";

	public static final String SMsgMetaDataNodeStr = "METADATA";

	public static final String SMsgMetaDataFieldsNodeStr = "FIELDS";

	public static final String SMsgMetaDataFieldNodeStr = "FIELD";

	public static final String SMsgRowDataNodeStr = "ROWDATA";

	public static final String SMsgRowNodeStr = "ROW";
	
	public static final char XPathDelim = '/';
	
	public static final String SNotFoundDataElement = "未找到名称为“%s”的数据元素";
	
	public static final String SMsgPersonnelNOdeStr = "PERSONNEL";
	public static final String SMsgVerNOdeStr = "VER";
	public static final String SMsgSrcNOdeStr = "SRC";
	public static final String SMsgDesNOdeStr = "DES";
	public static final String SMsgDevidNOdeStr = "DEVID";
	public static final String SMsgAppNOdeStr = "APP";
	public static final String SMsgTidNOdeStr = "TID";
	public static final String SMsgMsgidNOdeStr = "MSGID";
	public static final String SMsgCoridNOdeStr = "CORID";
	public static final String SMsgWorkdateNOdeStr = "WORKDATE";
	public static final String SMsgReserveNOdeStr = "RESERVE";

}
