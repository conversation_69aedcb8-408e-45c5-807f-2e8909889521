package com.tzx.commapi.rest.mapper;

import com.tzx.commapi.rest.vo.*;
import com.tzx.mobilepos.rest.model.TqJtztk;
import com.tzx.mobilepos.rest.model.TqZdk;
import com.tzx.mobilepos.rest.model.TsBck;
import com.tzx.publics.base.MyMapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface XinShangTieApiMapper {
    public List<TqThirdTempOrder> getTqThirdTempOrder(@Param("dataType") String dataType, @Param("startTime") String startTime, @Param("count") int count);


}
