package com.tzx.mobilepos.rest.service.impl;

import java.math.BigDecimal;
import java.util.*;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.tzx.commapi.rest.service.IUseYhfsApiService;
import com.tzx.commapi.rest.vo.UseYhfsParam;
import com.tzx.mobilepos.common.Constant;
import com.tzx.mobilepos.common.Data;
import com.tzx.mobilepos.rest.mapper.MobilePosAcewillCouponTempMapper;
import com.tzx.mobilepos.rest.mapper.MobilePosAcewillMemberinfoMapper;
import com.tzx.mobilepos.rest.mapper.MobilePosTqJtztkMapper;
import com.tzx.mobilepos.rest.mapper.MobilePosTqYhMtCouponsTempMapper;
import com.tzx.mobilepos.rest.mapper.MobilePosTqZdkMapper;
import com.tzx.mobilepos.rest.model.TqAcewilCouponCache;
import com.tzx.mobilepos.rest.model.TqFklslsk;
import com.tzx.mobilepos.rest.model.TqMemberInfo;
import com.tzx.mobilepos.rest.model.TqYhMtCouponsTemp;
import com.tzx.mobilepos.rest.model.TqZdk;
import com.tzx.mobilepos.rest.model.TsPsjgsdk;
import com.tzx.mobilepos.rest.service.IMobilePosAcewillMemberService;
import com.tzx.mobilepos.rest.vo.AcewillProductsVo;
import com.tzx.mobilepos.rest.vo.CouponRunningWater;
import com.tzx.mobilepos.rest.vo.Dish;
import com.tzx.mobilepos.rest.vo.PayResultData;
import com.tzx.mobilepos.rest.vo.WdDishVo;
import com.tzx.publics.listener.InitDataListener;
import com.tzx.publics.util.ArithUtil;
import com.tzx.publics.util.DateUtil;
import com.tzx.publics.util.DoubleUtil;
import com.tzx.publics.util.GsonUtil;
import com.tzx.publics.util.HttpClientUtil;
import com.tzx.publics.util.ParamUtil;
import com.tzx.publics.util.ReqDataUtil;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

@Service
public class MobilePosAcewillMemberServiceImpl implements IMobilePosAcewillMemberService {
	private final static Logger LOGGER = LoggerFactory.getLogger(MobilePosAcewillMemberServiceImpl.class);

	@Autowired
	private MobilePosTqZdkMapper tqZdkMapper;
	@Autowired
	private MobilePosAcewillCouponTempMapper acewillCouponTempMapper;
	@Autowired
	private MobilePosAcewillMemberinfoMapper acewillMemberinfoMapper;
	@Autowired
	private MobilePosTqJtztkMapper tqJtztkMapper;
	@Autowired
	private MobilePosTqYhMtCouponsTempMapper tqYhMtCouponsTempMapper;
	@Autowired
    private IUseYhfsApiService useYhfsApiService;

	public void findAcewillMember(Data data, Data result) {
		Map<String, Object> map = ReqDataUtil.getDataMap(data);
		String zdbh = ParamUtil.getStringValue(map, "zdbh", false, null);// 帐单编号
		String cno = ParamUtil.getStringValue(map, "cno", false, null);// 电子卡号、实体卡号或手机号
		acewillUserAccount(result, zdbh, cno);
	}

	/**
	 * 根据电子卡号、实体卡号或手机号获取用户的账户信息
	 * @param result
	 * @param zdbh
	 * @param cno
	 */
	public void acewillUserAccount(Data result, String zdbh, String cno) {
		List<JSONObject> dataList = new ArrayList<JSONObject>();
		TsPsjgsdk tsPsjgsdk = tqZdkMapper.getJg();// 机构数据
//		String rifUrl = tqZdkMapper.getRifUrl();
		Map<String, String> reqMap = new HashMap<String, String>();
		JSONObject obj = new JSONObject();
		reqMap.put("organ_id", tsPsjgsdk.getJgxh() + "");// 机构序号
		reqMap.put("type", "query");// 接口
		obj.put("cno", cno);// 电子卡号、实体卡号或手机号,动态二维码什么的
		reqMap.put("data", obj.toString());// 请求数据
//		rifUrl = rifUrl + "/acewillMemberAction.action";// 请求地址
		String rifUrl = acewillMemberinfoMapper.getACERifUrl();
		Map<String, String> map = HttpClientUtil.postSSLUrlWithParams(rifUrl, reqMap);// 请求获取返回数据
		String rStr = "";
		if ("0".equals(map.get("success"))) {
			rStr = map.get("data");
		} else {
			result.setCode(-1);
			result.setMsg(map.get("msg"));
			result.setSuccess(false);
			return;
		}
		int fkfsid = acewillCouponTempMapper.getFkfsid("2010");

		// 只要是重新查询了，不管查询结果如何，先清了临时表再说
		acewillCouponTempMapper.delTmi(zdbh);
		acewillCouponTempMapper.delTwct(zdbh);
		acewillCouponTempMapper.delDsfls(zdbh, "ACEWILL");
		tqZdkMapper.delTpd(zdbh, -100, "");
		
		JSONObject ua = JSONObject.fromObject(rStr);
		if ("200".equals(ua.optString("code"))) {
			JSONObject data = ua.optJSONObject("data");
			if (0 == data.optInt("errcode")) {
				JSONArray res = data.optJSONArray("res");
//				List<WdDishVo> wdvs = acewillCouponTempMapper.getWdDish(zdbh); // 卧单数据
				double fkje = acewillCouponTempMapper.getZdje(zdbh); // 付款金额
				for (int i = 0; i < res.size(); i++) {
					JSONObject resObj = res.optJSONObject(i);
					JSONObject card = new JSONObject();
					card.put("cno", resObj.optString("cno")); // 卡号
					card.put("balance", resObj.optString("balance")); // 卡余额
					card.put("name", resObj.optString("name")); // 用户名称
					card.put("type", resObj.optString("type")); // 卡类型，wx:微信卡 dp:点评卡 ph:实体卡 actual:实体卡
					card.put("in_effect", resObj.optString("in_effect")); // 卡是否有效
					card.put("grade_name", resObj.optString("grade_name")); // 卡等级名称
					card.put("is_vip_price", resObj.optString("is_vip_price")); // 是否使用会员价
					card.put("credit", resObj.optString("credit")); // 用户积分
					card.put("use_credit", resObj.optBoolean("use_credit")); // 是否可以使用积分消费
					
					if (resObj.containsKey("use_max_credit_money")) {
						card.put("use_max_credit_money", resObj.optInt("use_max_credit_money"));
					}
					if (resObj.containsKey("credit_deduct")) {
						card.put("credit_deduct", resObj.optInt("credit_deduct"));
					} else {
						card.put("credit_deduct", 1); // 多少积分抵1元
					}
					
					TqMemberInfo tmi = new TqMemberInfo();
					tmi.setBillid(zdbh);
					tmi.setScancode(resObj.optString("cno"));
					tmi.setRemark(resObj.toString());
					tmi.setCanvipprice(resObj.optString("is_vip_price"));
					tmi.setBalance(resObj.optInt("balance"));
					tmi.setIs_usable(0);
					tmi.setCredit(resObj.optInt("credit"));

					acewillMemberinfoMapper.insert(tmi);
					
					JSONArray coupons = resObj.optJSONArray("coupons");
					JSONArray couponList = new JSONArray();
//					List<JSONObject> couponList = new ArrayList<JSONObject>();
					for (int j = 0; j < coupons.size(); j++) {
						JSONObject coupon = coupons.optJSONObject(j);
						// 券校验可用情况
						Map<String, Object> ccMap = checkCoupon(zdbh, coupon, data.optString("acewill_shop_id"), fkje);
						JSONArray coupon_ids = coupon.optJSONArray("coupon_ids");
						for (int k = 0; k < coupon_ids.size(); k++) {
							String coupontype = "";
							double couponTotalmoney = coupon.optDouble("deno", 0);
							switch (coupon.optInt("type")) {
								case 1:
									coupontype = "CASHCOUPON";
									break;
								case 2:
									coupontype = "DISHCOUPON";
									break;
								case 5:
									coupontype = "DISHDISCOUNTCOUPON";
									couponTotalmoney = coupon.optDouble("max_discount_money", 0);
									break;
							}
							JSONArray products = coupon.optJSONArray("products");
							String coupondishcodes = products.toString();
							JSONArray products_ext = coupon.optJSONArray("products_ext");
							if (null == products_ext) {
								products_ext = new JSONArray();
							}
							String coupondishcodesext = products_ext.toString();
//							coupondishcode = coupondishcode.substring(1, coupondishcode.length());
//							if (products.size() > 0) {
//								coupondishcode = products.optString(0);
//							}
							
							TqAcewilCouponCache tacc = new TqAcewilCouponCache();
							tacc.setZdbh(zdbh);
							tacc.setScancode(coupon_ids.optString(k));
							tacc.setDatatype("ACEWILL");
							tacc.setUseok(0);
							tacc.setYhfsid(-100);// 写死id？还是通过编号查询？
							tacc.setCoupontype(coupontype);
							tacc.setCouponcode(coupon_ids.optString(k));
							tacc.setCouponprice(coupon.optDouble("deno"));
							tacc.setCoupondishcodes(coupondishcodes);
							tacc.setCoupondishcodesext(coupondishcodesext);
							tacc.setFkfs(fkfsid);
							tacc.setRemark(coupon.toString());
							tacc.setIs_usable(Integer.parseInt(ccMap.get("code").toString()));
							tacc.setCardcode(resObj.optString("cno"));
							tacc.setTemplateid(coupon.optString("template_id"));
							tacc.setUseablemsg(ccMap.get("useablemsg").toString());
							tacc.setCouponname(coupon.optString("title"));
							if (coupon.containsKey("sale_money_list")) {
								JSONObject saleMoneyList = coupon.optJSONObject("sale_money_list");
								if (saleMoneyList.containsKey(coupon_ids.optString(k))) {
									tacc.setCouponsale(ArithUtil.div(saleMoneyList.optDouble(coupon_ids.optString(k)), 100, 2));
								}
							}
							tacc.setCoupon_totalmoney(couponTotalmoney);
							acewillCouponTempMapper.insert(tacc);
							
							JSONObject couponObj = new JSONObject();
							couponObj.put("title", coupon.optString("title"));
							couponObj.put("couponid", coupon_ids.optString(k));
							couponObj.put("deno", coupon.optString("deno")); // 面值
							couponObj.put("coupontype", coupontype); // 类型
							couponObj.put("isusable", Integer.parseInt(ccMap.get("code").toString())); // 可用状态
							couponObj.put("useablemsg", ccMap.get("useablemsg").toString()); // 不可用原因
							
							couponList.add(couponObj);
						}
					}
					card.put("coupons", couponList);
					dataList.add(card);
				}
				result.setData(dataList);
				result.setCode(Constant.CODE_SUCCESS);
				result.setMsg("查询会员信息成功");
				result.setSuccess(true);
			} else {
				result.setCode(-1);
				result.setMsg(data.optString("errmsg"));
				result.setSuccess(false);
			}
		} else {
			result.setCode(-1);
			result.setMsg(ua.optString("msg"));
			result.setSuccess(false);
		}

	}

	/**
	 * 校验券的可用状态
	 * 
	 * 1.启用金额
	 * 2.账单是否包含菜品券菜品校验
	 * 3.本门店是否可用 
	 * 4.可用日期校验 
	 * 5.可用星期校验 
	 * 6.可用时段
	 * 
	 * @param coupon 券信息json数据
	 * @return 0:可用，1：不可用
	 */
	public Map<String, Object> checkCoupon(String zdbh, JSONObject coupon, String this_shop_id, double fkje) {
		Map<String, Object> ccMap = new HashMap<String, Object>();
		ccMap.put("code", 1);
		
		boolean ifSale = DateUtil.ifSale(coupon.optString("effective_time"), coupon.optString("failure_time"));
		int cycleType = coupon.optInt("cycle_type", 1);
		String weekStr = checkWeek2(coupon.optJSONArray("use_week_day"), cycleType);
		String monthStr = checkMonth(coupon.optJSONObject("use_month_day"), cycleType);
		String timeStr = checkTime(coupon.optJSONArray("valid_use_times"));
		
		if (coupon.optDouble("enable_amount") > fkje) {// 1.校验启用金额
			ccMap.put("useablemsg", "该券启用金额不满足，账单金额需达到" + coupon.optDouble("enable_amount") + "元！");
			return ccMap;
		}
		if (2 == coupon.optInt("type")) {// 2.校验账单明细中是否有可用菜品
			JSONArray products = coupon.optJSONArray("products");
			if (products.size() > 0) {
				int rc = 0;
				for (int i = 0; products.size() > i; i++) {
					String cmbh = products.optString(i);
					WdDishVo wdv = acewillCouponTempMapper.getWdDish(zdbh, cmbh);
					if (null != wdv) {
						rc = rc + 1;
					}
				}
				if (rc == 0) {
					ccMap.put("useablemsg", "本单无可核销菜品！");
					return ccMap;
				}
			} else {
				ccMap.put("useablemsg", "该券没有包含任何菜品，请联系相关人员进行确认！");
				return ccMap;
			}
		}
		if (5 == coupon.optInt("type")) {
			String productsExtStr = coupon.optString("products_ext", "");
			String[] productsExtList = productsExtStr.split(",");
			JSONArray productsExt = new JSONArray();
			if(productsExtStr.length() > 0){
				productsExt = JSONArray.fromObject(productsExtList);
			}
			JSONArray productsTemp = coupon.optJSONArray("products");
//			JSONArray products = new JSONArray();
//			for (int i = 0; productsExt.size() > i; i++) {
//				String extId = productsExt.optString(i);
//				if ("".equals(extId)) {
//					continue;
//				}
//				boolean isRepeat = true;
//				for (int j = 0; productsTemp.size() > j; j++) {
//					String cmbh = productsTemp.optString(j);
//					if (extId.equals(cmbh)) {
//						isRepeat = false;
//					}
//				}
//				if (isRepeat) {
//					products.add(extId);
//				}
//			}
//			coupon.put("products", products);
			coupon.put("products", productsExt);
			coupon.put("products_ext", productsTemp);
			coupon.put("mix_use", false);
//			if (products.size() > 0) {
//				int rc = 0;
//				for (int i = 0; products.size() > i; i++) {
//					String cmbh = products.optString(i);
//					WdDishVo wdv = acewillCouponTempMapper.getWdDish(zdbh, cmbh);
//					if (null != wdv) {
//						rc = rc + 1;
//					}
//				}
//				if (rc == 0) {
//					ccMap.put("useablemsg", "本单无可核销菜品！");
//					return ccMap;
//				}
//			} else {
//				ccMap.put("useablemsg", "该券没有包含任何菜品，请联系相关人员进行确认！");
//				return ccMap;
//			}
		}
		if (!checkSids(coupon, this_shop_id)) {// 3.本门店是否可用
			ccMap.put("useablemsg", "该券不可在本门店使用！");
			return ccMap;
		}
		if (!ifSale) {// 4.可用日期校验
			ccMap.put("useablemsg", "当前日期不在可用日期范围内！");
			return ccMap;
		}
//		if (!weekB) {// 5.可用星期校验
//			return 1;
//		}
		if (!"0".equals(weekStr)) { // 5.可用星期校验
			weekStr = weekStr.substring(1, weekStr.length());
			ccMap.put("useablemsg", "该券仅《" + weekStr + "》可用！");
			return ccMap;
		}
		if (!"0".equals(timeStr)) {// 6.可用时段
			ccMap.put("useablemsg", "当前时间不在可用时段范围内！");
			return ccMap;
		}
		if (!"0".equals(monthStr)) { // 7.可用日期校验
			ccMap.put("useablemsg", monthStr);
			return ccMap;
		}
		ccMap.put("code", 0);
		ccMap.put("useablemsg", "OK");
		return ccMap;
	}

	// 校验星期是否可用
	public boolean checkWeek1(JSONArray use_week_day) {
		boolean b = false;
		int week = DateUtil.dayForWeekToDate(new Date(), 0);
		List list = JSONArray.toList(use_week_day);
		if (list.indexOf(week + "") != -1) {
			b = true;
		}
		return b;
	}
	
	public String checkWeek2(JSONArray use_week_day, int cycleType) {
		if (1 != cycleType) {
			return "0";
		}

		int week = DateUtil.dayForWeekToDate(new Date(), 0);
		String weekStr = "0";
		for (int i = 0; i < use_week_day.size(); i++) {
			if (week == use_week_day.getInt(i)) {
				weekStr = "0";
				break;
			}
			switch (use_week_day.getInt(i)) {
				case 1:
					weekStr += ",星期一";
					break;
				case 2:
					weekStr += ",星期二";
					break;
				case 3:
					weekStr += ",星期三";
					break;
				case 4:
					weekStr += ",星期四";
					break;
				case 5:
					weekStr += ",星期五";
					break;
				case 6:
					weekStr += ",星期六";
					break;
				case 0:
					weekStr += ",星期日";
				break;
			}
		}
		return weekStr;
	}

	// 校验日期是否可用
	public String checkMonth(JSONObject useMonthDay, int cycleType) {
		if (2 != cycleType) {
			return "0";
		}

		String month = DateUtil.dayOfMonthToDate(new Date());
		String monthStr = "0";
		int type = useMonthDay.optInt("type", 1);
		String valueStr = useMonthDay.optString("value", "");
		String[] values = valueStr.split(",");
		boolean containsTarget = Arrays.asList(values).contains(month);
		if (1 == type) {
			if (!containsTarget) {
				monthStr = "仅每月【" + valueStr + "】日可用";
			}
		} else if (2 == type) {
			if (containsTarget) {
				monthStr = "每月【" + valueStr + "】日不可用";
			}
		}


		return monthStr;
	}

	// 校验时间是否可用
	public String checkTime(JSONArray valid_use_times) {
		String timeStr = "0";
		for (int i = 0; i < valid_use_times.size(); i++) {
			JSONObject timeObj = valid_use_times.optJSONObject(i);
			String start = timeObj.optString("start");
			String end = timeObj.optString("end");
			if (DateUtil.ifUsableTimes(start, end)) {
				timeStr = "0";
				break;
			}
			timeStr += "," + start + "-" + end;
		}
		return timeStr;
	}

	// 校验当前门店是否可用
	public boolean checkSids(JSONObject coupon, String this_shop_id) {
		boolean b = false;
		JSONArray sids = coupon.optJSONArray("sids");
		for (int i = 0; i < sids.size(); i++) {
			if (this_shop_id.equals(sids.getString(i))) {
				b = true;
				break;
			}
		}
		return b;
	}
	
	/**
	 * 根据账单号查询缓存中的微生活会员信息
	 * 
	 * @param param
	 * @param result
	 */
	public void cacheAcewillMember(Data data, Data result) {
		Map<String, Object> map = ReqDataUtil.getDataMap(data);
		String zdbh = ParamUtil.getStringValue(map, "zdbh", false, null);// 帐单编号
		List<JSONObject> dataList = new ArrayList<JSONObject>();

		List<TqMemberInfo> amis = acewillMemberinfoMapper.getAcewillMembers(zdbh, "");
		if(amis.size() == 0){
			result.setCode(-1);
			result.setMsg("无会员缓存信息，请查询会员");
			result.setSuccess(false);
			return;
		}
		
		for (int i = 0; i < amis.size(); i++) {
			TqMemberInfo ami = amis.get(i);
			JSONObject res = JSONObject.fromObject(ami.getRemark());
			JSONObject card = new JSONObject();
			card.put("cno", res.optString("cno")); // 卡号
			card.put("balance", ami.getBalance()); // 卡余额
			card.put("name", res.optString("name")); // 用户名称
			card.put("type", res.optString("type")); // 卡类型，wx:微信卡 dp:点评卡 ph:实体卡 actual:实体卡
			card.put("in_effect", res.optString("in_effect")); // 卡是否有效
			card.put("grade_name", res.optString("grade_name")); // 卡等级名称
			card.put("is_vip_price", res.optString("is_vip_price")); // 是否使用会员价
			card.put("credit", ami.getCredit()); // 用户积分
			card.put("use_credit", res.optBoolean("use_credit")); // 是否可以使用积分消费
			
			if (res.containsKey("use_max_credit_money")) {
				card.put("use_max_credit_money", res.optInt("use_max_credit_money")); // (单位：元) 积分可抵扣的最大金额 
			}
			if (res.containsKey("credit_deduct")) {
				card.put("credit_deduct", res.optInt("credit_deduct")); // 多少积分抵1元
			} else {
				card.put("credit_deduct", 1);
			}
			
			int usecount = 0;
			
			List<JSONObject> couponList = new ArrayList<JSONObject>();
			List<TqAcewilCouponCache> coupons = acewillCouponTempMapper.getAcewillCoupons(zdbh, res.optString("cno"));
			for (int j = 0; j < coupons.size(); j++) {
				TqAcewilCouponCache coupon = coupons.get(j);
				if(coupon.getUseok() == 0){
					JSONObject couponJson = JSONObject.fromObject(coupon.getRemark());
					JSONObject couponObj = new JSONObject();
					couponObj.put("title", couponJson.optString("title"));
					couponObj.put("couponid", coupon.getCouponcode());
					couponObj.put("deno", coupon.getCouponprice()); // 面值
					couponObj.put("coupontype", coupon.getCoupontype()); // 类型
					couponObj.put("isusable", coupon.getIs_usable()); // 可用状态
					couponObj.put("useablemsg", coupon.getUseablemsg()); // 不可用原因
								
					couponList.add(couponObj);	
				}

				if(coupon.getUseok() == 1){
					usecount = usecount + 1;
				}
									
			}
			card.put("usecount", usecount);
			card.put("coupons", couponList);
			dataList.add(card);
		}
		result.setData(dataList);
		result.setCode(Constant.CODE_SUCCESS);
		result.setMsg("查询会员信息成功");
		result.setSuccess(true);
	}
	
	/**
	 * 根据账单号清除该账单关联的会员信息
	 * @param param
	 * @param result
	 */
	public void refundAcewillMember(Data param, Data result) {

		Map<String, Object> map = ReqDataUtil.getDataMap(param);
		String zdbh = ParamUtil.getStringValue(map, "zdbh", false, null);// 帐单编号
		String jtbh = ParamUtil.getStringValue(map, "jtbh", false, null);// 机台编号

		result.setMsg("请先取消会员价，再进行操作,本次操作仅清除会员付款信息");
		
		if (acewillCouponTempMapper.getIsVipPriceCount(zdbh) <= 0) {
			acewillCouponTempMapper.delTwct(zdbh);
			acewillCouponTempMapper.delTmi(zdbh);
			tqZdkMapper.delTqWdkCouponTemp(zdbh, "");
			result.setMsg("清除成功");
		} else {
			List<TqMemberInfo> amis = acewillMemberinfoMapper.getAcewillMembers(zdbh, "");
			TqMemberInfo tmi = new TqMemberInfo();
			if (amis.size() > 0) {
				tmi = amis.get(0);
			} else {
				result.setCode(-1);
				result.setMsg("会员卡不存在，请重新查询");
				result.setSuccess(false);
			}
			double sub_balance = ArithUtil.mul(acewillMemberinfoMapper.findBalanceAmount(zdbh, "2001"), 100);
			double sub_credit = acewillMemberinfoMapper.findBalanceAmount(zdbh, "2002");
			// 获取卡余额
			double balance = tmi.getBalance();
			double credit = tmi.getCredit();
			// 将取消的支付金额加回到卡余额
			balance = ArithUtil.add(balance, sub_balance);
			JSONObject res = JSONObject.fromObject(tmi.getRemark());
			if (res.containsKey("credit_deduct")) {
				sub_credit = ArithUtil.mul(sub_credit, res.optInt("credit_deduct"));
			}
			credit = ArithUtil.add(credit, sub_credit);
			acewillMemberinfoMapper.updateToBalance(zdbh, tmi.getScancode(), balance, credit);
			
			tqZdkMapper.delTqWdkCouponTemp(zdbh, "");
			acewillCouponTempMapper.updateUsaByUseok(zdbh, "", 0);
		}
		
		acewillCouponTempMapper.delFkls(zdbh, "FKSX_HYK");
		acewillCouponTempMapper.delDsfls(zdbh, "ACEWILL");
		tqZdkMapper.delTpd(zdbh, -100, "");
		List<Integer> rwids = acewillCouponTempMapper.getRefundRwid(zdbh, -100);
		for (int rwid : rwids) {
			tqZdkMapper.cancelJd(zdbh, rwid, 1, jtbh, "", 1);
		}
		tqZdkMapper.findCalcMoney(zdbh);
		double open_amount = 0.00;
		double yfje = tqZdkMapper.getFkje(zdbh);
		double zdje = tqZdkMapper.getZdje(zdbh);
		open_amount = DoubleUtil.sub(zdje, yfje);
		PayResultData payResultData = new PayResultData();
		payResultData.setOpen_amount(open_amount + "");
		payResultData.setFkls(tqZdkMapper.getFklsList(zdbh));
		List<CouponRunningWater> crw = new ArrayList<CouponRunningWater>();
		List<CouponRunningWater> crw4 = tqZdkMapper.getCouponlsList(zdbh, "4");
		List<CouponRunningWater> crw6 = tqZdkMapper.getCouponlsList(zdbh, "6");
		crw.addAll(crw4);
		crw.addAll(crw6);
		payResultData.setCouponls(crw);
		List<JSONObject> dataList = new ArrayList<JSONObject>();
		dataList.add(JSONObject.fromObject(GsonUtil.GsonString(payResultData)));

		result.setCode(0);
		result.setData(dataList);
		result.setSuccess(true);
	}
	
	// 预消费
	public void acewillPreview(Data param, Data result) {
		Map<String, Object> map = ReqDataUtil.getDataMap(param);
		String zdbh = ParamUtil.getStringValue(map, "zdbh", false, null);// 帐单编号
		String skjh = ParamUtil.getStringValue(map, "skjh", false, null);// 收款机号
		
		TqZdk zdk = tqZdkMapper.getZdk(zdbh);
		if ("ZDSX_YJ".equals(zdk.getJzsx())) {
			result.setCode(-102);
			result.setMsg("账单《" + zdbh + "》已关闭，请返回首页查询该帐单！");
			result.setSuccess(false);
			return;
		}

		List<TqAcewilCouponCache> taccs = acewillCouponTempMapper.getCouponByUseok(zdbh, 1);
//		PayMentVo fklx = acewillCouponTempMapper.checkFkls(zdbh, "2001");
//
//		if (taccs.size() == 0 && null == fklx) {
//			result.setCode(0);
//			result.setMsg("无微生活券使用记录，无需验证");
//			result.setSuccess(true);
//			return;
//		}
		
		TqMemberInfo tmi = acewillMemberinfoMapper.getAcewillScancode(zdbh);
		
		if (null == tmi) {
			result.setCode(0);
			result.setMsg("无会员券使用记录，无需验证");
			result.setSuccess(true);
			return;
		}
		
		List<JSONObject> dataList = new ArrayList<JSONObject>();
		TsPsjgsdk tsPsjgsdk = tqZdkMapper.getJg();// 机构数据
//		String rifUrl = tqZdkMapper.getRifUrl();
		Map<String, String> reqMap = new HashMap<String, String>();
		JSONObject obj = new JSONObject(); // 请求数据
//		rifUrl = rifUrl + "/acewillMemberAction.action";// 请求地址
		String rifUrl = acewillMemberinfoMapper.getACERifUrl();
		reqMap.put("organ_id", tsPsjgsdk.getJgxh() + "");// 机构序号
		
		// 调用预消费接口，只要有过预消费记录，不管他有没有成功，都要冲正才可以使用
		Map<String, String> dsflsMap = acewillCouponTempMapper.findZddsflsk(zdbh, "ACEWILL", "");
		if (null != dsflsMap) {
			String yl3 = dsflsMap.get("yl3");
			String remark = dsflsMap.get("remark");
			if (yl3.equals("COMMITSTART")) {
				dataList.add(JSONObject.fromObject(remark));
				result.setCode(1);
				result.setData(dataList);
				result.setMsg("预消费成功，请根据验证方式进行验证");
				result.setSuccess(true);
				return;
			}
			
			reqMap.put("type", "rollback");// 预消费，冲正接口
			String biz_id_old = dsflsMap.get("qrcode");
//			String yl3 = dsflsMap.get("yl3");
//			if (yl3.equals("COMMITSTART")) {
//				reqMap.put("type", "cancel");// 如果是提交过消费，改为调用取消接口
//				obj.put("cashier_id", -1);
//			}
			
			obj.put("biz_id", biz_id_old);
			reqMap.put("data", obj.toString());// 请求数据

			Map<String, String> rbmap = HttpClientUtil.postSSLUrlWithParams(rifUrl, reqMap);// 请求获取返回数据
			String rbStr = "";
			if ("0".equals(rbmap.get("success"))) {
				rbStr = rbmap.get("data");
			} else {
				result.setCode(-1);
				result.setMsg(rbmap.get("msg"));
				result.setSuccess(false);
				return;
			}

			JSONObject rbua = JSONObject.fromObject(rbStr);
			if ("200".equals(rbua.optString("code"))) {
				JSONObject rbdata = rbua.optJSONObject("data");
				if (0 == rbdata.optInt("errcode")) {
					JSONObject rbres = rbdata.optJSONObject("res");
					if ("FAIL".equals(rbres.optString("result"))) {
						result.setCode(-1);
						result.setMsg("预消费冲正失败，请重新提交");
						result.setSuccess(false);
						return;
					}
				} else if (3010 == rbdata.optInt("errcode")) {
					// 账单号不存在，无需冲正
				} else if (3032 == rbdata.optInt("errcode")) {
					// 交易状态异常，可能是已经提交成功，无需继续提交
					result.setCode(-102);
					result.setMsg("交易状态异常，账单《" + zdbh + "》可能已经完成，请返回首页查询该帐单！");
					result.setSuccess(false);
					return;
				} else {
					result.setCode(-1);
					result.setMsg(rbdata.optString("errmsg"));
					result.setSuccess(false);
					return;
				}
			} else {
				result.setCode(-1);
				result.setMsg(rbua.optString("msg"));
				result.setSuccess(false);
				return;
			}
		}
		
		// 如果付款流水中没有微生活券付款，直接返回不用预消费
//		PayMentVo fklx1 = acewillCouponTempMapper.checkFkls(zdbh, "2010");
//
//		if (null == fklx1 && null == fklx) {
//			result.setCode(0);
//			result.setMsg("无微生活付款，无需验证");
//			result.setSuccess(true);
//			return;
//		}
		
		reqMap.put("type", "preview");// 预消费接口
		
		String cno = ""; // 卡号
		double consume_amount = 0; // 消费总金额 ，单位分
		double payment_amount = 0; // 实际支付金额(单位:分) 不传默认为0
		double credit_amount = 0; // 可积分金额
		JSONArray deno_coupon_ids = new JSONArray();
		JSONArray gift_coupons_ids = new JSONArray();
		JSONArray discount_coupons_ids = new JSONArray();

		String biz_id = tsPsjgsdk.getJgxh() + "_" + zdbh + "_" + new Date().getTime();

		for (TqAcewilCouponCache twct : taccs) {
			cno = twct.getCardcode();
			if ("CASHCOUPON".equals(twct.getCoupontype())) {
				deno_coupon_ids.add(twct.getCouponcode());
				continue;
			}
			if ("DISHCOUPON".equals(twct.getCoupontype())) {
				gift_coupons_ids.add(twct.getCouponcode());
				continue;
			}
			if ("DISHDISCOUNTCOUPON".equals(twct.getCoupontype())) {
				JSONObject discount_coupons = new JSONObject();
				discount_coupons.put("user_coupon_id", twct.getCouponcode());
				discount_coupons.put("deno", ArithUtil.mul(twct.getCoupon_totalmoney(), 100));
				discount_coupons_ids.add(discount_coupons);
				continue;
			}
		}
		
		consume_amount = ArithUtil.mul(acewillCouponTempMapper.findZdAmount(zdbh), 100);
		credit_amount = ArithUtil.mul(acewillCouponTempMapper.findPaymentAmount(zdbh), 100);
		List<AcewillProductsVo> products = acewillCouponTempMapper.findProducts(zdbh);
		
		for(AcewillProductsVo apv : products){
			List<String> product_use_coupon = new ArrayList<String>();
			product_use_coupon.add(apv.getCouponcode());
//			apv.setCoupons_ids(coupons_ids);
			if (null != apv.getCouponcode()) {
//				apv.setProduct_use_coupon(product_use_coupon);
				apv.setCoupons_ids(product_use_coupon);
			}
		}
		double daprice = acewillCouponTempMapper.getCouponPrice(zdbh);
		double cdaprice = acewillCouponTempMapper.getCountCouponPrice(zdbh);
		double aprice = ArithUtil.add(ArithUtil.mul(daprice, 100), ArithUtil.mul(cdaprice, 100));
		double sub_balance = ArithUtil.mul(acewillMemberinfoMapper.findBalanceAmount(zdbh, "2001"), 100);
		double sub_credit = acewillMemberinfoMapper.findBalanceAmount(zdbh, "2002");
		double sub_products = ArithUtil.mul(acewillMemberinfoMapper.findBalanceAmount(zdbh, "2010"), 100);

		payment_amount = ArithUtil.sub(consume_amount, sub_balance);
		payment_amount = ArithUtil.sub(payment_amount, ArithUtil.mul(sub_credit, 100));
		payment_amount = ArithUtil.sub(payment_amount, sub_products);
		consume_amount = ArithUtil.sub(ArithUtil.add(consume_amount, aprice), sub_products);
		
		if("".equals(cno)){
			cno =  tmi.getScancode();
		}

		obj.put("cno", cno);
		obj.put("cashier_id", -1); // 收银员id【注：传-1则为API默认收银员】
		obj.put("consume_amount", consume_amount); // 消费总金额 ，单位分
		obj.put("payment_amount", payment_amount); // 需要扣减了菜品券面值金额（不需要了），储值支付金额,积分也要减扣
		obj.put("payment_mode", 10); // 支付方式
		obj.put("sub_balance", sub_balance); // 消费使用储值金额，单位分
		obj.put("sub_credit", sub_credit); // 消费使用积分，单位个
		obj.put("credit_amount", credit_amount);
		obj.put("deno_coupon_ids", deno_coupon_ids);
		obj.put("gift_coupons_ids", gift_coupons_ids);
		obj.put("discount_coupons_ids", discount_coupons_ids);
		obj.put("diy_gift_coupon_pay", new JSONArray());
		obj.put("activity_ids", new JSONArray());
		obj.put("count_num", 0);
		obj.put("remark", "");
		obj.put("tags", new JSONArray());
		obj.put("products", products);
		obj.put("table_id", "");
		obj.put("biz_id", biz_id);
		obj.put("pay_info", new JSONObject());

		reqMap.put("data", obj.toString());// 请求数据
		
		Map<String, String> bbrqMap = tqJtztkMapper.findBbrq();
		String bbrq = DateUtil.getNowDateYYDDMM();
		if (null != bbrqMap && bbrqMap.size() != 0) {
			bbrq = bbrqMap.get("bbrq");
		}
		// 后先清除掉第三方流水表
		acewillCouponTempMapper.delDsfls(zdbh, "ACEWILL");
		// 重新插入
		acewillCouponTempMapper.insertTqZddsflsk(skjh,zdbh,DateUtil.parseDate(bbrq),new Date(),"ACEWILL",biz_id,"07",0,"PREVIEWSTART","");
		
		Map<String, String> rmap = HttpClientUtil.postSSLUrlWithParams(rifUrl, reqMap);// 请求获取返回数据
		String rStr = "";
		if ("0".equals(rmap.get("success"))) {
			rStr = rmap.get("data");
		} else {
			result.setCode(-1);
			result.setMsg(rmap.get("msg"));
			result.setSuccess(false);
			return;
		}
		JSONObject ua = JSONObject.fromObject(rStr);
		if ("200".equals(ua.optString("code"))) {
			JSONObject data = ua.optJSONObject("data");
			if (0 == data.optInt("errcode")) {
				acewillCouponTempMapper.updateZddsflsk(zdbh, "", "PREVIEWSUCCESS", data.optJSONObject("res").toString(), "ACEWILL");
//				acewillCouponTempMapper.insertTqZddsflsk(skjh,zdbh,DateUtil.parseDate(bbrq),new Date(),"ACEWILL",biz_id,"07",0,"PREVIEWSUCCESS");
				
				dataList.add(data.optJSONObject("res"));
				result.setCode(1);
				result.setData(dataList);
				result.setMsg("预消费成功，请根据验证方式进行验证");
				result.setSuccess(true);
			} else {
				result.setCode(-1);
				result.setMsg(data.optString("errmsg"));
				result.setSuccess(false);
			}
		} else {
			result.setCode(-1);
			result.setMsg(ua.optString("msg"));
			result.setSuccess(false);
		}
	}
	
	
	
	// 提交消费
	public void acewillCommit(Data param, Data result) {
		Map<String, Object> map = ReqDataUtil.getDataMap(param);
		String zdbh = ParamUtil.getStringValue(map, "zdbh", false, null);// 帐单编号
		String verifycode = ParamUtil.getStringValue(map, "verifycode", false, null);// 短信验证码或交易密码

		List<JSONObject> dataList = new ArrayList<JSONObject>();
		TsPsjgsdk tsPsjgsdk = tqZdkMapper.getJg();// 机构数据
//		String rifUrl = tqZdkMapper.getRifUrl();
		Map<String, String> reqMap = new HashMap<String, String>();
		JSONObject obj = new JSONObject(); // 请求数据
		reqMap.put("organ_id", tsPsjgsdk.getJgxh() + "");// 机构序号
		reqMap.put("type", "commit");// 接口

		Map<String, String> dsflsMap = acewillCouponTempMapper.findZddsflsk(zdbh, "ACEWILL", "");
		
		if (null == dsflsMap || (!"PREVIEWSUCCESS".equals(dsflsMap.get("yl3")) && !"COMMITSTART".equals(dsflsMap.get("yl3")))) {
			result.setCode(-1);
			result.setMsg("预消费信息不存在，请重新下单");
			result.setSuccess(false);
			return;
		}
		String biz_id = dsflsMap.get("qrcode");
		obj.put("biz_id", biz_id);
		obj.put("verify_code", verifycode);

		reqMap.put("data", obj.toString());// 请求数据
		
		acewillCouponTempMapper.updateZddsflsk(zdbh, "", "COMMITSTART", "", "ACEWILL");
		
//		rifUrl = rifUrl + "/acewillMemberAction.action";// 请求地址
		String rifUrl = acewillMemberinfoMapper.getACERifUrl();
		Map<String, String> rmap = HttpClientUtil.postSSLUrlWithParams(rifUrl, reqMap);// 请求获取返回数据
		String rStr = "";
		if ("0".equals(rmap.get("success"))) {
			rStr = rmap.get("data");
		} else {
			result.setCode(-1);
			result.setMsg(rmap.get("msg"));
			result.setSuccess(false);
			return;
		}
		JSONObject ua = JSONObject.fromObject(rStr);
		if ("200".equals(ua.optString("code"))) {
			JSONObject data = ua.optJSONObject("data");
			if (0 == data.optInt("errcode")) {
				JSONObject res =  data.optJSONObject("res");
				String deal_id = res.optString("deal_id");
				double stored_pay = res.optDouble("stored_pay");
				double stored_sale_pay = res.optDouble("stored_sale_pay"); // 单位分
				double stored_give_pay = ArithUtil.div(ArithUtil.sub(stored_pay, stored_sale_pay), 100, 2) ; // 单位元
				double stored_give_pays = stored_give_pay;
				acewillCouponTempMapper.updateZddsflsk(zdbh, deal_id, "COMMITSUCCESS", "", "ACEWILL");
				
//				POS_MEMBALANCE_USEDIS 会员储值付款记录优惠方式	0 不记录优惠，1按照会员系统传过来的优惠拆分记录优惠，2李先生按比例拆分		
//				POS_MEMCREDIT_USEDIS 会员积分付款抵记录优惠方式	 0不记录优惠， 1全部记录成优惠	
				if ("1".equals(InitDataListener.ggcsMap.get("POS_MEMBALANCE_USEDIS"))) {
					List<TqFklslsk> balanceList = acewillMemberinfoMapper.findBalanceList(zdbh, "2001");
					Dish dsfyhB = tqZdkMapper.getDsfyh("63");
					if (null != dsfyhB && balanceList.size() > 0 && stored_give_pay != 0) {
//						acewillDiscount(zdbh, dsfyhB, balanceList, stored_give_pay, stored_give_pays);
						acewillDiscount1(zdbh, dsfyhB, balanceList, stored_give_pays);
					}
				}
				if ("2".equals(InitDataListener.ggcsMap.get("POS_MEMBALANCE_USEDIS"))) {
					String acewillDiscount = InitDataListener.ggcsMap.get("ACEWILLDISCOUNT");
					if (null != acewillDiscount) {
						if (isNumeric(acewillDiscount) && Integer.parseInt(acewillDiscount) > 0 && Integer.parseInt(acewillDiscount) <=100) {
							List<TqFklslsk> balanceList = acewillMemberinfoMapper.findBalanceList(zdbh, "2001");
							Dish dsfyhB = tqZdkMapper.getDsfyh("73");
							double scale = Integer.parseInt(acewillDiscount);
							double scale_pay = ArithUtil.sub(ArithUtil.div(stored_pay, 100, 2), ArithUtil.div(ArithUtil.mul(stored_pay, scale), 10000, 2));
							scale_pay = ArithUtil.round(scale_pay, 2);
							if (null != dsfyhB && balanceList.size() > 0 && scale_pay != 0) {
//								acewillDiscount(zdbh, dsfyhB, balanceList, scale_pay, scale_pay);
								acewillDiscount1(zdbh, dsfyhB, balanceList, scale_pay);
							}
						} else {
							LOGGER.error("微生活储值未拆分:拆分比例=" + acewillDiscount);
						}
					} else {
						LOGGER.error("微生活储值未拆分:未设置拆分比例");
					}
				}
				
				if ("1".equals(InitDataListener.ggcsMap.get("POS_MEMCREDIT_USEDIS"))) {
					List<TqFklslsk> creditList = acewillMemberinfoMapper.findBalanceList(zdbh, "2002");
					Dish dsfyhC = tqZdkMapper.getDsfyh("67");
					if (null != dsfyhC && creditList.size() > 0) {
						double credit_give_pays = acewillMemberinfoMapper.findBalanceAmount(zdbh, "2002");
//						acewillDiscount(zdbh, dsfyhC, creditList, credit_give_pays, credit_give_pays);
						acewillDiscount1(zdbh, dsfyhC, creditList, credit_give_pays);
					}
				}
				
				acewillMemberinfoMapper.updateZdkByCwlxbh(zdbh, "1");
				
				dataList.add(res);
				result.setCode(0);
				result.setData(dataList);
				result.setMsg("提交消费成功");
				result.setSuccess(true);
			} else {
				result.setCode(-1);
				result.setMsg(data.optString("errmsg"));
				result.setSuccess(false);
			}
		} else {
			result.setCode(-1);
			result.setMsg(ua.optString("msg"));
			result.setSuccess(false);
		}
	}
	
	public void acewillDiscount(String zdbh, Dish dsfyh, List<TqFklslsk> fklsList, double stored_give_pay, double stored_give_pays) {
		
		double balance = 0;
		String jtbh = "";
		
		if (null != dsfyh && fklsList.size() > 0 && stored_give_pay != 0) {
			BigDecimal pa = new BigDecimal(stored_give_pays);
			for (TqFklslsk fkls : fklsList) {
				double fkje = fkls.getFkje();
				jtbh = fkls.getSkjh();
				if (fkje > stored_give_pay) {
					balance = ArithUtil.sub(fkje, stored_give_pay);
					acewillMemberinfoMapper.updateFklsById(fkls.getId(), balance);
					break;
				} else if(fkje < stored_give_pay){
					stored_give_pay = ArithUtil.mul(ArithUtil.sub(fkje, stored_give_pay), -1);
//					acewillMemberinfoMapper.delFklsById(fkls.getId());
					acewillMemberinfoMapper.updateFklsById(fkls.getId(), 0);
				} else if(fkje == stored_give_pay){
//					acewillMemberinfoMapper.delFklsById(fkls.getId());
					acewillMemberinfoMapper.updateFklsById(fkls.getId(), 0);
					break;
				}
			}
			
			tqZdkMapper.delYhtemp(zdbh);
			TqYhMtCouponsTemp tymct = new TqYhMtCouponsTemp();
			tymct.setYhfsid(Integer.parseInt(dsfyh.getYhfsid()));
			tymct.setClmxid(-1);
			tymct.setKdzdbh(zdbh);
			tymct.setPayableamt(new BigDecimal(-1));
			tymct.setPqlx("2");
			tymct.setYzm("");
			tymct.setJzid(-1);
			tymct.setOptype(2);
			tymct.setSjje(new BigDecimal(-1));
			tymct.setCmid(-1);
			tymct.setBuyprice(pa.abs());
			tqYhMtCouponsTempMapper.insert(tymct);
			tqZdkMapper.addCm(zdbh, -1, 1, jtbh, "", "", Math.random() * 10000 + "", dsfyh.getYhfsid(), 0);
			tqZdkMapper.zRtr(zdbh);
			tqZdkMapper.findCalcMoney(zdbh);
			
		}
	}
	
	public void acewillDiscount1(String zdbh, Dish dsfyh, List<TqFklslsk> fklsList, double stored_give_pays) {
		// 拆分部分
		for (TqFklslsk fkls : fklsList) {
			UseYhfsParam useYhfsParam = new UseYhfsParam();
			useYhfsParam.setBillid(zdbh);
			useYhfsParam.setOpType(91002);
			useYhfsParam.setYhfsId(Integer.parseInt(dsfyh.getYhfsid()));
			useYhfsParam.setJzid(fkls.getJzid());
			useYhfsParam.setSkjh(fkls.getSkjh());// 机号
			useYhfsParam.setJgtxbh(InitDataListener.organVo.getJgtxbh());// 价格体系编号
			useYhfsParam.setDisAmount(new BigDecimal(stored_give_pays));
			useYhfsParam.setBbrq(fkls.getJzbbrq());
			useYhfsParam.setInnerCalMoney(true);// 由接口内部使用均摊及账单金额重计算
			useYhfsApiService.CommUseYhfs(useYhfsParam);
		}
	}
	
	public boolean isNumeric(String str) {
		for (int i = str.length(); --i >= 0;) {
			if (!Character.isDigit(str.charAt(i))) {
				return false;
			}
		}
		return true;
	}

}
