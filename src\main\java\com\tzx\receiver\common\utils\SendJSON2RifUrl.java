package com.tzx.receiver.common.utils;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.tzx.publics.util.HttpClientUtil;
import org.apache.commons.lang.StringUtils;

import java.io.*;

/**
 * <AUTHOR>
 * @Date 2020-08-03
 * @Descption
 **/
public class SendJSON2RifUrl {
    public static void sendFile2MQ(String url,String fileName) throws Exception {
        if(StringUtils.isEmpty(url)||StringUtils.isEmpty(DBUtils.getGGCSK("POS_REALTIME"))){
            throw new Exception("没有配置URL地址");
        }
        File file = new File(fileName);
        if(!file.exists()){
            throw new Exception("没有找到生成的发送消息文件");
        }
        FileReader fileReader = null;
        BufferedReader bufferedReader = null;
        StringBuffer stringBuffer = new StringBuffer();
        try {
            fileReader = new FileReader(file);
            bufferedReader = new BufferedReader(fileReader);
            String tempStr ;
            while ((tempStr=bufferedReader.readLine())!=null){
                stringBuffer.append(tempStr);
            }
        } catch (FileNotFoundException e) {
            e.printStackTrace();
            throw new Exception("读取文件信息失败");
        }catch (IOException e){
            e.printStackTrace();
            throw new Exception("读取文件信息失败");
        }finally {
            if (fileReader != null) {
                try {
                    fileReader.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
        String fullUrl = DBUtils.getGGCSK("POS_REALTIME") + "/" + url;
        String responseStr = null;
        try{
            responseStr = HttpClientUtil.NewHttpClientByPost(fullUrl,stringBuffer.toString());
            if(StringUtils.isEmpty(responseStr)){
                throw new Exception("发送至HTTP失败");
            }
        }catch (Exception e){
            e.printStackTrace();
            throw new Exception("发送至HTTP失败");
        }
        String msg = "";
        try{
            JsonParser jsonParser = new JsonParser();
            JsonObject resutlJson = jsonParser.parse(responseStr).getAsJsonObject();
            if(responseStr==null){
                msg = "解析返回值失败,不是有效的JSON格式";
                throw new Exception("解析返回值失败,不是有效的JSON格式");
            }
            if(!resutlJson.has("code")){
                msg = "返回Json中没有code属性";
                throw new Exception("返回Json中没有code属性");

            }
            if(!resutlJson.get("code").getAsString().equals("0")){
                msg = "返回值非0,code= " + resutlJson.get("code").getAsString();
                if(resutlJson.has("messageinfo")){
                    msg = msg + " ,messageinfo=" + resutlJson.get("messageinfo").getAsString();
                }
                throw new Exception(msg);
            }
        }catch (Exception e){
            if(StringUtils.isNotEmpty(msg)){
                throw new Exception(msg);
            }else{
//                throw new Exception(e.getMessage());
                throw new Exception("其他处理JSON的异常");
            }

        }
    }
}
