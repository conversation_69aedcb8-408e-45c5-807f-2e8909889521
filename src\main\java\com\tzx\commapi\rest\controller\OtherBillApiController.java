package com.tzx.commapi.rest.controller;

import java.util.HashMap;
import java.util.UUID;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.tzx.commapi.common.CommApiException;
import com.tzx.commapi.rest.service.IOrderFirstPayService;
import com.tzx.commapi.rest.vo.CommApiData;
import com.tzx.miniapp.common.MiniAppData;
import com.tzx.miniapp.common.MiniAppLocker;
import com.tzx.miniapp.rest.service.IMiniAppFirstPayService;
import com.tzx.publics.base.BaseController;
import com.tzx.publics.common.BillNoData;
import com.tzx.publics.util.GlobalLockGetDataUtil;

import net.sf.json.JSONObject;

@RestController
@RequestMapping("/otherBill")
public class OtherBillApiController extends BaseController implements InitializingBean, DisposableBean {
	private final static Logger LOGGER = LoggerFactory.getLogger(OtherBillApiController.class);

	@Autowired
	private MiniAppLocker locker;
	@Autowired
	private GlobalLockGetDataUtil globalLockGetDataUtil;
	@Autowired
	private IMiniAppFirstPayService firstPayService;
	@Autowired
	private IOrderFirstPayService orderFirstPayService;

	private JSONObject GetSaleJsonObject(String methodTag, CommApiData data, String json, String uuid) {
		try {
			LOGGER.info("request={},\t UUID={},\t json={}", new Object[] { methodTag, uuid, json });
			data.setCode(0);
			return JSONObject.fromObject(json);
		} catch (Exception e) {
			e.printStackTrace();
			data.setMsg("解析json异常,请求JSON格式错误");
			data.setCode(1);
			LOGGER.error(e.getMessage());
			return null;
		}
	}

	@RequestMapping(value = "/shop/orderFirstPayNoDish", method = RequestMethod.POST)
	public String orderPrecheckCode(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) {
		String methodTag = "orderFirstPayNoDish";
		CommApiData data = new CommApiData();
		String uuid = UUID.randomUUID().toString();

		JSONObject dejsonobj = GetSaleJsonObject(methodTag, data, json, uuid);
		if (!data.getCode().equals(0)) {
			return JSONObject.fromObject(data).toString();
		}

		String outOrderId = JSONObject.fromObject(json).optString("out_order_id");
		int need_print_bill = JSONObject.fromObject(json).optInt("need_print_bill", 1);
		MiniAppData dataL = new MiniAppData();
		try {
			dataL = locker.lockerByNum(outOrderId, 1);
			LOGGER.info("Type： {},TradeNo： {},locker.lockerByNum：{}", methodTag, outOrderId, dataL.isSuccess());
			if (dataL.isSuccess()) {
				orderFirstPayService.orderPrecheckBefore(data, dejsonobj);
				if (data.getCode() == 0) {
					BillNoData billNoData = null;
					billNoData = globalLockGetDataUtil.getBillNoData("99", "9");
					orderFirstPayService.orderPrecheck(data, dejsonobj, billNoData);
					if (data.getCode() == 0 && need_print_bill == 1) {
						firstPayService.sendPrintDetailed(JSONObject.fromObject(json));
						firstPayService.sendPrint(JSONObject.fromObject(json), "jzd");
						firstPayService.sendKVS(JSONObject.fromObject(json));
						firstPayService.sendPrintKichen(JSONObject.fromObject(json));
					}
				}
			} else {
				data.setCode(1);
				data.setMsg("账单处理中，请勿重复提交:" + outOrderId);
				data.setData(new HashMap<String, Object>());
			}
		} catch (CommApiException cae) {
			cae.printStackTrace();
			LOGGER.error("Ignore this CommApiException", cae.getMessage());
			data.setCode(1);
			data.setMsg(cae.getMessage());
		} catch (Exception e) {
			e.printStackTrace();
			LOGGER.error("Ignore this exception", e);
			data.setCode(1);
			data.setMsg("系统错误:" + e);
			data.setData(new HashMap<String, Object>());
		} finally {
			if (dataL.isSuccess()) {
				locker.lockerByNum(outOrderId, 2);
				LOGGER.info("Type： {}, TradeNo： {}, locker.lockerByNum 解锁成功", methodTag, outOrderId);
			}
		}

		String returnStr = JSONObject.fromObject(data).toString();
		LOGGER.info("response={},\t UUID={},\t json={}", new Object[] { methodTag, uuid, returnStr });
		return returnStr;
	}

	@Override
	public void destroy() throws Exception {

	}

	@Override
	public void afterPropertiesSet() throws Exception {

	}
}
