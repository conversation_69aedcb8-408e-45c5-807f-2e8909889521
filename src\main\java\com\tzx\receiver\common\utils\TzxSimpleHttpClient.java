package com.tzx.receiver.common.utils;

import java.util.ArrayList;
import java.util.List;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

public class TzxSimpleHttpClient {
	
	public static String HttpClientByPostNew(Integer count,String url,String arg1,String arg2,String arg3,String arg4,String arg5) {
		
		String returnMsg = "";
		System.out.println("HttpClientByPostNew开始...");
		try {
			HttpClient client = new DefaultHttpClient();
			//String url = "http://192.168.40.31:9081/TzxCommunicate/IssueLogUpdate";
			HttpPost post = new HttpPost(url);
			List<NameValuePair> nvps = new ArrayList<NameValuePair>();   
			nvps.add(new BasicNameValuePair("sizeCount", count.toString()));
			nvps.add(new BasicNameValuePair("size", arg1));
			nvps.add(new BasicNameValuePair("zlbh", arg2));
			nvps.add(new BasicNameValuePair("dbName", arg3));
			nvps.add(new BasicNameValuePair("jgxh", arg4));
			nvps.add(new BasicNameValuePair("versionsTime", arg5));
			post.setEntity(new UrlEncodedFormEntity(nvps));
			System.out.println(post.getEntity());
			HttpResponse res = client.execute(post); 
			HttpEntity entity = res.getEntity();
			returnMsg = EntityUtils.toString(entity,"utf-8"); // 获取返回值
			System.out.println("returnMsg:"+returnMsg);
		} catch (Exception e) {
			e.printStackTrace();
		}
			
			return returnMsg;
}


	public static String HttpClientByPostByNVP(List<NameValuePair> nvps,String url) {

		String returnMsg = "";
		System.out.println("HttpClientByPostNew开始...");
		try {
			HttpClient client = new DefaultHttpClient();
			HttpPost post = new HttpPost(url);
			post.setEntity(new UrlEncodedFormEntity(nvps));
			System.out.println(post.getEntity());
			HttpResponse res = client.execute(post);
			HttpEntity entity = res.getEntity();
			returnMsg = EntityUtils.toString(entity,"utf-8"); // 获取返回值
			System.out.println("returnMsg:"+returnMsg);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return returnMsg;
	}
	public static void main(String[] args) {
		//HttpClientByPost("0","","");
		try {
		/*	InetAddress addr = InetAddress.getLocalHost();
			String ip=addr.getHostAddress().toString();//获得本机IP
			String ipF = ip.substring(0,ip.length()-1);
		    String ipL = ip.substring(ip.length()-1);
		    int ipNewL = Integer.valueOf(ipL)+2;
		    String ipNew = ipF+ipNewL;
		    
		    System.out.println(ip);
		    System.out.println(ipF);
		    System.out.println(ipL);
		    System.out.println(ipNewL);
		    System.out.println(ipNew);*/
		    
		} catch (Exception e) {
			e.printStackTrace();
		}	
	}
}
