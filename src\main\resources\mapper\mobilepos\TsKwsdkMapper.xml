<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.mobilepos.rest.mapper.MobilePosTsKwsdkMapper">
	<select id="findTsKwsdkBasicData" resultType="com.tzx.mobilepos.rest.vo.Taste" >
<!-- 		select id,kwbh as taste_code,kwnr1 as taste_content,bzfl as taste_type from ts_kwsdk -->
		select kwcm.kwid as id,sd.kwbh as taste_code,sd.kwnr1 as taste_content,sd.bzfl as taste_type, cm.cmmc1 as itemname, cm.cmid as itemid
		from ts_kwcmsdk kwcm
		left join ts_kwsdk sd on sd.id = kwcm.kwid
		left join ts_cmk cm on cm.xlid = kwcm.refid
		where kwcm.refmemo = 'CLASS_ID'
		UNION ALL
		select kwcm.kwid as id,sd.kwbh as taste_code,sd.kwnr1 as taste_content,sd.bzfl as taste_type, cm.cmmc1 as itemname, cm.cmid as itemid
		from ts_kwcmsdk kwcm
		left join ts_kwsdk sd on sd.id = kwcm.kwid
		left join ts_cmk cm on cm.cmid = kwcm.refid
		where kwcm.refmemo = 'ITEM_ID'
	</select>
</mapper>
