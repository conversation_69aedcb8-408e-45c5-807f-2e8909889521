package com.tzx.receiver.common.upload;


import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2019-04-20
 * @Descption
 **/
public class UploadTask {
    private UploadState state;
    private String organizeId;
    private String organizeCode;//机构编号
    private String organizeName;//机构名称
    private String opId;
    private String reportDate;
    private String shift;
    private String fileName;
    private boolean isOnlySendMQ;
    private Map<String,String> extendProperty;
    private UploadParam param;
    private String guid;

    public Map<String, String> getExtendProperty() {
        return extendProperty;
    }

    public void setExtendProperty(Map<String, String> extendProperty) {
        this.extendProperty = extendProperty;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public UploadState getState() {
        return state;
    }

    public void setState(UploadState state) {
        this.state = state;
    }

    public UploadParam getParam() {
        return param;
    }

    public void setParam(UploadParam param) {
        this.param = param;
    }

    public String getOrganizeId() {
        return organizeId;
    }

    public String getOrganizeCode() {
        return organizeCode;
    }

    public void setOrganizeCode(String organizeCode) {
        this.organizeCode = organizeCode;
    }

    public String getOrganizeName() {
        return organizeName;
    }

    public void setOrganizeName(String organizeName) {
        this.organizeName = organizeName;
    }

    public boolean isOnlySendMQ() {
        return isOnlySendMQ;
    }

    public void setOnlySendMQ(boolean onlySendMQ) {
        isOnlySendMQ = onlySendMQ;
    }

    public void setOrganizeId(String organizeId) {
        this.organizeId = organizeId;
    }

    public String getOpId() {
        return opId;
    }

    public void setOpId(String opId) {
        this.opId = opId;
    }

    public String getReportDate() {
        return reportDate;
    }

    public void setReportDate(String reportDate) {
        this.reportDate = reportDate;
    }

    public String getShift() {
        return shift;
    }

    public void setShift(String shift) {
        this.shift = shift;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public boolean getIsOnlySendMQ() {
        return isOnlySendMQ;
    }

    public void setIsOnlySendMQ(boolean onlySendMQ) {
        isOnlySendMQ = onlySendMQ;
    }
    @Override
    public String toString() {
        return "UpLoadTask{" +
                "state=" + state +
                ", organizeId='" + organizeId + '\'' +
                ", opId='" + opId + '\'' +
                ", reportDate='" + reportDate + '\'' +
                ", shift='" + shift + '\'' +
                ", fileName='" + fileName + '\'' +
                ", isOnlySendMQ=" + isOnlySendMQ +
                ",extendProperty=" + extendProperty +
                ", param=" + param +
                '}';
    }
}
