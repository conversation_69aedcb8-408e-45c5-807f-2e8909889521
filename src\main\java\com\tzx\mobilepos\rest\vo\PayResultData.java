package com.tzx.mobilepos.rest.vo;

import java.math.BigDecimal;
import java.util.List;

public class PayResultData {
	private String fkje;
	private String discountamount;
	private String fkfs;
	private String msg;
	private String privilegeamount;
	private String status;
	private String total;
	private String tradeno;
	private String appid;
	private String userid;
	private String yhfsid;
	private String wxyhfsid;
	private String jzid;
	private String open_amount;
	private String amount;
	private List<PaymentRunningWater> fkls;
	private List<CouponRunningWater> couponls;
	private List<PayResultCoupon> couponlist;
	private BigDecimal payableamt;
	private String third_part_offer;

	public String getFkje() {
		return fkje;
	}

	public void setFkje(String fkje) {
		this.fkje = fkje;
	}

	public String getDiscountamount() {
		return discountamount;
	}

	public void setDiscountamount(String discountamount) {
		this.discountamount = discountamount;
	}

	public String getFkfs() {
		return fkfs;
	}

	public void setFkfs(String fkfs) {
		this.fkfs = fkfs;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	public String getPrivilegeamount() {
		return privilegeamount;
	}

	public void setPrivilegeamount(String privilegeamount) {
		this.privilegeamount = privilegeamount;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getTotal() {
		return total;
	}

	public void setTotal(String total) {
		this.total = total;
	}

	public String getTradeno() {
		return tradeno;
	}

	public void setTradeno(String tradeno) {
		this.tradeno = tradeno;
	}

	public String getAppid() {
		return appid;
	}

	public void setAppid(String appid) {
		this.appid = appid;
	}

	public String getUserid() {
		return userid;
	}

	public void setUserid(String userid) {
		this.userid = userid;
	}

	public String getYhfsid() {
		return yhfsid;
	}

	public void setYhfsid(String yhfsid) {
		this.yhfsid = yhfsid;
	}

	public String getWxyhfsid() {
		return wxyhfsid;
	}

	public void setWxyhfsid(String wxyhfsid) {
		this.wxyhfsid = wxyhfsid;
	}

	public String getJzid() {
		return jzid;
	}

	public void setJzid(String jzid) {
		this.jzid = jzid;
	}

	public String getOpen_amount() {
		return open_amount;
	}

	public void setOpen_amount(String open_amount) {
		this.open_amount = open_amount;
	}

	public String getAmount() {
		return amount;
	}

	public void setAmount(String amount) {
		this.amount = amount;
	}

	public List<PaymentRunningWater> getFkls() {
		return fkls;
	}

	public void setFkls(List<PaymentRunningWater> fkls) {
		this.fkls = fkls;
	}

	public List<CouponRunningWater> getCouponls() {
		return couponls;
	}

	public void setCouponls(List<CouponRunningWater> couponls) {
		this.couponls = couponls;
	}

	public List<PayResultCoupon> getCouponlist() {
		return couponlist;
	}

	public void setCouponlist(List<PayResultCoupon> couponlist) {
		this.couponlist = couponlist;
	}

	public BigDecimal getPayableamt() {
		return payableamt;
	}

	public void setPayableamt(BigDecimal payableamt) {
		this.payableamt = payableamt;
	}

	public String getThird_part_offer() {
		return third_part_offer;
	}

	public void setThird_part_offer(String third_part_offer) {
		this.third_part_offer = third_part_offer;
	}

}
