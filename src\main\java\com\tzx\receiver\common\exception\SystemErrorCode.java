package com.tzx.receiver.common.exception;

/**
 * 系统错误，编码10000开始
 * 
 * <AUTHOR>
 * 
 */
public enum SystemErrorCode implements ErrorCode
{

	/**
	 * 系统内部错误
	 */
	SYSTEM_ERROR(10000),

	/**
	 * 校验重复错误
	 */
	NAME_REPEAT_ERROR(10001),

	/**
	 * 没有默认岗位
	 */
	NOT_DEFAULT_POST_ERROR(10002),
	/**
	 * 用户已过期
	 */
	USER_VALIDATE_ERROR(10003),
	/**
	 * 用户停用
	 */
	USER_DISABLE_ERROR(10004),
	/**
	 * 修改admin密码
	 */
	UPDATE_ADMIN_ERROR(10005),
	/**
	 * 原始密码错误
	 */
	ORIG_PASSWORD_ERROR(10006),
	/**
	 * 初始化用户信息错误
	 */
	INIT_USER_INFO_ERROR(10007),
	/**
	 * 用户名不存在
	 */
	NOT_USER_ERROR(10008),
	/**
	 * 用户名或密码错误
	 */
	USER_PASSWORD_ERROR(10009),
	/**
	 * 登录认证错误，请重试
	 */
	SHIRO_LOGIN_FAILURE(10010),
	/**
	 * 该用户还没有任何模块操作权限
	 */
	NOT_MODULE_ERROR(10011),
	/**
	 * 该日期营业额预估，已存在
	 */
	ESTIMATE_EXIST(10012),
	/**
	 * 该机构没有设置通信参数
	 */
	NOT_COMM_PARAM(10013),

	/**
	 * 执行P_SCM_BILL_DISPOSE_ZL过程出现错误
	 */
	CALL_BILL_DISPOSE_ZL_ERROR(10014),

	/**
	 * 物品冻结
	 */
	GOODS_FREEZE(10015),

	/**
	 * 下发数据错误
	 */
	MQ_COMMUNICATION_ERROR(10016),

	/**
	 * 该用户所属机构没有分配物流，或者没启用物流
	 */
	NOT_START_ORGAN(10017),
	/**
	 * 没有做首次零用金录入
	 */
	NO_CASH_STORE(10018),
	/**
	 * 支出超过当前现金
	 */
	NO_CASH_STORE_COUNT(10019),

	/**
	 * 发送订货单出现错误
	 */
	SEND_ORDER_ERROR(10020),

	/**
	 * 执行过程出现错误
	 */
	CALL_PROCEDURE_ERROR(10021),
	/**
	 * 时薪月薪转换删除失败，已经上传
	 */
	CHANGE_IS_UPLOAD(10022),

	/**
	 * 当前日期已统计
	 */
	ALREADY_STATISTICS_ERROR(10023),

	/**
	 * 当前日期已盘点
	 */
	ALREADY_TAKING_ERROR(10024),

	/**
	 * 该日期还未打样
	 */
	NOT_CLOSING_ERROR(10025),

	/**
	 * {0}日考勤还未审核
	 */
	NOT_ATTENDANCE_ERROR(10026),

	/**
	 * 已过订货时间，不允许订货
	 */
	ORDER_TIME_OUT_ERROR(10027),

	/**
	 * 该日期还未做销售分析
	 */
	NOT_SALES_RECORD_ERROR(10028),

	/**
	 * 物品库存不足
	 */
	NOT_GOODS_STOCK_ERROR(10029),

	/**
	 * 检测数据NULL错误
	 */
	DATA_NULL_ERROR(10030),

	/**
	 * 营业日期大于当前结束日期，不允许删除
	 */
	ORGAN_STOP_BUSI_ERROR(10031),

	/**
	 * 双方机构营业日期不一致
	 */
	PEER_ALREADY_STATISTICS_ERROR(10032),

	/**
	 * 录入金额和当日现金不符，请检查
	 */
	ENTERING_MONEY_ERROR(10033),

	/**
	 * 当前营业日期已经录入
	 */
	OPERATION_INFO_ERROR(10034),

	/**
	 * 还未录入存款与支票，请检查
	 */
	NOT_CASH_CHEQUE_DETAIL_ERROR(10035),

	/**
	 * 验证码输入错误
	 */
	AUTH_CODE_ERROR(10035),

	/**
	 * 已存在日结记录，不允许此操作
	 */
	EXIST_ORGAN_DAY_RECORD_ERROR(10036),

	/**
	 * 已录入存款与支票，不允许此操作
	 */
	EXIST_CASH_CHEQUE_DETAIL_ERROR(10037),

	/**
	 * 调入机构已盘点
	 */
	IN_ALREADY_TAKING_ERROR(10038),

	/**
	 * 请先导入MIS期初数据核对,然后进行日结
	 */
	NOT_BEGIN_ERROR(10039),

	/**
	 * 该日期子店还未打样
	 */
	CHILD_NOT_CLOSING_ERROR(10040),

	/**
	 * 没有原订货单信息
	 */
	NOT_UP_NC_NO_ERROR(10041),

	/**
	 * 已存在该类型盘点单
	 */
	STOCK_EXIST_ERROR(10042),

	/**
	 * 还未进行数据分析，不允许盘点
	 */
	NOT_DATA_ANALYSIS_ERROR(10043),

	/**
	 * {0}没有设置标准单价
	 */
	NOT_GOODS_PRICE_ERROR(10044),

	/**
	 * 没有可用的流水码
	 */
	NOT_USABLE_CODE(10045),

	/**
	 * 还未进行盘点，不允许日结
	 */
	NOT_STOCK_ERROR(10046),

	/**
	 * 现金日报不平衡{0}
	 */
	CASH_BALANCE_ERROR(10047),

	/**
	 * 还未进行水电气盘点，不允许日结
	 */
	NOT_T_E_TAKING_ERROR(10048),

	/**
	 * 还有人事调动未完成，不允许日结
	 */
	NOT_HR_TRANSFER_ERROR(10049),

	/**
	 * {0}子店该日期还未打样
	 */
	NOT_CHILD_CLOSING_ERROR(10050),

	/**
	 * 子店不允许操作库存业务
	 */
	NOT_CHILD_STOCK_ERROR(10051),

	/**
	 * 该时段存在生产模板，不能新增
	 */
	NOT_INSERT_PRODUCT_TEMP(10049),

	/**
	 * 一天只能编制一次生产计划
	 */
	NOT_INSERT_PRODUCT(10050),

	/**
	 * 存在营业数据，不允许暂停营业
	 */
	EXIST_BUSINESS_ERROR(10051),

	/**
	 * {0}子店该日期还未日结
	 */
	NOT_CHILD_DAILY_ERROR(10052),

	/**
	 * {0}最小订货数{1}
	 */
	MIN_SINGLE_STOCK_ERROR(10053),

	/**
	 * {0}最大订货数{1}
	 */
	MAX_SINGLE_STOCK_ERROR(10054),

	/**
	 * 该人员于{0}从{1}调入，未满6个月，不能调出！
	 */
	NOT_TRANSFER_MONTH_BETWEEN(10055),

	/**
	 * 该日考勤已上传不允许反审核
	 */
	ANTI_ATTENDANCE_ERROR(10056),

	/**
	 * 一天之内不允许多次调薪
	 */
	CHANGE_SALARY_ERROR(10057),

	/**
	 * 该日期信息，已存在
	 */
	ESTIMATE_INFO_EXIST(10058),

	/**
	 * 还未进行日盘点，不允许日结
	 */
	NOT_DAY_STOCK_ERROR(10059),

	/**
	 * 还未进行周盘点，不允许日结
	 */
	NOT_WEEK_STOCK_ERROR(10060),

	/**
	 * 还未进行月盘点，不允许日结
	 */
	NOT_MONTH_STOCK_ERROR(10061),

	/**
	 * 读取数据出现超时，请检查网络情况
	 */
	READ_TIMED_OUT_ERROR(10062),

	/**
	 * {0}物料还未录入封顶量
	 */
	CAP_WEIGHT_NOT_ERROR(10063),

	/**
	 * 该档期封顶量已录入
	 */
	CAP_WEIGHT_EXIST_ERROR(10064),

	/**
	 * {0}还未设置BOM
	 */
	CAP_WEIGHT_NOT_BOM_ERROR(10065),

	/**
	 * {0}物料首批订货已超封顶量{1}%,可订货数{2}
	 */
	CAP_WEIGHT_FIRST_ERROR(10066),

	/**
	 * {0}物料订货量已超封顶量,可订货数{1}
	 */
	CAP_WEIGHT_ULTRA_ERROR(10067),

	/**
	 * {0}调整数量小于已使用量
	 */
	CAP_WEIGHT_GOODS_ERROR(10068),

	/**
	 * 还有外送人事调动未完成，不允许日结
	 */
	NOT_WHR_TRANSFER_ERROR(10069),

	/**
	 * 存在时薪人员，不允许此操作
	 */
	EXIST_HR_EMPLOYEE_INFO_ERROR(10070),

	/**
	 * 存在外送时薪人员，不允许此操作
	 */
	EXIST_WHR_EMPLOYEE_INFO_ERROR(10071),

	/**
	 * 存在现金数据，不允许此操作
	 */
	EXIST_CASH_CHEQUE_DATA_ERROR(10072),

	/**
	 * 收银员实盘金额已现金小计不一致，不允许日结
	 */
	ERP_SYYSPJEK_ERROR(10073),

	/**
	 * 溢缺金额大于{0}元，不允许日结
	 */
	ERP_SYYSPJEK_YQ_ERROR(10074),

	/**
	 * 存在时薪短期异动人员，不允许此操作
	 */
	EXIST_HR_EMPLOYEE_TRANSFER_ERROR(10075),

	/**
	 * 存在外送时薪短期异动人员，不允许此操作
	 */
	EXIST_WHR_EMPLOYEE_TRANSFER_ERROR(10076),
	
	/**
	 * 会员卡预付款与实际激活卡片金额不一致
	 */ 
	DEPOSIT_RECEIVED_AND_ACTIVE_AMT_NOT_EQUAL_ERROR(10077),
	
	/**
	 * 存在已激活的卡片未被收取，不允许此操作
	 */ 
	EXIST_ACTIVED_CARD_NOT_OBTAIN_ERROR(10078),
	
	/**
	 * 存在未审核的外送人事考勤，不允许此操作
	 */ 
	EXIST_WHR_ATTENDANCE_NOT_AUDIT_ERROR(10079);

	private final int	number;

	private SystemErrorCode(int number)
	{
		this.number = number;
	}

	public int getNumber()
	{
		return number;
	}

}
