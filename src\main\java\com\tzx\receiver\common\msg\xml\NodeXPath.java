package com.tzx.receiver.common.msg.xml;



public class NodeXPath {

	/**
	 * 消息头元素路径
	 */
	public static String Head = HeadElement.getCurrentPath();

	/**
	 * 消息体元素路径
	 */
	public static String Msg = MsgElement.getCurrentPath();

	/**
	 * 数据列元素路径
	 * 
	 */
	public static String Msg_Datas = MsgDatasElement.getCurrentPath();

	/**
	 * 数据元素路径
	 */
	public static String Datas_Data = Msg_Datas + DPC.XPathDelim
			+ DPC.SMsgDataNodeStr;

	/**
	 * 元数据元素路径
	 * 
	 */
	public static String Data_MetaData = Datas_Data + DPC.XPathDelim
			+ DPC.SMsgMetaDataNodeStr;

	/**
	 * 行数据元素路径
	 */
	public static String Data_RowData = Datas_Data + DPC.XPathDelim
			+ DPC.SMsgRowDataNodeStr;

}
