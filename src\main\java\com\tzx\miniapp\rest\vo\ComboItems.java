package com.tzx.miniapp.rest.vo;

import java.io.Serializable;

public class ComboItems extends OmpDish implements Serializable {

	private String name;
	private int id;
	private int duid;
	private String dishsno;
	private int maxnum;
	private double aprice;
	private int rpdid;
	private int countx;

	private int number;

	private int dishToppingId;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public int getDuid() {
		return duid;
	}

	public void setDuid(int duid) {
		this.duid = duid;
	}

	public String getDishsno() {
		return dishsno;
	}

	public void setDishsno(String dishsno) {
		this.dishsno = dishsno;
	}

	public int getMaxnum() {
		return maxnum;
	}

	public void setMaxnum(int maxnum) {
		this.maxnum = maxnum;
	}

	public double getAprice() {
		return aprice;
	}

	public void setAprice(double aprice) {
		this.aprice = aprice;
	}

	public int getRpdid() {
		return rpdid;
	}

	public void setRpdid(int rpdid) {
		this.rpdid = rpdid;
	}

	public int getNumber() {
		return number;
	}

	public void setNumber(int number) {
		this.number = number;
	}

	public int getCountx() {
		return countx;
	}

	public void setCountx(int countx) {
		this.countx = countx;
	}

	public int getDishToppingId() {
		return dishToppingId;
	}

	public void setDishToppingId(int dishToppingId) {
		this.dishToppingId = dishToppingId;
	}
}
