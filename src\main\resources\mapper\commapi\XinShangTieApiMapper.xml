<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.commapi.rest.mapper.XinShangTieApiMapper">

	<select id="getTqThirdTempOrder" resultType="com.tzx.commapi.rest.vo.TqThirdTempOrder" >
		select * from TQ_THIRD_TEMPORDER where datatype = #{dataType}
		and createtime >= to_timestamp(#{startTime}, 'YYYY-MM-DD HH24:MI:SS')
		<if test="count != 0">
			order by createtime limit #{count}
		</if>
	</select>

</mapper>
