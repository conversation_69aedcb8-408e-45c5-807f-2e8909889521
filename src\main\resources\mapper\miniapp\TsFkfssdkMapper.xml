<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.miniapp.rest.mapper.MiniAppTsFkfssdkMapper">
	<select id="findTsFkfssdkBasicData" resultType="com.tzx.miniapp.rest.vo.PaymentWay">
       select tf.id, tf.fkfsmc1 as payment_name1, tf.fkfsmc2 as payment_name2, tfz.fklxmc1 as payment_class from ts_fkfssdk tf left join ts_fkfsfzk tfz on tfz.id = tf.fkfzid where tf.yl3 = 'ERP_FKFS_WX' or tf.yl3 = 'ERP_FKFS_ZFB'
    </select>
</mapper>
