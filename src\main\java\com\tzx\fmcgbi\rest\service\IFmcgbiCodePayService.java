package com.tzx.fmcgbi.rest.service;

import com.tzx.fmcgbi.common.FmcgbiData;
import com.tzx.publics.common.BillNoData;

import net.sf.json.JSONObject;

public interface IFmcgbiCodePayService {

	/**
	 * 付款接口，核对账单金额
	 * @param param
	 * @param result
	 * @return
	 */
	public FmcgbiData checkAmt(JSONObject requestJson);
	
	public FmcgbiData codePay(JSONObject requestJson, BillNoData billNoData) throws Exception ;
	
	public FmcgbiData queryCodePay(JSONObject requestJson) throws Exception ;
	
}
