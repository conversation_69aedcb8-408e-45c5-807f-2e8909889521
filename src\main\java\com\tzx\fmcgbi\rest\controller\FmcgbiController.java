package com.tzx.fmcgbi.rest.controller;

import java.util.UUID;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.tzx.fmcgbi.common.FmcgbiData;
import com.tzx.fmcgbi.common.FmcgbiException;
import com.tzx.fmcgbi.common.FmcgbiLocker;
import com.tzx.fmcgbi.rest.service.IFmcgbiCalcuFloatAmtService;
import com.tzx.fmcgbi.rest.service.IFmcgbiCodePayService;
import com.tzx.fmcgbi.rest.service.IFmcgbiQueryShopProdService;
import com.tzx.publics.base.BaseController;
import com.tzx.publics.common.BillNoData;
import com.tzx.publics.util.GlobalLockGetDataUtil;

import net.sf.json.JSONObject;

@RestController
public class FmcgbiController extends BaseController {
	private final static Logger LOGGER = LoggerFactory.getLogger(FmcgbiController.class);

	@Autowired
	private IFmcgbiCalcuFloatAmtService calcuFloatAmtService;
	@Autowired
	private IFmcgbiQueryShopProdService queryShopProdService;
	@Autowired
	private IFmcgbiCodePayService codePayService;
	@Autowired
	private FmcgbiLocker locker;
	@Autowired
	private GlobalLockGetDataUtil globalLockGetDataUtil;
	
	@RequestMapping(value = "/fmcgbi", method = RequestMethod.POST)
	public String synchrodataDish(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) {
		response.setContentType("text/html;charset=utf-8");
		FmcgbiData data = new FmcgbiData();
		JSONObject requestJson = JSONObject.fromObject(json);
		JSONObject responseJson = new JSONObject();
		String type = requestJson.optString("Type", "");
		String uuid = UUID.randomUUID().toString();
		LOGGER.info("request ({}) UUID={}: {}", type, uuid, requestJson);
		FmcgbiData dataL = new FmcgbiData();
		String tradeNo = type;
		if(requestJson.has("TradeNo")){
			tradeNo = requestJson.optString("TradeNo", "");
		}
		
		try {
			switch (type) {
			case "CalcuFloatAmt":// 计算优惠方案接口
				data = calcuFloatAmtService.calcuFloatAmt(requestJson);
				break;
			case "CodePay":// 支付码支付接口（微信/支付宝/会员码）
				dataL = locker.lockerByNum(tradeNo, 1);
				LOGGER.info("Type： {},TradeNo： {},locker.lockerByNum：{}", type, tradeNo, dataL.isSuccess());
				if (dataL.isSuccess()) {
					data = codePayService.checkAmt(requestJson);
					if (data.isSuccess()) {
						BillNoData billNoData = null;
						billNoData  = globalLockGetDataUtil.getBillNoData("99", "3");
						data = codePayService.codePay(requestJson, billNoData);
					}
				} else {
					responseJson.put("status", "1");
					responseJson.put("msg", "支付处理中，请勿重复提交:" + tradeNo);
					data.setData(responseJson);
				}
				break;
			case "QueryCodePay":// 查询付款状态接口
				dataL = locker.lockerByNum(tradeNo, 1);
				LOGGER.info("Type： {},TradeNo： {},locker.lockerByNum：{}", type, tradeNo, dataL.isSuccess());
				if (dataL.isSuccess()) {
					data = codePayService.queryCodePay(requestJson);
				} else {
					responseJson.put("status", "1");
					responseJson.put("msg", "INPROCESS：支付处理中:" + tradeNo);
					data.setData(responseJson);
				}
				break;
			case "QueryShopProd":// 同步商品到客户端接口
				data = queryShopProdService.queryShopProd(requestJson);
				break;
			default:
				responseJson.put("status", "1");
				responseJson.put("msg", "接口“" + type + "”不存在！");
				data.setData(responseJson);
				break;
			}
			LOGGER.info("response ({}) UUID={}: {}",type, uuid, JSONObject.fromObject(data).toString());
			return JSONObject.fromObject(data.getData()).toString();
		} catch (FmcgbiException fe) {
			fe.printStackTrace();
			responseJson.put("status", "1");
			responseJson.put("msg", fe.getMessage());
			data.setData(responseJson);
			LOGGER.info("response ({}) UUID={}: FmcgbiException:{}",type, uuid, JSONObject.fromObject(data).toString());
			return JSONObject.fromObject(data.getData()).toString();
		} catch (Exception e) {
			e.printStackTrace();
			responseJson.put("status", "1");
			responseJson.put("msg", "内部错误");
			data.setData(responseJson);
			LOGGER.info("response ({}) UUID={}: FmcgbiException:{}",type, uuid, e.getMessage());
			return JSONObject.fromObject(data.getData()).toString();
		} finally {
			if (dataL.isSuccess()) {
				locker.lockerByNum(tradeNo, 2);
				LOGGER.info("Type： {}, TradeNo： {}, locker.lockerByNum 解锁成功", type, tradeNo);
			}
		}
	}
	
}
