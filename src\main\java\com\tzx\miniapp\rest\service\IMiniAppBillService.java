package com.tzx.miniapp.rest.service;

import com.tzx.miniapp.common.Data;

public interface IMiniAppBillService {
	/**
	 * 创建账单
	 * 
	 * @param param
	 * @param result
	 */
	public void createBill(Data param, Data result);
	/**
     * 下单
     * @param param
     * @param result
     * @throws Exception
     */
	public void ordering(Data param, Data result) throws Exception;
	
	
	/**
     * 优惠校验
     * @param param
     * @param result
     * @throws Exception
     */
	public void checkDiscount(Data param, Data result) throws Exception;
	/**
     * 账单优惠
     * @param param
     * @param result
     * @throws Exception
     */
	public void discountOrder(Data param, Data result) throws Exception;
	
	/**
     * 结账
     * @param param
     * @param result
     * @throws Exception
     */
	public void accountsOrder(Data param, Data result) throws Exception;
	
	/**
     * 支付
     * @param param
     * @param result
     * @throws Exception
     */
	public void payment(Data param, Data result) throws Exception;
}
