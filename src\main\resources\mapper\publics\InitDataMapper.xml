<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.publics.mapper.InitDataMapper">
	<select id="getGgcs" resultType="com.tzx.publics.vo.GgcskVo">
		select * from ts_ggcsk
	</select>
	
	<select id="getThird" resultType="com.tzx.publics.vo.ThirdInfoVO">
		select * from ts_third_info
	</select>
	
	<select id="getOrgan" resultType="com.tzx.publics.vo.OrganVo">
		select jg.* from ts_psjgsdk jg where jg.jgbh = (select sdnr from ts_ggcsk gg where gg.sdbt = 'FDJGBH') limit 1
	</select>

	<select id="getbillid" resultType="java.lang.String"  parameterType="map" statementType="CALLABLE"  useCache="false">
		select * from P_GetBillID(#{skjh},'0',#{tname},#{fname})
	</select>

	<select id="getEcoTypeDic" resultType="com.tzx.receiver.entity.msg.EcoTypeDic">
		select et.* from tq_ecotypedic et where thirdcode not in ('-100','-101','-201')
	</select>
	
	<select id="getGgcsBySdbt" resultType="com.tzx.publics.vo.GgcskVo">
		select * from ts_ggcsk where sdbt = #{sdbt} limit 1
	</select>
	
	<insert id="insertGgcs" parameterType="com.tzx.publics.vo.GgcskVo">
		insert into ts_ggcsk(sdbt,sdnr,syfw,bzsm,jhid,yl1) values(#{ggcs.sdbt}, #{ggcs.sdnr}, #{ggcs.syfw}, #{ggcs.bzsm}, #{ggcs.jhid}, #{ggcs.yl1})
	</insert>
	
	<select id="getDevice" resultType="com.tzx.publics.vo.DeviceVo">
		select * from ts_jtsdk
	</select>

	<update id="updateGgcsBySdnr">
		update ts_ggcsk set sdnr = #{sdnr} where sdbt = #{sdbt}
	</update>

	<update id="updateGgcs">
		update ts_ggcsk set sdnr = #{ggcs.sdnr}, yl1 = #{ggcs.yl1} where sdbt = #{ggcs.sdbt}
	</update>

</mapper>
