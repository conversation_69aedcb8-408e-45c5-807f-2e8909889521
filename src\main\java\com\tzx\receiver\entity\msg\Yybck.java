package com.tzx.receiver.entity.msg;

import java.io.Serializable;

public class Yybck
  implements Serializable
{
  private Long id;
  private String bcbh;
  private String yydbh;
  private String bcmc;
  private String kssj;
  private String kssx;
  private String jssj;
  private String jssx;
  private String bczt;
  private Integer jgxh;

  public Long getId()
  {
    return this.id; }

  public void setId(Long id) {
    this.id = id; }

  public String getBcbh() {
    return this.bcbh; }

  public void setBcbh(String bcbh) {
    this.bcbh = bcbh; }

  public String getBcmc() {
    return this.bcmc; }

  public void setBcmc(String bcmc) {
    this.bcmc = bcmc; }

  public String getJssj() {
    return this.jssj; }

  public void setJssj(String jssj) {
    if ((!("".equals(jssj))) && (jssj != null))
      jssj = "2000-01-01 " + jssj;

    this.jssj = jssj; }

  public String getBczt() {
    return this.bczt; }

  public void setBczt(String bczt) {
    this.bczt = bczt; }

  public String getYydbh() {
    return this.yydbh; }

  public void setYydbh(String yydbh) {
    this.yydbh = yydbh; }

  public String getKssj() {
    return this.kssj; }

  public void setKssj(String kssj) {
    if ((!("".equals(kssj))) && (kssj != null))
      kssj = "2000-01-01 " + kssj;

    this.kssj = kssj; }

  public String getKssx() {
    return this.kssx; }

  public void setKssx(String kssx) {
    this.kssx = kssx; }

  public String getJssx() {
    return this.jssx; }

  public void setJssx(String jssx) {
    this.jssx = jssx; }

  public Integer getJgxh() {
    return this.jgxh; }

  public void setJgxh(Integer jgxh) {
    this.jgxh = jgxh;
  }
}