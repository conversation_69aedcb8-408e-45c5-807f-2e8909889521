package com.tzx.receiver.common.upload;

import com.tzx.receiver.common.utils.DBUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2019-04-21
 * @Descption
 **/
public class UploadGloVar {
    public final static int KEEY_FILE_DAY = 14;
    public final static int SINGLE_TASK_UPLOAD = 5;//单次上传任务最多处理数
    private static String organizeID; //分店机构序号
    private static String organizeCode; //分店机构编号
    private static String organizeName; //分店机构名称
    private static String mqUrl;
    private static String mqUser;
    private static String mqPassword;
    private static String downName;
    private static String uploadName;

    public static String getReportDate(){
       return DBUtils.getStringBySQL("select nr from ts_bmkzk where zdmc = 'BBRQ' ");
    }


    public static String getOrganizeID() {
        if(StringUtils.isEmpty(organizeID)){
            organizeID = DBUtils.getGGCSK("FDJGXH");

        }
        return organizeID;
    }
    public static String getOrganizeCode(){
        if(StringUtils.isEmpty(organizeCode)){
            organizeCode = DBUtils.getGGCSK("FDJGBH");

        }
        return organizeCode;
    }
    public static String getOrganizeName(){
        if(StringUtils.isEmpty(organizeName)){
            organizeName = DBUtils.getStringBySQL("select jgmc1 from ts_psjgsdk where " +
                    " jgxh = '" + UploadGloVar.getOrganizeID() + "'");

        }
        return organizeName;
    }


    public static String getMqUrl() {
        if(StringUtils.isEmpty(mqUrl)){
            mqUrl = DBUtils.getGGCSK("MQUrl");

        }
        return mqUrl;
    }

    public static String getMqUser() {
        if(StringUtils.isEmpty(mqUser)){
            mqUser = DBUtils.getGGCSK("MqUser");

        }
        return mqUser;
    }
    public static String getMqPassword() {
        if(StringUtils.isEmpty(mqPassword)){
            mqPassword = DBUtils.getGGCSK("MqPassword");

        }
        return mqPassword;
    }

    public static  String getDownName() {
        if(StringUtils.isEmpty(downName)){
            downName = DBUtils.getGGCSK("Downmq.name");

        }
        return downName;
    }

    public static  String getUploadName() {
        if(StringUtils.isEmpty(uploadName)){
            uploadName = DBUtils.getGGCSK("Upmq.name");

        }
        return uploadName;
    }
    
    public static void setGloVar(String sOrganizeID, String sOrganizeCode, String sOrganizeName, String sMqUrl, String sMqUser, String sMqPassword, String sDownName, String sUploadName){
    	organizeID = sOrganizeID;
        organizeCode = sOrganizeCode;
        organizeName = sOrganizeName;
        mqUrl = sMqUrl;
        mqUser = sMqUser;
        mqPassword = sMqPassword;
        downName = sDownName;
        uploadName = sUploadName;
    }
}
