package com.tzx.receiver.common.utils;

import org.apache.commons.net.telnet.TelnetClient;

import java.io.InputStream;
import java.io.PrintStream;

/**
 * <AUTHOR>
 * @Date 2019-05-05
 * @Descption
 **/
public class NetHelper {
    public static boolean checkTelnet(String addr,int port){
        try {
            TelnetClient telnetClient = new TelnetClient("vt200");  //指明Telnet终端类型，否则会返回来的数据中文会乱码
            telnetClient.setDefaultTimeout(3000); //socket延迟时间：5000ms
            telnetClient.connect(addr,port);  //建立一个连接,默认端口是23
            InputStream inputStream = telnetClient.getInputStream(); //读取命令的流
            PrintStream pStream = new PrintStream(telnetClient.getOutputStream());  //写命令的流
            byte[] b = new byte[1024];
            int size;
            size = inputStream.read(b);
            pStream.println("exit"); //写命令
            pStream.flush(); //将命令发送到telnet Server
            if(null != pStream) {
                pStream.close();
            }
            telnetClient.disconnect();
            return size > -1;
        } catch (Exception e) {
            return false;
        }
    }
}
