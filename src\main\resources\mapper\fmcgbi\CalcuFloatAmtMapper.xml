<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.fmcgbi.rest.mapper.FmcgbiCalcuFloatAmtMapper">
	<select id="queryItemPrice" resultType="com.tzx.fmcgbi.rest.vo.ProdVo">
		select cm.cmmc1 as ProdName, cm.cmbh as ProdNo, coalesce(tx.cmjg, cm.cmdj) as Price
		from ts_cmk cm
		left join ts_ggcsk gg on gg.sdbt = 'FDJGBH'
		left join ts_psjgsdk jg on jg.jgbh = gg.sdnr
		left join ts_cmjgtxk tx on tx.jgtxbh = jg.jgtxbh and tx.cmid = cm.cmid
		where cm.cmbh in 
		<foreach item="cmbh" index="index" collection="itemCodeList" open="(" separator="," close=")">
			#{cmbh}
		</foreach>
	</select>
</mapper>
