package com.tzx.miniapp.rest.controller;

import com.tzx.miniapp.common.Data;
import com.tzx.miniapp.common.MiniAppData;
import com.tzx.miniapp.common.MiniAppLocker;
import com.tzx.miniapp.rest.service.IMiniAppFirstPayService;
import com.tzx.miniapp.rest.service.IMiniAppQmOrderPrecheck;
import com.tzx.miniapp.rest.service.IMiniAppQmOrderPrecheckNew;
import com.tzx.miniapp.rest.service.IMiniAppShopBaseInfoService;
import com.tzx.publics.base.BaseController;
import com.tzx.publics.common.BillNoData;
import com.tzx.publics.util.GlobalLockGetDataUtil;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;

@CrossOrigin
@RestController
@RequestMapping("/posapi_qm")
public class MiniAppQMController extends BaseController {
	private final static Logger LOGGER = LoggerFactory.getLogger(MiniAppQMController.class);
	
	@Autowired
	private IMiniAppShopBaseInfoService shopBaseInfoService;
	@Autowired
	private IMiniAppQmOrderPrecheck qmOrderPrecheck;
	@Autowired
	private IMiniAppFirstPayService firstPayService;
	
	@Autowired
	private GlobalLockGetDataUtil globalLockGetDataUtil;
	@Autowired
	private MiniAppLocker locker;
	
	@Autowired
	private IMiniAppQmOrderPrecheckNew qmOrderPrecheckNew;
	
	/**
	 * 基本数据同步接口，企迈调用
	 *
	 * @param request
	 * @param response
	 * @param json
	 * @return
	 */
	@RequestMapping(value = "/shop/shopBaseInfo", method = RequestMethod.POST)
	public String shopBaseInfo(HttpServletRequest request, HttpServletResponse response, @RequestBody(required = false) String json) {
		response.setContentType("text/html;charset=utf-8");
		LOGGER.info("request(shopBaseInfo)：" + json);
		String data = shopBaseInfoService.shopBaseInfo();
		String logStr = data.length() > 1000 ? data.substring(0, 1000) : data;
		LOGGER.info("response(shopBaseInfo)：" + logStr);
		return data;
	}
	
	
	/**
	 * 企迈   落单，清台接口
	 * 企迈以后接口  不在校验餐谱 是否存在，以接口数据问为准
	 * @param request
	 * @param response
	 * @param json
	 * @return
	 */
	@RequestMapping(value = "/shop/orderFirstPay", method = RequestMethod.POST)
	public String orderPrecheck(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) {
		response.setContentType("text/html;charset=utf-8");
		LOGGER.info("request(orderFirstPay)：" + json);

		String outOrderId = JSONObject.fromObject(json).optString("out_order_id");
		int need_print_bill = JSONObject.fromObject(json).optInt("need_print_bill", 1);
		Data data = new Data();
		MiniAppData dataL = new MiniAppData();
		try {
			dataL = locker.lockerByNum(outOrderId, 1);
			LOGGER.info("Type： {},TradeNo： {},locker.lockerByNum：{}", "orderFirstPay", outOrderId, dataL.isSuccess());
			if (dataL.isSuccess()) {
				data = qmOrderPrecheck.orderPrecheckBefore(JSONObject.fromObject(json));
				if (data.getSuccess() == 1) {
					BillNoData billNoData = null;
					billNoData  = globalLockGetDataUtil.getBillNoData("99", "9");
					data = qmOrderPrecheck.orderPrecheck(JSONObject.fromObject(json), billNoData);
					if (data.getSuccess() != 0 && data.getCode() != 2 && need_print_bill == 1) {
						firstPayService.sendPrintDetailed(JSONObject.fromObject(json));
						firstPayService.sendPrint(JSONObject.fromObject(json), "jzd");
						firstPayService.sendKVS(JSONObject.fromObject(json));
						firstPayService.sendPrintKichen(JSONObject.fromObject(json));
					}
				}
			} else {
				data.setSuccess(0);
				data.setMsg("账单处理中，请勿重复提交:" + outOrderId);
				data.setData(new HashMap<String, Object>());
			}
		} catch (Exception e) {
			e.printStackTrace();
			LOGGER.error("Ignore this exception", e);
			data.setSuccess(0);
			data.setMsg("系统错误:" + e);
			data.setData(new HashMap<String, Object>());
		} finally {
			if (dataL.isSuccess()) {
				locker.lockerByNum(outOrderId, 2);
				LOGGER.info("Type： {}, TradeNo： {}, locker.lockerByNum 解锁成功", "orderFirstPay", outOrderId);
			}
		}
		LOGGER.info("response(orderFirstPay)：" + JSONObject.fromObject(data).toString());
		return JSONObject.fromObject(data).toString();
	}
	
	
	/**
	 * 企迈   落单，清台接口,老娘舅特殊原因，使用code作为菜品标识
	 * 企迈以后接口  不在校验餐谱 是否存在，以接口数据问为准
	 * @param request
	 * @param response
	 * @param json
	 * @return
	 */
	@RequestMapping(value = "/shop/orderFirstPayCode", method = RequestMethod.POST)
	public String orderPrecheckCode(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) {
		response.setContentType("text/html;charset=utf-8");
		LOGGER.info("request(orderFirstPayCode)：" + json);

		String outOrderId = JSONObject.fromObject(json).optString("out_order_id");
		Data data = new Data();
		MiniAppData dataL = new MiniAppData();
		try {
			dataL = locker.lockerByNum(outOrderId, 1);
			LOGGER.info("Type： {},TradeNo： {},locker.lockerByNum：{}", "orderFirstPay", outOrderId, dataL.isSuccess());
			if (dataL.isSuccess()) {
				data = qmOrderPrecheckNew.orderPrecheckBefore(JSONObject.fromObject(json));
				if (data.getSuccess() == 1) {
					BillNoData billNoData = null;
					billNoData  = globalLockGetDataUtil.getBillNoData("99", "9");
					data = qmOrderPrecheckNew.orderPrecheckCode(JSONObject.fromObject(json), billNoData);
					if (data.getSuccess() != 0 && data.getCode() != 2) {
						firstPayService.sendPrintDetailed(JSONObject.fromObject(json));
						firstPayService.sendPrint(JSONObject.fromObject(json), "jzd");
						firstPayService.sendKVS(JSONObject.fromObject(json));
						firstPayService.sendPrintKichen(JSONObject.fromObject(json));
					}	
				}
			} else {
				data.setSuccess(0);
				data.setMsg("账单处理中，请勿重复提交:" + outOrderId);
				data.setData(new HashMap<String, Object>());
			}
		} catch (Exception e) {
			e.printStackTrace();
			LOGGER.error("Ignore this exception", e);
			data.setSuccess(0);
			data.setMsg("系统错误:" + e);
			data.setData(new HashMap<String, Object>());
		} finally {
			if (dataL.isSuccess()) {
				locker.lockerByNum(outOrderId, 2);
				LOGGER.info("Type： {}, TradeNo： {}, locker.lockerByNum 解锁成功", "orderFirstPayCode", outOrderId);
			}
		}
		LOGGER.info("response(orderFirstPayCode)：" + JSONObject.fromObject(data).toString());
		return JSONObject.fromObject(data).toString();
	}
	
}
