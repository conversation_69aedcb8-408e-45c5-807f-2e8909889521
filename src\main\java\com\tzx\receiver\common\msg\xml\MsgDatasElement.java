package com.tzx.receiver.common.msg.xml;

import java.beans.BeanInfo;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import org.apache.commons.beanutils.ConvertUtils;
import org.dom4j.Attribute;
import org.dom4j.Element;

import com.tzx.receiver.common.msg.datatype.DataTypeAttribute;
import com.tzx.receiver.common.msg.datatype.DataTypeUnit;
import com.tzx.receiver.common.msg.datatype.DateTimeAttribute;
import com.tzx.receiver.common.msg.datatype.NumberAttribute;
import com.tzx.receiver.common.msg.datatype.StringAttribute;

/**
 * 
 * 数据包元素的数据列元素类。
 * 
 * <AUTHOR>
 * 
 */
public class MsgDatasElement extends BaseDPElement {

	public static final String SIsUniqueAttrStr = "IsUnique";

	public static final String SIsRequiredAttrStr = "IsRequired";

	public static final String SIsPrimaryKeyAttrStr = "IsPrimaryKey";

	public static final String SFileNameAttrStr = "FieldName";

	public static final String SDataTypeAttrStr = "DataType";

	public static final String SSizeAttrStr = "Size";

	public static final String SPrecisionAttrStr = "Precision";

	public static final String SScaleAttrStr = "Scale";

	public static final String SSubTypeAttrStr = "SubType";

	protected static final Object[] NULL_ARGS = {};

	/**
	 * 构造一个 MsgDatasElement 对象。
	 * 
	 */
	public MsgDatasElement(DataPacketBuilder builder, Element currentNode) {
		super(builder, currentNode);
	}

	/**
	 * 返回此元素对象所在的文档路径。这里返回的是<code>MSG/DATAS</code>
	 * 
	 */
	public static String getCurrentPath() {
		return MsgElement.getCurrentPath() + DPC.XPathDelim + DPC.SMsgDatasNodeStr;
	}

	protected Element GetDataNode(String ByName) {
		return builder.getNode(currentNode, DPC.SMsgDataNodeStr,
				DPC.SMsgNameAttrStr, ByName);
	}

	protected Element FindDataNode(String ByName) {
		return builder.fndNode(currentNode, DPC.SMsgDataNodeStr,
				DPC.SMsgNameAttrStr, ByName);
	}

	protected Element GetMetaDataNode(String ByName) {
		Element dataNode;
		dataNode = GetDataNode(ByName);
		return builder.getNode(dataNode, DPC.SMsgMetaDataNodeStr);
	}

	protected Element FindMetaDataNode(String ByName) {
		Element dataNode;
		dataNode = FindDataNode(ByName);
		if (dataNode != null) {
			return builder.findNode(dataNode, DPC.SMsgMetaDataNodeStr);
		} else {
			return null;
		}
	}

	protected Element GetFieldsNode(String ByName) {
		Element MetaDataNode;
		MetaDataNode = GetMetaDataNode(ByName);
		return builder.getNode(MetaDataNode, DPC.SMsgMetaDataFieldsNodeStr);
	}

	protected Element FindFieldsNode(String ByName) {
		Element MetaDataNode = FindMetaDataNode(ByName);
		if (MetaDataNode != null) {
			return builder.findNode(MetaDataNode, DPC.SMsgMetaDataFieldsNodeStr);
		} else {
			return null;
		}
	}

	protected Element GetRowDataNode(String ByName) {
		Element dataNode;
		dataNode = GetDataNode(ByName);
		return builder.getNode(dataNode, DPC.SMsgRowDataNodeStr);
	}

	protected Element FindRowDataNode(String ByName) {
		Element dataNode;
		dataNode = FindDataNode(ByName);
		if (dataNode != null) {
			return builder.findNode(dataNode, DPC.SMsgRowDataNodeStr);
		} else {
			return null;
		}
	}

	protected void writeBean(Element node, Object bean) {
		PropertyDescriptor[] propertyDescriptors = null;
		Class beanClass = bean.getClass();
		if (beanClass != null) {
			try {
				BeanInfo beanInfo = Introspector.getBeanInfo(beanClass);
				propertyDescriptors = beanInfo.getPropertyDescriptors();
			} catch (IntrospectionException e) {
				handleException(e);
			}
		}

		if (propertyDescriptors == null) {
			propertyDescriptors = new PropertyDescriptor[0];
		}

		int size = propertyDescriptors.length;

		for (int i = 0; i < size; i++) {
			PropertyDescriptor propertyDescriptor = propertyDescriptors[i];
			String name = propertyDescriptor.getName().toUpperCase();
			if (!name.equals("CLASS")){
				Method readMethod = propertyDescriptor.getReadMethod();
				try {
					Object data = readMethod.invoke(bean, NULL_ARGS);
					builder.writeString(node, name, new String(String.valueOf(data).getBytes(),DPC.SUTF8Encoding));	//GXY 080924
				} catch (Exception e) {
					handleException(e);
				}
			}
		}
	}

	protected void readBean(Element node, Object bean) {
		PropertyDescriptor[] propertyDescriptors = null;
		Class beanClass = bean.getClass();
		if (beanClass != null) {
			try {
				BeanInfo beanInfo = Introspector.getBeanInfo(beanClass);
				propertyDescriptors = beanInfo.getPropertyDescriptors();
			} catch (IntrospectionException e) {
				handleException(e);
			}
		}

		if (propertyDescriptors == null) {
			propertyDescriptors = new PropertyDescriptor[0];
		}

		int size = propertyDescriptors.length;
		DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
		for (int i = 0; i < size; i++) {
			PropertyDescriptor propertyDescriptor = propertyDescriptors[i];

			String name = propertyDescriptor.getName();
			Method readMethod = propertyDescriptor.getReadMethod();
			Method writeMethod = propertyDescriptor.getWriteMethod();
			Class propertyType = propertyDescriptor.getPropertyType();

			if (readMethod != null) {
				name = readMethod.getName().substring(3).toUpperCase();
				try {
					String strValue = builder.readString(node, name, "[null]");
					if (!strValue.equals("[null]") & !strValue.equalsIgnoreCase("null")) {
                        Object data = null;
                        if (propertyType.equals(java.util.Date.class)) {
                            data = dateFormat.parse(strValue);
                        } else {
                            data = ConvertUtils.convert(strValue, propertyType);
                        }
						Object[] args = { data };
						writeMethod.invoke(bean, args);
					}
				} catch (Exception e) {
					handleException(e);
				}
			}
		}
	}

	/**
	 * 添加单行数据到行数据元素下。
	 * 
	 * @param byName
	 *            指定数据元素的Name属性名称的值。使用此参数值检索或新建数据元素实例。
	 * @param bean
	 *            数据提供者，跟据对象的每一个属性名称和属性值，生成相应元素属性的名称和值。
	 */

	public void addRow(String byName, Object bean) {
		Element RowDataNode = GetRowDataNode(byName);
		Element RowNode = RowDataNode.addElement(DPC.SMsgRowNodeStr);
		if (RowNode != null) {
			writeBean(RowNode, bean);
		}
	}

	/**
	 * 添加多行数据到行数据元素下。
	 * 
	 * @param byName
	 *            指定数据元素的Name属性名称的值。使用此参数值检索或新建数据元素实例。
	 * @param beans
	 *            数据提供者，对象实例列表。
	 * 
	 * @see #addRow(String, Object)
	 */

	public void addRows(String byName, List beans) {
		Element RowDataNode = GetRowDataNode(byName);
		for (int i = 0; i < beans.size(); i++) {
			Object bean = beans.get(i);
			Element RowNode = RowDataNode.addElement(DPC.SMsgRowNodeStr);
			if (RowNode != null) {
				writeBean(RowNode, bean);
			}
		}
	}

	/**
	 * 获取行数据元素下的首条数据。
	 * 
	 * @param byName
	 *            指定数据元素的Name属性名称的值。使用此参数值去检索对应的元素实例。
	 * @param beanClass
	 *            指定返回对象的类。
	 * @return beanClass类的对象实例。
	 * @throws DataPacketException
	 */

	public Object getRow(String byName, Class beanClass) throws DataPacketException {
		Element RowDataNode = FindRowDataNode(byName);
		if (RowDataNode == null) {
			throw new DataPacketException(DPC.SNotFoundDataElement);
		}
		Object bean = null;
		Element RowNode = builder.firstNode(RowDataNode);
		if (RowNode != null) {
			try {
				bean = beanClass.newInstance();
				readBean(RowNode, bean);
			} catch (Exception e) {
				handleException(e);
			}
		}
		return bean;
	}
	
	/**
	 * 获取行数据元素下的全部数据。
	 * 
	 * @param byName
	 *            指定数据元素的Name属性名称的值。使用此参数值去检索对应的元素实例。
	 * @param beanClass
	 *            指定返回对象的类名。
	 * @return beanClass类的对象实例列表。
	 * @throws DataPacketException
	 */
	public List getRows(String byName, Class beanClass) throws DataPacketException {
		Element RowDataNode = FindRowDataNode(byName);
		if (RowDataNode == null) {
			throw new DataPacketException(DPC.SNotFoundDataElement);
		}
		List<Object> beans = new ArrayList<Object>();
		Iterator iter = RowDataNode.elementIterator();
		while (iter.hasNext()) {
			Element rowNode = (Element) iter.next();
			try {
				Object bean = beanClass.newInstance();
				readBean(rowNode, bean);
				beans.add(bean);
			} catch (Exception e) {
				handleException(e);
			}
		}
		return beans;
	}


    /**
	 * 读取字段定义，读取指定数据元素中元数据元素的字段定义。
	 * 
	 * @param byName
	 *            指定数据元素的Name属性名称的值。使用此参数值去检索对应的元素实例。
	 * @param fields
	 *            字段定义列表。
	 */

	public void getFields(String byName, List<MetaDataField> fields) {
		Element fieldsNode = FindFieldsNode(byName);
		if (fieldsNode == null)
			return;
		Iterator iter = fieldsNode.elementIterator();
		while (iter.hasNext()) {
			Element fieldNode = (Element) iter.next();
			MetaDataField metaField = new MetaDataField();
			String attrValue;
			attrValue = fieldNode.attributeValue(SFileNameAttrStr, "");
			metaField.setFieldName(attrValue);

			attrValue = fieldNode.attributeValue(SDataTypeAttrStr, metaField
					.getDataType().name());
			metaField.setDataType(DataTypeUnit.DataType.valueOf(attrValue));

			DataTypeAttribute fieldAttr = metaField.getDataTypeAttribute();
			if (fieldAttr instanceof StringAttribute) {
				StringAttribute tmpAttr = (StringAttribute) fieldAttr;
				attrValue = fieldNode.attributeValue(SSizeAttrStr, String
						.valueOf(tmpAttr.getSize()));
				tmpAttr.setSize(Integer.valueOf(attrValue).intValue());
			} else if (fieldAttr instanceof NumberAttribute) {
				NumberAttribute tmpAttr = (NumberAttribute) fieldAttr;
				attrValue = fieldNode.attributeValue(SPrecisionAttrStr, String
						.valueOf(tmpAttr.getPrecision()));
				tmpAttr.setPrecision(Integer.valueOf(attrValue).intValue());
				attrValue = fieldNode.attributeValue(SScaleAttrStr, String
						.valueOf(tmpAttr.getScale()));
				tmpAttr.setScale(Integer.valueOf(attrValue).intValue());
			} else if (fieldAttr instanceof DateTimeAttribute) {
				DateTimeAttribute tmpAttr = (DateTimeAttribute) fieldAttr;
				attrValue = fieldNode.attributeValue(SSubTypeAttrStr, tmpAttr
						.getSubType().name());
				tmpAttr.setSubType(DataTypeUnit.DateTimeSubType.valueOf(attrValue));
			}

			if (metaField.getIsPrimaryKey())
				fieldNode.addAttribute(SIsPrimaryKeyAttrStr, Boolean.toString(true));
			if (metaField.getIsRequired())
				fieldNode.addAttribute(SIsRequiredAttrStr, Boolean.toString(true));
			if (metaField.getIsUnique())
				fieldNode.addAttribute(SIsUniqueAttrStr, Boolean.toString(true));
			fields.add(metaField);
		}
	}

	/**
	 * 生成字段定义,生成字段定义到指定数据元素的元数据元素中。
	 * 
	 * @param byName
	 *            指定数据元素的Name属性名称的值。使用此参数值去检索对应的数据元素实例。
	 * @param fields
	 *            字段定义列表。
	 */
	public void setFields(String byName, List<MetaDataField> fields) {
		Element FieldsNode = GetFieldsNode(byName);
		for (int i = 0; i < fields.size(); i++) {
			MetaDataField metaField = fields.get(i);
			Element fieldNode = FieldsNode.addElement(DPC.SMsgMetaDataFieldNodeStr);

			fieldNode.addAttribute(SFileNameAttrStr, metaField.getFieldName());
			fieldNode.addAttribute(SDataTypeAttrStr, metaField.getDataType().name());

			DataTypeAttribute fieldAttr = metaField.getDataTypeAttribute();
			if (fieldAttr instanceof StringAttribute) {
				StringAttribute tmpAttr = (StringAttribute) fieldAttr;
				fieldNode.addAttribute(SSizeAttrStr, String.valueOf(tmpAttr
						.getSize()));
			} else if (fieldAttr instanceof NumberAttribute) {
				NumberAttribute tmpAttr = (NumberAttribute) fieldAttr;
				fieldNode.addAttribute(SPrecisionAttrStr, String.valueOf(tmpAttr
						.getPrecision()));
				fieldNode.addAttribute(SScaleAttrStr, String.valueOf(tmpAttr
						.getScale()));
			} else if (fieldAttr instanceof DateTimeAttribute) {
				DateTimeAttribute tmpAttr = (DateTimeAttribute) fieldAttr;
				fieldNode.addAttribute(SSubTypeAttrStr, tmpAttr.getSubType().name());
			}

			if (metaField.getIsPrimaryKey())
				fieldNode.addAttribute(SIsPrimaryKeyAttrStr, Boolean.toString(true));
			if (metaField.getIsRequired())
				fieldNode.addAttribute(SIsRequiredAttrStr, Boolean.toString(true));
			if (metaField.getIsUnique())
				fieldNode.addAttribute(SIsUniqueAttrStr, Boolean.toString(true));
		}
	}

	protected void handleException(Exception e) {
		System.err.println(e);
		// ignore introspection exceptions
	}

    public List getFullSql(String dbName) throws DataPacketException {
        Element RowDataNode = FindRowDataNode(dbName);
        if (RowDataNode == null) {
            throw new DataPacketException(DPC.SNotFoundDataElement);
        }
        List<String> sqls = new ArrayList<String>();
        Iterator iter = RowDataNode.elementIterator();
        while (iter.hasNext()) {
            Element rowNode = (Element) iter.next();
            try {
                String sql = readRowSql(dbName, rowNode);
                sqls.add(sql);
            } catch (Exception e) {
                handleException(e);
            }
        }
        return sqls;
    }

    private String readRowSql(String dbName, Element rowNode) {
        Iterator itr = rowNode.attributeIterator();
        String nameSql = "INSERT INTO " + dbName + " (";
        String valueSql = " VALUES (";
        while (itr.hasNext()) {
            Attribute attr = (Attribute)itr.next();
            if ((dbName.equals("B_ZDJEFJK") && attr.getName().equalsIgnoreCase("JB")) 
                    || (dbName.equals("B_RJXFFJK") && attr.getName().equalsIgnoreCase("JB"))
                    || (dbName.equals("TC_CZTYPE") && attr.getName().equalsIgnoreCase("CZJE"))
                    || (dbName.equals("TC_CZTYPE") && attr.getName().equalsIgnoreCase("JKJE"))
                    || (dbName.equals("TC_CZTYPE") && attr.getName().equalsIgnoreCase("ZDJE"))
                    || (dbName.equals("TC_CZTYPE") && attr.getName().equalsIgnoreCase("ZSBL"))
                    || (dbName.equals("B_HYKZK") && attr.getName().equalsIgnoreCase("SKJE"))
                    || (dbName.equals("B_HYKZK") && attr.getName().equalsIgnoreCase("CZJE"))
                    || (dbName.equals("B_HYKZK") && attr.getName().equalsIgnoreCase("YJJE"))
                    || (dbName.equals("B_HYKZK") && attr.getName().equalsIgnoreCase("ZDCZ"))
                    || (dbName.equals("B_HYKZK") && attr.getName().equalsIgnoreCase("ZDSK"))
                    || (dbName.equals("B_HYKZK") && attr.getName().equalsIgnoreCase("SHOUKJE"))
                    || (dbName.equals("B_FWFK") && attr.getName().equalsIgnoreCase("GDJJ"))
                    || (dbName.equals("B_FWFK") && attr.getName().equalsIgnoreCase("CSJJ"))
                    || (dbName.equals("B_FWFK") && attr.getName().equalsIgnoreCase("DKJJ"))
                    || (dbName.equals("B_FWFK") && attr.getName().equalsIgnoreCase("ZDXF"))
                    || (dbName.equals("B_FWFK") && attr.getName().equalsIgnoreCase("FWFL"))
                    || (dbName.equals("B_LQLXK") && attr.getName().equalsIgnoreCase("LQXE"))
                    || (dbName.equals("B_MDLXK") && attr.getName().equalsIgnoreCase("MDXE"))
                    || (dbName.equals("B_TSZKK") && attr.getName().equalsIgnoreCase("ZKL"))
                    || (dbName.equals("B_JFKJFFAK") && attr.getName().equalsIgnoreCase("JFJE")))// add 080812 tzx61,总部以admin进入，做店面初始化下发的        
            {
                nameSql += attr.getName() + ",";
                if (attr.getValue() != null && attr.getValue().length() > 0) {
                    valueSql += attr.getValue() + ",";
                } else {
                    valueSql += "0,";
                }
            } else {
                nameSql += attr.getName() + ",";
                String value = "";
                if (attr.getValue() != null && !attr.getValue().equals("null")) {
                    value = attr.getValue();
                }
                valueSql += "'" + value + "',";
            }
        }

        String sql = nameSql.substring(0, nameSql.length() - 1) + ")" + valueSql.substring(0, valueSql.length() - 1) + ")";
        return sql;
    }

    public void getInsertSql(String dbName, String primary) throws DataPacketException {
        Element RowDataNode = FindRowDataNode(dbName);
        if (RowDataNode == null) {
            throw new DataPacketException(DPC.SNotFoundDataElement);
        }
        Iterator iter = RowDataNode.elementIterator();
        while (iter.hasNext()) {
            Element rowNode = (Element) iter.next();
            try {
                readSingleSql(dbName, primary, rowNode);
                
            } catch (Exception e) {
                handleException(e);
            }
        }
        
    }
    private void readSingleSql(String dbName, String primary, Element rowNode) {
        Iterator itr = rowNode.attributeIterator();
        String insertNameSql = "INSERT INTO  " + dbName + " (";
        String insertValueSql = " VALUES (";
        
        String updateSql = "UPDATE " + dbName + " SET ";
        
        String[] primarys = primary.split(",");
        String primarySqls = "";
        while (itr.hasNext()) {
            Attribute attr = (Attribute)itr.next();
            if ((dbName.equals("B_ZDJEFJK") && attr.getName().equalsIgnoreCase("JB")) 
                    || (dbName.equals("B_RJXFFJK") && attr.getName().equalsIgnoreCase("JB"))
                    || (dbName.equals("TC_CZTYPE") && attr.getName().equalsIgnoreCase("CZJE"))
                    || (dbName.equals("TC_CZTYPE") && attr.getName().equalsIgnoreCase("JKJE"))
                    || (dbName.equals("TC_CZTYPE") && attr.getName().equalsIgnoreCase("ZDJE"))
                    || (dbName.equals("TC_CZTYPE") && attr.getName().equalsIgnoreCase("ZSBL"))
                    || (dbName.equals("B_HYKZK") && attr.getName().equalsIgnoreCase("SKJE"))
                    || (dbName.equals("B_HYKZK") && attr.getName().equalsIgnoreCase("CZJE"))
                    || (dbName.equals("B_HYKZK") && attr.getName().equalsIgnoreCase("YJJE"))
                    || (dbName.equals("B_HYKZK") && attr.getName().equalsIgnoreCase("ZDCZ"))
                    || (dbName.equals("B_HYKZK") && attr.getName().equalsIgnoreCase("ZDSK"))
                    || (dbName.equals("B_HYKZK") && attr.getName().equalsIgnoreCase("SHOUKJE"))
                    || (dbName.equals("B_FWFK") && attr.getName().equalsIgnoreCase("GDJJ"))
                    || (dbName.equals("B_FWFK") && attr.getName().equalsIgnoreCase("CSJJ"))
                    || (dbName.equals("B_FWFK") && attr.getName().equalsIgnoreCase("DKJJ"))
                    || (dbName.equals("B_FWFK") && attr.getName().equalsIgnoreCase("ZDXF"))
                    || (dbName.equals("B_FWFK") && attr.getName().equalsIgnoreCase("FWFL"))
                    || (dbName.equals("B_LQLXK") && attr.getName().equalsIgnoreCase("LQXE"))
                    || (dbName.equals("B_MDLXK") && attr.getName().equalsIgnoreCase("MDXE"))
                    || (dbName.equals("B_TSZKK") && attr.getName().equalsIgnoreCase("ZKL"))
                    || (dbName.equals("B_JFKJFFAK") && attr.getName().equalsIgnoreCase("JFJE")))// add 080812 tzx61,总部“积分转换规则设置”下发的        
            {
                insertNameSql += attr.getName() + ",";
                if (attr.getValue() != null && attr.getValue().length() > 0) {
                    insertValueSql += attr.getValue() + ",";
                    updateSql += attr.getName() + "=" + attr.getValue() + ","; 
                } else {
                    insertValueSql += "0,";
                    updateSql += attr.getName() + "=0,"; 
                }
            } else {
                insertNameSql += attr.getName() + ",";
                String value = "";
                if (attr.getValue() != null && !attr.getValue().equals("null")) {
                    value = attr.getValue();
                }
                insertValueSql += "'" + value + "',";
                updateSql += attr.getName() + "='" + value + "',"; 
            }
            
            for (int i = 0; i < primarys.length; i++) {
                if (attr.getName().equalsIgnoreCase(primarys[i])) {
                    primarySqls += attr.getName() + "='" + attr.getValue() + "' AND ";
                }
            }
        }
        
        insertNameSql.substring(0, insertNameSql.length() - 1);
        insertValueSql.substring(0, insertValueSql.length() - 1);
        String insertSql = insertNameSql.substring(0, insertNameSql.length() - 1) + ")" + insertValueSql.substring(0, insertValueSql.length() - 1) + ")";
        insertSqlList.add(insertSql);
        primarySqlList.add(primarySqls.substring(0, primarySqls.length() - 4));
        updateSqlList.add(updateSql.substring(0, updateSql.length() - 1) + " WHERE " + primarySqls.substring(0, primarySqls.length() - 4));
    }
    private List<String> insertSqlList = new ArrayList<String>();
    private List<String> updateSqlList = new ArrayList<String>();
    
    private List<String> primarySqlList = new ArrayList<String>();
    public List getPrimaryList() {
        return primarySqlList;
    }
    public List getInsertList() {
        return insertSqlList;
    }
    public List getUpdateList() {
        return updateSqlList;
    }

	@SuppressWarnings("unchecked")
	public List getDeleteSql(String dbName, String primary) throws DataPacketException {
		List resultList = new ArrayList();
        Element RowDataNode = FindRowDataNode(dbName);
        if (RowDataNode == null) {
            throw new DataPacketException(DPC.SNotFoundDataElement);
        }
        Iterator iter = RowDataNode.elementIterator();
        while (iter.hasNext()) {
            Element rowNode = (Element) iter.next();
            try {
                String sql = readDeleteSql(dbName, primary, rowNode);
                resultList.add(sql);
            } catch (Exception e) {
                handleException(e);
            }
        }
        return resultList;
    }
    private String readDeleteSql(String dbName, String primary, Element rowNode) {
        Iterator itr = rowNode.attributeIterator();
        String deleteSql = "DELETE FROM " + dbName;
        
        
        String[] primarys = primary.split(",");
        String primarySqls = "";
        while (itr.hasNext()) {
            Attribute attr = (Attribute)itr.next();
            for (int i = 0; i < primarys.length; i++) {
                if (attr.getName().equalsIgnoreCase(primarys[i])) {
                    primarySqls += attr.getName() + "='" + attr.getValue() + "' AND ";
                }
            }
        }
        return deleteSql + " WHERE " + primarySqls.substring(0, primarySqls.length() - 4); 

    }
}
