package com.tzx.kvs.rest.model;

import java.io.Serializable;
import java.math.BigDecimal;

import com.tzx.publics.base.BaseEntity;

public class TqKvsmxk extends BaseEntity implements Serializable {

	private static final long serialVersionUID = 1L;
	private int rwid;
    private String kdzdbh;
    private String cmbh;
    private String cmmc1;
    private String cmmc2;
    private String dwbh;
    private String cmggbh;
    private BigDecimal cmdj;
    private BigDecimal cmsl;
    private BigDecimal cmje;
    private BigDecimal sjje;
    private Object fsbbrq;
    private String fsskjh;
    private String yhfsbh;
    private String xlbh;
    private String cmsx;
    private String wdbz;
    private Integer tcid;
    private Integer tcdch;
    private Integer fzdch;
    private Object tmbj;
    private Integer dcxh;
    private String kwbz;
    private String tcsfgh;
    private String sfxsmx;
    private String xsms;
    private Integer cpbj;
    private String memo;
    private Integer scqrbj;
    private Integer jjcrwid;

	public int getRwid() {
		return rwid;
	}

	public void setRwid(int rwid) {
		this.rwid = rwid;
	}

	public String getKdzdbh() {
		return kdzdbh;
	}

	public void setKdzdbh(String kdzdbh) {
		this.kdzdbh = kdzdbh;
	}

	public String getCmbh() {
		return cmbh;
	}

	public void setCmbh(String cmbh) {
		this.cmbh = cmbh;
	}

	public String getCmmc1() {
		return cmmc1;
	}

	public void setCmmc1(String cmmc1) {
		this.cmmc1 = cmmc1;
	}

	public String getCmmc2() {
		return cmmc2;
	}

	public void setCmmc2(String cmmc2) {
		this.cmmc2 = cmmc2;
	}

	public String getDwbh() {
		return dwbh;
	}

	public void setDwbh(String dwbh) {
		this.dwbh = dwbh;
	}

	public String getCmggbh() {
		return cmggbh;
	}

	public void setCmggbh(String cmggbh) {
		this.cmggbh = cmggbh;
	}

	public BigDecimal getCmdj() {
		return cmdj;
	}

	public void setCmdj(BigDecimal cmdj) {
		this.cmdj = cmdj;
	}

	public BigDecimal getCmsl() {
		return cmsl;
	}

	public void setCmsl(BigDecimal cmsl) {
		this.cmsl = cmsl;
	}

	public BigDecimal getCmje() {
		return cmje;
	}

	public void setCmje(BigDecimal cmje) {
		this.cmje = cmje;
	}

	public BigDecimal getSjje() {
		return sjje;
	}

	public void setSjje(BigDecimal sjje) {
		this.sjje = sjje;
	}

	public Object getFsbbrq() {
		return fsbbrq;
	}

	public void setFsbbrq(Object fsbbrq) {
		this.fsbbrq = fsbbrq;
	}

	public String getFsskjh() {
		return fsskjh;
	}

	public void setFsskjh(String fsskjh) {
		this.fsskjh = fsskjh;
	}

	public String getYhfsbh() {
		return yhfsbh;
	}

	public void setYhfsbh(String yhfsbh) {
		this.yhfsbh = yhfsbh;
	}

	public String getXlbh() {
		return xlbh;
	}

	public void setXlbh(String xlbh) {
		this.xlbh = xlbh;
	}

	public String getCmsx() {
		return cmsx;
	}

	public void setCmsx(String cmsx) {
		this.cmsx = cmsx;
	}

	public String getWdbz() {
		return wdbz;
	}

	public void setWdbz(String wdbz) {
		this.wdbz = wdbz;
	}

	public Integer getTcid() {
		return tcid;
	}

	public void setTcid(Integer tcid) {
		this.tcid = tcid;
	}

	public Integer getTcdch() {
		return tcdch;
	}

	public void setTcdch(Integer tcdch) {
		this.tcdch = tcdch;
	}

	public Integer getFzdch() {
		return fzdch;
	}

	public void setFzdch(Integer fzdch) {
		this.fzdch = fzdch;
	}

	public Object getTmbj() {
		return tmbj;
	}

	public void setTmbj(Object tmbj) {
		this.tmbj = tmbj;
	}

	public Integer getDcxh() {
		return dcxh;
	}

	public void setDcxh(Integer dcxh) {
		this.dcxh = dcxh;
	}

	public String getKwbz() {
		return kwbz;
	}

	public void setKwbz(String kwbz) {
		this.kwbz = kwbz;
	}

	public String getTcsfgh() {
		return tcsfgh;
	}

	public void setTcsfgh(String tcsfgh) {
		this.tcsfgh = tcsfgh;
	}

	public String getSfxsmx() {
		return sfxsmx;
	}

	public void setSfxsmx(String sfxsmx) {
		this.sfxsmx = sfxsmx;
	}

	public String getXsms() {
		return xsms;
	}

	public void setXsms(String xsms) {
		this.xsms = xsms;
	}

	public Integer getCpbj() {
		return cpbj;
	}

	public void setCpbj(Integer cpbj) {
		this.cpbj = cpbj;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public Integer getScqrbj() {
		return scqrbj;
	}

	public void setScqrbj(Integer scqrbj) {
		this.scqrbj = scqrbj;
	}

	public Integer getJjcrwid() {
		return jjcrwid;
	}

	public void setJjcrwid(Integer jjcrwid) {
		this.jjcrwid = jjcrwid;
	}

}
