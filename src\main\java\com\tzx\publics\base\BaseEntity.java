package com.tzx.publics.base;

import javax.persistence.Transient;
import java.io.Serializable;
import java.util.List;

/**
 * 基础信息
 *
 * <AUTHOR>
 * @since 2018-02-05
 */
public class BaseEntity implements Serializable {
   
	private static final long serialVersionUID = 1L;

	@Transient
    private Integer page = 1;

    @Transient
    private Integer rows = 10;

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getRows() {
        return rows;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }
    @Transient
    private int level;
    @Transient
    private Boolean hasChild;
    @Transient
    private List<?> children;
    @Transient
    private Long parentId1;

	public int getLevel() {
		return level;
	}

	public void setLevel(int level) {
		this.level = level;
	}

	public Boolean getHasChild() {
		return hasChild;
	}

	public void setHasChild(Boolean hasChild) {
		this.hasChild = hasChild;
	}

	public List<?> getChildren() {
		return children;
	}

	public void setChildren(List<?> children) {
		this.children = children;
	}

	public Long getParentId1() {
		return parentId1;
	}

	public void setParentId1(Long parentId1) {
		this.parentId1 = parentId1;
	}
    
}
