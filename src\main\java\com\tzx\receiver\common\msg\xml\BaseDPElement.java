package com.tzx.receiver.common.msg.xml;

import org.dom4j.Element;


/**
 * 
 * 数据包元素的基础类。
 * 
 * <AUTHOR>
 * 
 */
public class BaseDPElement {

	protected DataPacketBuilder builder;

	protected Element currentNode;

	public BaseDPElement(DataPacketBuilder builder, Element currentNode) {
		this.builder = builder;
		this.currentNode = currentNode;
	}

	/**
	 * 返回此元素对象的<code>DataPacketBuilder</code>。
	 * 
	 * @return <code>{@link DataPacketBuilder}</code>对象实例。
	 */

	public DataPacketBuilder getBuilder() {
		return builder;
	}

	/**
	 * 返回与此元素对象关联的<code>Element</code>对象实例。
	 * 
	 * @return <code>{@link Element}</code>对象实例。
	 */

	public Element getCurrentNode() {
		return currentNode;
	}

	/**
	 * 返回此元素对象的路径。
	 * 
	 * @return
	 * 
	 */

	public static String getCurrentPath() {
		throw new AbstractMethodError();
	}
}
