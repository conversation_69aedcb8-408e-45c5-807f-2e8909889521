package com.tzx.commapi.rest.service.impl;

import com.tzx.commapi.common.CommApiException;
import com.tzx.commapi.rest.service.ICommApiService;
import com.tzx.commapi.rest.service.ICommSetApiService;
import com.tzx.commapi.rest.vo.CommApiData;
import com.tzx.publics.util.LockerByList;
import com.tzx.publics.util.LockerFactory;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;

/**
 * Created by <PERSON>xh on 2020-03-23.
 */
@Service
public class CommSetApiService implements ICommSetApiService {
    private final static Logger LOGGER = LoggerFactory.getLogger(CommSetApiService.class);
    //通用api服务
    @Autowired
    private ICommApiService commApiService;
    //通用 锁接口
    @Autowired
    private LockerFactory lockerFactory;

    @Override
    public String SetEcoSound(CommApiData data, JSONObject jsonObject) {
        String methodTag = "SetEcoSound";
        Boolean IsLock = false;
        data.setCode(1);
        ArrayList<String> arrayList = new ArrayList<String>();
        arrayList.add("SetEcoSound");
        LockerByList lockerByList = lockerFactory.getEcoSoundSetLocker();
        try {
            LOGGER.info("请求包文 {}",jsonObject.toString());
            IsLock = lockerByList.Lock(arrayList, 1);
            LOGGER.info("response{} lockid {} Lock：{} ", methodTag, StringUtils.join(arrayList, ","), IsLock);
            if (IsLock) {
                HashMap<String, String> inparam = new HashMap<String, String>();
                commApiService.SetEcoSound(data, inparam, jsonObject);
                LOGGER.info("response{}：{}", methodTag, JSONObject.fromObject(data).toString());
                return JSONObject.fromObject(data).toString();
            } else {
                data.setMsg("正在进行操作，请稍后重试！");
                data.setCode(1);
            }

            return JSONObject.fromObject(data).toString();

        }
        catch(CommApiException comme){
            data.setMsg(comme.getMessage());
            data.setCode(1);
            LOGGER.info("response {}：CommApiException {}",methodTag,JSONObject.fromObject(data).toString());
            return JSONObject.fromObject(data).toString();
        }
        catch (Exception e) {
            e.printStackTrace();
            data.setMsg("内部错误");
            data.setCode(1);
            LOGGER.error("response {}：otherExcetion {}",methodTag,e.getMessage());
            return JSONObject.fromObject(data).toString();
        } finally {
            if(IsLock) {
                lockerByList.UnLock(arrayList);
                LOGGER.info("response({}) lockerid {} unlock解锁成功",methodTag,StringUtils.join(arrayList,","));
            }
        }
    }

    @Override
    public String GetEcoSound(CommApiData data, JSONObject jsonObject) {
        String methodTag = "GetEcoSound";
        Boolean IsLock = false;
        data.setCode(1);
        ArrayList<String> arrayList = new ArrayList<String>();
        arrayList.add("SetEcoSound");
        LockerByList lockerByList = lockerFactory.getEcoSoundSetLocker();
        try {
            LOGGER.info("请求包文 {}",jsonObject.toString());
            IsLock = lockerByList.Lock(arrayList, 1);
            LOGGER.info("response{} lockid {} Lock：{} ", methodTag, StringUtils.join(arrayList, ","), IsLock);
            if (IsLock) {
                HashMap<String, String> inparam = new HashMap<String, String>();
                commApiService.GetEcoSound(data, inparam, jsonObject);
                LOGGER.info("response{}：{}", methodTag, JSONObject.fromObject(data).toString());
                return JSONObject.fromObject(data).toString();
            } else {
                data.setMsg("正在进行操作，请稍后重试！");
                data.setCode(1);
            }

            return JSONObject.fromObject(data).toString();

        }
        catch(CommApiException comme){
            data.setMsg(comme.getMessage());
            data.setCode(1);
            LOGGER.info("response {}：CommApiException {}",methodTag,JSONObject.fromObject(data).toString());
            return JSONObject.fromObject(data).toString();
        }
        catch (Exception e) {
            e.printStackTrace();
            data.setMsg("内部错误");
            data.setCode(1);
            LOGGER.error("response {}：otherExcetion {}",methodTag,e.getMessage());
            return JSONObject.fromObject(data).toString();
        } finally {
            if(IsLock) {
                lockerByList.UnLock(arrayList);
                LOGGER.info("response({}) lockerid {} unlock解锁成功",methodTag,StringUtils.join(arrayList,","));
            }
        }
    }
}

