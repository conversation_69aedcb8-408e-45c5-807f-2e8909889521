package com.tzx.miniapp.rest.service.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.tzx.miniapp.common.Data;
import com.tzx.miniapp.common.SysDictionary;
import com.tzx.miniapp.rest.mapper.MiniAppFirstPayMapper;
import com.tzx.miniapp.rest.mapper.MiniAppShopBaseInfoMapper;
import com.tzx.miniapp.rest.mapper.MiniAppShopStatusMapper;
import com.tzx.miniapp.rest.mapper.MiniAppTsPsjgsdkMapper;
import com.tzx.miniapp.rest.service.IData;
import com.tzx.miniapp.rest.service.IMiniAppZsSyncData;
import com.tzx.publics.util.PropertiesUtil;
import com.tzx.publics.util.SendRequest;

import net.sf.json.JSONObject;

@Service
public class MiniAppZsSyncDataImpl implements IMiniAppZsSyncData {

    private final static Logger LOGGER = LoggerFactory.getLogger(MiniAppZsSyncDataImpl.class);

    @Autowired
    MiniAppFirstPayMapper firstPayMapper;

    @Autowired
    MiniAppTsPsjgsdkMapper tsPsjgsdkMapper;
    
    @Autowired
    MiniAppShopBaseInfoMapper shopBaseInfoMapper;
    
    @Autowired
	MiniAppShopStatusMapper shopStatusMapper;

    @Override
    public Data synchrodata(JSONObject json) {
    	Data data = new Data();
//    	String propertiesPath = "/application.properties";
//		String zsApi = PropertiesUtil.readValueForClasses(propertiesPath, "zhongshang_api");
    	
		// 组装数据，发送
    	String zsApi = shopBaseInfoMapper.getTthird("ZHONGS", "URL");
		if(null == zsApi){
			zsApi = "";
		}
    	//1 同步菜品,类别
		sync(new MiniAppZsSyncDish(shopBaseInfoMapper, "CMSX_DP"), zsApi);
		// 2 同步分组
		sync(new MiniAppZsSyncDishGroup(shopBaseInfoMapper), zsApi);
		// 3 同步套餐
		sync(new MiniAppZsSyncDish(shopBaseInfoMapper, "CMSX_TC"), zsApi);
		
        data.setSuccess(SysDictionary.SUCCESS);
        data.setMsg("通知同步数据！");
        return data;
    }

    public static void sync(IData<?> data, String zsApi) {
		String rest = null;	
		try {
			// 拼接域名+接口地址
			String url = zsApi + data.getUrl();
			LOGGER.debug(url);
			// 发
			rest = SendRequest.zsPost(url,data.getParams());
			LOGGER.info("rest：" + rest);
		} catch (Exception e) {
			LOGGER.error(e.getMessage(),e);
		}
		LOGGER.debug(rest);
	}

    @Override
    public Data syncStoreIsOpen(JSONObject json) {
    	Data data = new Data();
//    	String propertiesPath = "/application.properties";
//		String zsApi = PropertiesUtil.readValueForClasses(propertiesPath, "zhongshang_api");
    	String zsApi = shopBaseInfoMapper.getTthird("ZHONGS", "URL");
		if(null == zsApi){
			zsApi = "";
		}
		// pos的开业状态，0 停止营业  1 开始营业
//		int isOpenShop = json.optInt("isOpenShop");
    	// 组装数据，发送
		sync(new MiniAppZsSyncStoreIsOpen(shopBaseInfoMapper, shopStatusMapper), zsApi);
		
        data.setSuccess(SysDictionary.SUCCESS);
        data.setMsg("同步门店状态成功！");
        return data;
    }
    
}
