<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.mobilepos.rest.mapper.MobilePosTsCmkMapper">
	<select id="findDishBasicData" resultType="com.tzx.mobilepos.rest.vo.Dish">
		select * from (select cm.cmid,cl.clid as item_menu_id,cl.id as details_id, cl.xmid as item_id ,cl.xmbh as item_no,
		cl.xmmc1 as item_name,cm.dwbh as unit_name,coalesce(tx.cmjg, cl.xmdj) as price,cm.foodboxset as foodboxid, 
		cm.pydm as phonetic_code, cl.kssj as start_time,cl.jssj as end_time, cl.ksrq as start_date, cl.jsrq as end_date,
		cm.cmsx as is_combo,cl.yhfsid,cl.yhfsbh,cm.sfxsmx,(gg2.sdnr||'/'||cm.picpath) as picpath, cl.showxh as number, 
		case when cl.yl3 = '' then '1,2,3,4,5,6,0' else cl.yl3 end as daysOfWeek, coalesce(tx.vipprice, '-1') as vip_price, 
		coalesce (cm.ifspec,'N') as ifspec, cl.yl4  
		from tq_clmxk cl
		left join ts_cmk cm on cm.cmid = cl.xmid 
		left join ts_ggcsk gg on sdbt = 'FDJGBH' 
		left join ts_ggcsk gg2 on gg2.sdbt = 'TZXIMGWEBADD' 
		left join ts_psjgsdk jg on jg.jgbh = gg.sdnr 
		left join ts_cmjgtxk tx on tx.jgtxbh = jg.jgtxbh and tx.cmid = cl.xmid
		left join ts_gqk gq on cl.xmid = gq.cmid
		left join tq_clsdk cls on cls.id=cl.clid
		left join tq_cbsdk cbs on cbs.id=cls.cbid
		where cl.clmxlb = #{clmxlb} and cbs.yl1 = 'TS'
		and cast(cl.ksrq AS DATE) &lt;= CURRENT_DATE
		and cast(cl.jsrq AS DATE) &gt;= CURRENT_DATE) a 
		where a.daysOfWeek like '%${week}%' and a.cmid is not null and coalesce (a.yl4,'1') &lt;&gt; '2'
    </select>
	<select id="findTcmxByXmid" resultType="com.tzx.mobilepos.rest.model.TsTcmxk">
		select * from ts_tcmxk where xmid = #{xmid}
	</select>
	
	<select id="findSpecBasicData" resultType="com.tzx.mobilepos.rest.vo.Dish">
		select cm.cmid as details_id, iis.itemid as item_id, cm.cmbh as item_no, cm.cmmc1 as item_name,
		cm.dwbh as unit_name, coalesce(tx.cmjg, cm.cmdj) as price, cm.foodboxset as foodboxid, 
		cm.pydm as phonetic_code, cm.cmsx as is_combo,cm.sfxsmx, coalesce(tx.vipprice, '-1') as vip_price, 
		coalesce (cm.ifspec,'N') as ifspec 
		from tq_iteminfospec iis inner join ts_cmk cm on iis.specitemid = cm.cmid
		left join ts_ggcsk gg on sdbt = 'fdjgbh' 
		left join ts_psjgsdk jg on jg.jgbh = gg.sdnr 
		left join ts_cmjgtxk tx on tx.jgtxbh = jg.jgtxbh and tx.cmid = cm.cmid
		where iis.itemid in (select distinct xmid from tq_clmxk)
    </select>
</mapper>
