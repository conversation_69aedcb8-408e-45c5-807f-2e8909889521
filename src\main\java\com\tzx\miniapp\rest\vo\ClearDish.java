package com.tzx.miniapp.rest.vo;

import java.io.Serializable;

public class ClearDish implements Serializable {

	private int dishid;
	private int dnid;
	private int meanid;
	private int type;
	private int status;
	private double count;
	private int pkid;
	private String dishsno;

	public int getDishid() {
		return dishid;
	}

	public void setDishid(int dishid) {
		this.dishid = dishid;
	}

	public int getDnid() {
		return dnid;
	}

	public void setDnid(int dnid) {
		this.dnid = dnid;
	}

	public int getMeanid() {
		return meanid;
	}

	public void setMeanid(int meanid) {
		this.meanid = meanid;
	}

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

	public int getStatus() {
		return status;
	}

	public void setStatus(int status) {
		this.status = status;
	}

	public double getCount() {
		return count;
	}

	public void setCount(double count) {
		this.count = count;
	}

	public int getPkid() {
		return pkid;
	}

	public void setPkid(int pkid) {
		this.pkid = pkid;
	}

	public String getDishsno() {
		return dishsno;
	}

	public void setDishsno(String dishsno) {
		this.dishsno = dishsno;
	}

}
