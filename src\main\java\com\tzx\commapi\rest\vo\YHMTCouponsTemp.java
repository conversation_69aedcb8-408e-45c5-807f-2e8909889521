package com.tzx.commapi.rest.vo;

import java.math.BigDecimal;

/**
 * Created by <PERSON>x<PERSON> on 2020-05-11.
 */
public class YHMTCouponsTemp {
    private Integer    yhfsid;
    private Integer    clmxid;
    private String     kdzdbh;
    private BigDecimal payableamt;
    private String     pqlx;
    private String     yzm;
    private Integer    jzid;
    private Integer    optype;
    private BigDecimal sjje;
    private Integer    cmid;
    private BigDecimal buyprice;
    private Integer    wdrwid;

    public Integer getYhfsid() {
        return yhfsid;
    }

    public void setYhfsid(Integer yhfsid) {
        this.yhfsid = yhfsid;
    }

    public Integer getClmxid() {
        return clmxid;
    }

    public void setClmxid(Integer clmxid) {
        this.clmxid = clmxid;
    }

    public String getKdzdbh() {
        return kdzdbh;
    }

    public void setKdzdbh(String kdzdbh) {
        this.kdzdbh = kdzdbh;
    }

    public BigDecimal getPayableamt() {
        return payableamt;
    }

    public void setPayableamt(BigDecimal payableamt) {
        this.payableamt = payableamt;
    }

    public String getPqlx() {
        return pqlx;
    }

    public void setPqlx(String pqlx) {
        this.pqlx = pqlx;
    }

    public String getYzm() {
        return yzm;
    }

    public void setYzm(String yzm) {
        this.yzm = yzm;
    }

    public Integer getJzid() {
        return jzid;
    }

    public void setJzid(Integer jzid) {
        this.jzid = jzid;
    }

    public Integer getOptype() {
        return optype;
    }

    public void setOptype(Integer optype) {
        this.optype = optype;
    }

    public BigDecimal getSjje() {
        return sjje;
    }

    public void setSjje(BigDecimal sjje) {
        this.sjje = sjje;
    }

    public Integer getCmid() {
        return cmid;
    }

    public void setCmid(Integer cmid) {
        this.cmid = cmid;
    }

    public BigDecimal getBuyprice() {
        return buyprice;
    }

    public void setBuyprice(BigDecimal buyprice) {
        this.buyprice = buyprice;
    }

    public Integer getWdrwid() {
        return wdrwid;
    }

    public void setWdrwid(Integer wdrwid) {
        this.wdrwid = wdrwid;
    }
}
