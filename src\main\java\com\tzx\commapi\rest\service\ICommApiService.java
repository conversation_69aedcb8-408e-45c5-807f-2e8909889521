package com.tzx.commapi.rest.service;

import com.tzx.commapi.rest.vo.CommApiData;
import net.sf.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;

/**
 * Created by <PERSON>xh on 2020-01-14.
 */
public interface ICommApiService {
    public CommApiData SetBefore(CommApiData data, HashMap<String,String> inparam, JSONObject jsondata);
    public CommApiData SetProcess(CommApiData data, HashMap<String,String> inparam, JSONObject jsondata);
    public CommApiData SetAfter(CommApiData data, HashMap<String,String> inparam, JSONObject jsondata);

    public CommApiData AddBefore(CommApiData data, HashMap<String,String> inparam, JSONObject jsondata);
    public CommApiData AddProcess(CommApiData data, HashMap<String,String> inparam, JSONObject jsondata);
    public CommApiData AddAfter(CommApiData data, HashMap<String,String> inparam, JSONObject jsondata);

    public CommApiData DecBefore(CommApiData data, HashMap<String,String> inparam, JSONObject jsondata);
    public CommApiData DecProcess(CommApiData data, HashMap<String,String> inparam, JSONObject jsondata);
    public CommApiData DecAfter(CommApiData data, HashMap<String,String> inparam, JSONObject jsondata);


    public ArrayList<String> GetSetList(JSONObject jsondata);
    public ArrayList<String> GetAddList(JSONObject jsondata);
    public ArrayList<String> GetDecList(JSONObject jsondata);

    public CommApiData SetEcoSound(CommApiData data, HashMap<String,String> inparam, JSONObject jsondata);
    public CommApiData GetEcoSound(CommApiData data, HashMap<String,String> inparam, JSONObject jsondata);
    
    public void qmMemberRequest(CommApiData data, JSONObject jsondata);
    
    public void merchantRequest(CommApiData data, JSONObject jsondata);

    public void qiMaiOtherRequest(CommApiData data, JSONObject jsondata);
    
    public void merchantRequestPost(CommApiData data, JSONObject jsondata);

    public void sendSmsPost(CommApiData data, JSONObject jsondata);
    
    public void ecoRequestPost(CommApiData data, JSONObject jsondata);

    public void getWyList(CommApiData data, JSONObject jsondata);

    public void repetitionSendWy(CommApiData data, JSONObject jsondata);

    public void businessData(CommApiData data, JSONObject jsondata);

    public void getEcoOrderBack(CommApiData data, JSONObject jsondata);
}
