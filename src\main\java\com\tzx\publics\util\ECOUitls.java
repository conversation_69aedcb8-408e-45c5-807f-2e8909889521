package com.tzx.publics.util;



import com.tzx.publics.listener.InitDataListener;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.codec.digest.DigestUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;

public class ECOUitls {

	public static String getSin(JSONObject jsonParam) {
		String appkey = InitDataListener.thirdMap.get("ECO_APPKEY");
		/*==============自定义序列化json，根据key和value判断是否序列化=============*/
		// 按照 key 的 ASCII 码字典序升序排序 JSON 对象
		JSONObject sortedJsonObject = sortJsonObject(jsonParam);
		// 将排序后的 JSON 对象转换为 URL 键值对字符串
		String jsonParent = generateUrlParams(sortedJsonObject);
		StringBuilder sb = new StringBuilder(jsonParent);
		sb.append(appkey);
		System.out.println("加密前："+sb.toString());
		Map<String,String> map = new HashMap<String,String>();
		return DigestUtils.md5Hex(sb.toString());
	}

	private static JSONObject sortJsonObject(JSONObject jsonObject) {
		TreeMap<String, Object> sortedMap = new TreeMap<>(new Comparator<String>() {
			@Override
			public int compare(String key1, String key2) {
				return key1.compareTo(key2);
			}
		});

		for (Object obj : jsonObject.entrySet()) {
			Map.Entry<String, Object> entry = (Map.Entry<String, Object>) obj;
			String key = entry.getKey();
			Object value = entry.getValue();

			if (value instanceof JSONArray) {
				JSONArray jsonArray = (JSONArray) value;
				if (!jsonArray.isEmpty()) {
					sortedMap.put(key, sortJsonArray(jsonArray)); // 递归排序子数组
				}
			} else if (value instanceof JSONObject) {
				JSONObject nestedJsonObject = (JSONObject) value;
				if (!nestedJsonObject.isEmpty()) {
					sortedMap.put(key, sortJsonObject(nestedJsonObject)); // 递归排序子对象
				}
			} else if (value != null && !(value instanceof Collection) && !value.toString().isEmpty()) {
				sortedMap.put(key, value);
			}
		}

		return JSONObject.fromObject(sortedMap);
	}

	private static JSONArray sortJsonArray(JSONArray jsonArray) {
		List<Object> sortedList = new ArrayList<>();
		for (Object obj : jsonArray) {
			if (obj instanceof JSONArray) {
				JSONArray nestedJsonArray = (JSONArray) obj;
				if (!nestedJsonArray.isEmpty()) {
					sortedList.add(sortJsonArray(nestedJsonArray)); // 递归排序子数组
				}
			} else if (obj instanceof JSONObject) {
				JSONObject nestedJsonObject = (JSONObject) obj;
				if (!nestedJsonObject.isEmpty()) {
					sortedList.add(sortJsonObject(nestedJsonObject)); // 递归排序子对象
				}
			} else if (obj != null && !(obj instanceof Collection) && !obj.toString().isEmpty()) {
				sortedList.add(obj);
			}
		}
		Collections.sort(sortedList, new Comparator() {
			@Override public int compare(Object o1, Object o2) {
				return o1.toString().compareTo(o2.toString());
			}
		});
		return JSONArray.fromObject(sortedList);
	}

	private static String generateUrlParams(JSONObject jsonObject) {
		StringBuilder sb = new StringBuilder();

		for (Object obj : jsonObject.entrySet()) {
			Map.Entry<String, Object> entry = (Map.Entry<String, Object>) obj;
			String key = entry.getKey();
			Object value = entry.getValue();

			if (value instanceof JSONArray) {
				JSONArray jsonArray = (JSONArray) value;
				if (!jsonArray.isEmpty()) {
					for (int i = 0; i < jsonArray.size(); i++) {
						Object arrayElement = jsonArray.get(i);
//						try {
//							String encodedKey = encodeValue(key + "[" + i + "]");
//							if (arrayElement instanceof JSONObject) {
//								sb.append(generateUrlParams((JSONObject) arrayElement, encodedKey));
//							} else {
//								String encodedValue = encodeValue(arrayElement.toString());
//								sb.append(encodedKey).append("=").append(encodedValue).append("&");
//							}
//						} catch (UnsupportedEncodingException e) {
//							e.printStackTrace();
//						}
						String encodedKey = key + "[" + i + "]";
						if (arrayElement instanceof JSONObject) {
							sb.append(generateUrlParams((JSONObject) arrayElement, encodedKey));
						} else {
							sb.append(encodedKey).append("=").append(arrayElement.toString()).append("&");
						}
					}
				}
			} else if (value instanceof JSONObject) {
				sb.append(generateUrlParams((JSONObject) value, key));
			} else if (value != null && !(value instanceof Collection) && !value.toString().isEmpty()) {
//				try {
//					String encodedKey = encodeValue(key);
//					String encodedValue = encodeValue(value.toString());
//					sb.append(encodedKey).append("=").append(encodedValue).append("&");
//				} catch (UnsupportedEncodingException e) {
//					e.printStackTrace();
//				}
				sb.append(key).append("=").append(value.toString()).append("&");
			}
		}

		if (sb.length() > 0) {
			sb.deleteCharAt(sb.length() - 1);
		}
		String urlParams = sb.toString();
//		urlParams = urlParams.replaceAll("%255B", "%5B").replaceAll("%255D", "%5D");
		return urlParams;
	}

	private static String generateUrlParams(JSONObject jsonObject, String parentKey) {
		StringBuilder sb = new StringBuilder();

		for (Object obj : jsonObject.entrySet()) {
			Map.Entry<String, Object> entry = (Map.Entry<String, Object>) obj;
			String key = entry.getKey();
			Object value = entry.getValue();

//			try {
//				String encodedKey = encodeValue(parentKey + "[" + encodeValue(key) + "]");
//				if (value instanceof JSONObject) {
//					sb.append(generateUrlParams((JSONObject) value, encodedKey));
//				} else {
//					String encodedValue = encodeValue(value.toString());
//					sb.append(encodedKey).append("=").append(encodedValue).append("&");
//				}
//			} catch (UnsupportedEncodingException e) {
//				e.printStackTrace();
//			}
			if (value instanceof JSONObject) {
				sb.append(generateUrlParams((JSONObject) value, key));
			} else {
				sb.append(key).append("=").append(value.toString()).append("&");
			}
		}

		return sb.toString();
	}

	private static String encodeValue(String value) throws UnsupportedEncodingException {
		String encodedValue = URLEncoder.encode(value, "UTF-8");
		// 将 "+" 替换为 "%20"，空格替换为加号（+）
		//return encodedValue.replace("+", "%20").replace("%20", "+");
		return encodedValue;
	}


	public static void main(String[] args) {
		// 示例 JSON 数据
		String jsonStr = "{\"tags\":[],\"shop_id\":\"3592793253\",\"sub_balance\":\"7500\",\"diy_gift_coupon_pay\":[],\"remark\":null,\"sub_credit\":\"0\",\"cno\":\"1177984\",\"cashier_id\":\"-1\",\"count_num\":null,\"gift_coupons_ids\":[\"1585459235115551493\"],\"activity_ids\":[],\"biz_id\":\"7177798788424091240\",\"deno_coupon_ids\":[\"1585300479841897178\"],\"payment_mode\":\"1\",\"payment_amount\":\"0\",\"products\":[{\"tags\":[],\"num\":\"1\",\"price\":\"4800\",\"no\":\"9003\",\"name\":\"荷花乌鱼片\",\"is_activity\":\"2\"}],\"consume_amount\":\"17300\",\"credit_amount\":null}";
		//credit_amount remark count_num
		// 将 JSON 字符串转换为 net.sf.json.JSONObject
		JSONObject jsonObject = JSONObject.fromObject(jsonStr);
		jsonObject.put("remark", null);
		jsonObject.put("count_num", null);
		jsonObject.put("credit_amount", null);
		// 按照 key 的 ASCII 码字典序升序排序 JSON 对象
		System.out.println(jsonObject.toString());
		JSONObject sortedJsonObject = sortJsonObject(jsonObject);
		System.out.println(sortedJsonObject);
		// 将排序后的 JSON 对象转换为 URL 键值对字符串
		String urlParams = generateUrlParams(sortedJsonObject);

		// 输出 URL 键值对字符串
		System.out.println(urlParams);
	}
}
