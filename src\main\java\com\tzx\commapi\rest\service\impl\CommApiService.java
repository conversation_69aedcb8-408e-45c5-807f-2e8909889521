package com.tzx.commapi.rest.service.impl;

import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;
import com.tzx.commapi.common.CommApiException;
import com.tzx.commapi.rest.mapper.CommApiMapper;
import com.tzx.commapi.rest.service.ICommApiService;
import com.tzx.commapi.rest.vo.*;
import com.tzx.ecoserver.common.EcoException;
import com.tzx.ecoserver.common.EcoONQueueManage;
import com.tzx.ecoserver.common.EcoRepData;
import com.tzx.ecoserver.common.PlayNewOrderThread;
import com.tzx.ecoserver.rest.service.IEcoOrderService;
import com.tzx.mobilepos.rest.model.TqJtztk;
import com.tzx.mobilepos.rest.model.TqZdk;
import com.tzx.mobilepos.rest.model.TsBck;
import com.tzx.publics.common.BillNoData;
import com.tzx.publics.listener.InitDataListener;
import com.tzx.publics.service.IInitDataService;
import com.tzx.publics.util.*;
import com.tzx.publics.vo.DeviceVo;
import com.tzx.publics.vo.OrganVo;

import com.tzx.receiver.common.utils.DownloadUtils;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.Map.Entry;

/**
 * Created by Zhouxh on 2020-01-14.
 */
@Service
public class CommApiService implements ICommApiService{
    private final static Logger LOGGER = LoggerFactory.getLogger(CommApiService.class);
    private String types[] = {"0", "1", "2", "3"};
    @Autowired
    private CommApiMapper commApiMapper;
    //播放新订单接口
    @Autowired
    private PlayNewOrderThread playNewOrderThread;
    @Autowired
	private IInitDataService initDataService;
    @Autowired
    private GlobalLockGetDataUtil globalLockGetDataUtil;
    @Autowired
    private IEcoOrderService ecoOrderService;
    @Override
    public CommApiData SetBefore(CommApiData data, HashMap<String, String> inparam, JSONObject jsondata) {
        data.setCode(0);
        return data;
    }

    @Override
    @Transactional
    public CommApiData SetProcess(CommApiData data, HashMap<String, String> inparam, JSONObject jsondata) {
        if(jsondata.optString("item_id").equals("")){
            throw new CommApiException("菜品编号不能为空");
        }
        ReqParam reqParam = new ReqParam();
        reqParam.setItem_id(jsondata.optString("item_id"));
        reqParam.setItem_name(jsondata.optString("item_name"));
        reqParam.setCmid(jsondata.optInt("cmid"));
        String type = jsondata.optString("type");
        if(!Arrays.asList(types).contains(type)){
            throw new CommApiException("操作类型"+type+"不存在");
        }
        reqParam.setType(type);
        if(type.equals("2")){
            reqParam.setCount(new BigDecimal(jsondata.optDouble("count")));
            LOGGER.info("沽清数量 = "+ jsondata.optInt("count",-1));
            if(jsondata.optInt("count",-1)<0){
                throw new CommApiException("沽清数量设置错误");
            }
        }
        else
            reqParam.setCount(new BigDecimal(0));
        GqkVo gqkVo = commApiMapper.getGQDish(reqParam);
        int rt = 0;
        if(null==gqkVo){
            //插入沽清
            LOGGER.info("开始设置沽清 type =" + type);
            if(type.equals("3")) //取消沽清直接返回1
                rt = 1;
            else
                rt =  commApiMapper.insertSetGq(reqParam);
        }
        else{
            LOGGER.info("开始设置沽清type = " + type + ",明细："+gqkVo.getCmid()+gqkVo.getCmbh() + gqkVo.getCmmc1() + gqkVo.getYl1());
             //更新沽清
            if(type.equals("3")) //取消沽清直接删除
                rt =  commApiMapper.deleteSetGq(reqParam);
            else
                rt =  commApiMapper.updateSetGq(reqParam);
        }
        LOGGER.info("设置沽清完毕 返回=" + rt);
        data.setCode(0);
        data.setMsg("设置沽清成功");

        return data;
    }

    @Override
    public CommApiData SetAfter(CommApiData data, HashMap<String, String> inparam, JSONObject jsondata) {
        return null;
    }

    @Override
    public CommApiData AddBefore(CommApiData data, HashMap<String, String> inparam, JSONObject jsondata) {
        data.setCode(0);
        return data;
    }

    @Override
    @Transactional
    public CommApiData AddProcess(CommApiData data, HashMap<String, String> inparam, JSONObject jsondata) {
        List<String> reqParams = new ArrayList<>();
        JSONArray jsonArray = jsondata.optJSONArray("dishinfo");
        HashMap<String,BigDecimal> dishCounts = new HashMap<String,BigDecimal>();
        if(jsonArray==null || jsonArray.size()==0){
            throw new CommApiException("未找到沽清菜品列表");
        }
        for (int i = 0; i < jsonArray.size() ; i++) {
            String item_id = jsonArray.getJSONObject(i).optString("item_id");
            BigDecimal count = new BigDecimal(jsonArray.getJSONObject(i).optDouble("count",-1));
            if(count.doubleValue()<=0){
                throw new CommApiException("菜品沽清数量必须大于0");
            }
            if(null==item_id || item_id.equals("")) {
                throw new CommApiException("未找到沽清菜品编号");
            }
            reqParams.add(item_id);
            if(null!=dishCounts.get(item_id)){
                count = count.add(dishCounts.get(item_id));
            }
            dishCounts.put(item_id,count);
        }
        if(reqParams.size() != jsonArray.size()){
            throw new CommApiException("菜品数组设置错误");
        }

        List<GqkVo>  gqkVos = commApiMapper.getGQDishs(reqParams);
        if(null == gqkVos || gqkVos.size() == 0){
            data.setMsg("没有沽清菜品，新增沽清成功");
            data.setCode(0);
            return  data;
        }
        List<GqkVo>  setGqkVos = new ArrayList<>();
        for(GqkVo gqkVo2:gqkVos) {
            if(gqkVo2.getYl1().equals("2")) {
                setGqkVos.add(gqkVo2);
            }
        }
        //更新数量大于0 执行更行沽清
        if(setGqkVos.size() > 0){
            for(GqkVo gqkVo3:setGqkVos) {
                ReqParam reqParam = new ReqParam();
                reqParam.setItem_id(gqkVo3.getCmbh());
                reqParam.setItem_name(gqkVo3.getCmmc1());
                reqParam.setCmid(gqkVo3.getCmid());
                reqParam.setCount( new BigDecimal(dishCounts.get(gqkVo3.getCmbh()).doubleValue()));

                int rt = commApiMapper.addGq(reqParam);
                if (rt == 0) {
                    throw new CommApiException("新增沽清数量失败");
                }
            }
        }

        //返回沽清数据列表
        List<ReqParam> reqParamList = new ArrayList<ReqParam>();
        if(setGqkVos.size() > 0){
            for(GqkVo gqkVo3:setGqkVos) {
                ReqParam reqParam = new ReqParam();
                reqParam.setItem_id(gqkVo3.getCmbh());
                reqParam.setItem_name(gqkVo3.getCmmc1());
                reqParam.setCount( new BigDecimal(ArithUtil.add(gqkVo3.getGqsl().doubleValue(),dishCounts.get(gqkVo3.getCmbh()).doubleValue())));
                reqParamList.add(reqParam);
            }
        }
        data.setDishinfo(reqParamList);

        data.setMsg("新增沽清成功");
        data.setCode(0);
        return data;
    }

    @Override
    public CommApiData AddAfter(CommApiData data, HashMap<String, String> inparam, JSONObject jsondata) {
        return null;
    }

    @Override
    public CommApiData DecBefore(CommApiData data, HashMap<String, String> inparam, JSONObject jsondata) {
        data.setCode(0);
        return data;
    }

    @Override
    @Transactional
    public CommApiData DecProcess(CommApiData data, HashMap<String, String> inparam, JSONObject jsondata) {
        List<String> reqParams = new ArrayList<>();
        JSONArray jsonArray = jsondata.optJSONArray("dishinfo");
        HashMap<String,BigDecimal> dishCounts = new HashMap<String,BigDecimal>();
        if(jsonArray==null || jsonArray.size()==0){
            throw new CommApiException("未找到沽清菜品列表");
        }
        for (int i = 0; i < jsonArray.size() ; i++) {
            String item_id = jsonArray.getJSONObject(i).optString("item_id");
            BigDecimal count = new BigDecimal(jsonArray.getJSONObject(i).optDouble("count",-1));
            if(count.doubleValue()<=0){
                throw new CommApiException("菜品沽清数量必须大于0");
            }
            if(null==item_id || item_id.equals("")) {
                throw new CommApiException("未找到沽清菜品编号");
            }
            reqParams.add(item_id);
            if(null!=dishCounts.get(item_id)){
                count = count.add(dishCounts.get(item_id));
            }
            dishCounts.put(item_id,count);
        }
        if(reqParams.size() != jsonArray.size()){
            throw new CommApiException("菜品数组设置错误");
        }

        List<GqkVo>  gqkVos = commApiMapper.getGQDishs(reqParams);
        if(null == gqkVos || gqkVos.size() == 0){
            data.setMsg("没有沽清菜品，扣减沽清成功");
            data.setCode(0);
            return  data;
        }
        String errs = "";
        List<GqkVo>  setGqkVos = new ArrayList<>();
        for(GqkVo gqkVo2:gqkVos) {
            if(gqkVo2.getYl1().equals("2")){
                if(gqkVo2.getGqsl().doubleValue() < dishCounts.get(gqkVo2.getCmbh()).doubleValue()){
                    errs = errs + gqkVo2.getCmmc1() + " 点菜数量:" + dishCounts.get(gqkVo2.getCmbh()).doubleValue() +
                            "大于剩余数量："+gqkVo2.getGqsl().doubleValue()+ ";";

                }
                setGqkVos.add(gqkVo2);
            }
            else{
                errs = errs + gqkVo2.getCmmc1() + " 已沽清;";
            }
        }
        if(!errs.equals("")){
            data.setCode(1);
            data.setMsg(errs);
            return  data;
        }
        //更新数量大于0 执行更行沽清
        if(setGqkVos.size() > 0){
            for(GqkVo gqkVo3:setGqkVos) {
                ReqParam reqParam = new ReqParam();
                reqParam.setItem_id(gqkVo3.getCmbh());
                reqParam.setItem_name(gqkVo3.getCmmc1());
                reqParam.setCmid(gqkVo3.getCmid());
                reqParam.setCount(dishCounts.get(gqkVo3.getCmbh()));

                int rt = commApiMapper.decGq(reqParam);
                if (rt == 0) {
                    throw new CommApiException("扣减沽清数量失败");
                }
            }
        }
        //返回沽清数据列表
        List<ReqParam> reqParamList = new ArrayList<ReqParam>();
        if(setGqkVos.size() > 0){
            for(GqkVo gqkVo3:setGqkVos) {
                ReqParam reqParam = new ReqParam();
                reqParam.setItem_id(gqkVo3.getCmbh());
                reqParam.setItem_name(gqkVo3.getCmmc1());
                reqParam.setCount( new BigDecimal(ArithUtil.sub(gqkVo3.getGqsl().doubleValue(),dishCounts.get(gqkVo3.getCmbh()).doubleValue())));
                reqParamList.add(reqParam);
            }
        }
        data.setDishinfo(reqParamList);

        data.setMsg("扣减沽清成功");
        data.setCode(0);
        return data;
    }

    @Override
    public CommApiData DecAfter(CommApiData data, HashMap<String, String> inparam, JSONObject jsondata) {
        return null;
    }

    public ArrayList<String> GetSetList(JSONObject jsondata){
        ArrayList<String> arrayList = new ArrayList<String>();
        String item_id = jsondata.optString("item_id");
        if(null==item_id || item_id.equals("")) {
            throw new CommApiException("未找到沽清菜品编号");
        }
        if(!arrayList.contains(item_id))
            arrayList.add(item_id);
        return  arrayList;
    }

    public ArrayList<String> GetAddList(JSONObject jsondata){
        ArrayList<String> arrayList = new ArrayList<String>();
        JSONArray jsonArray = jsondata.optJSONArray("dishinfo");
        if(jsonArray==null || jsonArray.size()==0){
            throw new CommApiException("未找到沽清菜品列表");
        }
        for (int i = 0; i < jsonArray.size() ; i++) {
            String item_id = jsonArray.getJSONObject(i).optString("item_id");
            int count = jsonArray.getJSONObject(i).optInt("count",-1);
            if(count<0){
                throw new CommApiException("菜品沽清数量设置不能小于0");
            }
            if(null==item_id || item_id.equals("")) {
                throw new CommApiException("未找到沽清菜品编号");
            }
            if(!arrayList.contains(item_id))
                arrayList.add(item_id);
        }
        return  arrayList;
    }

    public ArrayList<String> GetDecList(JSONObject jsondata){
        ArrayList<String> arrayList = new ArrayList<String>();
        JSONArray jsonArray = jsondata.optJSONArray("dishinfo");
        if(jsonArray==null || jsonArray.size()==0){
            throw new CommApiException("未找到沽清菜品列表");
        }
        for (int i = 0; i < jsonArray.size() ; i++) {
            String item_id = jsonArray.getJSONObject(i).optString("item_id");
            int count = jsonArray.getJSONObject(i).optInt("count",-1);
            if(count<0){
                throw new CommApiException("菜品沽清数量设置不能小于0");
            }
            if(null==item_id || item_id.equals("")) {
                throw new CommApiException("未找到沽清菜品编号");
            }
            if(!arrayList.contains(item_id))
                arrayList.add(item_id);
        }
        return  arrayList;
    }

    @Override
    public CommApiData SetEcoSound(CommApiData data, HashMap<String, String> inparam, JSONObject jsondata) {
        //设置
        String ecosound =  jsondata.optString("ecosound");
        String path = System.getProperty("user.dir");
        File file = new File(path);
        LOGGER.info("path:" + path);
        String fileName = null;
        path =  file.getParent() + File.separator;
        fileName = path + "ecosound.ini";
        File filepath = new File(fileName);
        if (!filepath.exists()) {
            try {
                filepath.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        PropertiesUtil.writeProperties(fileName,"ecosound",ecosound);
        InitDataListener.cacheMap.put("ecosound",ecosound);
        HashMap<String, String> param = new HashMap<String, String>();
        param.put("soundVoice",ecosound);
        param.put("test","1");
        EcoONQueueManage.runEcoQueue(param,this.playNewOrderThread);
        data.setCode(0);
        data.setMsg("设置成功");
        return data;
    }

    @Override
    public CommApiData GetEcoSound(CommApiData data, HashMap<String, String> inparam, JSONObject jsondata) {
        String ecosound = InitDataListener.cacheMap.get("ecosound");
        if(null==ecosound||ecosound.equals("")){
            ecosound = "50";
        }
        data.setCode(0);
        data.setMsg(ecosound);
        return  data;
    }
    
    @Override
	public void qmMemberRequest(CommApiData data, JSONObject jsondata) {
		try {
			String url = InitDataListener.thirdMap.get("QIMAI_URL");
			String openId = InitDataListener.thirdMap.get("QIMAI_OPENID");
			String grantCode = InitDataListener.thirdMap.get("QIMAI_GRANTCODE");
			String openKey = InitDataListener.thirdMap.get("QIMAI_OPENKEY");
			String timestamp = System.currentTimeMillis() + "";
			String nonce = (int) ((Math.random() * 9 + 1) * 10000) + "";
			String action = jsondata.optString("Action", "");
			JSONObject params = jsondata.optJSONObject("Params");
			
			int length = timestamp.length();
			if (timestamp.length() > 3) {
				timestamp = timestamp.substring(0, length - 3);
			}
			
			String srcStr = "GrantCode=" + grantCode + "&Nonce=" + nonce + "&OpenId=" + openId + "&Timestamp=" + timestamp;
			LOGGER.info("srcStr：" + srcStr);
			LOGGER.info("openKey：" + openKey);
			String qmSign = HamcUtil.getHmacSign(srcStr, "UTF-8", openKey, "HmacSHA1");
			LOGGER.info("qmSign：" + qmSign);
			String token = "";
			token = URLEncoder.encode(qmSign, "UTF-8");

//			String rest = null;
//			LOGGER.info("token：" + token);
//			JSONObject json = new JSONObject();
//			json.put("Action", action);
//			json.put("GrantCode", grantCode);
//			json.put("Nonce", nonce);
//			json.put("OpenId", openId);
//			json.put("Params", params);
//			json.put("Timestamp", timestamp);
//			json.put("Token", token);
//			LOGGER.info("请求企迈url：" + url);
//			LOGGER.info("请求企迈参数：" + json.toString());
//			rest = SendRequest.qmPost(url, json.toString());
//			LOGGER.info("企迈返回参数：" + rest);
//			if ("-1".equals(rest)) {
//				data.setCode(1);
//				data.setMsg("接口异常！");
//			} else if ("-2".equals(rest)) {
//				data.setCode(1);
//				data.setMsg("请求接口错误！");
//			} else if ("timedout".equals(rest)) {
//				data.setCode(1);
//				data.setMsg("网络请求超时，请稍后再试！");
//			} else {
//				data.setCode(0);
//				data.setData(rest);
//			}

            LOGGER.info("token：" + token);
            JSONObject json = new JSONObject();
            json.put("Action", action);
            json.put("GrantCode", grantCode);
            json.put("Nonce", nonce);
            json.put("OpenId", openId);
            json.put("Params", params);
            json.put("Timestamp", timestamp);
            json.put("Token", token);
            LOGGER.info("请求企迈url：" + url);
            LOGGER.info("请求企迈参数：" + json.toString());
            Map<String, String> responseMap = SendRequest.postSSLPost(url,json.toString());
            LOGGER.info("企迈返回参数：" + responseMap.get("data"));
            String rest = responseMap.get("success");
            if ("0".equals(rest)) {
                data.setCode(0);
                data.setData(responseMap.get("data"));
            } else {
                data.setCode(1);
                data.setMsg(responseMap.get("msg"));
            }

		} catch (Exception e) {
			data.setCode(1);
			data.setMsg("系统异常！");
			LOGGER.error(e.getMessage(), e);
		}
	}
    
    @Override
	public void merchantRequest(CommApiData data, JSONObject jsondata) {
    	try {
    		// 先校验，如果token不存在，或者过期则重新申请token
        	String jtbh = jsondata.optString("jtbh", "");
        	Map<String, DeviceVo> deviceMap = InitDataListener.deviceMap;
        	DeviceVo deviceVo = deviceMap.get(jtbh);
        	int c = checkToken(jsondata, deviceVo, jtbh);
        	
        	if(c == 0){
        		// 请求冥晨
        		deviceMap = InitDataListener.deviceMap;
        		deviceVo = deviceMap.get(jtbh);
            	Map<String, String> map = merchantSend(jsondata, deviceVo);
            	JSONObject dataT = JSONObject.fromObject(map.get("data"));
        		if ("0".equals(map.get("code"))) {
        			data.setCode(0);
        			data.setData(map.get("data"));
        			data.setMsg("成功");
        		} else {
        			data.setCode(1);
        			data.setMsg(map.get("msg"));
        		}
        		
        		if ("204006".equals(dataT.optString("errcode"))) {
					commApiMapper.updateJtztkToToken(jtbh, null, null);
					initDataService.refreshInternalStorage();
				}
        	} else {
        		data.setCode(1);
    			data.setMsg("Token获取失败");
        	}
    	} catch (Exception e) {
			data.setCode(1);
			data.setMsg("系统异常！");
			LOGGER.error(e.getMessage(), e);
		}
	}


    public int checkToken(JSONObject jsondata, DeviceVo deviceVo, String jtbh) {
		int c = 1;
		String token = deviceVo.getToken();
		String updateTime = deviceVo.getUpdatetime();
		boolean ifUpdate = true;
		
		// token不存在，直接更新； 如果存在，判断更新时间是否超过20小时，超过则更新
		if (null != token) {
			if (null != updateTime) {
				long hour = DateUtil.getDatePoorHour(new Date(), DateUtil.parseDateAll(updateTime));
				if (hour <= 20) {
					ifUpdate = false;
					c = 0;
				}
			}
		}
		if (ifUpdate) {
			OrganVo organ = InitDataListener.organVo;
			String jgbh = organ.getJgbh();
			jgbh = jgbh.substring(2);
			String merNo = InitDataListener.thirdMap.get("MINGCHEN_MERNO");
			String shopNo = merNo + jgbh;
			
			JSONObject job = new JSONObject();
			job.put("jtbh", jtbh);
			job.put("url", "api/merchant/device/token");
			job.put("orderNo", shopNo + jtbh + System.currentTimeMillis());
			job.put("transCode", "C042");
			job.put("deviceDate", DateUtil.getNowDateYYDDMMHHMMSS());
			
			JSONObject data = new JSONObject();
			data.put("mngKey", InitDataListener.thirdMap.get("MINGCHEN_MNGKEY"));
			
			job.put("data", data);

			// 请求冥晨
	    	Map<String, String> map = merchantSend(job, deviceVo);
			if ("0".equals(map.get("code"))) {
				JSONObject tokenObj = JSONObject.fromObject(map.get("data"));
				if ("0".equals(tokenObj.optString("errcode"))) {
					token = tokenObj.optString("token", "");
					updateTime = DateUtil.getNowDateYYDDMMHHMMSS();
					commApiMapper.updateJtztkToToken(jtbh, token, updateTime);
					c = 0;
					initDataService.refreshInternalStorage();
				}
				if ("204006".equals(tokenObj.optString("errcode"))) {
					commApiMapper.updateJtztkToToken(jtbh, null, null);
					initDataService.refreshInternalStorage();
				}
			}
		}
		return c;
	}
    
	public Map<String, String> merchantSend(JSONObject jsondata, DeviceVo deviceVo) {
		String url = InitDataListener.thirdMap.get("MINGCHEN_URL");
		OrganVo organ = InitDataListener.organVo;
		String jgbh = organ.getJgbh();
		jgbh = jgbh.substring(2);
		
		url = url + "/" + jsondata.optString("url", "");
		String token = deviceVo.getToken();
		String merNo = InitDataListener.thirdMap.get("MINGCHEN_MERNO");
		String shopNo = merNo + jgbh;
		String deviceNo = shopNo + deviceVo.getJtbh();
		String orderNo = jsondata.optString("orderNo", "");
		String channel = "0";
		String deviceDate = jsondata.optString("deviceDate", "");
		String version = "1.0";
		String transCode = jsondata.optString("transCode", "");
		
		Map<String, String> reqMap = new HashMap<String, String>();
		
		if(!"C042".equals(transCode)){
			reqMap.put("token", token);
		}
		reqMap.put("merNo", merNo);
		reqMap.put("shopNo", shopNo);
		reqMap.put("deviceNo", deviceNo);
		reqMap.put("orderNo", orderNo);
		reqMap.put("channel", channel);
		reqMap.put("deviceDate", deviceDate);
		reqMap.put("version", version);
		reqMap.put("transCode", transCode);
		
		JSONObject datas = jsondata.optJSONObject("data");
		
		Iterator it = datas.keys();
		while (it.hasNext()) {
			String key = (String) it.next();
			String value = datas.getString(key);
			reqMap.put(key, value);
		}
		LOGGER.info("请求" + transCode + "基础数据:" + JSONObject.fromObject(reqMap).toString());
		Map<String, String> map = HttpClientUtil.merchantSendGet(url, reqMap);

		return map;
	}


    @Override
    public void qiMaiOtherRequest(CommApiData data, JSONObject jsondata) {
        try {
            String url = InitDataListener.thirdMap.get("QIMAI_QIMAI_URL");
            String callbackShopName = InitDataListener.thirdMap.get("QIMAI_CALLBACK_SHOP_NAME");
            JSONObject params = jsondata.optJSONObject("Params");
            String rest = null;
            url = url + params.getString("qimai_data_url");
            params.remove("qimai_data_url");
            params.put("callbackShopName", callbackShopName);//

            LOGGER.info("qiMaiOtherRequest请求企迈url：" + url);
            LOGGER.info("请求企迈参数：" + params.toString());
//			rest = SendRequest.sendPost(url, json.toString());
            rest = SendRequest.qmPost(url, params.toString());
            LOGGER.info("企迈返回参数：" + rest);
            if ("-1".equals(rest)) {
                data.setCode(1);
                data.setMsg("接口异常！");
            } else if ("-2".equals(rest)) {
                data.setCode(1);
                data.setMsg("请求接口错误！");
            } else if ("timedout".equals(rest)) {
                data.setCode(1);
                data.setMsg("网络请求超时，请稍后再试！");
            } else {
                data.setCode(0);
                data.setData(rest);
            }
        } catch (Exception e) {
            data.setCode(1);
            data.setMsg("系统异常！");
            LOGGER.error(e.getMessage(), e);
        }
    }
    
    @Override
	public void merchantRequestPost(CommApiData data, JSONObject jsondata) {
    	try {
    		// 先校验，如果token不存在，或者过期则重新申请token
        	String jtbh = jsondata.optString("jtbh", "");
        	Map<String, DeviceVo> deviceMap = InitDataListener.deviceMap;
        	DeviceVo deviceVo = deviceMap.get(jtbh);
        	int c = checkToken(jsondata, deviceVo, jtbh);
        	
        	if(c == 0){
        		// 请求冥晨
        		deviceMap = InitDataListener.deviceMap;
        		deviceVo = deviceMap.get(jtbh);
            	merchantSendPost(data, jsondata, deviceVo);
            	JSONObject dataT = JSONObject.fromObject(data.getData());
        		if ("204006".equals(dataT.optString("errcode"))) {
    				commApiMapper.updateJtztkToToken(jtbh, null, null);
    				initDataService.refreshInternalStorage();
    			}
        	} else {
        		data.setCode(1);
    			data.setMsg("Token获取失败");
        	}
    	} catch (Exception e) {
			data.setCode(1);
			data.setMsg("系统异常！");
			LOGGER.error(e.getMessage(), e);
		}
	}

    @Override
    public void sendSmsPost(CommApiData data, JSONObject jsondata) {
        SendSms.sendSms(data, jsondata);
    }

    public void merchantSendPost(CommApiData data, JSONObject jsondata, DeviceVo deviceVo) {
    	try {
	    	String url = InitDataListener.thirdMap.get("MINGCHEN_URL");
			OrganVo organ = InitDataListener.organVo;
			String jgbh = organ.getJgbh();
			jgbh = jgbh.substring(2);
			
			url = url + "/" + jsondata.optString("url", "");
			String token = deviceVo.getToken();
			String merNo = InitDataListener.thirdMap.get("MINGCHEN_MERNO");
			String shopNo = merNo + jgbh;
			String deviceNo = shopNo + deviceVo.getJtbh();
			String orderNo = jsondata.optString("orderNo", "");
			String channel = "0";
			String deviceDate = jsondata.optString("deviceDate", "");
			String version = "1.0";
			String transCode = jsondata.optString("transCode", "");
			
			Map<String, String> reqMap = new HashMap<String, String>();
			
			if(!"C042".equals(transCode)){
				reqMap.put("token", token);
			}
			reqMap.put("merNo", merNo);
			reqMap.put("shopNo", shopNo);
			reqMap.put("deviceNo", deviceNo);
			reqMap.put("orderNo", orderNo);
			reqMap.put("channel", channel);
			reqMap.put("deviceDate", deviceDate);
			reqMap.put("version", version);
			reqMap.put("transCode", transCode);
			
			JSONObject datas = jsondata.optJSONObject("data");
			
			Iterator it = datas.keys();
			while (it.hasNext()) {
				String key = (String) it.next();
				String value = datas.getString(key);
				reqMap.put(key, value);
			}
			LOGGER.info("请求" + transCode + "基础数据:" + JSONObject.fromObject(reqMap).toString());
			StringBuilder param = new StringBuilder();
			Set<Entry<String, String>> entrySet = reqMap.entrySet();
			for (Entry<String, String> e : entrySet) {
				String key = e.getKey();
				String value = e.getValue();
				param.append("&" + key + "=" + URLEncoder.encode(value, "UTF-8"));
			}
			String paramStr = param.toString();
			paramStr = paramStr.substring(1);
			url = url + "?" + paramStr;
			LOGGER.info("请求冥晨参数：" + url);
			
			String rest = SendRequest.qmPost(url, new JSONObject().toString());
			LOGGER.info("冥晨返回参数：" + rest);
			if ("-1".equals(rest)) {
				data.setCode(1);
				data.setMsg("接口异常！");
			} else if ("-2".equals(rest)) {
				data.setCode(1);
				data.setMsg("请求接口错误！");
			} else if ("timedout".equals(rest)) {
				data.setCode(1);
				data.setMsg("网络请求超时，请稍后再试！");
			} else {
				data.setCode(0);
				data.setMsg("成功");
				data.setData(rest);
			}
		} catch (Exception e) {
			data.setCode(1);
			data.setMsg("系统异常！");
			LOGGER.error(e.getMessage(), e);
		}
	}
    
    @Override
	public void ecoRequestPost(CommApiData data, JSONObject jsondata) {
    	try {
            String url = InitDataListener.thirdMap.get("ECO_URL");
            JSONObject params = jsondata.optJSONObject("Params");
            String rest = null;
            url = url + "/" + params.getString("eco_data_url");
            params.remove("eco_data_url");

            LOGGER.info("ecoRequestPost请求ECO地址：" + url);
            LOGGER.info("请求ECO参数：" + params.toString());
            rest = SendRequest.qmPost(url, params.toString());
            LOGGER.info("ECO返回参数：" + rest);
            if ("-1".equals(rest)) {
                data.setCode(1);
                data.setMsg("接口异常！");
            } else if ("-2".equals(rest)) {
                data.setCode(1);
                data.setMsg("请求接口错误！");
            } else if ("timedout".equals(rest)) {
                data.setCode(1);
                data.setMsg("网络请求超时，请稍后再试！");
            } else {
                data.setCode(0);
                data.setData(rest);
            }
        } catch (Exception e) {
            data.setCode(1);
            data.setMsg("系统异常！");
            LOGGER.error(e.getMessage(), e);
        }
	}

    @Override
    public void getWyList(CommApiData data, JSONObject jsondata) {
        try {
            String billnum = jsondata.optString("billnum", "");
            String reportdate = jsondata.optString("reportdate", "");
            String orderno = jsondata.optString("orderno", "");
            int status = jsondata.optInt("status", -1);
            List<TqThirdTempOrderNew> tttoList = commApiMapper.getWyList(billnum, reportdate, orderno, status);
            data.setCode(0);
            data.setMsg("查询成功");
            data.setData(tttoList);
        } catch (Exception e) {
            data.setCode(1);
            data.setMsg("系统异常！");
            LOGGER.error(e.getMessage(), e);
        }
    }

    @Transactional
    public void repetitionSendWy(CommApiData data, JSONObject jsondata) {
        JSONObject obj = jsondata.optJSONObject("data");
        String sendType = obj.optString("send_type", "" );
        String prws = InitDataListener.ggcsMap.get("POS_REPETITION_WUYE_STATUS");

        if (null == prws || prws.trim().isEmpty()) {
            prws = "0";
        }
        if ("1".equals(sendType)) {
            JSONArray lists = obj.optJSONArray("list");
            for (int i = 0; i < lists.size(); i++) {
                JSONObject list = lists.optJSONObject(i);
                int id = list.optInt("id", 0);
                String billnum = list.optString("billnum", "");
                String orderno = list.optString("orderno", "");
                String reportdate = list.optString("reportdate", "");
                int status = list.optInt("status", -1);
                String type = list.optString("type", "1");

                if ("0".equals(prws) && status == 1) {
                    data.setCode(1);
                    data.setData("");
                    LOGGER.error("不可补发已成功物业数据！");
                    throw new CommApiException("不可补发已成功物业数据！");
                }

                if (status == -1) {
                    data.setCode(1);
                    data.setData("");
                    LOGGER.error("状态不可为空！");
                    throw new CommApiException("状态不可为空！");
                }

//            if (i == 3) {
//                data.setCode(1);
//                data.setData("");
//                LOGGER.error("对方不想和你说话，并向你抛了一个异常！");
//                throw new CommApiException("我有数据，我不查，我就报错，哎~我就是玩儿！");
//            }
                if ("2".equals(type)) {
                    // 历史数据，把数据从历史表拿到临时表，再删除历史数据
                    commApiMapper.repetitionTTTO(id, billnum, reportdate, orderno);
                    commApiMapper.delTTTO(id, billnum, reportdate, orderno);
                } else {
                    // 实时数据，直接改状态
                    commApiMapper.updateTqThirdTempOrderStatus(id, billnum);
                }
            }
        } else if ("2".equals(sendType)) {
            String billnum = obj.optString("billnum", "");
            String orderno = obj.optString("orderno", "");
            String reportdate = obj.optString("reportdate", "");
            int status = obj.optInt("status", -1);

            if ("0".equals(prws) && (status == 1 || status == -1)) {
                data.setCode(1);
                data.setData("");
                LOGGER.error("不可补发已成功物业数据！");
                throw new CommApiException("不可补发已成功物业数据！");
            }

            if ("".equals(reportdate)) {
                data.setCode(1);
                data.setData("");
                LOGGER.error("报表日期不能为空！");
                throw new CommApiException("报表日期不能为空！");
            }
            commApiMapper.updateTqThirdTempOrderStatusByBbrq(status, billnum, reportdate, orderno);
            commApiMapper.repetitionTTTOByBbrq(status, billnum, reportdate, orderno);
            commApiMapper.delTTTOByBbrq(status, billnum, reportdate, orderno);
        } else {
            data.setCode(1);
            data.setData("");
            LOGGER.error("发送类型type为空，请确认后重新请求！");
            throw new CommApiException("发送类型type为空，请确认后重新请求！");
        }

        data.setCode(0);
        data.setMsg("物业数据重发成功！");
        data.setData("");
    }

    @Override
    public void businessData(CommApiData data, JSONObject jsondata) {
        try {
            String jgxh = jsondata.optString("jgxh", "");
            String bbrq = jsondata.optString("bbrq", "");
            String url = InitDataListener.ggcsMap.get("URL_TZXINTERFACE") + "/tzxShopPullData/businessData";
            JSONObject bodyJson=new JSONObject();
            bodyJson.put("jgxh", jgxh);
            bodyJson.put("bbrq", bbrq);
            String body = bodyJson.toString();
            // curprogres : 0-初始状态；1-接收请求；2-请求总部成功；3-下载成功；4数据装载成功；
            TqBbscjdk bbscjdk = commApiMapper.getBbscjdkByBbrq(DateUtil.parseDate(bbrq));
            if (null == bbscjdk) {
                LOGGER.info("未查询到触发状态记录");
                data.setCode(1);
                data.setMsg("未查询到触发状态记录");
                return;
            }
            bbscjdk.setCurprogres(1);
            commApiMapper.setBbscjdkToCurprogres(bbscjdk);
            HttpResponse<String> response=  Unirest.post(url).header("Content-Type", "application/json").body(body).asString();
            LOGGER.info("调用拉取总部订单:{} ,\n请求参数：{},\n返回内容：{}",url, body, response.getBody());
            if (200 == response.getStatus()) {
                org.json.JSONObject jsonBody = new org.json.JSONObject(response.getBody());
                if(200 == jsonBody.getInt("code")) {
                    String downloadurl = jsonBody.optString("downloadurl", "");
                    //String downloadurl = "https://e7e6.oss-cn-beijing.aliyuncs.com/bohmq/2023-03-10/202303101753-55767ce8339e43f2aaa3401b84c5da44.zip";
                    bbscjdk.setCurprogres(2);
                    commApiMapper.setBbscjdkToCurprogres(bbscjdk);
                    boolean mark = true;
                    //失败重新下載  一共三次
                    for (int i = 0; i < 3; i++) {
                        String jsonStr = DownloadUtils.downLoadFromUrl(downloadurl);
                        LOGGER.info("解析消息，内容为：" + jsonStr);
                        bbscjdk.setCurprogres(3);
                        commApiMapper.setBbscjdkToCurprogres(bbscjdk);
                        if (StringUtils.isNotEmpty(jsonStr)) {
                            JSONObject dObj = JSONObject.fromObject(jsonStr);
                            mark = false;
                            // 开始处理下载的数据
                            // 先入 tq_zdk， 有了这些数据，才能修改 kdzdb，jzzdbh计数， 以及补写日始，开班记录。
                            // 处理 账单库数据
                            saveZdk(dObj.optJSONArray("fdzdklist"));
                            // 处理 卧单库数据
                            saveWdk(dObj.optJSONArray("fdzdmxklist"));
                            saveWdk(dObj.optJSONArray("fdzdmxqyList"));
                            // 处理 付款流水库数据
                            saveFklslsk(dObj.optJSONArray("fdzdfkfsklist"));
                            // 处理 充值账单库数据
                            saveCzzdk(dObj.optJSONArray("fdczzdkList"));
                            // 处理  充值账单付款流水数据
                            saveCzlslsk(dObj.optJSONArray("fdczfkfskList"));
                            bbscjdk.setCurprogres(4);
                            bbscjdk.setCurcode(0);
                            bbscjdk.setCurmsg("拉取总部数据成功");
                            bbscjdk.setStoptime(new Date());
                            commApiMapper.setBbscjdkToCurprogres(bbscjdk);
                            break;
                        }
                    }
                    if (mark) {
                        LOGGER.info("下载文件三次均失败");
                        data.setCode(1);
                        data.setMsg("下载文件失败");
                        bbscjdk.setCurcode(-1);
                        bbscjdk.setCurmsg("下载文件失败");
                        bbscjdk.setStoptime(new Date());
                        commApiMapper.setBbscjdkToCurprogres(bbscjdk);
                        return;
                    }
                } else {
                    LOGGER.info("总部返回参数失败");
                    data.setCode(1);
                    data.setMsg("总部返回参数失败");
                    bbscjdk.setCurcode(-1);
                    bbscjdk.setCurmsg("总部返回参数失败");
                    bbscjdk.setStoptime(new Date());
                    commApiMapper.setBbscjdkToCurprogres(bbscjdk);
                    return;
                }
            } else if (404 == response.getStatus()) {
                LOGGER.info("请求总部失败,接口不存在，如有需求请联系管理员");
                data.setCode(404);
                data.setMsg("请求总部失败,接口不存在");
                bbscjdk.setCurcode(-1);
                bbscjdk.setCurmsg("请求总部失败,接口不存在");
                bbscjdk.setStoptime(new Date());
                commApiMapper.setBbscjdkToCurprogres(bbscjdk);
                return;
            } else {
                LOGGER.info("请求总部失败");
                data.setCode(1);
                data.setMsg("请求总部失败");
                bbscjdk.setCurcode(-1);
                bbscjdk.setCurmsg("请求总部失败");
                bbscjdk.setStoptime(new Date());
                commApiMapper.setBbscjdkToCurprogres(bbscjdk);
                return;
            }

            data.setCode(0);
            data.setMsg("成功");
        } catch (Exception e) {
            data.setCode(1);
            data.setMsg("系统异常！");
            LOGGER.error(e.getMessage(), e);
        }
    }

    public void saveZdk(JSONArray fdzdklist) {
        List<FdzdkInfo> zdkList = new ArrayList<FdzdkInfo>();
        String newBh = "0000000001";
        for (int i = 0; i < fdzdklist.size(); i++) {
            FdzdkInfo fdzdkInfo = initFdzdkInfo(fdzdklist.getJSONObject(i), newBh);
            zdkList.add(fdzdkInfo);
            newBh = fdzdkInfo.getZdbhbm();
        }
        if (zdkList.size() > 0) {
            commApiMapper.insertZdkInDown(zdkList);
            commApiMapper.updateBh(newBh, "TQ_ZDK", "KDZDBH");
            commApiMapper.updateBh(newBh, "TQ_ZDK", "JZZDBH");
            // 拿到最早的开单时间，作为日始和开班时间
            saveJtztk();
        }
    }

    public void saveWdk(JSONArray fdzdmxklist) {
        List<FdzdmxkInfo> wdkList = new ArrayList<FdzdmxkInfo>();
        int newRwid = commApiMapper.nextvals("tq_wdk_rwid_seq");
        for (int i = 0; i < fdzdmxklist.size(); i++) {
            FdzdmxkInfo fdzdmxkInfo = initFdzdmxkInfo(fdzdmxklist.getJSONObject(i), newRwid);
            wdkList.add(fdzdmxkInfo);
            newRwid = fdzdmxkInfo.getNewRwid();
        }
        if (wdkList.size() > 0) {
            commApiMapper.insertWdkInDown(wdkList);
            commApiMapper.setvals("tq_wdk_rwid_seq", newRwid);
        }
    }

    public void saveFklslsk(JSONArray fdzdfkfsklist) {
        List<FdzdfkfskInfo> fklslskList = new ArrayList<FdzdfkfskInfo>();
        int newId = commApiMapper.nextvals("tq_fklslsk_id_seq");
        for (int i = 0; i < fdzdfkfsklist.size(); i++) {
            FdzdfkfskInfo fdzdfkfskInfo = initFdzdfkfskInfo(fdzdfkfsklist.getJSONObject(i), newId);
            fklslskList.add(fdzdfkfskInfo);
            newId = fdzdfkfskInfo.getNewId();
        }
        if (fklslskList.size() > 0) {
            commApiMapper.insertFklslskInDown(fklslskList);
            commApiMapper.setvals("tq_fklslsk_id_seq", newId);
        }
    }

    public void saveJtztk() {
        TqJtztk jtztk = commApiMapper.getCzsj();
        TsBck bck = commApiMapper.getBcid(jtztk.getCzsj());
        jtztk.setJhid("99");
        jtztk.setRybh("0001");
        jtztk.setRyxm("系统管理员");
        jtztk.setSqczrybh("0001");
        jtztk.setClbz("0");
        jtztk.setYgdlcs("1");
        int kssyId = commApiMapper.nextvals("tq_jtztk_id_seq");
        jtztk.setId(kssyId);
        jtztk.setCznr("KSSY");
        if (null != bck) {
            jtztk.setYl1(bck.getId() + "");
            jtztk.setYl2(bck.getBcmc1());
        }
        commApiMapper.insertJtztk(jtztk);

        int yydlId = commApiMapper.nextvals("tq_jtztk_id_seq");
        jtztk.setId(yydlId);
        jtztk.setCznr("YYDL");
        jtztk.setMemo("IN");
        commApiMapper.insertJtztk(jtztk);
    }

    public void saveCzzdk(JSONArray fdczzdkList) {
        List<FdczzdkInfo> czzdkList = new ArrayList<FdczzdkInfo>();
        for (int i = 0; i < fdczzdkList.size(); i++) {
            FdczzdkInfo fdczzdkInfo = initFdczzdkInfo(fdczzdkList.getJSONObject(i));
            czzdkList.add(fdczzdkInfo);
        }
        if (czzdkList.size() > 0) {
            commApiMapper.insertCzzdkInDown(czzdkList);
        }
    }

    public void saveCzlslsk(JSONArray fdczfkfskList) {
        List<FdczlslskInfo> czfklslskList = new ArrayList<FdczlslskInfo>();
        int newId = commApiMapper.nextvals("tq_czlslsk_id_seq");
        for (int i = 0; i < fdczfkfskList.size(); i++) {
            FdczlslskInfo fdczlslskInfo = initFdczlslskInfo(fdczfkfskList.getJSONObject(i), newId);
            czfklslskList.add(fdczlslskInfo);
            newId = fdczlslskInfo.getNewId();
        }
        if (czfklslskList.size() > 0) {
            commApiMapper.insertCzlslskInDown(czfklslskList);
            commApiMapper.setvals("tq_czlslsk_id_seq", newId);
        }
    }

    public FdzdkInfo initFdzdkInfo(JSONObject opt, String zdbhBm) {
        FdzdkInfo zdk = new FdzdkInfo();
        zdk.setJzzdbh(opt.optString("zdbh", ""));
        zdk.setKdzdbh(opt.optString("zdbh", ""));
        zdk.setLsdh("0000");
        zdk.setKdbbrq(DateUtil.parseDate(opt.optString("bbrq", "2020-01-01")));
        zdk.setJzbbrq(DateUtil.parseDate(opt.optString("bbrq", "2020-01-01")));
        zdk.setCbid(opt.optInt("cbid", 0));
        zdk.setZwbh(opt.optString("zwbh", ""));
        zdk.setXfks(opt.optInt("xfks", 0));
        zdk.setKtsj(DateUtil.parseDateAll(opt.optString("ksdcsj", "2020-01-01 00:00:00")));
        zdk.setJzsj(DateUtil.parseDateAll(opt.optString("jzsj", "2020-01-01 00:00:00")));
        zdk.setJzcs(1);
        zdk.setKtskjh(opt.optString("skjh", "99"));
        zdk.setJzskjh(opt.optString("skjh", "99"));
//        zdk.setFwyh(opt.optString("fwyh", "0001"));
        zdk.setFwyh("0001");
        zdk.setKtczry("0001");
        zdk.setJzczry("0001");
        zdk.setKtbcid(opt.optInt("bcid", 0));
        zdk.setJzbcid(opt.optInt("bcid", 0));
        zdk.setYddh(opt.optString("yddh", ""));
        zdk.setYjje(opt.optDouble("yjje", 0));
        zdk.setXmxjje(opt.optDouble("xjje", 0));
        zdk.setZdje(opt.optDouble("xjje", 0));
        zdk.setFkje(opt.optDouble("sjje", 0));
        zdk.setFkce(ArithUtil.sub(opt.optDouble("xjje", 0), opt.optDouble("sjje", 0)));
        zdk.setZkje(opt.optDouble("zkje", 0));
        zdk.setZrje(opt.optDouble("zrje", 0));
        zdk.setMlje(opt.optDouble("mlje", 0));
        zdk.setDpzkje(0.00);
        zdk.setYhje(opt.optDouble("yhje", 0));
        zdk.setFsje(opt.optDouble("fsje", 0));
        zdk.setDslj(opt.optDouble("dslj", 0));
        zdk.setMdje(opt.optDouble("mdje", 0));
        zdk.setYhczry("0001");
        zdk.setZkl(opt.optInt("zkl", 100));
        zdk.setMdyybh(opt.optString("ylzda", ""));
        zdk.setZdbz(opt.optString("zwbz", ""));
        zdk.setXsms(opt.optString("xsms", ""));
        zdk.setCwlxbh(opt.optString("wincardjf", "0"));
        zdk.setZdzt(opt.optString("zdzt", ""));
        zdk.setJzsx(opt.optString("jzbz", ""));
        zdk.setKsdcsj(DateUtil.parseDateAll(opt.optString("ksdcsj", "2020-01-01 00:00:00")));
        zdk.setKsjzsj(DateUtil.parseDateAll(opt.optString("ksjzsj", "2020-01-01 00:00:00")));
        zdk.setJzjssj(DateUtil.parseDateAll(opt.optString("jzjssj", "2020-01-01 00:00:00")));
        zdk.setBcqrsj(DateUtil.parseDateAll(opt.optString("bcqrsj", "2020-01-01 00:00:00")));
        zdk.setSource(opt.optString("source", "POS"));
        zdk.setPsfje(opt.optDouble("psfje", 0));
        zdk.setYhyhje(opt.optDouble("yhyhje", 0));
        zdk.setPtyhje(opt.optDouble("ptyhje", 0));
        zdk.setPyyjje(opt.optDouble("pyyjje", 0));
        zdk.setYhfkje(opt.optDouble("yhfkje", 0));
        zdk.setYongjje(opt.optDouble("yjjy", 0));
        zdk.setDeliveryType(opt.optInt("delivery_type", 0));
        zdk.setEcototalfee(opt.optDouble("ecototalfee", 0));
        zdk.setZpsfje(opt.optDouble("zpsfje", 0));
        zdk.setWmtype(opt.optInt("wmtype", 0));
        zdk.setCardno(opt.optString("dyhs", ""));
        zdk.setPhone(opt.optString("gkzh", ""));
        zdk.setMembername(opt.optString("gkxm", ""));
        zdk.setScbj(1);

        int iBm = Integer.parseInt(zdbhBm);
        String newBm = opt.optString("zdbh", "");
        newBm = newBm.substring(3,newBm.length());
        int newiBm = Integer.parseInt(newBm);
        if (newiBm > iBm) {
            zdbhBm = newiBm + "";
        } else {
            zdbhBm = iBm + "";
        }
        zdbhBm = StringUtil.addStrLeft(zdbhBm, 10, "0");
        zdk.setZdbhbm(zdbhBm);

        return zdk;
    }

    public FdzdmxkInfo initFdzdmxkInfo(JSONObject opt, int newRwid) {
        FdzdmxkInfo wdk = new FdzdmxkInfo();
        wdk.setRwid(opt.optInt("rwid", 0));
        wdk.setKdzdbh(opt.optString("zdbh", ""));
        wdk.setJzzdbh(opt.optString("zdbh", ""));
        wdk.setClmxid(opt.optInt("clmxid", -1));
        wdk.setCmid(opt.optInt("xmid", 0));
        wdk.setCmbh(opt.optString("xmbh", ""));
        wdk.setCmmc1(opt.optString("xmmc", ""));
        wdk.setDwbh(opt.optString("dwbh", ""));
        wdk.setTcfs(opt.optString("tcfs", ""));
        wdk.setTcbl(opt.optDouble("tcbl", 0));
        wdk.setTcje(opt.optDouble("tcje", 0));
        wdk.setFzsl(opt.optDouble("fzsl", 0));
        wdk.setFzje(0);
        wdk.setZdsj(0);
        wdk.setFwyh("0001");
        wdk.setCmdj(new BigDecimal(opt.optDouble("cmdj", 0)));
        wdk.setCmsl(opt.optInt("cmsl", 0));
        wdk.setCmje(new BigDecimal(opt.optDouble("cpje", 0)));
        wdk.setSjje(new BigDecimal(opt.optDouble("sjje", 0)));
        wdk.setZkzt(opt.optString("zkzt", ""));
        wdk.setXlzkzt(opt.optString("xlzkzt", ""));
        wdk.setZkl(opt.optInt("zkl", 100));
        String yhfsid = opt.optString("yhfsid", "");
        if("".equals(yhfsid)) {
            wdk.setYhfsid(null);
        } else {
            wdk.setYhfsid(opt.optInt("yhfsid", -1));
        }
        wdk.setYhfs(opt.optString("yhfs", ""));
        String xmsx = opt.optString("xmsx", "");
        String cmsx = "";
        if ("单品".equals(xmsx)) {
            cmsx = "CMSX_DP";
        } else if ("套菜".equals(xmsx)) {
            cmsx = "CMSX_TC";
        } else if ("明细".equals(xmsx)) {
            cmsx = "CMSX_MX";
        }
        wdk.setCmsx(cmsx);
        wdk.setWdbz(opt.optString("wdbz", ""));
        wdk.setTmbj(DateUtil.parseDateAll(opt.optString("tmbj", "2020-01-01 00:00:00")));
        wdk.setDcxh(opt.optInt("dcxh", 0));
        wdk.setFsbbrq(DateUtil.parseDate(opt.optString("bbrq", "2020-01-01")));
        wdk.setJzbbrq(DateUtil.parseDate(opt.optString("bbrq", "2020-01-01")));
        wdk.setFsbcid(opt.optInt("bcid", 0));
        wdk.setJzbcid(opt.optInt("bcid", 0));
        wdk.setFsskjh(opt.optString("skjh", "99"));
        wdk.setJzskjh(opt.optString("skjh", "99"));
        wdk.setKwbz(opt.optString("kwbh", ""));
        wdk.setDcbz(opt.optString("dcbz", ""));
        wdk.setCsbh(opt.optString("csbh", ""));
        wdk.setScbj(1);

        if (newRwid > wdk.getRwid()) {
            wdk.setNewRwid(newRwid);
        } else {
            wdk.setNewRwid(wdk.getRwid());
        }

        return wdk;
    }

    public FdzdfkfskInfo initFdzdfkfskInfo(JSONObject opt, int newId) {
        FdzdfkfskInfo fklslsk = new FdzdfkfskInfo();
        fklslsk.setId(opt.optInt("rwid", 0));
        fklslsk.setKdzdbh(opt.optString("zdbh", ""));
        fklslsk.setJzzdbh(opt.optString("zdbh", ""));
        fklslsk.setJzid(opt.optInt("jzid", 0));
        fklslsk.setFkfsmc1(opt.optString("fkfs", ""));
        fklslsk.setFkje(opt.optDouble("fkje", 0));
        fklslsk.setFksl(1);
        fklslsk.setFkhm(opt.optString("fkhm", ""));
        fklslsk.setSfzhm(opt.optString("sfzh", ""));
        fklslsk.setLxdh(opt.optString("lxdh", ""));
        fklslsk.setFkbz(opt.optString("fkbz", ""));
        fklslsk.setJzbbrq(DateUtil.parseDate(opt.optString("bbrq", "2020-01-01")));
        fklslsk.setJzbcid(opt.optInt("bcid", 0));
        fklslsk.setSkjh(opt.optString("skjh", "99"));
        fklslsk.setSkyh("0001");
        fklslsk.setCzsj(DateUtil.parseDateAll(opt.optString("fssj", "2020-01-01 00:00:00")));
        fklslsk.setSfysk("N");
        fklslsk.setSfzl("N");
        fklslsk.setLv(0);
        fklslsk.setBbje(opt.optDouble("bbje", 0));
        fklslsk.setScbj(1);

        if (newId > fklslsk.getId()) {
            fklslsk.setNewId(newId);
        } else {
            fklslsk.setNewId(fklslsk.getId());
        }

        return fklslsk;
    }

    public FdczzdkInfo initFdczzdkInfo(JSONObject opt) {
        FdczzdkInfo czzdk = new FdczzdkInfo();
        czzdk.setKdzdbh(opt.optString("zdbh", ""));
        czzdk.setLsdh("0000");
        czzdk.setKdbbrq(DateUtil.parseDate(opt.optString("bbrq", "2020-01-01")));
        czzdk.setCbid(opt.optInt("cbid", 0));
        czzdk.setXfks(opt.optInt("xfks", 1));
        czzdk.setKtsj(DateUtil.parseDateAll(opt.optString("ktsj", "2020-01-01 00:00:00")));
        czzdk.setJzsj(DateUtil.parseDateAll(opt.optString("jzsj", "2020-01-01 00:00:00")));
        czzdk.setJzcs(opt.optInt("jzcs", 1));
        czzdk.setKtskjh(opt.optString("ktskjh", "99"));
        czzdk.setJzskjh(opt.optString("jzskjh", "99"));
        czzdk.setFwyh("0001");
        czzdk.setKtczry("0001");
        czzdk.setJzczry("0001");
        czzdk.setKtbcid(opt.optInt("ktbcid", 0));
        czzdk.setJzbcid(opt.optInt("ktbcid", 0));
        czzdk.setYjje(opt.optDouble("yjje", 0));
        czzdk.setXmxjje(opt.optDouble("zjje", 0));
        czzdk.setZdje(opt.optDouble("zjje", 0));
        czzdk.setFkje(opt.optDouble("sjje", 0));
        czzdk.setFkce(opt.optDouble("fkce", 0));
        czzdk.setMlje(opt.optDouble("mlje", 0));
        czzdk.setYhje(opt.optDouble("yhje", 0));
        czzdk.setJzsx(opt.optString("jzsx", "ZDSX_YJ"));
        czzdk.setZdzt(opt.optString("zdzt", ""));
        czzdk.setKsdcsj(DateUtil.parseDateAll(opt.optString("ksdcsj", "2020-01-01 00:00:00")));
        czzdk.setKsjzsj(DateUtil.parseDateAll(opt.optString("ksjzsj", "2020-01-01 00:00:00")));
        czzdk.setJzjssj(DateUtil.parseDateAll(opt.optString("jzjssj", "2020-01-01 00:00:00")));
        czzdk.setSource(opt.optString("source", ""));
        czzdk.setYgdlcs(opt.optString("ygdlcs", "1"));
        czzdk.setZdbz(opt.optString("zdbz", ""));
        czzdk.setCardno(opt.optString("cardno", ""));
        czzdk.setBizid(opt.optString("bizid", ""));
        czzdk.setScbj(1);

        return czzdk;
    }

    public FdczlslskInfo initFdczlslskInfo(JSONObject opt, int newId) {
        FdczlslskInfo czlslsk = new FdczlslskInfo();
        czlslsk.setId(opt.optInt("rwid", 0));
        czlslsk.setKdzdbh(opt.optString("zdbh", ""));
        czlslsk.setZwbh(opt.optString("zwbh", ""));
        czlslsk.setFklxsx(opt.optString("fklxsx", ""));
        czlslsk.setJzid(opt.optInt("jzid", 0));
        czlslsk.setFkfsmc1(opt.optString("fkfsmc1", ""));
        czlslsk.setFkfsmc2(opt.optString("fkfsmc2", ""));
        czlslsk.setFkje(opt.optDouble("fkje", 0));
        czlslsk.setFksl(opt.optInt("fksl", 1));
        czlslsk.setFkkz(opt.optString("fkkz", ""));
        czlslsk.setFkhm(opt.optString("fkhm", ""));
        czlslsk.setSfzhm(opt.optString("sfzhm", ""));
        czlslsk.setLxdh(opt.optString("lxdh", ""));
        czlslsk.setFkbz(opt.optString("fkbz", ""));
        czlslsk.setJzbbrq(DateUtil.parseDate(opt.optString("bbrq", "2020-01-01")));
        czlslsk.setJzbcid(opt.optInt("bcid", 0));
        czlslsk.setSkjh(opt.optString("skjh", "99"));
        czlslsk.setSkyh("0001");
        czlslsk.setCzsj(DateUtil.parseDateAll(opt.optString("czsj", "2020-01-01 00:00:00")));
        czlslsk.setJgxh(opt.optString("jgxh", ""));
        czlslsk.setSfysk(opt.optString("sfysk", "N"));
        czlslsk.setSfzl(opt.optString("sfzl", "N"));
        czlslsk.setJthybh(opt.optString("jthybh", ""));
        czlslsk.setLv(opt.optDouble("lv", 0));
        czlslsk.setBbje(opt.optDouble("bbje", 0));
        czlslsk.setScbj(1);

        if (newId > czlslsk.getId()) {
            czlslsk.setNewId(newId);
        } else {
            czlslsk.setNewId(czlslsk.getId());
        }

        return czlslsk;
    }

    @Override
    public void getEcoOrderBack(CommApiData data, JSONObject jsondata) {
        try {

            EcoOrderBackVo eobv = GsonUtil.GsonToBean(jsondata.toString(), EcoOrderBackVo.class);
            List<TqZdk> zdkList = commApiMapper.getWmZdkList(eobv);

            // 处理外卖账单，直接返回需要查询的 订单号集合
            List<String> yddhList = getWmZdbhList(zdkList);
            // 获得以订单号为key的map
            Map<String, TqZdk> zdkMap = new HashMap<>();
            for (TqZdk tqZdk : zdkList) {
                String key = tqZdk.getYddh();
                zdkMap.put(key, tqZdk);
            }
            // 遍历全部订单，20个账单一个批次请求eco接口
            int startIndex = 0;
            int endIndex = 20;
            for (int i = startIndex; i < yddhList.size(); i += endIndex) {
                List<String> subList = yddhList.subList(i, Math.min(i + endIndex, yddhList.size()));
                String outorderid = "";
                for (int j = 0; j < subList.size(); j++) {
                    outorderid += subList.get(j) + ",";
                }
                outorderid = outorderid.substring(0, outorderid.length() - 1);
                JSONObject sendJsonData = new JSONObject();
                sendJsonData.put("outorderid", outorderid);

                String sign = ECOUitls.getSin(sendJsonData);
                String appId = InitDataListener.thirdMap.get("ECO_APPID");
                CommApiData ecoData = new CommApiData();
                String ecoDataUrl = "thirdorderapi/thirdpos/batchGetOrderDetail?sign=" + sign + "&app_id=" + appId;
                // 拿到全部返回数据
                JSONObject ecoResObj = sendEco(ecoDataUrl, sendJsonData);
                // 处理返回数据，解析出部分退，的订单json
                analysisEcoRes(ecoResObj, zdkMap);
            }

            data.setCode(0);
            data.setMsg("成功");
        } catch (Exception e) {
            data.setCode(1);
            data.setMsg("系统异常！");
            LOGGER.error(e.getMessage(), e);
        }
    }

    public JSONObject sendEco(String ecoDataUrl, JSONObject jsondata) {
        JSONObject result = new JSONObject();
        String url = InitDataListener.thirdMap.get("ECO_URL");
        String rest = null;
        url = url + "/" + ecoDataUrl;
        LOGGER.info("请求ECO地址：" + url);
        LOGGER.info("请求ECO参数：" + jsondata.toString());
        Map<String, String> paramsMap = new HashMap<String, String>();
        Map<String, String> jsondataMap = new HashMap<String, String>();
        jsondataMap.put("outorderid", jsondata.optString("outorderid", ""));
        rest = HttpClientUtil.commmonHttpPostECO(paramsMap, url, jsondataMap);
        //LOGGER.info("ECO返回参数：" + rest);
        if ("0".equals(paramsMap.get("code"))) {
            JSONObject restObj = JSONObject.fromObject(rest);
            boolean retSuccess = restObj.optBoolean("success", false);
            if (retSuccess) {
                result = restObj;
            }
        }
        return result;
    }

    // 处理外卖账单，直接返回需要查询的 订单号集合
    public List<String> getWmZdbhList(List<TqZdk> zdkList) {
        List<String> ydbhList = new ArrayList<String>();
        if (null != zdkList && zdkList.size() > 0) {
            for (TqZdk zdk : zdkList) {
                if (StringUtils.isNotBlank(zdk.getYddh())) {
                    String ydbh = zdk.getYddh();
                    if (ydbh.startsWith("ECO")) {
                        ydbh = ydbh.substring(3);
                        ydbhList.add(ydbh);
                    }
                }
            }
        }
        return ydbhList;
    }

    public void analysisEcoRes(JSONObject ecoResObj, Map<String, TqZdk> zdkMap) {
        JSONArray dataArr = ecoResObj.optJSONArray("data");
        for (int i = 0; i < dataArr.size(); i++) {
            JSONObject dejsonobj = dataArr.optJSONObject(i);
            String outorderid = dejsonobj.optString("outorderid", "");
            outorderid = "ECO" + outorderid;
            TqZdk zdk = zdkMap.get(outorderid);
            if (null != zdk) {
                double totalpriceOld = dejsonobj.optDouble("totalprice", 0);
                double refundPrice = dejsonobj.optDouble("refund_price", 0);
                double totalpriceNew =ArithUtil.sub(totalpriceOld, refundPrice);
                if (zdk.getZdje() > totalpriceNew) {
                    //dataObj 得到的是部分退的订单
                    //处理dataObj，达到退单数据要求
                    try {
                        JSONArray orderPart = new JSONArray();
                        JSONArray items = dejsonobj.optJSONArray("items");
                        for (int j = 0; j < items.size(); j++) {
                            JSONObject item = items.optJSONObject(j);
                            String partstatus = item.optString("partstatus", "0");
                            if ("2".equals(partstatus)) {
                                JSONObject refundItem = new JSONObject();
                                refundItem.put("itemid", item.optString("dishsno", ""));
                                refundItem.put("itemname", item.optString("itemname", ""));
                                refundItem.put("properties", item.optString("properties", "份"));
                                refundItem.put("bsetmeal", item.optString("bsetmeal", "0"));
                                refundItem.put("nature", "");
                                refundItem.put("partstatus", partstatus);
                                refundItem.put("partcount", item.optString("partcount", "1"));
                                refundItem.put("refund_price", item.optString("refund_price", "0"));
                                refundItem.put("kdishsno", item.optString("dishsno", ""));
                                refundItem.put("itemcount", item.optString("partcount", "1.00"));
                                orderPart.add(refundItem);
                            }
                        }
                        dejsonobj.put("orderPart", orderPart);
                        dejsonobj.put("shopreject_reason", "");

                        EcoRepData data = new EcoRepData();
                        HashMap<String, String> inparam = new HashMap<String, String>();
                        ecoOrderService.orderbackBefore(data, inparam, dejsonobj);
                        if (data.getSuccess().equals("true")) {
                            BillNoData billNoData = null;
                            BillNoData partBillNoData = null;
                            billNoData = globalLockGetDataUtil.getBillNoData("99", "8");
                            if (null != data.getIsOrderPart() && "Y".equals(data.getIsOrderPart())) {
                                partBillNoData = globalLockGetDataUtil.getBillNoData("99", "8");
                            }
                            ecoOrderService.orderbackProcess(billNoData, data, inparam, dejsonobj, partBillNoData);
                            // 不要发送打印，这里处理的账单早都接单了
                        }
                    } catch (EcoException ecoe) {
                        LOGGER.info("EcoException {}", ecoe.getMessage());
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }


}
