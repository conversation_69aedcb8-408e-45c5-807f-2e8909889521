package com.tzx.receiver.common.upload;

import com.tzx.receiver.common.utils.TzxSimpleHttpClient;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by Zhouxh on 2020-02-21.
 */
@Component
@Lazy(true)
public class UploadOverUpdateDispatcher {
    @Autowired
    private JdbcTemplate jdbcTemplate;
    private final Logger logger	= LoggerFactory.getLogger(getClass());
    public void excute(){
        try{
            String sql = "select * from tq_xfverlog where id in(\n" +
                    "select max(id) from tq_xfverlog where status=? group by dbname\n" +
                    ") ;";
            SqlRowSet sqlRowSet = jdbcTemplate.queryForRowSet(sql,new Object[]{"uploading"});
            while (sqlRowSet.next()){
                doUpload(sqlRowSet);
            }
        }catch (Exception E){
            logger.error("加载全局上传任务出现异常"+E.getMessage());
        }
    }
    @Transactional
    private void doUpload(SqlRowSet sqlRowSet){
        String url = "";
        List<NameValuePair> nvps = new ArrayList<>();
        url = sqlRowSet.getString("url");
        nvps.add(new BasicNameValuePair("sizeCount", sqlRowSet.getString("count")));
        nvps.add(new BasicNameValuePair("size", sqlRowSet.getString("count")));
        nvps.add(new BasicNameValuePair("zlbh", sqlRowSet.getString("zlbh")));
        nvps.add(new BasicNameValuePair("dbName", sqlRowSet.getString("dbName")));
        nvps.add(new BasicNameValuePair("jgxh", sqlRowSet.getString("jgxh")));
        nvps.add(new BasicNameValuePair("versionsTime", sqlRowSet.getString("versionsTime")));
        nvps.add(new BasicNameValuePair("status","S"));
        TzxSimpleHttpClient.HttpClientByPostByNVP(nvps,url);

        //更新数据库状态
        try{
            String sql = "update tq_xfverlog set status=? where id<=? and status=? and dbname=?";
            jdbcTemplate.update(sql, new Object[]{"done",sqlRowSet.getInt("id"),"uploading",sqlRowSet.getString("dbName")});
        }catch (Exception E){
            logger.error("更新回传pos同步状态异常",E.getMessage());
        }


    }
}
