package com.tzx.mobilepos.rest.mapper;

import com.tzx.publics.base.MyMapper;
import com.tzx.mobilepos.rest.model.TqYhMtCouponsTemp;
import org.apache.ibatis.annotations.Param;

public interface MobilePosTqYhMtCouponsTempMapper extends MyMapper<TqYhMtCouponsTemp> {

	public Integer findClmxid(@Param("xmid") Integer xmid);

	/**
	 *
     * @Description: 取消优惠方式
	 * @param @param zdbh
	 * @param @param yzm
	 * @return void
	 * @throws
	 * <AUTHOR>
	 * @email  <EMAIL>
	 * @date 2018-12-20
	 */
	public void cancelThirdYhfs(@Param("zdbh") String zdbh, @Param("yzm") String yzm);

	public void deleteByZdbh(@Param("zdbh") String zdbh);
	
}
