package com.tzx.publics.common;

import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;

public class Object2StringUtils {
	public static final String FORMAT_TIME_DATETIME = "yyyy-MM-dd HH:mm:ss";

	public static Map<String, String> map2String(Map<String, Object> oriMap) {
		if (oriMap != null && !oriMap.isEmpty()) {
			Map<String, String> targetMap = new HashMap<String, String>(oriMap.size());
			Set<String> keySet = oriMap.keySet();
			for (Iterator<String> it = keySet.iterator(); it.hasNext();) {
				String key = it.next();
				Object objVal = oriMap.get(key);
				String value = "";
				if (objVal instanceof java.sql.Time) {
					value = new DateTime(((java.sql.Time) objVal).getTime()).toString(FORMAT_TIME_DATETIME);
				} else if (objVal instanceof java.sql.Timestamp) {
					value = new DateTime(((java.sql.Timestamp) objVal).getTime()).toString(FORMAT_TIME_DATETIME);
				} else if (objVal instanceof java.sql.Date) {
					value = new DateTime(((java.sql.Date) objVal).getTime()).toString(FORMAT_TIME_DATETIME);
				} else {
					value = objVal.toString();
				}
				targetMap.put(key, StringUtils.trim(value));
			}
			return targetMap;
		}
		return null;
	}
	public static String object2String(Object val,String defaultValue) {
		String rtnVal =null;
		try {
			if(val != null) {
				rtnVal =  val.toString();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}finally {
			if(rtnVal == null || "".equals(rtnVal)) {
				rtnVal = defaultValue;
			}
		}
		return rtnVal;
	}
}
