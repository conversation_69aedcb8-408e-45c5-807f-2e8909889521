package com.tzx.miniapp.rest.model;

import com.tzx.publics.base.BaseEntity;

import javax.persistence.Table;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2018-08-13
 */
@Table(name = "TS_GGCSK")
public class TsGgcsk extends BaseEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	private String sdbt;
	private String sdnr;
	private String syfw;
	private String bzsm;
	private String qybh;
	private String jhid;
	private String yl1;
	private String yl2;
	private String yl3;

	public String getSdbt() {
		return sdbt;
	}

	public void setSdbt(String sdbt) {
		this.sdbt = sdbt;
	}

	public String getSdnr() {
		return sdnr;
	}

	public void setSdnr(String sdnr) {
		this.sdnr = sdnr;
	}

	public String getSyfw() {
		return syfw;
	}

	public void setSyfw(String syfw) {
		this.syfw = syfw;
	}

	public String getBzsm() {
		return bzsm;
	}

	public void setBzsm(String bzsm) {
		this.bzsm = bzsm;
	}

	public String getQybh() {
		return qybh;
	}

	public void setQybh(String qybh) {
		this.qybh = qybh;
	}

	public String getJhid() {
		return jhid;
	}

	public void setJhid(String jhid) {
		this.jhid = jhid;
	}

	public String getYl1() {
		return yl1;
	}

	public void setYl1(String yl1) {
		this.yl1 = yl1;
	}

	public String getYl2() {
		return yl2;
	}

	public void setYl2(String yl2) {
		this.yl2 = yl2;
	}

	public String getYl3() {
		return yl3;
	}

	public void setYl3(String yl3) {
		this.yl3 = yl3;
	}

}
