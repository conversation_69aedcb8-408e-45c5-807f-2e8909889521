package com.tzx.publics.common;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @Date 2019-05-05
 * @Descption
 **/
@Controller
public class WebViewController {
    @RequestMapping("/index")
    public void index(HttpServletRequest request, HttpServletResponse response){
        try {
            request.getRequestDispatcher("/static/pages/index.html").forward(request,response);
        } catch (ServletException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
    @RequestMapping("/")
    public void init(HttpServletRequest request, HttpServletResponse response){
        try {
            request.getRequestDispatcher("/static/pages/index.html").forward(request,response);
        } catch (ServletException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @RequestMapping("/task")
    public void task(HttpServletRequest request, HttpServletResponse response){
        try {
            request.getRequestDispatcher("/static/pages/task.html").forward(request,response);
        } catch (ServletException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
