package com.tzx.miniapp.rest.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import com.tzx.publics.base.BaseEntity;

@Table(name = "TS_YHFSSDK")
public class TsYhfssdk extends BaseEntity implements Serializable {

	private static final long serialVersionUID = 1L;

	@Column(name = "LBID")
	private int lbid;
	@Column(name = "ID", insertable = false)
	private int id;
	@Column(name = "YHFSBH")
	private String yhfsbh;
	@Column(name = "YHFSMC1")
	private String yhfsmc1;
	@Column(name = "YHFSMC2")
	private String yhfsmc2;
	@Column(name = "KSRQ")
	private String ksrq;
	@Column(name = "JSRQ")
	private String jsrq;
	@Column(name = "KSSJ")
	private String kssj;
	@Column(name = "JSSJ")
	private String jssj;
	@Column(name = "YHSX")
	private String yhsx;
	@Column(name = "ZKL")
	private int zkl;
	@Column(name = "ZKFAID")
	private int zkfaid;
	@Column(name = "YHJE")
	private double yhje;
	@Column(name = "ZKFS")
	private String zkfs;
	@Column(name = "PQLXID")
	private int pqlxid;
	@Column(name = "ZKFABH")
	private String zkfabh;
	@Column(name = "FZJE")
	private double fzje;
	@Column(name = "MEMO")
	private String memo;
	@Column(name = "YL1")
	private String yl1;
	@Column(name = "YL2")
	private String yl2;
	@Column(name = "YL3")
	private String yl3;
	@Column(name = "BUYDISHREL")
	private int buydishrel;

	public int getLbid() {
		return lbid;
	}

	public void setLbid(int lbid) {
		this.lbid = lbid;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getYhfsbh() {
		return yhfsbh;
	}

	public void setYhfsbh(String yhfsbh) {
		this.yhfsbh = yhfsbh;
	}

	public String getYhfsmc1() {
		return yhfsmc1;
	}

	public void setYhfsmc1(String yhfsmc1) {
		this.yhfsmc1 = yhfsmc1;
	}

	public String getYhfsmc2() {
		return yhfsmc2;
	}

	public void setYhfsmc2(String yhfsmc2) {
		this.yhfsmc2 = yhfsmc2;
	}

	public String getKsrq() {
		return ksrq;
	}

	public void setKsrq(String ksrq) {
		this.ksrq = ksrq;
	}

	public String getJsrq() {
		return jsrq;
	}

	public void setJsrq(String jsrq) {
		this.jsrq = jsrq;
	}

	public String getKssj() {
		return kssj;
	}

	public void setKssj(String kssj) {
		this.kssj = kssj;
	}

	public String getJssj() {
		return jssj;
	}

	public void setJssj(String jssj) {
		this.jssj = jssj;
	}

	public String getYhsx() {
		return yhsx;
	}

	public void setYhsx(String yhsx) {
		this.yhsx = yhsx;
	}

	public int getZkl() {
		return zkl;
	}

	public void setZkl(int zkl) {
		this.zkl = zkl;
	}

	public int getZkfaid() {
		return zkfaid;
	}

	public void setZkfaid(int zkfaid) {
		this.zkfaid = zkfaid;
	}

	public double getYhje() {
		return yhje;
	}

	public void setYhje(double yhje) {
		this.yhje = yhje;
	}

	public String getZkfs() {
		return zkfs;
	}

	public void setZkfs(String zkfs) {
		this.zkfs = zkfs;
	}

	public int getPqlxid() {
		return pqlxid;
	}

	public void setPqlxid(int pqlxid) {
		this.pqlxid = pqlxid;
	}

	public String getZkfabh() {
		return zkfabh;
	}

	public void setZkfabh(String zkfabh) {
		this.zkfabh = zkfabh;
	}

	public double getFzje() {
		return fzje;
	}

	public void setFzje(double fzje) {
		this.fzje = fzje;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public String getYl1() {
		return yl1;
	}

	public void setYl1(String yl1) {
		this.yl1 = yl1;
	}

	public String getYl2() {
		return yl2;
	}

	public void setYl2(String yl2) {
		this.yl2 = yl2;
	}

	public String getYl3() {
		return yl3;
	}

	public void setYl3(String yl3) {
		this.yl3 = yl3;
	}

	public int getBuydishrel() {
		return buydishrel;
	}

	public void setBuydishrel(int buydishrel) {
		this.buydishrel = buydishrel;
	}

}
