package com.tzx.miniapp.rest.service;

import com.tzx.miniapp.common.Data;
import net.sf.json.JSONObject;

/**
 * 通知服务
 */
public interface IMiniAppNotification {

    Data synchrodataDish(JSONObject json);

    void requestNotification(String url, String appKey, String merchant, String store, String brand, String type, int force);

    Data sendmealmsg(JSONObject json);
    
    Data qmSynchrodataDish(JSONObject json);
}
