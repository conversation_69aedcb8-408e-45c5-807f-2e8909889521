package com.tzx.mobilepos.rest.model;

import com.tzx.publics.base.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2018-05-15
 */
@Table(name = "TS_JTSDK")
public class TsJtsdk extends BaseEntity implements Serializable {

	private static final long serialVersionUID = 1L;
	@Id
	@Column(name = "JTBH")
	private String jtbh;
	@Column(name = "IPDZ")
	private String ipdz;
	@Column(name = "JTBM")
	private String jtbm;
	@Column(name = "JTSX")
	private String jtsx;
	@Column(name = "DBVER")
	private String dbver;

	public String getJtbh() {
		return jtbh;
	}

	public void setJtbh(String jtbh) {
		this.jtbh = jtbh;
	}

	public String getIpdz() {
		return ipdz;
	}

	public void setIpdz(String ipdz) {
		this.ipdz = ipdz;
	}

	public String getJtbm() {
		return jtbm;
	}

	public void setJtbm(String jtbm) {
		this.jtbm = jtbm;
	}

	public String getJtsx() {
		return jtsx;
	}

	public void setJtsx(String jtsx) {
		this.jtsx = jtsx;
	}

	public String getDbver() {
		return dbver;
	}

	public void setDbver(String dbver) {
		this.dbver = dbver;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

}
