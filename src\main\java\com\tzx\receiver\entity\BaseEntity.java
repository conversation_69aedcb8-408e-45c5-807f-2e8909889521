package com.tzx.receiver.entity;

/**
 * <AUTHOR>
 * @Date 2019-04-17
 * @Descption
 **/
import java.io.Serializable;
import java.util.List;

public class BaseEntity implements Serializable
{

    private static final long	serialVersionUID	= 1841646150515368881L;

    private Long				id;

    private Long[]				delIds;

    private List<String>		orgList;

    public List<String> getOrgList()
    {
        return orgList;
    }

    public void setOrgList(List<String> orgList)
    {
        this.orgList = orgList;
    }

    /**
     * @return the removeIds
     */
    public Long[] getDelIds()
    {
        return delIds;
    }

    /**
     * @param removeIds
     *            the removeIds to set
     */
    public void setDelIds(Long[] delIds)
    {
        this.delIds = delIds;
    }

    public Long getId()
    {
        return id;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

}
