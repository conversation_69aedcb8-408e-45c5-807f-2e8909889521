package com.tzx.fmcgbi.rest.mapper;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.tzx.fmcgbi.rest.model.TqJtztk;
import com.tzx.fmcgbi.rest.model.TqThirdExceptOrder;
import com.tzx.fmcgbi.rest.model.TqZdk;
import com.tzx.fmcgbi.rest.model.TsBmkzk;
import com.tzx.fmcgbi.rest.model.TsCmk;
import com.tzx.fmcgbi.rest.vo.AccountsOrder;
import com.tzx.fmcgbi.rest.vo.CalcMoney;
import com.tzx.publics.base.MyMapper;

/**
 * 支付码支付接口（微信/支付宝/会员码）
 * 
 * <AUTHOR>
 */

public interface FmcgbiCodePayMapper extends MyMapper<TqZdk> {
	// 查询菜品数据
	public List<TsCmk> queryItemInfo(@Param("itemCodeList") List<String> itemCodeList);

	// 获取班次id
	public String getBcid();

	// 获取编码控制库
	public TsBmkzk getBmkzk(@Param("bmc") String bmc, @Param("zdmc") String zdmc);

	// 获取机台状态
	public TqJtztk getJtZtk(@Param("bbrq") Date bbrq);

	// 添加菜品
	public int addCmNew(@Param("szdbh") String szdbh, @Param("aitemid") int aitemid, @Param("ixmsl") int ixmsl,
			@Param("sskjh") String sskjh, @Param("sxsyh") String sxsyh, @Param("skwbh") String skwbh,
			@Param("sggbh") String sggbh, @Param("atype") int atype, @Param("ssysdc") String ssysdc,
			@Param("sjgxh") String sjgxh, @Param("sjgtxbh") String sjgtxbh, @Param("skwbz") String skwbz);

	// 分摊优惠
	public void zRtr(@Param("zdbh") String zdbh);

	// 计算账单金额
	public CalcMoney calcMoney(@Param("zdbh") String zdbh);

	// tq_third_except_order 插入数据
	public int insertThirdExceptOrder(@Param("tteo") TqThirdExceptOrder tteo);

	// 更新第三方支付状态
	public int updateToStatus(@Param("outtradeno") String outtradeno, @Param("paystatus") String paystatus);

	// 调用结账过程
	public AccountsOrder accountsOrder(@Param("szdbh") String szdbh, @Param("ijzid") int ijzid,
			@Param("ifkje") BigDecimal ifkje, @Param("ifksl") int ifksl, @Param("sfkhm") String sfkhm,
			@Param("ssfzhm") String ssfzhm, @Param("slxdh") String slxdh, @Param("sfkbz") String sfkbz,
			@Param("sskjh") String sskjh, @Param("sskyh") String sskyh);

	// 结账完成，完善zdk
	public int updateZdk(@Param("qch") String qch, @Param("zzbz") String zzbz, @Param("zwbh") String zwbh,
			@Param("kdzdbh") String kdzdbh, @Param("jzzdbh") String jzzdbh, @Param("jzbbrq") Date jzbbrq,
			@Param("jzsj") Date jzsj, @Param("jzcs") int jzcs, @Param("jzskjh") String jzskjh,
			@Param("jzczry") String jzczry, @Param("jzsx") String jzsx, @Param("ksjzsj") Date ksjzsj,
			@Param("jzjssj") Date jzjssj, @Param("jzbcid") int jzbcid, @Param("xfks") int xfks);

	// 结账完成，完善wdk
	public int updateWdk(@Param("kdzdbh") String kdzdbh, @Param("jzzdbh") String jzzdbh, @Param("jzskjh") String jzskjh, @Param("jzbbrq") Date jzbbrq, @Param("jzbcid") int jzbcid);

	// 结账完成，完善fklslsk
	public int updateFklslsk(@Param("kdzdbh") String kdzdbh, @Param("jzzdbh") String jzzdbh, @Param("jzbcid") int jzbcid);

}