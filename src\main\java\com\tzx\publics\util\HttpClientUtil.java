package com.tzx.publics.util;

import net.sf.json.JSONObject;
import org.apache.commons.httpclient.*;
import org.apache.commons.httpclient.methods.GetMethod;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.StringRequestEntity;
import org.apache.commons.httpclient.params.HttpMethodParams;
import org.apache.commons.httpclient.util.IdleConnectionTimeoutThread;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLContextBuilder;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.SSLContext;
import java.io.*;
import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.*;
import java.util.Map.Entry;

/**
 * 实现Https请求 https=http+ssl安全加密通信
 * 
 */
public class HttpClientUtil {
	public static final int CONNECTION_TIMEOUT = 5000;// 连接超时
	public static final int READDATA_TIMEOUT = 10000;// 数据读取等待超时
	public static final String UTF8 = "UTF-8";// 编码格式
	public static final String PLAIN_TEXT_TYPE = "text/plain";
	private final static Logger LOGGER = LoggerFactory.getLogger(HttpClientUtil.class);

	private static int						defaultConnectionTimeout			= 8000;
	private static final long				defaultHttpConnectionManagerTimeout	= 3 * 1000;

	/** 回应超时时间, 由bean factory设置，缺省为10秒钟 */
	private static int						defaultSoTimeout					= 10000;

	/** 闲置连接超时时间, 由bean factory设置，缺省为60秒钟 */
	private static int						defaultIdleConnTimeout				= 60000;

	private static int						defaultMaxConnPerHost				= 30;

	private static int						defaultMaxTotalConn					= 80;

	private static HttpConnectionManager connectionManager;
	static
	{
		try
		{
			if (connectionManager == null)// 创建一个线程安全的HTTP连接池
			{
				connectionManager = new MultiThreadedHttpConnectionManager();
				connectionManager.getParams().setDefaultMaxConnectionsPerHost(defaultMaxConnPerHost);
				connectionManager.getParams().setMaxTotalConnections(defaultMaxTotalConn);
				IdleConnectionTimeoutThread ict = new IdleConnectionTimeoutThread();
				ict.addConnectionManager(connectionManager);
				ict.setConnectionTimeout(defaultIdleConnTimeout);
				ict.start();
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
	}
	protected static String inputStream2String(InputStream is) throws UnsupportedEncodingException {
		BufferedReader in = new BufferedReader(new InputStreamReader(is,"UTF-8"));
		StringBuffer buffer = new StringBuffer();
		String line = "";
		try {
			while ((line = in.readLine()) != null) {
				buffer.append(line);
			}
		} catch (IOException e) {
			e.printStackTrace();
		}
		return buffer.toString();
	}

	public static String NewHttpClientByPost(String checkURL, String paramsJson) throws Exception
	{
		String responseContent = "";
		HttpClient httpClient = new HttpClient(HttpClientUtil.connectionManager);
		PostMethod postMethod = new PostMethod(checkURL);

		try
		{
			LOGGER.info("请求地址" + checkURL + ":" + paramsJson);

			httpClient.getHttpConnectionManager().getParams().setConnectionTimeout(defaultConnectionTimeout);
			httpClient.getHttpConnectionManager().getParams().setSoTimeout(defaultSoTimeout);
			httpClient.getParams().setConnectionManagerTimeout(defaultHttpConnectionManagerTimeout);

			postMethod.setRequestEntity(new StringRequestEntity(paramsJson, "application/json", "UTF-8"));
			int statusCode = httpClient.executeMethod(postMethod);
			if (statusCode != HttpStatus.SC_OK)
			{
				//throw SystemException.getInstance(SystemErrorCode.MQ_COMMUNICATION_ERROR).set("STATUS_LINE", postMethod.getStatusLine().toString());
				throw new RuntimeException("HttpStatus:"+postMethod.getStatusLine().toString());
			}

			//responseContent = postMethod.getResponseBodyAsString();
			InputStream resStream = postMethod.getResponseBodyAsStream();
			responseContent = new String(inputStream2String(resStream));

			/*BufferedReader br = new BufferedReader(new InputStreamReader(resStream));
			StringBuffer resBuffer = new StringBuffer();
			String resTemp = "";
			while ((resTemp = br.readLine()) != null) {
				resBuffer.append(resTemp);
			}
			responseContent = resBuffer.toString();*/
			LOGGER.info("请求地址" + checkURL + "返回信息:" + responseContent);

		}
		catch (ConnectException e)
		{
			LOGGER.error("无法连接，调用地址:" + checkURL);
		}
		catch (ConnectTimeoutException e)
		{
			LOGGER.error("连接超时，调用地址:" + checkURL);
		}
		finally
		{
			postMethod.releaseConnection();
		}
		return responseContent;
	}

	public static String HttpClientByPost(String checkURL, String paramsJson) throws Exception {
		String responseContent = "";

		PostMethod postMethod = new PostMethod(checkURL);
		try {
			// System.out.println("向服务传递post的Json串是:" + paramsJson);
			LOGGER.info("向服务传递post的Json串是:" + paramsJson);

			postMethod.setRequestEntity(new StringRequestEntity(paramsJson, "text/json", "UTF-8"));

			responseContent = postMethod.getResponseBodyAsString();

			LOGGER.info("post请求返回的是：" + responseContent + "-------");

			return responseContent;
		} finally {
			postMethod.releaseConnection();
		}
	}
	/**
	 * 无需本地证书keyStore的SSL https带参数请求
	 * @param url
	 * @param reqMap
	 * @return
	 */
	public static Map<String, String> postSSLUrlWithParams(String url, Map<String, String> reqMap) {
		Map<String, String> map = new HashMap<String, String>();
		map.put("success", "-1");
		map.put("msg", "请求异常！");
		CloseableHttpClient httpClient = HttpClientUtil.createSSLInsecureClient();
		HttpPost post = new HttpPost(url);
		// 添加参数
		MultipartEntityBuilder builder = MultipartEntityBuilder.create();
		// 决中文乱码
		ContentType contentType = ContentType.create(PLAIN_TEXT_TYPE, UTF8);
		Charset chars = Charset.forName("utf8"); // Setting up the encoding
		builder.setCharset(chars);
		builder.setMode(HttpMultipartMode.BROWSER_COMPATIBLE);
		LOGGER.info("请求数据：" + reqMap.toString());
		if (reqMap != null && reqMap.keySet().size() > 0) {
			Iterator<Map.Entry<String, String>> iter = reqMap.entrySet().iterator();
			while (iter.hasNext()) {
				Map.Entry<String, String> entity = iter.next();
				builder.addTextBody(entity.getKey(), entity.getValue().toString(), contentType);
			}
		}
		StringBuilder sb = new StringBuilder();
		BufferedReader br = null;
		try {
			// 设置客户端请求的头参数getParams已经过时,现在用requestConfig对象替换
			// httpClient.getParams().setParameter(CoreConnectionPNames.CONNECTION_TIMEOUT,CONNECTION_TIMEOUT);
			RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(CONNECTION_TIMEOUT).setSocketTimeout(READDATA_TIMEOUT).build();
			post.setConfig(requestConfig);
			HttpEntity entity = builder.build();
			post.setEntity(entity);

			HttpResponse response = httpClient.execute(post);
			HttpEntity httpEntity = response.getEntity();
			br = new BufferedReader(new InputStreamReader(httpEntity.getContent(), UTF8));
			String s = null;
			while ((s = br.readLine()) != null) {
				sb.append(s);
			}
			LOGGER.info("返回数据：" + sb.toString());
			if(sb.length() > 0){
				map.put("success", "0");
				map.put("msg", "OK");
				map.put("data", sb.toString());
			} else {
				map.put("msg", "服务器链接异常！");
			}
		} catch (SocketTimeoutException e) {
			LOGGER.error("请求超时", e);
			map.put("msg", "网络请求超时，请稍后再试！");
			// throw new RuntimeException("请求超时异常", e);
		} catch (UnsupportedEncodingException e) {
			LOGGER.error("编码格式错误", e);
			map.put("msg", "编码格式错误！");
			// throw new RuntimeException("指定的编码集不对,您目前指定的编码集是:" + UTF8);
		} catch (ClientProtocolException e) {
			LOGGER.error("客户端协议异常", e);
			map.put("msg", "客户端协议异常！");
		} catch (IOException e) {
			LOGGER.error("读取流文件异常", e);
			map.put("msg", "读取流文件异常！");
			// throw new RuntimeException("读取流文件异常", e);
		} catch (Exception e) {
			LOGGER.error("通讯未知系统异常", e);
			map.put("msg", "通讯未知系统异常！");
			// throw new RuntimeException("通讯未知系统异常", e);
		} finally {
			if (br != null) {
				try {
					br.close();
				} catch (IOException e) {
					LOGGER.error("关闭br异常" + e);
					e.printStackTrace();
				}
			}
		}
		return map;
	}

	/**
	 * 创建一个SSL信任所有证书的httpClient对象
	 */
	public static CloseableHttpClient createSSLInsecureClient() {
		try {
			SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustStrategy() {
				// 默认信任所有证书
				public boolean isTrusted(X509Certificate[] arg0, String arg1) throws CertificateException {
					return true;
				}
			}).build();
			// AllowAllHostnameVerifier: 这种方式不对主机名进行验证，验证功能被关闭，是个空操作(域名验证)
			SSLConnectionSocketFactory sslcsf = new SSLConnectionSocketFactory(sslContext, SSLConnectionSocketFactory.ALLOW_ALL_HOSTNAME_VERIFIER);
			return HttpClients.custom().setSSLSocketFactory(sslcsf).build();
		} catch (KeyManagementException e) {
			e.printStackTrace();
		} catch (NoSuchAlgorithmException e) {
			e.printStackTrace();
		} catch (KeyStoreException e) {
			e.printStackTrace();
		}
		return HttpClients.createDefault();
	}



	public static String NewHttpClientXMLByPost(String checkURL, String paramXml)
	{
		String responseContent = "";
		HttpClient httpClient = new HttpClient(HttpClientUtil.connectionManager);
		PostMethod postMethod = new PostMethod(checkURL);

		try
		{
			LOGGER.info("请求地址" + checkURL + "：" + paramXml);
			httpClient.getHttpConnectionManager().getParams().setConnectionTimeout(defaultConnectionTimeout);
			httpClient.getHttpConnectionManager().getParams().setSoTimeout(defaultSoTimeout);
			httpClient.getParams().setConnectionManagerTimeout(defaultHttpConnectionManagerTimeout);
//			postMethod.addRequestHeader("Accept-Encoding","gzip, deflate, br");
//			postMethod.addRequestHeader("Connection","'keep-alive");

			postMethod.setRequestEntity(new StringRequestEntity(paramXml, "text/xml", "utf-8"));
			int statusCode = httpClient.executeMethod(postMethod);
			if (statusCode != HttpStatus.SC_OK)
			{
				LOGGER.error("返回状态码"+statusCode+"，调用地址:" + checkURL);
				return  responseContent;
			}

			InputStream resStream = postMethod.getResponseBodyAsStream();
			responseContent = new String(inputStream2String(resStream));

			LOGGER.info("请求地址" + checkURL + "返回信息:" + responseContent);

		} catch (ConnectException e) {
			LOGGER.error("无法连接，调用地址:" + checkURL);
			LOGGER.error("无法连接，调用地址:", e);
		} catch (ConnectTimeoutException e) {
			LOGGER.error("连接超时，调用地址:" + checkURL);
			LOGGER.error("无法连接，调用地址:", e);
		} catch (Exception e) {
			LOGGER.error("请求失败，其他异常:" + checkURL);
			LOGGER.error("请求失败，其他异常:", e);
		} finally {
			postMethod.releaseConnection();
		}
		return responseContent;
	}
	
	public static Map<String, String> merchantSendGet(String url, Map<String, String> reqMap) {
		Map<String, String> rMap = new HashMap<String, String>();
		rMap.put("code", "-1");
		rMap.put("msg", "请求异常！");
		
		String result = "";
        BufferedReader in = null;
        try {
			StringBuilder param = new StringBuilder();
			Set<Entry<String, String>> entrySet = reqMap.entrySet();
			for (Entry<String, String> e : entrySet) {
				String key = e.getKey();
				String value = e.getValue();
				param.append("&" + key + "=" + URLEncoder.encode(value, "UTF-8"));
			}
			String paramStr = param.toString();
			paramStr = paramStr.substring(1);
            String urlNameString = url + "?" + paramStr;
            LOGGER.info("url:" + urlNameString);
            URL realUrl = new URL(urlNameString);
            URLConnection connection = realUrl.openConnection();
            connection.setRequestProperty("accept", "*/*");
            connection.setRequestProperty("connection", "Keep-Alive");
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            connection.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            connection.connect();
//            Map<String, List<String>> map = connection.getHeaderFields();
//            for (String key : map.keySet()) {
//                System.out.println(key + "--->" + map.get(key));
//            }
            //接收核心返回过来的数据 xml  需要解析
            in = new BufferedReader(new InputStreamReader(connection.getInputStream(),"UTF-8"));
            String line;
            while ((line = in.readLine()) != null) {
                result += line;
            }
            LOGGER.info("返回数据：" + result);
			if(result.length() > 0){
				rMap.put("code", "0");
				rMap.put("msg", "OK");
				rMap.put("data", result);
			} else {
				rMap.put("msg", "服务器链接异常！");
			}
        } catch (SocketTimeoutException e) {
			LOGGER.error("请求超时", e);
			rMap.put("msg", "网络请求超时，请稍后再试！");
			// throw new RuntimeException("请求超时异常", e);
		} catch (UnsupportedEncodingException e) {
			LOGGER.error("编码格式错误", e);
			rMap.put("msg", "编码格式错误！");
			// throw new RuntimeException("指定的编码集不对,您目前指定的编码集是:" + UTF8);
		} catch (ClientProtocolException e) {
			LOGGER.error("客户端协议异常", e);
			rMap.put("msg", "客户端协议异常！");
		} catch (IOException e) {
			LOGGER.error("读取流文件异常", e);
			rMap.put("msg", "读取流文件异常！");
			// throw new RuntimeException("读取流文件异常", e);
		} catch (Exception e) {
			LOGGER.error("通讯未知系统异常", e);
			rMap.put("msg", "通讯未知系统异常！");
			// throw new RuntimeException("通讯未知系统异常", e);
		} finally {
            try {
                if (in != null) {
                    in.close();
                }
            } catch (Exception e2) {
            	rMap.put("msg", "通讯未知系统异常！");
                e2.printStackTrace();
            }
        }
        return rMap;
    }

	public static Map<String, String> commmonHttpGet(String url) {
		Map<String, String> rMap = new HashMap<String, String>();
		rMap.put("code", "-1");
		rMap.put("msg", "请求异常！");

		String result = "";
		BufferedReader in = null;
		try {

			String urlNameString = url;
			LOGGER.info("url:" + urlNameString);
			URL realUrl = new URL(urlNameString);
			URLConnection connection = realUrl.openConnection();
			connection.setRequestProperty("accept", "*/*");
			connection.setRequestProperty("connection", "Keep-Alive");
			connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
			connection.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
			connection.connect();
			//接收核心返回过来的数据 xml  需要解析
			in = new BufferedReader(new InputStreamReader(connection.getInputStream(),"UTF-8"));
			String line;
			while ((line = in.readLine()) != null) {
				result += line;
			}
			LOGGER.info("返回数据：" + result);
			if(result.length() > 0){
				rMap.put("code", "0");
				rMap.put("msg", "OK");
				rMap.put("data", result);
			} else {
				rMap.put("msg", "服务器链接异常！");
			}
		} catch (SocketTimeoutException e) {
			LOGGER.error("请求超时", e);
			rMap.put("msg", "网络请求超时，请稍后再试！");
			// throw new RuntimeException("请求超时异常", e);
		} catch (UnsupportedEncodingException e) {
			LOGGER.error("编码格式错误", e);
			rMap.put("msg", "编码格式错误！");
			// throw new RuntimeException("指定的编码集不对,您目前指定的编码集是:" + UTF8);
		} catch (ClientProtocolException e) {
			LOGGER.error("客户端协议异常", e);
			rMap.put("msg", "客户端协议异常！");
		} catch (IOException e) {
			LOGGER.error("读取流文件异常", e);
			rMap.put("msg", "读取流文件异常！");
			// throw new RuntimeException("读取流文件异常", e);
		} catch (Exception e) {
			LOGGER.error("通讯未知系统异常", e);
			rMap.put("msg", "通讯未知系统异常！");
			// throw new RuntimeException("通讯未知系统异常", e);
		} finally {
			try {
				if (in != null) {
					in.close();
				}
			} catch (Exception e2) {
				rMap.put("msg", "通讯未知系统异常！");
				e2.printStackTrace();
			}
		}
		return rMap;
	}


	public static String commmonHttpGet(Map<String, String> paramsMap, String url,String queryString){
		paramsMap.put("sysCode","1");//失败
		paramsMap.put("sysMsg","未知错误");//失败
		HttpClient httpClient = new HttpClient(HttpClientUtil.connectionManager);
		httpClient.getHttpConnectionManager().getParams().setConnectionTimeout(defaultConnectionTimeout);
		httpClient.getHttpConnectionManager().getParams().setSoTimeout(defaultSoTimeout);
		httpClient.getParams().setConnectionManagerTimeout(defaultHttpConnectionManagerTimeout);
		GetMethod getMethod = new GetMethod(url);
//		LOGGER.info(Arrays.toString(getMethod.getRequestHeaders()).toString());
//		getMethod.addRequestHeader("accept","*/*");
//		getMethod.addRequestHeader("Content-Type","application/x-www-form-urlencoded");
//		getMethod.addRequestHeader("connection","Keep-Alive");
//		getMethod.addRequestHeader("user-agent","Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
//		LOGGER.info(Arrays.toString(getMethod.getRequestHeaders()));
//		getMethod.getParams().setParameter(HttpMethodParams.SO_TIMEOUT, 5000);
//		getMethod.getParams()
//				.setParameter(HttpMethodParams.RETRY_HANDLER, new DefaultHttpMethodRetryHandler());
		LOGGER.info(url);
		LOGGER.info("请求url："+ url + ",请求参数："+ queryString);
		getMethod.setQueryString(queryString);
//		headerMap.entrySet().stream().forEach(item->{
//			getMethod.setRequestHeader(item.getKey(), item.getValue());
//		});

		String response = "";
		try {
			int statusCode = httpClient.executeMethod(getMethod);
			if (statusCode != HttpStatus.SC_OK) {
				LOGGER.error("请求出错: " + getMethod.getStatusLine());
				paramsMap.put("sysCode","2");//网络错误
				paramsMap.put("sysMsg","请求服务失败"+statusCode );//
				return  response;
			}
//			Arrays.stream(getMethod.getResponseHeaders()).forEach(item->{
//				log.info(item.getName() + "------------ " + item.getValue());
//			});
			byte[] responseBody = getMethod.getResponseBody();
			response = new String(responseBody, "UTF-8");
			paramsMap.put("sysCode","0");//成功
			paramsMap.put("sysMsg","请求成功");//
			LOGGER.info("响应参数:" + response);
		} catch (HttpException e) {
			paramsMap.put("sysCode","2");//网络错误
			paramsMap.put("sysMsg","请求服务失败,请检查输入的URL");//
			LOGGER.error("请检查输入的URL!");
			e.printStackTrace();
		} catch (IOException e) {
			paramsMap.put("sysCode","2");//网络错误
			paramsMap.put("sysMsg","请求服务失败,发生网络异常");//
			LOGGER.error(e.getMessage());
			e.printStackTrace();
		} catch (Exception e) {
			paramsMap.put("sysCode","2");//网络错误
			paramsMap.put("sysMsg","请求服务失败,发生异常");//
			LOGGER.error(e.getMessage());
			e.printStackTrace();
		}
		finally {
			getMethod.releaseConnection();
		}
		return response;
	}
	public static String commmonHttpPost(Map<String, String> paramsMap, String url,String queryString) {

		paramsMap.put("sysCode", "1");//失败
		paramsMap.put("sysMsg", "未知错误");//失败
		HttpClient httpClient = new HttpClient(HttpClientUtil.connectionManager);
		httpClient.getHttpConnectionManager().getParams().setConnectionTimeout(defaultConnectionTimeout);
		httpClient.getHttpConnectionManager().getParams().setSoTimeout(defaultSoTimeout);
		httpClient.getParams().setConnectionManagerTimeout(defaultHttpConnectionManagerTimeout);
		LOGGER.info(url);
		LOGGER.info("请求url：" + url + ",请求参数：" + queryString);
		PostMethod postMethod = new PostMethod(url);
		String response = "";
		try {
			//postMethod.addRequestHeader("Accept-Encoding","gzip, deflate, br");
			//postMethod.addRequestHeader("Connection","'keep-alive");

			postMethod.setRequestEntity(new StringRequestEntity(queryString, "text/xml", "utf-8"));
			int statusCode = httpClient.executeMethod(postMethod);
			if (statusCode != HttpStatus.SC_OK) {
				LOGGER.error("请求出错: " + postMethod.getStatusLine());
				paramsMap.put("sysCode", "2");//网络错误
				paramsMap.put("sysMsg", "请求服务失败" + statusCode);//
				return response;
			}

			InputStream resStream = postMethod.getResponseBodyAsStream();
			response = new String(inputStream2String(resStream));
			paramsMap.put("sysCode", "0");//成功
			paramsMap.put("sysMsg", "请求成功");//
			LOGGER.info("响应参数:" + response);

		} catch (HttpException e) {
			paramsMap.put("sysCode", "2");//网络错误
			paramsMap.put("sysMsg", "请求服务失败,请检查输入的URL");//
			LOGGER.error("请检查输入的URL!");
			e.printStackTrace();
		} catch (IOException e) {
			paramsMap.put("sysCode", "2");//网络错误
			paramsMap.put("sysMsg", "请求服务失败,发生网络异常");//
			LOGGER.error(e.getMessage());
			e.printStackTrace();
		} catch (Exception e) {
			paramsMap.put("sysCode", "2");//网络错误
			paramsMap.put("sysMsg", "请求服务失败,发生异常");//
			LOGGER.error(e.getMessage());
			e.printStackTrace();
		} finally {
			postMethod.releaseConnection();
		}
		return response;

	}
	
	public static String commmonHttpPostJson(Map<String, String> paramsMap, String url,String queryString) {

		paramsMap.put("sysCode", "1");//失败
		paramsMap.put("sysMsg", "未知错误");//失败
		HttpClient httpClient = new HttpClient(HttpClientUtil.connectionManager);
		httpClient.getHttpConnectionManager().getParams().setConnectionTimeout(defaultConnectionTimeout);
		httpClient.getHttpConnectionManager().getParams().setSoTimeout(defaultSoTimeout);
		httpClient.getParams().setConnectionManagerTimeout(defaultHttpConnectionManagerTimeout);
		LOGGER.info(url);
		LOGGER.info("请求url：" + url + ",请求参数：" + queryString);
		PostMethod postMethod = new PostMethod(url);
		String response = "";
		try {
			//postMethod.addRequestHeader("Accept-Encoding","gzip, deflate, br");
			//postMethod.addRequestHeader("Connection","'keep-alive");

			postMethod.setRequestEntity(new StringRequestEntity(queryString, "application/json", "utf-8"));
			int statusCode = httpClient.executeMethod(postMethod);
			if (statusCode != HttpStatus.SC_OK) {
				LOGGER.error("请求出错: " + postMethod.getStatusLine());
				paramsMap.put("sysCode", "2");//网络错误
				paramsMap.put("sysMsg", "请求服务失败" + statusCode);//
				return response;
			}

			InputStream resStream = postMethod.getResponseBodyAsStream();
			response = new String(inputStream2String(resStream));
			paramsMap.put("sysCode", "0");//成功
			paramsMap.put("sysMsg", "请求成功");//
			LOGGER.info("响应参数:" + response);

		} catch (ConnectException e) {
			paramsMap.put("sysCode", "2"); // 客户端异常
			paramsMap.put("sysMsg", "客户端异常，请稍后再试");//
			LOGGER.error("客户端异常，请稍后再试!");
			e.printStackTrace();
		} catch (HttpException e) {
			paramsMap.put("sysCode", "2");//网络错误
			paramsMap.put("sysMsg", "请求服务失败,请检查输入的URL");//
			LOGGER.error("请检查输入的URL!");
			e.printStackTrace();
		} catch (IOException e) {
			paramsMap.put("sysCode", "2");//网络错误
			paramsMap.put("sysMsg", "请求服务失败,发生网络异常");//
			LOGGER.error(e.getMessage());
			e.printStackTrace();
		} catch (Exception e) {
			paramsMap.put("sysCode", "2");//网络错误
			paramsMap.put("sysMsg", "请求服务失败,发生异常");//
			LOGGER.error(e.getMessage());
			e.printStackTrace();
		} finally {
			postMethod.releaseConnection();
		}
		return response;

	}

	public static String commmonHttpPost(Map<String, String> paramsMap, String url,HashMap<String, String> bodyMap,String traceID,String groupID,String shopID) {
		paramsMap.put("code", "1"); //失败
		paramsMap.put("msg", "未知错误"); //失败
		String result = "";
		try {
			PostMethod postMethod = null;
			postMethod = new PostMethod(url);
			postMethod.setRequestHeader("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");
			// 参数设置，需要注意的就是里边不能传NULL，要传空字符串

			int size = bodyMap.size();
			int index = 0;
			NameValuePair[] body = new NameValuePair[size];
			for (Map.Entry<String, String> bodyItem : bodyMap.entrySet()) {
				body[index++] = new NameValuePair(bodyItem.getKey(), bodyItem.getValue());
			}
			postMethod.setRequestBody(body);
			postMethod.setRequestHeader("traceID",traceID);
			postMethod.setRequestHeader("groupID",groupID);
			postMethod.setRequestHeader("shopID",shopID);

			org.apache.commons.httpclient.HttpClient httpClient = new org.apache.commons.httpclient.HttpClient();
			httpClient.getHttpConnectionManager().getParams().setConnectionTimeout(defaultConnectionTimeout);
			httpClient.getHttpConnectionManager().getParams().setSoTimeout(defaultSoTimeout);
			httpClient.getParams().setConnectionManagerTimeout(defaultHttpConnectionManagerTimeout);
			httpClient.executeMethod(postMethod); // 执行POST方法
			result = postMethod.getResponseBodyAsString();
			LOGGER.info("响应参数:" + result);
			paramsMap.put("code", "0");
			paramsMap.put("msg", "成功");
			return result;
		} catch (SocketTimeoutException e) {
			LOGGER.error("请求超时", e);
			e.printStackTrace();
			paramsMap.put("code", "1");
			paramsMap.put("msg", "请求超时");
		} catch (ConnectException e) {
			LOGGER.error("连接超时", e);
			e.printStackTrace();
			paramsMap.put("code", "1");
			paramsMap.put("msg", "连接超时");
		} catch (Exception e) {
			LOGGER.info("发送 POST 请求出现异常！" + e);
			e.printStackTrace();
		}
		return result;

	}

	public static String commmonHttpPostChangYi(Map<String, String> paramsMap, String postURL, HashMap<String, String> bodyMap) {
		paramsMap.put("code", "1"); //失败
		paramsMap.put("msg", "未知错误"); //失败
		String result = "";
		try {
			PostMethod postMethod = null;
			postMethod = new PostMethod(postURL);
			postMethod.setRequestHeader("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");
			// 参数设置，需要注意的就是里边不能传NULL，要传空字符串

			int size = bodyMap.size();
			int index = 0;
			NameValuePair[] body = new NameValuePair[size];
			for (Map.Entry<String, String> bodyItem : bodyMap.entrySet()) {
				body[index++] = new NameValuePair(bodyItem.getKey(), bodyItem.getValue());
			}
			postMethod.setRequestBody(body);

			org.apache.commons.httpclient.HttpClient httpClient = new org.apache.commons.httpclient.HttpClient();
			httpClient.getHttpConnectionManager().getParams().setConnectionTimeout(defaultConnectionTimeout);
			httpClient.getHttpConnectionManager().getParams().setSoTimeout(defaultSoTimeout);
			httpClient.getParams().setConnectionManagerTimeout(defaultHttpConnectionManagerTimeout);
			httpClient.executeMethod(postMethod); // 执行POST方法
			result = postMethod.getResponseBodyAsString();
			LOGGER.info("响应参数:" + result);
			paramsMap.put("code", "0");
			paramsMap.put("msg", "成功");
			return result;
		} catch (SocketTimeoutException e) {
			LOGGER.error("请求超时", e);
			e.printStackTrace();
			paramsMap.put("code", "1");
			paramsMap.put("msg", "请求超时");
		} catch (ConnectException e) {
			LOGGER.error("连接超时", e);
			e.printStackTrace();
			paramsMap.put("code", "1");
			paramsMap.put("msg", "连接超时");
		} catch (Exception e) {
			LOGGER.info("发送 POST 请求出现异常！" + e);
			e.printStackTrace();
		}
		return result;

	}
	
	public static String commmonHttpPostChangYi1(Map<String, String> paramsMap, String postURL, String queryString) {
		paramsMap.put("code", "1"); //失败
		paramsMap.put("msg", "未知错误"); //失败
		String result = "";
		try {
			PostMethod postMethod = new PostMethod(postURL);
			postMethod.addRequestHeader("Content-Type","application/x-www-form-urlencoded;charset=utf-8");
//			postMethod.setRequestHeader("Content-Type","application/x-www-form-urlencoded;charset=utf-8");
			// 参数设置，需要注意的就是里边不能传NULL，要传空字符串

//			int size = bodyMap.size();
//			int index = 0;
//			NameValuePair[] body = new NameValuePair[size];
//			for (Map.Entry<String, String> bodyItem : bodyMap.entrySet()) {
//				body[index++] = new NameValuePair(bodyItem.getKey(), bodyItem.getValue());
//			}
//			postMethod.setRequestBody(body);
			postMethod.setRequestEntity(new StringRequestEntity(queryString, "application/json", "utf-8"));
//			org.apache.commons.httpclient.HttpClient httpClient = new org.apache.commons.httpclient.HttpClient();
			HttpClient httpClient = new HttpClient(HttpClientUtil.connectionManager);
			httpClient.getHttpConnectionManager().getParams().setConnectionTimeout(defaultConnectionTimeout);
			httpClient.getHttpConnectionManager().getParams().setSoTimeout(defaultSoTimeout);
			httpClient.getParams().setConnectionManagerTimeout(defaultHttpConnectionManagerTimeout);
			
			httpClient.executeMethod(postMethod); // 执行POST方法
			result = postMethod.getResponseBodyAsString();
			LOGGER.info("响应参数:" + result);
			paramsMap.put("code", "0");
			paramsMap.put("msg", "成功");
			return result;
		} catch (SocketTimeoutException e) {
			LOGGER.error("请求超时", e);
			e.printStackTrace();
			paramsMap.put("code", "1");
			paramsMap.put("msg", "请求超时");
		} catch (ConnectException e) {
			LOGGER.error("连接超时", e);
			e.printStackTrace();
			paramsMap.put("code", "1");
			paramsMap.put("msg", "连接超时");
		} catch (Exception e) {
			LOGGER.info("发送 POST 请求出现异常！" + e);
			e.printStackTrace();
		}
		return result;

	}

	public static String commmonHttpPostJson(Map<String, String> paramsMap, String url,String queryString, String signKey) {
		paramsMap.put("sysCode", "1");//失败
		paramsMap.put("sysMsg", "未知错误");//失败
		HttpClient httpClient = new HttpClient(HttpClientUtil.connectionManager);
		httpClient.getHttpConnectionManager().getParams().setConnectionTimeout(defaultConnectionTimeout);
		httpClient.getHttpConnectionManager().getParams().setSoTimeout(defaultSoTimeout);
		httpClient.getParams().setConnectionManagerTimeout(defaultHttpConnectionManagerTimeout);
		LOGGER.info(url);
		LOGGER.info("请求url：" + url + ",请求参数：" + queryString);
		PostMethod postMethod = new PostMethod(url);
		String response = "";
		try {
			postMethod.addRequestHeader("signKey", signKey);
			postMethod.setRequestEntity(new StringRequestEntity(queryString, "application/json", "utf-8"));
			int statusCode = httpClient.executeMethod(postMethod);
			if (statusCode != HttpStatus.SC_OK) {
				LOGGER.error("请求出错: " + postMethod.getStatusLine());
				paramsMap.put("sysCode", "2");//网络错误
				paramsMap.put("sysMsg", "请求服务失败" + statusCode);//
				return response;
			}

			InputStream resStream = postMethod.getResponseBodyAsStream();
			response = new String(inputStream2String(resStream));
			paramsMap.put("sysCode", "0");//成功
			paramsMap.put("sysMsg", "请求成功");//
			LOGGER.info("响应参数:" + response);

		} catch (ConnectException e) {
			paramsMap.put("sysCode", "2"); // 客户端异常
			paramsMap.put("sysMsg", "客户端异常，请稍后再试");//
			LOGGER.error("客户端异常，请稍后再试!");
			e.printStackTrace();
		} catch (HttpException e) {
			paramsMap.put("sysCode", "2");//网络错误
			paramsMap.put("sysMsg", "请求服务失败,请检查输入的URL");//
			LOGGER.error("请检查输入的URL!");
			e.printStackTrace();
		} catch (IOException e) {
			paramsMap.put("sysCode", "2");//网络错误
			paramsMap.put("sysMsg", "请求服务失败,发生网络异常");//
			LOGGER.error(e.getMessage());
			e.printStackTrace();
		} catch (Exception e) {
			paramsMap.put("sysCode", "2");//网络错误
			paramsMap.put("sysMsg", "请求服务失败,发生异常");//
			LOGGER.error(e.getMessage());
			e.printStackTrace();
		} finally {
			postMethod.releaseConnection();
		}
		return response;

	}

	public static String commmonHttpPostJson(Map<String, String> paramsMap, String url,String queryString, String headerKey, String headerValue) {
		paramsMap.put("sysCode", "1");//失败
		paramsMap.put("sysMsg", "未知错误");//失败
		HttpClient httpClient = new HttpClient(HttpClientUtil.connectionManager);
		httpClient.getHttpConnectionManager().getParams().setConnectionTimeout(defaultConnectionTimeout);
		httpClient.getHttpConnectionManager().getParams().setSoTimeout(defaultSoTimeout);
		httpClient.getParams().setConnectionManagerTimeout(defaultHttpConnectionManagerTimeout);
		LOGGER.info(url);
		LOGGER.info("请求url：" + url + ",请求参数：" + queryString);
		PostMethod postMethod = new PostMethod(url);
		String response = "";
		try {
			postMethod.addRequestHeader(headerKey, headerValue);
			postMethod.setRequestEntity(new StringRequestEntity(queryString, "application/json", "utf-8"));
			int statusCode = httpClient.executeMethod(postMethod);
			if (statusCode != HttpStatus.SC_OK) {
				LOGGER.error("请求出错: " + postMethod.getStatusLine());
				paramsMap.put("sysCode", "2");//网络错误
				paramsMap.put("sysMsg", "请求服务失败" + statusCode);//
				return response;
			}

			InputStream resStream = postMethod.getResponseBodyAsStream();
			response = new String(inputStream2String(resStream));
			paramsMap.put("sysCode", "0");//成功
			paramsMap.put("sysMsg", "请求成功");//
			LOGGER.info("响应参数:" + response);

		} catch (ConnectException e) {
			paramsMap.put("sysCode", "2"); // 客户端异常
			paramsMap.put("sysMsg", "客户端异常，请稍后再试");//
			LOGGER.error("客户端异常，请稍后再试!");
			e.printStackTrace();
		} catch (HttpException e) {
			paramsMap.put("sysCode", "2");//网络错误
			paramsMap.put("sysMsg", "请求服务失败,请检查输入的URL");//
			LOGGER.error("请检查输入的URL!");
			e.printStackTrace();
		} catch (IOException e) {
			paramsMap.put("sysCode", "2");//网络错误
			paramsMap.put("sysMsg", "请求服务失败,发生网络异常");//
			LOGGER.error(e.getMessage());
			e.printStackTrace();
		} catch (Exception e) {
			paramsMap.put("sysCode", "2");//网络错误
			paramsMap.put("sysMsg", "请求服务失败,发生异常");//
			LOGGER.error(e.getMessage());
			e.printStackTrace();
		} finally {
			postMethod.releaseConnection();
		}
		return response;

	}

	public static String commmonHttpPostECO(Map<String, String> paramsMap, String postURL, Map<String, String> bodyMap) {
		paramsMap.put("code", "1"); //失败
		paramsMap.put("msg", "未知错误"); //失败
		String result = "";
		try {
			PostMethod postMethod = null;
			postMethod = new PostMethod(postURL);
			postMethod.setRequestHeader("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");
			// 参数设置，需要注意的就是里边不能传NULL，要传空字符串

			int size = bodyMap.size();
			int index = 0;
			NameValuePair[] body = new NameValuePair[size];
			for (Map.Entry<String, String> bodyItem : bodyMap.entrySet()) {
				body[index++] = new NameValuePair(bodyItem.getKey(), bodyItem.getValue());
			}
			postMethod.setRequestBody(body);

			org.apache.commons.httpclient.HttpClient httpClient = new org.apache.commons.httpclient.HttpClient();
			httpClient.getHttpConnectionManager().getParams().setConnectionTimeout(defaultConnectionTimeout);
			httpClient.getHttpConnectionManager().getParams().setSoTimeout(defaultSoTimeout);
			httpClient.getParams().setConnectionManagerTimeout(defaultHttpConnectionManagerTimeout);
			httpClient.executeMethod(postMethod); // 执行POST方法
			result = postMethod.getResponseBodyAsString();
			LOGGER.info("响应参数:" + result);
			paramsMap.put("code", "0");
			paramsMap.put("msg", "成功");
			return result;
		} catch (SocketTimeoutException e) {
			LOGGER.error("请求超时", e);
			e.printStackTrace();
			paramsMap.put("code", "1");
			paramsMap.put("msg", "请求超时");
		} catch (ConnectException e) {
			LOGGER.error("连接超时", e);
			e.printStackTrace();
			paramsMap.put("code", "1");
			paramsMap.put("msg", "连接超时");
		} catch (Exception e) {
			LOGGER.info("发送 POST 请求出现异常！" + e);
			e.printStackTrace();
		}
		return result;

	}
	
	
	
	
	
	
	public static void main(String[] args) {
		//测试commmonHttpGet
		Map<String, String> paramsMap = new HashMap<>();
		String responseStr = commmonHttpGet(paramsMap,"http://***************:8086/pos.asmx","/PostSales?strCallUserCode=test&strCallPassword=123456&strStoreCode=21001L210024&strSalesDocNo=350_990000000031_0110182707&strVipCode=&strRemark=&strSalesDate_YYYYMMDD=20240110&strSalesTime_HHMISS=182707&strTenderCode=CH&strType=SA&strItems={21001L2100241,1,19.0}");
		System.out.println("paramsMap:"+paramsMap.toString());
		System.out.println("responseStr:" + responseStr);
		JSONObject responseJson = null;
		int  status = 1;
		if("0".equals(paramsMap.get("sysCode"))) {
			try {
				responseJson = JSONObject.fromObject(responseStr);
				if(responseJson.getJSONObject("pay_response").optString("state").equals("1")){
					status = 0 ;
					LOGGER.info("业务返回成功");
				}
				else{
					status = 1 ;
					LOGGER.error("业务返回失败："+ responseJson.getJSONObject("pay_response").optString("msg"));
				}
			} catch (Exception e) {
				status = 1 ;
				LOGGER.error("解析返回json失败");
				e.printStackTrace();
			}
		}
		else if ("2".equals(paramsMap.get("sysCode"))){
			status = 2 ;
		}
		else if ("1".equals(paramsMap.get("sysCode"))){
			status = 1 ;
		}
		else{
			status = 1 ;
		}

		LOGGER.info("status:"+status +",responseJson:" + responseJson);

//		String url = "http://boh.test.meishijia.com:9081/TzxCommunicate/CardServices/acewillMemberAction.action";
//		Map<String, String> reqMap = new HashMap<String, String>();
//		reqMap.put("organ_id", "");
//		reqMap.put("type", "querybycoupon");
//		String str = HttpClientUtil.postSSLUrlWithParams(url, reqMap);
//		System.out.println(str);
	}

	public static String sendPostRequestBySoapXml(String reqURL, String sendData) {
		return HttpClientUtil.sendPostRequestBySoapXml(reqURL, sendData, 120 * 1000, 120 * 1000);
	}

	public static String sendPostRequestBySoapXml(String reqURL, String sendData, int socketTimeout, int connTimeout) {
		String responseContent = null;
		CloseableHttpClient httpClient = HttpClients.createDefault();
		HttpPost httpPost = new HttpPost(reqURL);
		httpPost.setHeader(HttpHeaders.CONTENT_TYPE, "application/soap+xml;charset=UTF-8");
		try {
			httpPost.setEntity(new StringEntity(sendData, "UTF-8"));
			HttpResponse response = httpClient.execute(httpPost);
			HttpEntity entity = response.getEntity();
			if (entity != null) {
				responseContent = EntityUtils.toString(entity, "UTF-8");
			}
		} catch (Exception e) {
			e.printStackTrace();
			LOGGER.error("与[" + reqURL + "]通信过程中发生异常,堆栈信息如下", e);
		} finally {
			try {
				httpClient.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return responseContent;
	}
}