package com.tzx.publics.util;

import java.io.*;
import java.lang.reflect.Array;
import java.net.SocketTimeoutException;
import java.net.URL;
import java.net.URLConnection;
import java.security.MessageDigest;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import org.apache.commons.codec.CharEncoding;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.druid.util.Base64;

import net.sf.json.JSONObject;

public class Util {
    private final static Logger LOGGER = LoggerFactory.getLogger(Util.class);

    public static String jsonPostRequest(String url,String sid, String param) {
        PrintWriter out = null;
        BufferedReader in = null;
        String result = "";
        try {
            LOGGER.info("url:" + url + "  request  " + param);
            URL realUrl = new URL(url);
            URLConnection conn = realUrl.openConnection();
            conn.setRequestProperty("Content-Type", "application/json");
            LOGGER.info("sid="+sid);
            if(StringUtils.isNotEmpty(sid)){
                conn.setRequestProperty("sid",sid);
            }
            // 设置连接主机超时（单位：毫秒）
            conn.setConnectTimeout(15000);
            // 设置从主机读取数据超时（单位：毫秒）
            conn.setReadTimeout(15000);
            conn.setDoOutput(true);
            conn.setDoInput(true);
            out = new PrintWriter(conn.getOutputStream());
            out.print(param);
            out.flush();
            in = new BufferedReader(new InputStreamReader(conn.getInputStream(), "UTF-8"));
            String line;
            while ((line = in.readLine()) != null) {
                result += line;
            }
            LOGGER.info("url:" + url + "  response  " + result);
        } catch (SocketTimeoutException e) {
            e.printStackTrace();
            LOGGER.error("Ignore this exception", e);
            return Const.CONNECT_TIMEOUT;
        } catch (Exception e) {
            e.printStackTrace();
            LOGGER.error("Ignore this exception", e);
            return Const.CONNECT_ERROR;
        } finally {
            try {
                if (out != null) {
                    out.close();
                }
                if (in != null) {
                    in.close();
                }
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }
        return result;

    }

    public static String jsonPostRequest(String url, String param) {
        return jsonPostRequest (url,null,param);
    }
    
    public static String jsonPostRequestQm(String url, String param) {
        PrintWriter out = null;
        BufferedReader in = null;
        String result = "";
        try {
            LOGGER.info("url:" + url + "  request  " + param);
            URL realUrl = new URL(url);
            URLConnection conn = realUrl.openConnection();
            conn.setRequestProperty("Content-Type", "application/json");
            // 设置连接主机超时（单位：毫秒）
            conn.setConnectTimeout(15000);
            // 设置从主机读取数据超时（单位：毫秒）
            conn.setReadTimeout(15000);
            conn.setDoOutput(true);
            conn.setDoInput(true);
            out = new PrintWriter(conn.getOutputStream());
            out.print(param);
            out.flush();
            in = new BufferedReader(new InputStreamReader(conn.getInputStream(), "UTF-8"));
            String line;
            while ((line = in.readLine()) != null) {
                result += line;
            }
            LOGGER.info("url:" + url + "  response  " + result);
            
        } catch (SocketTimeoutException e) {
        	e.printStackTrace();
            LOGGER.error("Ignore this exception", e);
			return Const.CONNECT_TIMEOUT;
        } catch (Exception e) {
            e.printStackTrace();
            LOGGER.error("Ignore this exception", e);
            return Const.CONNECT_ERROR;
        } finally {
            try {
                if (out != null) {
                    out.close();
                }
                if (in != null) {
                    in.close();
                }
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }
        return result;
    }

    /**
     * 小程序接口生成sign签名规则
     *
     * @param appkey
     * @param out_order_id
     * @return
     */
    public static String miniappSign(String appkey, String out_order_id) {
        LOGGER.info("appkey="+appkey+",out_order_id="+out_order_id);
        String md5Key = MD5Util.md5(appkey).toLowerCase();
        JSONObject outOrderId = new JSONObject();
        outOrderId.put("outorderid", out_order_id);
        String jsonStr = outOrderId.toString();
        String signStr = md5Key + "#" + jsonStr;
        return Base64.byteArrayToBase64(signStr.getBytes());
    }

    /**
     * 退款
     *
     * @param url
     * @param shopKey
     * @param out_order_id
     * @param sign
     * @return
     */
    public static String refund(String url, String shopKey, String sid, String out_order_id, String sign) {
        JSONObject param = new JSONObject();
        param.put("shopKey", shopKey);
        param.put("out_order_id", out_order_id);
        param.put("sign", sign);
        return jsonPostRequest(url,sid, param.toString());
    }
    
    public static String refundQm(String url, String callbackShopName, String out_order_id, String sign) {
        JSONObject param = new JSONObject();
        param.put("callbackShopName", callbackShopName);
        param.put("out_order_id", out_order_id);
        param.put("sign", sign);
        return jsonPostRequestQm(url, param.toString());
    }
    
	public static String md5(String s, String input_charset) {
        
        try {
        	byte[] defaultBytes = s.getBytes(input_charset);
            StringBuffer hexString = new StringBuffer();
            MessageDigest algorithm = MessageDigest.getInstance("MD5");
            algorithm.reset();
            algorithm.update(defaultBytes);
            byte messageDigest[] = algorithm.digest();
            for (byte aMessageDigest : messageDigest) {
                String hex = Integer.toHexString(0xFF & aMessageDigest);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            s = hexString + "";
            return hexString.toString();
        } catch (Exception ignored) {
        	throw new IllegalArgumentException("签名失败");
        }
        
    }
	
    /**
     * 签名字符串
     * @param text 需要签名的字符串
     * @param input_charset 编码格式
     * @return 签名结果
     */
    public static String ZSSign(String text, String input_charset) {
        return Util.md5(text,input_charset);
    }

    public static String sendGet(String url, String param) {
        String result = "";
        BufferedReader in = null;
        try {
            String urlNameString = url + "?" + param;
            URL realUrl = new URL(urlNameString);
            URLConnection connection = realUrl.openConnection();
            connection.setRequestProperty("accept", "*/*");
            connection.setRequestProperty("connection", "Keep-Alive");
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            connection.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            connection.connect();
            Map<String, List<String>> map = connection.getHeaderFields();

            for (String key : map.keySet()) {
                System.out.println(key + "--->" + map.get(key));
            }
            //接收核心返回过来的数据 xml  需要解析
            in = new BufferedReader(new InputStreamReader(connection.getInputStream(),"utf-8"));
            String line;
            while ((line = in.readLine()) != null) {
                result += line;
            }
        } catch (Exception e) {
            e.printStackTrace();
            LOGGER.error("Ignore this exception", e);
        }finally {
            try {
                if (in != null) {
                    in.close();
                }
            } catch (Exception e2) {
                e2.printStackTrace();
            }
        }
        return result;
    }
    
	@SuppressWarnings("rawtypes")
	public static boolean isNullOrEmpty(Object obj) {
		boolean isEmpty = false;
		if (obj == null) {
			isEmpty = true;
		} else if (obj instanceof String) {
			isEmpty = ((String) obj).trim().isEmpty();
		} else if (obj instanceof Collection) {
			isEmpty = (((Collection) obj).size() == 0);
		} else if (obj instanceof Map) {
			isEmpty = ((Map) obj).size() == 0;
		} else if (obj.getClass().isArray()) {
			isEmpty = Array.getLength(obj) == 0;
		}
		return isEmpty;
	}

    /**
     * 获取主板序列号
     * wmic baseboard get serialnumber
     */
    public static String getMotherboardSN() {
        String result = "";
        try {
            File file = File.createTempFile("realhowto", ".vbs");
            file.deleteOnExit();
            FileWriter fw = new java.io.FileWriter(file);

            String vbs = "Set objWMIService = GetObject(\"winmgmts:\\\\.\\root\\cimv2\")\n"
                    + "Set colItems = objWMIService.ExecQuery _ \n"
                    + " (\"Select * from Win32_BaseBoard\") \n"
                    + "For Each objItem in colItems \n"
                    + " Wscript.Echo objItem.SerialNumber \n"
                    + " exit for ' do the first cpu only! \n" + "Next \n";

            fw.write(vbs);
            fw.close();
            Process p = Runtime.getRuntime().exec("cscript //NoLogo " + file.getPath());
            BufferedReader input = new BufferedReader(new InputStreamReader(p.getInputStream()));
            String line;
            while ((line = input.readLine()) != null) {
                result += line;
            }
            input.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result.trim();
    }

    public static void main(String[] args) {
        String result = getMotherboardSN();
        System.out.println(result);
    }
}