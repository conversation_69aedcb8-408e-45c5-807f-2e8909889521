<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace=".mapper.ErpIngredientInfoMapper">

    <resultMap id="BaseResultMap" type=".domain.ErpIngredientInfo">
            <id property="id" column="id" jdbcType="OTHER"/>
            <result property="ingredientName" column="ingredient_name" jdbcType="VARCHAR"/>
            <result property="ingredientCode" column="ingredient_code" jdbcType="VARCHAR"/>
            <result property="optName" column="opt_name" jdbcType="VARCHAR"/>
            <result property="lastUploadTime" column="last_upload_time" jdbcType="TIMESTAMP"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="ingredientSort" column="ingredient_sort" jdbcType="OTHER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,ingredient_name,ingredient_code,
        opt_name,last_upload_time,status,
        ingredient_sort
    </sql>
</mapper>
