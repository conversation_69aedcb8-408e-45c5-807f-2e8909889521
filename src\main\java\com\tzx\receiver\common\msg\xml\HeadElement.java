package com.tzx.receiver.common.msg.xml;

import org.dom4j.Element;


/**
 * 
 * 数据包元素的消息头元素类。
 * 
 * <AUTHOR>
 * 
 */
public class HeadElement extends BaseDPElement {

	/**
	 * 构造一个 HeadElement 对象。
	 * 
	 */
	public HeadElement(DataPacketBuilder builder, Element currentNode) {
		super(builder, currentNode);
	}

	/**
	 * 返回此元素对象所在的文档路径。这里返回的是<code>HEAD</code>
	 * 
	 */
	public static String getCurrentPath() {
		return DPC.SHeadNodeStr;
	}

	/**
	 * 获取此元素对象下的<code>Version</code>子元素的值。
	 * 
	 * @return 如果子元素不存在，则返回<code>""</code>值，否则返回子元素的值。
	 */
	public String getVersion() {
		Element childNode = builder.findNode(currentNode, DPC.SHeadVersionNodeStr);
		if (childNode != null) {
			return builder.readString(childNode, "");
		} else {
			return "";
		}
	}

	/**
	 * 获取此元素对象下的<code>SenderID</code>子元素的值。
	 * 
	 * @return 如果子元素不存在，则返回<code>""</code>值，否则返回子元素的值。
	 */
	public String getSenderID() {
		Element childNode = builder.findNode(currentNode, DPC.SHeadSenderIDNodeStr);
		if (childNode != null) {
			return builder.readString(childNode, "");
		} else {
			return "";
		}
	}

	/**
	 * 获取此元素对象下的<code>ReceiverID</code>子元素的值。
	 * 
	 * @return 如果子元素不存在，则返回<code>""</code>值，否则返回子元素的值。
	 */
	public String getReceiverID() {
		Element childNode = builder
				.findNode(currentNode, DPC.SHeadReceiverIDNodeStr);
		if (childNode != null) {
			return builder.readString(childNode, "");
		} else {
			return "";
		}
	}

	/**
	 * 获取此元素对象下的<code>OperatorID</code>子元素的值。
	 * 
	 * @return 如果子元素不存在，则返回<code>""</code>值，否则返回子元素的值。
	 */
	public String getOperatorID() {
		Element childNode = builder
				.findNode(currentNode, DPC.SHeadOperatorIDNodeStr);
		if (childNode != null) {
			return builder.readString(childNode, "");
		} else {
			return "";
		}
	}

	/**
	 * 设置此元素对象下的<code>Version</code>子元素的值。
	 * 
	 * @param value
	 *            指定子元素的新值。
	 */
	public void setVersion(String value) {
		Element childNode = builder.getNode(currentNode, DPC.SHeadVersionNodeStr);
		builder.writeString(childNode, value);
	}

	/**
	 * 设置此元素对象下的<code>SenderID</code>子元素的值。
	 * 
	 * @param value
	 *            指定子元素的新值。
	 */
	public void setSenderID(String value) {
		Element childNode = builder.getNode(currentNode, DPC.SHeadSenderIDNodeStr);
		builder.writeString(childNode, value);
	}

	/**
	 * 设置此元素对象下的<code>ReceiverID</code>子元素的值。
	 * 
	 * @param value
	 *            指定子元素的新值。
	 */
	public void setReceiverID(String value) {
		Element childNode = builder.getNode(currentNode, DPC.SHeadReceiverIDNodeStr);
		builder.writeString(childNode, value);
	}

	/**
	 * 设置此元素对象下的<code>OperatorID</code>子元素的值。
	 * 
	 * @param value
	 *            指定子元素的新值。
	 */
	public void setOperatorID(String value) {
		Element childNode = builder.getNode(currentNode, DPC.SHeadOperatorIDNodeStr);
		builder.writeString(childNode, value);
	}

}
