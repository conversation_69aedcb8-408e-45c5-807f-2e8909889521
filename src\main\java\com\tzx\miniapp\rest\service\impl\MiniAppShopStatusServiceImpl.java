package com.tzx.miniapp.rest.service.impl;

import com.tzx.miniapp.common.Data;
import com.tzx.miniapp.rest.mapper.MiniAppFirstPayMapper;
import com.tzx.miniapp.rest.mapper.MiniAppShopStatusMapper;
import com.tzx.miniapp.rest.model.TsGgcsk;
import com.tzx.miniapp.rest.service.IMiniAppShopStatusService;
import com.tzx.publics.util.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Service
public class MiniAppShopStatusServiceImpl implements IMiniAppShopStatusService {
	private final static Logger LOGGER = LoggerFactory.getLogger(MiniAppShopStatusServiceImpl.class);

	@Autowired
	private MiniAppShopStatusMapper shopStatusMapper;
	
	@Autowired
	private MiniAppFirstPayMapper firstPayMapper;

	@Transactional
	public Data shopStatus() {
		Data data = new Data();
		try {
			// 查询报表日期
			Map<String, String> bbrqMap = shopStatusMapper.findBbrq();
			String bbrq = DateUtil.getNowDateYYDDMM();
			if (null != bbrqMap && bbrqMap.size() != 0) {
				bbrq = bbrqMap.get("bbrq");

				// 查询营业标志
				int start = shopStatusMapper.checkOpenStart(DateUtil.parseDate(bbrq));
				// 查询打烊标志
				int end = shopStatusMapper.checkOpenEnd(DateUtil.parseDate(bbrq));
				// 查询登录次数
				int ld = shopStatusMapper.loginDlcs(DateUtil.parseDate(bbrq));
				// 查询交班次数
				int lt = shopStatusMapper.loginTccs(DateUtil.parseDate(bbrq));
				// 是否正在交班
				TsGgcsk il = firstPayMapper.getGgcsToWs("ISLOGINOUTING");
				
				if(null != il && "Y".equals(il.getSdnr())){
					data.setSuccess(0);
					data.setMsg("门店正在盘点，请稍后再试！");
				} else if (0 == start) {
					data.setSuccess(0);
					data.setMsg("门店未营业！");
				} else if (0 < end) {
					data.setSuccess(0);
					data.setMsg("门店已打烊！");
				} else if (ld <= lt) {
					data.setSuccess(0);
					data.setMsg("员工未登录！");
				} else if (checkDysj(bbrq) > 0) {
					data.setSuccess(0);
					data.setMsg("系统日期已经大于营业日期，请做打烊后再进行当前操作！");
				} else {
					data.setSuccess(1);
					data.setMsg("正常营业！");
				}
			} else {
				data.setSuccess(0);
				data.setMsg("查询报表日期失败！");
			}
			return data;
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			data.setSuccess(0);
			data.setMsg("系统错误！");
			data.setData(new HashMap<String, Object>());
			return data;
		}
	}
	
	public int checkDysj(String bbrq) {
		// 查询是否二十四小时营业
		// TODO：这个参数小程序读取的是公共参数表，而POS读取的是配送机构设定表
		String sys24yy = shopStatusMapper.getSys24yy();
		// 最晚打烊时间
		String dysjsz = shopStatusMapper.getDyxzsjd();
		Date bbrqD = DateUtil.parseDateAll(bbrq + " 00:00:00");
		if("Y".equals(sys24yy)){
			// 二十四小时模式，返回当天起始自然时间与当前时间的天数差
			int day = DateUtil.daysBetween(bbrqD, DateUtil.parseDateAll(DateUtil.getNowDateYYDDMM() + " 00:00:00"));
			return day;
		} else {
			if("".equals(dysjsz) || null == dysjsz){
				dysjsz = "00:00:00";
			}
			Date bbrqDy = DateUtil.parseDateAll(bbrq + " " + dysjsz);
			Date dysj = DateUtil.parseDateAll(DateUtil.getNowDateYYDDMMHHMMSS());
			// 非二十四小时模式，返回报表日期加上最晚打烊时间加上一天与当前时间的天数差
			int day = DateUtil.daysBetween(DateUtil.getPlusDay(bbrqDy, 1), dysj);
			return day;
		}
	}
}
