package com.tzx.commapi.rest.service.impl;

import com.tzx.commapi.common.CommApiException;
import com.tzx.commapi.common.Constant;
import com.tzx.commapi.rest.service.ICommApiService;
import com.tzx.commapi.rest.service.ISaleOutApiService;
import com.tzx.commapi.rest.vo.CommApiData;
import com.tzx.publics.listener.InitDataListener;
import com.tzx.publics.util.LockerFactory;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;

/**
 * Created by Zhouxh on 2020-01-17.
 * 此服务用于controller层和其他内部Service（eco外卖、小程序、app等等）层与实际沽清业务层之间的业务层
 * 提供统一的对外接口调用
 */
@Service
public class SaleOutApiService implements ISaleOutApiService {
    private final static Logger LOGGER = LoggerFactory.getLogger(SaleOutApiService.class);
    //通用api服务
    @Autowired
    private ICommApiService commApiService;
    //通用 锁接口
    @Autowired
    private LockerFactory lockerFactory;
    @Override
    public String SaleOutSet(CommApiData data, JSONObject jsonObject) {
        String methodTag = "set";
        Boolean IsLock = false;
        data.setCode(1);
        if(!(null!=InitDataListener.ggcsMap.get("ISREALTIMEDISHCOUNT") &&
                InitDataListener.ggcsMap.get("ISREALTIMEDISHCOUNT").equals("Y"))){
            data.setCode(0);
            data.setMsg("未启用实时沽清参数ISREALTIMEDISHCOUNT");
            return JSONObject.fromObject(data).toString();
        }
        ArrayList<String> arrayList = null;
        int lvRetryCount = 1;
        try {
            LOGGER.info("请求包文 {}",jsonObject.toString());
            arrayList = commApiService.GetSetList(jsonObject);
            //锁定重试次数
            for(int i = 1; i<= Constant.LOCK_RETRY_COUNT; i++) {
                lvRetryCount = i;
                IsLock = lockerFactory.getSaleOutLocker().Lock(arrayList, 1);
                LOGGER.info("response{} lockid {} Lock：{} ", methodTag, StringUtils.join(arrayList, ","), IsLock);
                if (IsLock) {
                    HashMap<String, String> inparam = new HashMap<String, String>();
                    commApiService.SetBefore(data, inparam, jsonObject);
                    if (data.getCode().equals(0)) {
                        data.setCode(1);
                        commApiService.SetProcess(data, inparam, jsonObject);
                        if (data.getCode().equals(0)) {
                            commApiService.SetAfter(data, inparam, jsonObject);
                        }
                    }
                    LOGGER.info("response{}：{}", methodTag, JSONObject.fromObject(data).toString());
                    return JSONObject.fromObject(data).toString();
                } else {
                    Thread.sleep(Constant.LOCK_RETRY_SLEEP_TIME);
                    data.setMsg("正在进行操作，请稍后重试！");
                    data.setCode(1);
                }
            }
            LOGGER.info("处理完毕,重试次数", lvRetryCount );
            return JSONObject.fromObject(data).toString();

        }
        catch(CommApiException comme){
            data.setMsg(comme.getMessage());
            data.setCode(1);
            LOGGER.info("response {}：CommApiException {}",methodTag,JSONObject.fromObject(data).toString());
            return JSONObject.fromObject(data).toString();
        }
        catch (Exception e) {
            e.printStackTrace();
            data.setMsg("内部错误");
            data.setCode(1);
            LOGGER.error("response {}：otherExcetion {}",methodTag,e.getMessage());
            return JSONObject.fromObject(data).toString();
        } finally {
            if(IsLock) {
                lockerFactory.getSaleOutLocker().UnLock(arrayList);
                LOGGER.info("response({}) lockerid {} unlock解锁成功",methodTag,StringUtils.join(arrayList,","));
            }
        }
    }

    @Override
    public String SaleOutDec(CommApiData data, JSONObject jsonObject) {
        String methodTag = "dec";
        data.setCode(1);
        if(!(null!=InitDataListener.ggcsMap.get("ISREALTIMEDISHCOUNT") &&
                InitDataListener.ggcsMap.get("ISREALTIMEDISHCOUNT").equals("Y"))){
            data.setCode(0);
            data.setMsg("未启用实时沽清参数ISREALTIMEDISHCOUNT");
            return JSONObject.fromObject(data).toString();
        }
        Boolean IsLock = false;
        int lvRetryCount = 0;
        ArrayList<String> arrayList = null;
        try {
            LOGGER.info("请求包文 {}",jsonObject.toString());
            arrayList = commApiService.GetDecList(jsonObject);
            //锁定重试次数
            for(int i = 1; i<= Constant.LOCK_RETRY_COUNT; i++) {
                lvRetryCount = i;
                IsLock = lockerFactory.getSaleOutLocker().Lock(arrayList, 1);
                LOGGER.info("response{} lockid {} Lock：{} ", methodTag, StringUtils.join(arrayList, ","), IsLock);
                if (IsLock) {
                    HashMap<String, String> inparam = new HashMap<String, String>();
                    commApiService.DecBefore(data, inparam, jsonObject);
                    if (data.getCode().equals(0)) {
                        data.setCode(1);
                        commApiService.DecProcess(data, inparam, jsonObject);
                        if (data.getCode().equals(0)) {
                            commApiService.DecAfter(data, inparam, jsonObject);
                        }
                    }
                    LOGGER.info("response{}：{}", methodTag, JSONObject.fromObject(data).toString());
                    return JSONObject.fromObject(data).toString();
                } else {
                    Thread.sleep(Constant.LOCK_RETRY_SLEEP_TIME);
                    data.setMsg("正在进行操作，请稍后重试！");
                    data.setCode(1);
                }
            }
            LOGGER.info("处理完毕,重试次数", lvRetryCount);
            return JSONObject.fromObject(data).toString();
        }
        catch(CommApiException comme){
            data.setMsg(comme.getMessage());
            data.setCode(1);
            LOGGER.info("response {}：CommApiException {}",methodTag,JSONObject.fromObject(data).toString());
            return JSONObject.fromObject(data).toString();
        }
        catch (Exception e) {
            e.printStackTrace();
            data.setMsg("内部错误");
            data.setCode(1);
            LOGGER.error("response {}：otherExcetion {}",methodTag,e.getMessage());
            return JSONObject.fromObject(data).toString();
        } finally {
            if(IsLock) {
                lockerFactory.getSaleOutLocker().UnLock(arrayList);
                LOGGER.info("response({}) lockerid {} unlock解锁成功",methodTag,StringUtils.join(arrayList,","));
            }
        }
    }

    @Override
    public String SaleOutAdd(CommApiData data, JSONObject jsonObject) {
        String methodTag = "add";
        data.setCode(1);
        if(!(null!=InitDataListener.ggcsMap.get("ISREALTIMEDISHCOUNT") &&
                InitDataListener.ggcsMap.get("ISREALTIMEDISHCOUNT").equals("Y"))){
            data.setCode(0);
            data.setMsg("未启用实时沽清参数ISREALTIMEDISHCOUNT");
            return JSONObject.fromObject(data).toString();
        }
        Boolean IsLock = false;
        ArrayList<String> arrayList = null;
        int lvRetryCount = 0;
        try {
            LOGGER.info("请求包文 {}",jsonObject.toString());
            arrayList = commApiService.GetAddList(jsonObject);
            //锁定重试次数
            for(int i = 1; i<= Constant.LOCK_RETRY_COUNT; i++) {
                lvRetryCount = i;
                IsLock = lockerFactory.getSaleOutLocker().Lock(arrayList, 1);
                LOGGER.info("response{} lockid {} Lock：{} ", methodTag, StringUtils.join(arrayList, ","), IsLock);
                if (IsLock) {
                    HashMap<String, String> inparam = new HashMap<String, String>();
                    commApiService.AddBefore(data, inparam, jsonObject);
                    if (data.getCode().equals(0)) {
                        data.setCode(1);
                        commApiService.AddProcess(data, inparam, jsonObject);
                        if (data.getCode().equals(0)) {
                            commApiService.AddAfter(data, inparam, jsonObject);
                        }
                    }
//                    Thread.sleep(Constant.LOCK_TEST_SLEEP_TIME);
                    LOGGER.info("response{}：{}", methodTag, JSONObject.fromObject(data).toString());
                    return JSONObject.fromObject(data).toString();
                } else {
                    LOGGER.info("锁定失败，准备进行第 {} 次重试锁定", i);
                    Thread.sleep(Constant.LOCK_RETRY_SLEEP_TIME);
                    data.setMsg("正在进行操作，请稍后重试！");
                    data.setCode(1);
                }
            }
            LOGGER.info("处理完毕,重试次数", lvRetryCount);
            return JSONObject.fromObject(data).toString();

        }
        catch(CommApiException comme){
            data.setMsg(comme.getMessage());
            data.setCode(1);
            LOGGER.info("response {}：CommApiException {}",methodTag,JSONObject.fromObject(data).toString());
            return JSONObject.fromObject(data).toString();
        }
        catch (Exception e) {
            e.printStackTrace();
            data.setMsg("内部错误");
            data.setCode(1);
            LOGGER.error("response {}：otherExcetion {}",methodTag,e.getMessage());
            return JSONObject.fromObject(data).toString();
        } finally {
            if(IsLock) {
                lockerFactory.getSaleOutLocker().UnLock(arrayList);
                LOGGER.info("response({}) lockerid {} unlock解锁成功",methodTag,StringUtils.join(arrayList,","));
            }
        }
    }
}
