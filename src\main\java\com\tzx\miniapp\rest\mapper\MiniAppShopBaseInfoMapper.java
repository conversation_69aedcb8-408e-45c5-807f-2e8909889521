package com.tzx.miniapp.rest.mapper;

import com.tzx.miniapp.rest.vo.ComboItems;
import com.tzx.miniapp.rest.vo.Dish;
import com.tzx.miniapp.rest.vo.DishKinds;
import com.tzx.miniapp.rest.vo.ItemInfoSpec;
import com.tzx.miniapp.rest.vo.MainDish;
import com.tzx.miniapp.rest.vo.Mandatory;
import com.tzx.miniapp.rest.vo.Marketing;
import com.tzx.miniapp.rest.vo.Optional;
import com.tzx.miniapp.rest.vo.Shops;
import com.tzx.miniapp.rest.vo.Taste;
import com.tzx.miniapp.rest.vo.Toppings;
import com.tzx.miniapp.rest.vo.ZsDishGroupDetailsEntity;
import com.tzx.miniapp.rest.vo.ZsDishGroupEntity;
import com.tzx.miniapp.rest.vo.ZsDishInfoEntity;
import com.tzx.miniapp.rest.vo.ZsDishPackDetailsEntity;
import com.tzx.miniapp.rest.vo.ZsDishTypeInfoEntity;
import com.tzx.publics.base.MyMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2018-07-18
 */

public interface MiniAppShopBaseInfoMapper extends MyMapper<Dish> {

	public List<Taste> findTasteData();

	public List<Dish> findDishData(@Param("cbh") String cbh);

	public Dish findDishInfo(@Param("cmid") int cmid);

	public Dish findDishInfoByDishNo(@Param("cmbh") String cmbh);

	public Shops findShopsData();

	public List<Marketing> findMarketingData();

	public List<DishKinds> findDishKindsData(@Param("cbh") String cbh);

	public List<MainDish> findMainDishData(@Param("xmid") int xmid);

	public List<MainDish> findMainDishDataExt();

	public List<Mandatory> findMandatoryDishData(@Param("xmid") int xmid);


	public List<Mandatory> findMandatoryDishDataExt();

	public List<ComboItems> findComboItemsData(@Param("fzid") int xmid, @Param("selnum") int selnum);

	/**
	 * 查询套餐分组下明细菜品 (菜品列表必须在 cbh指定的餐谱渠道中, 存在)
	 */
	public List<ComboItems> findComboItemsDataExt(@Param("cbh") String cbh);

	/**
	 * @Description: 查询桌位数量
	 * @param @return
	 * @return Integer
	 * @throws
	 * <AUTHOR>
	 * @email  <EMAIL>
	 * @date 2018-11-8
	 */
	public Integer findTableCount();
	
	// 查询菜品口味备注（新）
	public List<Taste> findItemTasteList();
	
	// 查询菜品口味备注（比新的还新）
	public List<Taste> findItemTasteListNew();
	
	/**
	 * @Description: 众赏，同步菜品数据
	 * <AUTHOR>
	 * @date 2019-06-17
	 */
	public List<ZsDishInfoEntity> findZsDishData(@Param("sid") String sid, @Param("cmsx") String cmsx);
	
	public List<ZsDishTypeInfoEntity> findZsDishKindsData(@Param("sid") String sid);

	public List<ZsDishGroupEntity> findZsDishGroupData(@Param("sid") String sid);
	
	public List<ZsDishPackDetailsEntity> findZsDishPackDetailsData(@Param("sid") String sid, @Param("xmid") int xmid);
	
	public List<ZsDishGroupDetailsEntity> findZsDishGroupDetailsData(@Param("sid") String sid, @Param("fzid") int fzid);
	
	public String getTthird(@Param("thirdtype") String thirdtype, @Param("thirdcode") String thirdcode);
	
	public List<ItemInfoSpec> findItemInfoSpecDataExt();
	
	public boolean pgHasTable(@Param("table") String table);

	List<Toppings> findToppingsData();

    public Integer getOmpIdByBohId(String name, Integer bohId);

	/**
	 * 查询微信渠道下, 非必选分组下的单品列表 (单品必须在餐谱单品列表中)
	 */
	List<Optional> selectOptionsItem(@Param("cbh") String cbh);
}