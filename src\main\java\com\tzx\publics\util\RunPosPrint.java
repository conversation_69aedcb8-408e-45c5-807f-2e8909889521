package com.tzx.publics.util;

import com.sun.jna.Native;
import com.tzx.miniapp.rest.mapper.MiniAppFirstPayMapper;
import com.tzx.publics.common.ApplicationContextUtils;
import com.tzx.publics.listener.InitDataListener;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.*;

public class RunPosPrint implements Runnable {

	private final static Logger LOGGER = LoggerFactory.getLogger(RunPosPrint.class);
	
	private static TzxReportLibInt lib = null;
	private static String dllPath = "";
	private int printid;
	private String printStr;
	private String posNo;

	public RunPosPrint(int printid, String printStr, String posNo) {
		this.printid = printid;
		this.printStr = printStr;
		this.posNo = posNo;
	}
	
	public static void initDll() {
		if (lib == null) {
			synchronized (RunPosPrint.class) {
				// 系统 Windows 或者 Linux
//				String osName = System.getProperties().getProperty("os.name").toLowerCase();
//				// //架构 x86 或者 amd64
//				String osArch = System.getProperties().getProperty("os.arch").toLowerCase();
//				String path = System.getProperty("user.dir");
//				path = path + File.separator + "bin" + File.separator;
//				LOGGER.info("This OS is: " + osName + ";" + osArch + ";path:" + path);
//				String fileName = null;
//				if (osArch.indexOf("64") != -1) {// 64位
//					dllPath = path + "PrintBin" + File.separator;
//					fileName = dllPath + "tzxReportLib64.dll";
//				} else if (osArch.indexOf("86") != -1) {// 32位
//					dllPath = path + "PrintBin" + File.separator;
//					fileName = dllPath + "tzxReportLib.dll";
//				} else {// 不支持的
//					LOGGER.info("This OS is not support！");
//				}
				
				// //架构 x86 或者 amd64
				String path = System.getProperty("user.dir");

				File file = new File(path);
				LOGGER.info("path:" + path);
				String fileName = null;
				dllPath = file.getParent() + File.separator;
				fileName = dllPath + "tzxReportLib.dll";

				LOGGER.info("PrintDllFileName: " + fileName);
				lib = (TzxReportLibInt) Native.loadLibrary(fileName, TzxReportLibInt.class);
			}
		}
	}

	@Override
	public void run() {
		try {
			String useNewPrint = InitDataListener.ggcsMap.get("USENEWPRINT");
			if ((null != useNewPrint) && useNewPrint.equals("Y")) {
				// 新版打印
				MiniAppFirstPayMapper firstPayMapper =  ApplicationContextUtils.applicationContext.getBean(MiniAppFirstPayMapper.class);
				firstPayMapper.insertDyrwk("99", 1, printid, printStr, 0);
			} else {
				// 原来打印方式
				LOGGER.info("run begin");
				this.initDll();
	            LOGGER.info("initDll end");
				int iPosNo = Integer.parseInt("1" + posNo);
				File file = new File(dllPath + "ReportParam");
				if (!file.exists()) {
					file.mkdir();
				}
				savePrintParamFile(dllPath + "ReportParam" + File.separator + iPosNo + "_" + String.valueOf(printid) + ".txt", printStr);
				this.lib.PrintReportByPosNo(printid, iPosNo);
			}
			LOGGER.info("完成打印...");
		} catch (Exception e) {
			e.printStackTrace();
			LOGGER.info("run err=" + e.getMessage());
		}

	}

	public void savePrintParamFile(String fileName, String printStr) {
		File file = new File(fileName);
		Writer out = null;
		try {
			out = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(file), "GBK"));
			out.write(printStr);
			out.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

}
