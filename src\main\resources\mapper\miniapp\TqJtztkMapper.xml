<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.miniapp.rest.mapper.MiniAppTqJtztkMapper">
	<select id="findBbrq" resultType="Map">
		SELECT nr as bbrq FROM TS_BMKZK WHERE ZDMC ='BBRQ'
	</select>
	<select id="findState" resultType="com.tzx.miniapp.rest.model.TqJtztk">
		select * from tq_jtztk where bbrq = #{bbrq} and rybh = #{rybh} and cznr = #{cznr} and jhid = #{jhid} limit 1
	</select>
	<update id="updataCzsj">
		update tq_jtztk set czsj = #{czsj} where id = #{id}
	</update>
	<select id="checkOpenState" resultType="com.tzx.miniapp.rest.model.TqJtztk">
		select * from tq_jtztk where bbrq = #{bbrq} and cznr = 'JSSY' and jhid = '99' limit 1
	</select>
	<select id="loginCheck" resultType="com.tzx.miniapp.rest.vo.LoginCheck">
		select rybh,ygdlcs,count(rybh) from tq_jtztk where BBRQ=#{bbrq} and jhid=#{jhid} GROUP BY rybh,ygdlcs HAVING count(rybh)&lt;2 limit 1
	</select>
	<select id="getMaxYgdlcs" resultType="java.lang.Integer">
		select COALESCE(max(to_number(ygdlcs,'99G999D9S')),'0') ygdlcs from tq_jtztk where BBRQ=#{bbrq} and cznr='YYDL' and jhid=#{jhid};
	</select>
	<select id="getYgdlcs" resultType="java.lang.Integer">
		select COALESCE(max(to_number(ygdlcs,'99G999D9S')),'0') ygdlcs from tq_jtztk where BBRQ=#{bbrq} and cznr='YYDL' and jhid=#{jhid} and rybh =#{rybh};
	</select>
	<delete id="delJtztk">
		delete from tq_jtztk where BBRQ=#{bbrq} and cznr='YYTC' and jhid=#{jhid} and rybh =#{rybh};
	</delete>
</mapper>
