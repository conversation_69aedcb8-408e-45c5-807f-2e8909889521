package com.tzx.mobilepos.rest.mapper;

import com.tzx.publics.base.MyMapper;
import com.tzx.mobilepos.rest.model.TsJtsdk;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
 *
 * <AUTHOR>
 * @since 2018-05-15
 */

public interface MobilePosTsJtsdkMapper extends MyMapper<TsJtsdk> {

	public TsJtsdk findByIpdz(@Param("jtbm") String jtbm);
	
	public TsJtsdk getMaxJtbh(@Param("jtsx") String jtsx);
	
	public int updataIpdz(@Param("ipdz") String ipdz, @Param("jtbm") String jtbm);
	
	public int pTjskjqj(@Param("sbbrq") Date sbbrq, @Param("sskjh") String sskjh);
	
}