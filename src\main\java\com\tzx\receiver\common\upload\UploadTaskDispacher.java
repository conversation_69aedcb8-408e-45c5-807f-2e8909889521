package com.tzx.receiver.common.upload;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 * @Date 2019-04-20
 * @Descption
 **/
@Component
@Lazy(true)
public class UploadTaskDispacher {
    @Autowired
    private UpLoadTaskList upLoadTaskList;
    @Autowired
    private UploadTaskHandler uploadTaskHandler;
    private ReentrantLock lock = new ReentrantLock();
    public void doUploadDispatcher(){

        List<UploadTask> taskList = new ArrayList<UploadTask>();
        //先把所有的取出来放到一个临时变量里面，然后处理器去
        //处理这个变量里面的值，再处理的过程中，再进来的任务，让下次再处理
        lock.lock();
        try{
            //优化1、优先取新的数据，重新上传的空闲再处理 2、每次只处理固定条数，其余留到下次任务
            //再处理，防止一次取的全部都是未上传的，把任务队列堵死
            while (upLoadTaskList.size()>0&&taskList.size()<UploadGloVar.SINGLE_TASK_UPLOAD){
                UploadTask uploadTask = upLoadTaskList.getHighPriorityTask();
                taskList.add(uploadTask);
            }
//            //开始逐条处理
            for (UploadTask uploadTask : taskList) {
                uploadTaskHandler.exector(uploadTask);
            }
        }finally {
            lock.unlock();
        }

    }

}
