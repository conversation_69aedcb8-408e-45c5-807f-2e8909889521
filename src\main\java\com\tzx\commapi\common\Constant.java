package com.tzx.commapi.common;

/**
 * Created by <PERSON><PERSON><PERSON> on 2020-01-14.
 */
public class Constant {
    // COMMAPI_SETLOCK
    public static final String COMMAPI_SETLOCK = "SETLOCK";
    public static final  int LOCK_RETRY_COUNT = 1000;
    public static final  int LOCK_RETRY_SLEEP_TIME = 10;
//    public static final  int LOCK_TEST_SLEEP_TIME = 1000*50;
    public static final  String TASK_UPLOAD_QIMAI = "QIMAI";
    public static final  String TASK_QIMAI_DAYBEGIN = "QIMAIDAYBEGIN";
    public static final  String TASK_QIMAI_DAYEND = "QIMAIDAYEND";

    public static final Integer TASK_STATUS_BEGIN = 0;
    public static final Integer TASK_STATUS_SUCCESS = 1;
    public static final Integer TASK_STATUS_FAIL = 2;

    // 老物业接口配置，新配置可以兼容
    public static final String TASK_UPLOAD_JPWY = "JPWY"; // 乡村基，晶品物业
    public static final String TASK_UPLOAD_FANGXING = "FANGXING"; // 李先生，江西服务区，方兴物业
    public static final String TASK_UPLOAD_KECHUAN = "KECHUAN"; // 大米先生，上海星荟中心店，科传物业
    // 以下是新物业接口配置
    public static final String TASK_UPLOAD_WUYE2 = "WUYE2"; // 物业上传，打样标识，打样上传，通过此标识，生成 WUYE3
    public static final String TASK_UPLOAD_WUYE3 = "WUYE3"; // 物业上传，实时标识
    // 李先生
    public static final String JKMODE_FANGXING = "TZXJK_FANGXING"; // 李先生，江西服务区，方兴物业
    public static final String JKMODE_JINLU = "TZXJK_JINLU"; // 李先生，金鹿物业
    public static final String JKMODE_CHANGYI = "TZXJK_CHANGYI"; // 李先生，长益物业，青岛胶东机场
    public static final String JKMODE_CHAOTONG = "TZXJK_CHAOTONG"; // 李先生，物业，哈尔滨太平机场
    public static final String JKMODE_CHANGJINGYIKANG = "TZXJK_CHANGJINGYIKANG"; // 李先生，长京益康物业，北京大兴机场
    public static final String JKMODE_WUTIECHUANMEI = "TZXJK_WUTIECHUANMEI"; // 李先生，武铁传媒物业，武汉火车站站内餐厅 webservice
    public static final String JKMODE_TAIYUANTIELU = "TZXJK_TAIYUANTIELU"; // 李先生，太原铁路
    public static final String JKMODE_ZHESHANG = "TZXJK_ZHESHANG"; // 李先生，江西服务区 浙商收银

    public static final String JKMODE_CHENGDUDONGZHAN = "TZXJK_CHENGDUDONGZHAN"; // 李先生，成都东站，兴易物业

    // 蒸小皖
    public static final String JKMODE_YIDA = "TZXJK_YIDA"; // 蒸小皖，南陵服务区驿达物业
    public static final String JKMODE_HEFEIXINQIAO = "TZXJK_HEFEIXINQIAO"; // 蒸小皖，合肥新桥机场科传物业
    // 泰希家
    public static final String JKMODE_SHANTOUWANXIANGCHENG = "TZXJK_SHANTOUWANXIANGCHENG"; // 泰希家，汕头万象城华润物业
    // 大米先生
    public static final String JKMODE_JPWY = "TZXJK_JPWY"; // 乡村基，晶品物业
    public static final String JKMODE_KECHUAN = "TZXJK_KECHUAN"; // 大米先生，上海星荟中心店，科传物业
    public static final String JKMODE_XIEXINXINGGUANG = "TZXJK_XIEXINXINGGUANG"; // 大米先生，上海协信星光物业
    public static final String JKMODE_WUCAICHENGHUARUN = "TZXJK_WUCAICHENGHUARUN"; // 大米先生，上海五彩城华润物业
    public static final String JKMODE_LVDIWAITANCHAOFANG = "TZXJK_LVDIWAITANCHAOFANG"; // 大米先生，绿地外滩潮方物业
    public static final String JKMODE_XUHUIRIGUANG = "TZXJK_XUHUIRIGUANG"; // 大米先生，徐汇日光物业
    public static final String JKMODE_WUXIANJIHUI = "TZXJK_WUXIANJIHUI"; // 大米先生，无限极荟物业
    public static final String JKMODE_JINHONGQIAOJINDIE = "TZXJK_JINHONGQIAOJINDIE"; // 大米先生，金虹桥国际中心金蝶物业
    public static final String JKMODE_JINRONGJIERSL = "TZXJK_JINRONGJIERSL"; // 大米先生，金融街RSL物业
    public static final String JKMODE_WANXIANGCHENGHUARUN = "TZXJK_WANXIANGCHENGHUARUN"; // 大米先生，无锡万象城华润物业（大米华润通用）
    public static final String JKMODE_XINGSHAHUARUN = "TZXJK_XINGSHAHUARUN"; // 大米先生，星沙万象汇华润物业（大米华润通用）
    public static final String JKMODE_BUYECHENGKECHUAN = "TZXJK_BUYECHENGKECHUAN"; // 大米先生，上海不夜城 科传
    public static final String JKMODE_XINGYANGKECHUAN = "TZXJK_XINGYANGKECHUAN"; // 大米先生，上海星扬餐厅，科传物业(大米科传通用)
    public static final String JKMODE_CHENGDUHUARUN = "TZXJK_CHENGDUHUARUN"; // 大米先生，成都万象汇华润物业（大米华润通用）
    public static final String JKMODE_HUANQIUMAOYIKECHUAN = "TZXJK_HUANQIUMAOYIKECHUAN"; // 大米先生，环球贸易
    public static final String JKMODE_DINGXIANGGUOJIGUANGCHANG = "TZXJK_DINGXIANGGUOJIGUANGCHANG"; // 大米先生，丁香国际广场，酆泽物业
    public static final String JKMODE_CHONGQINGBEIZHAN = "TZXJK_CHONGQINGBEIZHAN"; // 大米先生，重庆火车北站，兴易物业
    public static final String JKMODE_SHANGHAIWANXIANGCHENG = "TZXJK_SHANGHAIWANXIANGCHENG"; // 大米先生，上海万象汇华润物业
    public static final String JKMODE_BEIJINGCHANGYINGTIANJIE = "TZXJK_BEIJINGCHANGYINGTIANJIE"; // 乡村基，北京长楹天街店物业
    public static final String JKMODE_JINGANJINGPING = "TZXJK_JINANJINGPING"; // 大米先生，静安精品
    public static final String JKMODE_BAOLISHIGUANG = "TZXJK_BAOLISHIGUANG"; //乡村基,上海保利时光店
    // 鼎友
    public static final String JKMODE_HUARUNWANXIANGHUI = "TZXJK_HUARUNWANXIANGHUI"; // 鼎友，长沙星沙 ，华润万象汇，通用
    public static final String JKMODE_HANGZHOUDONGZHANWANXIANGHUIHUARUN = "TZXJK_HANGZHOUDONGZHANWANXIANGHUIHUARUN"; //鼎友-杭州东站万象汇 华润
    public static final String JKMODE_WUHANTIANDI = "TZXJK_WUHANTIANDI"; // 大米先生，武汉天地
    public static final String JKMODE_XUHUIRIGUANG_V2 = "TZXJK_XUHUIRIGUANG_V2"; // 大米先生，徐汇日光物业V2
    public static final String JKMODE_XISANQIWANXIANGHUI = "TZXJK_XISANQIWANXIANGHUI"; // 西三旗万象汇
    public static final String JKMODE_SHUZHOUZHONGXIN = "TZXJK_SHUZHOUZHONGXIN"; // 苏州中心餐厅
    public static final String JKMODE_PUJIANGHUANLESONG = "TZXJK_PUJIANGHUANLESONG"; // 浦江欢乐颂
    public static final String JKMODE_HONGSHULINKECHUAN = "TZXJK_HONGSHULIN"; // 大米先生 绿景佐阾红树林店
}
