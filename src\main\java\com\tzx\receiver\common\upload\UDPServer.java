package com.tzx.receiver.common.upload;

import java.net.*;
import java.util.ResourceBundle;

import com.tzx.receiver.common.utils.SendXML2RifMQ;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class UDPServer extends Thread {
	private ResourceBundle tzxresource = null;
	private DatagramSocket server = null;
	private byte[] recvBuf;
	private int udpPort = 3005;
	
	private static UDPServer listener = null;
	
	private volatile boolean isRun = true;
	
	protected Logger	logger	= LoggerFactory.getLogger(getClass());
	
	static{
		listener = new UDPServer();
		listener.init();
	}
	
	private UDPServer(){
		
	}
	
	private void init(){
		tzxresource = ResourceBundle.getBundle("tzxmqconfig"); 
		String port = tzxresource.getString("udpPort");
		udpPort = new Integer(port.trim()).intValue();
	}
	
	private void close(){
		isRun = false;
	}
	
	public static void listenStart(){
		listener.start();
	}
	
	public static void listenStop(){
		listener.close();
	}
	


	public void run() {
		logger.info("UDP服务启动");
		try {
			server = new DatagramSocket(udpPort);
		} catch (Exception e) {
			logger.info(udpPort+"冲突");
			e.printStackTrace();
			return;
		}
		recvBuf = new byte[400];
		String recvStr = "";
		while (isRun) {
			try {
				DatagramPacket recvPacket = new DatagramPacket(recvBuf,	recvBuf.length);
				server.receive(recvPacket);
				recvStr = new String(recvPacket.getData(), 0, recvPacket.getLength(), "gb2312");
				logger.info("received udp message is:>>>>" + recvStr);
				String FileName = recvStr.substring(recvStr.indexOf("FileName=") + 9, recvStr.length());
				logger.info("xxxxxreceived file name is xxxx" + FileName);

				SendXML2RifMQ.sendFile2MQ(FileName);
			} catch (Exception e) {
				e.printStackTrace();
				logger.info("发生异常" + e.toString() + "上传操作可能未成功！");
			}
			catch (Throwable t) {
				logger.info("发生Throwable" + t.toString() + "上传操作可能未成功！");
			}
		}
		logger.info("UDP服务关闭");
	}
}