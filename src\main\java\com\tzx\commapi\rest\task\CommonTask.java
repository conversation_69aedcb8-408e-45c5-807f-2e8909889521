package com.tzx.commapi.rest.task;

import com.tzx.commapi.common.CommApiException;
import com.tzx.commapi.common.Constant;
import com.tzx.commapi.rest.service.ITaskApiService;
import com.tzx.publics.listener.InitDataListener;
import com.tzx.publics.util.Util;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Configuration // 1.主要用于标记配置类，兼备Component的效果。
@EnableScheduling // 2.开启定时任务
public class CommonTask {
	private final static Logger LOGGER = LoggerFactory.getLogger(CommonTask.class);
	@Autowired
	private ITaskApiService taskApiService;

	// 企迈上传会员订单， 每10秒一次
	@Scheduled(cron = "*/10 * * * * ?")
	private void configureTasks() {
		taskApiService.doQimaiDataTask();
	}

	// 李先生企迈上传日始，打样状态 每10秒执行一次
	@Scheduled(cron = "*/10 * * * * ?")
	private void configureQmStatusTasks() {
		taskApiService.doQmStatusTask();
	}

	// 乡村基上传晶品物业接口 每10秒执行一次
	// @Scheduled(cron = "*/10 * * * * ?")
	// private void configureJpwyTasks() {
	// taskApiService.DoJpDataTask();
	// }

	// 李先生高速方兴物业接口 每10秒执行一次
	// @Scheduled(cron = "*/10 * * * * ?")
	// private void configureFxwyTasks() {
	// taskApiService.DoFangXingDataTask();
	// }

	// 乡村基上传科传物业接口 每10秒执行一次
	// @Scheduled(cron = "*/10 * * * * ?")
	// private void configureKcwyTasks() {
	// taskApiService.DoKeChuanDataTask();
	// }

	// 物业任务分发
	@Scheduled(cron = "*/10 * * * * ?")
	private void allocatingRealEstateTasks() {
		String jkmode = InitDataListener.ggcsMap.get("JKMODE");
		String wyversion = InitDataListener.ggcsMap.get("POS_WUYEUPLOAD_TYPE");

		// 从总部获取日结后账单（4），也放到这里，单独去拉取，流程基本一致，只是数据来源不同
		// 从总部获取实时账单（5），这里只做本地那部分单子的上传，流程与其他物业相同，总部部分另起一个定时器
		// 李先生兴易需求，要求账单按照自然日T+1上传（6），给这种模式单独启用一个类型，便于扩展
		if (!Util.isNullOrEmpty(wyversion) && (wyversion.equals("2") || wyversion.equals("3") || wyversion.equals("4") || wyversion.equals("5"))) {
			if (wyversion.equals("2") || wyversion.equals("4")) {
				// 如果是打样上传，先去做数据，转换为wuye3 数据
				try {
					taskApiService.doWy2To3Data();
				}  catch (CommApiException cae) {
					LOGGER.error("Ignore this CommApiException", cae);
				} catch (Exception e) {
					LOGGER.error("Ignore this Exception", e);
				}
			}
			// 新的物业上传
			taskApiService.doWy2And3DataTask();
		} else if (!Util.isNullOrEmpty(wyversion) && wyversion.equals("6")) {
			// 按照自然日 T+1 上传
			taskApiService.doNaturalDayTask();
		} else {
			switch (jkmode) {
			// 乡村基上传晶品物业接口
			case Constant.JKMODE_JPWY:
				taskApiService.doJpDataTask();
				break;
			// 李先生高速方兴物业接口 每10秒执行一次
			case Constant.JKMODE_FANGXING:
				taskApiService.doFangXingDataTask();
				break;
			// 大米先生上传科传物业接口 每10秒执行一次，改为新版，老版废弃
//			case Constant.JKMODE_KECHUAN:
//				taskApiService.doKeChuanDataTask();
//				break;
			default:
				break;
			}
		}
	}

	/**
	 * 物业接口数据拉取: 从总部拉取AI智能设备的订单 (这部分订单不在pos上)
	 */
	@Scheduled(cron = "0 */5 * * * ?")
	private void bohDataEstateTasks() {
		String wyversion = InitDataListener.ggcsMap.get("POS_WUYEUPLOAD_TYPE");
		if (!Util.isNullOrEmpty(wyversion) && wyversion.equals("5")) {
			taskApiService.bohDataEstateTasks();
		}

	}

	// 查询下发记录，新下发通知pos， 1分钟一次
	@Scheduled(cron = "0 */1 * * * ?")
	private void downloadMsgTasks() {
		String posDownloadMsg = InitDataListener.ggcsMap.get("POS_DOWNLOAD_MSG");
		if (!Util.isNullOrEmpty(posDownloadMsg) && posDownloadMsg.equals("1")) {
			taskApiService.downloadMsgTasks();
		}
	}

	// 授权校验， 每小时一次
	@Scheduled(fixedDelay = 1000 * 60 * 60)
	private void lockCtrlVerifyTasks() {
		taskApiService.lockCtrlVerifyTasks();
	}


}














