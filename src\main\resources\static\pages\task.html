<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>任务配置</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <style>
        .container { margin-top: 50px; }
        .form-group { margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h2 class="mb-4">任务配置</h2>
        <div class="form-group row">
            <label class="col-sm-2 col-form-label">报表日期:</label>
            <div class="col-sm-4">
                <input type="text" class="form-control" id="bbrq" readonly>
            </div>
            <div class="col-sm-2">
                <button class="btn btn-primary" onclick="updateBbrq()">修改</button>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 col-form-label">更新时间:</label>
            <div class="col-sm-4">
                <input type="text" class="form-control" id="lastUpdatetime" readonly>
            </div>
            <div class="col-sm-2">
                <button class="btn btn-primary" onclick="updateLastUpdatetime()">修改</button>
            </div>
        </div>
        <div class="form-group row">
            <div class="col-sm-2">
                <button id="startBtn" class="btn btn-success" onclick="startTask()">开始</button>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let updateTimer; // 用于存储定时器ID

        // 页面加载时获取配置并启动定时更新
        $(document).ready(function() {
            loadConfig();
            // 启动定时更新，每3秒更新一次
            updateTimer = setInterval(loadConfig, 3000);
        });

        // 页面关闭或切换时清除定时器
        $(window).on('unload', function() {
            if (updateTimer) {
                clearInterval(updateTimer);
            }
        });

        function loadConfig() {
            $.get('/task/getConfig', function(data) {
                $('#bbrq').val(data.bbrq || '');
                $('#lastUpdatetime').val(data.lastUpdatetime || '');
            });
        }

        function updateBbrq() {
            let newValue = prompt('请输入新的报表日期 (格式: YYYY-MM-DD):', $('#bbrq').val());
            if (newValue) {
                $.post('/task/updateBbrq', {bbrq: newValue}, function(response) {
                    alert(response.message);
                    if (response.success) {
                        loadConfig();
                    }
                });
            }
        }

        function updateLastUpdatetime() {
            let newValue = prompt('请输入新的更新时间 (格式: YYYY-MM-DD HH:mm:ss):', $('#lastUpdatetime').val());
            if (newValue) {
                $.post('/task/updateLastUpdatetime', {lastUpdatetime: newValue}, function(response) {
                    alert(response.message);
                    if (response.success) {
                        loadConfig();
                    }
                });
            }
        }

        function startTask() {
            // 禁用开始按钮
            $('#startBtn').prop('disabled', true).text('执行中...');

            $.post('/task/start', function(response) {
                alert(response.message);
                // 重新启用开始按钮
                $('#startBtn').prop('disabled', false).text('开始');
                // 刷新配置数据
                loadConfig();
            }).fail(function(jqXHR, textStatus, errorThrown) {
                alert('任务执行失败: ' + errorThrown);
                // 重新启用开始按钮
                $('#startBtn').prop('disabled', false).text('开始');
            });
        }
    </script>
</body>
</html>
