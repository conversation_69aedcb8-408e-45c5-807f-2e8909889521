package com.tzx.locationsystem.rest.service.impl;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.tzx.locationsystem.rest.mapper.LocationMapper;
import com.tzx.locationsystem.rest.service.ILocationService;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

@Service
public class LocationService  implements ILocationService {
	@Autowired
	private LocationMapper locationMapper;
	
	public void CreateLocationInformation(JSONObject data) {
		locationMapper.deleteLocation();
		JSONArray tableInfoList = data.names();
		for (int i = 0; i < tableInfoList.size(); i++) {
			//JSONObject o = tableInfoList.getJSONObject(i);
			String cph = tableInfoList.getString(i);
			JSONArray infoList = data.getJSONArray(cph);
			String zwh = infoList.getString(0).trim();
			if (!zwh.equals("0")) {
				locationMapper.insertLocation(cph, zwh);
			}
		}
	}
}
