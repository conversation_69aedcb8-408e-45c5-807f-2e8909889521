package com.tzx.miniapp.rest.vo;

import javax.persistence.Entity;
import java.io.Serializable;

@Entity
public class BtYdxm1 implements Serializable {

	private String yddh;
	private String xmbh;
	private String xmmc;
	private int scbj;
	private String memo;
	private double yl1;
	private double yl2;
	private String yl3;
	private String yl4;
	private String yl5;

	public String getYddh() {
		return yddh;
	}

	public void setYddh(String yddh) {
		this.yddh = yddh;
	}

	public String getXmbh() {
		return xmbh;
	}

	public void setXmbh(String xmbh) {
		this.xmbh = xmbh;
	}

	public String getXmmc() {
		return xmmc;
	}

	public void setXmmc(String xmmc) {
		this.xmmc = xmmc;
	}

	public int getScbj() {
		return scbj;
	}

	public void setScbj(int scbj) {
		this.scbj = scbj;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public double getYl1() {
		return yl1;
	}

	public void setYl1(double yl1) {
		this.yl1 = yl1;
	}

	public double getYl2() {
		return yl2;
	}

	public void setYl2(double yl2) {
		this.yl2 = yl2;
	}

	public String getYl3() {
		return yl3;
	}

	public void setYl3(String yl3) {
		this.yl3 = yl3;
	}

	public String getYl4() {
		return yl4;
	}

	public void setYl4(String yl4) {
		this.yl4 = yl4;
	}

	public String getYl5() {
		return yl5;
	}

	public void setYl5(String yl5) {
		this.yl5 = yl5;
	}

}
