package com.tzx.config;

import com.tzx.receiver.common.mapper.ObjectMapper;
import com.tzx.receiver.common.utils.DBUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;

@Configuration
public class RestTemplateConfig {
    Logger log = LoggerFactory.getLogger(RestTemplateConfig.class);
    @Bean
    public RestTemplate restTemplate(){
        //设置一下超时时长。
        String pms_timeout = DBUtils.getGGCSK("PMS_TIMEOUT");
        Integer pms_timeout_int = 5;
        if (StringUtils.isBlank(pms_timeout)){
            try{
                pms_timeout_int = Integer.parseInt(pms_timeout);
            }catch (Exception e){
                log.info("读取PMS超时时间默认时间发生异常，使用默认值");
            }
        }
        HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        httpRequestFactory.setConnectionRequestTimeout(pms_timeout_int*1000);
        httpRequestFactory.setConnectTimeout(pms_timeout_int*1000);
        httpRequestFactory.setReadTimeout(pms_timeout_int*1000);
        RestTemplate restTemplate =  new RestTemplate(httpRequestFactory);

        return  restTemplate;
    }
}
