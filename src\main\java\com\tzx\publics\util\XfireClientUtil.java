package com.tzx.publics.util;

import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

import org.codehaus.xfire.client.Client;
import org.codehaus.xfire.transport.http.CommonsHttpMessageSender;
import org.w3c.dom.Document;
import org.w3c.dom.NodeList;


/**
 * xfire工具类
 * 
 * 
 */
public class XfireClientUtil {
	private static final int	CLIENT_TIME_OUT	= 15 * 1000;

	public static Map<String, String> invoke(String serviceUrl, String tagName, String value) throws Exception {
		return XfireClientUtil.invoke(serviceUrl, "execact", tagName, value);
	}
 
	public static Map<String, String> invoke(String serviceUrl, String method, String tagName, Object... value) throws Exception {
		Map<String, String> map = new HashMap<String, String>();
		Client client = XfireClientUtil.getClient(serviceUrl);
		Object[] results = client.invoke(method, value);

		Document d = (Document)results[0];
		NodeList nl =d.getElementsByTagName(tagName);
		NodeList n2 = nl.item(0).getChildNodes();
		for (int i = 0; i < n2.getLength(); i++) {
//			map.put(n2.item(i).getNodeName(), n2.item(i).getTextContent());
			map.put(n2.item(i).getLocalName(), n2.item(i).getTextContent());
		}
		return map;
	}

	public static Client getClient(String serviceUrl) throws Exception
	{
		URL _url = new URL(serviceUrl);
		HttpURLConnection httpConnection = (HttpURLConnection) _url.openConnection();
		httpConnection.setReadTimeout(CLIENT_TIME_OUT);// 设置http连接的读超时,单位是毫秒
		httpConnection.setConnectTimeout(CLIENT_TIME_OUT);
		httpConnection.connect();
		Client client = new Client(httpConnection.getInputStream(), null);
		client.setProperty(CommonsHttpMessageSender.HTTP_TIMEOUT, String.valueOf(CLIENT_TIME_OUT));// 设置发送的超时限制,单位是毫秒;
		client.setProperty(CommonsHttpMessageSender.DISABLE_KEEP_ALIVE, "true");
		client.setProperty(CommonsHttpMessageSender.DISABLE_EXPECT_CONTINUE, "true");
		return client;
	}

	public static void main(String[] args) {
		String http="http://180.168.153.8:6666/GetSkfsData.svc?wsdl";
		try {
			Map<String, String> mapR =  XfireClientUtil.invoke(http,"GetSkfsData", "GetSkfsDataResult",null);
			System.out.println(mapR);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
