package com.tzx.commapi.rest.vo;

import java.util.Date;

/**
 * Created by <PERSON><PERSON><PERSON> on 2020-09-09.
 */
public class TqThirdTempOrder {
    private Long  id;
    private String billnum;
    private Date   reportdate;
    private String orderno;
    private Integer status;
    private Integer errcount;
    private Integer runinterval;
    private String datatype;
    private Date    createtime;
    private Date    updatetime;
    private String remark;
    private Date jzsj;


    public void SetTqThirdTempOrder(Long id, String billnum, Date reportdate, String orderno, Integer status, Integer errcount, Integer runinterval, String datatype, Date createtime, Date updatetime) {
        this.id = id;
        this.billnum = billnum;
        this.reportdate = reportdate;
        this.orderno = orderno;
        this.status = status;
        this.errcount = errcount;
        this.runinterval = runinterval;
        this.datatype = datatype;
        this.createtime = createtime;
        this.updatetime = updatetime;
    }

    public void SetTaskToTqThirdTempOrder(Long id, String billnum, Date reportdate, String orderno, Integer status, Integer errcount, Integer runinterval, String datatype, Date createtime, Date updatetime, Date jzsj) {
        this.id = id;
        this.billnum = billnum;
        this.reportdate = reportdate;
        this.orderno = orderno;
        this.status = status;
        this.errcount = errcount;
        this.runinterval = runinterval;
        this.datatype = datatype;
        this.createtime = createtime;
        this.updatetime = updatetime;
        this.jzsj = jzsj;
    }

    public String getBillnum() {
        return billnum;
    }

    public void setBillnum(String billnum) {
        this.billnum = billnum;
    }

    public Date getReportdate() {
        return reportdate;
    }

    public void setReportdate(Date reportdate) {
        this.reportdate = reportdate;
    }

    public String getOrderno() {
        return orderno;
    }

    public void setOrderno(String orderno) {
        this.orderno = orderno;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getErrcount() {
        return errcount;
    }

    public void setErrcount(Integer errcount) {
        this.errcount = errcount;
    }

    public Integer getRuninterval() {
        return runinterval;
    }

    public void setRuninterval(Integer runinterval) {
        this.runinterval = runinterval;
    }

    public String getDatatype() {
        return datatype;
    }

    public void setDatatype(String datatype) {
        this.datatype = datatype;
    }

    public Date getCreatetime() {
        return createtime;
    }

    public void setCreatetime(Date createtime) {
        this.createtime = createtime;
    }

    public Date getUpdatetime() {
        return updatetime;
    }

    public void setUpdatetime(Date updatetime) {
        this.updatetime = updatetime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getJzsj() {
        return jzsj;
    }

    public void setJzsj(Date jzsj) {
        this.jzsj = jzsj;
    }
}
