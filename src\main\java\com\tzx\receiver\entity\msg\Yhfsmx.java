package com.tzx.receiver.entity.msg;

import java.io.Serializable;

public class Yhfsmx
  implements Serializable
{
  private Integer id;
  private String mxbh;
  private Integer yhid;
  private Integer xmid;
  private String xmmc;
  private Double yhdj;
  private Double yhje;
  private Double zkl;
  private Double zke;
  private String bz;
  private String yl1;
  private String yl2;
  private String yl3;
  private String yhfsbh;
  private String xmmc1;
  private String xmmc2;
  private Integer fzxh;

  public Integer getId()
  {
    return this.id; }

  public void setId(Integer id) {
    this.id = id; }

  public String getMxbh() {
    return this.mxbh; }

  public void setMxbh(String mxbh) {
    this.mxbh = mxbh; }

  public Integer getYhid() {
    return this.yhid; }

  public void setYhid(Integer yhid) {
    this.yhid = yhid; }

  public Integer getXmid() {
    return this.xmid; }

  public void setXmid(Integer xmid) {
    this.xmid = xmid; }

  public String getXmmc() {
    return this.xmmc; }

  public void setXmmc(String xmmc) {
    this.xmmc = xmmc; }

  public Double getYhdj() {
    return this.yhdj; }

  public void setYhdj(Double yhdj) {
    this.yhdj = yhdj; }

  public Double getYhje() {
    return this.yhje; }

  public void setYhje(Double yhje) {
    this.yhje = yhje; }

  public Double getZkl() {
    return this.zkl; }

  public void setZkl(Double zkl) {
    this.zkl = zkl; }

  public Double getZke() {
    return this.zke; }

  public void setZke(Double zke) {
    this.zke = zke; }

  public String getBz() {
    return this.bz; }

  public void setBz(String bz) {
    this.bz = bz; }

  public String getYl1() {
    return this.yl1; }

  public void setYl1(String yl1) {
    this.yl1 = yl1; }

  public String getYl2() {
    return this.yl2; }

  public void setYl2(String yl2) {
    this.yl2 = yl2; }

  public String getYl3() {
    return this.yl3; }

  public void setYl3(String yl3) {
    this.yl3 = yl3; }

  public String getYhfsbh() {
    return this.yhfsbh; }

  public void setYhfsbh(String yhfsbh) {
    this.yhfsbh = yhfsbh; }

  public String getXmmc1() {
    return this.xmmc1; }

  public void setXmmc1(String xmmc1) {
    this.xmmc1 = xmmc1; }

  public String getXmmc2() {
    return this.xmmc2; }

  public void setXmmc2(String xmmc2) {
    this.xmmc2 = xmmc2; }

  public Integer getFzxh() {
    return this.fzxh; }

  public void setFzxh(Integer fzxh) {
    this.fzxh = fzxh;
  }
}