package com.tzx.receiver.entity.msg;

import java.io.Serializable;
import java.util.Date;

public class Companycustom
  implements Serializable
{
  private Integer id;
  private Integer cono;
  private String cotel;
  private String fax;
  private String addr;
  private String post;
  private String depositbank;
  private String bankaccount;
  private String businessarea;
  private String industry;
  private String industrystatus;
  private String website;
  private String taxcode;
  private String szdq;
  private String isprotocol;
  private String protocolno;
  private Date protocoldate;
  private String simplename;
  private String recommender;
  private String memo;
  private String state;
  private String creator;
  private Date creatdate;
  private String name1;
  private String billtye;
  private Double debit;
  private Integer regorg;
  private String pydm;
  private String wbdm;
  private String password;
  private String zkfs;
  private Double zkl;
  private Integer zkfa;
  private String tcfs;
  private Double tcbl;
  private Double tcje;
  private String shr;
  private Date shrq;
  private String shzt;
  private Integer gkdj;
  private Integer gklx;
  private Double zhye;
  private Double mzje;
  private Double ljgze;
  private Double yjje;
  private Double ljxf;
  private Integer ljxfcs;
  private Double ljjf;
  private String cwkmbh;
  private String cwkmmc;
  private String name2;
  private Double orgzkl;
  private Integer orgzkfa;
  private Double ydgzxe;
  private Integer rgzcs;
  private Double dcgzxe;
  private Integer jgxh;
  private String sfxs;
  private Date begindate;
  private Date enddate;

  public String getSfxs()
  {
    return this.sfxs; }

  public void setSfxs(String sfxs) {
    this.sfxs = sfxs; }

  public Integer getId() {
    return this.id; }

  public void setId(Integer id) {
    this.id = id; }

  public Integer getCono() {
    return this.cono; }

  public void setCono(Integer cono) {
    this.cono = cono; }

  public String getCotel() {
    return this.cotel; }

  public void setCotel(String cotel) {
    this.cotel = cotel; }

  public String getFax() {
    return this.fax; }

  public void setFax(String fax) {
    this.fax = fax; }

  public String getAddr() {
    return this.addr; }

  public void setAddr(String addr) {
    this.addr = addr; }

  public String getPost() {
    return this.post; }

  public void setPost(String post) {
    this.post = post; }

  public String getDepositbank() {
    return this.depositbank; }

  public void setDepositbank(String depositbank) {
    this.depositbank = depositbank; }

  public String getBankaccount() {
    return this.bankaccount; }

  public void setBankaccount(String bankaccount) {
    this.bankaccount = bankaccount; }

  public String getBusinessarea() {
    return this.businessarea; }

  public void setBusinessarea(String businessarea) {
    this.businessarea = businessarea; }

  public String getIndustry() {
    return this.industry; }

  public void setIndustry(String industry) {
    this.industry = industry; }

  public String getIndustrystatus() {
    return this.industrystatus; }

  public void setIndustrystatus(String industrystatus) {
    this.industrystatus = industrystatus; }

  public String getWebsite() {
    return this.website; }

  public void setWebsite(String website) {
    this.website = website; }

  public String getTaxcode() {
    return this.taxcode; }

  public void setTaxcode(String taxcode) {
    this.taxcode = taxcode; }

  public String getSzdq() {
    return this.szdq; }

  public void setSzdq(String szdq) {
    this.szdq = szdq; }

  public String getIsprotocol() {
    return this.isprotocol; }

  public void setIsprotocol(String isprotocol) {
    this.isprotocol = isprotocol; }

  public String getProtocolno() {
    return this.protocolno; }

  public void setProtocolno(String protocolno) {
    this.protocolno = protocolno; }

  public Date getProtocoldate() {
    return this.protocoldate; }

  public void setProtocoldate(Date protocoldate) {
    this.protocoldate = protocoldate; }

  public String getSimplename() {
    return this.simplename; }

  public void setSimplename(String simplename) {
    this.simplename = simplename; }

  public String getRecommender() {
    return this.recommender; }

  public void setRecommender(String recommender) {
    this.recommender = recommender; }

  public String getMemo() {
    return this.memo; }

  public void setMemo(String memo) {
    this.memo = memo; }

  public String getState() {
    return this.state; }

  public void setState(String state) {
    this.state = state; }

  public String getCreator() {
    return this.creator; }

  public void setCreator(String creator) {
    this.creator = creator; }

  public Date getCreatdate() {
    return this.creatdate; }

  public void setCreatdate(Date creatdate) {
    this.creatdate = creatdate; }

  public String getName1() {
    return this.name1; }

  public void setName1(String name1) {
    this.name1 = name1; }

  public String getBilltye() {
    return this.billtye; }

  public void setBilltye(String billtye) {
    this.billtye = billtye; }

  public Double getDebit() {
    return this.debit; }

  public void setDebit(Double debit) {
    this.debit = debit; }

  public Integer getRegorg() {
    return this.regorg; }

  public void setRegorg(Integer regorg) {
    this.regorg = regorg; }

  public String getPydm() {
    return this.pydm; }

  public void setPydm(String pydm) {
    this.pydm = pydm; }

  public String getWbdm() {
    return this.wbdm; }

  public void setWbdm(String wbdm) {
    this.wbdm = wbdm; }

  public String getPassword() {
    return this.password; }

  public void setPassword(String password) {
    this.password = password; }

  public String getZkfs() {
    return this.zkfs; }

  public void setZkfs(String zkfs) {
    this.zkfs = zkfs; }

  public Double getZkl() {
    return this.zkl; }

  public void setZkl(Double zkl) {
    this.zkl = zkl; }

  public Integer getZkfa() {
    return this.zkfa; }

  public void setZkfa(Integer zkfa) {
    this.zkfa = zkfa; }

  public String getTcfs() {
    return this.tcfs; }

  public void setTcfs(String tcfs) {
    this.tcfs = tcfs; }

  public Double getTcbl() {
    return this.tcbl; }

  public void setTcbl(Double tcbl) {
    this.tcbl = tcbl; }

  public Double getTcje() {
    return this.tcje; }

  public void setTcje(Double tcje) {
    this.tcje = tcje; }

  public String getShr() {
    return this.shr; }

  public void setShr(String shr) {
    this.shr = shr; }

  public Date getShrq() {
    return this.shrq; }

  public void setShrq(Date shrq) {
    this.shrq = shrq; }

  public String getShzt() {
    return this.shzt; }

  public void setShzt(String shzt) {
    this.shzt = shzt; }

  public Integer getGkdj() {
    return this.gkdj; }

  public void setGkdj(Integer gkdj) {
    this.gkdj = gkdj; }

  public Integer getGklx() {
    return this.gklx; }

  public void setGklx(Integer gklx) {
    this.gklx = gklx; }

  public Double getZhye() {
    return this.zhye; }

  public void setZhye(Double zhye) {
    this.zhye = zhye; }

  public Double getMzje() {
    return this.mzje; }

  public void setMzje(Double mzje) {
    this.mzje = mzje; }

  public Double getLjgze() {
    return this.ljgze; }

  public void setLjgze(Double ljgze) {
    this.ljgze = ljgze; }

  public Double getYjje() {
    return this.yjje; }

  public void setYjje(Double yjje) {
    this.yjje = yjje; }

  public Double getLjxf() {
    return this.ljxf; }

  public void setLjxf(Double ljxf) {
    this.ljxf = ljxf; }

  public Integer getLjxfcs() {
    return this.ljxfcs; }

  public void setLjxfcs(Integer ljxfcs) {
    this.ljxfcs = ljxfcs; }

  public Double getLjjf() {
    return this.ljjf; }

  public void setLjjf(Double ljjf) {
    this.ljjf = ljjf; }

  public String getCwkmbh() {
    return this.cwkmbh; }

  public void setCwkmbh(String cwkmbh) {
    this.cwkmbh = cwkmbh; }

  public String getCwkmmc() {
    return this.cwkmmc; }

  public void setCwkmmc(String cwkmmc) {
    this.cwkmmc = cwkmmc; }

  public String getName2() {
    return this.name2; }

  public void setName2(String name2) {
    this.name2 = name2; }

  public Double getOrgzkl() {
    return this.orgzkl; }

  public void setOrgzkl(Double orgzkl) {
    this.orgzkl = orgzkl; }

  public Integer getOrgzkfa() {
    return this.orgzkfa; }

  public void setOrgzkfa(Integer orgzkfa) {
    this.orgzkfa = orgzkfa; }

  public Double getYdgzxe() {
    return this.ydgzxe; }

  public void setYdgzxe(Double ydgzxe) {
    this.ydgzxe = ydgzxe; }

  public Integer getRgzcs() {
    return this.rgzcs; }

  public void setRgzcs(Integer rgzcs) {
    this.rgzcs = rgzcs; }

  public Double getDcgzxe() {
    return this.dcgzxe; }

  public void setDcgzxe(Double dcgzxe) {
    this.dcgzxe = dcgzxe; }

  public Integer getJgxh() {
    return this.jgxh; }

  public void setJgxh(Integer jgxh) {
    this.jgxh = jgxh;
  }

  public Date getBegindate() {
    return begindate;
  }

  public void setBegindate(Date begindate) {
    this.begindate = begindate;
  }

  public Date getEnddate() {
    return enddate;
  }

  public void setEnddate(Date enddate) {
    this.enddate = enddate;
  }
}