package com.tzx.ecoserver.rest.vo;

import javax.persistence.Entity;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Created by <PERSON>x<PERSON> on 2019-10-30.
 */

@Entity
public class EcoOrderdiscount implements Serializable {

    private String order_code;//
    private String discount_type;
    private String discount_desc;
    private BigDecimal discount_fee;
    private BigDecimal platform_rate;
    private BigDecimal shop_rate;
    private String activity_id;
    private String pay_no;

    public String getOrder_code() {
        return order_code;
    }

    public void setOrder_code(String order_code) {
        this.order_code = order_code;
    }

    public String getDiscount_type() {
        return discount_type;
    }

    public void setDiscount_type(String discount_type) {
        this.discount_type = discount_type;
    }

    public String getDiscount_desc() {
        return discount_desc;
    }

    public void setDiscount_desc(String discount_desc) {
        this.discount_desc = discount_desc;
    }

    public BigDecimal getDiscount_fee() {
        return discount_fee;
    }

    public void setDiscount_fee(BigDecimal discount_fee) {
        this.discount_fee = discount_fee;
    }

    public BigDecimal getPlatform_rate() {
        return platform_rate;
    }

    public void setPlatform_rate(BigDecimal platform_rate) {
        this.platform_rate = platform_rate;
    }

    public BigDecimal getShop_rate() {
        return shop_rate;
    }

    public void setShop_rate(BigDecimal shop_rate) {
        this.shop_rate = shop_rate;
    }

    public String getActivity_id() {
        return activity_id;
    }

    public void setActivity_id(String activity_id) {
        this.activity_id = activity_id;
    }

    public String getPay_no() {
        return pay_no;
    }

    public void setPay_no(String pay_no) {
        this.pay_no = pay_no;
    }
}
