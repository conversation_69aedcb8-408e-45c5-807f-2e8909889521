package com.tzx.receiver.common.utils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

/**
 * 生产uuid
 * <AUTHOR>
 *
 */
public class UUIDUtils
{
	 /**
	  * 生产唯一的标示.
	  * @return
	  */
	public static String UUIdGenerator(){
		 UUID uuId = UUID.randomUUID();
	 	return uuId.toString().replace("-", "");
	}
	/**
	 * 产生一个时间字符
	 * @return
	 */
	public static String generatorDateNo(){
		SimpleDateFormat dateFormat =new SimpleDateFormat("0yyyyMMddHHmmssssssss");
	  return dateFormat.format(new Date());
	} 

}
