<!DOCTYPE html>
<html lang="zh-CN" xmlns:align="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <!-- Bootstrap -->
    <link href="../bootstrap-3.3.7-dist/css/bootstrap.css" rel="stylesheet">

    <!-- HTML5 shim 和 Respond.js 是为了让 IE8 支持 HTML5 元素和媒体查询（media queries）功能 -->
    <!-- 警告：通过 file:// 协议（就是直接将 html 页面拖拽到浏览器中）访问页面时 Respond.js 不起作用 -->
    <!--[if lt IE 9]>
    <script src="../js/common/html5shiv.min.js"></script>
    <script src="../js/common/respond.min.js"></script>
    <![endif]-->
    <script type="text/javascript" src="../js/common/jquery.min.js"></script>
    <script type="text/javascript" src="../bootstrap-3.3.7-dist/js/bootstrap.min.js"></script>
    <script type="text/javascript" src="../js/pages/down.js"></script>
</head>
<style>

    .table th {

        text-align: center;
        height: 38px;
        background-color: #f0f4f4;

    }

</style>
<body>
<div class="container-fluid">
    <div class="alert alert-danger alert-dismissible" role="alert" id = "uploadmsg" style="display: none">
        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        加载下发日志发生异常
    </div>
    <table class="table table-striped table-bordered table-hover">
        <thead>
        <tr>
            <th width="100px" align="center">表名称</th>
            <th width="80px" align="center">处理时间</th>
            <th width="100px" align="center">处理结果</th>
            <th  align="center">异常原因</th>
        </tr>
        </thead>
        <tbody id = "uploadtbbody">
        <tr>
            <td>TS_CMK_XF</td>
            <td>2019-5-6 17:50:01</td>
            <td>成功</td>
            <td>无异常</td>
        </tr>
        </tbody>
    </table>
    <div class="container-fluid">
</body>
</html>