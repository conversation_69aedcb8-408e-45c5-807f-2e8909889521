package com.tzx.fmcgbi.rest.vo;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @since 2020-01-13
 */
public class ThirdExceptOrderVo implements Serializable {
	private static final long serialVersionUID = 1L;

	private String billid;
	private Date createtime;
	private Date updatetime;
	private String scan_code;
	private String paytypeid;
	private String paytime;
	private String orderno;
	private String refund_orderno;
	private String productcode;
	private String dynamicid_type;
	private double cashamount;
	private String ordermc;
	private String dataname;
	private int updatecount;
	private String first_pay_status;
	private String last_pay_status;
	private String pay_status;
	private String posno;
	private String payname;
	private int errcount;

	private String lsdh;
	private String ktczry;
	private String kdbbrq;
	private int ktbcid;

	public String getBillid() {
		return billid;
	}

	public void setBillid(String billid) {
		this.billid = billid;
	}

	public Date getCreatetime() {
		return createtime;
	}

	public void setCreatetime(Date createtime) {
		this.createtime = createtime;
	}

	public Date getUpdatetime() {
		return updatetime;
	}

	public void setUpdatetime(Date updatetime) {
		this.updatetime = updatetime;
	}

	public String getScan_code() {
		return scan_code;
	}

	public void setScan_code(String scan_code) {
		this.scan_code = scan_code;
	}

	public String getPaytypeid() {
		return paytypeid;
	}

	public void setPaytypeid(String paytypeid) {
		this.paytypeid = paytypeid;
	}

	public String getPaytime() {
		return paytime;
	}

	public void setPaytime(String paytime) {
		this.paytime = paytime;
	}

	public String getOrderno() {
		return orderno;
	}

	public void setOrderno(String orderno) {
		this.orderno = orderno;
	}

	public String getRefund_orderno() {
		return refund_orderno;
	}

	public void setRefund_orderno(String refund_orderno) {
		this.refund_orderno = refund_orderno;
	}

	public String getProductcode() {
		return productcode;
	}

	public void setProductcode(String productcode) {
		this.productcode = productcode;
	}

	public String getDynamicid_type() {
		return dynamicid_type;
	}

	public void setDynamicid_type(String dynamicid_type) {
		this.dynamicid_type = dynamicid_type;
	}

	public double getCashamount() {
		return cashamount;
	}

	public void setCashamount(double cashamount) {
		this.cashamount = cashamount;
	}

	public String getOrdermc() {
		return ordermc;
	}

	public void setOrdermc(String ordermc) {
		this.ordermc = ordermc;
	}

	public String getDataname() {
		return dataname;
	}

	public void setDataname(String dataname) {
		this.dataname = dataname;
	}

	public int getUpdatecount() {
		return updatecount;
	}

	public void setUpdatecount(int updatecount) {
		this.updatecount = updatecount;
	}

	public String getFirst_pay_status() {
		return first_pay_status;
	}

	public void setFirst_pay_status(String first_pay_status) {
		this.first_pay_status = first_pay_status;
	}

	public String getLast_pay_status() {
		return last_pay_status;
	}

	public void setLast_pay_status(String last_pay_status) {
		this.last_pay_status = last_pay_status;
	}

	public String getPay_status() {
		return pay_status;
	}

	public void setPay_status(String pay_status) {
		this.pay_status = pay_status;
	}

	public String getPosno() {
		return posno;
	}

	public void setPosno(String posno) {
		this.posno = posno;
	}

	public String getPayname() {
		return payname;
	}

	public void setPayname(String payname) {
		this.payname = payname;
	}

	public int getErrcount() {
		return errcount;
	}

	public void setErrcount(int errcount) {
		this.errcount = errcount;
	}

	public String getLsdh() {
		return lsdh;
	}

	public void setLsdh(String lsdh) {
		this.lsdh = lsdh;
	}

	public String getKtczry() {
		return ktczry;
	}

	public void setKtczry(String ktczry) {
		this.ktczry = ktczry;
	}

	public String getKdbbrq() {
		return kdbbrq;
	}

	public void setKdbbrq(String kdbbrq) {
		this.kdbbrq = kdbbrq;
	}

	public int getKtbcid() {
		return ktbcid;
	}

	public void setKtbcid(int ktbcid) {
		this.ktbcid = ktbcid;
	}

}
