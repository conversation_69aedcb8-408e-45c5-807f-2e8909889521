package com.tzx.publics.base.entity;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

public class MethodInfo {

	private Method method;

	private Map<String, Method> methods = new HashMap<>();

	public MethodInfo(String oper, Method method) {
		if (oper != null && oper.trim().length() > 0) {
			methods.put(oper, method);
		} else {
			this.method = method;
		}
	}

	public Method getMethod(String oper) {
		if (oper == null || oper.trim().length() == 0||oper.trim().equals("NULL")) {
			return method;
		} else {
			if (methods.containsKey(oper)) {
				return methods.get(oper);
			} else {
				return null;
			}
		}
	}

	public void setMethod(Method method) {
		this.method = method;
	}

}
