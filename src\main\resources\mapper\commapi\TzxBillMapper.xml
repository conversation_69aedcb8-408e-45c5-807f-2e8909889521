<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tzx.commapi.rest.mapper.TzxBillMapper">
	<select id="selectBill" parameterType="net.sf.json.JSONObject" resultType="com.tzx.commapi.rest.vo.FdzdkInfo">
		select
			tq_zdk.jzzdbh,
			tq_zdk.kdzdbh,
			tq_zdk.pcbh,
			tq_zdk.kdlxbh,
			tq_zdk.lsdh,
			tq_zdk.kdbbrq,
			tq_zdk.jzbbrq,
			tq_zdk.cbid,
			tq_zdk.zwbh,
			tq_zdk.xfks,
			tq_zdk.ktsj,
			tq_zdk.jzsj,
			tq_zdk.jzcs,
			tq_zdk.ktskjh,
			tq_zdk.jzskjh,
			tq_zdk.fwyh,
			tq_zdk.ktczry,
			tq_zdk.jzczry,
			tq_zdk.ktbcid,
			tq_zdk.jzbcid,
			tq_zdk.nld,
			tq_zdk.fwfzid,
			tq_zdk.fwfbh,
			tq_zdk.fwfje,
			tq_zdk.fwfzkl,
			tq_zdk.zdxfzt,
			tq_zdk.yddh,
			tq_zdk.kdxdh,
			tq_zdk.ydsj,
			tq_zdk.dyzdcs,
			tq_zdk.yjje,
			tq_zdk.xmxjje,
			tq_zdk.zdje,
			tq_zdk.fkje,
			tq_zdk.fkce,
			tq_zdk.zkje,
			tq_zdk.zrje,
			tq_zdk.mlje,
			tq_zdk.dpzkje,
			tq_zdk.yhje,
			tq_zdk.fsje,
			tq_zdk.dslj,
			tq_zdk.mdje,
			tq_zdk.yhczry,
			tq_zdk.zkl,
			tq_zdk.zdzkl,
			tq_zdk.zkfaid,
			tq_zdk.zkfabh,
			tq_zdk.mdyyid,
			tq_zdk.mdyybh,
			tq_zdk.yhfsid,
			tq_zdk.yhfsbh,
			tq_zdk.zzbz,
			tq_zdk.zdbz,
			tq_zdk.xsms,
			tq_zdk.cwlxbh,
			tq_zdk.cwlxmc,
			tq_zdk.zdzt,
			tq_zdk.yclxid,
			tq_zdk.gkqtid,
			tq_zdk.zdylxid,
			tq_zdk.men,
			tq_zdk.woamen,
			tq_zdk.etrs,
			tq_zdk.lrrs,
			tq_zdk.jzsx,
			tq_zdk.scbj,
			tq_zdk.transflag,
			tq_zdk.zccs,
			tq_zdk.fzzdbh,
			tq_zdk.ksdcsj,
			tq_zdk.ksjzsj,
			tq_zdk.jzjssj,
			tq_zdk.bcqrsj,
			tq_zdk.source,
			tq_zdk.ygdlcs,
			tq_zdk.cpbj,
			tq_zdk.yl1,
			tq_zdk.yl2,
			tq_zdk.yl3,
			tq_zdk.psfje,
			tq_zdk.yhyhje,
			tq_zdk.ptyhje,
			tq_zdk.pyyjje,
			tq_zdk.yhfkje,
			1 AS ms,
			tq_zdk.yongjje,
			tq_zdk.delivery_type,
			tq_zdk.ecototalfee,
			tq_zdk.zpsfje,
			tq_zdk.wmtype,
			tq_zdk.qch,
			tq_zdk.cardno,
			tq_zdk.phone,
			tq_zdk.membername,
			tq_zdk.rfbbrq,
			tq_zdk.qjdh
		from tq_zdk where kdzdbh = #{ORDERCODE,jdbcType=VARCHAR} and jzbbrq = #{bbrq}
		union
		select
			tq_zdlsk.jzzdbh,
			tq_zdlsk.kdzdbh,
			tq_zdlsk.pcbh,
			tq_zdlsk.kdlxbh,
			tq_zdlsk.lsdh,
			tq_zdlsk.kdbbrq,
			tq_zdlsk.jzbbrq,
			tq_zdlsk.cbid,
			tq_zdlsk.zwbh,
			tq_zdlsk.xfks,
			tq_zdlsk.ktsj,
			tq_zdlsk.jzsj,
			tq_zdlsk.jzcs,
			tq_zdlsk.ktskjh,
			tq_zdlsk.jzskjh,
			tq_zdlsk.fwyh,
			tq_zdlsk.ktczry,
			tq_zdlsk.jzczry,
			tq_zdlsk.ktbcid,
			tq_zdlsk.jzbcid,
			tq_zdlsk.nld,
			tq_zdlsk.fwfzid,
			tq_zdlsk.fwfbh,
			tq_zdlsk.fwfje,
			tq_zdlsk.fwfzkl,
			tq_zdlsk.zdxfzt,
			tq_zdlsk.yddh,
			tq_zdlsk.kdxdh,
			tq_zdlsk.ydsj,
			tq_zdlsk.dyzdcs,
			tq_zdlsk.yjje,
			tq_zdlsk.xmxjje,
			tq_zdlsk.zdje,
			tq_zdlsk.fkje,
			tq_zdlsk.fkce,
			tq_zdlsk.zkje,
			tq_zdlsk.zrje,
			tq_zdlsk.mlje,
			tq_zdlsk.dpzkje,
			tq_zdlsk.yhje,
			tq_zdlsk.fsje,
			tq_zdlsk.dslj,
			tq_zdlsk.mdje,
			tq_zdlsk.yhczry,
			tq_zdlsk.zkl,
			tq_zdlsk.zdzkl,
			tq_zdlsk.zkfaid,
			tq_zdlsk.zkfabh,
			tq_zdlsk.mdyyid,
			tq_zdlsk.mdyybh,
			tq_zdlsk.yhfsid,
			tq_zdlsk.yhfsbh,
			tq_zdlsk.zzbz,
			tq_zdlsk.zdbz,
			tq_zdlsk.xsms,
			tq_zdlsk.cwlxbh,
			tq_zdlsk.cwlxmc,
			tq_zdlsk.zdzt,
			tq_zdlsk.yclxid,
			tq_zdlsk.gkqtid,
			tq_zdlsk.zdylxid,
			tq_zdlsk.men,
			tq_zdlsk.woamen,
			tq_zdlsk.etrs,
			tq_zdlsk.lrrs,
			tq_zdlsk.jzsx,
			tq_zdlsk.scbj,
			tq_zdlsk.transflag,
			tq_zdlsk.zccs,
			tq_zdlsk.fzzdbh,
			tq_zdlsk.ksdcsj,
			tq_zdlsk.ksjzsj,
			tq_zdlsk.jzjssj,
			tq_zdlsk.bcqrsj,
			tq_zdlsk.source,
			tq_zdlsk.ygdlcs,
			tq_zdlsk.cpbj,
			tq_zdlsk.yl1,
			tq_zdlsk.yl2,
			tq_zdlsk.yl3,
			tq_zdlsk.psfje,
			tq_zdlsk.yhyhje,
			tq_zdlsk.ptyhje,
			tq_zdlsk.pyyjje,
			tq_zdlsk.yhfkje,
			0 AS ms,
			tq_zdlsk.yongjje,
			tq_zdlsk.delivery_type,
			tq_zdlsk.ecototalfee,
			tq_zdlsk.zpsfje,
			tq_zdlsk.wmtype,
			tq_zdlsk.qch,
			tq_zdlsk.cardno,
			tq_zdlsk.phone,
			tq_zdlsk.membername,
			tq_zdlsk.rfbbrq,
			tq_zdlsk.qjdh
		from tq_zdlsk where kdzdbh = #{ORDERCODE,jdbcType=VARCHAR} and jzbbrq = #{bbrq}
	</select>

	<select id="selectBillItem" parameterType="net.sf.json.JSONObject" resultType="com.tzx.commapi.rest.vo.FdzdmxkInfo">
		select * from tq_wdk where kdzdbh = #{ORDERCODE,jdbcType=VARCHAR} and jzbbrq = #{bbrq}
		union
		select * from tq_wdk where kdzdbh = #{ORDERCODE,jdbcType=VARCHAR} and jzbbrq = #{bbrq}
	</select>

	<select id="selectBillPayment" parameterType="net.sf.json.JSONObject" resultType="com.tzx.commapi.rest.vo.FdzdfkfskInfo">
		select * from tq_fklslsk where kdzdbh = #{ORDERCODE,jdbcType=VARCHAR} and jzbbrq = #{bbrq}
		union
		select * from tq_fklslsk where kdzdbh = #{ORDERCODE,jdbcType=VARCHAR} and jzbbrq = #{bbrq}
	</select>

</mapper>
