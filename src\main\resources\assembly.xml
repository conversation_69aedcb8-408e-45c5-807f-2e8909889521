<assembly xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.3" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.3 http://maven.apache.org/xsd/assembly-1.1.3.xsd">
	<id>package</id>
	<formats>
		<format>zip</format>
	</formats>
	<includeBaseDirectory>true</includeBaseDirectory>
	<fileSets>
		<!--<fileSet>-->
			<!--<directory>${basedir}/src/main/resources</directory>-->
			<!--<includes>-->
				<!--<include>*.xml</include>-->
				<!--<include>*.properties</include>-->
			<!--</includes>-->
			<!--<filtered>true</filtered>-->
			<!--<outputDirectory>${file.separator}config</outputDirectory>-->
		<!--</fileSet>-->

		<!--<fileSet>-->
			<!--<directory>src/main/resources/runScript</directory>-->
			<!--<outputDirectory>${file.separator}bin</outputDirectory>-->
		<!--</fileSet>-->
		<fileSet>
			<directory>${project.build.directory}/lib</directory>
			<outputDirectory>${file.separator}lib</outputDirectory>
			<includes>
				<include>*.jar</include>
			</includes>
		</fileSet>
		<fileSet>
			<directory>${project.build.directory}</directory>
			<outputDirectory>${file.separator}</outputDirectory>
			<includes>
				<include>*.jar</include>
			</includes>
		</fileSet>
	</fileSets>
</assembly>