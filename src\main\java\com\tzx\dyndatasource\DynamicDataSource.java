package com.tzx.dyndatasource;
 
import com.tzx.config.MatualDbUtil;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;
 
import javax.sql.DataSource;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2020-03-13
 * @Descption 动态的数据源
 **/
public class DynamicDataSource extends AbstractRoutingDataSource {
    private static final ThreadLocal<String> contextHolder = new ThreadLocal<>();
 
    public DynamicDataSource( Map<Object, Object> targetDataSources) {
        super.setDefaultTargetDataSource((DataSource)targetDataSources.get(
                "UAT".equalsIgnoreCase(MatualDbUtil.getGGCSK("ThreeAndOneDB"))?
                        "UAT":"PROD"));
        super.setTargetDataSources(targetDataSources);
        super.afterPropertiesSet();
    }
 
    @Override
    protected Object determineCurrentLookupKey() {
        return getDataSource();
    }
 
    public static void setDataSource(String dataSource) {
        contextHolder.set(dataSource);
    }
 
    public static String getDataSource() {
        return contextHolder.get();
    }
 
    public static void clearDataSource() {
        contextHolder.remove();
    }
 
}
