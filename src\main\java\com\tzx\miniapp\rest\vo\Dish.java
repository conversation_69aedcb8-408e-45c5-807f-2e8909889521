package com.tzx.miniapp.rest.vo;

import java.io.Serializable;
import java.util.List;

import javax.persistence.Entity;
import javax.persistence.Transient;

@Entity
public class Dish extends OmpDish implements Serializable {

	private int id;
	private int xmid;
	private String name;
	private String dishsno;
	private String describ;
	private String info;
	private int type;
	private boolean wxDishs;
	private int pkid;
	private String icon;
	private String image;
	private String dishimg;
	private int min_unit;
	private int min_count;
	private int min_reduce;
	private int soldout;
	private String priceName;
	private int isWeigh;
	private float leftamount;
	private float price;
	private String dwname;
	// 会员价
	private float vipPrice;
	private String iffoodbox;
	private int foodboxset;
	private String ifspec; // 是否多规格

	/**
	 * 餐盒费
	 */
	private Double box_price;

	@Transient
	private List<Integer> dishkind;

	@Transient
	private List<Norms> norms;

	@Transient
	private List<Object> cooks;

	@Transient
	private Setmeals setmeals;

	@Transient
	private List<ItemTasteGroup> memo;

	private int did;
	private int number;
	private String dishunit;
	private String dSaleStart;
	private String dSaleEnd;

	private int dHide; // 是否隐藏：1显示，2隐藏
	private int dOrder; // 排序值，升序排列

	private String ksrq;// 开始售卖日期
	private String jsrq;// 结束日期
	private String daysOfWeek;// 适用星期 1，2，3，4，5，6，0 0为周日

	/**
	 * 对应菜品 dishToppingId
	 */
	private String dishToppingId;

	private Integer duid;

	private String pkno;

	public Integer getDuid() {
		return duid;
	}

	public void setDuid(Integer duid) {
		this.duid = duid;
	}

	public float getVipPrice() {
		return vipPrice;
	}

	public void setVipPrice(float vipPrice) {
		this.vipPrice = vipPrice;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public int getXmid() {
		return xmid;
	}

	public void setXmid(int xmid) {
		this.xmid = xmid;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDishsno() {
		return dishsno;
	}

	public void setDishsno(String dishsno) {
		this.dishsno = dishsno;
	}

	public String getDescrib() {
		return describ;
	}

	public void setDescrib(String describ) {
		this.describ = describ;
	}

	public String getInfo() {
		return info;
	}

	public void setInfo(String info) {
		this.info = info;
	}

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

	public boolean isWxDishs() {
		return wxDishs;
	}

	public void setWxDishs(boolean wxDishs) {
		this.wxDishs = wxDishs;
	}

	public int getPkid() {
		return pkid;
	}

	public void setPkid(int pkid) {
		this.pkid = pkid;
	}

	public String getIcon() {
		return icon;
	}

	public void setIcon(String icon) {
		this.icon = icon;
	}

	public String getImage() {
		return image;
	}

	public void setImage(String image) {
		this.image = image;
	}

	public String getDishimg() {
		return dishimg;
	}

	public void setDishimg(String dishimg) {
		this.dishimg = dishimg;
	}

	public int getMin_unit() {
		return min_unit;
	}

	public void setMin_unit(int min_unit) {
		this.min_unit = min_unit;
	}

	public int getMin_count() {
		return min_count;
	}

	public void setMin_count(int min_count) {
		this.min_count = min_count;
	}

	public int getMin_reduce() {
		return min_reduce;
	}

	public void setMin_reduce(int min_reduce) {
		this.min_reduce = min_reduce;
	}

	public int getSoldout() {
		return soldout;
	}

	public void setSoldout(int soldout) {
		this.soldout = soldout;
	}

	public String getPriceName() {
		return priceName;
	}

	public void setPriceName(String priceName) {
		this.priceName = priceName;
	}

	public int getIsWeigh() {
		return isWeigh;
	}

	public void setIsWeigh(int isWeigh) {
		this.isWeigh = isWeigh;
	}

	public float getLeftamount() {
		return leftamount;
	}

	public void setLeftamount(float leftamount) {
		this.leftamount = leftamount;
	}

	public float getPrice() {
		return price;
	}

	public void setPrice(float price) {
		this.price = price;
	}

	public String getDwname() {
		return dwname;
	}

	public void setDwname(String dwname) {
		this.dwname = dwname;
	}

	public List<Integer> getDishkind() {
		return dishkind;
	}

	public void setDishkind(List<Integer> dishkind) {
		this.dishkind = dishkind;
	}

	public List<Norms> getNorms() {
		return norms;
	}

	public void setNorms(List<Norms> norms) {
		this.norms = norms;
	}

	public List<Object> getCooks() {
		return cooks;
	}

	public void setCooks(List<Object> cooks) {
		this.cooks = cooks;
	}

	public Setmeals getSetmeals() {
		return setmeals;
	}

	public void setSetmeals(Setmeals setmeals) {
		this.setmeals = setmeals;
	}

	public int getDid() {
		return did;
	}

	public void setDid(int did) {
		this.did = did;
	}

	public int getNumber() {
		return number;
	}

	public void setNumber(int number) {
		this.number = number;
	}

	public String getDishunit() {
		return dishunit;
	}

	public void setDishunit(String dishunit) {
		this.dishunit = dishunit;
	}

	public String getdSaleStart() {
		return dSaleStart;
	}

	public void setdSaleStart(String dSaleStart) {
		this.dSaleStart = dSaleStart;
	}

	public String getdSaleEnd() {
		return dSaleEnd;
	}

	public void setdSaleEnd(String dSaleEnd) {
		this.dSaleEnd = dSaleEnd;
	}

	public int getdHide() {
		return dHide;
	}

	public void setdHide(int dHide) {
		this.dHide = dHide;
	}

	public int getdOrder() {
		return dOrder;
	}

	public void setdOrder(int dOrder) {
		this.dOrder = dOrder;
	}

	public String getKsrq() {
		return ksrq;
	}

	public void setKsrq(String ksrq) {
		this.ksrq = ksrq;
	}

	public String getJsrq() {
		return jsrq;
	}

	public void setJsrq(String jsrq) {
		this.jsrq = jsrq;
	}

	public String getDaysOfWeek() {
		return daysOfWeek;
	}

	public void setDaysOfWeek(String daysOfWeek) {
		this.daysOfWeek = daysOfWeek;
	}

	public String getIffoodbox() {
		return iffoodbox;
	}

	public void setIffoodbox(String iffoodbox) {
		this.iffoodbox = iffoodbox;
	}

	public int getFoodboxset() {
		return foodboxset;
	}

	public void setFoodboxset(int foodboxset) {
		this.foodboxset = foodboxset;
	}

	public List<ItemTasteGroup> getMemo() {
		return memo;
	}

	public void setMemo(List<ItemTasteGroup> memo) {
		this.memo = memo;
	}

	public String getIfspec() {
		return ifspec;
	}

	public void setIfspec(String ifspec) {
		this.ifspec = ifspec;
	}

	public String getDishToppingId() {
		return dishToppingId;
	}

	public void setDishToppingId(String dishToppingId) {
		this.dishToppingId = dishToppingId;
	}

	public Double getBox_price() {
		return box_price;
	}

	public void setBox_price(Double box_price) {
		this.box_price = box_price;
	}

	public String getPkno() {
		return pkno;
	}

	public void setPkno(String pkno) {
		this.pkno = pkno;
	}
}
