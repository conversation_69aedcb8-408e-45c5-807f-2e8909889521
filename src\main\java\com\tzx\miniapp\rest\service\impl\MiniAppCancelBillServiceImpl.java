package com.tzx.miniapp.rest.service.impl;

import com.tzx.miniapp.common.Constant;
import com.tzx.miniapp.common.Data;
import com.tzx.miniapp.rest.mapper.*;
import com.tzx.miniapp.rest.model.TqJtztk;
import com.tzx.miniapp.rest.model.TsGgcsk;
import com.tzx.miniapp.rest.model.TsPsjgsdk;
import com.tzx.miniapp.rest.service.IMiniAppCancelBillService;
import com.tzx.miniapp.rest.vo.Shops;
import com.tzx.miniapp.rest.vo.TqYyddylsk;
import com.tzx.publics.listener.InitDataListener;
import com.tzx.publics.util.*;
import com.tzx.receiver.common.utils.DBUtils;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;

@Service
public class MiniAppCancelBillServiceImpl implements IMiniAppCancelBillService {
	private final static Logger LOGGER = LoggerFactory.getLogger(MiniAppFirstPayServiceImpl.class);

	@Autowired
	private MiniAppFirstPayMapper firstPayMapper;

	@Autowired
	private MiniAppShopStatusMapper shopStatusMapper;

	@Autowired
	private MiniAppCancelBillMapper cancelBillMapper;

	@Autowired
	private MiniAppShopBaseInfoMapper shopBaseInfoMapper;

	@Autowired
	private MiniAppTsPsjgsdkMapper tsPsjgsdkMapper;

	@Transactional
	public Data cancelBill(JSONObject obj) {
		Data data = new Data();
		try {
			data.setSuccess(0);
			data.setMsg("退单失败");

			String yddbh = obj.optString("out_order_id");
			String tsyddbh = Constant.BILL_PREFIX + yddbh;
			LOGGER.info("小程序发起退款,平台订单编号：" + yddbh);

			// TqZdk tqZdk = cancelBillMapper.findBill(tsyddbh);
			Map<String, String> bbrqMap = shopStatusMapper.findBbrq();
			String dqbbrq = DateUtil.getNowDateYYDDMM();
			if (null != bbrqMap && bbrqMap.size() != 0) {
				dqbbrq = bbrqMap.get("bbrq");
			}
			TqJtztk jtzt = firstPayMapper.getJtZtk(DateUtil.parseDate(dqbbrq));

			int canceR = cancelBillMapper.cancelBill(tsyddbh, DateUtil.parseDate(dqbbrq), "99", 0, jtzt.getRybh());

			JSONObject datajob = new JSONObject();
			if (canceR == 0) {
//				firstPayMapper.updateBtYdd(tsyddbh, "", "7", "", "");
				firstPayMapper.updateBtYddToCancel(tsyddbh, "7");
				datajob.put("out_order_id", yddbh);
				data.setSuccess(1);
				data.setMsg("退单成功");
				data.setData(datajob);
			} else if (canceR == -100) {
				data.setSuccess(1);
				data.setMsg("退单失败，没有预定单");
			} else if (canceR == -200) {
				data.setSuccess(1);
				data.setMsg("退单失败，没有预定对应的账单");
			} else {
				data.setSuccess(1);
				data.setMsg("退单失败");
			}
//			insertYddy(tsyddbh);

			return data;
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			data.setSuccess(0);
			data.setMsg("系统错误");
			data.setData(new HashMap<String, Object>());
			return data;
		}
	}

	@Transactional
	public Data posCancelBill(JSONObject obj) {
		Data data = new Data();
		try {
			data.setSuccess(1);
			data.setMsg("退单失败");
            String retundApi = "/api/Order/refund";
			String propertiesPath = "/application.properties";
            String appKey=DBUtils.getGGCSK("WELCRM_APPKEY");
            if(StringUtils.isEmpty(appKey)){
                appKey = PropertiesUtil.readValueForClasses(propertiesPath, "welcrm_appkey");
            }

            if (StringUtils.isEmpty(appKey)) {
                data.setMsg("应用密钥未配置！退款失败！");
                return data;
            }

			TsPsjgsdk jg = tsPsjgsdkMapper.findLocalShopConfig();
			String shopkey = jg == null ? null : jg.getJydd();

            if (StringUtils.isEmpty(shopkey)) {
                data.setMsg("门店密码未配置！退款失败！");
                return data;
            }


            String welcrm_api=DBUtils.getGGCSK("WELCRM_API");
            if(StringUtils.isEmpty(welcrm_api)){
                welcrm_api = PropertiesUtil.readValueForClasses(propertiesPath, "welcrm_api_refund");
            }
            if (StringUtils.isEmpty(welcrm_api)) {
                data.setMsg("微生活退款接口地址未配置！退款失败！");
                return data;
            }

			String apiRefund = welcrm_api+retundApi;

			String tsyddbh = obj.optString("yddbh");
			String yddh = tsyddbh.replaceFirst("TS", "");

            String shopUUID=InitDataListener.organVo.getUuid();

			String sign = Util.miniappSign(appKey, yddh);
			String result = Util.refund(apiRefund, shopkey,shopUUID ,yddh, sign);
			if (Const.CONNECT_ERROR.equals(result)) {
				data.setMsg("连接错误，退款失败！");
			} else {
				LOGGER.info("微生活退款返回：" + result);
				JSONObject resultJson = JSONObject.fromObject(result);
				int code = resultJson.getInt("errcode");
				if (code == 0) {
					firstPayMapper.updateBtYddToCancel(tsyddbh, "7");
//					insertYddy(tsyddbh);
					data.setSuccess(0);
					data.setMsg("微生活小程序退款成功！");
				} else {
					data.setMsg("微生活小程序退款失败:" + resultJson.optString("errmsg"));
				}
			}

			return data;
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			data.setSuccess(1);
			data.setMsg("系统错误");
			return data;
		}
	}

	public void insertYddy(String tsyddbh) {
		
		String bbrq = DateUtil.getNowDateYYDDMM();
		Map<String, String> bbrqMap = shopStatusMapper.findBbrq();
		if (null != bbrqMap && bbrqMap.size() != 0) {
			bbrq = bbrqMap.get("bbrq");
		}
		String isPrint = "1";
		TsGgcsk ggcsp = firstPayMapper.getGgcsToWs("POSISPRINTXCXBILL");
		if (null != ggcsp && !"".equals(ggcsp.getSdnr())) {
			isPrint = ggcsp.getSdnr();
		}
		if("0".equals(isPrint)){
//			String dyjmc = PropertiesUtil.readValueForClasses("/application.properties", "wx02printname");
			String dyjmc = "";
//			try {
//				dyjmc = new String(dyjmc.getBytes("ISO-8859-1"), "UTF-8");
//			} catch (UnsupportedEncodingException e) {
//				e.printStackTrace();
//			}
			Shops shops = shopBaseInfoMapper.findShopsData();

			TqYyddylsk yyddyls = new TqYyddylsk();

			yyddyls.setFdjgxh(shops.getSid());
			yyddyls.setYddh(tsyddbh);
			yyddyls.setBbrq(DateUtil.formatTimestamp(bbrq, "yyyy-MM-dd"));
			yyddyls.setDyzt(0);
			yyddyls.setDyjmc(dyjmc);
			yyddyls.setDylx("qx");
			yyddyls.setNeed_invoice("N");
			yyddyls.setDdlx("wx");
			firstPayMapper.insertTqYyddylsk(yyddyls);
		}
	}
}
