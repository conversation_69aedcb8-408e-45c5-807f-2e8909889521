package com.tzx.commapi.rest.controller;

import com.tzx.commapi.rest.service.IXinShangTieApiService;
import com.tzx.commapi.rest.vo.XstApiData;
import com.tzx.publics.base.BaseController;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.UUID;

@RestController
@RequestMapping("")
public class XinShangTieApiController extends BaseController implements InitializingBean, DisposableBean {
    @Override
    public void destroy() throws Exception {}
    @Override
    public void afterPropertiesSet() throws Exception {}

    private final static Logger LOGGER = LoggerFactory.getLogger(XinShangTieApiController.class);

    @Autowired
    private IXinShangTieApiService xinShangTieApiService;

    private JSONObject GetSaleJsonObject(String methodTag, XstApiData data, String json, String uuid){
        try {
            LOGGER.info("request({}) UUID={},\t  json={}", new Object[]{uuid, methodTag, json});
            data.setCode(0);
            return  JSONObject.fromObject(json);
        } catch (Exception e) {
            e.printStackTrace();
            data.setMsg("解析json异常,请求JSON格式错误");
            data.setCode(1);
            LOGGER.error(e.getMessage());
            return null;
        }
    }

    @RequestMapping(value = "/GetSaleOrders", method = RequestMethod.POST)
    public String getEcoOrderBack(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) {
        XstApiData data = new XstApiData();
        String uuid = UUID.randomUUID().toString();

        JSONObject dejsonobj = GetSaleJsonObject("GetSaleOrders", data, json, uuid);
        if(!data.getCode().equals(0)){
            return  JSONObject.fromObject(data).toString();
        }
        xinShangTieApiService.getSaleOrders(data, dejsonobj);
        LOGGER.info("response(GetSaleOrders) UUID={},\t json={}", new Object[] {uuid, JSONObject.fromObject(data).toString()});
        return  JSONObject.fromObject(data).toString();
    }

    @RequestMapping(value = "/get/GetSaleOrders", method = RequestMethod.GET)
    public String GetSaleOrdersGet(HttpServletRequest request, HttpServletResponse response) {
        XstApiData data = new XstApiData();
        String uuid = UUID.randomUUID().toString();

        String shopNO = request.getParameter("shopNO");
        String startTime = request.getParameter("startTime");
        String count = request.getParameter("count");

        JSONObject json = new JSONObject();
        json.put("shopNO", shopNO);
        json.put("startTime", startTime);
        json.put("count", count);

        JSONObject dejsonobj = GetSaleJsonObject("GetSaleOrders", data, json.toString(), uuid);
        if(!data.getCode().equals(0)){
            return  JSONObject.fromObject(data).toString();
        }
        xinShangTieApiService.getSaleOrders(data, dejsonobj);
        LOGGER.info("response(GetSaleOrders) UUID={},\t json={}", new Object[] {uuid, JSONObject.fromObject(data).toString()});
        return  JSONObject.fromObject(data).toString();
    }
}
