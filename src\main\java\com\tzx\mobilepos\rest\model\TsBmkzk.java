package com.tzx.mobilepos.rest.model;

import com.tzx.publics.base.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;

@Table(name = "TQ_ZDLSK")
public class TsBmkzk extends BaseEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	@Column(name = "BMC")
	private String bmc;
	@Column(name = "ZDMC")
	private String zdmc;
	@Column(name = "NR")
	private String nr;
	@Column(name = "SFYZ")
	private String sfyz;
	@Column(name = "CDZD")
	private String cdzd;
	@Column(name = "MEMO")
	private String memo;

	public String getBmc() {
		return bmc;
	}

	public void setBmc(String bmc) {
		this.bmc = bmc;
	}

	public String getZdmc() {
		return zdmc;
	}

	public void setZdmc(String zdmc) {
		this.zdmc = zdmc;
	}

	public String getNr() {
		return nr;
	}

	public void setNr(String nr) {
		this.nr = nr;
	}

	public String getSfyz() {
		return sfyz;
	}

	public void setSfyz(String sfyz) {
		this.sfyz = sfyz;
	}

	public String getCdzd() {
		return cdzd;
	}

	public void setCdzd(String cdzd) {
		this.cdzd = cdzd;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

}
