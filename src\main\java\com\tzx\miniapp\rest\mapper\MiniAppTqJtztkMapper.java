package com.tzx.miniapp.rest.mapper;

import com.tzx.publics.base.MyMapper;
import com.tzx.miniapp.rest.model.TqJtztk;
import com.tzx.miniapp.rest.vo.LoginCheck;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @since 2018-05-15
 */

public interface MiniAppTqJtztkMapper extends MyMapper<TqJtztk> {
	public Map<String, String> findBbrq();
	
	public TqJtztk findState(@Param("bbrq") Date bbrq, @Param("rybh") String rybh, @Param("cznr") String cznr, @Param("jhid") String jhid);
	
	public int updataCzsj(@Param("czsj") Date czsj, @Param("id") int id);
	
	public TqJtztk checkOpenState(@Param("bbrq") Date bbrq);
	
	public LoginCheck loginCheck(@Param("bbrq") Date bbrq, @Param("jhid") String jhid);
	
	public int getMaxYgdlcs(@Param("bbrq") Date bbrq, @Param("jhid") String jhid);
	
	public int getYgdlcs(@Param("bbrq") Date bbrq, @Param("jhid") String jhid, @Param("rybh") String rybh);
	
	public int delJtztk(@Param("bbrq") Date bbrq, @Param("jhid") String jhid, @Param("rybh") String rybh);
}