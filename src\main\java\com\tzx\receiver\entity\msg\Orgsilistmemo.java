package com.tzx.receiver.entity.msg;

import java.io.Serializable;

public class Orgsilistmemo
  implements Serializable
{
  private Integer id;
  private Integer jgxh;
  private String tcbh;
  private Integer cmxh;
  private String cmbh;
  private String cmfh;
  private Double cmsl;
  private Double fzje;
  private Integer tcid;
  private Integer xmid;
  private Integer cmid;
  private String zbcmbh;

  public Integer getId()
  {
    return this.id; }

  public void setId(Integer id) {
    this.id = id; }

  public Integer getJgxh() {
    return this.jgxh; }

  public void setJgxh(Integer jgxh) {
    this.jgxh = jgxh; }

  public String getTcbh() {
    return this.tcbh; }

  public void setTcbh(String tcbh) {
    this.tcbh = tcbh; }

  public Integer getCmxh() {
    return this.cmxh; }

  public void setCmxh(Integer cmxh) {
    this.cmxh = cmxh; }

  public String getCmbh() {
    return this.cmbh; }

  public void setCmbh(String cmbh) {
    this.cmbh = cmbh; }

  public String getCmfh() {
    return this.cmfh; }

  public void setCmfh(String cmfh) {
    this.cmfh = cmfh; }

  public Double getCmsl() {
    return this.cmsl; }

  public void setCmsl(Double cmsl) {
    this.cmsl = cmsl; }

  public Double getFzje() {
    return this.fzje; }

  public void setFzje(Double fzje) {
    this.fzje = fzje; }

  public Integer getTcid() {
    return this.tcid; }

  public void setTcid(Integer tcid) {
    this.tcid = tcid; }

  public Integer getXmid() {
    return this.xmid; }

  public void setXmid(Integer xmid) {
    this.xmid = xmid; }

  public Integer getCmid() {
    return this.cmid; }

  public void setCmid(Integer cmid) {
    this.cmid = cmid; }

  public String getZbcmbh() {
    return this.zbcmbh; }

  public void setZbcmbh(String zbcmbh) {
    this.zbcmbh = zbcmbh;
  }
}