package com.tzx.receiver.entity.msg;

/**
 * TsMicvsCouponConfig
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-05-14
 */
public class TsMicvsCouponConfig {

//    id integer not null,
    private Integer id;
//    coupontitile varchar(100) null,  --冥晨独立券渠道名称 美团 抖音 快手

    private String coupontitile;
//    applchannel varchar(100) null,  --冥晨返回的独立券渠道编码 美团 抖音 快手

    private String applchannel;
//    yhfsid integer not null,  -- 优惠ID
    private Integer yhfsid;

//    yhfsmc varchar(100) null,  -- 优惠名称
    private String yhfsmc;

//    fkid integer not null,  -- 付款ID
    private Integer fkid;

//    fkfsmc varchar(100) null  -- 付款名称
    private String fkfsmc;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getCoupontitile() {
        return coupontitile;
    }

    public void setCoupontitile(String coupontitile) {
        this.coupontitile = coupontitile;
    }

    public String getApplchannel() {
        return applchannel;
    }

    public void setApplchannel(String applchannel) {
        this.applchannel = applchannel;
    }

    public Integer getYhfsid() {
        return yhfsid;
    }

    public void setYhfsid(Integer yhfsid) {
        this.yhfsid = yhfsid;
    }

    public String getYhfsmc() {
        return yhfsmc;
    }

    public void setYhfsmc(String yhfsmc) {
        this.yhfsmc = yhfsmc;
    }

    public Integer getFkid() {
        return fkid;
    }

    public void setFkid(Integer fkid) {
        this.fkid = fkid;
    }

    public String getFkfsmc() {
        return fkfsmc;
    }

    public void setFkfsmc(String fkfsmc) {
        this.fkfsmc = fkfsmc;
    }
}
