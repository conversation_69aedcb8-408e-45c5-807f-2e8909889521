<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.miniapp.rest.mapper.MiniAppTsCzykMapper">
	<select id="findLogin" resultType="com.tzx.miniapp.rest.model.TsCzyk" >
		select * from ts_czyk where czybh = #{czybh} and czymm = #{czymm}
	</select>
	<select id="findByCzybh" resultType="com.tzx.miniapp.rest.model.TsCzyk" >
		select * from ts_czyk where czybh = #{czybh}
	</select>
</mapper>
