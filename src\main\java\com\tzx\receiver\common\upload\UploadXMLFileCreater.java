package com.tzx.receiver.common.upload;

import com.tzx.publics.util.DateUtil;
import com.tzx.receiver.common.utils.DBUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.OutputFormat;
import org.dom4j.io.XMLWriter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.jdbc.support.rowset.SqlRowSetMetaData;

import java.io.File;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.sql.Types;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @Date 2019-04-22
 * @Descption
 **/
public class UploadXMLFileCreater implements IUploadFileCreater {
    private final Logger logger = LoggerFactory.getLogger(getClass());
    @Override
    public boolean createUploadFile(UploadTask uploadTask) {
        String guid = uploadTask.getGuid();
        //1、生成表态
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            SimpleDateFormat sdf2 = new SimpleDateFormat("yyyyMMddHHmmss");
//            String basePath = "../UploadCaches/" ;
//            String path = "../UploadCaches/" + sdf.format(new Date());
            String basePath = new File(System.getProperty("user.dir")).getParent()  + File.separator + "UploadCaches";
            File baseFile = new File(basePath);
            String path = basePath + File.separator + sdf.format(new Date());
            File file = new File(path);
            //文件夹不存在，则创建
            if(!file.exists()){
                file.mkdirs();
            }
            //删除以前数据
            File[] files = baseFile.listFiles();
            if (files != null){
                String firstFile = basePath + sdf.format(DateUtils.addDays(new Date(),-UploadGloVar.KEEY_FILE_DAY));
                for (File file1 : files) {
                    //比较名称比他小，就删除
                    if( file1.isDirectory()&&file1.getAbsolutePath().compareTo(firstFile)<0){
                        file1.delete();
                    }
                }
            }


            //文件名称
            String fileName = "[" + uploadTask.getParam().getCommand() + "]" + sdf2.format(new Date()) + ".xml";

            //开始编辑XML的内容
            Document document= DocumentHelper.createDocument();
//            document.setXMLEncoding("GB2312");
            Element root = document.addElement("DATAPACKET");
            //XML头内容
            Element head = root.addElement("HEAD");
            Element Version = head.addElement("Version");
            Version.addText("1");
            Element SRC = head.addElement("SRC");
            SRC.addText(uploadTask.getOrganizeId());
            Element DEVID = head.addElement("DEVID");
            DEVID.addText("");
            Element APP = head.addElement("APP");
            APP.addText("TZX-StoreBusinessSystem");
            Element TID = head.addElement("TID");
            TID.addText("TXDTASHUOCHW35DG39SG0LHHAW04YSDFGH");
            Element MSGID = head.addElement("MSGID");
            MSGID.addText("{" + UUID.randomUUID().toString().toUpperCase() + "}");
            Element CORID = head.addElement("CORID");
            CORID.addText("20051024092733000440");

            Element WORKDATE = head.addElement("WORKDATE");
            WORKDATE.addText(uploadTask.getReportDate());
            Element PERSONNEL = head.addElement("PERSONNEL");
            PERSONNEL.addText(uploadTask.getOpId());
            Element RESERVE = head.addElement("RESERVE");
            RESERVE.addText("STRING");

            //XML消息内容
            Element MSG = root.addElement("MSG");
            Element SUBSYSTEM = MSG.addElement("SUBSYSTEM");
            SUBSYSTEM.addText("CRM");
            Element ACTION = MSG.addElement("ACTION");
            if ((uploadTask.getParam().getCommand().toUpperCase().equals("KQSC"))){
                ACTION.addText("KCREALTIMEBILL");
            }else{
                ACTION.addText(uploadTask.getParam().getCommand().toUpperCase());
            }


            //开始创建数据内容
            Element DATAS = MSG.addElement("DATAS");
            //开始循环插入表结构
            for (Map.Entry<String,String> entry : uploadTask.getParam().getTables().entrySet()) {
                String table = entry.getKey();
                String alias = entry.getValue();
                Element DATA = DATAS.addElement("DATA");
                DATA.addAttribute("Name",alias);
                Element ROWDATA = DATA.addElement("ROWDATA");
                //查询表内容
                SqlRowSet dataByTable = DBUtils.getDataByTable(table);
                if(dataByTable!=null){
                    while (dataByTable.next()){
                        Element ROW = ROWDATA.addElement("ROW");
                        SqlRowSetMetaData metaData = dataByTable.getMetaData();
                        for (int i = 1; i <= metaData.getColumnCount(); i++) {
                            String columnName =  metaData.getColumnName(i).toUpperCase();
                            String columnValue = dataByTable.getString(i);
                            if(columnValue!=null&&columnName!=null){
                                //发现有些字段是个很长的空格，处理一下

                                if(metaData.getColumnType(i) == Types.DATE||metaData.getColumnType(i) == Types.TIME
                                        ||metaData.getColumnType(i) == Types.TIMESTAMP){
                                    SimpleDateFormat sdf3 = null;
                                    if (columnName.equals("BBRQ")){
                                        sdf3 = new SimpleDateFormat("yyyy-MM-dd");
                                    }else{
                                        sdf3 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                                    }

                                    columnValue =  sdf3.format( dataByTable.getDate(i)) ;
                                }else{
                                    columnValue = StringUtils.isBlank(columnValue)?"":columnValue;
                                }
                                ROW.addAttribute(columnName,columnValue);
                            }

                        }
                    }

                }


            }
            // 美化格式,并输出
            OutputFormat format = OutputFormat.createPrettyPrint();
//            format.setEncoding("GB2312");
            format.setEncoding("UTF-8");
            XMLWriter writer = new XMLWriter( new FileOutputStream( path + "/" + fileName ),format );
            writer.write( document );
            writer.close();

            File xmlFile = new File(path + "/" + fileName);
            String xmlFileName = xmlFile.getCanonicalPath();
//            xmlFileName = xmlFileName.replace("..","");
//            xmlFileName = xmlFileName.replace("//","/");
//            xmlFileName = xmlFileName.replace("\\\\","\\");
            uploadTask.setFileName(xmlFileName);
        } catch (Exception e) {
            DBUtils.updateUploadDBLog("exceptionmsg", "生成XML出错", guid);
            DBUtils.updateUploadDBLog("upprocess", "100%", guid);
            DBUtils.updateUploadDBLog("endtime", DateUtil.getNowDateYYDDMMHHMMSS(), guid);
            DBUtils.updateUploadDBLog("runresult", "2", guid);
            logger.error(e.getMessage());
            return false;
        }
        //最后设置一下文件名

        DBUtils.updateUploadDBLog("uploadfilename", uploadTask.getFileName(), uploadTask.getGuid());
        DBUtils.updateUploadDBLog("upprocess", "75%", uploadTask.getGuid());
        return true;
    }
}
