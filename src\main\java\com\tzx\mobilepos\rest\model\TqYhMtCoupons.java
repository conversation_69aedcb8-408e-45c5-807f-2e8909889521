package com.tzx.mobilepos.rest.model;

import com.tzx.publics.base.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "TQ_YHMTCOUPONSORDERS")
public class TqYhMtCoupons  extends BaseEntity implements Serializable {

	private static final long serialVersionUID = -8603235992800392545L;

	@Column(name = "YHFSID")
	private Integer yhfsid;
	
	@Column(name = "XMID")
	private Integer xmid;
	
	@Column(name = "KDZDBH")
	private String kdzdbh;
	
	@Column(name = "PAYABLEAMT")
	private BigDecimal payableamt;
	
	@Column(name = "PQLX")
	private String pqlx;
	
	@Column(name = "YZM")
	private String yzm;
	
	@Column(name = "CREATETIME")
	private Date createtime;
	
	@Column(name = "UPDATETIME")
	private Date updatetime;
	
	@Column(name = "UPDATECOUNT")
	private Integer updatecount;
	
	@Column(name = "FIRST_STATUS")
	private String firststatus;
	
	@Column(name = "LAST_STATUS")
	private String laststatus;
	
	@Column(name = "STATUS")
	private String status;
	
	@Column(name = "JZID")
	private Integer jzid;
	
	@Column(name = "ISDONE")
	private String isdone;
	
	@Column(name = "wdrwid")
	private Integer wdrwid;

	@Column(name = "sjje")
	private BigDecimal sjje;

	@Column(name = "buyprice")
	private BigDecimal buyprice;

	@Column(name = "outtradeno")
	private String outtradeno;
	
	public TqYhMtCoupons() {
		super();
	}



	public TqYhMtCoupons(Integer yhfsid, Integer xmid, String kdzdbh,
			BigDecimal payableamt, String pqlx, String yzm, Date createtime,
			Date updatetime, Integer updatecount, String firststatus,
			String laststatus, String status, Integer jzid, String isdone,
			Integer wdrwid, BigDecimal sjje) {
		super();
		this.yhfsid = yhfsid;
		this.xmid = xmid;
		this.kdzdbh = kdzdbh;
		this.payableamt = payableamt;
		this.pqlx = pqlx;
		this.yzm = yzm;
		this.createtime = createtime;
		this.updatetime = updatetime;
		this.updatecount = updatecount;
		this.firststatus = firststatus;
		this.laststatus = laststatus;
		this.status = status;
		this.jzid = jzid;
		this.isdone = isdone;
		this.wdrwid = wdrwid;
		this.sjje = sjje;
	}



	public Integer getYhfsid() {
		return yhfsid;
	}

	public void setYhfsid(Integer yhfsid) {
		this.yhfsid = yhfsid;
	}

	public Integer getXmid() {
		return xmid;
	}

	public void setXmid(Integer xmid) {
		this.xmid = xmid;
	}

	public String getKdzdbh() {
		return kdzdbh;
	}

	public void setKdzdbh(String kdzdbh) {
		this.kdzdbh = kdzdbh;
	}

	

	public BigDecimal getPayableamt() {
		return payableamt;
	}


	public void setPayableamt(BigDecimal payableamt) {
		this.payableamt = payableamt;
	}


	public String getPqlx() {
		return pqlx;
	}

	public void setPqlx(String pqlx) {
		this.pqlx = pqlx;
	}

	public String getYzm() {
		return yzm;
	}

	public void setYzm(String yzm) {
		this.yzm = yzm;
	}

	public Date getCreatetime() {
		return createtime;
	}

	public void setCreatetime(Date createtime) {
		this.createtime = createtime;
	}

	public Date getUpdatetime() {
		return updatetime;
	}

	public void setUpdatetime(Date updatetime) {
		this.updatetime = updatetime;
	}

	public Integer getUpdatecount() {
		return updatecount;
	}

	public void setUpdatecount(Integer updatecount) {
		this.updatecount = updatecount;
	}

	public String getFirststatus() {
		return firststatus;
	}

	public void setFirststatus(String firststatus) {
		this.firststatus = firststatus;
	}

	public String getLaststatus() {
		return laststatus;
	}

	public void setLaststatus(String laststatus) {
		this.laststatus = laststatus;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public Integer getJzid() {
		return jzid;
	}

	public void setJzid(Integer jzid) {
		this.jzid = jzid;
	}

	public String getIsdone() {
		return isdone;
	}

	public void setIsdone(String isdone) {
		this.isdone = isdone;
	}

	public Integer getWdrwid() {
		return wdrwid;
	}

	public void setWdrwid(Integer wdrwid) {
		this.wdrwid = wdrwid;
	}

	public BigDecimal getSjje() {
		return sjje;
	}

	public void setSjje(BigDecimal sjje) {
		this.sjje = sjje;
	}

	public BigDecimal getBuyprice() {
		return buyprice;
	}

	public void setBuyprice(BigDecimal buyprice) {
		this.buyprice = buyprice;
	}

	public String getOuttradeno() {
		return outtradeno;
	}

	public void setOuttradeno(String outtradeno) {
		this.outtradeno = outtradeno;
	}
}
