package com.tzx.publics.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class StringFilterUtil {

	/**
	 * 表情符过滤器类
	 * @param str
	 * @return
	 */
	public static String filterEmoji(String str) {
		return filterEmoji(str,"  ");
	}
	
	/**
	 * 过滤表情字符
	 * @param str
	 * @param reStr
	 * @return
	 */
	public static String filterEmoji(String str,String reStr){
		return filterSpecial(str,reStr);
	}
	
	/**
	 * 过滤除[大小写英文字母、阿拉伯数字、中文汉字、中英文标点符号、空格等]之外的字符
	 * @param str
	 * @param reStr
	 * @return
	 */
	public static String filterSpecial(String str,String reStr){
		if (str.trim().isEmpty()) {
			return str;
		}
		//保留大小写26个英文字母、0-9阿拉伯数字、中文汉字、中英文标点符号、空格等
		String pattern = "[^(a-zA-Z0-9\\u4e00-\\u9fa5\\p{P}\\p{N}+~$`^=|<>～｀＄＾＋＝｜＜＞￥×\\n\\r)]";
		//String pattern = "[^0-9a-zA-Z\u4e00-\u9fa5+~$`^=|<>～｀＄＾＋＝｜＜＞￥×\\s\\n\\r]+";
		Pattern emoji = Pattern.compile(pattern);
		Matcher emojiMatcher = emoji.matcher(str);
		str = emojiMatcher.replaceAll(reStr);
		// postgresql9.0版本"\"转义字符替换
//		if (str != null && str.contains("\\") && Constant.STANDARD_CONFORMING_STRINGS_OFF
//				.equalsIgnoreCase(XmlUtils.getNodeInfo("SQL_NOESCAPE_FLAG"))) {
//			str = str.replaceAll("\\\\", "|");
//		}
		return str;
	}
	
	/**
	 * 针对PostgreSQL9.0版本数据库"\"转义字符需要再次转义
	 * @param srcData
	 * @return
	 */
	public static String convertEscapeOfData(String srcData){
		String result = srcData;
//		if(srcData != null && XmlUtils.getNodeInfo("SQL_NOESCAPE_FLAG")!=null
//				&& XmlUtils.getNodeInfo("SQL_NOESCAPE_FLAG").equalsIgnoreCase(Constant.STANDARD_CONFORMING_STRINGS_OFF)){
//			result = srcData.replaceAll("\\\\", "\\\\\\\\");
//		}
		
		return result;
	}
	
	public static void main(String[] args){
		String str = "要汤！不要饮料！谢谢\u0000\u0000\u0000\u0000 收餐人隐私号 13140291915_3641，手机号 187****8788";
		System.out.println(str);
		System.out.println(filterEmoji(str,""));
	}
}
