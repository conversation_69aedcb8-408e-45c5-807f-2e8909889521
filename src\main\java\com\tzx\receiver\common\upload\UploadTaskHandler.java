package com.tzx.receiver.common.upload;

import com.tzx.publics.listener.InitDataListener;
import com.tzx.publics.util.DateUtil;
import com.tzx.receiver.common.utils.DBUtils;
import com.tzx.receiver.common.utils.SendJSON2RifUrl;
import com.tzx.receiver.common.utils.SendXML2RifMQ;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 * @Date 2019-04-21
 * @Descption
 **/
@Component
@Lazy(true)
public class UploadTaskHandler {
    @Value("${msg.upload.filetype}")
    private String fileType;
    private final Logger logger = LoggerFactory.getLogger(getClass());
    @Autowired
    JdbcTemplate jdbcTemplate;
    private ReentrantLock lock = new ReentrantLock();
    private boolean exeTotal(UploadTask uploadTask){
        String guid = uploadTask.getGuid();
        try {
            String sql = uploadTask.getParam().getTotalCommand();
            //Delphi 中占位符是%s,java中用的是? 所以要改一下


            SqlRowSet sqlRowSet = null;
            if(sql.indexOf("%s")!=-1){
                sql = sql.replace("%s", "?");
                sqlRowSet = jdbcTemplate.queryForRowSet(sql,
                        DateUtils.parseDate(uploadTask.getReportDate(),"yyyy-MM-dd") );
            }else{
                //原有方式执行统计过程固定的传入值是bbrq,现可支持其他参数，且支持多个
                Object[] args = null;
                //开始解析了多个不确定参数了，这里有点麻烦
                int pNumber = 0;
                List<Object> pValues = new ArrayList<>();
                String splitSql = "";
                String beforeSql = "";
                while (sql.indexOf("%")!=-1){
                    String dataType = "";
                    String pName = null;
                    int startInx = 0;
                    startInx = sql.indexOf("%") + 1;
                    int endInx = 0;
                    if(sql.indexOf(",")!=-1){
                        endInx = sql.indexOf(",");
                    }else{
                        endInx = sql.indexOf(")");
                    }

                    pName = sql.substring(startInx,endInx);
                    pName = pName.trim();
                    beforeSql = sql.substring(0, endInx + 1);
                    sql = sql.substring(endInx + 1);
                    beforeSql = beforeSql.replaceFirst("\\%" + pName,"?");
                    if(pName.lastIndexOf("#d") != -1){
                        dataType = "date";
                        pName = pName.substring(0, pName.length() - 2);
                    }
                    //再开始从任务对象中找参数值对象
                    String pMethod = "get" + pName.substring(0,1).toUpperCase()+ pName.substring(1);
                    Class uploadTaskClass = uploadTask.getClass();
                    Method getMethod = null;
                    try{
                        getMethod = uploadTaskClass.getMethod(pMethod);
                    }catch (Exception e){

                    }
                    //如果有get方法，就去get
                    if(getMethod != null){
                        if(dataType.equals("date"))
                            pValues.add(DateUtils.parseDate((String)getMethod.invoke(uploadTask), "yyyy-MM-dd HH:mm:ss"));
                        else
                            pValues.add(getMethod.invoke(uploadTask));
                    }else if(uploadTask.getExtendProperty().containsKey(pName)){
                        //get方法没有尝试去扩展属性找
                        if(dataType.equals("date"))
                            pValues.add(DateUtils.parseDate(uploadTask.getExtendProperty().get(pName),"yyyy-MM-dd HH:mm:ss"));
                        else
                            pValues.add(uploadTask.getExtendProperty().get(pName));
                    }else{
                        //还没有就执行失败
                        DBUtils.updateUploadDBLog("exceptionmsg", "解析传入参数失败。", guid);
                        DBUtils.updateUploadDBLog("upprocess", "100%", guid);
                        DBUtils.updateUploadDBLog("endtime", DateUtil.getNowDateYYDDMMHHMMSS(), guid);
                        DBUtils.updateUploadDBLog("runresult", "1", guid);
                        logger.info("没有任何营业数据，上传过程已经退出。");
                        //  DBUtils.updateUploadDBLog("upstate","结束",guid);
                        return false;
                    }
                    splitSql = splitSql + beforeSql;

                }
                splitSql = splitSql + sql;
                args = pValues.toArray();
                //开始执行SQL
                sqlRowSet = jdbcTemplate.queryForRowSet(splitSql, args );

            }


            if (sqlRowSet.next()) {
                int N = sqlRowSet.getInt(1);
                //返回值不为0，则统计出现异常
                if (N != 0) {
                    //异常的提示
                    switch (N) {
                        case 1:
                            DBUtils.updateUploadDBLog("exceptionmsg", "没有任何营业数据，上传过程已经退出。", guid);
                            DBUtils.updateUploadDBLog("upprocess", "100%", guid);
                            DBUtils.updateUploadDBLog("endtime", DateUtil.getNowDateYYDDMMHHMMSS(), guid);
                            DBUtils.updateUploadDBLog("runresult", "1", guid);
                            logger.info("没有任何营业数据，上传过程已经退出。");
                          //  DBUtils.updateUploadDBLog("upstate","结束",guid);
                            break;
                        case -4:
                            DBUtils.updateUploadDBLog("exceptionmsg", "没有统计到数据。", guid);
                            DBUtils.updateUploadDBLog("upprocess", "100%", guid);
                            DBUtils.updateUploadDBLog("endtime", DateUtil.getNowDateYYDDMMHHMMSS(), guid);
                            DBUtils.updateUploadDBLog("runresult", "1", guid);
                            logger.info("没有统计到数据。");
                           // DBUtils.updateUploadDBLog("upstate","结束",guid);
                            break;
                        case -5:
                            DBUtils.updateUploadDBLog("exceptionmsg", "已经打烊，不再执行定时上传。", guid);
                            DBUtils.updateUploadDBLog("upprocess", "100%", guid);
                            DBUtils.updateUploadDBLog("endtime", DateUtil.getNowDateYYDDMMHHMMSS(), guid);
                            DBUtils.updateUploadDBLog("runresult", "1", guid);
                            logger.info("已经打烊，不再执行定时上传。");
                         //   DBUtils.updateUploadDBLog("upstate","结束",guid);
                            break;
                        default:
                            DBUtils.updateUploadDBLog("exceptionmsg", "统计异常，返回值" + N, guid);
                            DBUtils.updateUploadDBLog("upprocess", "100%", guid);
                            DBUtils.updateUploadDBLog("endtime", DateUtil.getNowDateYYDDMMHHMMSS(), guid);
                            DBUtils.updateUploadDBLog("runresult", "2", guid);
                            logger.info("统计异常，返回值" + N);
                         //   DBUtils.updateUploadDBLog("upstate","结束",guid);
                            break;
                    }
                    return false;
                }
            } else {
                //没有返回值，则统计出错
                DBUtils.updateUploadDBLog("exceptionmsg", "统计过程出错01。", guid);
                DBUtils.updateUploadDBLog("upprocess", "100%", guid);
                DBUtils.updateUploadDBLog("endtime", DateUtil.getNowDateYYDDMMHHMMSS(), guid);
                DBUtils.updateUploadDBLog("runresult", "2", guid);
                logger.info("统计过程出错01。");
                //DBUtils.updateUploadDBLog("upstate","结束",guid);
                return false;
            }
        } catch (Exception e) {
            //执行存储过程报错，则统计出错
            DBUtils.updateUploadDBLog("exceptionmsg", "统计过程出错02。", guid);
            DBUtils.updateUploadDBLog("upprocess", "100%", guid);
            DBUtils.updateUploadDBLog("endtime", DateUtil.getNowDateYYDDMMHHMMSS(), guid);
            DBUtils.updateUploadDBLog("runresult", "2", guid);
//            logger.info("统计过程出错02。");
            logger.error("统计过程出错02。",e);
           // DBUtils.updateUploadDBLog("upstate","结束",guid);
            return false;
        }
        //几种异常都排除后，则统计成功
        DBUtils.updateUploadDBLog("upprocess", "50%", guid);
        return true;
    }

    private boolean createUploadFile(UploadTask uploadTask){
        IUploadFileCreater uploadFileCreater = null;
        if(uploadTask.getParam().getMsgType() == UploadDataType.JSON){
            uploadFileCreater = new UploadJsonFileCreater();
        }else{
            uploadFileCreater = new UploadXMLFileCreater();
        }

        return uploadFileCreater.createUploadFile(uploadTask);
    }
    public void exector(UploadTask uploadTask) {
        lock.lock();
        try {
            logger.info("开始处理任务:" + uploadTask.toString());
            String guid = uploadTask.getGuid();
            if(!(uploadTask.getIsOnlySendMQ()&&uploadTask.getParam().getCommand().equals("KCREALTIMEBILL"))) {
                //如果是POS则不允许上传
                if (DBUtils.getGGCSK("JTSX").toUpperCase().equals("POS")) {
                    DBUtils.updateUploadDBLog("exceptionmsg", "当前机台属性为POS，不做任何处理。", guid);
                    DBUtils.updateUploadDBLog("upprocess", "100%", guid);
                    DBUtils.updateUploadDBLog("upstate","完成",guid);
                    DBUtils.updateUploadDBLog("endtime", DateUtil.getNowDateYYDDMMHHMMSS(), guid);
                    DBUtils.updateUploadDBLog("runresult", "1", guid);
                    logger.info("当前机台属性为POS，不做任何处理。");
                    return;
                }
                DBUtils.insertBBSCRZK(uploadTask);

                DBUtils.updateUploadDBLog("upstate","统计数据",guid);
                //第一步执行统计过程语句 ,执行失败后，直接退出，不再执行下面的内容
                if (!exeTotal(uploadTask)) {
                    DBUtils.updateUploadDBLog("upstate","完成",guid);
                    DBUtils.updateReportTransProgres(uploadTask,1,3,-1,"执行统计过程失败");
                    return;
                }
                DBUtils.updateReportTransProgres(uploadTask,1,3,0,"执行统计过程成功");
                //第二步开始拼凑XML或JSON文件
                DBUtils.updateUploadDBLog("upstate","生成文件",guid);
                if(!createUploadFile(uploadTask)){
                    DBUtils.updateUploadDBLog("upstate","完成",guid);
                    DBUtils.updateReportTransProgres(uploadTask,2,3,-1,
                            uploadTask.getParam().getMsgType() == UploadDataType.JSON?"生成JSON文件失败":"生成XML文件失败");
                    logger.info("生成{}文件失败",uploadTask.getParam().getMsgType().equals(
                            UploadDataType.JSON)?"JSON":"XML");
                    return;
                }
                DBUtils.updateReportTransProgres(uploadTask,2,3,0,"生成XML文件成功");
            }

            //如果配置了URL就发送HTTP，否则就发送MQ
            if(StringUtils.isNotEmpty(uploadTask.getParam().getSendUrl())){
                try {
                    SendJSON2RifUrl.sendFile2MQ(uploadTask.getParam().getSendUrl(),uploadTask.getFileName());

                    DBUtils.updateUploadDBLog("exceptionmsg", "上传成功", guid);
                    DBUtils.updateUploadDBLog("upprocess", "100%", guid);
                    DBUtils.updateUploadDBLog("upstate","完成",guid);
                    DBUtils.updateUploadDBLog("endtime", DateUtil.getNowDateYYDDMMHHMMSS(), guid);
                    DBUtils.updateUploadDBLog("runresult", "0", guid);
                    DBUtils.updateReportTransProgres(uploadTask,3,3,0,"发送至MQ成功");

                } catch (Exception e) {
                    DBUtils.updateUploadDBLog("exceptionmsg", e.getMessage(), guid);
                    DBUtils.updateUploadDBLog("upprocess", "100%", guid);
                    DBUtils.updateUploadDBLog("upstate","完成",guid);
                    DBUtils.updateUploadDBLog("endtime", DateUtil.getNowDateYYDDMMHHMMSS(), guid);
                    DBUtils.updateUploadDBLog("runresult", "-1", guid);
                    DBUtils.updateReportTransProgres(uploadTask,2,3,-1,"发送至HTTP失败");
                    e.printStackTrace();
                    return;
                }
            }else{
                //第四步上传至MQ
                DBUtils.updateUploadDBLog("upstate","发送至MQ",guid);
                try {
                    SendXML2RifMQ.sendFile2MQ(uploadTask.getFileName());
                    //如果上传MQ成功，标志为3。这一天也不再传了
                    if(uploadTask.getParam().getCommand().equalsIgnoreCase("DAYEND")){
                        DBUtils.execSQL("update tq_bbscrzk set clbz = '3' where mlmc = 'DAYEND' and bbrq = '" + uploadTask.getReportDate() + "'" );
                    }
                    InitDataListener.mqStatus  = true;

                } catch (Exception e) {
                    InitDataListener.mqStatus  = false;
                    DBUtils.updateUploadDBLog("exceptionmsg", "发送至MQ失败", guid);
                    DBUtils.updateUploadDBLog("upprocess", "100%", guid);
                    DBUtils.updateUploadDBLog("upstate","完成",guid);
                    DBUtils.updateUploadDBLog("endtime", DateUtil.getNowDateYYDDMMHHMMSS(), guid);
                    DBUtils.updateUploadDBLog("runresult", "-1", guid);
                    DBUtils.updateReportTransProgres(uploadTask,2,3,-1,"发送至MQ失败");
                    e.printStackTrace();
                    return;
                }

                DBUtils.updateUploadDBLog("exceptionmsg", "上传成功", guid);
                DBUtils.updateUploadDBLog("upprocess", "100%", guid);
                DBUtils.updateUploadDBLog("upstate","完成",guid);
                DBUtils.updateUploadDBLog("endtime", DateUtil.getNowDateYYDDMMHHMMSS(), guid);
                DBUtils.updateUploadDBLog("runresult", "0", guid);
                DBUtils.updateReportTransProgres(uploadTask,3,3,0,"发送至MQ成功");
            }

        } finally {
            logger.info("处理任务完成:" + uploadTask.toString());
            lock.unlock();
        }


    }

}
