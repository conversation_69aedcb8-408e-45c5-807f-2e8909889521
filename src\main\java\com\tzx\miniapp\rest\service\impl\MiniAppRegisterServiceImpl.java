package com.tzx.miniapp.rest.service.impl;

import com.tzx.miniapp.common.Data;
import com.tzx.miniapp.rest.mapper.MiniAppTsJtsdkMapper;
import com.tzx.miniapp.rest.service.IMiniAppRegisterService;
import com.tzx.publics.util.SystemException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class MiniAppRegisterServiceImpl implements IMiniAppRegisterService {
	private final static Logger LOGGER = LoggerFactory.getLogger(MiniAppRegisterServiceImpl.class);
	
	@Autowired
	private MiniAppTsJtsdkMapper tsJtsdkMapper;

	/**
	 * 注册
	 */
	@Transactional
	public void register(Data data, Data result) throws SystemException{
//		Map<String, Object> map = ReqDataUtil.getDataMap(data);
//		List<JSONObject> dataList = new ArrayList<JSONObject>();
//		
//		String ipdz = ParamUtil.getStringValue(map, "ipdz", false, null);// ip地址
//		String jtbm = ParamUtil.getStringValue(map, "jtbm", false, null);// 机台编码
//		String jtsx = ParamUtil.getStringValue(map, "jtsx", false, null);// 机台属性
//		
//		TsJtsdk jtsdk = tsJtsdkMapper.findByIpdz(jtbm);
//		if(null != jtsdk){
//			tsJtsdkMapper.updataIpdz(ipdz, jtbm);
//			dataList.add(JSONObject.fromObject(GsonUtil.GsonString(jtsdk)));
////			result.setData(dataList);
////			result.setCode(Constant.CODE_PARAM_FAILURE);
//			result.setMsg(Constant.REGISTER_YES);
//		}else{
//			int jtbhNew = 51;
//			TsJtsdk jtbhOld = tsJtsdkMapper.getMaxJtbh(jtsx);
//			if(null != jtbhOld){
//				jtbhNew = Integer.parseInt(jtbhOld.getJtbh())  + 1;
//			}
//			TsJtsdk entity = new TsJtsdk();
//			entity.setJtbh(jtbhNew + "");
//			entity.setIpdz(ipdz);
//			entity.setJtbm(jtbm);
//			entity.setJtsx(jtsx);
//			tsJtsdkMapper.insert(entity);
//			
//			dataList.add(JSONObject.fromObject(GsonUtil.GsonString(entity)));
////			result.setData(dataList);
////			result.setCode(Constant.CODE_SUCCESS);
//			result.setMsg(Constant.REGISTER_SUCCESS);
//		}
////		result.setSuccess(true);
//		
	}

}
