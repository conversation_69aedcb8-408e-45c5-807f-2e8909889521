package com.tzx.mobilepos.rest.service.impl;

import com.tzx.publics.common.ApplicationContextUtils;
import com.tzx.mobilepos.rest.service.IMobilePosEhcacheService;
import net.sf.ehcache.Cache;
import net.sf.ehcache.Element;
import org.apache.log4j.Logger;
import org.springframework.cache.ehcache.EhCacheCacheManager;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;

@Service
public class MobilePosEhcacheServieImp implements IMobilePosEhcacheService {

	private static final Logger logger = Logger.getLogger(MobilePosEhcacheServieImp.class);
	
	@Resource(name="ehcacheManager")
	private EhCacheCacheManager springCacheManager;

	public EhCacheCacheManager getManager() {
		if(springCacheManager == null) {
			springCacheManager = ApplicationContextUtils.applicationContext.getBean(EhCacheCacheManager.class);
		}
		return springCacheManager;
	}

	private final Cache getCache(String name) {
		String[] cacheNames = getManager().getCacheManager().getCacheNames();
		if (Arrays.binarySearch(cacheNames, name) < 0) {
			logger.error("ehcache.xml 没有配置内存，【" + name + "】");
			return null;
		}
		return getManager().getCacheManager().getCache(name);
	}

	@Override
	public String getResponseJson(String uuid) {
		Cache cache = getCache(REQ_CACHE_NAME);
		if (cache == null) {
			return null;
		}
		Element element = getCache(REQ_CACHE_NAME).get(uuid);
		return element != null ? element.getObjectValue().toString() : null;
	}

	@Override
	public void addResponseJson(String uuid, String json) {
		Cache cache = getCache(REQ_CACHE_NAME);
		if (cache == null) {
			return;
		}
		getCache(REQ_CACHE_NAME).put(new Element(uuid, json));
	}

}
