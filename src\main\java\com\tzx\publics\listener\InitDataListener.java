package com.tzx.publics.listener;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import javax.servlet.ServletContext;

import com.tzx.miniapp.rest.service.IMiniAppShopBaseInfoService;
import com.tzx.receiver.entity.msg.EcoTypeDic;

import net.sf.json.JSONObject;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.context.ServletContextAware;

import com.tzx.publics.service.IInitDataService;
import com.tzx.publics.vo.DeviceVo;
import com.tzx.publics.vo.OrganVo;

@Service
public class InitDataListener implements InitializingBean, ServletContextAware {

	@Autowired
	private IInitDataService initDataService;
	@Autowired
	private IMiniAppShopBaseInfoService shopBaseInfoService;


	public static Map<String, String> ggcsMap = new HashMap<String, String>(); // 公共参数map
	public static Map<String, String> thirdMap = new HashMap<String, String>(); // third_info 参数map
	public static OrganVo organVo = new OrganVo();
	public static Map<String,EcoTypeDic> ecoTypeDicMap = new HashMap<>();
	public static Map<String, DeviceVo> deviceMap = new HashMap<String, DeviceVo>();
	public static JSONObject wyObj = new JSONObject();
	// Data\wuyeconfig.json

	public static Map<String, String> cacheMap = new ConcurrentHashMap<String, String>();
	public static volatile Boolean mqStatus = true;
	
	@Override
	public void afterPropertiesSet() throws Exception {

	}

	public void loadGlovarParamsAndOrgan() {
		//initDataService.initMq(); // 李先生全部门店更新后，代码删掉
		ggcsMap = initDataService.getGgcsMap();
		thirdMap = initDataService.getThirdMap();
		organVo = initDataService.getOrganVo();
		ecoTypeDicMap = initDataService.getEcoTypeDicMap();
		deviceMap = initDataService.getDeviceMap();
		wyObj = initDataService.getWuYeObj();

		if ("1".equals(ggcsMap.get("POS_MEMBER_TYPE"))) {
			// 更新缓存
			shopBaseInfoService.updateCache();
		}
	}

	public void refreshGlovarParamsAndOrgan() {
		loadGlovarParamsAndOrgan();
	}

	@Override
	public void setServletContext(ServletContext servletContext) {
		loadGlovarParamsAndOrgan();
	}

}
