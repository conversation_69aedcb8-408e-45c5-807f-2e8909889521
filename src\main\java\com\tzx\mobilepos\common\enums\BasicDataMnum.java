package com.tzx.mobilepos.common.enums;

/**
 * 同步数据枚举
 *
 * <AUTHOR> @Date 20180520
 */
public enum BasicDataMnum {
	// 全部
	ALL("all"),
	// 菜品
	DISH("dish"),
	// 菜品類別
	ITEM_CLASS("item_class"),
	// 套餐明细
	COMBO_DETAILS("combo_details"),
	// 套餐分组明细
	COMBO_GROUP("combo_group"),
	// 支付类型
	PAYMENT_WAY("payment_way"),
	// 折扣
	DISCOUNT("discount"), REASON("reason"),
	// 口味备注
	TASTE("taste"),

	BUSINESS_AREA("business_area"),
	//已估清菜品
	CLEAR_DISH("clear_dish"),
	// 做法
	METHOD("method"),
	// 系统参数
	SYS_PARAMETER("sys_parameter"), 
	//规格
	UNIT("unit"),
	//公共参数
	COMMON_PARAM("common_param"),
	//集团会员
	PARTY_MEMBER("party_member")
	;
	
	String value;

	BasicDataMnum(String value) {
		this.value = value;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

}
