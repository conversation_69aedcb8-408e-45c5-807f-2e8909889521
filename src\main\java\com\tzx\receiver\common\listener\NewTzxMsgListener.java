package com.tzx.receiver.common.listener;

import com.tzx.publics.base.BaseController;
import com.tzx.receiver.common.msg.MessageDispatcher;
import com.tzx.receiver.common.upload.UDPServer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2020-03-18
 * @Descption
 **/
public class NewTzxMsgListener extends BaseController implements ApplicationRunner, DisposableBean {
    protected Logger logger	= LoggerFactory.getLogger(getClass());
    @Override
    public void destroy() throws Exception {
        MessageDispatcher.listenStop();
        UDPServer.listenStop();
        logger.info("销毁工作完成...");
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        MessageDispatcher.listenStart();
        logger.info("消息监听器初始化工作完成...");
        UDPServer.listenStart();
        logger.info("UDPServer初始化工作完成...");
    }
}
