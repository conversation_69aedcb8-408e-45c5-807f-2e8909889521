package com.tzx.mobilepos.rest.mapper;

import com.tzx.publics.base.MyMapper;
import com.tzx.mobilepos.rest.model.TqYhMtCoupons;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public interface MobilePosTqYhMtCouponsMapper extends MyMapper<TqYhMtCoupons> {
	
	public int updateYhMtCouponsMapper(@Param("sjje") BigDecimal sjje, @Param("yzm") String yzm, @Param("isDone") String isDone, @Param("status") String status, @Param("datenow") Date datenow, @Param("kdzdbh") String kdzdbh, @Param("payableamt") BigDecimal payableamt, @Param("pqlx") String pqlx, @Param("xmid") Integer xmid, @Param("wdrwid") Integer wdrwid, @Param("buyprice") BigDecimal buyprice);

	public int updateYhMtCouponsMapper1(@Param("sjje") BigDecimal sjje, @Param("yzm") String yzm, @Param("isDone") String isDone, @Param("status") String status, @Param("datenow") Date datenow, @Param("kdzdbh") String kdzdbh, @Param("payableamt") BigDecimal payableamt, @Param("pqlx") String pqlx, @Param("xmid") Integer xmid, @Param("wdrwid") Integer wdrwid, @Param("buyprice") BigDecimal buyprice);

	public void deleteYhMtCouponsMapper(@Param("kdzdbh") String kdzdbh, @Param("yzm") String yzm);

	//查询美团验证码
	public List<String> findMtYzm(@Param("fkje") BigDecimal fkje, @Param("yhfsid") Integer yhfsid, @Param("status") String status, @Param("zdbh") String zdbh);

	public int updateYhMtCouponsMapperToLogin(@Param("status") String status, @Param("isdone") String isdone, @Param("zdbh") String zdbh);

	//根据验证码更新状态
	public void updateYhMtCouponsByYzm(@Param("status") String status, @Param("yzm") String yzm);

	public Integer findClid(@Param("yhsx") String yhsx);

	public void deleteYhDyCouponsMapper(@Param("kdzdbh") String kdzdbh, @Param("outtradeno") String outtradeno);

	public int updateYhDyCouponsMapper(@Param("sjje") BigDecimal sjje, @Param("yzm") String yzm, @Param("isDone") String isDone, @Param("status") String status, @Param("datenow") Date datenow, @Param("kdzdbh") String kdzdbh, @Param("payableamt") BigDecimal payableamt, @Param("pqlx") String pqlx, @Param("xmid") Integer xmid, @Param("wdrwid") Integer wdrwid, @Param("buyprice") BigDecimal buyprice, @Param("outtradeno") String outtradeno);

	public void updateDyMtCouponsByYzm(@Param("status") String status, @Param("outtradeno") String outtradeno);
}
