package com.tzx.miniapp.rest.model;

import com.tzx.publics.base.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

@Table(name = "TQ_TCDCXZLSK")
public class TqTcdcxzlsk extends BaseEntity implements Serializable {
	private static final long serialVersionUID = 1L;
	@Id
	@Column(name = "ID", insertable = false)
	private int id;
	@Column(name = "KDZDBH")
	private String kdzdbh;
	@Column(name = "TCID")
	private int tcid;
	@Column(name = "TCXH")
	private int tcxh;
	@Column(name = "MXXMID")
	private int mxxmid;
	@Column(name = "CMSL")
	private int cmsl;
	@Column(name = "XCSL")
	private int xcsl;
	@Column(name = "CMDJ")
	private double cmdj;
	@Column(name = "CMJE")
	private double cmje;
	@Column(name = "FZJE")
	private double fzje;
	@Column(name = "MXLXID")
	private int mxlxid;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getKdzdbh() {
		return kdzdbh;
	}

	public void setKdzdbh(String kdzdbh) {
		this.kdzdbh = kdzdbh;
	}

	public int getTcid() {
		return tcid;
	}

	public void setTcid(int tcid) {
		this.tcid = tcid;
	}

	public int getTcxh() {
		return tcxh;
	}

	public void setTcxh(int tcxh) {
		this.tcxh = tcxh;
	}

	public int getMxxmid() {
		return mxxmid;
	}

	public void setMxxmid(int mxxmid) {
		this.mxxmid = mxxmid;
	}

	public int getCmsl() {
		return cmsl;
	}

	public void setCmsl(int cmsl) {
		this.cmsl = cmsl;
	}

	public int getXcsl() {
		return xcsl;
	}

	public void setXcsl(int xcsl) {
		this.xcsl = xcsl;
	}

	public double getCmdj() {
		return cmdj;
	}

	public void setCmdj(double cmdj) {
		this.cmdj = cmdj;
	}

	public double getCmje() {
		return cmje;
	}

	public void setCmje(double cmje) {
		this.cmje = cmje;
	}

	public double getFzje() {
		return fzje;
	}

	public void setFzje(double fzje) {
		this.fzje = fzje;
	}

	public int getMxlxid() {
		return mxlxid;
	}

	public void setMxlxid(int mxlxid) {
		this.mxlxid = mxlxid;
	}

}
