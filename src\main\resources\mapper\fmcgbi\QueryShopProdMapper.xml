<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.fmcgbi.rest.mapper.FmcgbiQueryShopProdMapper">
	<select id="queryShopProd" resultType="com.tzx.fmcgbi.rest.vo.ProdVo">
		select * from (
			select cm.cmbh as ProdNo, cm.cmmc1 as ProdName, cm.dwbh as Unit, coalesce(tx.cmjg, cl.xmdj, cm.cmdj, 9999) as Price, '' as BarCode, 
			case when cl.clid is null then '0' else cl.clid end as CateNo, '0' as Status, case when cl.yl2 is null then '1' else '0' end as IsSell, 
			case when cl.showxh is null then 1 else cast(cl.showxh as int) end as dOrder
			from ts_cmk cm
			left join (select clo.* from tq_clmxk clo left join tq_clsdk cls on cls.id=clo.clid left join tq_cbsdk cbs on cbs.id=cls.cbid where clo.clmxlb = 'CPYSMXLB_CPXM' and (cbs.yl1 = 'TS' or ((cbs.yl1 is null or cbs.yl1 = '') and cbs.cbmc1 = '堂食'))) cl on cl.xmid = cm.cmid
			left join ts_ggcsk gg on gg.sdbt = 'FDJGBH'
			left join ts_psjgsdk jg on jg.jgbh = gg.sdnr
			left join ts_cmjgtxk tx on tx.jgtxbh = jg.jgtxbh and tx.cmid = cm.cmid
		) as a order by a.CateNo, a.dOrder
	</select>
</mapper>
