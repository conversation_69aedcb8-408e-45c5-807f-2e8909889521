package com.tzx.kvs.rest.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.tzx.kvs.rest.model.TqKvsmxk;
import com.tzx.kvs.rest.vo.KvsInfoOnlyQchVo;
import com.tzx.publics.base.MyMapper;

public interface KvsInfoMapper extends MyMapper<TqKvsmxk> {
	
	public String getLastUpdateTime();

	public List<KvsInfoOnlyQchVo> getKvsInfoOnlyQch(@Param("inF") String inF, @Param("notInF") String notInF, @Param("showSource") List<String> showSource);
	
	public int insertKvsfzk(@Param("kdzdbh") String kdzdbh, @Param("groupid") int groupid, @Param("type") String type);

	public int updateKvsChangeTime();
	
	public List<KvsInfoOnlyQchVo> getRecoverKvsInfoOnlyQch(@Param("type") String type, @Param("showCount") int showCount, @Param("timeLimit") int timeLimit);

	public int delKvsfzk(@Param("kdzdbh") String kdzdbh, @Param("groupid") int groupid, @Param("type") String type);

}
