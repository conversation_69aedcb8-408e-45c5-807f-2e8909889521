package com.tzx.miniapp.rest.service.impl;

import java.util.List;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tzx.miniapp.rest.mapper.MiniAppShopBaseInfoMapper;
import com.tzx.miniapp.rest.service.IData;
import com.tzx.miniapp.rest.vo.Shops;
import com.tzx.miniapp.rest.vo.ZsDishGroupDetailsEntity;
import com.tzx.miniapp.rest.vo.ZsDishGroupEntity;
import com.tzx.miniapp.rest.vo.ZsDishGroupVo;

/**
 * MiniAppZsSyncDish
 * 
 * <AUTHOR> 2019年06月17日
 */
public class MiniAppZsSyncDishGroup extends IData<ZsDishGroupVo> {
	MiniAppShopBaseInfoMapper shopBaseInfoMapper;

	public MiniAppZsSyncDishGroup(MiniAppShopBaseInfoMapper shopBaseInfoMapper) {
		this.shopBaseInfoMapper = shopBaseInfoMapper;
	}
	@Override
	public String getParams() throws JsonProcessingException {
		ZsDishGroupVo  dv = new ZsDishGroupVo();
		Shops shops = shopBaseInfoMapper.findShopsData();
		dv.setDataVersion(System.currentTimeMillis() + "");
		dv.setStoreId(shops.getSid() + "");
		
		List<ZsDishGroupEntity> groupList = shopBaseInfoMapper.findZsDishGroupData(shops.getSid() + "");
		for (ZsDishGroupEntity group : groupList) {
			group.setGroupDetailList(buildGroupDetail(group.getMerGroupId(), shops.getSid() + ""));
		}
		
		dv.setGroupList(groupList);
		return buildReturn(dv, 2, shopBaseInfoMapper);
	}
	
	private List<ZsDishGroupDetailsEntity> buildGroupDetail(String fzid, String sid) {
		List<ZsDishGroupDetailsEntity> groupDetailList = shopBaseInfoMapper.findZsDishGroupDetailsData(sid, Integer.parseInt(fzid));
		return groupDetailList;
	}
	
	public String getUrl() {
		return "store/syncStoreProductGroup";
	}
}
