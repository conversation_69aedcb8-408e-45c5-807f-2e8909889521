<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.miniapp.rest.mapper.MiniAppShopBaseInfoMapper">

	<select id="findTasteData" resultType="com.tzx.miniapp.rest.vo.Taste">
		select kwcm.refid as pkid, kwcm.kwid as omid, sd.kwnr1 as ordermemo
		from ts_kwcmsdk kwcm
		left join ts_kwsdk sd on sd.id = kwcm.kwid
		where kwcm.refmemo = 'CLASS_ID'
	</select>

	<select id="findDishData" resultType="com.tzx.miniapp.rest.vo.Dish">
		<!-- 		select cm.cmid as id, cm.cmid as xmid, cm.cmmc1 as name, cm.cmbh as dishsno, '' as describ, cl.memo as info,  -->
		<!-- 		case when cm.cmsx = 'CMSX_TC' then 2 else 1 end as type, true as wxDishs,  -->
		<!-- 		case when cl.clid is null then -1 else cl.clid end as pkid, '' as icon, -->
		<!-- 		'' as image, (gg2.sdnr||'/'||cm.picpath) as dishimg, 1 as min_unit, 1 as min_count,  -->
		<!-- 		1 as min_reduce, case when gq.id is not null then 1 else 0 end as soldout, 0 as isWeigh,  -->
		<!-- 		cm.dwbh as priceName, cm.dwbh as dwname, '' as priceName, coalesce(tx.cmjg, cl.xmdj, cm.cmdj, 9999) as price, -->
		<!-- 		coalesce(tx.vipprice, tx.cmjg,cl.xmdj,cm.cmdj, 9999) as vipprice, cl.clid as menuid, -1 as leftamount,  -->
		<!-- 		case when cl.kssj is null then '00:00:00' else cl.kssj end as dSaleStart,  -->
		<!-- 		case when cl.jssj is null then '23:59:59' else cl.jssj end as dSaleEnd,  -->
		<!-- 		case when cl.yl2 is null then 2 else cast(cl.yl2 as int) end as dHide,  -->
		<!-- 		case when cl.showxh is null then 1 else cast(cl.showxh as int) end as dOrder  -->
		<!-- 		from ts_cmk cm  -->
		<!-- 		left join (select clo.* from tq_clmxk clo left join tq_clsdk cls on cls.id=clo.clid left join tq_cbsdk cbs on cbs.id=cls.cbid where clo.clmxlb = 'CPYSMXLB_CPXM' and (cbs.yl1 = 'TS' or ((cbs.yl1 is null or cbs.yl1 = '') and cbs.cbmc1 = '堂食'))) cl on cl.xmid = cm.cmid  -->
		<!-- 		left join ts_ggcsk gg on gg.sdbt = 'FDJGBH'  -->
		<!-- 		left join ts_psjgsdk jg on jg.jgbh = gg.sdnr -->
		<!-- 		left join ts_cmjgtxk tx on tx.jgtxbh = jg.jgtxbh and tx.cmid = cm.cmid -->
		<!-- 		left join ts_gqk gq on cm.cmid = gq.cmid  -->
		<!-- 		left join ts_ggcsk gg2 on gg2.sdbt = 'TZXIMGWEBADD'  -->
		select * from (select cm.omp_dnid,cm.omp_dishesid,cm.cmid as id, cm.cmid as dishToppingId ,cm.cmid as xmid, cm.cmmc1 as name, cm.cmbh as dishsno, '' as describ, cl.memo as info,
		case when cm.cmsx = 'CMSX_TC' then 2 else 1 end as type, true as wxDishs,
		case when cl.clid is null then -1 else cl.clid end as pkid, '' as icon,
		'' as image, (gg2.sdnr||'/'||cm.picpath) as dishimg, 1 as min_unit, 1 as min_count,
		1 as min_reduce, case when gq.gqsl > 0 then 0 when gq.id is not null then 1 else 0 end as soldout, 0 as isWeigh,
		cm.dwbh as priceName, cm.dwbh as dwname, '' as priceName, coalesce(tx.cmjg, cl.xmdj, cm.cmdj, 9999) as price,
		coalesce(tx.vipprice, tx.cmjg,cl.xmdj,cm.cmdj, 9999) as vipprice, case when cl.clid is null then -1 else cl.clid end as menuid, -1 as leftamount,
		case when cl.kssj is null then '00:00:00' else cl.kssj end as dSaleStart,
		case when cl.jssj is null then '23:59:59' else cl.jssj end as dSaleEnd,
		case when cl.yl2 is null then 2 else cast(cl.yl2 as int) end as dHide,
		case when cl.showxh is null then 1 else cast(cl.showxh as int) end as dOrder,
		cl.ksrq, cl.jsrq, case when cl.yl3 = '' or cl.yl3 is null then '1,2,3,4,5,6,0' else cl.yl3 end as daysOfWeek,
		coalesce(cm.iffoodbox, 'N') as iffoodbox, coalesce(cm.foodboxset, -1) as foodboxset, cm.ifspec
		, COALESCE(bx.cmdj,0) box_price,
		cm.xlbh as pkno
		from vs_cmk cm
		inner join (select clo.*,cls.clbh from tq_clmxk clo left join tq_clsdk cls on cls.id=clo.clid left join tq_cbsdk cbs on cbs.id=cls.cbid where clo.clmxlb = 'CPYSMXLB_CPXM' and cbs.yl1 = #{cbh}) cl on cl.xmid = cm.cmid
		left join ts_ggcsk gg on gg.sdbt = 'FDJGBH'
		left join ts_psjgsdk jg on jg.jgbh = gg.sdnr
		left join ts_cmjgtxk tx on tx.jgtxbh = jg.jgtxbh and tx.cmid = cm.cmid
		left join ts_gqk gq on cm.cmid = gq.cmid
		left join ts_ggcsk gg2 on gg2.sdbt = 'TZXIMGWEBADD'
		inner join (select distinct xmid from (select a.xmid from tq_clmxk a, ts_cmk b where a.xmid = b.cmid and b.cmsx = 'CMSX_TC' union select cmid from ts_cmk where cmsx = 'CMSX_DP') as temptable) tc on tc.xmid = cm.cmid
		LEFT JOIN ts_cmk bx on bx.cmid=cm.foodboxset
		union
		<!-- 微生活附加菜单多小类关联表，由于小类主键属于自增字段，所以组合数据的时候，加上1000000 -->
		select  cm.omp_dnid,cm.omp_dishesid,cm.cmid as id, cm.cmid as dishToppingId, cm.cmid as xmid, cm.cmmc1 as name, cm.cmbh as dishsno, '' as describ, cl.memo as info,
		case when cm.cmsx = 'CMSX_TC' then 2 else 1 end as type, true as wxDishs,
		xcx.xlid + 1000000 as pkid, '' as icon,
		'' as image, (gg2.sdnr||'/'||cm.picpath) as dishimg, 1 as min_unit, 1 as min_count,
		1 as min_reduce, case when gq.gqsl > 0 then 0 when gq.id is not null then 1 else 0 end as soldout, 0 as isWeigh,
		cm.dwbh as priceName, cm.dwbh as dwname, '' as priceName, coalesce(tx.cmjg, cl.xmdj, cm.cmdj, 9999) as price,
		coalesce(tx.vipprice, tx.cmjg,cl.xmdj,cm.cmdj, 9999) as vipprice, xcx.xlid + 1000000 as menuid, -1 as leftamount,
		case when xcx.kssj is null then '00:00:00' else xcx.kssj end as dSaleStart,
		case when xcx.jssj is null then '23:59:59' else xcx.jssj end as dSaleEnd,
		case when xcx.sfxs is null then 1 when xcx.sfxs = 'N' then 2 else 1 end as dHide,
		case when xcx.cmxh is null then 1 else cast(xcx.cmxh as int) end as dOrder,
		xcx.ksrq, xcx.jsrq, case when xcx.weekday = '' or xcx.weekday is null then '1,2,3,4,5,6,0' else xcx.weekday end as daysOfWeek,
		coalesce(cm.iffoodbox, 'N') as iffoodbox, coalesce(cm.foodboxset, -1) as foodboxset, cm.ifspec
		, COALESCE(bx.cmdj,0) box_price,
		cm.xlbh as pkno
		from vs_cmk cm
		inner join ts_xcxflmxk xcx on cm.cmid = xcx.cmid
		left join (select clo.*,cls.clbh from tq_clmxk clo left join tq_clsdk cls on cls.id=clo.clid left join tq_cbsdk cbs on cbs.id=cls.cbid where clo.clmxlb = 'CPYSMXLB_CPXM' and (cbs.yl1 = 'TS' or ((cbs.yl1 is null or cbs.yl1 = '') and cbs.cbmc1 = '堂食'))) cl on cl.xmid = cm.cmid
		left join ts_ggcsk gg on gg.sdbt = 'FDJGBH'
		left join ts_psjgsdk jg on jg.jgbh = gg.sdnr
		left join ts_cmjgtxk tx on tx.jgtxbh = jg.jgtxbh and tx.cmid = cm.cmid
		left join ts_gqk gq on cm.cmid = gq.cmid
		left join ts_ggcsk gg2 on gg2.sdbt = 'TZXIMGWEBADD'
		LEFT JOIN ts_cmk bx on bx.cmid=cm.foodboxset
		) as tempTable order by pkid
	</select>



	<select id="findDishInfo" resultType="com.tzx.miniapp.rest.vo.Dish">
    select cm.cmid as id, cm.cmid as xmid, cm.cmmc1 as name, cm.cmbh as dishsno, '' as describ, cl.memo as info,
    case when cm.cmsx = 'CMSX_TC' then 2 else 1 end as type, true as wxDishs,
    case when cl.clid is null then -1 else cl.clid end as pkid, '' as icon,
    '' as image, (gg2.sdnr||'/'||cm.picpath) as dishimg, 1 as min_unit, 1 as min_count,
    1 as min_reduce, case when gq.id is not null then 1 else 0 end as soldout, 0 as isWeigh,
    cm.dwbh as priceName, cm.dwbh as dwname, '' as priceName, coalesce(tx.cmjg, cl.xmdj, cm.cmdj, 9999) as price,
    coalesce(tx.vipprice, tx.cmjg,cl.xmdj,cm.cmdj, 9999) as vipprice, case when cl.clid is null then -1 else cl.clid end as menuid, -1 as leftamount,
    case when cl.kssj is null then '00:00:00' else cl.kssj end as dSaleStart,
    case when cl.jssj is null then '23:59:59' else cl.jssj end as dSaleEnd,
    case when cl.yl2 is null then 2 else cast(cl.yl2 as int) end as dHide,
    case when cl.showxh is null then 1 else cast(cl.showxh as int) end as dOrder,
    cl.ksrq, cl.jsrq, case when cl.yl3 = '' or cl.yl3 is null then '1,2,3,4,5,6,0' else cl.yl3 end as daysOfWeek ,
    coalesce(cm.iffoodbox, 'N') as iffoodbox, coalesce(cm.foodboxset, -1) as foodboxset
    from ts_cmk cm
    left join (select clo.* from tq_clmxk clo left join tq_clsdk cls on cls.id=clo.clid left join tq_cbsdk cbs on cbs.id=cls.cbid where clo.clmxlb = 'CPYSMXLB_CPXM' and (cbs.yl1 = 'TS' or ((cbs.yl1 is null or cbs.yl1 = '') and cbs.cbmc1 = '堂食'))) cl on cl.xmid = cm.cmid
    left join ts_ggcsk gg on gg.sdbt = 'FDJGBH'
    left join ts_psjgsdk jg on jg.jgbh = gg.sdnr
    left join ts_cmjgtxk tx on tx.jgtxbh = jg.jgtxbh and tx.cmid = cm.cmid
    left join ts_gqk gq on cm.cmid = gq.cmid
    left join ts_ggcsk gg2 on gg2.sdbt = 'TZXIMGWEBADD'
    where cm.cmid = #{cmid}
  </select>

	<select id="findDishInfoByDishNo" resultType="com.tzx.miniapp.rest.vo.Dish">
		select cm.cmid as id, cm.cmid as xmid, cm.cmmc1 as name, cm.cmbh as dishsno, '' as describ, cl.memo as info,
			   case when cm.cmsx = 'CMSX_TC' then 2 else 1 end as type, true as wxDishs,
			   case when cl.clid is null then -1 else cl.clid end as pkid, '' as icon,
			   '' as image, (gg2.sdnr||'/'||cm.picpath) as dishimg, 1 as min_unit, 1 as min_count,
			   1 as min_reduce, case when gq.id is not null then 1 else 0 end as soldout, 0 as isWeigh,
			   cm.dwbh as priceName, cm.dwbh as dwname, '' as priceName, coalesce(tx.cmjg, cl.xmdj, cm.cmdj, 9999) as price,
			   coalesce(tx.vipprice, tx.cmjg,cl.xmdj,cm.cmdj, 9999) as vipprice, case when cl.clid is null then -1 else cl.clid end as menuid, -1 as leftamount,
			   case when cl.kssj is null then '00:00:00' else cl.kssj end as dSaleStart,
			   case when cl.jssj is null then '23:59:59' else cl.jssj end as dSaleEnd,
			   case when cl.yl2 is null then 2 else cast(cl.yl2 as int) end as dHide,
			   case when cl.showxh is null then 1 else cast(cl.showxh as int) end as dOrder,
			   cl.ksrq, cl.jsrq, case when cl.yl3 = '' or cl.yl3 is null then '1,2,3,4,5,6,0' else cl.yl3 end as daysOfWeek ,
			   coalesce(cm.iffoodbox, 'N') as iffoodbox, coalesce(cm.foodboxset, -1) as foodboxset
		from ts_cmk cm
				 left join (select clo.* from tq_clmxk clo left join tq_clsdk cls on cls.id=clo.clid left join tq_cbsdk cbs on cbs.id=cls.cbid where clo.clmxlb = 'CPYSMXLB_CPXM' and (cbs.yl1 = 'TS' or ((cbs.yl1 is null or cbs.yl1 = '') and cbs.cbmc1 = '堂食'))) cl on cl.xmid = cm.cmid
				 left join ts_ggcsk gg on gg.sdbt = 'FDJGBH'
				 left join ts_psjgsdk jg on jg.jgbh = gg.sdnr
				 left join ts_cmjgtxk tx on tx.jgtxbh = jg.jgtxbh and tx.cmid = cm.cmid
				 left join ts_gqk gq on cm.cmid = gq.cmid
				 left join ts_ggcsk gg2 on gg2.sdbt = 'TZXIMGWEBADD'
		where cm.cmbh = #{cmbh}
	</select>

	<select id="findShopsData" resultType="com.tzx.miniapp.rest.vo.Shops">
		select jg.jgxh as sid, 0 as bid, jgmc1 as shopname, jgdz as shopadd, '' as shoplocation,
		'' as lng, ''as lat, 2 as ordermode, 0 as is_bind_user, jg.jgbh as shopcode
		from ts_ggcsk gg
		left join ts_psjgsdk jg on gg.sdnr =
		jg.jgbh
		where gg.sdbt = 'FDJGBH'
	</select>

	<select id="findDishKindsData" resultType="com.tzx.miniapp.rest.vo.DishKinds">
		<!-- 只读取微信渠道下的菜品分类数据 -->
		select cl.id, cl.clmc1 as name, cl.showxh as seq, cl.clbh as dishkindsno, 0 as must,
		1 as must_seq, 0 as suggest, cl.kssj as dkSaleStart, cl.jssj as dkSaleEnd,
		cast(cl.showxh as int) as dkOrder, 1 as dkHide,
		cl.photos as icon
		from tq_clsdk cl
		INNER join tq_cbsdk cb on cb.id = cl.cbid
		where cb.yl1 = #{cbh}
		and cl.sfxs = 'Y' and cl.clsx &lt;&gt; 'CPYSSX_YH'
		union
		<!-- 微生活附加菜单多小类关联表，由于小类主键属于自增字段，所以组合数据的时候，加上1000000 -->
		select distinct 1000000 + xlid, xlmc1, xlxh, trim(to_char(1000000 + xlid, '0999999')), 0 as must, 1 as must_seq, 0 as sugges, xlkssj, xljssj, xlxh, 1 as dkHide,
		'' as icon
		from ts_xcxflmxk
	</select>

	<select id="findMainDishData" resultType="com.tzx.miniapp.rest.vo.MainDish">
		<!--    select cm.cmmc1 as name, tc.mxxmid as id, case when cl.id is null then 0 else cl.id end as duid, tc.cmsl as number, cm.cmbh as dishsno, 0 as practiceid  -->
		<!--    from ts_tcmxk tc  -->
		<!--    left join ts_cmk cm on tc.mxxmid = cm.cmid  -->
		<!--    left join tq_clmxk cl on cl.xmid = tc.mxxmid  -->
		<!--    left join tq_clsdk cls on cls.id=cl.clid -->
		<!--    left join tq_cbsdk cbs on cbs.id=cls.cbid -->
		<!--    where -->
		<!--    tc.xmid = #{xmid} and tc.mxlx = 'ERP_MXLX_SINGLE' and (cbs.yl1 = 'TS' or ((cbs.yl1 is null or cbs.yl1 = '') and cbs.cbmc1 = '堂食')) -->
		select tc.xmid,cm.cmmc1 as name, tc.mxxmid as id, case when tc.mxxmid is null then 0 else tc.mxxmid end as duid, tc.cmsl as number, cm.cmbh as dishsno, 0 as practiceid
		from ts_tcmxk tc left join ts_cmk cm on tc.mxxmid = cm.cmid
		where tc.xmid = #{xmid} and tc.mxlx = 'ERP_MXLX_SINGLE'
	</select>

	<select id="findMainDishDataExt" resultType="com.tzx.miniapp.rest.vo.MainDish">
		<!--    select cm.cmmc1 as name, tc.mxxmid as id, case when cl.id is null then 0 else cl.id end as duid, tc.cmsl as number, cm.cmbh as dishsno, 0 as practiceid  -->
		<!--    from ts_tcmxk tc  -->
		<!--    left join ts_cmk cm on tc.mxxmid = cm.cmid  -->
		<!--    left join tq_clmxk cl on cl.xmid = tc.mxxmid  -->
		<!--    left join tq_clsdk cls on cls.id=cl.clid -->
		<!--    left join tq_cbsdk cbs on cbs.id=cls.cbid -->
		<!--    where -->
		<!--    tc.xmid = #{xmid} and tc.mxlx = 'ERP_MXLX_SINGLE' and (cbs.yl1 = 'TS' or ((cbs.yl1 is null or cbs.yl1 = '') and cbs.cbmc1 = '堂食')) -->
		<!--套餐暂不支持配料功能-->
		select cm.omp_dnid,cm.omp_dishesid,tc.xmid,cm.cmmc1 as name, tc.mxxmid as id,<!--tc.mxxmid as dishToppingId,--> case when tc.mxxmid is null then 0 else tc.mxxmid end as duid, tc.cmsl as number, cm.cmbh as dishsno, 0 as practiceid
		from ts_tcmxk tc left join vs_cmk cm on tc.mxxmid = cm.cmid
		where tc.mxlx = 'ERP_MXLX_SINGLE'
	</select>

	<select id="findMandatoryDishData" resultType="com.tzx.miniapp.rest.vo.Mandatory">
    select tc.xmid, fz.fzmc1 as title, tc.mxxmid as id,tc.mxxmid as dishToppingId, tc.xcsl as
    selnum
    from ts_tcmxk tc left join ts_xmfzk fz on tc.mxxmid = fz.id
    where tc.xmid =
    #{xmid} and tc.mxlx = 'ERP_MXLX_GROUP'
  </select>

	<select id="findMandatoryDishDataExt" resultType="com.tzx.miniapp.rest.vo.Mandatory">
    select tc.xmid, fz.fzmc1 as title, tc.mxxmid as id,tc.mxxmid as dishToppingId, tc.xcsl as
    selnum
    from ts_tcmxk tc left join ts_xmfzk fz on tc.mxxmid = fz.id
    where tc.mxlx = 'ERP_MXLX_GROUP'
  </select>

	<select id="findComboItemsData" resultType="com.tzx.miniapp.rest.vo.ComboItems">
		<!-- 		select case when fzmx.fzsl = 0.5 then cm.cmmc1 || '(半份)' else cm.cmmc1 end as name, fzmx.mxid as id,  -->
		<!-- 		case when cl.id is null then 0 else cl.id end as duid, cm.cmbh as dishsno, #{selnum} as maxnum, fzmx.fzje as aprice, fzmx.fzid as rpdid -->
		<!-- 		from ts_tcfzmxk fzmx -->
		<!-- 		left join ts_cmk cm on fzmx.mxid = cm.cmid -->
		<!-- 		left join tq_clmxk cl on cl.xmid = fzmx.mxid  -->
		<!-- 		left join tq_clsdk cls on cls.id=cl.clid -->
		<!-- 		left join tq_cbsdk cbs on cbs.id=cls.cbid -->
		<!-- 		where fzmx.fzid = #{fzid}  -->
		<!-- 		and (cbs.yl1 = 'TS' or ((cbs.yl1 is null or cbs.yl1 = '') and cbs.cbmc1 = '堂食')) -->
		select case when fzmx.fzsl = 0.5 then cm.cmmc1 || '(半份)' else cm.cmmc1 end as name, fzmx.mxid as id,fzmx.mxid as dishToppingId,
		case when cm.cmid is null then 0 else cm.cmid end as duid, cm.cmbh as dishsno, #{selnum} as maxnum, fzmx.fzje as aprice, fzmx.fzid as rpdid
		from ts_tcfzmxk fzmx
		left join ts_cmk cm on fzmx.mxid = cm.cmid
		where fzmx.fzid = #{fzid}
	</select>

	<select id="findComboItemsDataExt" resultType="com.tzx.miniapp.rest.vo.ComboItems">
		<!--    select case when fzmx.fzsl = 0.5 then cm.cmmc1 || '(半份)' else cm.cmmc1 end as name, fzmx.mxid as id,  -->
		<!--    case when cl.id is null then 0 else cl.id end as duid, cm.cmbh as dishsno, #{selnum} as maxnum, fzmx.fzje as aprice, fzmx.fzid as rpdid -->
		<!--    from ts_tcfzmxk fzmx -->
		<!--    left join ts_cmk cm on fzmx.mxid = cm.cmid -->
		<!--    left join tq_clmxk cl on cl.xmid = fzmx.mxid  -->
		<!--    left join tq_clsdk cls on cls.id=cl.clid -->
		<!--    left join tq_cbsdk cbs on cbs.id=cls.cbid -->
		<!--    where fzmx.fzid = #{fzid}  -->
		<!--    and (cbs.yl1 = 'TS' or ((cbs.yl1 is null or cbs.yl1 = '') and cbs.cbmc1 = '堂食')) -->
		select cm.omp_dnid,
			   cm.omp_dishesid,
			   case when fzmx.fzsl = 0.5 then cm.cmmc1 || '(半份)' else cm.cmmc1 end as name,
			   fzmx.mxid                                                             as id,
			   fzmx.mxid                                                             as dishToppingId,
			   case when cm.cmid is null then 0 else cm.cmid end                     as duid,
			   cm.cmbh                                                               as dishsno,
			   0                                                                     as maxnum,
			   fzmx.fzje                                                             as aprice,
			   fzmx.fzid                                                             as rpdid,
			   case when fzmx.sfmr = 'Y' then 1 else 0 end                           as countx
		from ts_tcfzmxk fzmx
				 left join vs_cmk cm on fzmx.mxid = cm.cmid
		where exists (select 1
					  from tq_clmxk clo
							   inner join ts_cmk cmk on clo.xmid = cmk.cmid and cmk.cmsx = 'CMSX_DP'
							   left join tq_clsdk cls on cls.id = clo.clid
							   left join tq_cbsdk cbs on cbs.id = cls.cbid
					  where clo.clmxlb = 'CPYSMXLB_CPXM'
						and cbs.yl1 = #{cbh}
						and cmk.cmid = cm.cmid)
	</select>

	<resultMap type="com.tzx.miniapp.rest.vo.Marketing"
		id="detailMarketing">
		<id property="dsid" column="dsid" jdbcType="NUMERIC" javaType="int" />
		<result property="money" column="money" jdbcType="NUMERIC"
			javaType="int" />
		<result property="name" column="name" jdbcType="VARCHAR"
			javaType="java.lang.String" />
		<result property="submoney" column="submoney"
			jdbcType="NUMERIC" javaType="java.lang.Integer" />
		<result property="validtime" column="validtime"
			jdbcType="VARCHAR" javaType="java.lang.String" />
		<result property="applytimes" column="applytimes"
			jdbcType="VARCHAR" javaType="java.lang.String" />
		<result property="type" column="type" javaType="int" />
		<collection property="buy"
			ofType="com.tzx.miniapp.rest.vo.DishLite" select="findBuyDishesByOfferId"
			javaType="java.util.ArrayList" column="dsid" />
		<collection property="gift"
			ofType="com.tzx.miniapp.rest.vo.DishLite" select="findGiftDishesByOfferId"
			javaType="java.util.ArrayList" column="dsid" />
	</resultMap>

	<select id="selectOptionsItem" resultType="com.tzx.miniapp.rest.vo.Optional">
		SELECT cm.cmid,
			   fzc.cmmc1        "name",
			   fzc.omp_dishesid ID,
			   fzc.omp_dnid     duid,
			   fzc.cmbh         dishsno,
			   mx.cmsl          maxnum,
			   fzc.fzje         aprice,
			   fzc.fzid         rpdid,
			   NULL             "dishToppingId",
			   fzc.xlid         pkid,
			   xm.fzmc1         title,
			   0                "optionalMinnum",
			   mx.cmsl          "optionalMaxnum",
			   0                "optionalHasTip"
		FROM ts_cmk cm
				 INNER JOIN ts_tcmxk mx ON cm.cmid = mx.xmid AND mx.mxlx = 'ERP_MXLX_ANY_GROUP'
				 INNER JOIN ts_xmfzk xm ON xm.ID = mx.mxxmid
				 INNER JOIN (SELECT * FROM ts_tcfzmxk fz LEFT JOIN vs_cmk vc ON fz.mxid = vc.cmid) fzc ON fzc.fzid = xm.ID
		where exists (select 1
					  from tq_clmxk clo
							   inner join ts_cmk cmk on clo.xmid = cmk.cmid and cmk.cmsx = 'CMSX_DP'
							   left join tq_clsdk cls on cls.id = clo.clid
							   left join tq_cbsdk cbs on cbs.id = cls.cbid
					  where clo.clmxlb = 'CPYSMXLB_CPXM'
						and cbs.yl1 = #{cbh}
						and cmk.cmid = fzc.cmid)
	</select>

	<select id="findMarketingData" resultMap="detailMarketing">
		<!-- 		select yh.id -->
		<!-- 		dsid,yh.yhfsmc1 "name",tj.zdxe money,yh.yhje -->
		<!-- 		submoney, -->
		<!-- 		yh.ksrq||'—'||yh.jsrq validtime,yh.kssj||'—'||yh.jssj -->
		<!-- 		applytimes, -->
		<!-- 		case when yh.yhsx = '11' THEN '3' ELSE yh.yhsx END "type" -->
		<!-- 		from -->
		<!-- 		ts_yhfssdk yh -->
		<!-- 		left join -->
		<!-- 		ts_yhfstjk tj on tj.yhfsid = yh."id" -->
		<!-- 		where -->
		<!-- 		yh.yhsx in ('1','2','11') -->
		select sd.id as dsid,sd.yhfsmc1 as name,tj.zdxe as money,sd.zkl as submoney,
		sd.ksrq||'—'||sd.jsrq as validtime,sd.kssj||'—'||sd.jssj as applytimes, '3' as type
		from tq_clmxk cl left join ts_yhfssdk sd on cl.yhfsid = sd.id left join ts_yhfstjk tj on sd.id = tj.yhfsid
		where cl.clmxlb = 'CPYSMXLB_YHFS' and sd.yhsx = '15' and sd.zkfs = '04'
		and to_char(now(),'YYYY-MM-DD') &gt; sd.ksrq and to_char(now(),'YYYY-MM-DD') &lt; sd.jsrq
		and to_char(current_timestamp,'hh24:mi:ss') &gt; sd.kssj and to_char(current_timestamp,'hh24:mi:ss') &lt; sd.jssj
		union all
		select sd.id as dsid,sd.yhfsmc1 as name,tj.zdxe as money,sd.yhje as submoney,
		sd.ksrq||'—'||sd.jsrq as validtime,sd.kssj||'—'||sd.jssj as applytimes, '1' as type
		from tq_clmxk cl left join ts_yhfssdk sd on cl.yhfsid = sd.id left join ts_yhfstjk tj on sd.id = tj.yhfsid
		where cl.clmxlb = 'CPYSMXLB_YHFS' and sd.yhsx = '1'
		and to_char(now(),'YYYY-MM-DD') &gt; sd.ksrq and to_char(now(),'YYYY-MM-DD') &lt; sd.jsrq
		and to_char(current_timestamp,'hh24:mi:ss') &gt; sd.kssj and to_char(current_timestamp,'hh24:mi:ss') &lt; sd.jssj
	</select>

	<!-- 买赠中赠菜查询 -->
	<select id="findGiftDishesByOfferId" parameterType="int"
			resultType="com.tzx.miniapp.rest.vo.DishLite">
		SELECT dish.cmbh dishsno,dish.cmid normsid,MX.xmdj num FROM
		ts_yhfsmxk mx
		LEFT JOIN ts_yhfssdk fs on fs."id" = MX.yhfsid
		LEFT JOIN
		ts_cmk dish on mx.xmid = dish.cmid
		where mx.YL1 = 'Y' and fs.yhsx
		in('11') and fs.id = #{dsid}
	</select>

	<!-- 买赠中买的菜查询 -->
	<select id="findBuyDishesByOfferId" parameterType="int"
			resultType="com.tzx.miniapp.rest.vo.DishLite">
		SELECT dish.cmbh dishsno,dish.cmid normsid,MX.xmdj num FROM
		ts_yhfsmxk mx
		LEFT JOIN ts_yhfssdk fs on fs."id" = MX.yhfsid
		LEFT JOIN
		ts_cmk dish on mx.xmid = dish.cmid
		where mx.YL1 = 'N' and fs.yhsx
		in('11') and fs.id = #{dsid}
	</select>

	<!-- 查询数量 -->
	<select id="findTableCount" resultType="Integer">
		select cast(jg.zwsl as int) as zwsl from ts_psjgsdk jg ,(select * from ts_ggcsk where sdbt = 'FDJGBH') gg where jg.jgbh = gg.sdnr
	</select>
	
	
	<select id="findZsDishData" resultType="com.tzx.miniapp.rest.vo.ZsDishInfoEntity">
		select case when cl.showxh is null then 1 else cast(cl.showxh as int) end as sort,
		case when cl.kssj is null then '000000' else replace(cl.kssj,':','') end as sellStartTime,
		case when cl.jssj is null then '235959' else replace(cl.jssj,':','') end as sellEndTime,
		#{sid} || '_' || cm.cmid as dishId, cm.cmid as merDishId, cm.cmmc1 as name, cm.pydm as spellCode,
		case when cl.clid is null then #{sid} || '_' || cm.xlid else #{sid} || '_' || cl.clid end as pordCategory, cl.memo as description, 
<!-- 		(gg2.sdnr||'/'||cm.picpath) as imageUrl,  -->
		cm.picpath as imageUrl, 
<!-- 		1 as status, case when cl.yl2 is null then 0 else cast(cl.yl2 as int) end as isOpen, -->
		1 as status, case when cl.yl2 is null then 0 when cl.yl2 = '2' then 0 else cast(cl.yl2 as int) end as isOpen, 
		1 as incrementUnit, 1 as minOrderCount, 0 as isSingleSale, 0 as isBatching, 
		0 as isSpecialty,b.kwnr1 as tasteList, 0 as isNeedConfirmFoodNumber, 0 as takeawayTag, 0 as workingLunchTag, 
		'' as foodEnName, 0 as isAutoAdd, 1 as isCanRefund, 0 as tasteIsRequired, 1 as tasteIsMultiple, 
		0 as makingMethodIsRequired, 1 as makingMethodIsMultiple, case when cm.cmsx = 'CMSX_TC' then 2 else 1 end as type, 
		coalesce(tx.cmjg, cl.xmdj, cm.cmdj, 9999) as price, cm.dwbh as dwname, 
<!-- 		coalesce(tx1.cmjg, 0) as takeoutPackagingFee  -->
		case when cm.iffoodbox &lt;&gt; 'Y' then coalesce(tx1.cmjg * 100, 0) else 0 end as takeoutPackagingFee, cm.cmbh as code 
		from ts_cmk cm
		left join (select clo.* from tq_clmxk clo left join tq_clsdk cls on cls.id=clo.clid left join tq_cbsdk cbs on cbs.id=cls.cbid where clo.clmxlb = 'CPYSMXLB_CPXM' and (cbs.yl1 = 'TS' or ((cbs.yl1 is null or cbs.yl1 = '') and cbs.cbmc1 = '堂食'))) cl on cl.xmid = cm.cmid
		left join ts_ggcsk gg on gg.sdbt = 'FDJGBH'
		left join ts_psjgsdk jg on jg.jgbh = gg.sdnr
		left join ts_cmjgtxk tx on tx.jgtxbh = jg.jgtxbh and tx.cmid = cm.cmid 
<!-- 		left join ts_ggcsk gg2 on gg2.sdbt = 'TZXIMGWEBADD' -->
		left join ts_cmjgtxk tx1 on tx1.jgtxbh = jg.jgtxbh and tx1.cmid = cm.foodboxset 
		left join (select a.cmid,string_agg(a.kwnr1,',') as kwnr1 from (select sd.kwnr1, cm.cmid from ts_kwcmsdk kwcm left join ts_kwsdk sd on sd.id = kwcm.kwid left join ts_cmk cm on cm.xlid = kwcm.refid where kwcm.refmemo = 'CLASS_ID' union 
			select sd.kwnr1, cm.cmid from ts_kwcmsdk kwcm left join ts_kwsdk sd on sd.id = kwcm.kwid left join ts_cmk cm on cm.cmid = kwcm.refid where kwcm.refmemo = 'ITEM_ID') a group by a.cmid order by a.cmid) b on b.cmid = cm.cmid 
			inner join (select distinct xmid from (select a.xmid from tq_clmxk a, ts_cmk b where a.xmid = b.cmid and b.cmsx = 'CMSX_TC' union select cmid from ts_cmk where cmsx = 'CMSX_DP') as temptable) tc on tc.xmid = cm.cmid
		where cm.cmsx = #{cmsx}
	</select>
	
	<select id="findZsDishKindsData" resultType="com.tzx.miniapp.rest.vo.ZsDishTypeInfoEntity">
<!-- 		select #{sid} || '_' || cl.id as categoryId, cl.id as merCategoryId, '' as parentCategoryId, 1 as status, cl.clmc1 as name, cast(cl.showxh as int) as sort  -->
<!-- 		from tq_clsdk cl left join tq_cbsdk cb on cb.id = cl.cbid -->
<!-- 		where (cb.yl1 = 'TS' or ((cb.yl1 is null or cb.yl1 = '') and cb.cbmc1 = '堂食')) -->
<!-- 		and cl.sfxs = 'Y' and cl.clsx &lt;&gt; 'CPYSSX_YH' -->
		select #{sid} || '_' || cl.id as categoryId, cl.id as merCategoryId, '' as parentCategoryId, 1 as status, cl.clmc1 as name, cast(cl.showxh as int) as sort 
		from tq_clsdk cl left join tq_cbsdk cb on cb.id = cl.cbid
		where (cb.yl1 = 'TS' or ((cb.yl1 is null or cb.yl1 = '') and cb.cbmc1 = '堂食'))
		and cl.sfxs = 'Y' and cl.clsx &lt;&gt; 'CPYSSX_YH'
		union 
		select #{sid} || '_' || xl.id as categoryId, xl.id as merCategoryId, '' as parentCategoryId, 0 as status, xl.xlmc1 as name, 127 as sort 
		from ts_xlk xl where xl.id not in 
		(select cl.id from tq_clsdk cl left join tq_cbsdk cb on cb.id = cl.cbid
		where (cb.yl1 = 'TS' or ((cb.yl1 is null or cb.yl1 = '') and cb.cbmc1 = '堂食'))
		and cl.sfxs = 'Y' and cl.clsx &lt;&gt; 'CPYSSX_YH')
	</select>
	
	<select id="findZsDishGroupData" resultType="com.tzx.miniapp.rest.vo.ZsDishGroupEntity">
		select fz.id as itemGroupCode, #{sid} || '_' || fz.id as groupId, fz.id as merGroupId, fz.fzmc1 as itemGroupName , 
		fz.wbdm as fiveCode, fz.pydm as phoneticCode, fz.memo as remark, 1 as validState, 0 as itemGroupPrice  
		from ts_xmfzk fz
	</select>
	
	<select id="findZsDishPackDetailsData" resultType="com.tzx.miniapp.rest.vo.ZsDishPackDetailsEntity">
		select case when tc.mxlx= 'ERP_MXLX_SINGLE' then 1 else 2 end as type, #{sid} || '_' || tc.mxxmid as detailsId, 
		#{sid} || '_' || tc.mxxmid as unitId, case when tc.mxlx= 'ERP_MXLX_ANY_GROUP' then 0 else 1 end as changeNumState,
<!-- 		case when tc.mxlx= 'ERP_MXLX_SINGLE' then tc.cmsl else 1 end as prodCount, -->
<!-- 		case when tc.mxlx= 'ERP_MXLX_SINGLE' then -1 else 3 end as groupnum,  -->
		case when tc.mxlx= 'ERP_MXLX_SINGLE' then tc.cmsl else fz.selnum end as prodCount,
		tc.cmsl as selnum
		from ts_tcmxk tc left join (select fzid,count(id) as selnum from ts_tcfzmxk group by fzid) fz on fz.fzid = tc.mxxmid
		where tc.xmid = #{xmid} 
	</select>
	
	<select id="findZsDishGroupDetailsData" resultType="com.tzx.miniapp.rest.vo.ZsDishGroupDetailsEntity">		
		select case when mx.sfmr = 'Y' then 1 else 0 end as isdefault, #{sid} || '_' || mx.mxid as dishId, 
		#{sid} || '_' || mx.mxid as unitId, mx.fzje * 100 as makeupMoney, mx.fzsl as quantityLimit 
		from ts_tcfzmxk mx where mx.fzid = #{fzid} 
	</select>

	<!-- 查询菜品口味备注（新） -->
	<select id="findItemTasteList" resultType="com.tzx.miniapp.rest.vo.Taste">
		select distinct a.itemid as pkid, string_agg(a.omid || '_' ||a.ordermemo,',') as ordermemo 
		from (select kwcm.kwid as omid, sd.kwnr1 as ordermemo, cm.cmid as itemid from ts_kwcmsdk kwcm
		left join ts_kwsdk sd on sd.id = kwcm.kwid left join ts_cmk cm on cm.xlid = kwcm.refid
		where kwcm.refmemo = 'CLASS_ID' union 
		select kwcm.kwid as omid, sd.kwnr1 as ordermemo, cm.cmid as itemid from ts_kwcmsdk kwcm
		left join ts_kwsdk sd on sd.id = kwcm.kwid left join ts_cmk cm on cm.cmid = kwcm.refid
		where kwcm.refmemo = 'ITEM_ID') a group by a.itemid order by itemid
	</select>
	
	<!-- 查询菜品口味备注（比新的还新） -->
	<select id="findItemTasteListNew" resultType="com.tzx.miniapp.rest.vo.Taste">
		select distinct b.itemid as pkid, string_agg(b.title || '_' || b.omid || '_' || b.ordermemo || '_' || b.aprice || '_' || b.titleid || '_' || 
		 (case when b.sfmr = 'Y' then 1 else 2 end) || '_' || (case when b.sfbx = 'Y' then 1 else 2 end) || '_' || 2 || '_' || b.minnum || '_' || b.maxnum || '_' || b.detailid, ',') as ordermemo from 
		(select lx.id as titleid, lx.kwmc as title, lx.sfbx, lx.minnum, lx.maxnum, a.*,coalesce(tx.cmjg, cmk.cmdj, 0) as aprice from 
		(select sd.lxid, cm.cmid as itemid, kwcm.kwid as omid, sd.kwnr1 as ordermemo, coalesce(sd.itemid, 0) as detailid, sd.sfmr, sd.sort from ts_kwcmsdk kwcm
		left join ts_kwsdk sd on sd.id = kwcm.kwid left join ts_cmk cm on cm.xlid = kwcm.refid where kwcm.refmemo = 'CLASS_ID' union 
		select sd.lxid, cm.cmid as itemid, kwcm.kwid as omid, sd.kwnr1 as ordermemo, coalesce(sd.itemid, 0) as detailid, sd.sfmr, sd.sort from ts_kwcmsdk kwcm
		left join ts_kwsdk sd on sd.id = kwcm.kwid left join ts_cmk cm on cm.cmid = kwcm.refid where kwcm.refmemo = 'ITEM_ID') a 
		left join ts_kwlxsdk lx on lx.id = a.lxid left join ts_cmk cmk on cmk.cmid = a.detailid left join ts_ggcsk gg on gg.sdbt = 'FDJGBH' 
		left join ts_psjgsdk jg on jg.jgbh = gg.sdnr left join ts_cmjgtxk tx on tx.jgtxbh = jg.jgtxbh and tx.cmid = a.detailid order by a.itemid,lx.sort,lx.ID,a.sort,a.omid) b group by b.itemid order by b.itemid
	</select>
	
	<!-- 第三方信息表 -->
	<select id="getTthird" resultType="java.lang.String">
		select thirdvalue from ts_third_info where thirdtype = #{thirdtype} and thirdcode = #{thirdcode}  limit 1
	</select>
	
	<select id="findItemInfoSpecDataExt" resultType="com.tzx.miniapp.rest.vo.ItemInfoSpec">
		select b.cmsx as iscombo, a.itemid,r.omp_id as duid, b.cmmc1 as itemname, coalesce(c.cmjg,b.cmdj) as price,
		coalesce(c.vipprice,c.cmjg,b.cmdj) as memberprice, a.specitemname as name
		from tq_iteminfospec a inner join ts_cmk b on a.specitemid = b.cmid
		left join ts_ggcsk gg on gg.sdbt = 'FDJGBH'
		left join ts_psjgsdk jg on jg.jgbh = gg.sdnr
		left join ts_cmjgtxk c on b.cmid = c.cmid and c.jgtxbh = jg.jgtxbh
	    left join omp_boh_id_ref r on r.boh_id=a.id
		where b.cmsx = 'CMSX_DP'
		and r.name='spec';
	</select>
	
	<select id="pgHasTable" resultType="java.lang.Boolean">
		select pg_has_table(#{table}) as exi
	</select>

	<!--<select id="findDishDataWithTopping" resultType="com.tzx.miniapp.rest.vo.Dish">
		select * from (select cm.cmid as id, cm.cmid as xmid, cm.cmmc1 as name, cm.cmbh as dishsno, '' as describ, cl.memo as info,
		case when cm.cmsx = 'CMSX_TC' then 2 else 1 end as type, true as wxDishs,
		case when cl.clid is null then -1 else cl.clid end as pkid, '' as icon,
		'' as image, (gg2.sdnr||'/'||cm.picpath) as dishimg, 1 as min_unit, 1 as min_count,
		1 as min_reduce, case when gq.gqsl > 0 then 0 when gq.id is not null then 1 else 0 end as soldout, 0 as isWeigh,
		cm.dwbh as priceName, cm.dwbh as dwname, '' as priceName, coalesce(tx.cmjg, cl.xmdj, cm.cmdj, 9999) as price,
		coalesce(tx.vipprice, tx.cmjg,cl.xmdj,cm.cmdj, 9999) as vipprice, case when cl.clid is null then -1 else cl.clid end as menuid, -1 as leftamount,
		case when cl.kssj is null then '00:00:00' else cl.kssj end as dSaleStart,
		case when cl.jssj is null then '23:59:59' else cl.jssj end as dSaleEnd,
		case when cl.yl2 is null then 2 else cast(cl.yl2 as int) end as dHide,
		case when cl.showxh is null then 1 else cast(cl.showxh as int) end as dOrder,
		cl.ksrq, cl.jsrq, case when cl.yl3 = '' or cl.yl3 is null then '1,2,3,4,5,6,0' else cl.yl3 end as daysOfWeek,
		coalesce(cm.iffoodbox, 'N') as iffoodbox, coalesce(cm.foodboxset, -1) as foodboxset, cm.ifspec
		from ts_cmk cm
		left join (select clo.* from tq_clmxk clo left join tq_clsdk cls on cls.id=clo.clid left join tq_cbsdk cbs on cbs.id=cls.cbid where clo.clmxlb = 'CPYSMXLB_CPXM' and (cbs.yl1 = 'TS' or ((cbs.yl1 is null or cbs.yl1 = '') and cbs.cbmc1 = '堂食'))) cl on cl.xmid = cm.cmid
		left join ts_ggcsk gg on gg.sdbt = 'FDJGBH'
		left join ts_psjgsdk jg on jg.jgbh = gg.sdnr
		left join ts_cmjgtxk tx on tx.jgtxbh = jg.jgtxbh and tx.cmid = cm.cmid
		left join ts_gqk gq on cm.cmid = gq.cmid
		left join ts_ggcsk gg2 on gg2.sdbt = 'TZXIMGWEBADD'
		inner join (select distinct xmid from (select a.xmid from tq_clmxk a, ts_cmk b where a.xmid = b.cmid and b.cmsx = 'CMSX_TC' union select cmid from ts_cmk where cmsx = 'CMSX_DP') as temptable) tc on tc.xmid = cm.cmid
		union
		&lt;!&ndash; 微生活附加菜单多小类关联表，由于小类主键属于自增字段，所以组合数据的时候，加上1000000 &ndash;&gt;
		select cm.cmid as id, cm.cmid as xmid, cm.cmmc1 as name, cm.cmbh as dishsno, '' as describ, cl.memo as info,
		case when cm.cmsx = 'CMSX_TC' then 2 else 1 end as type, true as wxDishs,
		xcx.xlid + 1000000 as pkid, '' as icon,
		'' as image, (gg2.sdnr||'/'||cm.picpath) as dishimg, 1 as min_unit, 1 as min_count,
		1 as min_reduce, case when gq.gqsl > 0 then 0 when gq.id is not null then 1 else 0 end as soldout, 0 as isWeigh,
		cm.dwbh as priceName, cm.dwbh as dwname, '' as priceName, coalesce(tx.cmjg, cl.xmdj, cm.cmdj, 9999) as price,
		coalesce(tx.vipprice, tx.cmjg,cl.xmdj,cm.cmdj, 9999) as vipprice, xcx.xlid + 1000000 as menuid, -1 as leftamount,
		case when xcx.kssj is null then '00:00:00' else xcx.kssj end as dSaleStart,
		case when xcx.jssj is null then '23:59:59' else xcx.jssj end as dSaleEnd,
		case when xcx.sfxs is null then 1 when xcx.sfxs = 'N' then 2 else 1 end as dHide,
		case when xcx.cmxh is null then 1 else cast(xcx.cmxh as int) end as dOrder,
		xcx.ksrq, xcx.jsrq, case when xcx.weekday = '' or xcx.weekday is null then '1,2,3,4,5,6,0' else xcx.weekday end as daysOfWeek,
		coalesce(cm.iffoodbox, 'N') as iffoodbox, coalesce(cm.foodboxset, -1) as foodboxset, cm.ifspec
		from ts_cmk cm
		inner join ts_xcxflmxk xcx on cm.cmid = xcx.cmid
		left join (select clo.* from tq_clmxk clo left join tq_clsdk cls on cls.id=clo.clid left join tq_cbsdk cbs on cbs.id=cls.cbid where clo.clmxlb = 'CPYSMXLB_CPXM' and (cbs.yl1 = 'TS' or ((cbs.yl1 is null or cbs.yl1 = '') and cbs.cbmc1 = '堂食'))) cl on cl.xmid = cm.cmid
		left join ts_ggcsk gg on gg.sdbt = 'FDJGBH'
		left join ts_psjgsdk jg on jg.jgbh = gg.sdnr
		left join ts_cmjgtxk tx on tx.jgtxbh = jg.jgtxbh and tx.cmid = cm.cmid
		left join ts_gqk gq on cm.cmid = gq.cmid
		left join ts_ggcsk gg2 on gg2.sdbt = 'TZXIMGWEBADD') as tempTable order by pkid
	</select>-->

	<select id="findToppingsData" resultType="com.tzx.miniapp.rest.vo.Toppings">
		select i.id                      tpid,
			   c.cmbh 					tpno,
			   i.ingredient_id           remarkid,
			   (case
					when coalesce(jg.id, -1) = -1 then cast(coalesce(c.cmdj, 0.00) as numeric(12, 2))
					else jg.cmjg end)    addprice,
			   i.show_sort               "order",
			   c.cmmc1                   tpname,
			   s.opt_id                  rcid,
			   i.ingredient_name         remarktypename,
			   CASE
				   WHEN i.min_quantity>=1 and i.max_quantity>1 THEN '0'
				   WHEN i.min_quantity=1 and i.max_quantity=1 THEN '1'
				   ELSE '2'
			   END  selectflag,
			   i.max_quantity            selectcount,
			   ''                        remarrkexplain
		from ts_ingredient_detail_info i
				 left join ts_ingredient_set_info s on i.ingredient_id = s.ingredient_id
				 LEFT JOIN ts_cmk c on i.item_id = c.cmid
				 left join ts_cmjgtxk jg on c.cmid = jg.cmid
				 LEFT join (select jg.jgtxbh
							from ts_psjgsdk jg
									 LEFT JOIN ts_ggcsk gc
											   on jg.jgbh = gc.sdnr
							where gc.sdbt = 'FDJGBH') csk
						   on jg.jgtxbh = csk.jgtxbh

	</select>

	<select id="getOmpIdByBohId" resultType="java.lang.Integer">
		select omp_id from OMP_BOH_ID_REF where name=#{name} and boh_id=#{boh_id}
	</select>

	<!--<select id="findDishDataWithTopping" resultType="com.tzx.miniapp.rest.vo.Dish">

	'select distinct PID,dishname,cmid,min_quantity,max_quantity,show_sort,dishprice from (' + #13#10 +
	//&#45;&#45; 主菜小类对应的配料
	'select a.id as PID, d.cmmc1 as dishname, d.cmid , a.min_quantity,a.max_quantity ,a.show_sort,'  +  #13#10 +
	' (case when coalesce(jg.id,-1) = -1 then cast(coalesce(d.cmdj,0.00) as numeric(12,2))  else jg.cmjg end) as dishprice ' + #13#10 +
	' from ts_ingredient_detail_info a ' + #13#10 +
	'inner join ( ' + #13#10 +
	'select b.ingredient_id from ts_cmk a  ' + #13#10 +
	'inner join ts_ingredient_set_info b on a.xlid = b.opt_id and lower(b.opt_type)=''class_id'' and a.cmid = '+aItemsID+' ' + #13#10 +
	')b on a.ingredient_id = b.ingredient_id ' + #13#10 +
	'left join ts_cmk d on a.item_id = d.cmid ' + #13#10 +
	'left join ts_cmjgtxk jg on d.cmid =  jg.cmid and jg.jgtxbh = '''+JGTXBH+''' ' + #13#10 +

	'union all ' + #13#10 +
	// &#45;&#45; 主菜id 对应的配料
	'select a.id as PID, d.cmmc1 as dishname, d.cmid , a.min_quantity,a.max_quantity ,a.show_sort,  ' + #13#10 +
	'(case when coalesce(jg.id,-1) = -1 then cast(coalesce(d.cmdj,0.00) as numeric(12,2))  else jg.cmjg end) as dishprice  ' + #13#10 +
	'from ts_ingredient_detail_info a ' + #13#10 +
	' inner join ts_ingredient_set_info b on a.ingredient_id=b.ingredient_id ' + #13#10 +
	'  and lower(b.opt_type)=''item_id'' and b.opt_id = '+aItemsID+'  ' + #13#10 +
	'left join ts_cmk d on a.item_id = d.cmid  ' + #13#10 +
	'left join ts_cmjgtxk jg on d.cmid =  jg.cmid and jg.jgtxbh =  '''+JGTXBH+'''  ' + #13#10 +

	') a';

	</select>-->

</mapper>
