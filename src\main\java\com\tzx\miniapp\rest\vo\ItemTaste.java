package com.tzx.miniapp.rest.vo;

public class ItemTaste {
	private int omid; // 备注ID
	private int omkid; // 分组ID
	private int soldout; // 0 是沽清
	private String ordermemo; // 备注名称
	private double aprice; // 加价
	private boolean selected; // 是否默认选中
	private String detailid; // 加价菜id，0表示没有加价菜

	public int getOmid() {
		return omid;
	}

	public void setOmid(int omid) {
		this.omid = omid;
	}

	public int getOmkid() {
		return omkid;
	}

	public void setOmkid(int omkid) {
		this.omkid = omkid;
	}

	public int getSoldout() {
		return soldout;
	}

	public void setSoldout(int soldout) {
		this.soldout = soldout;
	}

	public String getOrdermemo() {
		return ordermemo;
	}

	public void setOrdermemo(String ordermemo) {
		this.ordermemo = ordermemo;
	}

	public double getAprice() {
		return aprice;
	}

	public void setAprice(double aprice) {
		this.aprice = aprice;
	}

	public boolean isSelected() {
		return selected;
	}

	public void setSelected(boolean selected) {
		this.selected = selected;
	}

	public String getDetailid() {
		return detailid;
	}

	public void setDetailid(String detailid) {
		this.detailid = detailid;
	}

}
