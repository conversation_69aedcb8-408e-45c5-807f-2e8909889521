package com.tzx.mobilepos.rest.mapper;

import com.tzx.publics.base.MyMapper;
import com.tzx.mobilepos.rest.model.TqThirdExceptOrder;
import org.apache.ibatis.annotations.Param;

/**
 *
 * <AUTHOR>
 * @since 2018-10-24
 */

public interface MobilePosTqThirdExceptOrderMapper extends MyMapper<TqThirdExceptOrder> {

	public int updateToStatus(@Param("outtradeno") String outtradeno, @Param("paystatus") String paystatus);
	
	public int updateByRefund(@Param("billid") String billid, @Param("paystatus") String paystatus);
	
	public int constraintByRefund(@Param("billid") String billid, @Param("paystatus") String paystatus);
}