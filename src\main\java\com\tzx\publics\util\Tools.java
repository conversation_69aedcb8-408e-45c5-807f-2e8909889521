package com.tzx.publics.util;


import net.sf.json.JSONObject;

import java.lang.reflect.Field;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

public class Tools
{
	
	public static boolean hv(String s)
	{
		return s == null || s.isEmpty() ? false : true;
	}
	
	public static boolean hv(List<?> l)
	{
		return l == null || l.isEmpty() ? false : true;
	}
	
	public static boolean hv(Object o)
	{
		return o == null ? false : true;
	}
	
	public static Object fromJsonToJava(JSONObject json,Class pojo) throws Exception{
		         // 首先得到pojo所定义的字段
		          Field [] fields = pojo.getDeclaredFields();
		          // 根据传入的Class动态生成pojo对象
		          Object obj = pojo.newInstance();
		          for(Field field: fields){
		              // 设置字段可访问（必须，否则报错）
		              field.setAccessible(true);
		             // 得到字段的属性名
		             String name = field.getName();
		             // 这一段的作用是如果字段在JSONObject中不存在会抛出异常，如果出异常，则跳过。
		             try{
		                     json.get(name);
		             }catch(Exception ex){
		                 continue;
		             }
		             if(json.get(name) != null && !"".equals(json.getString(name))){
	                 // 根据字段的类型将值转化为相应的类型，并设置到生成的对象中。
		                 if(field.getType().equals(Long.class) || field.getType().equals(long.class)){
		                     field.set(obj, Long.parseLong(json.getString(name)));
		                 }else if(field.getType().equals(String.class)){
		                     field.set(obj, json.getString(name));
	                 } else if(field.getType().equals(Double.class) || field.getType().equals(double.class)){
		                     field.set(obj, Double.parseDouble(json.getString(name)));
		                } else if(field.getType().equals(Integer.class) || field.getType().equals(int.class)){
		                    field.set(obj, Integer.parseInt(json.getString(name)));
		                 } else if(field.getType().equals(Date.class)){
		                     field.set(obj, Date.parse(json.getString(name)));
		                 }else{
		                     continue;
		                 }
		             }
		         }
		         return obj;
		     }
	
	public static boolean isUpdateSuccess(Object obj) {
		try {
			if ( Integer.valueOf(obj.toString()).intValue() > 0 ) {
				return true;
			}
		} catch (Exception e) {
			//ignore exception
		}
		return false;
	}
	/**
	 * 判断Object对象是NUll或empty
	 * @param obj
	 * @return
	 */
	public static boolean isNullOrEmpty(Object obj) 
	{  
        if (obj == null)  
            return true;  
  
        if (obj instanceof CharSequence)
        {
        	if (((CharSequence) obj).length() == 0)
            {
            	return true;
            }
        }
            
        if (obj instanceof Collection)  
            return ((Collection) obj).isEmpty();  
  
        if (obj instanceof Map)  
            return ((Map) obj).isEmpty();  
  
        if (obj instanceof Object[]) {  
            Object[] object = (Object[]) obj;  
            if (object.length == 0) {  
                return true;  
            }  
            boolean empty = true;  
            for (int i = 0; i < object.length; i++) {  
                if (!isNullOrEmpty(object[i])) {  
                    empty = false;  
                    break;  
                }  
            }  
            return empty;  
        }
		return false;
    }
	
	public static Integer parseInt(String obj,Integer defaultInt) throws Exception
	{
		if(isNullOrEmpty(obj))
		{
			return defaultInt;
		}
		else
		{
			return Integer.parseInt(obj);
		}
	}
	
}
