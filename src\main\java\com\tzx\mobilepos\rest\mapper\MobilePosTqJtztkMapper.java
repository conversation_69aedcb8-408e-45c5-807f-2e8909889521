package com.tzx.mobilepos.rest.mapper;

import com.tzx.publics.base.MyMapper;
import com.tzx.mobilepos.rest.model.TqJtztk;
import com.tzx.mobilepos.rest.vo.LoginCheck;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @since 2018-05-15
 */

public interface MobilePosTqJtztkMapper extends MyMapper<TqJtztk> {
	public Map<String, String> findBbrq();
	
	public TqJtztk findState(@Param("bbrq") Date bbrq, @Param("rybh") String rybh, @Param("cznr") String cznr, @Param("jhid") String jhid);
	
	public int updataCzsj(@Param("czsj") Date czsj, @Param("id") int id);
	
//	public TqJtztk checkOpenState(@Param("bbrq") Date bbrq);
	
	public LoginCheck loginCheck(@Param("bbrq") Date bbrq, @Param("jhid") String jhid);
	
	public int getMaxYgdlcs(@Param("bbrq") Date bbrq, @Param("jhid") String jhid);
	
	public int getYgdlcs(@Param("bbrq") Date bbrq, @Param("jhid") String jhid, @Param("rybh") String rybh, @Param("appLogin") String appLogin);
	
	public int delJtztk(@Param("bbrq") Date bbrq, @Param("jhid") String jhid, @Param("rybh") String rybh);

	public void deletetcdcxzlsk(@Param("bill_num") String bill_num, @Param("tcid") Integer tcid, @Param("tcxh") Integer tcxh, @Param("item_id") Integer item_id);

	public int checkOpenStart(@Param("bbrq") Date bbrq);

	public int checkOpenEnd(@Param("bbrq") Date bbrq);

	public int checkOpen(@Param("bbrq") Date bbrq, @Param("cznr") String cznr);

	public TqJtztk getJtzt(@Param("bbrq") Date bbrq, @Param("jtbh") String jtbh, @Param("czybh") String czybh, @Param("appLogin") String appLogin, @Param("appLogout") String appLogout);

	public TqJtztk getCurrentJtzt(@Param("bbrq") Date bbrq, @Param("jtbh") String jtbh, @Param("czybh") String czybh);

	public int updataLoginShiftState(@Param("bcid") String bcid, @Param("bcmc") String bcmc, @Param("jtbh") String jtbh, @Param("czybh") String czybh, @Param("dlcs") String dlcs, @Param("cznr") String cznr);

	public TqJtztk getJtztJh(@Param("bbrq") Date bbrq, @Param("jtbh") String jtbh, @Param("dlcs") String dlcs);
	
//	public TqJtztk getJtztJh(@Param("bbrq") Date bbrq, @Param("jtbh") String jtbh);
	
	public TqJtztk getJtztNew(@Param("bbrq") Date bbrq, @Param("jhid") String jhid, @Param("appLogin") String appLogin, @Param("appLogout") String appLogout);
}