<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.miniapp.rest.mapper.MiniAppTsTcmxkMapper">
	<select id="findTsTcmxkBasicData" resultType="com.tzx.miniapp.rest.vo.ComboDetails">
        select tc.id,tc.xmid as item_id,tc.cmbh as item_no,tc.cmmc as item_name,tc.mxxmid as details_id,
        cm.cmmc1 as details_name,tc.sjjg as price,tc.cmsl::bigint as item_count,tc.mxlx as details_type, cm.dwbh as item_unit_name 
        from ts_tcmxk tc left join ts_cmk cm on cm.cmid = tc.mxxmid
    </select>
</mapper>
