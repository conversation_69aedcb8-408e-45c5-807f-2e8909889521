package com.tzx.receiver.common.msg.xml;

/**
 * 
 * 数据包的过程、函数定义。
 * 
 * <AUTHOR>
 * 
 */
public class DataPacket {
	/**
	 * 去掉字符串以<code>"/"</code>结束的字符。
	 * 
	 * @param value
	 *            指定需要去掉的字符。
	 * @return 回返去掉后的字符串。
	 */
	public static String revisePrefixPath(String value) {
		String newStr = value;
		if (newStr.startsWith("/"))
			newStr = newStr.substring(1);
		return (newStr);
	}

	/**
	 * 去掉字符串以<code>"/"</code>开始的字符。
	 * 
	 * @param value
	 *            指定需要去掉的字符。
	 * @return 回返去掉后的字符串。
	 */
	public static String reviseSuffixPath(String value) {
		String newStr = value;
		if (newStr.endsWith("/"))
			newStr = newStr.substring(0, newStr.length() - 1);
		return (newStr);
	}

	/**
	 * 去掉字符串前后的"/"字符。
	 * 
	 * @param value
	 *            指定需要去掉的字符。
	 * @return 回返去掉后的字符串。
	 */
	public static String fixPath(String value) {
		String newStr = value;
		if (newStr.startsWith("/"))
			newStr = newStr.substring(1);

		if (newStr.endsWith("/"))
			newStr = newStr.substring(0, newStr.length() - 1);

		return (newStr);
	}

}
