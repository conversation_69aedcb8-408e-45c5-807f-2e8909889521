package com.tzx.mobilepos.rest.model;

import com.tzx.publics.base.BaseEntity;

import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * @since 2019-01-16
 */
@Table(name = "TS_BCK")
public class TsBck extends BaseEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	private int id;
	private String bcbh;
	private String bcmc1;
	private String bcmc2;
	private Date kssj;
	private Date jssj;
	private String bczt;
	private String cwkmbh;
	private String yl1;
	private String yl2;
	private String yl3;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getBcbh() {
		return bcbh;
	}

	public void setBcbh(String bcbh) {
		this.bcbh = bcbh;
	}

	public String getBcmc1() {
		return bcmc1;
	}

	public void setBcmc1(String bcmc1) {
		this.bcmc1 = bcmc1;
	}

	public String getBcmc2() {
		return bcmc2;
	}

	public void setBcmc2(String bcmc2) {
		this.bcmc2 = bcmc2;
	}

	public Date getKssj() {
		return kssj;
	}

	public void setKssj(Date kssj) {
		this.kssj = kssj;
	}

	public Date getJssj() {
		return jssj;
	}

	public void setJssj(Date jssj) {
		this.jssj = jssj;
	}

	public String getBczt() {
		return bczt;
	}

	public void setBczt(String bczt) {
		this.bczt = bczt;
	}

	public String getCwkmbh() {
		return cwkmbh;
	}

	public void setCwkmbh(String cwkmbh) {
		this.cwkmbh = cwkmbh;
	}

	public String getYl1() {
		return yl1;
	}

	public void setYl1(String yl1) {
		this.yl1 = yl1;
	}

	public String getYl2() {
		return yl2;
	}

	public void setYl2(String yl2) {
		this.yl2 = yl2;
	}

	public String getYl3() {
		return yl3;
	}

	public void setYl3(String yl3) {
		this.yl3 = yl3;
	}

}
