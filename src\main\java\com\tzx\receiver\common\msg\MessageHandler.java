package com.tzx.receiver.common.msg;

import com.tzx.publics.listener.InitDataListener;
import com.tzx.receiver.common.msg.xml.DPC;
import com.tzx.receiver.common.msg.xml.DataPacketBuilder;
import com.tzx.receiver.common.msg.xml.MsgDatasElement;
import com.tzx.receiver.common.msg.xml.MsgElement;
import com.tzx.receiver.common.utils.DBUtils;
import com.tzx.receiver.common.utils.DownloadUtils;
import com.tzx.receiver.common.utils.SpringContextHolder;
import com.tzx.receiver.service.MsgBaseService;
import com.tzx.receiver.service.ObsClientService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ResourceBundle;
import java.util.concurrent.TimeUnit;


public class MessageHandler implements Runnable{
	protected Logger	logger	= LoggerFactory.getLogger(getClass());
	
	private String msgTxt = null;
	
	private int tryCount = 1;
	
	private static ResourceBundle cshxfsource = null;
	
	static{
		cshxfsource = ResourceBundle.getBundle("posmsgtype");
	}
	
	public MessageHandler(String msgTxt){
		this.msgTxt = msgTxt;
	}
	
	public void doHandle(String msgData) throws Exception{
		//得到本地配置文件内容
		
		String xPath;
		DataPacketBuilder request = new DataPacketBuilder();
		request.loadFromXML(msgData);
		logger.info("解析XML文件完成");

		xPath = MsgElement.getCurrentPath() + "/ID";
		String idString = request.readString(xPath, "");
		xPath = MsgElement.getCurrentPath() + "/DATATYPE";
		String dataType = request.readString(xPath, "");
		xPath = MsgElement.getCurrentPath() + "/OPERATOR";
		String operator = request.readString(xPath, "");
		xPath = MsgElement.getCurrentPath() + "/SERVERDATA";
		String serverData = request.readString(xPath, "");
		xPath = MsgElement.getCurrentPath() + "/CHILDDATA";
		String dbName = request.readString(xPath, "");
		System.out.println("dbName = " + dbName);// add 080611
		xPath = MsgElement.getCurrentPath() + "/PRIMARY";
		String primary = request.readString(xPath, "");

		//新增下发门店机构序号 下发特殊标识
		xPath = MsgElement.getCurrentPath() + "/ZLBH";
		String zlbh = request.readString(xPath, "");
		xPath = MsgElement.getCurrentPath() + "/JGXH";
		String jgxh = request.readString(xPath, "");
		xPath = MsgElement.getCurrentPath() + "/VERSIONSTIME";
		String versionsTime = request.readString(xPath, "");
		xPath = MsgElement.getCurrentPath() + "/CSHXFBZ";
		String cshxfbz = request.readString(xPath, "");
		//下发回调地址
		xPath = MsgElement.getCurrentPath() + "/DOWNMSGURL";
		String downMsgUrl="";
		try{
			 downMsgUrl = request.readString(xPath, "");
		}catch(Exception e){
			e.printStackTrace();
		}
        logger.info("成功获取关键参数：ID=" + idString + ",DATATYPE=" + dataType +  ",OPERATOR=" + operator +
                ",SERVERDATA=" + serverData +  ",CHILDDATA=" + dbName +  ",PRIMARY=" + primary
                + ",ZLBH=" + zlbh +  ",JGXH=" + jgxh +
                ",VERSIONSTIME=" + versionsTime +  ",CSHXFBZ=" + cshxfbz +  ",DOWNMSGURL=" + downMsgUrl );

		//下发类型
		//保存下载信息跳出
		xPath = MsgElement.getCurrentPath() + "/DELIVERYMETHOD";
		String deliverymethod = request.readString(xPath, "");

		//下载地址
		xPath = MsgElement.getCurrentPath() + "/DOWNLOADURL";
		String downloadurl = request.readString(xPath, "");

		//判断下发类型
		if (StringUtils.isNotEmpty(deliverymethod) && StringUtils.equals(deliverymethod, "2")) {


			boolean mark = true;
			//失败重新下載  一共三次
			for (int i = 0; i < 3; i++) {
                String xmlstr = DownloadUtils.downLoadFromUrl(downloadurl);
                if (StringUtils.isNotEmpty(xmlstr)) {
                    mark = false;
                    //更新xml内容
                    request = new DataPacketBuilder();
                    request.loadFromXML(xmlstr);
					logger.info("重新解析XML文件完成");
                    break;
                }
            }
            if (mark) {
                logger.info("下载文件三次均失败");
                DBUtils.insertLog(downloadurl, "N");
                return;
            }
			DBUtils.insertLog(downloadurl, "Y");
        }

		//华为OBS下载
		if (StringUtils.isNotEmpty(deliverymethod) && StringUtils.equals(deliverymethod, "4")) {
			//SETINFO = SK@AK
			xPath = MsgElement.getCurrentPath() + "/SETINFO";
			String key = request.readString(xPath, "");
			assert key != null;

			boolean mark = true;
			//失败重新下載  一共三次 0,5,10
			for (int i = 0; i < 3; i++) {
				String xmlstr = ObsClientService.getContentByObjectUrl(downloadurl,key);
				if (StringUtils.isNotEmpty(xmlstr)) {
					mark = false;
					//更新xml内容
					request = new DataPacketBuilder();
					request.loadFromXML(xmlstr);
					logger.info("重新解析XML文件完成:"+xmlstr);
					break;
				}
				Thread.sleep(TimeUnit.SECONDS.toMillis(i*5));
			}
			if (mark) {
				logger.info("下载文件三次均失败");
				DBUtils.insertLog(downloadurl, "N");
				return;
			}
			DBUtils.insertLog(downloadurl, "Y");
		}

        //System.out.println("zlbh = " + zlbh);
		//System.out.println("jgxh = " + jgxh);
		//System.out.println("versionsTime = " + versionsTime);
		//System.out.println("cshxfbz = " + cshxfbz);
		//System.out.println("localcshxf = " + localcshxf);
		//判断下发标志是否一致，否则中断
		
		ResourceManager resourceManager = ResourceManager.getInstance();
		String transViewName = serverData.replace("_", "");
		String viewName = "";
		boolean isTrunct = true;
		String entityPackage = "com.tzx.receiver.entity.msg.";
		
		try {
			viewName = resourceManager.getString(transViewName);
            logger.info("当前处理数据为="+transViewName);
        } catch (Exception e) {
			logger.info("Can't find resource for bundle,该字符串在配置文件没有找到相应的key");
		}
		
		if (viewName != null && !(viewName.equalsIgnoreCase("NULL"))
				&& !(viewName.equals(""))) {
			String clientTBName = resourceManager.getString(transViewName
					+ "TBName");
			String primarykey = resourceManager.getString(transViewName + "_"
					+ primary);
			// logger.debug("开始接受：" + serverData + " 数据");
			Class clazzObj = Class.forName(entityPackage + viewName);
            logger.info("获取实体类完成");
			MsgElement requestMsg1 = new MsgElement(request, request.getNode(
					request.getRootElement(), DPC.SMsgNodeStr));
			MsgDatasElement responseDatas1 = requestMsg1.getDatas();
			List datas1 = responseDatas1.getRows(dbName, clazzObj);
            logger.info("获取实体对象集完成");
			System.out.println("clientTBName : "+clientTBName);
			if (clientTBName.indexOf("$") >= 0) {
				clientTBName = clientTBName.substring(
						clientTBName.indexOf("$") + 1, clientTBName.length());
				isTrunct = false;
			}
			MsgBaseService msgBaseService = SpringContextHolder.getBean(MsgBaseService.class);

			if (dataType.equals("FULL")) {// 初始化下发
				
				String localcshxf = cshxfsource.getString("localcshxf");
				//String url = cshxfsource.getString("url");
				if(!localcshxf.equals(cshxfbz)){
					logger.info("cshxfbz="+cshxfbz+",localcshxf = " + localcshxf+",标志不一致");
					return;
				}
				Map<String,Object> params = new HashMap<String,Object>();
				params.put("dbName", dbName);
				params.put("url", downMsgUrl);
				params.put("zlbh", zlbh);
				params.put("jgxh", jgxh);
				params.put("versionsTime", versionsTime);
				//params.put("downMsgUrl", downMsgUrl);
				msgBaseService.initxf(isTrunct, clientTBName, datas1,params);
				
			} else {// 日常下发操作
//				if ("VST_SHOPMONITOR".equals(dbName)){
					msgBaseService.dailyxf(operator, clientTBName, datas1,primarykey);
//				}
			}

			//刷新全局参数和机构信息
//			InitDataListener initDataListener = SpringContextHolder.getBean(InitDataListener.class);
//			initDataListener.refreshGlovarParamsAndOrgan();

		}
	}
	
	public void run() {
		String txt = this.msgTxt;
		int count = this.tryCount;
		boolean isComplete = false;
		while(count>0){
			try{
				doHandle(txt);
				isComplete = true;
				break;
			}catch(Throwable ex){
				//ex.printStackTrace();
				logger.error("System error", ex);
				logger.info("消息处理失败 count:"+count);
				count--;
			}
		}
		if(!isComplete){
			logger.info("MessageHandler遇到异常");
			logger.info("msgTxt : " +msgTxt);
		}	
	}

}
