package com.tzx.miniapp.rest.mapper;

import com.tzx.publics.base.MyMapper;
import com.tzx.miniapp.rest.model.TqZdk;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
 *
 * <AUTHOR> @since 2018-09-13
 */

public interface MiniAppCancelBillMapper extends MyMapper<TqZdk> {

	public TqZdk findBill(@Param("yddh") String yddh);

	public int cancelBill(@Param("ayddh") String yydh, @Param("aydrq") Date bbrq, @Param("askjh") String skjh, @Param("iygdlcs") int ygdlcs, @Param("sczry") String czry);

}