package com.tzx.miniapp.rest.service.impl;

import com.tzx.miniapp.common.Constant;
import com.tzx.miniapp.common.Data;
import com.tzx.miniapp.rest.mapper.MiniAppTqJtztkMapper;
import com.tzx.miniapp.rest.mapper.MiniAppTqZdkMapper;
import com.tzx.miniapp.rest.model.TsPsjgsdk;
import com.tzx.miniapp.rest.service.IMiniAppBillService;
import com.tzx.miniapp.rest.vo.BillMoney;
import com.tzx.miniapp.rest.vo.CalcMoney;
import com.tzx.miniapp.rest.vo.PayResultData;
import com.tzx.miniapp.rest.vo.WdDishVo;
import com.tzx.publics.util.DateUtil;
import com.tzx.publics.util.GsonUtil;
import com.tzx.publics.util.SystemException;
import net.sf.json.JSONObject;
import org.jdom2.Document;
import org.jdom2.Element;
import org.jdom2.input.SAXBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.xml.sax.InputSource;

import java.io.StringReader;
import java.util.*;

@Service
public class MiniAppBillServiceImp implements IMiniAppBillService {
	private final static Logger LOGGER = LoggerFactory.getLogger(MiniAppBillServiceImp.class);

	@Autowired
	private MiniAppTqZdkMapper tqZdkMapper;
	@Autowired
	private MiniAppTqJtztkMapper tqJtztkMapper;

	/**
	 * 创建账单
	 */
	@Transactional
	public void createBill(Data data, Data result) throws SystemException {
//		Map<String, Object> map = ReqDataUtil.getDataMap(data);
		List<JSONObject> dataList = new ArrayList<JSONObject>();

//		String jtbh = ParamUtil.getStringValue(map, "jtbh", false, null);// 机台号
		// String jtbm = ParamUtil.getStringValue(map, "jtbm", false, null);//
		// 机台编码
//		String czybh = ParamUtil.getStringValue(map, "czybh", false, null);// 操作员编号
		long kdzdbh = 0L;
		long lsdh = 0L;
//		TqZdk tqZdks = tqZdkMapper.getZdCount(jtbh);
		int ktbcid = tqZdkMapper.getBcid();
		
//		if (null != tqZdks) {
//			kdzdbh = Long.parseLong(tqZdks.getKdzdbh());
//			lsdh = Long.parseLong(tqZdks.getLsdh());
//			tqZdkMapper.initZdk(kdzdbh + "", ktbcid);
//			tqZdkMapper.delWdk(kdzdbh + "");
//		} else {
//			TsBmkzk tsBmkzKdzdbh = tqZdkMapper.getBh("TQ_ZDK", "KDZDBH");
//			String kdzdbhOld = jtbh + tsBmkzKdzdbh.getNr();
//			kdzdbh = Long.parseLong(kdzdbhOld) + 1;
//			tqZdkMapper.updateBh(subBh(jtbh, kdzdbh), "TQ_ZDK", "KDZDBH");
//			TsBmkzk tsBmkzlsdh = tqZdkMapper.getBh("TQ_ZDK", "LSDH");
//			String jtbhOld = jtbh + tsBmkzlsdh.getNr();
//			lsdh = Long.parseLong(jtbhOld) + 1;
//			tqZdkMapper.updateBh(subBh(jtbh, lsdh), "TQ_ZDK", "LSDH");
//			Map<String, String> bbrqMap = tqJtztkMapper.findBbrq();
//			String bbrq = DateUtil.getNowDateYYDDMM();
//			if (null != bbrqMap && bbrqMap.size() != 0) {
//				bbrq = bbrqMap.get("bbrq");
//			}
//
//			TqZdk tqZdk = new TqZdk();
//			tqZdk.setKdzdbh(kdzdbh + "");
//			tqZdk.setLsdh(lsdh + "");
//			tqZdk.setJzcs(0);
//			tqZdk.setKtskjh(jtbh);
//			tqZdk.setFwyh(czybh);
//			tqZdk.setKtczry(czybh);
//			tqZdk.setKtsj(new Date());
//			tqZdk.setKdbbrq(DateUtil.parseDate(bbrq));
//			tqZdk.setJzsx("ZDSX_WJ");
//			tqZdk.setSource("ANDROID");
//			tqZdk.setCbid(-1);
//			tqZdk.setXfks(1);
//			tqZdk.setDyzdcs(1);
//			tqZdk.setXsms("XSMS_TS");
//			tqZdk.setYgdlcs("1");
//			tqZdk.setZkl(100);
//			tqZdk.setYhfsbh(null);
//			tqZdk.setKtbcid(ktbcid);
//			tqZdk.setZdzt("");
//			
//			tqZdkMapper.insert(tqZdk);
//		}

		Map<String, String> mapr = new HashMap<String, String>();
		mapr.put("kdzdbh", kdzdbh + "");
		mapr.put("lsdh", lsdh + "");

		dataList.add(JSONObject.fromObject(GsonUtil.GsonString(mapr)));
//		result.setData(dataList);
//		result.setCode(Constant.CODE_SUCCESS);
		result.setMsg("生成账单成功");
//		result.setSuccess(true);

	}

	public String subBh(String jtbh, long newBh) {

		String bhStr = newBh + "";

		return bhStr.substring(jtbh.length());
	}

	/**
	 * 菜品/套餐下单
	 */
	@Transactional
	public void ordering(Data param, Data result) throws Exception {
		int result_ = 0;
		try {
			List<JSONObject> dataList = new ArrayList<JSONObject>();
			Map<String, String> bbrqMap = tqJtztkMapper.findBbrq();
			String bbrq = DateUtil.getNowDateYYDDMM();
			if (null != bbrqMap && bbrqMap.size() != 0) {
				bbrq = bbrqMap.get("bbrq");
			}
//			for (JSONObject obj : param.getData()) {
//				OrderVo orderVo = (OrderVo) JSONObject.toBean(obj, OrderVo.class);
//				JSONArray item = obj.getJSONArray("item");
//				List<ItemVo> items = (List<ItemVo>) JSONArray.toCollection(item, ItemVo.class);
//				int ygdlcs = tqJtztkMapper.getYgdlcs(DateUtil.parseDate(bbrq), orderVo.getJtbh(), orderVo.getWaiter_num());
//				tqZdkMapper.updateKtczry(orderVo.getBill_num(), orderVo.getWaiter_num(), ygdlcs);
//				for (ItemVo v : items) {
////					if (null != v.getComboDetails() && v.getComboDetails().size() > 0) {
//					if ("CMSX_TC".equals(v.getIs_combo())) {
//						result_ = tqZdkMapper.addTc(orderVo.getBill_num(), v.getItem_id(), v.getItem_count());
//						if (result_ != 0) {
//							break;
//						}
//					}
//					result_ = tqZdkMapper.addCm(orderVo.getBill_num(), v.getItem_id(), v.getItem_count(), orderVo.getJtbh(), "", v.getItem_taste(), "");
//					if (result_ != 0) {
//						break;
//					}
//				}
////				BillMoney bm = tqZdkMapper.findBillMoney(orderVo.getBill_num());
//				BillMoney bm = getBillMoney(orderVo.getBill_num());
//				dataList.add(JSONObject.fromObject(GsonUtil.GsonString(bm)));
//			}
			if (result_ == 0) {
//				result.setData(dataList);
				result.setMsg(Constant.ORDER_DISH_SUCCESS);
			} else if(result_ == -1){
				result.setMsg("没有未结账单记录");
			} else if(result_ == -2){
				result.setMsg("没有餐类明细");
			} else if(result_ == -6){
				result.setMsg("账单结账属性不正确");
			}
		} catch (Exception e) {
			e.printStackTrace();
			result_ = -1;
			result.setMsg("系统异常");
			LOGGER.error("下单报错:{}", e.getMessage());
		} finally {
//			result.setCode(result_ == 0 ? Constant.CODE_SUCCESS : -1);
//			result.setSuccess(result_ == 0);
		}
	}

	/**
	 * 优惠活动校验
	 */
	@Transactional
	public void checkDiscount(Data param, Data result) throws Exception {
		int result_ = 0;
		try {
			List<JSONObject> dataList = new ArrayList<JSONObject>();
//			for (JSONObject obj : param.getData()) {
//				OrderVo orderVo = (OrderVo) JSONObject.toBean(obj, OrderVo.class);
//				JSONArray item = obj.getJSONArray("item");
//				List<ItemVo> items = (List<ItemVo>) JSONArray.toCollection(item, ItemVo.class);
//
//				for (ItemVo v : items) {
//					String yhsx =  tqZdkMapper.getYhsx(v.getDiscount_id());
//					result_ = tqZdkMapper.yhfstj(v.getDiscount_id(), orderVo.getBill_num(), new BigDecimal(v.getItem_count()), yhsx);
//					if (result_ != 0) {
//						break;
//					}
//				}
//			}
//			result.setCode(result_);
			result.setMsg(result_ == 0 ? "优惠可用" : "优惠方式不满足限制条件！");
//			result.setSuccess(result_ == 0);
		} catch (Exception e) {
			e.printStackTrace();
			result_ = -1;
			result.setMsg("系统异常");
			LOGGER.error("验证优惠方式错误:{}", e.getMessage());
		} 
	}
	/**
	 * 使用优惠活动
	 */
	@Transactional
	public void discountOrder(Data param, Data result) throws Exception {
		int result_ = 0;
		try {
			List<JSONObject> dataList = new ArrayList<JSONObject>();
//			for (JSONObject obj : param.getData()) {
//				OrderVo orderVo = (OrderVo) JSONObject.toBean(obj, OrderVo.class);
//				JSONArray item = obj.getJSONArray("item");
//				List<ItemVo> items = (List<ItemVo>) JSONArray.toCollection(item, ItemVo.class);
//
//				for (ItemVo v : items) {
//					result_ = tqZdkMapper.addYhfs(v.getItem_id(), v.getDiscount_id(), orderVo.getBill_num(), orderVo.getJtbh(), v.getItem_count());
//					if (result_ != 0) {
//						break;
//					}
//				}
////				BillMoney bm = tqZdkMapper.findBillMoney(orderVo.getBill_num());
//				BillMoney bm = getBillMoney(orderVo.getBill_num());
//				dataList.add(JSONObject.fromObject(GsonUtil.GsonString(bm)));
//			}
			if (result_ == 0) {
//				result.setData(dataList);
				result.setMsg(Constant.DISCOUNT_SUCCESS);
			} else if(result_ == -1){
				result.setMsg("没有该优惠方式");
			} else if(result_ == -2){
				result.setMsg("没有未结账单记录");
			} 
		} catch (Exception e) {
			e.printStackTrace();
			result_ = -1;
			result.setMsg("系统异常");
			LOGGER.error("优惠报错:{}", e.getMessage());
		} finally {
//			result.setCode(result_ == 0 ? Constant.CODE_SUCCESS : -1);
//			result.setSuccess(result_ == 0);
		}
	}
	
	/**
	 * 结账
	 */
	@Transactional
	public void accountsOrder(Data param, Data result) throws Exception {
//		Map<String, Object> map = ReqDataUtil.getDataMap(param);

//		String zdbh = ParamUtil.getStringValue(map, "zdbh", false, null);// 账单号
//		int jzid = ParamUtil.getIntegerValue(map, "jzid", false, null);// 付款方式id
//		int fksl = ParamUtil.getIntegerValue(map, "fksl", false, null);// 付款数量
//		String fkje = ParamUtil.getStringValue(map, "fkje", false, null);//付款金额
//		String fkhm = ParamUtil.getStringValue(map, "fkhm", false, null);// 付款号码，扫描微信或者支付宝二维码那个号码
//		String skjh = ParamUtil.getStringValue(map, "skjh", false, null);// 收款机号，机台号
//		String skyh = ParamUtil.getStringValue(map, "skyh", false, null);// 收款人账号
		
		Date ksjzsj = new Date();
//		BillMoney bm = tqZdkMapper.findBillMoney(zdbh);
//		BigDecimal actualmoney = new BigDecimal("0.00");
//		if (bm != null) {
//			actualmoney = new BigDecimal(bm.getActual_money());
//		}
//		BillMoney billmoney = getBillMoney(zdbh);
//		BigDecimal actualmoney = new BigDecimal(fkje);
//		AccountsOrder ao = tqZdkMapper.accountsOrder(zdbh, jzid, actualmoney, fksl, fkhm, "", "", "", skjh, skyh);
//		long jzzdbhl = 0L;
//		if (ao != null && "0".equals(ao.getA())) {
//			int jzbcid = tqZdkMapper.getBcid();
//			TsBmkzk tsBmkzJzzdbh = tqZdkMapper.getBh("TQ_ZDK", "JZZDBH");
//			String jzzdbhOld = skjh + tsBmkzJzzdbh.getNr();
//			jzzdbhl = Long.parseLong(jzzdbhOld) + 1;
//			tqZdkMapper.updateBh(subBh(skjh, jzzdbhl), "TQ_ZDK", "JZZDBH");
//			String jzzdbh = jzzdbhl + "";
//			
//			//更新 tq_zdk tq_wdk tq_fklslsk
//			Map<String, String> bbrqMap = tqJtztkMapper.findBbrq();
//			String jzbbrq = DateUtil.getNowDateYYDDMM();
//			if (null != bbrqMap && bbrqMap.size() != 0) {
//				jzbbrq = bbrqMap.get("bbrq");
//			}
//			Date jzjssj = new Date();
//			tqZdkMapper.updateZdk(zdbh, jzzdbh, DateUtil.parseDate(jzbbrq), jzjssj, 1, skjh, skyh, "ZDSX_YJ", ksjzsj, jzjssj, jzbcid);
//			tqZdkMapper.updateWdk(zdbh, jzzdbh, skjh, DateUtil.parseDate(jzbbrq), jzbcid);
//			tqZdkMapper.updateFklslsk(zdbh, jzzdbh, jzbcid);
//			
//			AccountsReturnVo arv = new AccountsReturnVo();
//			BillMoney billmoney = tqZdkMapper.findBillMoney(zdbh);
			
//			List<ItemVo> discounts = tqZdkMapper.getDiscountR(zdbh);
//			List<ItemVo> items = tqZdkMapper.getItemR(zdbh);
//			for (ItemVo item : items) {
//				if("CMSX_TC".equals(item.getIs_combo())){
//					List<ComboDetails> comboDetails = tqZdkMapper.getComboDetailsR(zdbh, item.getItem_id());
//					item.setComboDetails(comboDetails);
//				}
//				if(null != item.getItem_taste() && !"".equals(item.getItem_taste())){
//					String itemtastes = convertStr(item.getItem_taste());
//					String tasteNames = tqZdkMapper.getTasteNames(itemtastes);
//					item.setTaste_name(tasteNames);
//				}
//			}
			
//			arv.setKdzdbh(billmoney.getKdzdbh());
//			arv.setCope_with_money(billmoney.getCope_with_money());
//			arv.setActual_money(billmoney.getActual_money());
//			arv.setDiscount_money(billmoney.getDiscount_money());
//			arv.setDiscounts(discounts);
//			arv.setItems(items);
			
			List<JSONObject> dataList = new ArrayList<JSONObject>();
//			dataList.add(JSONObject.fromObject(GsonUtil.GsonString(arv)));
//			result.setData(dataList);
//			result.setCode(Constant.CODE_SUCCESS);
			result.setMsg(Constant.PAYMENT_SUCCESS);
//			result.setSuccess(true);
//		} else {
//			result.setCode(-1);
//			result.setMsg(Constant.PAYMENT_FAILURE);
//			result.setSuccess(false);
//		}
	}
	
	/**
	 * 支付
	 * 
	 * @param param
	 * @param result
	 * @return
	 */
	public void payment(Data param, Data result) {
//		Map<String, Object> paramMap = ReqDataUtil.getDataMap(param);
//		String dataType = ParamUtil.getStringValue(paramMap, "data_type", false, null);
//		String payNum = ParamUtil.getStringValue(paramMap, "pay_num", false, null);
//		String zdbh = ParamUtil.getStringValue(paramMap, "zdbh", false, null);
//		String jtbh = ParamUtil.getStringValue(paramMap, "jtbh", false, null);
//		String date_time = ParamUtil.getStringValue(paramMap, "date_time", false, null);

		String xmlStr = "";
//		String fkfsid = checkType(payNum);
		String rifUrl = tqZdkMapper.getRifUrl();
//		if(!"-1".equals(fkfsid)){
//			PaymentType pt = null;
//			pt = PaymentType.valueOf(dataType.toUpperCase());
//			switch (pt) {
//			case PAY:
//				xmlStr = jointXML("01", fkfsid, zdbh, payNum, jtbh, date_time);
//				break;
//			case CANCEL:
//				xmlStr = jointXML("02", fkfsid, zdbh, payNum, jtbh, date_time);
//				break;
//			case QUERY:
//				xmlStr = jointXML("03", fkfsid, zdbh, payNum, jtbh, date_time);
//				break;
//			
//			default:
//				break;
//			}
//			String rxmlStr = SendRequest.sendPost(rifUrl, xmlStr);
//			
//			if("-1".equals(rxmlStr)){
//				payResultData.setStatus("-1");
//				payResultData.setMsg("总部接口异常！");
////				result.setCode(Constant.CODE_PARAM_FAILURE);
////				result.setSuccess(false);
//			}else{
//				try {
//					readStringXmlOut(rxmlStr);
//				} catch (Exception e) {
//					payResultData.setStatus("-1");
//					payResultData.setMsg("返回数据异常！");
//					List<JSONObject> dataList = new ArrayList<JSONObject>();
//					dataList.add(JSONObject.fromObject(GsonUtil.GsonString(payResultData)));
////					result.setData(dataList);
////					result.setCode(Constant.CODE_PARAM_FAILURE);
////					result.setSuccess(false);
//					e.printStackTrace();
//				}
////				result.setCode(Constant.CODE_SUCCESS);
////				result.setSuccess(true);
//				payResultData.setJzid(fkfsid);
//				List<JSONObject> dataList = new ArrayList<JSONObject>();
//				dataList.add(JSONObject.fromObject(GsonUtil.GsonString(payResultData)));
////				result.setData(dataList);
//			}
//		} else {
//			payResultData.setStatus("-1");
//			payResultData.setMsg("没有此支付方式！");
//			List<JSONObject> dataList = new ArrayList<JSONObject>();
//			dataList.add(JSONObject.fromObject(GsonUtil.GsonString(payResultData)));
//			result.setData(dataList);
//			result.setCode(-1);
//			result.setMsg("没有此支付方式！");
//			result.setSuccess(false);
//		}
		
	}
	
	/**
	 * 组装xml数据
	 * 
	 * @return
	 */
	@Transactional
	public String jointXML(String czlx, String fkfsid, String zdbh, String dynamicid, String jtbh, String date_time) {
		TsPsjgsdk tsPsjgsdk =tqZdkMapper.getJg();
//		BillMoney bm = tqZdkMapper.findBillMoney(zdbh);
		BillMoney bm = getBillMoney(zdbh);
		List<WdDishVo> wdv =tqZdkMapper.getWdDish(zdbh);
		String jgxh = formatJgxh(tsPsjgsdk.getJgxh()); 
		String outtradeno = jgxh + zdbh + date_time;
		StringBuilder reqXml = new StringBuilder();
		reqXml.append("<?xml version=\"1.0\" encoding=\"UTF-8\" ?>");
		reqXml.append("<DATAPACKET>");
		reqXml.append("<HEAD>");
		reqXml.append("<Version>1.0</Version>");
		reqXml.append("<SRC>" + tsPsjgsdk.getJgxh() + "</SRC>");
		reqXml.append("<DES>00</DES>");
		reqXml.append("<DEVID/>");
		reqXml.append("<APP>TZX-StoreBusinessSystem</APP>");
		reqXml.append("<TID>TXDTASHUOCHW35DG39SG0LHHAW04YSDFGH</TID>");
		reqXml.append("<MSGID>{" + java.util.UUID.randomUUID() + "}</MSGID>");
		reqXml.append("<CORID>20051024092733000440</CORID>");
		reqXml.append("<WORKDATE>" + DateUtil.getNowDateYYDDMM() + "</WORKDATE>");
		reqXml.append("<PERSONNEL>0001</PERSONNEL>");
		reqXml.append("<RESERVE>STRING</RESERVE>");
		reqXml.append("</HEAD>");
		reqXml.append("<MSG>");
		reqXml.append("<SUBSYSTEM>CRM</SUBSYSTEM>");
		reqXml.append("<ACTION>ONLINEPAYMENT</ACTION>");
		reqXml.append("<DATAS>");
		reqXml.append("<DATA Name=\"HYK\">");
		reqXml.append("<ROWDATA>");
		reqXml.append("<ROW FKFSID=\"" + fkfsid + "\" FKJGXH=\"" + tsPsjgsdk.getJgxh() + "\" CZLX=\"" + czlx + "\" OUTTRADENO=\"" + outtradeno + "\" SUBJECT=\"" + tsPsjgsdk.getJgmc1() + "\" TOTAL=\"" + bm.getActual_money() + "\" PRODUCTCODE=\"BARCODE_PAY_OFFLINE\" DYNAMICIDTYPE=\"barcode\" DYNAMICID=\"" + dynamicid + "\" STOREID=\"" + tsPsjgsdk.getJgbh() + "\" TERMINALID=\"" + jtbh + "\"/>");
		reqXml.append("</ROWDATA>");
		reqXml.append("</DATA>");
		reqXml.append("<DATA Name=\"HYXX\">");
		reqXml.append("<ROWDATA>");
		reqXml.append("<ROW SFGS=\"\" ZHYID=\"\" ZHYGX=\"1\" ZFBJ=\"1\" HYSFLX=\"\" XSRY=\"\" FZRY=\"\" HYLX=\"\" HYXB=\"\" ZJLX=\"\" ZJHM=\"\" HYMZ=\"\" HYJG=\"\" CSRQ=\"\" WHCD=\"\" YSR=\"\" ZCMC=\"\" SJHM=\"\" JTDH=\"\" EMAIL=\"\" ADDRS=\"\" YZBM=\"\" HYZK=\"\" ZNZK=\"\" YYAH=\"\" PIC1=\"\" CPHM=\"\" CAR=\"\" SFGSCY=\"\" GSID=\"0\" ZWMC=\"\" BMMC=\"\" GSMC=\"\" GSDH=\"\" GSCZ=\"\" GSDZ=\"\" GSYZBM=\"\" KHYH=\"\" YHZH=\"\" YWFW=\"\" SSHY=\"\" HYDW=\"\" WWW=\"\" SWZH=\"\" GMRS=\"\" SZDQ=\"\" SFXYDW=\"\" XYDH=\"\" XYRQ=\"\"/>");
		reqXml.append("</ROWDATA>");
		reqXml.append("</DATA>");
		reqXml.append("<DATA Name=\"FKLS\"> ");
		reqXml.append("<ROWDATA/>");
		reqXml.append("</DATA>");
		reqXml.append("<DATA Name=\"GOODSDETAIL\">");
		reqXml.append("<ROWDATA>");
		if (null != wdv) {
			for (int i = 0; i < wdv.size(); i++) {
				reqXml.append("<ROW GOODSID=\"" + wdv.get(i).getClmxid() + "\" GOODSNAME=\"" + wdv.get(i).getCmmc1() + "\" QUANTITY=\"" + wdv.get(i).getCmsl() + "\" PRICE=\"" + wdv.get(i).getSjje() + "\"/>");
			}
		}
		reqXml.append("</ROWDATA>");
		reqXml.append("</DATA>");
		reqXml.append("</DATAS>");
		reqXml.append("</MSG>");
		reqXml.append("</DATAPACKET>");
		
//		reqXml.append("<?xml version=\"1.0\" encoding=\"UTF-8\" ?>");
//		reqXml.append("<req version=\"1.0\">");
//		reqXml.append("<disk command=\"list\" >");
//		reqXml.append("<vpath name=\"" + fileName + "\" dir=\"/\"/>");
//		reqXml.append("</disk>");
//		reqXml.append("</req>");
		String requestXml = reqXml.toString();
		return requestXml;
	}
	
	@Transactional
	public String checkType(String dynamicid) {
		String wx = "10,11,12,13,14,15";
		String zfb = "25,26,27,28,29,30";
		String fkfsid = "-1";
		String dynamicids = dynamicid.substring(0, 2);
		if(wx.indexOf(dynamicids) > -1){
			fkfsid =tqZdkMapper.getFkfsid("ERP_FKFS_WX");
			
		} else if(zfb.indexOf(dynamicids) > -1){
			fkfsid =tqZdkMapper.getFkfsid("ERP_FKFS_ZFB");
			
		}
		
		return fkfsid;
	}
	
	static PayResultData payResultData = new PayResultData();
	public static void readStringXmlOut(String xml) throws Exception {
		StringReader read = new StringReader(xml);
		InputSource source = new InputSource(read);
		SAXBuilder sb = new SAXBuilder();
		Document doc = (Document) sb.build(source);
		Element root = doc.getRootElement();
		parse(root);
	}

	public static PayResultData parse(Element root) {
		List nodes = root.getChildren();
		int len = nodes.size();
		if (len != 0) {
			for (int i = 0; i < len; i++) {
				Element element = (Element) nodes.get(i);// 循环依次得到子元素
				if("ROW".equals(element.getName())){
					payResultData.setFkje(element.getAttributeValue("AMOUNT"));
					payResultData.setDiscountamount(element.getAttributeValue("DISCOUNTAMOUNT"));
					payResultData.setFkfs(element.getAttributeValue("FKFS"));
//					payResultData.setMsg(element.getAttributeValue("MSG"));
					if("order not exist".equals(element.getAttributeValue("MSG"))){
						payResultData.setMsg("订单不存在!");
					} else if("Business Failed".equals(element.getAttributeValue("MSG"))){
						payResultData.setMsg("交易失败!");
					} else {
						payResultData.setMsg(element.getAttributeValue("MSG"));
					}
					payResultData.setPrivilegamount(element.getAttributeValue("PRIVILEGEAMOUNT"));
					payResultData.setStatus(element.getAttributeValue("STATUS"));
					payResultData.setTotal(element.getAttributeValue("TOTAL"));
					payResultData.setTradeno(element.getAttributeValue("TRADENO"));
					payResultData.setUserid(element.getAttributeValue("USERID"));
					payResultData.setYhfsid(element.getAttributeValue("YHFSID"));
				}
				parse(element);
			}
		}
		return payResultData;
	}
	
	public String convertStr(String item_taste) {
		if (item_taste.indexOf(",") > -1) {
			item_taste = item_taste.replaceAll(",", "','");
		}
		item_taste = "'" + item_taste + "'";
		return item_taste;
	}
	
	public BillMoney getBillMoney(String zdbh) {
		BillMoney bm = new BillMoney();
		CalcMoney cm = tqZdkMapper.findCalcMoney(zdbh);
		bm.setKdzdbh(zdbh);
		bm.setCope_with_money(cm.getYsje().setScale(2) + "");
		bm.setActual_money(cm.getZdje().setScale(2) + "");
		bm.setDiscount_money(cm.getYsje().subtract(cm.getZdje()).setScale(2).toString());
		return bm;
	}
	
	public static String formatJgxh(int jgxh) {
		String str_xh = String.valueOf(jgxh);
		String str = "000000";
		str_xh = str.substring(0, 6 - str_xh.length()) + str_xh;
		return str_xh;
	}


}
