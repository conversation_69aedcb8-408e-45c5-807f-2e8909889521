package com.tzx.publics.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.HashMap;

public class InvitationCodeUtils {
    private static final Logger logger = LoggerFactory.getLogger(InvitationCodeUtils.class);
    private static final char[] charArray = "123456789abcdefghijkmnopqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ".toCharArray();
    private static final int scale = 58;

//    public static String encodeStr(Long num) {
//        StringBuilder sb = new StringBuilder();
//        try {
//            while (num != 0) {
//                sb.append(charArray[(int) (num % scale)]);
//                num = num / scale;
//            }
//            return sb.reverse().toString();
//        } catch (Exception ex) {
//            logger.error("InvitationCodeUtils encodeStr 编码出错", ex);
//        }
//        return "";
//    }

    public static String encodeStr(String numStr) {
        StringBuilder sb = new StringBuilder();
        try {
            long num = Long.parseLong(numStr);
            while (num != 0) {
                sb.append(charArray[(int) (num % scale)]);
                num = num / scale;
            }
            return sb.reverse().toString();
        } catch (Exception ex) {
            logger.error("InvitationCodeUtils encodeStr 编码出错", ex);
        }
        return "";
    }

    public static String decodeStr(String str) {
        StringBuffer sb = new StringBuffer(str);
        str = sb.reverse().toString();
        HashMap<String, Integer> map = new HashMap<String, Integer>();
        for (int i = 0; i < charArray.length; i++) {
            map.put(charArray[i] + "", i);
        }
        long sum = 0;
        try {
            for (int i = 0; i < str.length(); i++) {
                sum += (long)Math.pow(scale, i) * map.get(str.charAt(i) + "");
            }
            return String.valueOf(sum);
        } catch (Exception ex) {
            logger.error("InvitationCodeUtils decodeStr 输入不符合规范");
        }
        return "";
    }

    public static void main(String[] args) {
        String ordernum = "3054115832309600038";
//        String ordernumEn = "E" + encodeStr(Long.parseLong(ordernum));
        String ordernumEn = encodeStr(ordernum);
        logger.info("订单号={}编码后={}", ordernum, ordernumEn);
        System.out.println("86bwy3GSca1".equals(ordernumEn));
        String ordernumDe = decodeStr(ordernumEn);
        logger.info("编码后={} 解码后={}", ordernumEn, ordernumDe);
        System.out.println("3054115832309600038".equals(ordernumDe));

    }

}
