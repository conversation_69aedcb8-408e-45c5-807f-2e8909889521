package com.tzx.mobilepos.rest.mapper;

import com.tzx.mobilepos.rest.model.*;
import com.tzx.mobilepos.rest.vo.*;
import com.tzx.publics.base.MyMapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2018-05-23
 */

public interface MobilePosTqZdkMapper extends MyMapper<TqZdk> {

	public TsBmkzk getBh(@Param("bmc") String bmc, @Param("zdmc") String zdmc);

	public TqZdk getZdCount(@Param("ktskjh") String ktskjh);

	public int initZdk(@Param("kdzdbh") String kdzdbh, @Param("ktbcid") int ktbcid, @Param("czybh") String czybh, @Param("ygdlcs") String ygdlcs);

	public int delWdk(@Param("kdzdbh") String kdzdbh);

	public int delFkls(@Param("kdzdbh") String kdzdbh);

	public int updateBh(@Param("nr") String nr, @Param("bmc") String bmc, @Param("zdmc") String zdmc);

	public int addTc(@Param("szdbh") String szdbh, @Param("aitemid") int aitemid, @Param("ixmsl") int ixmsl);

	public int addCm(@Param("szdbh") String szdbh, @Param("aitemid") int aitemid, @Param("ixmsl") int ixmsl, @Param("sskjh") String sskjh, @Param("sxsyh") String sxsyh, @Param("skwbh") String skwbh, @Param("sggbh") String sggbh, @Param("skwbz") String skwbz, @Param("atype") int atype);

	public String getYhsx(@Param("id") int id);

	public int yhfstj(@Param("iyhfsid") int iyhfsid, @Param("szdbh") String szdbh, @Param("isl") BigDecimal isl, @Param("syhsx") String syhsx);

	public int addYhfs(@Param("aitemid") int aitemid, @Param("iyhfsid") int iyhfsid, @Param("szdbh") String szdbh, @Param("sskjh") String sskjh, @Param("ixmsl") int ixmsl);

	public BillMoney findBillMoney(@Param("zdbh") String zdbh);

	public CalcMoney findCalcMoney(@Param("zdbh") String zdbh);

	public AccountsOrder accountsOrder(@Param("szdbh") String szdbh, @Param("ijzid") int ijzid, @Param("ifkje") BigDecimal ifkje, @Param("ifksl") int ifksl, @Param("sfkhm") String sfkhm, @Param("ssfzhm") String ssfzhm, @Param("slxdh") String slxdh, @Param("sfkbz") String sfkbz, @Param("sskjh") String sskjh, @Param("sskyh") String sskyh);

	public int updateZdk(@Param("qch") String qch, @Param("zzbz") String zzbz, @Param("zwbh") String zwbh, @Param("kdzdbh") String kdzdbh, @Param("jzzdbh") String jzzdbh, @Param("jzbbrq") Date jzbbrq, @Param("jzsj") Date jzsj, @Param("jzcs") int jzcs, @Param("jzskjh") String jzskjh, @Param("jzczry") String jzczry, @Param("jzsx") String jzsx, @Param("ksjzsj") Date ksjzsj, @Param("jzjssj") Date jzjssj, @Param("jzbcid") int jzbcid, @Param("xfks") int xfks);

	public int updateWdk(@Param("kdzdbh") String kdzdbh, @Param("jzzdbh") String jzzdbh, @Param("jzskjh") String jzskjh, @Param("jzbbrq") Date jzbbrq, @Param("jzbcid") int jzbcid);

	public int updateFklslsk(@Param("kdzdbh") String kdzdbh, @Param("jzzdbh") String jzzdbh, @Param("jzbcid") int jzbcid);

	public PaymentWay getFkfsid(@Param("yl3") String yl3);

	public TsPsjgsdk getJg();

	public List<WdDishVo> getWdDish(@Param("kdzdbh") String kdzdbh);
	
	public List<WdDishVo> getWdDishNew(@Param("kdzdbh") String kdzdbh);

	public String getRifUrl();

	public List<ItemVo> getDiscountR(@Param("kdzdbh") String kdzdbh);

	public List<ItemVo> getItemR(@Param("kdzdbh") String kdzdbh);

	public List<ComboDetails> getComboDetailsR(@Param("kdzdbh") String kdzdbh, @Param("clmxid") int clmxid);

	public String getTasteNames(@Param("kwbhs") String kwbhs);

	public TsBck getBcid();

	public int updateKtczry(@Param("kdzdbh") String kdzdbh, @Param("ktczry") String ktczry, @Param("ygdlcs") int ygdlcs);

	public TsGgcsk getGgcs(@Param("cdzd") String cdzd);

	// 根据开台收款机号查询账单
	public List<BilledOrderVo> queryOrderByKtskjh(@Param("jzczry") String jzczry, @Param("jzsx") String jzsx, @Param("ktczry") String ktczry);

	// 根据开单账单编号查询明细
	public List<TqWdk> queryOrderDetailByKdzdbh(@Param("kdzdbh") String kdzdbh);

	// 查询账单信息
	public List<BilledOrderVo> queryBillInfo(@Param("ktskjh") String ktskjh, @Param("kdzdbh") String kdzdbh);

	// 根据开单账单编号查询账单信息
	public List<BillItemVo> queryBillDetailByKdzdbh(@Param("kdzdbh") String kdzdbh, @Param("clmxid") Integer clmxid, @Param("sftc") String sftc, @Param("dcxh") Integer dcxh, @Param("syyhfkfsid") int syyhfkfsid);

	// 查询内置折让优惠活动
	public DiscountVo queryDiscountzZr();

	// 查询优惠活动
	public List<DiscountVo> queryDiscount(@Param("kdzdbh") String kdzdbh, @Param("icbid") int icbid, @Param("sellModel") String sellModel, @Param("sjgtxbh") String sjgtxbh);

	// 获取就餐方式id
	public Integer findIcibd(@Param("sellType") String sellType);

	// 查询价格体系编号
	public String findJgtxbh(@Param("kdzdbh") String kdzdbh);

	public void addTcdcxzlsk(@Param("kdzdbh") String kdzdbh, @Param("tcid") Integer tcid, @Param("tcxh") Integer tcxh, @Param("item_id") Integer item_id, @Param("item_count") Double item_count, @Param("xcsl") Integer xcsl, @Param("cmdj") Double cmdj, @Param("cmje") Double cmje, @Param("fzje") Double fzje, @Param("mxlxid") Integer mxlxid);

	// public GroupDetails findTcInfo(@Param("item_id") int item_id,@Param("cmid") int cmid);
	public Integer findTcInfo(@Param("item_id") int item_id);

	// public GroupDetails findItem(@Param("id")Integer id, @Param("mxid")Integer mxid, @Param("fzid")Integer fzid, @Param("tcid") Integer tcid);
	public GroupDetails findItem(@Param("id") Integer id, @Param("tcid") Integer tcid, @Param("tcxh") Integer tcxh);

	// 退单
	public Integer cancelBill(@Param("azdbh") String azdbh, @Param("azdrq") Date azdrq, @Param("askjh") String askjh, @Param("sczry") String sczry);

	// 使用app可用的优惠方式
	public void useAppCanUseYhfs(@Param("bill_num") String bill_num, @Param("icbid") Integer icbid, @Param("sjgtxbh") String sjgtxbh, @Param("sskjh") String sskjh, @Param("ifuntype") Integer ifuntype);

	// 获取找零金额
	public String changeMone(@Param("kdzdbh") String kdzdbh);

	// 获取付款信息
	public List<PayMentVo> getPayMent(@Param("kdzdbh") String kdzdbh);

	// 分摊优惠
	public void zRtr(@Param("bill_num") String bill_num);

	// 更新销售模式
	public int updateXsms(@Param("kdzdbh") String kdzdbh, @Param("xsms") String xsms, @Param("qch") String qch);
	
	// 获取账单中被沽清的菜品
	public String getGqplk(@Param("kdzdbh") String kdzdbh);

	// 取消活动或者菜品，目前没有取消菜品
	public int cancelJd(@Param("szdbh") String szdbh, @Param("irwid") int irwid, @Param("isl") int isl, @Param("sskjh") String sskjh, @Param("sczrh") String sczrh, @Param("deltype") int deltype);

	// 更新卧单库是否加价菜
	public int updateJjcrwid(@Param("kdzdbh") String kdzdbh);

	// 获取当前机台当前登陆人全部现金账单金额
	public String getFullMoney(@Param("jtbh") String jtbh, @Param("bbrq") Date bbrq, @Param("bcid") int bcid, @Param("czybh") String czybh);

	// 获取已上交的收款金额（现金）
	public String getCrossedMoney(@Param("bbrq") Date bbrq, @Param("bcid") int bcid, @Param("czybh") String czybh);

	public double getFkje(@Param("zdbh") String zdbh);

	public double getZdje(@Param("zdbh") String zdbh);

	public List<PaymentRunningWater> getFklsList(@Param("zdbh") String zdbh);

	public List<CouponRunningWater> getCouponlsList(@Param("zdbh") String zdbh, @Param("optype") String optype);

	public int delFkje(@Param("kdzdbh") String kdzdbh, @Param("fklxsx") String fklxsx, @Param("fklsid") int fklsid, @Param("fkhm") String fkhm, @Param("fkbz") String fkbz);

	public int delFkjeByFkhm(@Param("kdzdbh") String kdzdbh, @Param("fkhm") String fkhm);
	
	public int insertMantualZrAmount(@Param("kdzdbh") String kdzdbhs, @Param("zrje") double zrje);

	public int delMantualZrAmount(@Param("kdzdbh") String kdzdbhs);

	public PaymentWay getPaymentWay(@Param("fkfsid") Integer fkfsid);

	public int updateTaste(@Param("rwid") Integer rwid, @Param("kdzdbh") String kdzdbh, @Param("kwbz") String kwbz);

	public String findYhfsid(@Param("yhsx") String yhsx);

	/**
	 * @Description: 根据付款流水查询验证码
	 * @param @param fklsid
	 * @param @return
	 * @return String
	 * @throws <AUTHOR>
	 * @email <EMAIL>
	 * @date 2018-11-21
	 */
	public String getMtYzm(@Param("fklsid") String fklsid);

	/**
	 * @Description: 查询优惠权限
	 * <AUTHOR>
	 * @date 2018-11-27
	 */
	public List<DiscountsRoot> queryDiscountsRoot(@Param("yhfsid") int yhfsid);

	/**
	 * @Description: 根据机台编号获取未结帐单付款记录
	 * <AUTHOR>
	 * @date 2018-11-29
	 */
	public PayMentVo getPayMentToJtbh(@Param("jtbh") String jtbh);

	/**
	 * @Description: 清除帐单号下所有付款记录
	 * <AUTHOR>
	 * @date 2018-11-29
	 */
	public int delFkjeAll(@Param("kdzdbh") String kdzdbh);

	public TqZdk getZdWj(@Param("jtbh") String jtbh);

	/**
	 *
	 * @Description: 查询菜品金额
	 * @param @param zdbh
	 * @param @param xmid
	 * @param @return
	 * @return BigDecimal
	 * @throws <AUTHOR>
	 * @email <EMAIL>
	 * @date 2018-12-5
	 */
	public BigDecimal findCmje(@Param("zdbh") String zdbh, @Param("xmid") String xmid);

	/**
	 * @Description: 票券码写入握单表
	 * @param @param zdbh
	 * @param @param xmid
	 * @param @param yzm
	 * @return void
	 * @throws <AUTHOR>
	 * @email <EMAIL>
	 * @date 2018-12-5
	 */
	public void updatePqhm(@Param("zdbh") String zdbh, @Param("xmid") Integer xmid, @Param("yzm") String yzm);

	/**
	 * @Description: 获取未交款账单现金金额
	 * <AUTHOR>
	 * @date 2018-12-4
	 */
	public String getNoCrossedMoney(@Param("jtbh") String jtbh, @Param("bbrq") Date bbrq, @Param("bcid") int bcid, @Param("czybh") String czybh, @Param("sbbrq") String sbbrq);

	/**
	 * @Description: 查询支付宝微信是否有多余记录
	 * <AUTHOR>
	 * @date 2018-12-04
	 */
	public int countDsfFkls(@Param("kdzdbh") String kdzdbh, @Param("jzid") Integer jzid, @Param("fkhm") String fkhm);

	public int getZdmx(@Param("zdbh") String zdbh);

	public TqZdk getZdk(@Param("kdzdbh") String kdzdbh);

	public String getOuttradeno(@Param("fklsid") String fklsid);

	public double getFkjeNoZl(@Param("zdbh") String zdbh);

	/**
	 * @Description: 获取美团菜品券菜品数量
	 * @param @param zdbh
	 * @param @return
	 * @return List<WdDishVo>
	 * @throws <AUTHOR>
	 * @email <EMAIL>
	 * @date 2018-12-5
	 */
	public List<WdDishVo> getWdMtDish(@Param("zdbh") String zdbh);

	public String callP_SendKVSData(@Param("szdbh") String szdbh);

    public Integer checkIsEnablePv(@Param("paymentMark") String paymentMark);

	/**
	 * @Description: 查询优惠是否多余记录
	 * <AUTHOR>
	 * @date 2018-12-20
	 */
	public int countYhtemp(@Param("kdzdbh") String kdzdbh, @Param("yzm") String yzm, @Param("jzid") String jzid);

	public int delYhtemp(@Param("kdzdbh") String kdzdbh);

	public Dish getDsfyh(@Param("yhsx") String yhsx);

	public double getZdjeAndDsf1(@Param("zdbh") String zdbh);

	public double getZdjeAndDsf2(@Param("zdbh") String zdbh);

	public int delTpd(@Param("kdzdbh") String kdzdbh, @Param("yhfsid") int yhfsid, @Param("yzm") String yzm);

	public List<BuywdandYhitems> getBuywdandYhitems(@Param("szdbh") String szdbh, @Param("iyhfsid") int iyhfsid, @Param("syhsx") String syhsx, @Param("icbid") int icbid, @Param("ssellmode") String ssellmode, @Param("sjgtxbh") String sjgtxbh);

	public int getBuydishrel(@Param("id") int id);

	public List<BuywdandYhitems> getYhneedChoitems(@Param("iyhfsid") int iyhfsid, @Param("syhsx") String syhsx, @Param("icbid") int icbid, @Param("ssellmode") String ssellmode, @Param("sjgtxbh") String sjgtxbh);

	/**
	 * @Description: 插入买加价购临时表tq_wdandyhtempitem
	 * <AUTHOR>
	 * @date 2019-03-16
	 */
	public int insertWdandYhTempItem(@Param("yhfsid") int yhfsid, @Param("clmxid") int clmxid, @Param("kdzdbh") String kdzdbh, @Param("isneedadd") String isneedadd, @Param("cmsl") int cmsl, @Param("usetag") int usetag, @Param("rwid") int rwid);

	/**
	 * @Description: 插入买加价购临时表tq_yhmaintempitem
	 * <AUTHOR>
	 * @date 2019-03-16
	 */
	public int insertYhMainTempItem(@Param("yhfsid") int yhfsid, @Param("kdzdbh") String kdzdbh, @Param("yhtimes") int yhtimes, @Param("paramtype") int paramtype);

	/**
	 * @Description: 插入买加价购临时表tq_yhtempitem
	 * <AUTHOR>
	 * @date 2019-03-16
	 */
	public int insertYhTempItem(@Param("yhfsid") int yhfsid, @Param("clmxid") int clmxid, @Param("kdzdbh") String kdzdbh, @Param("isgift") String isgift, @Param("cmsl") int cmsl);

	public int delWdandYhTempItem(@Param("kdzdbh") String kdzdbh);

	public int delYhMainTempItem(@Param("kdzdbh") String kdzdbh);

	public int delYhTempItem(@Param("kdzdbh") String kdzdbh);

	// 根据开单账单编号和菜目id查询
	public List<TqWdk> queryWdkByCmid(@Param("kdzdbh") String kdzdbh, @Param("clmxid") int clmxid);

	public int cancelThirdYhfs(@Param("zdbh") String zdbh, @Param("yzm") String yzm, @Param("yhfsid") int yhfsid);

	public int insertTqWdkCouponTemp(@Param("zdbh") String zdbh, @Param("couponcode") String couponcode, @Param("couponprice") double couponprice);

	public int insertTqWdkCouponTempQm(@Param("zdbh") String zdbh, @Param("couponcode") String couponcode, @Param("couponprice") double couponprice, @Param("couponsale") double couponsale);

	public int delTqWdkCouponTemp(@Param("zdbh") String zdbh, @Param("couponcode") String couponcode);

	public int delTqAcewilCouponCache(@Param("zdbh") String zdbh);
	
	public int delZdk(@Param("kdzdbh") String kdzdbh);
	
	// 根据开台收款机号查询账单
	public List<BilledOrderVo> queryOrderToWj(@Param("ktczry") String ktczry);

	public List<TqThirdExceptOrder> findRetryTteoList(@Param("billid") String billid);
	
	public List<TqZdk> getZdWjList(@Param("jtbh") String jtbh);
	
	public List<WdDishVo> getWdDishMtZh(@Param("kdzdbh") String kdzdbh);
	
	public WdDishVo gettWdrwid(@Param("zdbh") String zdbh, @Param("cmid") int cmid);
	
	public int getXmidByXmbh(@Param("cmbh") String cmbh);
	
	public int updateByInvoice(@Param("kdzdbh") String kdzdbh, @Param("kfpsqm") String kfpsqm, @Param("kfpje") double kfpje, @Param("isselprinteinvoice") String isselprinteinvoice);
	
	public double getKpje(@Param("kdzdbh") String kdzdbh);
	
	public List<WdDishVo> getAddSaleOutData(@Param("kdzdbh") String kdzdbh);
	
	public CalcMoney findCancleMoling(@Param("szdbh") String szdbh, @Param("itype") int itype);
	
	public CalcMoney findMoling(@Param("szdbh") String szdbh, @Param("itype") int itype);
	
	public TqWdk queryWdkByRwid(@Param("rwid") int rwid);

	public List<TqFklslsk> getFklslsk(@Param("kdzdbh") String kdzdbh);
	
	public TsYhfssdk getBjyhYh(@Param("yhfsbh") String yhfsbh);
	
	public List<TqWdkCouponTemp> getTqWdkCouponTemp(@Param("zdbh") String zdbh);
	
	public List<QmWdDishVo> getWdQmDish(@Param("zdbh") String zdbh, @Param("qmType") String qmType);
	
	public List<QmUploadWdDishVo> getWdQmDishUpload(@Param("zdbh") String zdbh, @Param("bbrq") Date bbrq, @Param("yht") String yht, @Param("qmType") String qmType);
	
	public List<QmWdDishVo> getWdQmDishRefundUpload(@Param("zdbh") String zdbh, @Param("bbrq") Date bbrq, @Param("qmType") String qmType);
	
	public TqZdk getQmZdk(@Param("kdzdbh") String kdzdbh, @Param("bbrq") Date bbrq);
	
	public List<FklsVo> getVqFklsList(@Param("kdzdbh") String kdzdbh);
	
	public int getVqFklsDsfCount(@Param("kdzdbh") String kdzdbh);
	
	public List<FklsVo> getBizidList(@Param("kdzdbh") String kdzdbh);
	
	public List<QmUploadWdDishVo> getWdQmDishUploadCoupons(@Param("zdbh") String zdbh, @Param("bbrq") Date bbrq, @Param("yht") String yht, @Param("qmType") String qmType);

    int insertTqBillOtherInfo(@Param("billno") String zdbh, @Param("userid") String userid, @Param("bizid") String outtradeno,
                              @Param("fwyh") String fwyh, @Param("skjh") String skjh, @Param("bbrq") Date bbrq);

    int insertTqCommThirdOrder(@Param("billid") String zdbh, @Param("bbrq") Date bbrq, @Param("scan_code") String authcode, @Param("paytypeid") String payid,
                               @Param("orderno") String outtradeno, @Param("amount") Double sjje);

    int insertTqMemberInfo(@Param("billid") String zdbh, @Param("scan_code") String authcode, @Param("cardno") String cardno,
                           @Param("phone") String phone);

}