package com.tzx.miniapp.rest.vo;

import java.util.List;

public class ZsDishInfoEntity {
	private static final long serialVersionUID = 1L;

	// 0否；1是
	private String dishId; // 菜品ID
	private String merDishId; // 商户菜品ID
	private String name; // 菜品名称
	private String spellCode; // 拼音代码
	private String pordCategory; // 菜品分类ID(categoryId)
	private String description; // 菜品描述
	private int shareType; // 是否关联菜品*
	private int isPopular; // 是否推荐菜*
	private int isFee; // 是否是赠品*
	private int isNew; // 是否是新品0：否，1：是*
	private String imageUrl; // 图片信息
	private int status; // 状态：0：已停用，1：启用 2:删除
	private List<ZsDishUnitEntity> unitList; // 商品规格
	private List<ZsDishPackDetailsEntity> packDetailsList; // 套餐明细
	// 门店差异化参数
	private int isOpen; // 是否对用户开放
	private int takeoutPackagingFee; // 打包费
	private int incrementUnit; // 最小增加单元
	private int minOrderCount; // 起售份数
	private int isSingleSale; // 如果是配料，这个配料是否允许单点
	private int isBatching; // 是否配料
	private String tasteList; // 口味，多个间逗号分隔
	private int isSpecialty; // 是否店家招牌菜

	private int isNeedConfirmFoodNumber; // 是否需要确认点菜
	private int takeawayTag; // 外卖表示, 0：不外送 1：可外送2：仅外送 （默认为 1可外送）
	private int workingLunchTag; // 工作餐标记 0：非工作餐 1：可做工作餐 2：仅做工作餐 （默认为0，不能做工作餐）
	private String foodEnName; // 英文名
	private int isAutoAdd; // 是否默认自动加入
	private int isCanRefund; // 是否支持退订退款，0：不支持 1：支持（默认）
	private int tasteIsRequired; // 口味是否必选项 0：不限 1：必选
	private int tasteIsMultiple; // 口味是否多选
	private int makingMethodIsRequired; // 作法是否必选项 0：不限 1：必选
	private int makingMethodIsMultiple; // 作法是否多选项 0：不是 1：是
	private Integer type;// 1 菜品 2 套餐

	private String dwname;
	private String price;

	private Integer sort;
	private String sellStartTime;
	private String sellEndTime;

	private String code;// 菜品编码

	public String getDishId() {
		return dishId;
	}

	public void setDishId(String dishId) {
		this.dishId = dishId;
	}

	public String getMerDishId() {
		return merDishId;
	}

	public void setMerDishId(String merDishId) {
		this.merDishId = merDishId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getSpellCode() {
		return spellCode;
	}

	public void setSpellCode(String spellCode) {
		this.spellCode = spellCode;
	}

	public String getPordCategory() {
		return pordCategory;
	}

	public void setPordCategory(String pordCategory) {
		this.pordCategory = pordCategory;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public int getShareType() {
		return shareType;
	}

	public void setShareType(int shareType) {
		this.shareType = shareType;
	}

	public int getIsPopular() {
		return isPopular;
	}

	public void setIsPopular(int isPopular) {
		this.isPopular = isPopular;
	}

	public int getIsFee() {
		return isFee;
	}

	public void setIsFee(int isFee) {
		this.isFee = isFee;
	}

	public int getIsNew() {
		return isNew;
	}

	public void setIsNew(int isNew) {
		this.isNew = isNew;
	}

	public String getImageUrl() {
		return imageUrl;
	}

	public void setImageUrl(String imageUrl) {
		this.imageUrl = imageUrl;
	}

	public int getStatus() {
		return status;
	}

	public void setStatus(int status) {
		this.status = status;
	}

	public List<ZsDishUnitEntity> getUnitList() {
		return unitList;
	}

	public void setUnitList(List<ZsDishUnitEntity> unitList) {
		this.unitList = unitList;
	}

	public List<ZsDishPackDetailsEntity> getPackDetailsList() {
		return packDetailsList;
	}

	public void setPackDetailsList(List<ZsDishPackDetailsEntity> packDetailsList) {
		this.packDetailsList = packDetailsList;
	}

	public int getIsOpen() {
		return isOpen;
	}

	public void setIsOpen(int isOpen) {
		this.isOpen = isOpen;
	}

	public int getTakeoutPackagingFee() {
		return takeoutPackagingFee;
	}

	public void setTakeoutPackagingFee(int takeoutPackagingFee) {
		this.takeoutPackagingFee = takeoutPackagingFee;
	}

	public int getIncrementUnit() {
		return incrementUnit;
	}

	public void setIncrementUnit(int incrementUnit) {
		this.incrementUnit = incrementUnit;
	}

	public int getMinOrderCount() {
		return minOrderCount;
	}

	public void setMinOrderCount(int minOrderCount) {
		this.minOrderCount = minOrderCount;
	}

	public int getIsSingleSale() {
		return isSingleSale;
	}

	public void setIsSingleSale(int isSingleSale) {
		this.isSingleSale = isSingleSale;
	}

	public int getIsBatching() {
		return isBatching;
	}

	public void setIsBatching(int isBatching) {
		this.isBatching = isBatching;
	}

	public String getTasteList() {
		return tasteList;
	}

	public void setTasteList(String tasteList) {
		this.tasteList = tasteList;
	}

	public int getIsSpecialty() {
		return isSpecialty;
	}

	public void setIsSpecialty(int isSpecialty) {
		this.isSpecialty = isSpecialty;
	}

	public int getIsNeedConfirmFoodNumber() {
		return isNeedConfirmFoodNumber;
	}

	public void setIsNeedConfirmFoodNumber(int isNeedConfirmFoodNumber) {
		this.isNeedConfirmFoodNumber = isNeedConfirmFoodNumber;
	}

	public int getTakeawayTag() {
		return takeawayTag;
	}

	public void setTakeawayTag(int takeawayTag) {
		this.takeawayTag = takeawayTag;
	}

	public int getWorkingLunchTag() {
		return workingLunchTag;
	}

	public void setWorkingLunchTag(int workingLunchTag) {
		this.workingLunchTag = workingLunchTag;
	}

	public String getFoodEnName() {
		return foodEnName;
	}

	public void setFoodEnName(String foodEnName) {
		this.foodEnName = foodEnName;
	}

	public int getIsAutoAdd() {
		return isAutoAdd;
	}

	public void setIsAutoAdd(int isAutoAdd) {
		this.isAutoAdd = isAutoAdd;
	}

	public int getIsCanRefund() {
		return isCanRefund;
	}

	public void setIsCanRefund(int isCanRefund) {
		this.isCanRefund = isCanRefund;
	}

	public int getTasteIsRequired() {
		return tasteIsRequired;
	}

	public void setTasteIsRequired(int tasteIsRequired) {
		this.tasteIsRequired = tasteIsRequired;
	}

	public int getTasteIsMultiple() {
		return tasteIsMultiple;
	}

	public void setTasteIsMultiple(int tasteIsMultiple) {
		this.tasteIsMultiple = tasteIsMultiple;
	}

	public int getMakingMethodIsRequired() {
		return makingMethodIsRequired;
	}

	public void setMakingMethodIsRequired(int makingMethodIsRequired) {
		this.makingMethodIsRequired = makingMethodIsRequired;
	}

	public int getMakingMethodIsMultiple() {
		return makingMethodIsMultiple;
	}

	public void setMakingMethodIsMultiple(int makingMethodIsMultiple) {
		this.makingMethodIsMultiple = makingMethodIsMultiple;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public String getDwname() {
		return dwname;
	}

	public void setDwname(String dwname) {
		this.dwname = dwname;
	}

	public String getPrice() {
		return price;
	}

	public void setPrice(String price) {
		this.price = price;
	}

	public Integer getSort() {
		return sort;
	}

	public void setSort(Integer sort) {
		this.sort = sort;
	}

	public String getSellStartTime() {
		return sellStartTime;
	}

	public void setSellStartTime(String sellStartTime) {
		this.sellStartTime = sellStartTime;
	}

	public String getSellEndTime() {
		return sellEndTime;
	}

	public void setSellEndTime(String sellEndTime) {
		this.sellEndTime = sellEndTime;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

}
