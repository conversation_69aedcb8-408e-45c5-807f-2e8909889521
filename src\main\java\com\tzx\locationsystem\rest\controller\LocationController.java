package com.tzx.locationsystem.rest.controller;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import net.sf.json.JSONObject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.tzx.locationsystem.rest.service.ILocationService;
import com.tzx.publics.base.BaseController;

/**
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/resp")
public class LocationController extends BaseController {
	// 定义生成日志对象
	private final static Logger LOGGER = LoggerFactory.getLogger(LocationController.class);
	
	@Autowired
	// 基本数据同步接口，微生活插件调用
	private ILocationService locationService;

	/**
	 * 接收银杏岛定位系统推送餐牌桌位数据
	 *
	 * @param request
	 * @param response
	 * @param json
	 * @return
	 */
	@RequestMapping(value = "", method = RequestMethod.POST)
	public String receiveLocationData(HttpServletRequest request, HttpServletResponse response, @RequestBody(required = false) String json) {
		response.setContentType("text/html;charset=utf-8");
		LOGGER.info("request(LocationData)：" + json);
		JSONObject o = JSONObject.fromObject(json);
		locationService.CreateLocationInformation(o);
		LOGGER.info("response(LocationData)：");
		return "received";
	}
}
