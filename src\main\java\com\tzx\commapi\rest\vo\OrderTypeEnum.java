package com.tzx.commapi.rest.vo;

public enum OrderTypeEnum {

    XSMS_TS("XSMS_TS", "堂食"),
    XSMS_WS("XSMS_WS", "外卖"),
    XSMS_WM("XSMS_WM", "自提"),

    ;

    private String code;

    private String name;

    OrderTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        for (OrderTypeEnum state : OrderTypeEnum.values()) {
            if (state.getCode().equals(code)) {
                return state.getName();
            }
        }
        return "其它";
    }

}
