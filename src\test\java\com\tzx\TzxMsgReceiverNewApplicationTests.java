package com.tzx;

import com.tzx.receiver.common.utils.DBUtils;
import net.sf.json.JSONObject;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.*;

//@RunWith(SpringRunner.class)
//@SpringBootTest
public class TzxMsgReceiverNewApplicationTests {

    @Test
    public void contextLoads() {
    }



    public static void main(String[] args){

        Set<String> hashset = new HashSet<String>(); //这个只是为了去重
        hashset.add("111");
        hashset.add("222");
        hashset.add("111");
        hashset.add("111");

        System.out.println(hashset.size());

        System.out.println(hashset.toArray());
        Map<String,String> map = new HashMap<String,String>();
        String guid = "";
        map.put("ONLYSENDMQ","1");
        map.put("GUID","11");
        //added by zhouxh 201909
        if(map.get("ONLYSENDMQ")==null||!map.get("ONLYSENDMQ").equals("1")?false:true) {
            guid = map.get("GUID").toLowerCase();
        }else{
            guid = "22";
        }
        System.out.println(guid);


        JSONObject jsonObject = new JSONObject();
//        StringBuilder sb = new StringBuilder();
//        String licensekey = "";
//        String username = "";
//        String password = "";
//        Integer pagerecords = 0;
//        Integer pageno = 0;
//        Integer updatecount = 0;
//        String messagetype = "SALESDATA";
//        Integer messageid = 332;
//        String version = "V332M";
//
//        String localstorecode = "";
//        String txdate_yyyymmdd = "";
//        String txtime_hhmmss = "";
//        String mallid  = "";
//        String storecode = "";
//        String tillid = "";
//        String salestype = "";
//        String txdocno = "";
//        String mallitemcode = "";
//        String cashier = "";
//        Integer netqty = 0;
//        Integer originalamount = 0;
//        BigDecimal sellingamount = new BigDecimal(0);
//        Integer couponqty = 0;
//        BigDecimal ttltaxamount1 = new BigDecimal(0);
//        BigDecimal ttltaxamount2 = new BigDecimal(0);
//        BigDecimal netamount = new BigDecimal(0);
//        BigDecimal paidamount = new BigDecimal(0);
//        Integer  changeamount = 0;
//        String  issueby = "";
//        String  issuedate_yyyymmdd = "";
//        String  issuetime_hhmmss  = "";
//
//        String iscounteritemcode = "";
//        Integer lineno = 0;
//        String counteritemcode = "";
//        String itemcode = "";
//        String plucode = "";
//        Integer invttype = 0;
//        BigDecimal qty = new BigDecimal(0);
//        Integer exstk2sales = 1;
//        BigDecimal originalprice = new BigDecimal(0);
//        BigDecimal sellingprice = new BigDecimal(0);
//        Integer vipdiscountpercent = 0;
//        Integer vipdiscountless = 0;
//        Integer totaldiscountless1 = 0;
//        Integer totaldiscountless2 = 0;
//        Integer totaldiscountless = 0;
////        BigDecimal netamount = new BigDecimal(0);
//        BigDecimal bonusearn = new BigDecimal(0);
//
//        Integer tLineno = 0;
//        String tendercode = "OT";
//        Integer tendertype = 0;
//        Integer tendercategory = 0;
//        BigDecimal payamount = new BigDecimal(0);
//        BigDecimal baseamount = new BigDecimal(0);
//        Integer excessamount = 0;
//
//
//        sb.append(String.format("<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
//                "<S:Envelope xmlns:S=\"http://schemas.xmlsoap.org/soap/envelope/\">\n" +
//                "  <S:Body>\n" +
//                "    <postsalescreate xmlns=\"http://tempurl.org\">\n" +
//                "      <astr_request>\n" +
//                "  <header>\n" +
//                "    <licensekey>%s</licensekey>\n" +
//                "    <username>%s</username>\n" +
//                "    <password>%s</password>\n" +
//                "    <pagerecords>%d</pagerecords>\n" +
//                "    <pageno>%d</pageno>\n" +
//                "    <updatecount>%d</updatecount>\n" +
//                "    <messagetype>%s</messagetype>\n" +
//                "    <messageid>%d</messageid>\n" +
//                "    <version>%s</version>\n" +
//                "  </header>\n", licensekey,username,password,pagerecords,pageno,updatecount,messagetype,messageid,version));
//        sb.append( String.format("  <salestotal>\n" +
//                "    <localstorecode>%s</localstorecode>\n" +
//                "    <txdate_yyyymmdd>%s</txdate_yyyymmdd>\n" +
//                "    <txtime_hhmmss>%s</txtime_hhmmss>\n" +
//                "    <mallid>%s</mallid>\n" +
//                "    <storecode>%s</storecode>\n" +
//                "    <tillid>%s</tillid>\n" +
//                "    <salestype>%s</salestype>\n" +
//                "    <txdocno>%s</txdocno>\n" +
//                "    <mallitemcode>%s</mallitemcode>\n" +
//                "    <cashier>%s</cashier>\n" +
//                "    <netqty>%d</netqty>\n" +
//                "    <originalamount>%d</originalamount>\n" +
//                "    <sellingamount>%f</sellingamount>\n" +
//                "    <couponqty>%d</couponqty>\n" +
//                "    <ttltaxamount1>%f</ttltaxamount1>\n" +
//                "    <ttltaxamount2>%f</ttltaxamount2>\n" +
//                "    <netamount>%f</netamount>\n" +
//                "    <paidamount>%f</paidamount>\n" +
//                "    <changeamount>%d</changeamount>\n" +
//                "    <issueby>%s</issueby>\n" +
//                "    <issuedate_yyyymmdd>%s</issuedate_yyyymmdd>\n" +
//                "    <issuetime_hhmmss>%s</issuetime_hhmmss>\n" +
//                "  </salestotal>\n",localstorecode, txdate_yyyymmdd, txtime_hhmmss, mallid, storecode, tillid, salestype, txdocno, mallitemcode, cashier, netqty, originalamount, sellingamount, couponqty, ttltaxamount1, ttltaxamount2,netamount, paidamount, changeamount, issueby, issuedate_yyyymmdd, issuetime_hhmmss ));
//        sb.append( String.format("  <salesitems>\n" +
//                        "    <salesitem>\n" +
//                        "      <iscounteritemcode>%s</iscounteritemcode>\n" +
//                        "      <lineno>%d</lineno>\n" +
//                        "      <storecode>%s</storecode>\n" +
//                        "      <mallitemcode>%s</mallitemcode>\n" +
//                        "      <counteritemcode>%s</counteritemcode>\n" +
//                        "      <itemcode>%s</itemcode>\n" +
//                        "      <plucode>%s</plucode>\n" +
//                        "      <invttype>%d</invttype>\n" +
//                        "      <qty>%f</qty>\n" +
//                        "      <exstk2sales>%d</exstk2sales>\n" +
//                        "      <originalprice>%f</originalprice>\n" +
//                        "      <sellingprice>%f</sellingprice>\n" +
//                        "      <vipdiscountpercent>%d</vipdiscountpercent>\n" +
//                        "      <vipdiscountless>%d</vipdiscountless>\n" +
//                        "      <totaldiscountless1>%d</totaldiscountless1>\n" +
//                        "      <totaldiscountless2>%d</totaldiscountless2>\n" +
//                        "      <totaldiscountless>%d</totaldiscountless>\n" +
//                        "      <netamount>%f</netamount>\n" +
//                        "      <bonusearn>%f</bonusearn>\n" +
//                        "    </salesitem>\n" +
//                        "  </salesitems>\n" +
//                        "  <salestenders>\n" +
//                        "    <salestender>\n" +
//                        "      <lineno>%d</lineno>\n" +
//                        "      <tendercode>%s</tendercode>\n" +
//                        "      <tendertype>%d</tendertype>\n" +
//                        "      <tendercategory>%d</tendercategory>\n" +
//                        "      <payamount>%f</payamount>\n" +
//                        "      <baseamount>%f</baseamount>\n" +
//                        "      <excessamount>%d</excessamount>\n" +
//                        "    </salestender>\n" +
//                        "  </salestenders>\n" +
//                        " </astr_request>\n" +
//                        "    </postsalescreate>\n" +
//                        "  </S:Body>\n" +
//                        "</S:Envelope>",iscounteritemcode,lineno,storecode, mallitemcode, counteritemcode, itemcode, plucode, invttype, qty, exstk2sales
//                ,originalprice, sellingprice, vipdiscountpercent, vipdiscountless, totaldiscountless1,totaldiscountless2, totaldiscountless
//                ,netamount, bonusearn, tLineno, tendercode, tendertype, tendercategory, payamount, baseamount, excessamount ));
////        LOGGER.info(sb.toString());
//        String requestParam = sb.toString();
    }
}
