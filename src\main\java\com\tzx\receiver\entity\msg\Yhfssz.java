package com.tzx.receiver.entity.msg;

import java.util.Date;

public class Yhfssz
{
  private Integer id;
  private String yhfsbh;
  private String yhmc1;
  private String yhmc2;
  private Date ksrq;
  private Date jsrq;
  private String kssj;
  private String jssj;
  private Double zkl;
  private String zkfa;
  private Double yhje;
  private String zkfs;
  private Integer pqlxid;
  private Double fzje;
  private String yhsx;
  private Integer yhlbid;
  private String bz;
  private String zt;
  private String yl1;
  private String yl2;
  private String yl3;
  private String BUYDISHREL;
  private String USERS;
  private String MEMBERGRADE;
  private String memberGradeNames;
  private Integer menulimit;
  private String freefoodselection;
  private Double freequantity;

  private String ifzdyzk;
  private Double maxzkl;

  public String getIfzdyzk() {
    return ifzdyzk;
  }

  public void setIfzdyzk(String ifzdyzk) {
    this.ifzdyzk = ifzdyzk;
  }

  public Double getMaxzkl() {
    return maxzkl;
  }

  public void setMaxzkl(Double maxzkl) {
    this.maxzkl = maxzkl;
  }

  public Integer getId()
  {
    return this.id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public String getYhfsbh() {
    return this.yhfsbh;
  }

  public void setYhfsbh(String yhfsbh) {
    this.yhfsbh = yhfsbh;
  }

  public String getYhmc1() {
    return this.yhmc1;
  }

  public void setYhmc1(String yhmc1) {
    this.yhmc1 = yhmc1;
  }

  public String getYhmc2() {
    return this.yhmc2;
  }

  public void setYhmc2(String yhmc2) {
    this.yhmc2 = yhmc2;
  }

  public Date getKsrq() {
    return this.ksrq;
  }

  public void setKsrq(Date ksrq) {
    this.ksrq = ksrq;
  }

  public Date getJsrq() {
    return this.jsrq;
  }

  public void setJsrq(Date jsrq) {
    this.jsrq = jsrq;
  }

  public String getKssj() {
    return this.kssj;
  }

  public void setKssj(String kssj) {
    this.kssj = kssj;
  }

  public String getJssj() {
    return this.jssj;
  }

  public void setJssj(String jssj) {
    this.jssj = jssj;
  }

  public Double getZkl() {
    return this.zkl;
  }

  public void setZkl(Double zkl) {
    this.zkl = zkl;
  }

  public String getZkfa() {
    return this.zkfa;
  }

  public void setZkfa(String zkfa) {
    this.zkfa = zkfa;
  }

  public Double getYhje() {
    return this.yhje;
  }

  public void setYhje(Double yhje) {
    this.yhje = yhje;
  }

  public String getZkfs() {
    return this.zkfs;
  }

  public void setZkfs(String zkfs) {
    this.zkfs = zkfs;
  }

  public Integer getPqlxid() {
    return this.pqlxid;
  }

  public void setPqlxid(Integer pqlxid) {
    this.pqlxid = pqlxid;
  }

  public Double getFzje() {
    return this.fzje;
  }

  public void setFzje(Double fzje) {
    this.fzje = fzje;
  }

  public String getYhsx() {
    return this.yhsx;
  }

  public void setYhsx(String yhsx) {
    this.yhsx = yhsx;
  }

  public Integer getYhlbid() {
    return this.yhlbid;
  }

  public void setYhlbid(Integer yhlbid) {
    this.yhlbid = yhlbid;
  }

  public String getBz() {
    return this.bz;
  }

  public void setBz(String bz) {
    this.bz = bz;
  }

  public String getZt() {
    return this.zt;
  }

  public void setZt(String zt) {
    this.zt = zt;
  }

  public String getYl1() {
    return this.yl1;
  }

  public void setYl1(String yl1) {
    this.yl1 = yl1;
  }

  public String getYl2() {
    return this.yl2;
  }

  public void setYl2(String yl2) {
    this.yl2 = yl2;
  }

  public String getYl3() {
    return this.yl3;
  }

  public void setYl3(String yl3) {
    this.yl3 = yl3;
  }

  public String getBUYDISHREL() {
    return BUYDISHREL;
  }

  public void setBUYDISHREL(String BUYDISHREL) {
    this.BUYDISHREL = BUYDISHREL;
  }

  public String getUSERS() {
    return USERS;
  }

  public void setUSERS(String USERS) {
    this.USERS = USERS;
  }

  public String getMEMBERGRADE() {
    return MEMBERGRADE;
  }

  public void setMEMBERGRADE(String MEMBERGRADE) {
    this.MEMBERGRADE = MEMBERGRADE;
  }

  public String getMemberGradeNames() {
    return memberGradeNames;
  }

  public void setMemberGradeNames(String memberGradeNames) {
    this.memberGradeNames = memberGradeNames;
  }

  public Integer getMenulimit() {
    return menulimit;
  }

  public void setMenulimit(Integer menulimit) {
    this.menulimit = menulimit;
  }

  public String getFreefoodselection() {
    return freefoodselection;
  }

  public void setFreefoodselection(String freefoodselection) {
    this.freefoodselection = freefoodselection;
  }

  public Double getFreequantity() {
    return freequantity;
  }

  public void setFreequantity(Double freequantity) {
    this.freequantity = freequantity;
  }
}