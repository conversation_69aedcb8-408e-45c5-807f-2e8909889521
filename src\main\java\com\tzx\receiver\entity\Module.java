package com.tzx.receiver.entity;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
/**
 * <AUTHOR>
 * @Date 2019-04-17
 * @Descption
 **/
public class Module extends BaseEntity
{
    /**
     *
     */
    private static final long	serialVersionUID	= 1L;
    private Module				platformModule;
    private Long				parentId;
    private Short				level1;
    private Long				moduleName;
    private String				zhCode;
    private String				moduleCode;
    private String				moduleType;
    private String				moduleLink;
    private String				link;
    private String				imgSrc;
    private String				helpLink;
    private String				moduleDesc;
    private String				moduleKey;
    private String				memo;
    private Date				createDate;
    private String				creator;
    private String				status;
    private String				sysLevel;

    private List<Module>		childList			= new ArrayList<Module>();

    /**
     * @return the childList
     */
    public List<Module> getChildList()
    {
        return childList;
    }

    /**
     * @param childList
     *            the childList to set
     */
    public void setChildList(List<Module> childList)
    {
        this.childList = childList;
    }

    /**
     * @return the platformModule
     */
    public Module getPlatformModule()
    {
        return platformModule;
    }

    /**
     * @param platformModule
     *            the platformModule to set
     */
    public void setPlatformModule(Module platformModule)
    {
        this.platformModule = platformModule;
    }

    /**
     * @return the level
     */
    public Short getLevel1()
    {
        return level1;
    }

    /**
     * @param level
     *            the level to set
     */
    public void setLevel1(Short level)
    {
        this.level1 = level;
    }

    /**
     * @return the moduleName
     */
    public Long getModuleName()
    {
        return moduleName;
    }

    /**
     * @param moduleName
     *            the moduleName to set
     */
    public void setModuleName(Long moduleName)
    {
        this.moduleName = moduleName;
    }

    /**
     * @return the moduleCode
     */
    public String getModuleCode()
    {
        return moduleCode;
    }

    /**
     * @param moduleCode
     *            the moduleCode to set
     */
    public void setModuleCode(String moduleCode)
    {
        this.moduleCode = moduleCode;
    }

    /**
     * @return the moduleType
     */
    public String getModuleType()
    {
        return moduleType;
    }

    /**
     * @param moduleType
     *            the moduleType to set
     */
    public void setModuleType(String moduleType)
    {
        this.moduleType = moduleType;
    }

    /**
     * @return the moduleLink
     */
    public String getModuleLink()
    {
        return moduleLink;
    }

    /**
     * @param moduleLink
     *            the moduleLink to set
     */
    public void setModuleLink(String moduleLink)
    {
        this.moduleLink = moduleLink;
    }

    /**
     * @return the imgSrc
     */
    public String getImgSrc()
    {
        return imgSrc;
    }

    /**
     * @param imgSrc
     *            the imgSrc to set
     */
    public void setImgSrc(String imgSrc)
    {
        this.imgSrc = imgSrc;
    }

    /**
     * @return the helpLink
     */
    public String getHelpLink()
    {
        return helpLink;
    }

    /**
     * @param helpLink
     *            the helpLink to set
     */
    public void setHelpLink(String helpLink)
    {
        this.helpLink = helpLink;
    }

    /**
     * @return the moduleDesc
     */
    public String getModuleDesc()
    {
        return moduleDesc;
    }

    /**
     * @param moduleDesc
     *            the moduleDesc to set
     */
    public void setModuleDesc(String moduleDesc)
    {
        this.moduleDesc = moduleDesc;
    }

    /**
     * @return the moduleKey
     */
    public String getModuleKey()
    {
        return moduleKey;
    }

    /**
     * @param moduleKey
     *            the moduleKey to set
     */
    public void setModuleKey(String moduleKey)
    {
        this.moduleKey = moduleKey;
    }

    /**
     * @return the memo
     */
    public String getMemo()
    {
        return memo;
    }

    /**
     * @param memo
     *            the memo to set
     */
    public void setMemo(String memo)
    {
        this.memo = memo;
    }

    /**
     * @return the createDate
     */
    public Date getCreateDate()
    {
        return createDate;
    }

    /**
     * @param createDate
     *            the createDate to set
     */
    public void setCreateDate(Date createDate)
    {
        this.createDate = createDate;
    }

    /**
     * @return the creator
     */
    public String getCreator()
    {
        return creator;
    }

    /**
     * @param creator
     *            the creator to set
     */
    public void setCreator(String creator)
    {
        this.creator = creator;
    }

    /**
     * @return the status
     */
    public String getStatus()
    {
        return status;
    }

    /**
     * @param status
     *            the status to set
     */
    public void setStatus(String status)
    {
        this.status = status;
    }

    /**
     * @return the sysLevel
     */
    public String getSysLevel()
    {
        return sysLevel;
    }

    /**
     * @param sysLevel
     *            the sysLevel to set
     */
    public void setSysLevel(String sysLevel)
    {
        this.sysLevel = sysLevel;
    }

    /**
     * @return the parentId
     */
    public Long getParentId()
    {
        return parentId;
    }

    /**
     * @param parentId
     *            the parentId to set
     */
    public void setParentId(Long parentId)
    {
        this.parentId = parentId;
    }

    /**
     * @return the zhCode
     */
    public String getZhCode()
    {
        return zhCode;
    }

    /**
     * @param zhCode
     *            the zhCode to set
     */
    public void setZhCode(String zhCode)
    {
        this.zhCode = zhCode;
    }

    public String getLink()
    {
        return link;
    }

    public void setLink(String link)
    {
        this.link = link;
    }

}