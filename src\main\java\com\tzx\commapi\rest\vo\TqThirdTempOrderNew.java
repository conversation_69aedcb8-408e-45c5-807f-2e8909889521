package com.tzx.commapi.rest.vo;

import java.util.Date;

/**
 * Created by <PERSON><PERSON><PERSON> on 2020-09-09.
 */
public class TqThirdTempOrderNew {
    private Long id;
    private String billnum;
    private String reportdate;
    private String orderno;
    private Integer status;
    private Integer errcount;
    private Integer runinterval;
    private String datatype;
    private String  createtime;
    private String updatetime;
    private String remark;
    private String type;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBillnum() {
        return billnum;
    }

    public void setBillnum(String billnum) {
        this.billnum = billnum;
    }

    public String getReportdate() {
        return reportdate;
    }

    public void setReportdate(String reportdate) {
        this.reportdate = reportdate;
    }

    public String getOrderno() {
        return orderno;
    }

    public void setOrderno(String orderno) {
        this.orderno = orderno;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getErrcount() {
        return errcount;
    }

    public void setErrcount(Integer errcount) {
        this.errcount = errcount;
    }

    public Integer getRuninterval() {
        return runinterval;
    }

    public void setRuninterval(Integer runinterval) {
        this.runinterval = runinterval;
    }

    public String getDatatype() {
        return datatype;
    }

    public void setDatatype(String datatype) {
        this.datatype = datatype;
    }

    public String getCreatetime() {
        return createtime;
    }

    public void setCreatetime(String createtime) {
        this.createtime = createtime;
    }

    public String getUpdatetime() {
        return updatetime;
    }

    public void setUpdatetime(String updatetime) {
        this.updatetime = updatetime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
