package com.tzx.kvs.rest.service;

import com.tzx.kvs.common.KvsData;

import net.sf.json.JSONObject;

public interface IKvsInfoService {

	public void getKvsInfo(KvsData data, JSONObject requestJson);

	public void removeBill(KvsData data, JSONObject requestJson);
	
	public void getRecoverKvsInfo(KvsData data, JSONObject requestJson);
	
	public void recoverRemoveBill(KvsData data, JSONObject requestJson);
	
}
