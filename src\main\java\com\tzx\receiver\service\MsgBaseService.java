package com.tzx.receiver.service;

import java.util.List;
import java.util.Map;

import com.tzx.publics.listener.InitDataListener;
import com.tzx.publics.util.Util;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.tzx.receiver.dao.MsgBaseDao;
import com.tzx.receiver.common.msg.MsgConstant;
import com.tzx.receiver.common.utils.TzxSimpleHttpClient;

@Service
@Transactional(readOnly = true)
public class MsgBaseService {
	
	protected Logger	logger	= LoggerFactory.getLogger(getClass());

	@Autowired
	MsgBaseDao dao;

	@Transactional(readOnly = false)
	public void truncat(String tbnames){
		dao.truncat(tbnames);
	}
	
	@Transactional(readOnly = false)
	public void insert(String tbname, Object object){
		dao.insert(tbname, object);
	}
	
	@Transactional(readOnly = false)
	public void update( String tbname, Object object,String where){
		dao.update(tbname, object, where);
	}
	
	@Transactional(readOnly = false)
	public void delete(String tbname, Object object,String where){
		dao.delete(tbname, object, where);
	}
	
	// 初始化下发的处理
	@Transactional(readOnly = false)
	public void initxf(boolean isTrunct ,String tableNames, List list,Map<String,Object> params){ 
		String dbName = params.get("dbName")==null ? null : params.get("dbName").toString();
		String url = params.get("url")==null ? null : params.get("url").toString();
		String zlbh = params.get("zlbh")==null ? null : params.get("zlbh").toString();
		String jgxh = params.get("jgxh")==null ? null : params.get("jgxh").toString();
		String versionsTime = params.get("versionsTime")==null ? null : params.get("versionsTime").toString();
		
		int count = 0;
		if(isTrunct){
			dao.truncat(tableNames);
		}
		for(Object el : list){
			dao.insert(tableNames, el);
		}
		if("".equals(url)||url==null){
			logger.debug("没有下发监听回调地址");
		}else{
			params.put("count",list.size());
			String POS_UPDATEMENU_UPLOAD = InitDataListener.ggcsMap.get("POS_UPDATEMENU_UPLOAD");
			if(null!=POS_UPDATEMENU_UPLOAD && POS_UPDATEMENU_UPLOAD.equals("1")){
				dao.insertXfVerLog(params);
			}
			else{
				logger.info("未启用POS更新菜单回传总部参数POS_UPDATEMENU_UPLOAD:"+POS_UPDATEMENU_UPLOAD);
			}
			logger.info("返回开始:"+ "begin："+count);
			logger.info("返回开始:"+ list.size() + "");
			logger.info("返回开始:url"+ url + "");
			
			if(count==0){
				count=list.size();
			}
			TzxSimpleHttpClient.HttpClientByPostNew(count,url,list.size() + "",
					zlbh, dbName, jgxh, versionsTime);
			logger.info("返回结束:"+ "over");
		}

		String posDownloadMsg = InitDataListener.ggcsMap.get("POS_DOWNLOAD_MSG");
		if (!Util.isNullOrEmpty(posDownloadMsg) && posDownloadMsg.equals("1")) {
			dao.insertXfMsgLog(params);
		}
	}
	
	
	// 日常下发的处理
	@Transactional(readOnly = false)
	public void dailyxf(String operator ,String tableNames, List datas1,String where){
		String primarykey = where;
		
		for (int i = 0; i < datas1.size(); i++) {
			Object objdata = datas1.get(i);
			if (operator.equalsIgnoreCase(MsgConstant.OPERATOR_INSERT)) {// 新增
				dao.insert(tableNames, objdata);
			} else if (operator.equalsIgnoreCase(MsgConstant.OPERATOR_UPDATE)) {// 修改
				boolean isExist = dao.find(tableNames, objdata, primarykey);
				// System.out.println(isExist);
				if (isExist) {
					dao.update(tableNames, objdata, primarykey);		
				} else {
					dao.insert(tableNames, objdata);
				}
			} else if (operator.equalsIgnoreCase(MsgConstant.OPERATOR_DELETE)) {// 删除
				dao.delete(tableNames, objdata, primarykey);
			}

		}
		/*
		logger.info("返回开始:"+ "begin");
		logger.info("返回开始:"+ datas1.size() + "");
		TzxSimpleHttpClient.HttpClientByPostNew(url,datas1.size() + "",
				zlbh, dbName, jgxh, versionsTime);
		logger.info("返回结束:"+ "over");
		*/
	}
	
	@Transactional(readOnly = true)
	public String queryOrganNo(){
		String organNo = dao.queryOrganNo();
		return organNo;
	}
	
}
