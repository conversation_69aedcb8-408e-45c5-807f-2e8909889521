package com.tzx.miniapp.rest.vo;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Entity;

@Entity
public class BtSaleoutdishType implements Serializable {

	private String yddh;
	private Integer xmid;
	private String xmbh;
	private String xmmc;
	private double xmsl;
	private Date create_time;
	private Date restrict_time;
	private Integer step;

	public String getYddh() {
		return yddh;
	}

	public void setYddh(String yddh) {
		this.yddh = yddh;
	}

	public Integer getXmid() {
		return xmid;
	}

	public void setXmid(Integer xmid) {
		this.xmid = xmid;
	}

	public String getXmbh() {
		return xmbh;
	}

	public void setXmbh(String xmbh) {
		this.xmbh = xmbh;
	}

	public String getXmmc() {
		return xmmc;
	}

	public void setXmmc(String xmmc) {
		this.xmmc = xmmc;
	}

	public double getXmsl() {
		return xmsl;
	}

	public void setXmsl(double xmsl) {
		this.xmsl = xmsl;
	}

	public Date getCreate_time() {
		return create_time;
	}

	public void setCreate_time(Date create_time) {
		this.create_time = create_time;
	}

	public Date getRestrict_time() {
		return restrict_time;
	}

	public void setRestrict_time(Date restrict_time) {
		this.restrict_time = restrict_time;
	}

	public Integer getStep() {
		return step;
	}

	public void setStep(Integer step) {
		this.step = step;
	}

}
