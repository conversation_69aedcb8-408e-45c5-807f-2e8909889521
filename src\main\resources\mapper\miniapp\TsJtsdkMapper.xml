<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.miniapp.rest.mapper.MiniAppTsJtsdkMapper">
	<select id="findByIpdz" resultType="com.tzx.miniapp.rest.model.TsJtsdk" >
		select JTBH,IPDZ,JTSX,JTBM,DBVER from ts_jtsdk where JTBM = #{jtbm}
	</select>
	<select id="getMaxJtbh" resultType="com.tzx.miniapp.rest.model.TsJtsdk" >
		select MAX(JTBH) as JTBH from ts_jtsdk where JTSX = #{jtsx}
	</select>
	<update id="updataIpdz">
		update ts_jtsdk set ipdz = #{ipdz} where jtbm = #{jtbm}
	</update>
	<select id="pTjskjqj" resultType="java.lang.Integer" >
		select * from P_TJSKJQJ(#{sbbrq},#{sskjh})
	</select>
</mapper>
