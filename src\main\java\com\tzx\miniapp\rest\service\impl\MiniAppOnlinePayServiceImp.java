package com.tzx.miniapp.rest.service.impl;

import com.tzx.publics.common.SimpleRestClient;
import com.tzx.miniapp.rest.service.IMiniAppOnlinePayService;
import com.tzx.miniapp.rest.service.IMiniAppPosEhcacheService;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
public class MiniAppOnlinePayServiceImp implements IMiniAppOnlinePayService {

	private final static Logger LOGGER = LoggerFactory.getLogger(MiniAppOnlinePayServiceImp.class);
	@Autowired
	private IMiniAppPosEhcacheService posEhcacheServie;

	@Override
	public String doService(String url, String httpid, String json) {
		LOGGER.info("request url={},uuid="+httpid+",body={}",url,json);
		String response = posEhcacheServie.getResponseJson(httpid);
		if (null != response && response.length() > 0) {
			//return response;
		} else {
			RestTemplate rest = SimpleRestClient.getClient();
			response = rest.postForObject(url, json, String.class);
			if(response!=null) {
				try {
					JSONObject obj = JSONObject.fromObject(response);
					if(obj.containsKey("success")) {
						posEhcacheServie.addResponseJson(httpid, response);
					}else {
						response = null;
					}
				}catch(Exception e){
					LOGGER.error("Ignore this exception", e);
				}
			}
			if(response == null) {
				JSONObject result = new JSONObject();
				result.put("msg", "请求失败");
				result.put("success", "false");
				result.put("data", "");
			}
		}
		LOGGER.info("response url={},uuid="+httpid+",body={}",url,response);
		return response;
	}
}
