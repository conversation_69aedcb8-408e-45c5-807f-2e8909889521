package com.tzx.receiver.common.upload;

import com.tzx.commapi.rest.vo.TqTask;
import com.tzx.commapi.rest.service.ITaskApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2020-08-03
 * @Descption
 **/
@Component
@Lazy
public class UploadTaskTableDispatcher {
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private UpLoadTaskList upLoadTaskList;
    @Autowired
    private UpLoadParamsList upLoadParamsList;
    @Autowired
    private ITaskApiService taskApiService;
    public void doScanTaskTable(){



        String sql = "SELECT DISTINCT tasktype,secondtype,objid,objparams,createtime,cwlxbh, zdzt, jzbbrq, source, wmtype, jzsj FROM TQ_TASK WHERE tasktype = 'UPLOAD' ";
        SqlRowSet sqlRowSet = jdbcTemplate.queryForRowSet(sql);
        while (sqlRowSet.next()){
            //删除一下因为网络原因导致的上传失败的数据，因为此时触发的上传数据是最新的数据，如果自动补传网络失败的任务在后面，有
            //可能会造成前面的数据冲掉最新数据
            String exeSQl = "delete from tq_uploadlog where runresult = '-1' and command = '" + sqlRowSet.getString("secondtype") +
                    "' and sourcemsg like '%extend=" + sqlRowSet.getString("objparams") + "%'";
            jdbcTemplate.execute(exeSQl);


            //插入到任务队列
            String msg = "ACTION=" + sqlRowSet.getString("secondtype") + "|BBRQ="+
                    UploadGloVar.getReportDate() + "|OptBH=9999|OptName=9999|FDJGXH=" +
                    UploadGloVar.getOrganizeID() + "|extend=" + sqlRowSet.getString("objparams");

            if(upLoadParamsList.findUploadParamByCommand(sqlRowSet.getString("secondtype") )!=null){

                upLoadTaskList.addTask(msg);
            }



            //added by zhouxh  物业接口处理
            TqTask tqTask  = new TqTask(sqlRowSet.getString("tasktype"),sqlRowSet.getString("secondtype"),sqlRowSet.getString("objid"),sqlRowSet.getString("objparams"),sqlRowSet.getDate("createtime"),sqlRowSet.getString("cwlxbh")
                    ,sqlRowSet.getString("zdzt"),sqlRowSet.getDate("jzbbrq"),sqlRowSet.getString("source"),sqlRowSet.getInt("wmtype"),sqlRowSet.getTimestamp("jzsj"));
            //Add by SunFumeng 上传至PMS 2022-5-10 14:59:57
            taskApiService.doPMSSyncOrder(tqTask,true);
            //Add End

            taskApiService.UploadJPData(tqTask);



            // 方兴物业接口
            //taskApiService.UploadFangXingData(tqTask);
            
			// 科传物业接口 , 走新版实时上传 uploadWy3Data
			// taskApiService.UploadKeChuanData(tqTask);
            
            // 企迈订单上传接口处理
            taskApiService.uploadQimaiData(tqTask);
            
            // 新版通用实时上传物业接口
            taskApiService.uploadWy3Data(tqTask);

            //删除同样的任务，目的是如果有短时间内有多个任务的时候，只上传一次就好。这样能优化速度
            exeSQl = "delete from TQ_TASK where tasktype = 'UPLOAD' and secondtype = '"  + sqlRowSet.getString("secondtype") +"' " +
                    " and objparams = '" + sqlRowSet.getString("objparams") + "'";

            jdbcTemplate.execute(exeSQl);

        }

    }
}
