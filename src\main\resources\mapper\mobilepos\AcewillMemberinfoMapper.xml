<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.mobilepos.rest.mapper.MobilePosAcewillMemberinfoMapper">

	<select id="getAcewillMembers" resultType="com.tzx.mobilepos.rest.model.TqMemberInfo">
		select * from tq_memberinfo where billid = #{zdbh} and is_usable = 0 
		<if test="scancode != null and scancode != ''">
			and scancode = #{scancode} 
		</if>
		order by id
	</select>

	<update id="updateUsaByCardcode">
		update tq_memberinfo set is_usable = #{usable} where billid = #{zdbh} and scancode &lt;&gt; #{scancode}
	</update>
	
	<update id="updateToBalance">
		update tq_memberinfo set scancode = scancode 
		<if test="balance != null and balance !=-1">
			, balance = #{balance} 
		</if>
		<if test="credit != null and credit !=-1">
			, credit = #{credit} 
		</if>
		where billid = #{zdbh} and scancode = #{scancode}
	</update>
	
	<select id="getACERifUrl" resultType="java.lang.String" >
		select sdnr from ts_ggcsk where sdbt = 'ACE_WILL_BOH_URL'
	</select>
	
	<select id="getAcewillScancode" resultType="com.tzx.mobilepos.rest.model.TqMemberInfo">
		select * from tq_memberinfo where billid = #{zdbh} and is_usable = 0 and is_pv <![CDATA[ <> ]]>'1' limit 1
	</select>
	
	<select id="findBalanceAmount" resultType="java.lang.Double" >
		select coalesce(sum(fkls.fkje), 0) as balance_amount from tq_fklslsk fkls left join ts_fkfssdk fksd on fkls.jzid = fksd.id where fkls.kdzdbh = #{zdbh} and fksd.fkfsbh = #{fkfsbh}
	</select>
	
	<select id="findBalanceYhAmount" resultType="java.lang.Double" >
		select coalesce(sum(zrje), 0.00) from tq_wdk where kdzdbh = #{zdbh} and yhfs in ('85','87')
	</select>
	
	<select id="findPointsYhAmount" resultType="java.lang.Double" >
		select coalesce(sum(zrje), 0.00) from tq_wdk where kdzdbh = #{zdbh} and yhfs in ('86')
	</select>
	
	<select id="findBalanceList" resultType="com.tzx.mobilepos.rest.model.TqFklslsk" >
		select fkls.* from tq_fklslsk fkls left join ts_fkfssdk fksd on fkls.jzid = fksd.id where fkls.kdzdbh = #{zdbh} and fksd.fkfsbh = #{fkfsbh} order by fkls.fkje desc
	</select>
	
	<update id="updateFklsById">
		update tq_fklslsk set fkje = #{fkje} where id = #{id} 
	</update>
	
	<delete id="delFklsById">
		delete from tq_fklslsk where id = #{id}
	</delete>

	<update id="updateZdkByCwlxbh">
		update tq_zdk set cwlxbh = #{cwlxbh} 
		where kdzdbh = #{kdzdbh}
	</update>
	
	<select id="getIsVipPrice" resultType="java.lang.String">
		select COALESCE(isVipPrice, 'N') from ts_crm_member_rule where id = #{grade} limit 1
	</select>
	
	<select id="findBalanceAmountVq" resultType="java.lang.Double" >
		select coalesce(sum(fkls.fkje), 0) as balance_amount from vq_fklsk fkls left join ts_fkfssdk fksd on fkls.jzid = fksd.id where fkls.kdzdbh = #{zdbh} and fksd.fkfsbh = #{fkfsbh}
	</select>

	<select id="getTqBillOtherinfo" resultType="com.tzx.mobilepos.rest.vo.TqBillOtherinfoVo" >
		select * from tq_bill_otherinfo where billno = #{zdbh} limit 1
	</select>

	<select id="getIsPvMembers" resultType="com.tzx.mobilepos.rest.model.TqMemberInfo">
		select * from tq_memberinfo where billid = #{zdbh} and is_pv = #{isPv}
	</select>

</mapper>
