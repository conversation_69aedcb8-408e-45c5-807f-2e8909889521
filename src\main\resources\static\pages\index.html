<!DOCTYPE html>
<html lang="zh-CN" xmlns:align="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <title>天子星门店服务</title>
    <!-- Bootstrap -->
    <link href="static/bootstrap-3.3.7-dist/css/bootstrap.css" rel="stylesheet">

    <!-- HTML5 shim 和 Respond.js 是为了让 IE8 支持 HTML5 元素和媒体查询（media queries）功能 -->
    <!-- 警告：通过 file:// 协议（就是直接将 html 页面拖拽到浏览器中）访问页面时 Respond.js 不起作用 -->
    <!--[if lt IE 9]>
    <script src="static/js/common/html5shiv.min.js"></script>
    <script src="static/js/common/respond.min.js"></script>
    <![endif]-->
    <script type="text/javascript" src="static/js/common/jquery.min.js"></script>
    <script type="text/javascript" src="static/bootstrap-3.3.7-dist/js/bootstrap.min.js"></script>
    <script type="text/javascript" src="static/js/pages/index.js"></script>
</head>
<body>
<div class="container-fluid">
    <!-- 导航条-->
    <nav class="navbar navbar-default navbar-static-top" style="background-color: #337ab7">
        <!-- Brand and toggle get grouped for better mobile display -->
        <div class="navbar-header">
            <a class="navbar-brand" href="#"><span style=" color: #ffffff; font-size: 26px; font-family: STKaiti">&nbsp;&nbsp;天子星门店服务&nbsp;&nbsp;&nbsp;&nbsp;</span></a>
        </div>

        <!-- Collect the nav links, forms, and other content for toggling -->
        <div class="collapse navbar-collapse" id="bs-example-navbar-collapse-1">
            <ul class="nav navbar-nav" id="nvr">
                <!--              <li class="active"><a href="#uploadlog" data-toggle="tab">数据上传日志</a></li>
                              <li><a href="#downlog" data-toggle="tab">数据下发日志日志</a></li>
                              <li><a href="#version" data-toggle="tab">程序版本号</a></li>-->
                <li class="active"><a href="#" onclick="changenvr(0,'static/pages/upload.html')" >数据上传日志</a>
                </li>
               <!-- <li><a href="#" onclick="changenvr(1,'static/pages/down.html')" >数据下发日志日志</a></li>
-->
                <li><a href="#" onclick="changenvr(1,'static/pages/version.html')">程序版本号</a></li>
                -
            </ul>
        </div><!-- /.navbar-collapse -->
        <!--  内容 -->
    </nav>
    <!--    <div id="myTabContent" class="tab-content">
            <div class="tab-pane active" id="uploadlog">
                <iframe scrolling="no" name="uploadtb" frameborder="0" src="static/pages/upload.html" width="100%"
                        onload="this.height=0;var fdh=(this.Document?this.Document.body.scrollHeight:this.contentDocument.body.offsetHeight);this.height=(fdh>700?fdh:700)"></iframe>

            </div>
            <div class="tab-pane fade" id="downlog">
                <p>数据下发</p>
            </div>
            <div class="tab-pane fade" id="version">
                <p>版本信息</p>
            </div>
        </div>-->

    <iframe id="imain"  scrolling="no" name="uploadtb" frameborder="0" src="static/pages/upload.html" width="100%"
            onload="resetframeheight(this)"
    ></iframe>


</div>
</body>
</html>