package com.tzx.commapi.rest.vo;


import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by Zhouxh on 2020-05-11.
 */
public class UseYhfsParam {
    private String billid;//账单编号
    private String yzm;//验证码，很多为空
    private BigDecimal disAmount;//优惠金额
    private Integer   yhfsId;//优惠方式id
    //操作类型 91003 不计收入付款类型
    //操作类型 91002 微生活积分优惠拆分、储值优惠拆分
    //操作类型 1002 支付宝三方支付优惠
    private Integer   opType;
    private BigDecimal dslj;//多收礼卷金额
    private Integer    jzid;//微生活积分和储值拆分 对应的付款id
    private Integer    fklsid;//不计收入付款更新id
    private Date bbrq;//报表日期
    private Boolean    hasDslj;//是否有多收礼卷
    private Boolean    isInnerCalMoney;//是否方法内部重新计算金额，默认为true，如果外部统一调用，这个参数可谓false
    private String     skjh;//机台编号
    private String     jgtxbh;//对应的价格体系编号
    public String getBillid() {
        return billid;
    }

    public void setBillid(String billid) {
        this.billid = billid;
    }

    public String getYzm() {
        if(null==yzm){
            yzm = "";
        }
        return yzm;
    }

    public void setYzm(String yzm) {
        this.yzm = yzm;
    }

    public BigDecimal getDisAmount() {
        if(null==disAmount){
            disAmount = new BigDecimal(0);
        }
        return disAmount;
    }

    public void setDisAmount(BigDecimal disAmount) {
        this.disAmount = disAmount;
    }

    public Integer getYhfsId() {
        return yhfsId;
    }

    public void setYhfsId(Integer yhfsId) {
        this.yhfsId = yhfsId;
    }

    public Integer getOpType() {
        return opType;
    }

    public void setOpType(Integer opType) {
        this.opType = opType;
    }

    public BigDecimal getDslj() {
        return dslj;
    }

    public void setDslj(BigDecimal dslj) {
        if(null==dslj){
            dslj = new BigDecimal(0);
        }
        this.dslj = dslj;
    }

    public Integer getJzid() {
        if(null==jzid){
            jzid = -1;
        }
        return jzid;
    }

    public void setJzid(Integer jzid) {
        this.jzid = jzid;
    }

    public Integer getFklsid() {
        if(null==fklsid){
            fklsid = -1;
        }
        return fklsid;
    }

    public void setFklsid(Integer fklsid) {
        this.fklsid = fklsid;
    }

    public Date getBbrq() {
        return bbrq;
    }

    public void setBbrq(Date bbrq) {
        this.bbrq = bbrq;
    }

    public Boolean getHasDslj() {
        if(null==dslj){
            hasDslj = false;
        }
        return hasDslj;
    }

    public void setHasDslj(Boolean hasDslj) {
        this.hasDslj = hasDslj;
    }

    public Boolean getInnerCalMoney() {
        if(null==isInnerCalMoney){
            isInnerCalMoney = true;
        }
        return isInnerCalMoney;
    }

    public void setInnerCalMoney(Boolean innerCalMoney) {
        isInnerCalMoney = innerCalMoney;
    }

    public String getSkjh() {
        return skjh;
    }

    public void setSkjh(String skjh) {
        this.skjh = skjh;
    }

    public String getJgtxbh() {
        return jgtxbh;
    }

    public void setJgtxbh(String jgtxbh) {
        this.jgtxbh = jgtxbh;
    }

    @Override
    public String toString(){
        return  new StringBuilder("账单号=").append(billid).append(",操作类型=").append(opType)
                .append(",yzm=").append(yzm).append(",优惠金额=").append(disAmount)
                .append(",yhfsid=").append(yhfsId).append(",dslj=").append(dslj)
                .append(",jzid=").append(jzid).append(",fklsid=").append(fklsid)
                .append(",bbrq=").append(bbrq).append(",hasDslj=").append(getHasDslj())
                .append(",isInnerCalMoney=").append(getInnerCalMoney())
                .append(",skjh=").append(getSkjh())
                .append(",jgtxbh=").append(getJgtxbh())
                .toString();
    }
}
