package com.tzx.publics.util;


import com.google.gson.ExclusionStrategy;
import com.google.gson.FieldAttributes;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import java.util.HashSet;
import java.util.Set;

public final class GsonHelper
{
	private static Gson gson;

	public static Gson getGson()
	{
		if (gson == null)
		{
			// GsonHelper.SuperClassExclusionStrategy strategy = new
			// GsonHelper.SuperClassExclusionStrategy(null, Model.class);
			gson = new GsonBuilder().create();
		}
		return gson;
	}

	public static class SuperClassExclusionStrategy implements ExclusionStrategy
	{

		private final Class<?>	excludedThisClass;
		private final Class<?>	excludedThisClassFields;


		public SuperClassExclusionStrategy(Class<?> excludedThisClass, Class<?> excluedThisClassFields)
		{
			this.excludedThisClass = excludedThisClass;
			this.excludedThisClassFields = excluedThisClassFields;
		}

		@Override
		public boolean shouldSkipClass(Class<?> clazz)
		{
			if (clazz == null) return false;
			if (clazz.equals(excludedThisClass)) return true;
			return shouldSkipClass(clazz.getSuperclass());
		}

		@Override
		public boolean shouldSkipField(FieldAttributes f)
		{
			return f.getDeclaringClass().equals(excludedThisClassFields) || ignoreFileds.contains(f.getName());
		}

		private Set<String> ignoreFileds = new HashSet<String>();

		public void putIgnoreFields(String... args)
		{
			for (String s : args)
			{
				if (s == null || s.length() == 0) continue;
				else ignoreFileds.add(s);
			}
		}
	}
}
