package com.tzx.receiver.common.msg.xml;

import org.dom4j.*;
import org.dom4j.io.OutputFormat;
import org.dom4j.io.SAXReader;
import org.dom4j.io.XMLWriter;

import java.io.*;
import java.util.Iterator;
import java.util.List;

/**
 * 
 * 数据包构建器，封装了读写XML文档的功能，使得读取或生成头元素和消息元素更容易。
 * 
 * <AUTHOR>
 * 
 */
public class DataPacketBuilder
{

	private OutputFormat	outputFormat;

	private boolean			isPrettyFormat;

	private Document		document;

	private HeadElement		head;

	private MsgElement		msg;

	/**
	 * 构造一个 DataPacketBuilder 对象。
	 * 
	 */

	public DataPacketBuilder()
	{
		outputFormat = new OutputFormat();
		outputFormat.setNewLineAfterDeclaration(false);
		outputFormat.setEncoding(DPC.SUTF8Encoding); // GXY 080924
		setIsPrettyFormat(true);
	}

	public OutputFormat getOutputFormat()
	{
		return outputFormat;
	}

	public boolean getIsPrettyFormat()
	{
		return isPrettyFormat;
	}

	public void setIsPrettyFormat(boolean isPrettyFormat)
	{
		if (isPrettyFormat)
		{
			outputFormat.setIndentSize(2);
			outputFormat.setNewlines(true);
			outputFormat.setTrimText(true);
			outputFormat.setPadText(false); // 每个元素后面是否加上空格
			outputFormat.setIndent("\t");
		}
		else
		{
			outputFormat.setIndent(false);
			outputFormat.setNewlines(false);
			outputFormat.setTrimText(true);
		}
		this.isPrettyFormat = isPrettyFormat;
	}

	protected Document newDocument()
	{
		Document document = DocumentHelper.createDocument();
		document.setXMLEncoding(DPC.SUTF8Encoding);
		return document;
	}

	public Document getDocument()
	{
		if (document == null)
		{
			document = newDocument();
		}
		return document;
	}

	public Element getRootElement()
	{
		Element rootElement = getDocument().getRootElement();
		if (rootElement == null)
		{
			rootElement = getDocument().addElement(DPC.SDocumentNodeName);
		}
		return rootElement;
	}

	/**
	 * 
	 * 获取指向HEAD元素的<code>HeadElement</code>对象。如果XML文档中HEAD元素不存在，则自动创建此元素。
	 * 
	 * @return 指向HEAD元素的<code>{@link HeadElement}</code>对象。
	 */

	public HeadElement getHead()
	{
		if (head == null)
		{
			head = new HeadElement(this, getNode(getRootElement(), DPC.SHeadNodeStr));
		}
		return head;
	}

	/**
	 * 
	 * 获取指向MSG元素的<code>MsgElement</code>对象。如果XML文档中MSG元素不存在，则自动创建此元素。
	 * 
	 * @return 指向MSG元素的<code>{@link MsgElement}</code>对象。
	 */

	public MsgElement getMsg()
	{
		if (msg == null)
		{
			msg = new MsgElement(this, getNode(getRootElement(), DPC.SMsgNodeStr));
		}
		return msg;
	}

	/**
	 * 重建XML文档。
	 * 
	 */

	public void reset()
	{
		msg = null;
		head = null;
		document = null;
	}

	/**
	 * 加载XML文档内容从指定的<code>InputStream</code>对象。如果文档内容格式有问题会抛出异常。
	 * 
	 * @param Stream
	 *            从InputStream加载。
	 * @throws DocumentException
	 * @throws UnsupportedEncodingException
	 */

	public void loadFromStream(InputStream Stream) throws DocumentException
	{

		SAXReader saxReader = new SAXReader();
		/*
		 * SAXReader saxReader = new SAXReader();
		 * saxReader.setEncoding("UTF-8"); document = saxReader.read(Stream);
		 */
		InputStreamReader strInStream = new InputStreamReader(Stream); // gxy
																		// 080924
		reset();
		document = saxReader.read(strInStream);

	}

	/**
	 * 保存XML文档内容到指定的<code>OutputStream</code>对象。文档内容输出的格式由{@link #getOutputFormat()}参数决定。
	 * 
	 * @param Stream
	 * @throws IOException
	 */

	public void saveToStream(OutputStream Stream) throws IOException
	{
		XMLWriter xmlWriter = new XMLWriter(Stream, outputFormat);
		xmlWriter.write(getDocument());
		xmlWriter.close();
	}

	/**
	 * 加载XML文档内容从指定的<code>File</code>对象。如果文档内容格式有问题会抛出异常。
	 * 
	 * @param file
	 *            <code>File</code>对象的XML文档内容。
	 * @throws DocumentException
	 */

	public void loadFromFile(File file) throws DocumentException
	{
		SAXReader saxReader = new SAXReader();
		reset();
		document = saxReader.read(file);
	}

	/**
	 * 保存XML文档内容到指定的<code>File</code>对象。文档内容输出的格式由{@link #getOutputFormat()}参数决定。
	 * 
	 * @param file
	 *            <code>File</code>对象的XML文档内容。
	 * @throws IOException
	 */

	public void saveToFile(File file) throws IOException
	{
		XMLWriter xmlWriter = new XMLWriter(new FileWriter(file), outputFormat);
		xmlWriter.write(getDocument());
		xmlWriter.close();
	}

	/**
	 * 从指定的<code>String</code>对象中加载XML文档内容。
	 * 
	 * @param XML
	 *            <code>String</code>对象的XML文档内容。
	 * @throws DocumentException
	 *             当加载的内容格式有误时抛出此异常。
	 */

	public void loadFromXML(String XML) throws DocumentException
	{
		// InputStream is = new ByteArrayInputStream(XML.getBytes());
		// loadFromStream(is);
		reset();
		document = DocumentHelper.parseText(XML);
	}

	/**
	 * 返回XML文档内容。文档内容输出的格式由{@link #getOutputFormat()}参数决定。
	 * 
	 * @return <code>String</code>对象的XML文档内容。
	 * @throws IOException
	 */

	public String getXML() throws IOException
	{
//		ByteArrayOutputStream os = new ByteArrayOutputStream();
//		saveToStream(os);
//		return os.toString();
		return getDocument().asXML();
	}

	/**
	 * 返回XML文档的一个元素实例。跟据指定的参数查找一个元素实例。
	 * 
	 * @param ParentNode
	 *            元素实例，查找此元素下的所有子元素或子元素的属性。
	 * @param NodeName
	 *            子元素名称，指定要查找的子元素的名称。
	 * @param ByAttrName
	 *            属性名称，指定要查找的子元素的属性名称。
	 * @param ByAttrValue
	 *            属性名称值，指定要查找的子元素的属性名称等于该值的元素。
	 * @param DoCreate
	 *            是否创建，当为<code>true</code>时，如果未找到，则创建相应的子元素，并且写入相应属性名称的值。
	 * @return 如果查找的元素实例存或DoCreate参数为<code>true</code>，返回一个{@link Element}对象，否则返回<code>null</code>。
	 */
	public Element selectNode(Element ParentNode, String NodeName, String ByAttrName, String ByAttrValue, boolean DoCreate)
	{
		Element currentNode, childNode;
		currentNode = ParentNode;
		if (currentNode != null)
		{
			Iterator iter = currentNode.elementIterator();
			while (iter.hasNext())
			{
				childNode = (Element) iter.next();

				if ((childNode.getName().equalsIgnoreCase(NodeName)) && (childNode.attribute(ByAttrName) != null) && (childNode.attributeValue(ByAttrName).equals(ByAttrValue)))
				{
					return childNode;
				}
			}
			if (DoCreate)
			{
				childNode = currentNode.addElement(NodeName);
				childNode.addAttribute(ByAttrName, ByAttrValue);
				return childNode;
			}
			return null;
		}
		else
		{
			return null;
		}
	}

	public Element selectNode(Element ParentNode, String NodeName, int Index, boolean DoCreate)
	{
		Element currentNode, childNode;
		currentNode = ParentNode;
		List list = currentNode.elements();
		if (currentNode != null)
		{
			if ((Index > -1) && (Index < list.size()))
			{
				childNode = (Element) list.get(Index);
				return childNode;
			}
			if (DoCreate)
			{
				childNode = currentNode.addElement(NodeName);
				return childNode;
			}
			return null;
		}
		else
		{
			return null;
		}
	}

	/**
	 * 返回XML文档的一个元素实例。跟据指定的参数查找一个元素实例。
	 * 
	 * @param ParentNode
	 *            元素实例，查找此元素下的所有子元素或子元素的属性。
	 * @param NodeName
	 *            子元素名称，指定要查找的子元素的名称。
	 * @param DoCreate
	 *            是否创建，当为<code>true</code>时，如果未找到，则创建相应的元素，并且写入相应属性名称的值。
	 * @return 如果找到返回一个{@link Element}对象，否则返回<code>null</code>。
	 */

	public Element selectNode(Element ParentNode, String NodeName, boolean DoCreate)
	{
		Element currentNode, childNode;
		currentNode = ParentNode;
		if (currentNode != null)
		{
			childNode = currentNode.element(NodeName);
			if (childNode != null) return childNode;
			if (DoCreate)
			{
				childNode = currentNode.addElement(NodeName);
				return childNode;
			}
			return null;

		}
		else
		{
			return null;
		}
	}

	/**
	 * 返回XML文档的一个元素实例。
	 * 
	 * @param XPath
	 *            元素路径，指定要查找元素的路径。
	 * @param DoCreate
	 *            是否创建，当为<code>true</code>时，如果未找到，则创建相应的元素，并且写入相应属性名称的值。
	 * @return 如果找到返回一个{@link Element}对象，否则返回<code>null</code>。
	 */

	public Element selectNode(String XPath, boolean DoCreate)
	{
		Element currentNode, childNode;
		String path, nodeName;
		int count, p;
		path = DataPacket.fixPath(XPath) + DPC.XPathDelim;
		count = path.length();
		p = 0;
		currentNode = getRootElement();
		for (int i = 0; i <= count - 1; i++)
		{

			if ((count != 1) && (path.charAt(i) == DPC.XPathDelim))
			{
				nodeName = path.substring(p, i);
				childNode = selectNode(currentNode, nodeName, DoCreate);
				if (childNode == null) return null;
				currentNode = childNode;
				p = i + 1;
			}
		}
		return currentNode;
	}

	/**
	 * 返回XML文档的一个元素实例。
	 * 
	 * @param XPath
	 *            元素路径，指定查找元素的路径
	 * @param ByAttrName
	 *            属性名称，指定要查找元素的属性名称。
	 * @param ByAttrValue
	 *            属性名称值，指定要查找元素的属性名称等于该值的元素。
	 * @param DoCreate
	 *            是否创建，当为<code>true</code>时，如果未找到，则创建相应的元素，并且写入相应属性名称的值。
	 * @return 如果找到返回一个{@link Element}对象，否则返回<code>null</code>。
	 */

	public Element selectNode(String XPath, String ByAttrName, String ByAttrValue, boolean DoCreate)
	{
		String newXPath = DataPacket.fixPath(XPath);
		String nodeName = "";
		int i = newXPath.lastIndexOf(DPC.XPathDelim);
		if (i > 1)
		{
			nodeName = newXPath.substring(i + 1, newXPath.length());
			newXPath = newXPath.substring(0, i);
		}
		else
		{
			nodeName = newXPath;
			newXPath = "";
		}

		Element parentNode = selectNode(newXPath, DoCreate);
		if (parentNode != null)
		{
			return selectNode(parentNode, nodeName, ByAttrName, ByAttrValue, DoCreate);
		}
		return null;

	}

	/**
	 * 查找XML文档的一个元素实例。相当于调用<code>SelectNode</code>方法，<code>DoCreate</code>参数为<code>false</code>。
	 * 
	 * @see #selectNode(Element, String, boolean)
	 */
	public Element findNode(Element ParentNode, String NodeName)
	{
		return selectNode(ParentNode, NodeName, false);
	}

	/**
	 * 查找XML文档的一个元素实例。相当于调用<code>SelectNode</code>方法，<code>DoCreate</code>参数为<code>false</code>。
	 * 
	 * @see #selectNode(Element, String, String, String, boolean)
	 */
	public Element fndNode(Element ParentNode, String NodeName, String ByAttrName, String ByAttrValue)
	{
		return selectNode(ParentNode, NodeName, ByAttrName, ByAttrValue, false);
	}

	/**
	 * 获取XML文档的一个元素实例。相当于调用<code>SelectNode</code>方法，<code>DoCreate</code>参数为<code>true</code>。
	 * 
	 * @see #selectNode(Element, String, boolean)
	 */

	public Element getNode(Element ParentNode, String NodeName)
	{
		Element currentNode = selectNode(ParentNode, NodeName, true);
		// assert (currentNode != null);
		return currentNode;
	}

	/**
	 * 获取XML文档的一个元素实例。相当于调用<code>SelectNode</code>方法，<code>DoCreate</code>参数为<code>true</code>。
	 * 
	 * @see #selectNode(Element, String, String, String, boolean)
	 */
	public Element getNode(Element ParentNode, String NodeName, String ByAttrName, String ByAttrValue)
	{
		Element currentNode = selectNode(ParentNode, NodeName, ByAttrName, ByAttrValue, true);
		// assert (currentNode != null);
		return currentNode;
	}

	/*
	 * public Element FirstNode(Element ParentNode) { assert (ParentNode !=
	 * null); if (ParentNode.nodeCount() > 0) return (Element)
	 * ParentNode.node(0); else return null; }
	 */

	public Element firstNode(Element ParentNode)
	{
		// assert (ParentNode != null);
		List list = ParentNode.elements();
		if (list.size() > 0) return (Element) list.get(0);
		else return null;
	}

	/**
	 * 读取指定元素或指定元素属性名称的值。
	 * 
	 * @param node
	 *            指定读取元素实例。
	 * @param attrName
	 *            指定属性名称，如果为<code>null</code>，则读取元素值，否则读取指定元素属性名称的值。
	 * @param defaultValue
	 *            指定默认的返回值。
	 * @return 返回读取后的值，当被读取的元素或元素属性不存在时，返回<code>defaultValue</code>值。
	 */
	public String readString(Element node, String attrName, String defaultValue)
	{
		Element currentNode = node;
		if (currentNode != null)
		{
			if (attrName == null)
			{
				String text = currentNode.getText();
				return (text == null) ? defaultValue : text;
			}
			else return currentNode.attributeValue(attrName, defaultValue);
		}
		return null;
	}

	/**
	 * 读取指定元素的值。相当于调用下面的代码：
	 * 
	 * <code>readString(node, null, defaultValue)</code>
	 * 
	 * @see #readString(Element, String, String)
	 */

	public String readString(Element node, String defaultValue)
	{
		return readString(node, null, defaultValue);
	}

	/**
	 * 读取指定元素或指定元素属性名称的值。
	 * 
	 * @param XPath
	 *            指定读取元素的路径。
	 * @param attrName
	 *            指定属性名称，如果为<code>null</code>，则读取元素值，否则读取指定元素属性名称的值。
	 * @param defaultValue
	 *            指定默认的返回值。
	 * @return 返回读取后的值，当被读取的元素或元素属性不存在时，返回<code>defaultValue</code>值。
	 */
	public String readString(String XPath, String attrName, String defaultValue)
	{
		Element currentNode = selectNode(XPath, false);
		if (currentNode != null) return readString(currentNode, attrName, defaultValue);
		else return defaultValue;
	}

	/**
	 * 读取指定元素的值。相当于调用下面的代码：
	 * 
	 * <code>readString(XPath, null, defaultValue)</code>
	 * 
	 * @see #readString(String, String, String)
	 */
	public String readString(String XPath, String defaultValue)
	{
		return readString(XPath, null, defaultValue);
	}

	/**
	 * 写入值到指定元素或指定元素的属性。
	 * 
	 * @param node
	 *            指定需要写入值的元素实例。
	 * @param attrName
	 *            指定属性名称，如果为<code>null</code>，则修改元素的值，否则修改元素属性的值。
	 * @param value
	 *            指定需要写入的新值。
	 */

	public void writeString(Element node, String attrName, String value)
	{
		Element currentNode = node;
		if (currentNode != null)
		{
			if (attrName == null) currentNode.setText(value);
			else
			{
				Attribute attr = currentNode.attribute(attrName);
				if (attr == null) currentNode.addAttribute(attrName, value);
				else attr.setValue(value);
			}
		}
	}

	/**
	 * 写入值到指定元素。相当于调用下面的代码：
	 * 
	 * <code>writeString(node, null, value)</code>
	 * 
	 * @see #writeString(Element, String, String)
	 */
	public void writeString(Element node, String value)
	{
		writeString(node, null, value);
	}

	/**
	 * 写入值到指定元素或指定元素的属性。
	 * 
	 * @param XPath
	 *            指定需要写入值的元素路径。
	 * @param attrName
	 *            指定属性名称，如果为<code>null</code>，则修改元素的值，否则修改元素属性的值。
	 * @param value
	 *            指定需要写入的新值。
	 */
	public void writeString(String XPath, String attrName, String value)
	{
		Element currentNode = selectNode(XPath, true);
		if (currentNode != null) writeString(currentNode, attrName, value);
	}

	/**
	 * 写入值到指定元素。相当于调用下面的代码：
	 * 
	 * <code>writeString(XPath, null, value)</code>
	 * 
	 * @see #writeString(String, String, String)
	 */
	public void writeString(String XPath, String value)
	{
		writeString(XPath, null, value);
	}

}
