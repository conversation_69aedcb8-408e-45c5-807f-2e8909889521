<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.miniapp.rest.mapper.TsIngredientSetInfoMapper">
  <resultMap id="BaseResultMap" type="com.tzx.miniapp.rest.model.TsIngredientSetInfo">
    <!--@Table ts_ingredient_set_info-->
    <result column="ingredient_id" jdbcType="VARCHAR" property="ingredientId" />
    <result column="opt_id" jdbcType="INTEGER" property="optId" />
    <result column="opt_type" jdbcType="VARCHAR" property="optType" />
  </resultMap>
  <sql id="Base_Column_List">
    ingredient_id, opt_id, opt_type
  </sql>
  <insert id="insert" parameterType="com.tzx.miniapp.rest.model.TsIngredientSetInfo">
    insert into ts_ingredient_set_info (ingredient_id, opt_id, opt_type
      )
    values (#{ingredientId,jdbcType=VARCHAR}, #{optId,jdbcType=INTEGER}, #{optType,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.tzx.miniapp.rest.model.TsIngredientSetInfo">
    insert into ts_ingredient_set_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ingredientId != null">
        ingredient_id,
      </if>
      <if test="optId != null">
        opt_id,
      </if>
      <if test="optType != null">
        opt_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ingredientId != null">
        #{ingredientId,jdbcType=VARCHAR},
      </if>
      <if test="optId != null">
        #{optId,jdbcType=INTEGER},
      </if>
      <if test="optType != null">
        #{optType,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>