package com.tzx.ecoserver.rest.vo;

import javax.persistence.Entity;
import java.io.Serializable;

@Entity
public class EcoBtYdd implements Serializable {

	private String yddh;
	private double totalprice;
	private int ydrs;//就餐人数
	private int men;
	private int women;
	private int eldernum;
	private int childnum;
	private String shops_id;
	private String hybh;
	private String ydrq;
	private String ydbc;
	private String ddzt;//订单状态
	private String ddsj;//下单时间
	private String qdsj;
	private String mealtime;// 就餐时间
	private int channel;// 下单来源 “0”为门店下单；“1”为线上下单；默认为“0”。V1.1新增 paychannel
	private String kwxh;//备注
	private String lxr;// 联系人
	private String lxrdh;// 联系人电话
	private String diningway;// 销售模式
	private String zlbh;// 桌位编号
	private String shrbh;// 取餐号
	private double yl1;//订单总金额
	private String paystatus;//付款状态
	private String paychannel;//付款channel
	private double yl2;//优惠金额
	private double yl3;//配送费
	private double shop_real_amount;//商户实收
	private String taxpayerid;//纳税人识别号
	private String recieptdept;//发票信息
	private Integer pick_type;//就餐类型
	private String khjlbh;//平台流水号
	private String khjlmc;//平台门店名称
	private double package_box_fee;//餐盒费
	private String yl5;//来源第几单
	private String khtmtz;//来源名称
	private String member_address;//配送地址
    private double shop_rate;//商户承担优惠金额
    private double platform_rate;//平台承担优惠金额
    private double commission_amount;//平台佣金
    private String jk;//付款方式
    private String bz;//配送时间
    private String yl4;//外卖来源
	private Integer zkfa;//用于表示 预订单转订单状态 初始未0, 1表示订单转订单成功
    private Integer shippingmethod;//配送类型
	private Integer parkingnum;//外卖类型 0 自运营外卖 1平台外卖
	private String pdrbh;//用于存取美团一维码
	private Double ysje;
	private Double zlje;

	public String getPdrbh() {
		return pdrbh;
	}

	public void setPdrbh(String pdrbh) {
		this.pdrbh = pdrbh;
	}

	public Integer getParkingnum() {
		return parkingnum;
	}

	public void setParkingnum(Integer parkingnum) {
		this.parkingnum = parkingnum;
	}

	public String getMealtime() {
		return mealtime;
	}

	public void setMealtime(String mealtime) {
		this.mealtime = mealtime;
	}

	public int getChannel() {
		return channel;
	}

	public void setChannel(int channel) {
		this.channel = channel;
	}

	public String getYddh() {
		return yddh;
	}

	public void setYddh(String yddh) {
		this.yddh = yddh;
	}

	public double getTotalprice() {
		return totalprice;
	}

	public void setTotalprice(double totalprice) {
		this.totalprice = totalprice;
	}

	public int getYdrs() {
		return ydrs;
	}

	public void setYdrs(int ydrs) {
		this.ydrs = ydrs;
	}

	public int getMen() {
		return men;
	}

	public void setMen(int men) {
		this.men = men;
	}

	public int getWomen() {
		return women;
	}

	public void setWomen(int women) {
		this.women = women;
	}

	public int getEldernum() {
		return eldernum;
	}

	public void setEldernum(int eldernum) {
		this.eldernum = eldernum;
	}

	public int getChildnum() {
		return childnum;
	}

	public void setChildnum(int childnum) {
		this.childnum = childnum;
	}

	public String getShops_id() {
		return shops_id;
	}

	public void setShops_id(String shops_id) {
		this.shops_id = shops_id;
	}

	public String getHybh() {
		return hybh;
	}

	public void setHybh(String hybh) {
		this.hybh = hybh;
	}

	public String getYdrq() {
		return ydrq;
	}

	public void setYdrq(String ydrq) {
		this.ydrq = ydrq;
	}

	public String getYdbc() {
		return ydbc;
	}

	public void setYdbc(String ydbc) {
		this.ydbc = ydbc;
	}

	public String getDdzt() {
		return ddzt;
	}

	public void setDdzt(String ddzt) {
		this.ddzt = ddzt;
	}

	public String getDdsj() {
		return ddsj;
	}

	public void setDdsj(String ddsj) {
		this.ddsj = ddsj;
	}

	public String getQdsj() {
		return qdsj;
	}

	public void setQdsj(String qdsj) {
		this.qdsj = qdsj;
	}

	public String getKwxh() {
		return kwxh;
	}

	public void setKwxh(String kwxh) {
		this.kwxh = kwxh;
	}

	public String getLxr() {
		return lxr;
	}

	public void setLxr(String lxr) {
		this.lxr = lxr;
	}

	public String getLxrdh() {
		return lxrdh;
	}

	public void setLxrdh(String lxrdh) {
		this.lxrdh = lxrdh;
	}

	public String getDiningway() {
		return diningway;
	}

	public void setDiningway(String diningway) {
		this.diningway = diningway;
	}

	public String getZlbh() {
		return zlbh;
	}

	public void setZlbh(String zlbh) {
		this.zlbh = zlbh;
	}

	public String getShrbh() {
		return shrbh;
	}

	public void setShrbh(String shrbh) {
		this.shrbh = shrbh;
	}

    public double getYl1() {
        return yl1;
    }

    public void setYl1(double yl1) {
        this.yl1 = yl1;
    }

    public String getPaystatus() {
        return paystatus;
    }

    public void setPaystatus(String paystatus) {
        this.paystatus = paystatus;
    }

    public String getPaychannel() {
        return paychannel;
    }

    public void setPaychannel(String paychannel) {
        this.paychannel = paychannel;
    }

    public double getYl2() {
        return yl2;
    }

    public void setYl2(double yl2) {
        this.yl2 = yl2;
    }

    public double getYl3() {
        return yl3;
    }

    public void setYl3(double yl3) {
        this.yl3 = yl3;
    }

    public double getShop_real_amount() {
        return shop_real_amount;
    }

    public void setShop_real_amount(double shop_real_amount) {
        this.shop_real_amount = shop_real_amount;
    }

    public String getTaxpayerid() {
        return taxpayerid;
    }

    public void setTaxpayerid(String taxpayerid) {
        this.taxpayerid = taxpayerid;
    }

    public String getRecieptdept() {
        return recieptdept;
    }

    public void setRecieptdept(String recieptdept) {
        this.recieptdept = recieptdept;
    }

    public Integer getPick_type() {
        return pick_type;
    }

    public void setPick_type(Integer pick_type) {
        this.pick_type = pick_type;
    }

    public String getKhjlbh() {
        return khjlbh;
    }

    public void setKhjlbh(String khjlbh) {
        this.khjlbh = khjlbh;
    }

    public double getPackage_box_fee() {
        return package_box_fee;
    }

    public void setPackage_box_fee(double package_box_fee) {
        this.package_box_fee = package_box_fee;
    }

    public String getYl5() {
        return yl5;
    }

    public void setYl5(String yl5) {
        this.yl5 = yl5;
    }

    public String getKhtmtz() {
        return khtmtz;
    }

    public void setKhtmtz(String khtmtz) {
        this.khtmtz = khtmtz;
    }

    public String getMember_address() {
        return member_address;
    }

    public void setMember_address(String member_address) {
        this.member_address = member_address;
    }

    public double getShop_rate() {
        return shop_rate;
    }

    public void setShop_rate(double shop_rate) {
        this.shop_rate = shop_rate;
    }

    public double getPlatform_rate() {
        return platform_rate;
    }

    public void setPlatform_rate(double platform_rate) {
        this.platform_rate = platform_rate;
    }

    public double getCommission_amount() {
        return commission_amount;
    }

    public void setCommission_amount(double commission_amount) {
        this.commission_amount = commission_amount;
    }

    public String getJk() {
        return jk;
    }

    public void setJk(String jk) {
        this.jk = jk;
    }

    public String getBz() {
        return bz;
    }

    public void setBz(String bz) {
        this.bz = bz;
    }

    public String getYl4() {
        return yl4;
    }

    public void setYl4(String yl4) {
        this.yl4 = yl4;
    }

	public Integer getZkfa() {
		return zkfa;
	}

	public void setZkfa(Integer zkfa) {
		this.zkfa = zkfa;
	}

	public Integer getShippingmethod() {
		return shippingmethod;
	}

	public void setShippingmethod(Integer shippingmethod) {
		this.shippingmethod = shippingmethod;
	}

	public String getKhjlmc() {
		return khjlmc;
	}

	public void setKhjlmc(String khjlmc) {
		this.khjlmc = khjlmc;
	}

	public Double getYsje() {
		return ysje;
	}

	public void setYsje(Double ysje) {
		this.ysje = ysje;
	}

	public Double getZlje() {
		return zlje;
	}

	public void setZlje(Double zlje) {
		this.zlje = zlje;
	}
}
