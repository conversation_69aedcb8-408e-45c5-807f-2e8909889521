package com.tzx.dyndatasource;

import com.alibaba.druid.sql.visitor.functions.Nil;
import com.tzx.publics.listener.InitDataListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2020-03-16
 * @Descption
 **/
@Controller
@ResponseBody
@RequestMapping("/db")
public class DSConverController {
    Logger logger = LoggerFactory.getLogger(DSConverController.class);
    @Autowired
    InitDataListener initDataListener;
    @Autowired
    DynamicDataSource dynamicDataSource;
    @Autowired
    @Qualifier("prodDataSource")
    DataSource prodDataSource;
    @Autowired
    @Qualifier("uatDataSource")
    DataSource uatDataSource;
    @RequestMapping(value = "/switch",method = RequestMethod.POST)
    public Map<String,String> switchDs(@RequestBody Map<String,String> map){
        Map<String ,String> res = new HashMap<>();
        String type = map.get("type");
        try{
            if(type.equals(DSType.UAT.getValue())){
                DynamicDataSourceConfig.dsType = DSType.UAT;
                logger.info("当前数据库切换为测试库");
                //更新默认数据源为测试数据源,业务数据不走切面中代码控制数据源，则走默认的
                dynamicDataSource.setDefaultTargetDataSource(uatDataSource);
                dynamicDataSource.afterPropertiesSet();
            }else{
                DynamicDataSourceConfig.dsType = DSType.PROD;
                logger.info("当前数据库切换为正式库");
                //更新默认数据源为测试数据源,业务数据不走切面中代码控制数据源，则走默认的
                dynamicDataSource.setDefaultTargetDataSource(prodDataSource);
                dynamicDataSource.afterPropertiesSet();
            }
//            initDataListener.loadGlovarParamsAndOrgan();//重新加载全局数据   这里不用了，统一性小程序更新通知里加了
            res.put("code","0");
            res.put("msg","切换成功");
        }catch (Exception e){
            res.put("code","1");
            res.put("msg","切换失败");
        }
        return res;
    }
}
