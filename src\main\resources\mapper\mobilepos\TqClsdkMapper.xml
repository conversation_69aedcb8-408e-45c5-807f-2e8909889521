<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.mobilepos.rest.mapper.MobilePosTqClsdkMapper">
	<select id="findItemClassBasicData" resultType="com.tzx.mobilepos.rest.vo.ItemClass" >
		select cl.id, cl.clbh as itemclass_code, cl.clmc1 as itemclass_name, cl.showxh as number, 
		cl.kssj as start_time,cl.jssj as end_time, cl.ksrq as start_date, cl.jsrq as end_date 
		from tq_clsdk cl left join tq_cbsdk cb on cb.id = cl.cbid 
		where cb.yl1 = 'TS' and cl.sfxs = 'Y' and cl.clsx &lt;&gt; 'CPYSSX_YH' 
	</select>
</mapper>
