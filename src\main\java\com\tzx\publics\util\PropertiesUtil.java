package com.tzx.publics.util;

import java.io.*;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

/**
 * 
 * <p>
 * Title: 属性文件操作工具
 * </p>
 * <p>
 * Description:
 * </p>
 * <p>
 * Company: tzx
 * </p>
 * 
 * <AUTHOR>
 * @date Aug 4, 2013
 */
public class PropertiesUtil {

	/**
	 * 
	 * <p>
	 * Title: readValue
	 * </p>
	 * <p>
	 * Description:根据key读取value
	 * </p>
	 * 
	 * <AUTHOR>
	 * @param filePath
	 * @param key
	 * @return
	 */
	public static String readValue(String filePath, String key) {
		Properties props = new Properties();
		InputStream in = null;
		try {
			in = new BufferedInputStream(new FileInputStream(filePath));
			props.load(in);
			String value = props.getProperty(key);
			return value;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		} finally {
			if (in != null) {
				try {
					in.close();
				} catch (IOException e) {
				}
			}
		}
	}

	/**
	 * 
	 * <p>
	 * Title: readValue
	 * </p>
	 * <p>
	 * Description:根据key读取value
	 * </p>
	 * 
	 * <AUTHOR>
	 * @param filePath
	 * @param key
	 * @return
	 */
	public static String readValueForClasses(String filePath, String key) {
		Properties props = new Properties();
		InputStream in = null;
		try {
			in = PropertiesUtil.class.getResourceAsStream(filePath);
			props.load(in);
			String value = props.getProperty(key);
			return value;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		} finally {
			if (in != null) {
				try {
					in.close();
				} catch (IOException e) {
				}
			}
		}
	}

	/**
	 * 
	 * <p>
	 * Title: readProperties
	 * </p>
	 * <p>
	 * Description: 读取properties的全部信息
	 * </p>
	 * 
	 * <AUTHOR>
	 * @param filePath
	 */
	public static Properties readProperties(String filePath) {
		Properties props = new Properties();
		InputStream in = null;
		try {
			in = new BufferedInputStream(new FileInputStream(filePath));
			props.load(in);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (in != null) {
				try {
					in.close();
				} catch (IOException e) {
				}
			}
		}

		return props;
	}

	public static Properties readResourceProperties(String filePath) {
		Properties props = new Properties();
		InputStream in = null;
		try {
			in = PropertiesUtil.class.getResourceAsStream(filePath);
			props.load(in);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (in != null) {
				try {
					in.close();
				} catch (IOException e) {
				}
			}
		}

		return props;
	}

	/**
	 * 
	 * <p>
	 * Title: writeProperties
	 * </p>
	 * <p>
	 * Description:写入properties信息
	 * </p>
	 * 
	 * <AUTHOR>
	 * @param filePath
	 * @param parameterName
	 * @param parameterValue
	 */
	public static void writeProperties(String filePath, String parameterName, String parameterValue) {
		Properties prop = new Properties();
		try {
			InputStream fis = new FileInputStream(filePath);
			// 从输入流中读取属性列表（键和元素对）
			prop.load(fis);
			// 调用 Hashtable 的方法 put。使用 getProperty 方法提供并行性。
			// 强制要求为属性的键和值使用字符串。返回值是 Hashtable 调用 put 的结果。
			OutputStream fos = new FileOutputStream(filePath);
			prop.setProperty(parameterName, parameterValue);
			// 以适合使用 load 方法加载到 Properties 表中的格式，
			// 将此 Properties 表中的属性列表（键和元素对）写入输出流
			prop.store(fos, "Update '" + parameterName + "' value");
		} catch (IOException e) {
			System.err.println("Visit " + filePath + " for updating " + parameterName + " value error");
		}
	}

	public static Map<String, String> setPropertiesFileToMap(String fileName) throws FileNotFoundException {
		Properties prop = new Properties();
		Map<String, String> propMap = new HashMap<String, String>();
		InputStream in = new FileInputStream(fileName);
		try {
			prop.load(in);
			Set<Object> keyset = prop.keySet();
			for (Object object : keyset) {
				// String propValue=
				// prop.getProperty(object.toString()).toString();
				propMap.put(object.toString(), prop.getProperty(object.toString()).toString());
				// System.out.println(object.toString()+" : "+propValue);
			}

			return propMap;
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			try {
				if (in != null)
					in.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return null;
	}

	/**
	 * 
	 * @param filePath
	 * @param key
	 * @param charset
	 * @return
	 */
	public static String readValue(String filePath, String key, String charset) {
		Properties props = new Properties();
		InputStream in = null;
		InputStreamReader read = null;
		BufferedReader reader = null;
		try {
			in = new FileInputStream(filePath);
			read = new InputStreamReader(in, charset);
			reader = new BufferedReader(read);
			props.load(reader);
			String value = props.getProperty(key);
			return value;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		} finally {
			try {
				if (reader != null) {
					reader.close();
				}
				if (read != null) {
					read.close();
				}
				if (in != null) {
					in.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}

	public static void main(String[] args) throws Exception {
		/*
		 * readValue("info.properties", "url"); writeProperties("info.properties",
		 * "age", "21"); readProperties("info.properties"); System.out.println("OK");
		 */
		String prePath = "E:\\TestEnv\\xiangcj-POS\\nebula_boh";// System.getenv("NEBULA_BOH_HOME")
		String p = readValue(prePath + "/wmprint/printsetting.properties", "baiduprintname", "GBK");
		System.out.println(p + " , " + new String(p.getBytes(), "UTF-8"));
	}

}
