<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.ecoserver.rest.mapper.EcoOrderMapper">

  <delete id="clearYdd">
    delete from bt_ydd where yddh=#{yddh}
  </delete>
	<delete id="clearYdxm1">
		delete from bt_ydxm1 where yddh=#{yddh}
	</delete>
	<delete id="clearYdxm2">
		delete from bt_ydxm2 where yddh=#{yddh}
	</delete>
  <delete id="clearYddTcSelectMx">
    delete from bt_tcselectmx where yddh=#{yddh}
  </delete>
	<delete id="clearYddActive">
		delete from bt_yddactive where yddh=#{yddh}
	</delete>
	<delete id="clearEcoOrderdiscount">
		delete from bt_order_discount where order_code=#{yddh}
	</delete>
	<insert id="insertBtYdd" parameterType="com.tzx.miniapp.rest.vo.BtYdd">
		insert into bt_ydd
		(yddh,totalprice,ydrs,men,women,eldernum,childnum,shops_id,hybh,ydrq,qdsj,ydbc,ddzt,paychannel,mealtime,kwxh,lxr,lxrdh,diningway,zlbh,
		yl1,paystatus,yl2,yl3,shop_real_amount,taxpayerid,recieptdept,pick_type,khjlbh,package_box_fee,yl5,khtmtz,member_address,
		shop_rate,platform_rate,commission_amount,jk,bz,yl4,ddsj,zkfa,Shippingmethod,parkingnum,pdrbh,khjlmc,shrbh,ysje,zlje)
		values(#{by.yddh,jdbcType=VARCHAR},#{by.totalprice,jdbcType=NUMERIC},#{by.ydrs,jdbcType=INTEGER},
		#{by.men,jdbcType=INTEGER},#{by.women,jdbcType=INTEGER},#{by.eldernum,jdbcType=INTEGER},
		#{by.childnum,jdbcType=INTEGER},#{by.shops_id,jdbcType=VARCHAR},#{by.hybh,jdbcType=VARCHAR},
		#{by.ydrq,jdbcType=VARCHAR}, #{by.qdsj,jdbcType=VARCHAR},
		#{by.ydbc,jdbcType=VARCHAR}, #{by.ddzt,jdbcType=VARCHAR},
		#{by.channel,jdbcType=INTEGER},#{by.mealtime,jdbcType=VARCHAR},#{by.kwxh,jdbcType=VARCHAR},
		#{by.lxr,jdbcType=VARCHAR},#{by.lxrdh,jdbcType=VARCHAR},#{by.diningway,jdbcType=VARCHAR},#{by.zlbh,jdbcType=VARCHAR},
		#{by.yl1,jdbcType=NUMERIC},#{by.paystatus,jdbcType=VARCHAR},#{by.yl2,jdbcType=NUMERIC},
		#{by.yl3,jdbcType=NUMERIC},#{by.shop_real_amount,jdbcType=NUMERIC},#{by.taxpayerid,jdbcType=VARCHAR},#{by.recieptdept,jdbcType=VARCHAR},
		#{by.pick_type,jdbcType=INTEGER},#{by.khjlbh,jdbcType=VARCHAR},#{by.package_box_fee,jdbcType=NUMERIC},#{by.yl5,jdbcType=VARCHAR},
		#{by.khtmtz,jdbcType=VARCHAR},#{by.member_address,jdbcType=VARCHAR},#{by.shop_rate,jdbcType=NUMERIC},
		#{by.platform_rate,jdbcType=NUMERIC},#{by.commission_amount,jdbcType=NUMERIC},#{by.jk,jdbcType=VARCHAR},
		#{by.bz,jdbcType=VARCHAR},#{by.yl4,jdbcType=VARCHAR},#{by.ddsj,jdbcType=VARCHAR},#{by.zkfa,jdbcType=INTEGER},
		#{by.shippingmethod,jdbcType=INTEGER},#{by.parkingnum,jdbcType=INTEGER},#{by.pdrbh,jdbcType=VARCHAR},#{by.khjlmc,jdbcType=VARCHAR},#{by.shrbh,jdbcType=VARCHAR},
		#{by.ysje},#{by.zlje})
	</insert>

	<insert id="insertBtYdxm2" parameterType="java.util.List">
		insert into bt_ydxm2
		(yddh, xmid, xmbh, xmsx,
		xmdj,xmsl,zkl,totalprice,dwbh,kwbh,cmje,tcbh,tcdch,fzsl,fzje,dcxh,xmmc,yl3,isactivity,tag,packid
		,top_item_id,bsetmealid)
		values
		<foreach collection="list" item="ydxm" index="index"
				 separator=",">
			(
			#{ydxm.yddh}, #{ydxm.xmid}, #{ydxm.xmbh}, #{ydxm.xmsx},
			#{ydxm.xmdj},
			#{ydxm.xmsl}, #{ydxm.zkl}, #{ydxm.totalprice},
			#{ydxm.dwbh},
			#{ydxm.kwbh}, #{ydxm.cmje}, #{ydxm.tcbh}, #{ydxm.tcdch},
			#{ydxm.fzsl}, #{ydxm.fzje}, #{ydxm.dcxh}, #{ydxm.xmmc}, #{ydxm.cmje}, #{ydxm.isactivity}
			,'',#{ydxm.packid},#{ydxm.top_item_id},#{ydxm.bsetmealid}
			)
		</foreach>
	</insert>

	<insert id="insertEcoOrderdiscount" parameterType="java.util.List">
		insert into bt_order_discount
		(order_code, discount_type, activity_id, discount_desc,
		discount_fee,platform_rate,shop_rate, pay_no)
		values
		<foreach collection="list" item="item" index="index"
				 separator=",">
			(
			#{item.order_code}, #{item.discount_type}, #{item.activity_id}, #{item.discount_desc},
			#{item.discount_fee},#{item.platform_rate}, #{item.shop_rate}, #{item.pay_no}
			)
		</foreach>
	</insert>
	<select id="getbillid" resultType="java.lang.String"  parameterType="map" statementType="CALLABLE"  useCache="false">
		select * from P_GetBillID(#{skjh},'0',#{tname},#{fname})
	</select>


	<select id="ecoOrderToBill" resultType="java.lang.Integer">
		select * from p_ecoordertobill(#{syydh}, #{akdzdbh}, #{ajzzdbh},#{alsdh},#{abbbc},#{abbrq},#{aczry},#{askjh},#{aygdlcs})
	</select>

	<select id="getYddCountByYddh" resultType="java.lang.Integer">
		select count(yddh) from bt_ydd where yddh=#{yddh}
	</select>

	<select id="findDishInfos" resultType="java.util.Map" parameterType="java.util.List">
		select cmbh,cmsx,cmid from ts_cmk where cmbh in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>
	<select id="findDishTcInfos" resultType="java.util.Map" parameterType="java.util.List">
		SELECT b.dydlid, a.cmbh AS tcbh, a.mxlx, b.cmbh, b.cmmc1,a.cmsl FROM TS_TCMXK a, TS_CMK b WHERE
		a.MXXMID = b.CMID and a.CMBH IN
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>
	<select id="getJtZtk" resultType="com.tzx.miniapp.rest.model.TqJtztk">
		select * from tq_jtztk where bbrq = #{bbrq} and jhid = '99' and cznr = 'YYDL' order by czsj desc limit 1
	</select>

	<select id="getBtYdd" resultType="java.util.Map" >
		SELECT ddzt,yddh,refund_type,COALESCE(parkingnum,1) isplatform  from bt_ydd where yddh=#{yddh}
	</select>

	<select id="cancelecoBill" resultType="java.lang.Integer">
		select * from p_cancelecobill(#{syydh}, #{akdzdbh}, #{ajzzdbh},#{alsdh},#{abbbc},#{abbrq},#{aczry},#{askjh},#{aygdlcs},#{acancelreason})
	</select>
	<select id="cancelecoBillhis" resultType="java.lang.Integer">
		select * from p_cancelecobillhis(#{syydh}, #{akdzdbh}, #{ajzzdbh},#{alsdh},#{abbbc},#{abbrq},#{aczry},#{askjh},#{aygdlcs},#{acancelreason})
	</select>
	<select id="getGGCS" resultType="java.util.Map">
		select * from ts_ggcsk where sdbt = #{sdbt}
	</select>
	<select id="getZdByYddh" resultType="java.util.Map" >
		SELECT yddh,yhfkje,kdzdbh,wmtype,sfkfp,kfpsqm as dh,kdbbrq,kfpje,source from tq_zdk where yddh=#{yddh}
	</select>
	<select id="getZdByYddhhis" resultType="java.util.Map" >
		SELECT yddh,yhfkje,kdzdbh,wmtype,sfkfp,kfpsqm as dh,kdbbrq,kfpje,source from tq_zdlsk where yddh=#{yddh}
	</select>
	<select id="singlecancelecobill" resultType="java.lang.Integer">
		select * from p_singlecancelecobill(#{syydh}, #{akdzdbh}, #{ajzzdbh},#{alsdh},#{abbbc},#{abbrq},#{aczry},#{askjh},#{aygdlcs},#{acancelreason})
	</select>
	<select id="singlecancelecobillhis" resultType="java.lang.Integer">
		select * from p_singlecancelecobillhis(#{syydh}, #{akdzdbh}, #{ajzzdbh},#{alsdh},#{abbbc},#{abbrq},#{aczry},#{askjh},#{aygdlcs},#{acancelreason})
	</select>

	<insert id="insertBtYdxm1" parameterType="java.util.List">
		insert into bt_ydxm1
		(yddh, xmbh, xmmc,yl1,yl2,yl3)
		values
		<foreach collection="list" item="ydxm" index="index"
				 separator=",">
			(
			#{ydxm.yddh}, #{ydxm.xmbh}, #{ydxm.xmmc},
			#{ydxm.yl1},#{ydxm.yl2},#{ydxm.yl3}
			)
		</foreach>
	</insert>
	<select id="getPreZDKByYddh" resultType="java.util.Map" >
		SELECT yddh,kdzdbh,wmtype,kdbbrq,ygdlcs,fwyh,source from tq_zdk where yddh=#{yddh}
	</select>
	<select id="getPreZDLSKByYddh" resultType="java.util.Map" >
		SELECT yddh,kdzdbh,wmtype,kdbbrq,ygdlcs,fwyh,source from tq_zdlsk where yddh=#{yddh}
	</select>

	<select id="getYdxm2ByYddh" resultType="java.util.Map" >
		SELECT yddh,xmbh,xmmc,xmsl from  bt_ydxm2 where yddh=#{yddh}
	</select>

	<select id="findDishTcInfosObj" resultType="java.util.Map" parameterType="java.util.List">
		SELECT b.dydlid, a.cmbh AS tcbh, a.mxlx, b.cmbh, b.cmmc1,a.cmsl FROM TS_TCMXK a, TS_CMK b WHERE
		a.MXXMID = b.CMID and a.CMBH IN
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

  <select id="getErrorYddCountByYddh" resultType="java.lang.Integer">
    select count(yddh) from error_ydd where yddh = #{yddh}
  </select>

  <insert id="insertErrorYdd" parameterType="com.tzx.ecoserver.rest.vo.EcoErrorYdd">
    insert into error_ydd
    (yddh,totalprice,ydrs,men,women,eldernum,childnum,shops_id,hybh,ydrq,qdsj,ydbc,ddzt,paychannel,mealtime,kwxh,lxr,lxrdh,diningway,zlbh,
    yl1,paystatus,yl2,yl3,shop_real_amount,taxpayerid,recieptdept,pick_type,khjlbh,package_box_fee,yl5,khtmtz,member_address,
    shop_rate,platform_rate,commission_amount,jk,bz,yl4,ddsj,zkfa,Shippingmethod,parkingnum)
    values(#{by.yddh,jdbcType=VARCHAR},#{by.totalprice,jdbcType=NUMERIC},#{by.ydrs,jdbcType=INTEGER},
    #{by.men,jdbcType=INTEGER},#{by.women,jdbcType=INTEGER},#{by.eldernum,jdbcType=INTEGER},
    #{by.childnum,jdbcType=INTEGER},#{by.shops_id,jdbcType=VARCHAR},#{by.hybh,jdbcType=VARCHAR},
    #{by.ydrq,jdbcType=VARCHAR}, #{by.qdsj,jdbcType=VARCHAR},
    #{by.ydbc,jdbcType=VARCHAR}, #{by.ddzt,jdbcType=VARCHAR},
    #{by.channel,jdbcType=INTEGER},#{by.mealtime,jdbcType=VARCHAR},#{by.kwxh,jdbcType=VARCHAR},
    #{by.lxr,jdbcType=VARCHAR},#{by.lxrdh,jdbcType=VARCHAR},#{by.diningway,jdbcType=VARCHAR},#{by.zlbh,jdbcType=VARCHAR},
    #{by.yl1,jdbcType=NUMERIC},#{by.paystatus,jdbcType=VARCHAR},#{by.yl2,jdbcType=NUMERIC},
    #{by.yl3,jdbcType=NUMERIC},#{by.shop_real_amount,jdbcType=NUMERIC},#{by.taxpayerid,jdbcType=VARCHAR},#{by.recieptdept,jdbcType=VARCHAR},
    #{by.pick_type,jdbcType=INTEGER},#{by.khjlbh,jdbcType=VARCHAR},#{by.package_box_fee,jdbcType=NUMERIC},#{by.yl5,jdbcType=VARCHAR},
    #{by.khtmtz,jdbcType=VARCHAR},#{by.member_address,jdbcType=VARCHAR},#{by.shop_rate,jdbcType=NUMERIC},
    #{by.platform_rate,jdbcType=NUMERIC},#{by.commission_amount,jdbcType=NUMERIC},#{by.jk,jdbcType=VARCHAR},
    #{by.bz,jdbcType=VARCHAR},#{by.yl4,jdbcType=VARCHAR},#{by.ddsj,jdbcType=VARCHAR},#{by.zkfa,jdbcType=INTEGER},
    #{by.shippingmethod,jdbcType=INTEGER},#{by.parkingnum,jdbcType=INTEGER})
  </insert>

  <insert id="insertErrorYdxm2" parameterType="java.util.List">
    insert into error_ydxm2
    (yddh, xmid, xmbh, xmsx,
    xmdj,xmsl,zkl,totalprice,dwbh,kwbh,cmje,tcbh,tcdch,fzsl,fzje,dcxh,xmmc,yl3,isactivity,tag,packid,top_item_id)
    values
    <foreach collection="list" item="ydxm" index="index"
         separator=",">
      (
      #{ydxm.yddh}, #{ydxm.xmid}, #{ydxm.xmbh}, #{ydxm.xmsx},
      #{ydxm.xmdj},
      #{ydxm.xmsl}, #{ydxm.zkl}, #{ydxm.totalprice},
      #{ydxm.dwbh},
      #{ydxm.kwbh}, #{ydxm.cmje}, #{ydxm.tcbh}, #{ydxm.tcdch},
      #{ydxm.fzsl}, #{ydxm.fzje}, #{ydxm.dcxh}, #{ydxm.xmmc}, #{ydxm.cmje}, #{ydxm.isactivity}
      ,'',#{ydxm.packid},#{ydxm.top_item_id}
      )
    </foreach>
  </insert>

  <insert id="insertErrorOrderdiscount" parameterType="java.util.List">
    insert into error_order_discount
    (order_code, discount_type, activity_id, discount_desc,
    discount_fee,platform_rate,shop_rate)
    values
    <foreach collection="list" item="item" index="index"
         separator=",">
      (
      #{item.order_code}, #{item.discount_type}, #{item.activity_id}, #{item.discount_desc},
      #{item.discount_fee},#{item.platform_rate}, #{item.shop_rate}
      )
    </foreach>
  </insert>

  <select id="sendErrorPrint" resultType="java.lang.Integer">
    select * from p_ErrorEcoBillSendKitchen(#{yddh})
  </select>

  <select id="getErrorYdd" resultType="java.util.Map" >
    SELECT ddzt,yddh,refund_type from error_ydd where yddh=#{yddh}
  </select>
  
  <select id="cancelEcoErrorBill" resultType="java.lang.Integer">
    select * from p_cancelEcoErrorBill(#{syydh})
  </select>
  
  <insert id="insertErrorYdxm1" parameterType="java.util.List">
    insert into error_ydxm1
    (yddh, xmbh, xmmc,yl1,yl2,yl3)
    values
    <foreach collection="list" item="ydxm" index="index"
         separator=",">
      (
      #{ydxm.yddh}, #{ydxm.xmbh}, #{ydxm.xmmc},
      #{ydxm.yl1},#{ydxm.yl2},#{ydxm.yl3}
      )
    </foreach>
  </insert>
 
  <select id="singleCancelEcoErrorBill" resultType="java.lang.Integer">
    select * from p_singleCancelEcoErrorBill(#{syydh})
  </select>

	<update id="updateYddPackageBoxFee">
		update bt_ydd
		set Package_box_fee = Package_box_fee - #{PackageBoxFee}
		where yddh = #{yddh}
	</update>


	<insert id="insertBtPayments" parameterType="java.util.List">
		insert into bt_payments
		(yddh, pay_name, pay_no, pay_count,
		vocount,pay_bb,pay_channel,fzzhje)
		values
		<foreach collection="list" item="btpayment" index="index"
				 separator=",">
			(
			#{btpayment.yddh}, #{btpayment.pay_name}, #{btpayment.pay_no}, #{btpayment.pay_count},
			#{btpayment.vocount},#{btpayment.pay_count} - #{btpayment.vocount},#{btpayment.pay_channel}
			,#{btpayment.fzzhje}
			)
		</foreach>
	</insert>
	<insert id="insertBtPaymentsPart" parameterType="java.util.List">
		insert into bt_payments_part
		(yddh, pay_name, pay_no, pay_count,
		vocount,pay_bb,pay_channel,fzzhje)
		values
		<foreach collection="list" item="btpayment" index="index"
				 separator=",">
			(
			#{btpayment.yddh}, #{btpayment.pay_name}, #{btpayment.pay_no}, #{btpayment.pay_count},
			#{btpayment.vocount},#{btpayment.pay_count} - #{btpayment.vocount},#{btpayment.pay_channel}
			,#{btpayment.fzzhje}
			)
		</foreach>
	</insert>
	<insert id="insertErrBtPayments" parameterType="java.util.List">
		insert into error_payments
		(yddh, pay_name, pay_no, pay_count,
		vocount,pay_bb,pay_channel)
		values
		<foreach collection="list" item="btpayment" index="index"
				 separator=",">
			(
			#{btpayment.yddh}, #{btpayment.pay_name}, #{btpayment.pay_no}, #{btpayment.pay_count},
			#{btpayment.vocount},#{btpayment.pay_count} - #{btpayment.vocount},#{btpayment.pay_channel}
			)
		</foreach>
	</insert>

	<select id="callP_SendKVSData" resultType="java.lang.String">
		select * from sendkvsdata(#{szdbh})
	</select>
	<delete id="deleteKvsMXK">
		delete from TQ_KVSMXK where kdzdbh=#{szdbh}
	</delete>
	<update id="updateKvsMxkTime">
		UPDATE tq_kvsmxk_change_time SET Last_Update_Time = CURRENT_TIMESTAMP;
	</update>
	<update id="updateKvsChangeTime">
		UPDATE tq_kvs_change_time SET Last_Update_Time = CURRENT_TIMESTAMP;
	</update>
	<update id="updateKdsChangeTime">
		UPDATE tq_kds_change_time SET Last_Update_Time = CURRENT_TIMESTAMP;
	</update>

	<select id="ecoOrderToBillNoItem" resultType="java.lang.Integer">
		select * from p_ecoordertobillnoitem(#{syydh}, #{akdzdbh}, #{ajzzdbh},#{alsdh},#{abbbc},#{abbrq},#{aczry},#{askjh},#{aygdlcs})
	</select>

	<select id="singlecancelecobillNoItem" resultType="java.lang.Integer">
		select * from p_singlecancelecobillnoitem(#{syydh}, #{akdzdbh}, #{ajzzdbh},#{alsdh},#{abbbc},#{abbrq},#{aczry},#{askjh},#{aygdlcs},#{acancelreason})
	</select>

	<select id="singlecancelecobillNoItemHis" resultType="java.lang.Integer">
		select * from p_singlecancelecobillnoitemhis(#{syydh}, #{akdzdbh}, #{ajzzdbh},#{alsdh},#{abbbc},#{abbrq},#{aczry},#{askjh},#{aygdlcs},#{acancelreason})
	</select>

	<select id="getkdzdbhByYddh" resultType="java.lang.String">
		select kdzdbh from vq_zdk where yddh=#{yddh}
	</select>

	<insert id="insertTqYyddylsk" parameterType="com.tzx.miniapp.rest.vo.TqYyddylsk">
		insert into tq_yyddylsk (fdjgxh,yddh,bbrq,dyzt,dyjmc,dylx,need_invoice,ddlx)
		values(
		#{yyddyls.fdjgxh,jdbcType=INTEGER},#{yyddyls.yddh,jdbcType=VARCHAR},#{yyddyls.bbrq,jdbcType=DATE},
		#{yyddyls.dyzt,jdbcType=INTEGER},#{yyddyls.dyjmc,jdbcType=VARCHAR},#{yyddyls.dylx,jdbcType=VARCHAR},
		#{yyddyls.need_invoice,jdbcType=VARCHAR},#{yyddyls.ddlx,jdbcType=VARCHAR})
	</insert>

	<insert id="insertBtTcSelectMx" parameterType="java.util.List">
		INSERT INTO bt_tcselectmx (
			yddh, fzsl, fzje, cmmc, bsetmealid, dcxh
		) VALUES 
		<foreach collection="list" item="item" separator=",">
			(
				#{item.yddh},
				#{item.fzsl},
				#{item.fzje},
				#{item.cmmc},
				#{item.bsetmealid},
				#{item.dcxh}
			)
		</foreach>
	</insert>

</mapper>
