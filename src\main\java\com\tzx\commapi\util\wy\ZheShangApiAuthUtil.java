package com.tzx.commapi.util.wy;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

/**
 * 浙商物业API接口认证工具类
 */
public class ZheShangApiAuthUtil {

    private static final String APP_KEY_PARAM = "appKey";
    private static final String TIMESTAMP_PARAM = "timestamp";
    private static final String SIGN_PARAM = "sign";

    /**
     * 构建包含认证参数的完整请求URL。
     *
     * @param baseUrl   基础接口URL (不含查询参数)
     * @param appKey    应用标识
     * @param appSecret 应用密钥
     * @return 包含 appKey, timestamp, sign 的完整URL
     */
    public static String buildAuthenticatedUrl(String baseUrl, String appKey, String appSecret) {
        // 确保baseUrl不以 '?' 或 '&' 结尾，以防万一
        String cleanBaseUrl = baseUrl.endsWith("?") || baseUrl.endsWith("&") ? baseUrl.substring(0, baseUrl.length() - 1) : baseUrl;

        long timestamp = System.currentTimeMillis() / 1000; // 使用Unix时间戳 (秒)
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put(APP_KEY_PARAM, appKey);
        queryParams.put(TIMESTAMP_PARAM, String.valueOf(timestamp));

        // 创建签名时只使用 appKey 和 timestamp
        String sign = createSign(queryParams, appSecret);

        // 构建最终URL
        StringBuilder urlBuilder = new StringBuilder(cleanBaseUrl);
        // 判断是添加 '?' 还是 '&'
        urlBuilder.append(cleanBaseUrl.contains("?") ? "&" : "?");
        urlBuilder.append(APP_KEY_PARAM).append("=").append(appKey); // TODO: URL编码 appKey (如果需要)
        urlBuilder.append("&").append(TIMESTAMP_PARAM).append("=").append(timestamp);
        urlBuilder.append("&").append(SIGN_PARAM).append("=").append(sign);

        return urlBuilder.toString();
    }

    /**
     * 根据浙商API规则创建签名。
     *
     * @param paramMap  包含需要签名的查询参数的Map (不应包含sign本身)
     * @param appSecret 应用密钥
     * @return 计算得到的签名字符串 (MD5 Hex 小写)
     */
    private static String createSign(Map<String, String> paramMap, String appSecret) {
        // 使用TreeMap确保参数按字典序排序
        TreeMap<String, String> treeMap = new TreeMap<>(paramMap);

        List<String> fields = treeMap.entrySet().stream()
                // 确保过滤掉 sign 参数（虽然在这个流程里调用时还不包含sign，但作为通用方法最好加上）
                .filter(entry -> !entry.getKey().equals(SIGN_PARAM))
                // 拼接 key=value 格式
                .map(entry -> entry.getKey() + "=" + entry.getValue()) // TODO: URL编码 value (如果需要)
                .collect(Collectors.toList());

        // 使用 '&' 连接参数字符串
        String sortedParams = StringUtils.join(fields, '&');

        // 将 appSecret 拼接在两端
        String stringToSign = appSecret + sortedParams + appSecret;

        // 计算 MD5 Hex
        return DigestUtils.md5Hex(stringToSign);
    }

    // 私有构造函数，防止实例化工具类
    private ZheShangApiAuthUtil() {
    }
}