<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.mobilepos.rest.mapper.MobilePosTsFkfssdkMapper">
	<select id="findTsFkfssdkBasicData" resultType="com.tzx.mobilepos.rest.vo.PaymentWay">
		select b.id, b.fkfsbh as payno, b.fkfsmc1 as payment_name1, 
		b.fklxsx as payment_class, coalesce(a.showxh, 999) as showxh, b.yl3 payment_mark
		from tq_paymentbutton a inner join TS_FKFSSDK b on a.fkfsid = b.id
		where a.sfxs = 'Y' and b.fklxsx = 'FKSX_DSF' and (b.yl3 = '' or b.yl3 = 'ERP_FKFS_MT') or b.fklxsx = 'FKSX_GZ' 
		or b.fklxsx = 'FKSX_XJ' 
		<if test='posMemberType == "1"'>
			or b.fkfsbh = '2010' or b.fkfsbh = '2001' or b.fkfsbh = '2002' 
		</if>
		<if test='posMemberType == "4"'>
			or b.fkfsbh = '1971' or b.fkfsbh = '1972' or b.fkfsbh = '1973' 
		</if>
		or b.yl3 = 'ERP_FKFS_MTZH' or b.yl3 = 'ERP_FKFS_DYQ' order by a.showxh
    </select>
    <select id="getFkfssdkByType" resultType="com.tzx.mobilepos.rest.vo.PaymentWay">
		select b.id, b.fkfsbh as payno, b.fkfsmc1 as payment_name1, b.fklxsx as payment_class, a.showxh,b.yl3 payment_mark
		from tq_paymentbutton a inner join TS_FKFSSDK b on a.fkfsid = b.id
		where a.sfxs = 'Y' 
		and b.fklxsx = #{fklxsx} 
		and b.yl3 = #{yl3}
		order by a.showxh limit 1 
    </select>
</mapper>
