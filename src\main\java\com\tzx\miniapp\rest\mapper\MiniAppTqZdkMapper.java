package com.tzx.miniapp.rest.mapper;

import com.tzx.publics.base.MyMapper;
import com.tzx.miniapp.rest.model.TqZdk;
import com.tzx.miniapp.rest.model.TsBmkzk;
import com.tzx.miniapp.rest.model.TsPsjgsdk;
import com.tzx.miniapp.rest.vo.*;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2018-05-23
 */

public interface MiniAppTqZdkMapper extends MyMapper<TqZdk> {

	public TsBmkzk getBh(@Param("bmc") String bmc, @Param("zdmc") String zdmc);
	
	public TqZdk getZdCount(@Param("ktskjh") String ktskjh);
	
	public int initZdk(@Param("kdzdbh") String kdzdbh, @Param("ktbcid") int ktbcid);
	
	public int delWdk(@Param("kdzdbh") String kdzdbh);

	public int updateBh(@Param("nr") String nr, @Param("bmc") String bmc, @Param("zdmc") String zdmc);

	public int addTc(@Param("szdbh") String szdbh, @Param("aitemid") int aitemid, @Param("ixmsl") int ixmsl);

	public int addCm(@Param("szdbh") String szdbh, @Param("aitemid") int aitemid, @Param("ixmsl") int ixmsl, @Param("sskjh") String sskjh, @Param("sxsyh") String sxsyh, @Param("skwbh") String skwbh, @Param("sggbh") String sggbh);

	public String getYhsx(@Param("id") int id);

	public int yhfstj(@Param("iyhfsid") int iyhfsid, @Param("szdbh") String szdbh, @Param("isl") BigDecimal isl, @Param("syhsx") String syhsx);

	public int addYhfs(@Param("aitemid") int aitemid, @Param("iyhfsid") int iyhfsid, @Param("szdbh") String szdbh, @Param("sskjh") String sskjh, @Param("ixmsl") int ixmsl);

	public BillMoney findBillMoney(@Param("zdbh") String zdbh);

	public CalcMoney findCalcMoney(@Param("zdbh") String zdbh);

	public AccountsOrder accountsOrder(@Param("szdbh") String szdbh, @Param("ijzid") int ijzid, @Param("ifkje") BigDecimal ifkje, @Param("ifksl") int ifksl, @Param("sfkhm") String sfkhm, @Param("ssfzhm") String ssfzhm, @Param("slxdh") String slxdh, @Param("sfkbz") String sfkbz, @Param("sskjh") String sskjh, @Param("sskyh") String sskyh);
	
	public int updateZdk(@Param("kdzdbh") String kdzdbh, @Param("jzzdbh") String jzzdbh, @Param("jzbbrq") Date jzbbrq, @Param("jzsj") Date jzsj, @Param("jzcs") int jzcs, @Param("jzskjh") String jzskjh, @Param("jzczry") String jzczry, @Param("jzsx") String jzsx, @Param("ksjzsj") Date ksjzsj, @Param("jzjssj") Date jzjssj, @Param("jzbcid") int jzbcid);
	
	public int updateWdk(@Param("kdzdbh") String kdzdbh, @Param("jzzdbh") String jzzdbh, @Param("jzskjh") String jzskjh, @Param("jzbbrq") Date jzbbrq, @Param("jzbcid") int jzbcid);
	
	public int updateFklslsk(@Param("kdzdbh") String kdzdbh, @Param("jzzdbh") String jzzdbh, @Param("jzbcid") int jzbcid);
	
	public String getFkfsid(@Param("yl3") String yl3);
	
	public TsPsjgsdk getJg();
	
	public List<WdDishVo> getWdDish(@Param("kdzdbh") String kdzdbh);
	
	public String getRifUrl();
	
	public List<ItemVo> getDiscountR(@Param("kdzdbh") String kdzdbh);
	
	public List<ItemVo> getItemR(@Param("kdzdbh") String kdzdbh);
	
	public List<ComboDetails> getComboDetailsR(@Param("kdzdbh") String kdzdbh, @Param("clmxid") int clmxid);
	
	public String getTasteNames(@Param("kwbhs") String kwbhs);
	
	public int getBcid();
	
	public int updateKtczry(@Param("kdzdbh") String kdzdbh, @Param("ktczry") String ktczry, @Param("ygdlcs") int ygdlcs);
	
	public int updateZrje(@Param("kdzdbh") String kdzdbh, @Param("zrje") double zrje);
	
	public int delZdkByYddh(@Param("yddh") String yddh);
	
	public int delWdkByYddh(@Param("yddh") String yddh);
	
	public int delFklslskByYddh(@Param("yddh") String yddh);
}