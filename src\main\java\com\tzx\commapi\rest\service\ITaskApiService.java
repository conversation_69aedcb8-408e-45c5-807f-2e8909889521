package com.tzx.commapi.rest.service;

import com.tzx.commapi.rest.vo.TqTask;
import com.tzx.commapi.rest.vo.CommApiData;

/**
 * Created by Zhouxh on 2020-09-08.
 */
public interface ITaskApiService {
    void removePMSOrderTask(String uuid);
    public void doPMSSyncOrder(TqTask tqTask,boolean persistence);

    public CommApiData UploadJPData(TqTask tqTask);
    public void doJpDataTask();
    
    public CommApiData uploadQimaiData(TqTask tqTask);
    public void doQimaiDataTask();

    public CommApiData UploadFangXingData(TqTask tqTask);
    public void doFangXingDataTask();
    
    public CommApiData UploadKeChuanData(TqTask tqTask);
    public void doKeChuanDataTask();
    
    public void doQmStatusTask();
    
    public CommApiData uploadWy3Data(TqTask tqTask);
    public void doWy2And3DataTask();
    
    public void doWy2To3Data();

    public void bohDataEstateTasks();

    public void downloadMsgTasks();

    public void lockCtrlVerifyTasks();

    public void doNaturalDayTask();
}
