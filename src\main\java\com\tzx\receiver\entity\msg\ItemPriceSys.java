package com.tzx.receiver.entity.msg;

import java.io.Serializable;

public class ItemPriceSys implements Serializable {
	private Integer id;
	private Integer jgxh;
	private String jgtxbh;
	private Integer xmid;
	private String cmbh;
	private String cmggbh;
	private Double cmje;
	private Double hy;
	private Double cr; // 12306
	private Double xms; // 订餐小秘书
	private Double syt; // 商宴通
	private Double xe; // 小E
	private Double mcw; // 美餐网
	private Double dj; // 到家
	private Double channel01;
	private Double channel02;
	private Double channel03;
	private Double channel04;
	private Double channel05;
	private Double channel06;
	private Double channel07;
	private Double channel08;
	private Double channel09;
	private Double channel10;

	public Double getHy() {
		return hy;
	}

	public void setHy(Double hy) {
		this.hy = hy;
	}

	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getJgxh() {
		return this.jgxh;
	}

	public void setJgxh(Integer jgxh) {
		this.jgxh = jgxh;
	}

	public String getJgtxbh() {
		return this.jgtxbh;
	}

	public void setJgtxbh(String jgtxbh) {
		this.jgtxbh = jgtxbh;
	}

	public Integer getXmid() {
		return this.xmid;
	}

	public void setXmid(Integer xmid) {
		this.xmid = xmid;
	}

	public String getCmbh() {
		return this.cmbh;
	}

	public void setCmbh(String cmbh) {
		this.cmbh = cmbh;
	}

	public String getCmggbh() {
		return this.cmggbh;
	}

	public void setCmggbh(String cmggbh) {
		this.cmggbh = cmggbh;
	}

	public Double getCmje() {
		return this.cmje;
	}

	public void setCmje(Double cmje) {
		this.cmje = cmje;
	}

	public Double getCr() {
		return cr;
	}

	public void setCr(Double cr) {
		this.cr = cr;
	}

	public Double getXms() {
		return xms;
	}

	public void setXms(Double xms) {
		this.xms = xms;
	}

	public Double getSyt() {
		return syt;
	}

	public void setSyt(Double syt) {
		this.syt = syt;
	}

	public Double getXe() {
		return xe;
	}

	public void setXe(Double xe) {
		this.xe = xe;
	}

	public Double getMcw() {
		return mcw;
	}

	public void setMcw(Double mcw) {
		this.mcw = mcw;
	}

	public Double getDj() {
		return dj;
	}

	public void setDj(Double dj) {
		this.dj = dj;
	}

	public Double getChannel01() {
		return channel01;
	}

	public void setChannel01(Double channel01) {
		this.channel01 = channel01;
	}

	public Double getChannel02() {
		return channel02;
	}

	public void setChannel02(Double channel02) {
		this.channel02 = channel02;
	}

	public Double getChannel03() {
		return channel03;
	}

	public void setChannel03(Double channel03) {
		this.channel03 = channel03;
	}

	public Double getChannel04() {
		return channel04;
	}

	public void setChannel04(Double channel04) {
		this.channel04 = channel04;
	}

	public Double getChannel05() {
		return channel05;
	}

	public void setChannel05(Double channel05) {
		this.channel05 = channel05;
	}

	public Double getChannel06() {
		return channel06;
	}

	public void setChannel06(Double channel06) {
		this.channel06 = channel06;
	}

	public Double getChannel07() {
		return channel07;
	}

	public void setChannel07(Double channel07) {
		this.channel07 = channel07;
	}

	public Double getChannel08() {
		return channel08;
	}

	public void setChannel08(Double channel08) {
		this.channel08 = channel08;
	}

	public Double getChannel09() {
		return channel09;
	}

	public void setChannel09(Double channel09) {
		this.channel09 = channel09;
	}

	public Double getChannel10() {
		return channel10;
	}

	public void setChannel10(Double channel10) {
		this.channel10 = channel10;
	}
}