package com.tzx.miniapp.rest.service.impl;

import com.tzx.miniapp.common.Data;
import com.tzx.miniapp.common.SysDictionary;
import com.tzx.miniapp.rest.mapper.MiniAppFirstPayMapper;
import com.tzx.miniapp.rest.mapper.MiniAppTsPsjgsdkMapper;
import com.tzx.miniapp.rest.model.TqZdk;
import com.tzx.miniapp.rest.model.TsGgcsk;
import com.tzx.miniapp.rest.model.TsPsjgsdk;
import com.tzx.miniapp.rest.service.IMiniAppNotification;
import com.tzx.publics.listener.InitDataListener;
import com.tzx.publics.util.*;
import com.tzx.publics.vo.OrganVo;

import com.tzx.receiver.common.utils.DBUtils;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.Executor;

@Service
public class MiniAppNotificationImpl implements IMiniAppNotification {

    private final static Logger LOGGER = LoggerFactory.getLogger(MiniAppNotificationImpl.class);

    @Autowired
    MiniAppFirstPayMapper firstPayMapper;

    @Autowired
    MiniAppTsPsjgsdkMapper tsPsjgsdkMapper;

    @Autowired
    Executor executor;

    @Override
    public Data synchrodataDish(JSONObject json) {
        Data data = new Data();
        String propertiesPath = "/application.properties";
        String notificationApi = "/api/synchrodataDish";
        String merchant = null;
        String brand = null;
        String store = null;
        // 	要同步的数据类型二选一：dish 菜品, dishkind 菜类, all 菜品菜类
        String type = "all";
        type = json.optString("type", type);
        //是否强制同步数据 1 重置数据（在.net后台所做修改将被覆盖）, 0 同步
        int force = 1;
        force = json.optInt("force", 1);
        //商户
        TsGgcsk merchantId = firstPayMapper.getGgcsToWs(SysDictionary.GGCS_WELCRM_MERCHANT_ID);
        if (merchantId != null) merchant = merchantId.getSdnr();
        if (merchant == null) {
            data.setSuccess(SysDictionary.FAILURE);
            data.setMsg("商户ID未配置！");
            return data;
        }
        //门店
        TsPsjgsdk tsPsjgsdk = tsPsjgsdkMapper.findLocalShopConfig();
        if (tsPsjgsdk != null && tsPsjgsdk.getJgcs() != 0) store = StringUtil.getString(tsPsjgsdk.getJgcs());
        if (store == null) {
            data.setSuccess(SysDictionary.FAILURE);
            data.setMsg("门店ID未配置！");
            return data;
        }
        //品牌
        TsGgcsk brandId = firstPayMapper.getGgcsToWs(SysDictionary.GGCS_WELCRM_BRAND_ID);
        if (brandId != null) brand = brandId.getSdnr();
        if (brand == null) {
            data.setSuccess(SysDictionary.FAILURE);
            data.setMsg("品牌ID未配置！");
            return data;
        }
        String appKey= DBUtils.getGGCSK("WELCRM_APPKEY");
        if(StringUtils.isEmpty(appKey)){
            appKey = PropertiesUtil.readValueForClasses(propertiesPath, "welcrm_appkey");
        }
        if(StringUtils.isEmpty(appKey)){
            data.setSuccess(SysDictionary.FAILURE);
            data.setMsg("APPKEY未配置！");
            return data;
        }
        String welcrm_api=DBUtils.getGGCSK("WELCRM_API");
        if(StringUtils.isEmpty(welcrm_api)){
            welcrm_api = PropertiesUtil.readValueForClasses(propertiesPath, "welcrm_api_refund");
        }
        if(StringUtils.isEmpty(welcrm_api)){
            data.setSuccess(SysDictionary.FAILURE);
            data.setMsg("接口地址未配置！");
            return data;
        }
        final String notificationDish = welcrm_api + notificationApi;
        final String merchant_ = merchant;
        final String store_ = store;
        final String brand_ = brand;
        final String type_ = type;
        final int force_ = force;
        final String finalAppKey = appKey;
        executor.execute(new Runnable() {
            @Override
            public void run() {
                requestNotification(notificationDish, finalAppKey, merchant_, store_, brand_, type_, force_);
            }
        });
        data.setSuccess(SysDictionary.SUCCESS);
        data.setMsg("通知同步数据！");
        return data;
    }

    @Override
    public void requestNotification(String url, String appKey, String merchant, String store, String brand, String type, int force) {
        LOGGER.info("商户ID:" + merchant + " 品牌ID:" + brand + " 门店ID:" + store);
        String sign = Util.miniappSign(appKey, merchant + "_" + brand + "_" + store);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("sid", StringUtil.getInt(store, -1));
        jsonObject.put("type", type);
        jsonObject.put("force", force);
        jsonObject.put("sign", sign);
        LOGGER.info("url:" + url + "  request  " + jsonObject.toString());
        String resp = Util.jsonPostRequest(url, jsonObject.toString());
        LOGGER.info("url:" + url + "  response  " + resp);
    }

    @Override
    public Data sendmealmsg(JSONObject json) {
        Data data = new Data();
        String orderId = json.optString("zdbh");
        TqZdk zdk=firstPayMapper.getZdbhByZdbh(orderId);
        if (orderId == null||zdk==null||zdk.getYddh()==null||Tools.isNullOrEmpty(zdk.getYddh())) {
            data.setSuccess(SysDictionary.FAILURE);
            data.setMsg("订单号不存在！");
            return data;
        }
		String yddbh = zdk.getYddh().replaceFirst("TS", "");
        String propertiesPath = "/application.properties";
        String sendApi = "/api/sendmealmsg/" + yddbh;
        String welcrm_api=DBUtils.getGGCSK("WELCRM_API");
        if(StringUtils.isEmpty(welcrm_api)){
            welcrm_api = PropertiesUtil.readValueForClasses(propertiesPath, "welcrm_api_refund");
        }
        if(StringUtils.isEmpty(welcrm_api)){
            data.setSuccess(SysDictionary.FAILURE);
            data.setMsg("接口地址未配置！");
            return data;
        }
        String url = welcrm_api + sendApi;
        String resp = Util.jsonPostRequest(url, "");
        LOGGER.info("url:" + url + "  response  " + resp);
        if (Const.CONNECT_ERROR.equals(resp) || Tools.isNullOrEmpty(resp)){
            data.setSuccess(SysDictionary.FAILURE);
            data.setMsg("微生活连接失败！");
            return data;
        }
        data.setSuccess(SysDictionary.SUCCESS);
        data.setMsg("取餐通知成功");
        return data;
    }
    
    
    @Override
	public Data qmSynchrodataDish(JSONObject json) {
		Data data = new Data();
		data.setSuccess(1);
		data.setMsg("通知同步失败");
		try {
			String notificationApi = "/api/sync/goodsInfo";
			String url = InitDataListener.thirdMap.get("QIMAI_QIMAI_URL");
			String callbackShopName = InitDataListener.thirdMap.get("QIMAI_CALLBACK_SHOP_NAME");
			OrganVo organVo = InitDataListener.organVo;
			String shopId = organVo.getJgbh(); // 实际为编号

			final String notificationDish = url + notificationApi;
			final String callbackShopName_ = callbackShopName;
			final String shopId_ = shopId;
			executor.execute(new Runnable() {
				@Override
				public void run() {
					qmRequestNotification(notificationDish, callbackShopName_, shopId_);
				}
			});
			data.setSuccess(SysDictionary.SUCCESS);
			data.setMsg("通知同步数据！");
			return data;
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			data.setSuccess(1);
			data.setMsg("系统错误");
			return data;
		}
	}
    
    public void qmRequestNotification(String url, String callbackShopName, String shopId) {
        LOGGER.info("商户标识:" + callbackShopName + " 门店编号:" + shopId);
        
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("callbackShopName", callbackShopName);
        jsonObject.put("shop_id", shopId);
        LOGGER.info("url:" + url + "  request  " + jsonObject.toString());
        String resp = Util.jsonPostRequest(url, jsonObject.toString());
        LOGGER.info("url:" + url + "  response  " + resp);
    }

}
