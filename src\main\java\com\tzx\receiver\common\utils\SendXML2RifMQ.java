package com.tzx.receiver.common.utils;

import com.tzx.receiver.common.upload.UploadGloVar;
import org.apache.activemq.ActiveMQConnection;
import org.apache.activemq.ActiveMQConnectionFactory;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;


import javax.jms.*;
import java.io.*;
import java.util.ResourceBundle;

/**
 * <AUTHOR>
 * @Date 2019-04-15
 * @Descption XML形式的MQ上传  将UDP的和HTTP的统一抽离到这里
 **/
public class SendXML2RifMQ {
    private static Log log = LogFactory.getLog(SendXML2RifMQ.class);
    private static ResourceBundle tzxresource = null;

    public static void sendFile2MQ(String fileName) throws Exception {
        try {
            log.info("开始处理XML文件，文件名为：" + fileName);
            String sendMsg = readFile(fileName, "UTF-8");

            ConnectionFactory connectionFactory = null;
            Connection connection = null;
            QueueSession session = null;
            Destination producerDstination = null;
            MessageProducer producer = null;
            String recvStr = "";
            String remoteQueueName = null;
//            DBUtils dbUtils = new DBUtils();

            tzxresource = ResourceBundle.getBundle("tzxmqconfig");
            if (StringUtils.isEmpty(remoteQueueName)) {
                remoteQueueName = UploadGloVar.getUploadName();
            }
            try {
                if (connectionFactory == null) {
                    connectionFactory = new ActiveMQConnectionFactory(
                            UploadGloVar.getMqUser(),
                            UploadGloVar.getMqPassword(),
                            UploadGloVar.getMqUrl() );
                }
                try {
                    //不知道哪个超时时间有作用， 就都设置吧。否则会出现，mq被卡死的现象

//                    ((ActiveMQConnectionFactory) connectionFactory).setSendTimeout(5000);
//                    ((ActiveMQConnectionFactory) connectionFactory).setWarnAboutUnstartedConnectionTimeout(5000);
//                    ((ActiveMQConnectionFactory) connectionFactory).setWarnAboutUnstartedConnectionTimeout(5000);
                    connection = connectionFactory.createConnection();
                } catch (Exception e) {
                    connectionFactory = new ActiveMQConnectionFactory(
                            UploadGloVar.getMqUser(),
                            UploadGloVar.getMqPassword(),
                            UploadGloVar.getMqUrl());
                    //不知道哪个超时时间有作用， 就都设置吧。否则会出现，mq被卡死的现象
//                    ((ActiveMQConnectionFactory) connectionFactory).setSendTimeout(5000);
////                    ((ActiveMQConnectionFactory) connectionFactory).setConnectResponseTimeout(5000);
//                    ((ActiveMQConnectionFactory) connectionFactory).setWarnAboutUnstartedConnectionTimeout(5000);
//                    ((ActiveMQConnectionFactory) connectionFactory).setWarnAboutUnstartedConnectionTimeout(5000);

                    connection = connectionFactory.createConnection();
                }
                int port = Integer.parseInt(UploadGloVar.getMqUrl().substring(
                        StringUtils.lastIndexOf(UploadGloVar.getMqUrl(),":")+1));
                String server = UploadGloVar.getMqUrl().substring(UploadGloVar.getMqUrl().indexOf("tcp://")+6,
                        StringUtils.lastIndexOf(UploadGloVar.getMqUrl(),":"));

                //连接开始之前，先判断一下Telnet的状态，因为有时候超时不好用
                if(!NetHelper.checkTelnet(server,port)){
                    log.info("网络连接异常");
                    throw new RuntimeException();
                }
                connection.start();

                session = (QueueSession) connection.createSession(false, 1);
                producerDstination = session.createQueue(remoteQueueName);
                producer = session.createProducer(producerDstination);
                producer.setDeliveryMode(2);

                TextMessage tmsg = session.createTextMessage();
                if (sendMsg != null) {
                    tmsg.setText(sendMsg);
                    producer.send(tmsg);
                    log.info("发送到MQ成功");
                }
            } catch (Exception e) {
                throw e;

            } finally {
				try {
					if (null != producer) {
						producer.close();
					}
				} catch (Exception e) {
					e.printStackTrace();

				}
				try {
					if (null != session) {
						session.close();
					}
				} catch (Exception e) {
					e.printStackTrace();

				}
				try {
					if (null != connection) {
						connection.close();
					}
				} catch (Exception e) {
					e.printStackTrace();

				}
            }

        } catch (Exception e) {
            throw e;
        }
    }

    // /可以对文件本身的编码进行设定
    private static String readFile(String filePath, String EnCoding) {
        String s = "";
        File file = null;
        FileInputStream fileInputStream = null;
        InputStreamReader inputStreamReader = null;
        BufferedReader br = null;
        try {
            // 读取文件时指定字符编码(new FileInputStream( new File(szFileName)), EnCoding)
            file = new File(filePath);
            fileInputStream = new FileInputStream(file);
            inputStreamReader = new InputStreamReader(fileInputStream, EnCoding);
            br = new BufferedReader(inputStreamReader);
            StringBuffer stringBuffer = new StringBuffer();
            while ((s = br.readLine()) != null) {
                stringBuffer.append(s);
                stringBuffer.append("\n");
            }
            s = stringBuffer.toString();
            // System.out.println(s);
        } catch (Exception e) {
            log.info("读消息文件时候发生异常： " + e.toString());
            e.printStackTrace();
        } finally {
            try {
                br.close();
                inputStreamReader.close();
                fileInputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return s;
    }
}
