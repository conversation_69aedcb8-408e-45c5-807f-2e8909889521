package com.tzx.kvs.rest.controller;

import java.net.URLDecoder;
import java.util.UUID;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.tzx.kvs.common.KvsData;
import com.tzx.kvs.common.KvsException;
import com.tzx.kvs.rest.service.IKvsInfoService;
import com.tzx.mobilepos.common.Data;
import com.tzx.publics.base.BaseController;
import com.tzx.publics.common.BaseData;
import com.tzx.publics.service.INumberSynchronizedService;

import net.sf.json.JSONObject;




@CrossOrigin
@RestController
@RequestMapping("/kvs")
public class KvsController extends BaseController {
	private final static Logger LOGGER = LoggerFactory.getLogger(KvsController.class);
	
	@Autowired
	private IKvsInfoService kvsInfoService;
	@Autowired
	private INumberSynchronizedService numberSynchronizedService;
	
	private JSONObject GetSaleJsonObject(KvsData data, String methodTag, String uuid, String jsonStr) {
		try {
			String udjson = URLDecoder.decode(jsonStr, "UTF-8");
			udjson = udjson.substring(0, udjson.lastIndexOf("}") + 1);
//			LOGGER.info("request ({}), UUID={}, json={}, udjson={}", new Object[] { methodTag, uuid, jsonStr, udjson });
			data.setCode(0);
			return JSONObject.fromObject(udjson);
		} catch (Exception e) {
			e.printStackTrace();
			data.setMsg("解析json异常,请求JSON格式错误");
			data.setCode(1);
			LOGGER.error(e.getMessage());
			return null;
		}
	}

	@RequestMapping(value = "/getKvsInfo", method = RequestMethod.POST, produces = MediaType.APPLICATION_FORM_URLENCODED_VALUE, consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
	public String getItemInfo(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) {
		response.setContentType("text/html;charset=utf-8");
		String methodTag = "getKvsInfo";
		KvsData data = new KvsData();
		String uuid = UUID.randomUUID().toString().replace("-", "");
		
		try {
			JSONObject jsonobj = GetSaleJsonObject(data, methodTag, uuid, json);
			if (data.getCode() != 0) {
				return JSONObject.fromObject(data).toString();
			}
			kvsInfoService.getKvsInfo(data, jsonobj);
		} catch (KvsException pe) {
			pe.printStackTrace();
			LOGGER.error("Ignore this PosException", pe.getMessage());
			data.setMsg(pe.getMessage());
		} catch (Exception e) {
			e.printStackTrace();
			LOGGER.error("Ignore this Exception", e);
			data.setCode(1);
			data.setMsg("系统错误:" + e);
		} finally {
//			LOGGER.info("response({}), UUID={}, json={}", new Object[] { methodTag, uuid, JSONObject.fromObject(data).toString() });
		}
		return JSONObject.fromObject(data).toString();
	}

	
	@RequestMapping(value = "/removeBill", method = RequestMethod.POST, produces = MediaType.APPLICATION_FORM_URLENCODED_VALUE, consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
	public String removeBill(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) {
		response.setContentType("text/html;charset=utf-8");
		String methodTag = "removeBill";
		KvsData data = new KvsData();
		String uuid = UUID.randomUUID().toString().replace("-", "");
		
		String number = "";
		BaseData dataB = new BaseData();
		try {
			JSONObject jsonobj = GetSaleJsonObject(data, methodTag, uuid, json);
			if (data.getCode() != 0) {
				return JSONObject.fromObject(data).toString();
			}
			JSONObject jsonData = jsonobj.optJSONObject("data");
			String kdzdbh = jsonData.optString("kdzdbh", "");
			number = "KVS" + kdzdbh;
			dataB = numberSynchronizedService.billSynchronized(number, 1);
			if (dataB.getCode() == 0) {
				kvsInfoService.removeBill(data, jsonobj);
			} else {
				data.setCode(1);
				data.setMsg(dataB.getMsg());
			}
		} catch (KvsException ke) {
			ke.printStackTrace();
			LOGGER.error("Ignore this PosException", ke.getMessage());
			data.setMsg(ke.getMessage());
		} catch (Exception e) {
			e.printStackTrace();
			LOGGER.error("Ignore this Exception", e);
			data.setCode(1);
			data.setMsg("系统错误:" + e);
		} finally {
			if (dataB.getCode() == 0) {
				numberSynchronizedService.billSynchronized(number, 2);
			}
//			LOGGER.info("response({}), UUID={}, json={}", new Object[] { methodTag, uuid, JSONObject.fromObject(data).toString() });
		}
		return JSONObject.fromObject(data).toString();
	}
	
	@RequestMapping(value = "/getRecoverKvsInfo", method = RequestMethod.POST, produces = MediaType.APPLICATION_FORM_URLENCODED_VALUE, consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
	public String getRecoverKvsInfo(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) {
		response.setContentType("text/html;charset=utf-8");
		String methodTag = "getRecoverKvsInfo";
		KvsData data = new KvsData();
		String uuid = UUID.randomUUID().toString().replace("-", "");
		
		try {
			JSONObject jsonobj = GetSaleJsonObject(data, methodTag, uuid, json);
			if (data.getCode() != 0) {
				return JSONObject.fromObject(data).toString();
			}
			kvsInfoService.getRecoverKvsInfo(data, jsonobj);
		} catch (KvsException pe) {
			pe.printStackTrace();
			LOGGER.error("Ignore this PosException", pe.getMessage());
			data.setMsg(pe.getMessage());
		} catch (Exception e) {
			e.printStackTrace();
			LOGGER.error("Ignore this Exception", e);
			data.setCode(1);
			data.setMsg("系统错误:" + e);
		} finally {
//			LOGGER.info("response({}), UUID={}, json={}", new Object[] { methodTag, uuid, JSONObject.fromObject(data).toString() });
		}
		return JSONObject.fromObject(data).toString();
	}
	
	@RequestMapping(value = "/recoverRemoveBill", method = RequestMethod.POST, produces = MediaType.APPLICATION_FORM_URLENCODED_VALUE, consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
	public String recoverRemoveBill(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) {
		response.setContentType("text/html;charset=utf-8");
		String methodTag = "recoverRemoveBill";
		KvsData data = new KvsData();
		String uuid = UUID.randomUUID().toString().replace("-", "");
		
		String number = "";
		BaseData dataB = new BaseData();
		try {
			JSONObject jsonobj = GetSaleJsonObject(data, methodTag, uuid, json);
			if (data.getCode() != 0) {
				return JSONObject.fromObject(data).toString();
			}
			JSONObject jsonData = jsonobj.optJSONObject("data");
			String kdzdbh = jsonData.optString("kdzdbh", "");
			number = "KVS" + kdzdbh;
			dataB = numberSynchronizedService.billSynchronized(number, 1);
			if (dataB.getCode() == 0) {
				kvsInfoService.recoverRemoveBill(data, jsonobj);
			} else {
				data.setCode(1);
				data.setMsg(dataB.getMsg());
			}
		} catch (KvsException ke) {
			ke.printStackTrace();
			LOGGER.error("Ignore this PosException", ke.getMessage());
			data.setMsg(ke.getMessage());
		} catch (Exception e) {
			e.printStackTrace();
			LOGGER.error("Ignore this Exception", e);
			data.setCode(1);
			data.setMsg("系统错误:" + e);
		} finally {
			if (dataB.getCode() == 0) {
				numberSynchronizedService.billSynchronized(number, 2);
			}
//			LOGGER.info("response({}), UUID={}, json={}", new Object[] { methodTag, uuid, JSONObject.fromObject(data).toString() });
		}
		return JSONObject.fromObject(data).toString();
	}
}
