<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.miniapp.rest.mapper.MiniAppOrderPrecheckMapper">

  	<insert id="insertBtYddActive">
		insert into bt_yddpayactive
		(yddh,pay_name,pay_money,couponcodes,active_type
		<if test="active_id != null and active_id != 0">
			, active_id
		</if>
		)
		values(#{yddh}, #{pay_name}, #{pay_money}, #{couponcodes}, #{active_type}
		<if test="active_id != null and active_id != 0">
			, #{active_id}
		</if>
		)
	</insert>
  
	<delete id="clearYdd">
		delete from bt_ydd where yddh=#{yddh}
	</delete>
	<delete id="clearYdxm1">
		delete from bt_ydxm1 where yddh=#{yddh}
	</delete>
	<delete id="clearYdxm2">
		delete from bt_ydxm2 where yddh=#{yddh}
	</delete>
	<delete id="clearYddTcSelectMx">
		delete from bt_tcselectmx where yddh=#{yddh}
	</delete>
	<delete id="clearYddActive">
		delete from bt_yddactive where yddh=#{yddh}
	</delete>
	<delete id="clearYddPayActive">
		delete from bt_yddpayactive where yddh=#{yddh}
	</delete>
	<delete id="clearYddPayments">
		delete from bt_payments where yddh=#{yddh}
	</delete>
	
	<insert id="insertBtYdd" parameterType="com.tzx.miniapp.rest.vo.BtYdd">
		insert into bt_ydd
		(yddh,totalprice,ydrs,men,women,eldernum,childnum,shops_id,hybh,ydrq,qdsj,ydbc,ddzt,paychannel,mealtime,kwxh,lxr,lxrdh,diningway,zlbh,yl4)
		values(#{by.yddh,jdbcType=VARCHAR},#{by.totalprice,jdbcType=NUMERIC},#{by.ydrs,jdbcType=INTEGER},
		#{by.men,jdbcType=INTEGER},#{by.women,jdbcType=INTEGER},#{by.eldernum,jdbcType=INTEGER},
		#{by.childnum,jdbcType=INTEGER},#{by.shops_id,jdbcType=VARCHAR},#{by.hybh,jdbcType=VARCHAR},
		#{by.ydrq,jdbcType=VARCHAR}, #{by.qdsj,jdbcType=VARCHAR},
		#{by.ydbc,jdbcType=VARCHAR}, #{by.ddzt,jdbcType=VARCHAR},
		#{by.channel,jdbcType=INTEGER},#{by.mealtime,jdbcType=VARCHAR},#{by.kwxh,jdbcType=VARCHAR},
		#{by.lxr,jdbcType=VARCHAR},#{by.lxrdh,jdbcType=VARCHAR},#{by.diningway,jdbcType=VARCHAR},
		#{by.zlbh,jdbcType=VARCHAR},#{by.yl4,jdbcType=VARCHAR})
	</insert>
  <insert id="insertBtYdxm1" parameterType="com.tzx.miniapp.rest.vo.BtYdxm1">
    insert into bt_ydxm1
    (yddh, xmbh, xmmc, memo, yl1,yl2,yl3,yl4,yl5)
    values (#{yddh}, #{xmbh},
    #{xmmc}, #{memo}, #{yl1}, #{yl2}, #{yl3}, #{yl4}, #{yl5})
  </insert>
	<insert id="insertBtYdxm2" parameterType="java.util.List">
		insert into bt_ydxm2
		(yddh, xmid, xmbh, xmsx, xmdj, 
		xmsl, zkl, totalprice, dwbh, kwbh, 
		cmje, tcbh, tcdch, fzsl, fzje, 
		dcxh, xmmc, yl3, isactivity, top_item_id, 
		yl4, yl2, yl5)
		values
		<foreach collection="list" item="ydxm" index="index" separator=",">
			(
			#{ydxm.yddh}, #{ydxm.xmid}, #{ydxm.xmbh}, #{ydxm.xmsx}, #{ydxm.xmdj}, 
			#{ydxm.xmsl}, #{ydxm.zkl}, #{ydxm.totalprice}, #{ydxm.dwbh}, #{ydxm.kwbh}, 
			#{ydxm.cmje}, #{ydxm.tcbh}, #{ydxm.tcdch}, #{ydxm.fzsl}, #{ydxm.fzje}, 
			#{ydxm.dcxh}, #{ydxm.xmmc}, #{ydxm.yl3}, #{ydxm.isactivity}, #{ydxm.top_item_id}, 
			#{ydxm.yl4}, #{ydxm.yl2}, #{ydxm.yl5}
			)
		</foreach>
	</insert>
  <insert id="insertBtTcselectmx" parameterType="java.util.List">
    insert into bt_tcselectmx
    (yddh, fzsl, fzje, dcxh,fzid,mxid)
    values
    <foreach collection="list" item="tsmx" index="index"
      separator=",">
      (
      #{tsmx.yddh}, #{tsmx.fzsl}, #{tsmx.fzje}, #{tsmx.dcxh},
      #{tsmx.fzid}, #{tsmx.mxid}
      )
    </foreach>
  </insert>
	<insert id="insertBtActivity" parameterType="java.util.List">
		insert into bt_yddactive
		(yddh, dcxh, active_type,active_id, active_current_name, active_name, ydxmid, buy_ydxmid, buy, gift, coupon_money)
		values
		<foreach collection="list" item="active" index="index"
			separator=",">
			(#{active.yddh}, #{active.dcxh}, #{active.active_type}, #{active.active_id}, #{active.active_current_name}, #{active.active_name},
			#{active.ydxmid}, #{active.buy_ydxmid}, 
			#{active.buy}, #{active.gift}, #{active.coupon_money})
		</foreach>
	</insert>
	
	<update id="updateYddActiveGiftYdxmid">
    update bt_yddactive set ydxmid = b.id
    from bt_ydxm2 b
    where bt_yddactive.yddh = b.yddh and (b.xmsx = '单品' or b.xmsx = '套餐') and b.yddh = #{yddh} and bt_yddactive.dcxh = b.dcxh and b.dcxh = #{dcxh}
	</update>
  <update id="updateYddActiveBuyYdxmid">
    update bt_yddactive set buy_ydxmid = b.id
    from bt_ydxm2 b
    where bt_yddactive.yddh = b.yddh and (b.xmsx = '单品' or b.xmsx = '套餐') and b.yddh = #{yddh} and bt_yddactive.dcxh = #{dcxh} and b.dcxh = #{maindcxh}
  </update>
 
  <select id="selectYhfssdk" resultType="java.lang.Integer">
    select count(*) as yhjlsl from ts_yhfssdk where id = #{id}
  </select>
  <insert id="insertYhfssdk">
    insert into ts_yhfssdk
    (id, yhfsbh, yhfsmc1, ksrq, jsrq, kssj, jssj, yhsx, buydishrel)
    values (#{id}, #{yhfsbh}, #{yhfsmc1}, '2018-01-01', '2099-01-01', '00:00:00', '23:59:59', #{yhsx}, 1)
  </insert>
   
  <select id="selectYhfssdk_xf" resultType="java.lang.Integer">
    select count(*) as yhjlsl from ts_yhfssdk_xf where id = #{id}
  </select>
  <insert id="insertYhfssdk_xf">
    insert into ts_yhfssdk_xf
    (id, yhfsbh, yhfsmc1, ksrq, jsrq, kssj, jssj, yhsx, buydishrel)
    values (#{id}, #{yhfsbh}, #{yhfsmc1}, '2018-01-01', '2099-01-01', '00:00:00', '23:59:59', #{yhsx}, 1)
  </insert>
  
	<select id="getBcid" resultType="java.lang.Integer">
<!-- 		select id from ts_bck where bcbh = (select * from p_getbcbh(CAST(now() AS DATETIME))) -->
		select * from p_getbcmc(CAST(now() AS DATETIME))
	</select>
	
	<select id="getDiscount" resultType="com.tzx.miniapp.rest.vo.BtYdxm1">
		select a.* from (
		select '3' as yl1, sd.id as yl2, sd.yhfsbh as xmbh, sd.yhfsmc1 as xmmc,
		${total} as yl3, tj.zdxe, (${total} -
		round(sd.zkl::numeric/100::numeric,2)*${total}) as yl4,
		round(sd.zkl::numeric/100::numeric,2)*${total} as yl5
		from tq_clmxk cl left join ts_yhfssdk sd on cl.yhfsid = sd.id
		left join ts_yhfstjk tj on sd.id = tj.yhfsid
		where cl.clmxlb = 'CPYSMXLB_YHFS' and sd.yhsx = '15' and sd.zkfs = '04'
		and to_char(now(),'yyyy-mm-dd') &gt; sd.ksrq and
		to_char(now(),'yyyy-mm-dd') &lt; sd.jsrq
		and to_char(current_timestamp,'hh24:mi:ss') &gt; sd.kssj and
		to_char(current_timestamp,'hh24:mi:ss') &lt; sd.jssj
		and tj.zdxe &lt;=${total}
		union all
		select sd.yhsx as yl1, sd.id as yl2, sd.yhfsbh as xmbh, sd.yhfsmc1 as xmmc,
		${total} as yl3, tj.zdxe, sd.yhje as yl4,
		${total}-sd.yhje as yl5
		from tq_clmxk cl left join ts_yhfssdk sd on cl.yhfsid = sd.id
		left join ts_yhfstjk tj on sd.id = tj.yhfsid
		where cl.clmxlb = 'CPYSMXLB_YHFS' and sd.yhsx = '1'
		and to_char(now(),'yyyy-mm-dd') &gt; sd.ksrq and
		to_char(now(),'yyyy-mm-dd') &lt; sd.jsrq
		and to_char(current_timestamp,'hh24:mi:ss') &gt; sd.kssj and
		to_char(current_timestamp,'hh24:mi:ss') &lt; sd.jssj
		and tj.zdxe &lt;=${total}
		) a order by a.yl5 asc limit 1
	</select>
	
	<select id="getFzsl" resultType="java.lang.Double">
		select coalesce(max(fzsl),-1) 
		from ts_tcfzmxk where fzid = #{fzid} and mxid = #{mxid} limit 1
	</select>
	
	<select id="getIsVipPrice" resultType="java.lang.String">
		select COALESCE(isVipPrice, 'N') from ts_crm_member_rule where id = #{grade} limit 1
	</select>

	<select id="addCmByZs" useCache="false" resultType="java.lang.Integer" >
		select * from P_AddCM_APP (#{szdbh}, #{aitemid}, #{ixmsl}, #{sskjh}, #{sxsyh}, #{skwbh}, #{sggbh}, #{skwbz}, #{atype});
	</select>
	
	
	<select id="getClmxidByXmbh" resultType="java.lang.String">
		select a.id from tq_clmxk a left join tq_clsdk b on a.clid = b.id 
		left join tq_cbsdk c on b.cbid = c.id where c.yl1 = 'TS' and a.xmbh = #{xmbh} 
	</select>
	
	<select id="addTcByZs" resultType="java.lang.Integer" >
		select * from P_ADDTC(#{szdbh}, #{aitemid}, #{ixmsl});
	</select>
	
	<delete id="deletetcdcxzlsk">
		delete from tq_tcdcxzlsk where kdzdbh = #{bill_num} and mxlxid is not null<!-- and tcid = #{tcid} and tcxh = #{tcxh}  -->
		<if test="item_id != null and item_id != ''">
			and mxxmid = #{item_id}
		</if>
	</delete>
	
	<select id="getItmeIdByXmbh" resultType="java.lang.Integer">
		select cmid from ts_cmk where cmbh = #{cmbh} limit 1
	</select>
	
	<select id="getTcxh" resultType="com.tzx.miniapp.rest.vo.GroupDetails">
		select tc.* from ts_tcmxk tc where xmid = #{tcid} and (mxlx = 'ERP_MXLX_GROUP' or mxlx = 'ERP_MXLX_ANY_GROUP') and mxxmid = #{fzid} limit 1
	</select>
	
	<select id="getFzmxid" resultType="java.lang.Integer">
		select id from ts_tcfzmxk where fzid = #{fzid} and fzje = #{fzje} and mxid = #{mxid} limit 1
	</select>
	
	<insert id="addTcdcxzlsk">
		insert into tq_tcdcxzlsk(kdzdbh,tcid,tcxh,mxxmid,cmsl,xcsl,cmdj,cmje,fzje,mxlxid)
		values(#{kdzdbh},#{tcid},#{tcxh},#{item_id},#{item_count},'1',#{cmdj},#{cmje},#{fzje},#{mxlxid});
	</insert>
	
	<select id="getZdk" resultType="com.tzx.miniapp.rest.model.TqZdk">
		select * from tq_zdk where yddh = #{yddh} limit 1
	</select>
	
	<select id="getZdlsk" resultType="com.tzx.miniapp.rest.model.TqZdk">
		select * from tq_zdlsk where yddh = #{yddh} limit 1
	</select>
	
	<select id="getClmxidByXmid" resultType="java.lang.String">
		select a.id from tq_clmxk a left join tq_clsdk b on a.clid = b.id 
		left join tq_cbsdk c on b.cbid = c.id where c.yl1 = 'TS' and a.xmid = #{xmid} 
	</select>
	
	<select id="getNewWdk" resultType="com.tzx.miniapp.rest.model.TqWdk">
		select * from tq_wdk where kdzdbh = #{kdzdbh} order by dcxh desc limit 1
	</select>
	
	<select id="getYhfs" resultType="com.tzx.miniapp.rest.model.TsYhfssdk">
		select * from ts_yhfssdk where yhsx = #{yhsx} limit 1
	</select>
	
	<select id="addCmNew" useCache="false" resultType="java.lang.Integer" >
		select * from P_AddCM_APP (#{szdbh}, #{aitemid}, #{ixmsl}, #{sskjh}, #{sxsyh}, #{skwbh}, #{sggbh}, #{skwbz}, #{atype});
<!-- 		select * from p_addcmnew(#{szdbh}, #{aitemid}, #{ixmsl},#{sskjh},#{sxsyh},#{skwbh},#{sggbh},0,sSYSDC,sJGXH,sJGTXBH,'cmjg',-1,-1,#{skwbz}); -->
	</select>
	
	<insert id="insertBtYddByZs" parameterType="com.tzx.miniapp.rest.vo.BtYdd">
		insert into bt_ydd
		(yddh,totalprice,ydrs,men,women,eldernum,childnum,shops_id,hybh,ydrq,qdsj,ydbc,ddzt,paychannel,
		mealtime,kwxh,lxr,lxrdh,diningway,zlbh,shrbh,yl5,member_address,yl4,horseman_name,yl1,yl2,shop_rate,
		fail_type2,bill_num,invalid_desc,mobile)
		values(#{by.yddh,jdbcType=VARCHAR},#{by.totalprice,jdbcType=NUMERIC},#{by.ydrs,jdbcType=INTEGER},
		#{by.men,jdbcType=INTEGER},#{by.women,jdbcType=INTEGER},#{by.eldernum,jdbcType=INTEGER},
		#{by.childnum,jdbcType=INTEGER},#{by.shops_id,jdbcType=VARCHAR},#{by.hybh,jdbcType=VARCHAR},
		#{by.ydrq,jdbcType=VARCHAR}, #{by.qdsj,jdbcType=VARCHAR},
		#{by.ydbc,jdbcType=VARCHAR}, #{by.ddzt,jdbcType=VARCHAR},
		#{by.channel,jdbcType=INTEGER},#{by.mealtime,jdbcType=VARCHAR},#{by.kwxh,jdbcType=VARCHAR},
		#{by.lxr,jdbcType=VARCHAR},#{by.lxrdh,jdbcType=VARCHAR},#{by.diningway,jdbcType=VARCHAR},
		#{by.zlbh,jdbcType=VARCHAR},#{by.shrbh,jdbcType=VARCHAR},#{by.yl5,jdbcType=VARCHAR},
		#{by.member_address,jdbcType=VARCHAR},#{by.yl4,jdbcType=VARCHAR},#{by.horseman_name,jdbcType=VARCHAR},
		#{by.yl1,jdbcType=VARCHAR},#{by.yl2,jdbcType=VARCHAR},#{by.shop_rate,jdbcType=VARCHAR},
		#{by.fail_type2,jdbcType=VARCHAR},#{by.bill_num,jdbcType=VARCHAR},
		#{by.invalid_desc,jdbcType=VARCHAR},#{by.mobile,jdbcType=VARCHAR})
	</insert>

	<select id="addCmNewByZs" useCache="false" resultType="java.lang.Integer">
		select * from p_addcmnew(
			#{szdbh}, 
			#{aitemid}, 
			#{ixmsl}, 
			#{sskjh}, 
			#{sxsyh}, 
			#{skwbh}, 
			#{sggbh},
			6, 
			#{ssysdc}, 
			#{sjgxh},
			#{sjgtxbh},
			'cmjg',
			-1,
			-1,
			#{skwbz}
			)
	</select>
	
	<select id="getFoodBoxSetByCmid" resultType="java.lang.String">
		select foodboxset from ts_cmk where cmid = #{cmid} and iffoodbox &lt;&gt; 'Y' 
	</select>
	
	<select id="getTsCmk" resultType="com.tzx.miniapp.rest.model.TsCmk">
		select * from ts_cmk where cmid = #{cmid} limit 1
	</select>
	
	<select id="checkClmx" resultType="java.lang.String" parameterType="java.util.List">
<!-- 		select a.cmmc1 as cmmc from  -->
<!-- 		(select cl.xmid,cm.* from ts_cmk cm left join tq_clmxk cl on cl.clmxlb = 'CPYSMXLB_CPXM' and cl.xmid = cm.cmid) as a  -->
<!-- 		where xmid is null and a.cmid in  -->
		
		select a.cmmc1 as cmmc from (select b.xmid,cm.* from ts_cmk cm 
		left join (select * from tq_clmxk cl left join tq_clsdk cls on cls.id=cl.clid left join tq_cbsdk cbs on cbs.id=cls.cbid 
		where cl.clmxlb = 'CPYSMXLB_CPXM' and (cbs.yl1 = 'TS' or ((cbs.yl1 is null or cbs.yl1 = '') and cbs.cbmc1 = '堂食'))) as b on b.xmid = cm.cmid) as a 
		where xmid is null and a.cmid in
		<foreach item="cmid" index="index" collection="itemIdList" open="(" separator="," close=")">
			#{cmid}
		</foreach>
	</select>
	
	<select id="checkPrice" resultType="com.tzx.miniapp.rest.vo.Dish" parameterType="java.util.List">
		select cm.cmid as xmid, cm.cmmc1 as name, coalesce(tx.cmjg, cl.xmdj, cm.cmdj, 9999) as price
		from ts_cmk cm
		left join ts_ggcsk gg on gg.sdbt = 'FDJGBH'
		left join ts_psjgsdk jg on jg.jgbh = gg.sdnr
		left join ts_cmjgtxk tx on tx.jgtxbh = jg.jgtxbh and tx.cmid = cm.cmid 
		left join (select clo.* from tq_clmxk clo left join tq_clsdk cls on cls.id=clo.clid left join tq_cbsdk cbs on cbs.id=cls.cbid where clo.clmxlb = 'CPYSMXLB_CPXM' and (cbs.yl1 = 'TS' or ((cbs.yl1 is null or cbs.yl1 = '') and cbs.cbmc1 = '堂食'))) cl on cl.xmid = cm.cmid 
		where cm.cmid in 
		<foreach item="cmid" index="index" collection="itemIdList" open="(" separator="," close=")">
			#{cmid}
		</foreach>
	</select>
	
	<select id="getSendAmount" resultType="com.tzx.miniapp.rest.model.TsCmk">
		select * from ts_cmk where cmmc1 like '%${cmmc1}%' limit 1
	</select>
	
	<insert id="insertBtSaleoutdish" parameterType="java.util.List">
		insert into bt_saleoutdish_type 
		(yddh, xmid, xmbh, xmmc, xmsl, create_time, restrict_time, step)
		values
		<foreach collection="list" item="saleout" index="index" separator=",">
			(#{saleout.yddh}, #{saleout.xmid}, #{saleout.xmbh}, #{saleout.xmmc},
			#{saleout.xmsl}, #{saleout.create_time}, #{saleout.restrict_time}, 
			#{saleout.step})
		</foreach>
	</insert>
	
	<update id="updateSaleoutStep">
	    update bt_saleoutdish_type set step = #{step} where yddh = #{yddh} 
	</update>
	
	<delete id="delBtSaleoutdish">
		delete from bt_saleoutdish_type where yddh = #{yddh}
	</delete>
	
	<select id="getBtSaleoutdish" resultType="com.tzx.commapi.rest.vo.ReqParam">
		select xmbh as item_id, xmsl as count from bt_saleoutdish_type where step = 1 and #{nowDate} &gt; restrict_time
	</select>
	
	<update id="updateAddSaleOut">
	    update bt_saleoutdish_type set step = #{step} where step = 1 and #{nowDate} &gt; restrict_time
	</update>
	
	<delete id="clearYddPayActiveByType">
		delete from bt_yddpayactive where yddh = #{yddh} and active_type = 'MEMBER'
	</delete>
	
	<select id="getAddDish" resultType="com.tzx.miniapp.rest.model.TsCmk">
		select cm.* from ts_kwsdk kw left join ts_cmk cm on kw.itemid = cm.cmid where kw.id = #{kwid}
	</select>
	
	<select id="getIdByCode" resultType="java.lang.Integer">
		select cmid from ts_cmk where cmbh = #{dishsno}
	</select>
	
	<select id="getFoodBoxInfo" resultType="com.tzx.miniapp.rest.model.TsCmk">
		select * from ts_cmk where cmmc1 like '%${cmmc}%' limit 1
	</select>
	
	<select id="getDishByCode" resultType="com.tzx.miniapp.rest.model.TsCmk">
		select * from ts_cmk where cmbh = #{cmbh} limit 1
	</select>

	<select id="getDishByName" resultType="com.tzx.miniapp.rest.model.TsCmk">
		select * from ts_cmk where cmmc1 = #{cmmc} limit 1
	</select>

	<select id="getDishById" resultType="com.tzx.miniapp.rest.model.TsCmk">
		select * from ts_cmk where cmid = #{cmid} limit 1
	</select>

	<select id="getDishByIngredientId" resultType="com.tzx.miniapp.rest.model.TsCmk">
		select c.* from ts_ingredient_detail_info t LEFT JOIN ts_cmk c on t.item_id=c.cmid  where t.id=#{ingredientId} LIMIT 1;
	</select>

	<select id="getAllDishes" resultType="com.tzx.miniapp.rest.model.TsCmk">
		select * from ts_cmk;
	</select>

	<select id="getDishJgtxByCode" resultType="com.tzx.miniapp.rest.model.TsCmk">
		SELECT
			COALESCE ( tx.cmjg, cm.cmdj ) cmdj,
			cm.*
		FROM
			ts_cmk cm
				LEFT JOIN ts_ggcsk gg ON sdbt = 'FDJGBH'
				LEFT JOIN ts_psjgsdk jg ON jg.jgbh = gg.sdnr
				LEFT JOIN ts_cmjgtxk tx ON tx.jgtxbh = jg.jgtxbh
				AND tx.cmid = cm.cmid
		where cm.cmbh = #{cmbh} limit 1
	</select>
	
</mapper>
