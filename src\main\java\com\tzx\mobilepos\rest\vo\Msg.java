package com.tzx.mobilepos.rest.vo;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import java.util.List;

/**
* @ClassName: Msg
* <AUTHOR>
* @date 2018-11-9
* @email <EMAIL>
* @Description: 美团验券返回
*/

public class Msg {


	private String SUBSYSTEM;
	
	private String ANSWER;
	private String ACTION;

	private List<Data> DATAS;
	
	public String getSUBSYSTEM() {
		return SUBSYSTEM;
	}

	public void setSUBSYSTEM(String sUBSYSTEM) {
		SUBSYSTEM = sUBSYSTEM;
	}

	public String getANSWER() {
		return ANSWER;
	}

	public void setANSWER(String aNSWER) {
		ANSWER = aNSWER;
	}

	public String getACTION() {
		return ACTION;
	}

	public void setACTION(String aCTION) {
		ACTION = aCTION;
	}

	@XmlElementWrapper(name = "DAT<PERSON>")  
    @XmlElement(name = "<PERSON>AT<PERSON>") 
	public List<Data> getDATAS() {
		return DATAS;
	}

	public void setDATAS(List<Data> dATAS) {
		DATAS = dATAS;
	}

}
