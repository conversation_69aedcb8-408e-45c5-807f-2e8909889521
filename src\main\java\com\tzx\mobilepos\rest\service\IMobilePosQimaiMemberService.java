package com.tzx.mobilepos.rest.service;

import com.tzx.mobilepos.common.Data;

public interface IMobilePosQimaiMemberService {
	/**
	 * 根据会员动态二维码/手机号  信息查询微生活会员信息
	 * 
	 * @param param
	 * @param result
	 */
	public void findQimaiMember(Data param, Data result);
	
	/**
	 * 根据账单号清楚该账单关联的会员信息
	 * @param param
	 * @param result
	 */
	public void refundQimaiMember(Data param, Data result);
	
	/**
	 * 根据账单号查询缓存中的企迈会员信息
	 * 
	 * @param param
	 * @param result
	 */
	public void cacheQimaiMember(Data param, Data result);
	
	/**
	 * 企迈会员，预消费
	 * 
	 * @param param
	 * @param result
	 */
	public void qimaiPreview(Data param, Data result);
	
	/**
	 * 企迈会员，提交消费
	 * 
	 * @param param
	 * @param result
	 */
	public void qimaiCommit(Data param, Data result);
	
}
