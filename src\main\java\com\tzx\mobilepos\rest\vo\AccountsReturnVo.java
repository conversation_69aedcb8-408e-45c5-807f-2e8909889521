package com.tzx.mobilepos.rest.vo;

import java.util.List;

public class AccountsReturnVo {
	private String kdzdbh;// 账单编号
	private String cope_with_money;// 应付金额
	private String actual_money;// 实结金额
	private String discount_money;// 优惠金额
	private String payment_money;// 付款金额
	private String change_money;// 找零金额
	public List<ItemVo> items;// 菜品列表
	public List<ItemVo> discounts;// 优惠列表
	private String third_part_offer;// 第三方优惠
	private String shop_name;// 门店名称
	private String jzsj;// 结账时间
	private String qch;// 取餐号/桌位号
	private String qch_title;// 取餐号/桌位号 标题
	private String xmxjje;// 小计金额
	private String invoice;// 电子发票链接字符串
	public List<PaymentRunningWater> payments;// 支付列表

	public String getKdzdbh() {
		return kdzdbh;
	}

	public void setKdzdbh(String kdzdbh) {
		this.kdzdbh = kdzdbh;
	}

	public String getCope_with_money() {
		return cope_with_money;
	}

	public void setCope_with_money(String cope_with_money) {
		this.cope_with_money = cope_with_money;
	}

	public String getActual_money() {
		return actual_money;
	}

	public void setActual_money(String actual_money) {
		this.actual_money = actual_money;
	}

	public String getDiscount_money() {
		return discount_money;
	}

	public void setDiscount_money(String discount_money) {
		this.discount_money = discount_money;
	}

	public List<ItemVo> getItems() {
		return items;
	}

	public void setItems(List<ItemVo> items) {
		this.items = items;
	}

	public List<ItemVo> getDiscounts() {
		return discounts;
	}

	public void setDiscounts(List<ItemVo> discounts) {
		this.discounts = discounts;
	}

	public String getPayment_money() {
		return payment_money;
	}

	public void setPayment_money(String payment_money) {
		this.payment_money = payment_money;
	}

	public String getChange_money() {
		return change_money;
	}

	public void setChange_money(String change_money) {
		this.change_money = change_money;
	}

	public String getThird_part_offer() {
		return third_part_offer;
	}

	public void setThird_part_offer(String third_part_offer) {
		this.third_part_offer = third_part_offer;
	}

	public String getShop_name() {
		return shop_name;
	}

	public void setShop_name(String shop_name) {
		this.shop_name = shop_name;
	}

	public String getJzsj() {
		return jzsj;
	}

	public void setJzsj(String jzsj) {
		this.jzsj = jzsj;
	}

	public String getQch() {
		return qch;
	}

	public void setQch(String qch) {
		this.qch = qch;
	}

	public String getQch_title() {
		return qch_title;
	}

	public void setQch_title(String qch_title) {
		this.qch_title = qch_title;
	}

	public String getXmxjje() {
		return xmxjje;
	}

	public void setXmxjje(String xmxjje) {
		this.xmxjje = xmxjje;
	}

	public List<PaymentRunningWater> getPayments() {
		return payments;
	}

	public void setPayments(List<PaymentRunningWater> payments) {
		this.payments = payments;
	}

	public String getInvoice() {
		return invoice;
	}

	public void setInvoice(String invoice) {
		this.invoice = invoice;
	}
 

}
