package com.tzx.miniapp.rest.vo;

import javax.persistence.Entity;
import java.io.Serializable;

@Entity
public class BtActivity implements Serializable {
	private String yddh;
	private int dcxh;
	private int active_id;
	private int active_type;
	private String active_current_name;
	private String active_name;
	private int ydxmid;
	private int buy_ydxmid;
	private String buy;
	private String gift;
	private double coupon_money;
	
	public String getActive_current_name() {
		return active_current_name;
	}

	public void setActive_current_name(String active_current_name) {
		this.active_current_name = active_current_name;
	}

	public int getActive_type() {
		return active_type;
	}

	public void setActive_type(int active_type) {
		this.active_type = active_type;
	}

	public int getBuy_ydxmid() {
		return buy_ydxmid;
	}

	public void setBuy_ydxmid(int buy_ydxmid) {
		this.buy_ydxmid = buy_ydxmid;
	}

	public String getYddh() {
		return yddh;
	}

	public void setYddh(String yddh) {
		this.yddh = yddh;
	}

	public int getDcxh() {
		return dcxh;
	}

	public void setDcxh(int dcxh) {
		this.dcxh = dcxh;
	}

	public int getActive_id() {
		return active_id;
	}

	public void setActive_id(int active_id) {
		this.active_id = active_id;
	}

	public String getActive_name() {
		return active_name;
	}

	public void setActive_name(String active_name) {
		this.active_name = active_name;
	}

	public int getYdxmid() {
		return ydxmid;
	}

	public void setYdxmid(int ydxmid) {
		this.ydxmid = ydxmid;
	}

	public String getBuy() {
		return buy;
	}

	public void setBuy(String buy) {
		this.buy = buy;
	}

	public String getGift() {
		return gift;
	}

	public void setGift(String gift) {
		this.gift = gift;
	}

	public double getCoupon_money() {
		return coupon_money;
	}

	public void setCoupon_money(double coupon_money) {
		this.coupon_money = coupon_money;
	}

}
