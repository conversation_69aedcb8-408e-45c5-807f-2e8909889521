package com.tzx.receiver.common.msg.datatype;

import com.tzx.receiver.common.msg.datatype.DataTypeUnit.DateTimeSubType;


/**
 * 日期时间数据类型的类型属性类。
 * 
 * <AUTHOR>
 * 
 */
public class DateTimeAttribute extends DataTypeAttribute {

	private DateTimeSubType subType;

	/**
	 * 获取子类型。
	 * 
	 * @return 子类型的值。
	 */
	public DateTimeSubType getSubType() {
		return subType;
	}

	/**
	 * 设置子类型。
	 * 
	 * @param subType
	 *            子类型的值。
	 */
	public void setSubType(DateTimeSubType subType) {
		this.subType = subType;
	}

	public String toString() {
		return "DateTimeAttribute(subType=" + subType + ")";
	}

}
