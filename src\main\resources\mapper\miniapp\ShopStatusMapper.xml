<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.miniapp.rest.mapper.MiniAppShopStatusMapper">

	<!-- 查询报表日期 -->
	<select id="findBbrq" resultType="Map">
		SELECT nr as bbrq FROM TS_BMKZK
		WHERE ZDMC = 'BBRQ'
	</select>

	<!-- 查询开始营业标记 -->
	<select id="checkOpenStart" resultType="java.lang.Integer">
		select count(id) from
		tq_jtztk where bbrq = #{bbrq} and cznr = 'KSSY' and jhid = '99'
	</select>

	<!-- 查询打烊标记 -->
	<select id="checkOpenEnd" resultType="java.lang.Integer">
		select count(id) from
		tq_jtztk where bbrq = #{bbrq} and cznr = 'JSSY' and jhid = '99'
	</select>

	<!-- 查询登录次数 -->
	<select id="loginDlcs" resultType="java.lang.Integer">
		select count(id) from tq_jtztk
		where BBRQ = #{bbrq} and jhid = '99' and cznr = 'YYDL'
	</select>

	<!-- 查询交班次数 -->
	<select id="loginTccs" resultType="java.lang.Integer">
		select count(id) from tq_jtztk
		where BBRQ = #{bbrq} and jhid = '99' and cznr = 'YYTC'
	</select>

	<!-- 查询是否二十四小时营业标志 -->
	<select id="getSys24yy" resultType="java.lang.String">
		select sdnr from ts_ggcsk
		where sdbt = 'SYS24YY'
	</select>

	<!-- 最晚打烊时间 -->
	<select id="getDyxzsjd" resultType="java.lang.String">
		select sdnr from ts_ggcsk
		where sdbt = 'DYXZSJD'
	</select>
	
	<select id="checkShopOpenStart" resultType="com.tzx.miniapp.rest.model.TqJtztk">
		select * from tq_jtztk where bbrq = #{bbrq} and cznr in ('KSSY', 'JSSY') and jhid = '99' order by id desc limit 1
	</select>
	
	<select id="checkShopClassesStart" resultType="com.tzx.miniapp.rest.model.TqJtztk">
		select * from tq_jtztk where bbrq = #{bbrq} and cznr in ('YYDL', 'YYTC') and jhid = '99' order by id desc limit 1
	</select>

</mapper>
