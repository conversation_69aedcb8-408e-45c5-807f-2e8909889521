package com.tzx.receiver.entity.msg;

import java.io.Serializable;
import java.util.Date;

public class <PERSON>c<PERSON>tons
  implements Serializable
{
  private Integer id;
  private Integer cpysid;
  private String funbh;
  private String funname1;
  private String funname2;
  private String butcolor;
  private String sfxs;
  private Integer xssx;
  private String funcid;
  private String offpage;
  private Date ksrq;
  private Date jsrq;
  private String kssj;
  private String jssj;
  private String memo;
  private String ylzd1;
  private String ylzd2;
  private String ylzd3;
  private Integer jgxh;
  private Integer yl4;
  private Double yl5;
  private Integer cbid;

  public Integer getId()
  {
    return this.id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public Integer getCpysid() {
    return this.cpysid;
  }

  public void setCpysid(Integer cpysid) {
    this.cpysid = cpysid;
  }

  public String getFunbh() {
    return this.funbh;
  }

  public void setFunbh(String funbh) {
    this.funbh = funbh;
  }

  public String getFunname1() {
    return this.funname1;
  }

  public void setFunname1(String funname1) {
    this.funname1 = funname1;
  }

  public String getFunname2() {
    return this.funname2;
  }

  public void setFunname2(String funname2) {
    this.funname2 = funname2;
  }

  public String getButcolor() {
    return this.butcolor;
  }

  public void setButcolor(String butcolor) {
    this.butcolor = butcolor;
  }

  public String getSfxs() {
    return this.sfxs;
  }

  public void setSfxs(String sfxs) {
    this.sfxs = sfxs;
  }

  public Integer getXssx() {
    return this.xssx;
  }

  public void setXssx(Integer xssx) {
    this.xssx = xssx;
  }

  public String getFuncid() {
    return this.funcid;
  }

  public void setFuncid(String funcid) {
    this.funcid = funcid;
  }

  public String getOffpage() {
    return this.offpage;
  }

  public void setOffpage(String offpage) {
    this.offpage = offpage;
  }

  public Date getKsrq() {
    return this.ksrq;
  }

  public void setKsrq(Date ksrq) {
    this.ksrq = ksrq;
  }

  public Date getJsrq() {
    return this.jsrq;
  }

  public void setJsrq(Date jsrq) {
    this.jsrq = jsrq;
  }

  public String getKssj() {
    return this.kssj;
  }

  public void setKssj(String kssj) {
    this.kssj = kssj;
  }

  public String getJssj() {
    return this.jssj;
  }

  public void setJssj(String jssj) {
    this.jssj = jssj;
  }

  public String getMemo() {
    return this.memo;
  }

  public void setMemo(String memo) {
    this.memo = memo;
  }

  public String getYlzd1() {
    return this.ylzd1;
  }

  public void setYlzd1(String ylzd1) {
    this.ylzd1 = ylzd1;
  }

  public String getYlzd2() {
    return this.ylzd2;
  }

  public void setYlzd2(String ylzd2) {
    this.ylzd2 = ylzd2;
  }

  public String getYlzd3() {
    return this.ylzd3;
  }

  public void setYlzd3(String ylzd3) {
    this.ylzd3 = ylzd3;
  }

  public Integer getJgxh() {
    return this.jgxh;
  }

  public void setJgxh(Integer jgxh) {
    this.jgxh = jgxh;
  }

  public Integer getYl4() {
    return this.yl4;
  }

  public void setYl4(Integer yl4) {
    this.yl4 = yl4;
  }

  public Double getYl5() {
    return this.yl5;
  }

  public void setYl5(Double yl5) {
    this.yl5 = yl5;
  }

  public Integer getCbid() {
    return this.cbid; }

  public void setCbid(Integer cbid) {
    this.cbid = cbid;
  }
}