package com.tzx.mobilepos.rest.vo;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "DATAPACKET")
public class DATAPACKET implements Serializable{
	private static final long serialVersionUID = 3598232984470114660L;
	private Head HEAD;
	private Msg MSG;
	public DATAPACKET() {
		super();
	}
	public DATAPACKET(Head hEAD, Msg mSG) {
		super();
		HEAD = hEAD;
		MSG = mSG;
	}
	public Head getHEAD() {
		return HEAD;
	}
	public void setHEAD(Head hEAD) {
		HEAD = hEAD;
	}
	public Msg getMSG() {
		return MSG;
	}
	public void setMSG(Msg mSG) {
		MSG = mSG;
	}
	
	
}
