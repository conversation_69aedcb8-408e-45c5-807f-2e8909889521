package com.tzx.mobilepos.rest.model;

import com.tzx.publics.base.BaseEntity;

import javax.persistence.Table;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2018-05-21
 */
@Table(name = "TS_KWSDK")
public class TsKwsdk extends BaseEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	private int id;
	private String kwbh;
	private String kwnr1;
	private String kwnr2;
	private double zfje;
	private String jsfs;
	private int dyfaid;
	private String bzfl;
	private String pydm;
	private String wbdm;
	private String yl1;
	private String yl2;
	private String yl3;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getKwbh() {
		return kwbh;
	}

	public void setKwbh(String kwbh) {
		this.kwbh = kwbh;
	}

	public String getKwnr1() {
		return kwnr1;
	}

	public void setKwnr1(String kwnr1) {
		this.kwnr1 = kwnr1;
	}

	public String getKwnr2() {
		return kwnr2;
	}

	public void setKwnr2(String kwnr2) {
		this.kwnr2 = kwnr2;
	}

	public double getZfje() {
		return zfje;
	}

	public void setZfje(double zfje) {
		this.zfje = zfje;
	}

	public String getJsfs() {
		return jsfs;
	}

	public void setJsfs(String jsfs) {
		this.jsfs = jsfs;
	}

	public int getDyfaid() {
		return dyfaid;
	}

	public void setDyfaid(int dyfaid) {
		this.dyfaid = dyfaid;
	}

	public String getBzfl() {
		return bzfl;
	}

	public void setBzfl(String bzfl) {
		this.bzfl = bzfl;
	}

	public String getPydm() {
		return pydm;
	}

	public void setPydm(String pydm) {
		this.pydm = pydm;
	}

	public String getWbdm() {
		return wbdm;
	}

	public void setWbdm(String wbdm) {
		this.wbdm = wbdm;
	}

	public String getYl1() {
		return yl1;
	}

	public void setYl1(String yl1) {
		this.yl1 = yl1;
	}

	public String getYl2() {
		return yl2;
	}

	public void setYl2(String yl2) {
		this.yl2 = yl2;
	}

	public String getYl3() {
		return yl3;
	}

	public void setYl3(String yl3) {
		this.yl3 = yl3;
	}

}
