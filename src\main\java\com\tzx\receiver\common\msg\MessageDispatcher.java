package com.tzx.receiver.common.msg;

import com.tzx.TzxApplication;
import com.tzx.publics.util.StringUtil;
import com.tzx.receiver.common.upload.UploadGloVar;
import com.tzx.receiver.common.utils.DBUtils;
import com.tzx.receiver.common.utils.SpringContextHolder;
import org.apache.activemq.ActiveMQConnectionFactory;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.jms.*;
import java.util.ResourceBundle;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;

public class MessageDispatcher extends Thread {
//	DBUtils dbUtils = new DBUtils();
    private boolean jmcConnected = false;
	protected Logger	logger	= LoggerFactory.getLogger(getClass());
	
	private volatile boolean isRun = true;
	
	private int tryCount = 3;
	
	private ConnectionFactory jmsConnectionFactory = null;
	
	ThreadPoolExecutor threadPool = null;
	
	private ReentrantLock lock = new ReentrantLock();
	
	private ResourceBundle tzxresource = null;
	
	private volatile long timeTag = 0l;
	
	private static MessageDispatcher Dispatcher = null;
	
	static{
		Dispatcher = new MessageDispatcher();
		Dispatcher.init();
	}

	private static final int maxLength = 1<<21; //最大内存占用量6M

	private MessageDispatcher(){
		super("MessageListener");
	}
	
	public static void listenStart(){
		Dispatcher.start();
	}
	
	public static void listenStop(){
		Dispatcher.close();
	}
	
	public static boolean checkActive(){
		return Dispatcher.isActive();
	}
	
	public void init(){
		lock.lock();
		try{
			loadResource();
			createThreadPool();
//			refreshJmsConnFactory();
		}catch(Exception ex) {
			ex.printStackTrace();
		}finally{
			lock.unlock();
		}
	}
	
	public  boolean isActive(){
		boolean ret = false;
		long befor = timeTag;
		waitForWorker(33);
		long after = timeTag;
		ret = (after > befor) && isRun;
		return ret;
	}
	
	public void close(){
		isRun = false;
		threadPool.shutdown();
	}
	
	private void createThreadPool(){
		threadPool =  new ThreadPoolExecutor(3, 3, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<Runnable>());
		threadPool.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy()); // 线程池的拒绝策略 抛出异常
	}
	
	private void refreshJmsConnFactory(){
	    //这里先不直接运行，而是等到容器里的jdbc组件注册完成后再运行
//		int chkCnt = 0;
//        while (true){DBUtils.getJdbcTemplate()!=null
//            try {
//                Thread.sleep(100);
//                if(++chkCnt>600);
//                if(DBUtils.getJdbcTemplate()!=null){
//                    break;
//                }
//
//            } catch (InterruptedException e) {
//                break;
//            }
//
//        }
		String mqUser = UploadGloVar.getMqUser();
		String mqPassword = UploadGloVar.getMqPassword();
		String mqUrl = UploadGloVar.getMqUrl();
		this.jmsConnectionFactory = new ActiveMQConnectionFactory(mqUser, mqPassword, mqUrl);
        jmcConnected = true;
	}
	
	private void loadResource(){
		ResourceBundle rb = ResourceBundle.getBundle("posmsgtype");
		String pname = rb.getString("tzx.type.pos");
		ResourceManager.init(ResourceManager.class.getPackage().toString()
				.substring(8)+ "." + pname/*"msgresource_xqjc"*/);
	}
	
	private Connection getJmsConnection(){
		int count = tryCount;
		Connection conn = null;
		if (!jmcConnected){
		    refreshJmsConnFactory();
        }
		while (count>0){
			try{
				conn = this.jmsConnectionFactory.createConnection();
				break;
			}catch(Exception ex) {
				ex.printStackTrace();
				if(count==1){
					refreshJmsConnFactory();
				}
				logger.info("获取jmsConnection 失败 count ： "+count);
				count = count -1;
			}
		}
		return conn;
	}
	
	private void waitForWorker(int s){
		try{
			Thread.sleep(s*1000);
		}catch(InterruptedException ex){
			ex.printStackTrace();
			logger.error("System Error", ex);
		}
	} 
	
	private void dispatche(String data){
		int count = tryCount;
		boolean isTry = true;
		MessageHandler handler = new MessageHandler(data);
		while( isTry && count>0){
			isTry = false;
			try{
				threadPool.execute(handler);
				break;
			}catch(RejectedExecutionException e){
				e.printStackTrace();
				isTry = true;
				count--;
				if(count>0){
					waitForWorker(1);
				}
			}
		}
		if(isTry){
			//System.out.println("线程池派发任务失败 任务内容："+data);
			logger.info("线程池派发任务失败 任务内容："+data);
		}
	}
	
	private void clearExecutionContext(MessageConsumer consumer
		,QueueSession queueSession
		,Connection mqConn){
		try{
			if(consumer!=null){
				consumer.close();
			}
			if(queueSession!=null){
				queueSession.close();
			}
			if(mqConn!=null){
				mqConn.close();
			}
		}catch(Exception ex) {
			ex.printStackTrace();
			//System.out.println("clearExecutionContext 释放资源出现异常");
			logger.error("clearExecutionContext 释放资源出现异常", ex);
		}
	}
	String queueName = null;
	public void run() {
		logger.info("消息监听开始！");
		//延迟一下启动，否则有可能线程比容器初始化快
		while (!TzxApplication.loadFinish){
			try {
				Thread.sleep(100);
			} catch (InterruptedException e) {

			}
		}
		logger.info("消息监听线程启动成功！");
//		Thread.sleep();

		Connection mqConn = null;
		QueueSession queueSession = null;
		Destination dstination = null;
		MessageConsumer consumer = null;
		Message message = null;
		while (isRun){
			timeTag = System.currentTimeMillis();
			System.out.println("timeTag -> "+timeTag);
			try{
				mqConn = getJmsConnection();
				mqConn.start();
				if (StringUtils.isBlank(queueName)){
					queueName = UploadGloVar.getDownName();
				}
				queueSession = (QueueSession) mqConn.createSession(false,Session.AUTO_ACKNOWLEDGE);
				dstination = queueSession.createQueue(queueName);
				consumer = queueSession.createConsumer(dstination);
				message = consumer.receive();
				while(message!=null){
					logger.info("收到消息");
					timeTag = System.currentTimeMillis();

					TextMessage textMsg = (TextMessage) message;
					String msgText = textMsg.getText();

					String[] parts = StringUtil.splitString(msgText, maxLength);
					for (String part : parts) {
						logger.info("收到消息，内容为：" + part);
					}

					if (msgText != null && !"".equals(msgText)) {
						dispatche(msgText);
					}
					message = consumer.receive();
				}
				Thread.sleep(5000);
				/*
				if (message == null) {
					Thread.sleep(3000);
				} else {
					TextMessage textMsg = (TextMessage) message;
					String msgText = textMsg.getText();
					if (msgText != null && !"".equals(msgText)) {
						dispatche(msgText);
					}
				}
				*/
			//}catch(Throwable ex) {
			}catch(Exception ex) {
				ex.printStackTrace();
				logger.error("System Error", ex);
			} finally {
				clearExecutionContext(consumer,queueSession,mqConn);
				consumer = null;
				queueSession = null;
				mqConn = null;
			}
		}
		logger.info("消息监听结束！");
	}
}
