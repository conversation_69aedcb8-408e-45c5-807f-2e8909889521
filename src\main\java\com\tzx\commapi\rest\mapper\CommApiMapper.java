package com.tzx.commapi.rest.mapper;

import com.tzx.mobilepos.rest.model.TqJtztk;
import com.tzx.mobilepos.rest.model.TqZdk;
import com.tzx.commapi.rest.vo.*;
import com.tzx.mobilepos.rest.model.TsBck;
import com.tzx.publics.base.MyMapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019-10-26.
 */
public interface CommApiMapper extends MyMapper<ReqParam> {
    public int insertSetGq(@Param("reqParam")ReqParam reqParam);
    public int updateSetGq(@Param("reqParam")ReqParam reqParam);
    public int deleteSetGq(@Param("reqParam")ReqParam reqParam);
    public GqkVo getGQDish(@Param("reqParam")ReqParam reqParam);
    public List<GqkVo> getGQDishs(List<String> whereStrs);

    public int addGqs(List<GqkVo > gqkVos);
    public int decGqs(List<GqkVo > gqkVos);

    public int addGq(@Param("reqParam")ReqParam reqParam);
    public int decGq(@Param("reqParam")ReqParam reqParam);
    public int updateFklsByJzid(@Param("kdzdbh")String kdzdbh,@Param("jzid")Integer jzid,@Param("yhje")BigDecimal yhje);
    public int updateFklsByFklsid(@Param("kdzdbh")String kdzdbh,@Param("fklsid")Integer fklsid);
    public int deleteYhfsParam(@Param("yhfsParam")YhfsParam yhfsParam);
    public int saveYhfsParam(@Param("yhfsParam")YhfsParam yhfsParam);
    public int UpdateDsljByZdbh(@Param("kdzdbh")String kdzdbh);
    public int DeleteYhMTcouponsTempItems(@Param("yhmtCouponsTemp")YHMTCouponsTemp yhmtCouponsTemp);
    public int SaveYhMTcouponsTempItems(@Param("yhmtCouponsTemp")YHMTCouponsTemp yhmtCouponsTemp);
    public int AddThirdYhfs(@Param("kdzdbh")String kdzdbh,@Param("itemid")Integer itemid,
                            @Param("yhfsid")Integer yhfsid,@Param("code")String code,@Param("skjh")String skjh
                            ,@Param("jgtxbh")String jgtxbh);
    public int ExecBillDiscountShare(@Param("kdzdbh")String kdzdbh);
    public CalcMoneyRt CalcMoney(@Param("kdzdbh")String kdzdbh);

    public int AddTqThirdTempOrder(@Param("param")TqThirdTempOrder param);
    public int UpdateTqThirdTempOrder(@Param("param")TqThirdTempOrder param);
    public List<TqThirdTempOrder> getTqThirdTempOrder(@Param("dataType")String dataType);
    public List<TqThirdTempOrder> getTqThirdTempOrderByBillNum(@Param("dataType")String dataType,@Param("billNum")String billNum);


    public List<Vq_JPWY_Zdk> getVqJpwyZdk(@Param("billNum")String billNum,@Param("reportDate")Date reportDate);

    public int updateJtztkToToken(@Param("jtbh") String jtbh, @Param("token") String token, @Param("updateTime") String updateTime);

    public List<Map<String, Object>> getFXZdmxList(@Param("billNum")String billNum, @Param("reportDate")Date reportDate);

	public TqZdk getVqZdk(@Param("kdzdbh") String kdzdbh, @Param("bbrq") Date bbrq);

	public List<Map<String, Object>> getKCZdmxList(@Param("billNum") String billNum, @Param("reportDate") Date reportDate, @Param("itemCode") String itemCode, @Param("itemOrgid") String itemOrgid);

	public List<Map<String, Object>> getKCFklsList(@Param("billNum") String billNum);
	
	public List<Map<String, Object>> getFXFklsList(@Param("billNum") String billNum);
	
	public List<TqThirdTempOrder> getTqThirdTempOrderByInType(@Param("dataTypes") List<String> dataTypes);
	
	public List<Map<String, Object>> getJLZdmxList(@Param("billNum") String billNum, @Param("reportDate") Date reportDate);
	
	public List<TqZdk> getWy2VqZdkList(@Param("bbrq") Date bbrq);
	
	public int updateZdkToWyscbj(@Param("kdzdbh") String kdzdbh, @Param("wyscbj") int wyscbj);
	
	public List<Map<String, Object>> getCYZdmxList(@Param("billNum") String billNum, @Param("reportDate") Date reportDate);

    public DayEndSummarizing getOrderSummarizing(@Param("bbrq") Date bbrq);

    public List<Map<String, String>> getTqXfMsgLog(@Param("pdmm") String pdmm, @Param("status") String status);

    public int updateTqXfMsgLog(@Param("versionstime") String versionstime,@Param("status") String status);

    public List<TqThirdTempOrderNew> getWyList(@Param("billnum") String billnum, @Param("reportdate") String reportdate, @Param("orderno") String orderno, @Param("status") int status);

    public int updateTqThirdTempOrderStatus(@Param("id") int id, @Param("billnum") String billnum);

    public int repetitionTTTO(@Param("id") int id, @Param("billnum") String billnum, @Param("reportdate") String reportdate, @Param("orderno") String orderno);

    public int delTTTO(@Param("id") int id, @Param("billnum") String billnum, @Param("reportdate") String reportdate, @Param("orderno") String orderno);

    public int updateTqThirdTempOrderStatusByBbrq(@Param("status") int status, @Param("billnum") String billnum, @Param("reportdate") String reportdate, @Param("orderno") String orderno);

    public int repetitionTTTOByBbrq(@Param("status") int status, @Param("billnum") String billnum, @Param("reportdate") String reportdate, @Param("orderno") String orderno);

    public int delTTTOByBbrq(@Param("status") int status, @Param("billnum") String billnum, @Param("reportdate") String reportdate, @Param("orderno") String orderno);

    public int updateLockCtrlGgcsN();

    public int updateLockCtrlGgcsXfN();

    public int updateLockCtrlGgcsY();

    public int updateLockCtrlGgcsXfY();

    public List<Map<String, Object>> getFklsBySfsrList(@Param("billNum") String billNum);

    public List<Vq_JPWY_Zdk> getVqJpwyByBbrq(@Param("reportDate") Date reportDate);

    public int insertZdkInDown(List<FdzdkInfo> info);

    public int insertWdkInDown(List<FdzdmxkInfo> info);

    public int insertFklslskInDown(List<FdzdfkfskInfo> info);

    public int updateBh(@Param("nr") String nr, @Param("bmc") String bmc, @Param("zdmc") String zdmc);

    public int nextvals(@Param("seq") String seq);

    public int setvals(@Param("seq") String seq, @Param("value") int value);

    public TqJtztk getCzsj();

    public int insertJtztk(TqJtztk info);

    public int insertCzzdkInDown(List<FdczzdkInfo> info);

    public int insertCzlslskInDown(List<FdczlslskInfo> info);

    public TqBbscjdk getBbscjdkByBbrq(@Param("bbrq") Date bbrq);

    public int setBbscjdkToCurprogres(TqBbscjdk info);

    public TsBck getBcid(Date date);

    public List<TqZdk> getWmZdkList(EcoOrderBackVo info);

    public List<Map<String, Object>> getFklsXyList(@Param("billNum") String billNum);

    public List<TqThirdTempOrder> getTqThirdTempOrderByTime(@Param("dataType")String dataType, @Param("dateType") String dateType);

    public int updateTqThirdTempOrderbatch(@Param("param") TqThirdTempOrder param, @Param("tttpIds") List<Long> tttpIds);

    List<Map<String, Object>> getVqFklsList(@Param("billNum") String billNum);

    List<Map<String, Object>> geVqZdmxList(@Param("billNum") String billNum, @Param("reportDate") Date reportDate);
    
}
