package com.tzx.miniapp.rest.service.impl;

import com.tzx.ecoserver.rest.mapper.EcoOrderMapper;
import com.tzx.ecoserver.rest.vo.EcoOrderdiscount;
import com.tzx.miniapp.common.Constant;
import com.tzx.miniapp.common.Data;
import com.tzx.miniapp.rest.entity.TqWdk;
import com.tzx.miniapp.rest.mapper.MiniAppFirstPayMapper;
import com.tzx.miniapp.rest.mapper.MiniAppOrderPrecheckMapper;
import com.tzx.miniapp.rest.mapper.MiniAppShopBaseInfoMapper;
import com.tzx.miniapp.rest.mapper.MiniAppShopStatusMapper;
import com.tzx.miniapp.rest.mapper.MiniAppTqZdkMapper;
import com.tzx.miniapp.rest.mapper.TqWdkMapper;
import com.tzx.miniapp.rest.model.TqJtztk;
import com.tzx.miniapp.rest.model.TqZdk;
import com.tzx.miniapp.rest.model.TsCmk;
import com.tzx.miniapp.rest.model.TsFkfssdk;
import com.tzx.miniapp.rest.model.TsGgcsk;
import com.tzx.miniapp.rest.model.TsYhfssdk;
import com.tzx.miniapp.rest.service.IMiniAppOmpService;
import com.tzx.miniapp.rest.vo.BtPayments;
import com.tzx.miniapp.rest.vo.BtYdd;
import com.tzx.miniapp.rest.vo.BtYdxm2;
import com.tzx.miniapp.rest.vo.DishVo;
import com.tzx.miniapp.rest.vo.Shops;
import com.tzx.mobilepos.rest.mapper.MobilePosAcewillCouponTempMapper;
import com.tzx.publics.common.BillNoData;
import com.tzx.publics.listener.InitDataListener;
import com.tzx.publics.util.ArithUtil;
import com.tzx.publics.util.BigDecimalUtil;
import com.tzx.publics.util.DateUtil;
import com.tzx.receiver.entity.msg.EcoTypeDic;
import net.sf.json.JSONArray;
import net.sf.json.JSONException;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class MiniAppOmpServiceImpl implements IMiniAppOmpService {

    private final static Logger LOGGER = LoggerFactory.getLogger(MiniAppOmpServiceImpl.class);

    @Autowired
    private MiniAppOrderPrecheckMapper orderPrecheckMapper;
    @Autowired
    private MiniAppFirstPayMapper firstPayMapper;
    @Autowired
    private MiniAppShopStatusMapper shopStatusMapper;
    @Autowired
    private MiniAppShopBaseInfoMapper shopBaseInfoMapper;
    @Autowired
    private MobilePosAcewillCouponTempMapper acewillCouponTempMapper;
    @Autowired
    private EcoOrderMapper ecoOrderMapper;
    @Autowired
    private MiniAppTqZdkMapper tqZdkMapper;
    @Autowired
    private TqWdkMapper tqWdkMapper;

    /**
     * 营销活动优惠(暂用微生活优惠yhsx=6的编号)
     */
    private final static String PROMOTION_YHFSBH = "80000000";
    private final static String CMSX_DP = "CMSX_DP";
    private final static String CMSX_TC = "CMSX_TC";
    private final static String CMSX_MX = "CMSX_MX";

    /**
     * 微生活优惠
     */
    private final static String WLIFE_YHSX_PROMOTION = "6";
    /**
     *  微生活会员卡赠送优惠
     */
    private final static String WLIFE_YHSX_MEMBER_GIVE = "63";
    /**
     * 会员储值赠送分摊
     */
    private final static String WLIFE_YHSX_MEMBER_GIVE_SHARE = "73";
    /**
     * 微生活积分抵现
     */
    private final static String WLIFE_YHSX_MEMBER_CREDIT = "67";

    /**
     * 三方优惠券
     */
    private final static List<String> THIRD_COUPON = Arrays.asList("meituan","douyin","alipaycoupon","littleRedBook");
    private final static List<String> INGORE_RULE_CATEGORY = Arrays.asList("0"/*会员菜品券*/,"20"/*会员菜品折扣券*/);
    private final static List<String> NOPAYINFO_CATEGORY = Arrays.asList("20"/*会员菜品折扣券*/);

    @Transactional
    public Data orderPrecheckCode(JSONObject orderData, BillNoData billNoData) {
        // 创建返回数据对象
        Data data = new Data();
        // 小程序订单号
        String outOrderId = orderData.optString("out_order_id");
        // 持久化使用第预订单号字段，为了快速区分，小程序增加了  TS 前缀
        String outOrderIdInDB = Constant.BILL_PREFIX + outOrderId;


        data.setYddbh(outOrderIdInDB);

        // 报表日期
        Map<String, String> bbrqMap = shopStatusMapper.findBbrq();
        // 生成默认报表日期
        String bbrq = DateUtil.getNowDateYYDDMM();
        if (null != bbrqMap && bbrqMap.size() != 0) {
            bbrq = bbrqMap.get("bbrq");
        }
        // 判断营业状态，因为推送异常账单时不判断营业状态，所以这里控制一下
        TqJtztk jtztk = shopStatusMapper.checkShopOpenStart(DateUtil.parseDate(bbrq));
        if (null == jtztk || "JSSY".equals(jtztk.getCznr())) {
            data.setSuccess(0);
            data.setMsg("门店已打烊，请开店后重试！");
            data.setData(new HashMap<String, Object>());
            return data;
        }

        TqZdk tqzdk = orderPrecheckMapper.getZdk(outOrderIdInDB);
        TqZdk tqzdlsk = orderPrecheckMapper.getZdlsk(outOrderIdInDB);

        if ((null != tqzdk && "ZDSX_YJ".equals(tqzdk.getJzsx())) || (null != tqzdlsk && "ZDSX_YJ".equals(tqzdlsk.getJzsx()))) {
            JSONObject joData = new JSONObject();
            joData.put("identify", outOrderId);
            if (null != tqzdk) {
                joData.put("meal_number", tqzdk.getQch());
            }
            if (null != tqzdlsk) {
                joData.put("meal_number", tqzdlsk.getQch());
            }
            data.setData(joData);
            data.setSuccess(1);
            data.setCode(2);
            data.setMsg("订单已同步");
            return data;
        }

        // 如果本预订单号是重复落单，直接清掉老数据
//        orderPrecheckMapper.clearYdd(outOrderIdInDB);
//        orderPrecheckMapper.clearYdxm1(outOrderIdInDB);
//        orderPrecheckMapper.clearYdxm2(outOrderIdInDB);
//        orderPrecheckMapper.clearYddTcSelectMx(outOrderIdInDB);
//        orderPrecheckMapper.clearYddPayments(outOrderIdInDB);
        clearOrder(outOrderIdInDB);

        JSONObject orderInfo = orderData.optJSONObject("order_info");
        JSONObject payInfo = orderData.optJSONObject("pay_info");
        JSONObject member = orderInfo.optJSONObject("member");
        // cardr_type 会员卡类型（1：众赏会员，2：百福会员）  ， pos数据库  cwlxbh 1：微生活，2：众赏，3：百福，4：企迈，5：冥晨

        // 默认失败
        data.setSuccess(0);
        // 保存平台预定账单编号
        data.setYddbh(outOrderId);
        try {
            String kdzdbh = billNoData.getKdzdbh();
            Map<String, String> rMap = createBill(orderInfo, kdzdbh, outOrderIdInDB, bbrq, billNoData);
            // 写入 与订单表 bt_ydd
            jointYdd(kdzdbh, orderInfo, outOrderIdInDB, rMap, member);
            //落单
            Map<String, String> orderingR = ordering(orderInfo, kdzdbh, outOrderIdInDB, rMap);
            if ("true".equals(orderingR.get("resultFlag"))) {
                boolean aoflag = firstPay(outOrderIdInDB, payInfo, kdzdbh, bbrq, rMap, orderInfo);
                if (aoflag) {
                    // 这里可以转账单了
                    LOGGER.info("select * from p_zwyydtozd_xcx('" + outOrderIdInDB + "','" + billNoData.getLsdh() + "','" +
                            kdzdbh + "','" + billNoData.getJzzdbh() + "','" + Integer.parseInt(rMap.get("bcid")) + "','" + DateUtil.parseDate(bbrq) + "','" + rMap.get("czybh") + "','99','" + Integer.parseInt(rMap.get("ygdlcs")) + "')");
                    int ytzR = firstPayMapper.yddToZdXcx(outOrderIdInDB, billNoData.getLsdh(), kdzdbh, billNoData.getJzzdbh(), Integer.parseInt(rMap.get("bcid")), DateUtil.parseDate(bbrq), rMap.get("czybh"), Integer.parseInt(rMap.get("ygdlcs")));

                    LOGGER.info("预订单转订单执行完成，返回代码：" + ytzR);
                    if (ytzR == 0) {

                        // 写 tq_acewil_dealdetails c
                        jointDealdetails(kdzdbh, member, bbrq, "99");
                        //重新分摊会员优惠
                        shareMemberPromotion(kdzdbh);
                        //重新分摊套餐明细
                        shareBillDetails(kdzdbh);
                        //更新账单库金额
                        acewillCouponTempMapper.updateTqZdkAfterShare(kdzdbh);
                        //校验实结金额和付款金额是否一致
                        Map<String,BigDecimal> resultMap=acewillCouponTempMapper.selectFkjeAndSjjeByKzdzbh(kdzdbh);
                        if(null!=resultMap&& !resultMap.isEmpty()){
                            BigDecimal fkje=resultMap.get("fkje");
                            BigDecimal sjje=resultMap.get("sjje");
                            if(fkje.compareTo(sjje)!=0){
                                LOGGER.error("实结金额和付款金额不一致，实结金额："+sjje+"，付款金额："+fkje);
                                clearOrder(outOrderIdInDB);
                                data.setSuccess(0);
                                data.setMsg("实结金额和付款金额不一致");
                                data.setData(new HashMap<String, Object>());
                                return data;
                            }
                        }

                        JSONObject joData = new JSONObject();
                        joData.put("identify", outOrderId);
                        joData.put("meal_number", rMap.get("qch"));
                        joData.put("ordermode",orderData.optString("ordermode"));
                        joData.put("pos_order_id", kdzdbh);
                        data.setData(joData);
                        data.setSuccess(1);
                        data.setMsg("付款成功");

                    } else if (ytzR == -100) {
                        LOGGER.info("预订单转订单失败， 预订单数据不存在,平台订单编号：" + outOrderIdInDB + "，错误代码：" + ytzR);
                        data.setSuccess(0);
                        data.setMsg("转订单失败， 预订单数据不存在，错误代码：" + ytzR);
                        data.setData(new HashMap<String, Object>());
                    } else if (ytzR == -200) {
                        LOGGER.info("预订单转订单失败,账单已存在：" + outOrderIdInDB + "，错误代码：" + ytzR);
                        data.setSuccess(0);
                        data.setMsg("转订单失败，账单已存在，错误代码：" + ytzR);
                        data.setData(new HashMap<String, Object>());
                    } else if (ytzR == -800) {
                        LOGGER.info("预订单转订单失败，收银已交班，请重新开班后再试,平台订单编号：" + outOrderIdInDB + "，错误代码：" + ytzR);
                        data.setSuccess(0);
                        data.setMsg("转订单失败， 收银已交班，请重新开班后再试，错误代码：" + ytzR);
                        data.setData(new HashMap<String, Object>());
                    } else {
                        LOGGER.info("预订单转订单失败,平台订单编号：" + outOrderIdInDB + "，错误代码：" + ytzR);
                        clearOrder(outOrderIdInDB);
                        data.setSuccess(0);
                        data.setMsg("转订单失败，错误代码：" + ytzR);
                        data.setData(new HashMap<String, Object>());
                    }
                } else {
                    clearOrder(outOrderIdInDB);
                    data.setSuccess(0);
                    data.setMsg("下单失败");
                    data.setData(new HashMap<String, Object>());
                }
            } else {
                clearOrder(outOrderIdInDB);
                data.setSuccess(0);
                data.setMsg("下单失败:" + orderingR.get("resultMsg"));
                data.setData(new HashMap<String, Object>());
            }
            return data;
        } catch (Exception e) {
            LOGGER.error("Ignore this exception", e);
            clearOrder(outOrderIdInDB);
            data.setSuccess(0);
            data.setMsg("系统错误:" + e);
            data.setData(new HashMap<String, Object>());
            return data;
        }
    }

    public void clearOrder(String yddh) {
        if(yddh.contains("debug")) {
            return;
        }
        tqZdkMapper.delWdkByYddh(yddh);
        tqZdkMapper.delFklslskByYddh(yddh);
        tqZdkMapper.delZdkByYddh(yddh);
        orderPrecheckMapper.clearYdd(yddh);
        orderPrecheckMapper.clearYdxm1(yddh);
        orderPrecheckMapper.clearYdxm2(yddh);
        orderPrecheckMapper.clearYddTcSelectMx(yddh);
        orderPrecheckMapper.clearYddPayments(yddh);
        ecoOrderMapper.clearEcoOrderdiscount(yddh);
    }

    public Map<String, String> ordering(JSONObject orderInfo, String kdzdbh, String outOrderIdInDB, Map<String, String> rMap) {
        String resultFlag = "true";
        String resultMsg = "落单成功；";
        Map<String, String> resultMap = new HashMap<String, String>();
        int result_ = 0;
        JSONArray normalitems = new JSONArray();
        if (orderInfo.has("normalitems")) {
            normalitems = orderInfo.getJSONArray("normalitems");
        }

        JSONArray setmeal = new JSONArray();
        if (orderInfo.has("setmeal")) {
            setmeal = orderInfo.getJSONArray("setmeal");
        }
        List<BtYdxm2> bymx = new ArrayList<BtYdxm2>();
        int yddcxh = 0;
        int tcdcxh = 0;
//        int fdnumber = 0;
//        double foodboxAmount = 0;

        //单品
        for (int i = 0; i < normalitems.size(); i++) {
            yddcxh = yddcxh + 1;
            JSONObject item = normalitems.getJSONObject(i);
//			Integer did = orderPrecheckMapper.getIdByCode(item.optString("dishsno"));
            TsCmk cmk = orderPrecheckMapper.getDishByCode(getItemCodeBySpeccode(item));
            if (null == cmk) {
                resultMsg = "《" + item.optString("name") + "》";
                result_ = -2;
            }
            if (result_ != 0) {
                break;
            }
            item.put("did", cmk.getCmid());
            item.put("dishsno", cmk.getCmbh());
            item.put("localName", cmk.getCmmc1());
            item.put("dishunit",cmk.getDwbh());
//			if (orderInfo.optInt("diningWay", 1) == 2 && 0 != item.optDouble("foodbox_amount", 0)) {
//				fdnumber = fdnumber + item.optInt("number");
//				double fbTemp = ArithUtil.mul(item.optDouble("foodbox_amount", 0), item.optInt("number"));
//				foodboxAmount = ArithUtil.add(foodboxAmount, fbTemp);
//			}

            BtYdxm2 ydxm2 = jointYdmx(item, outOrderIdInDB, "CMSX_DP", yddcxh, null, 0, 0, "", "");
            bymx.add(ydxm2);
        }

        //套餐
        for (int i = 0; i < setmeal.size(); i++) {
            if (result_ != 0) {
                break;
            }
            yddcxh = yddcxh + 1;
            tcdcxh = tcdcxh + 1;
            JSONObject item = setmeal.getJSONObject(i);
//            Integer did = orderPrecheckMapper.getIdByCode(item.optString("dishsno"));
            TsCmk cmk = orderPrecheckMapper.getDishByCode(getItemCodeBySpeccode(item));
            if (null == cmk) {
                result_ = -2;
                resultMsg = "《" + item.optString("name") + "》";
            }
            if (result_ != 0) {
                break;
            }
            item.put("did", cmk.getCmid());
            item.put("localName", cmk.getCmmc1());
            Integer tcid = cmk.getCmid();
            String tcbh = item.optString("dishsno");
            BtYdxm2 ydxm2 = jointYdmx(item, outOrderIdInDB, "CMSX_TC", yddcxh, null, tcid, tcdcxh, tcbh, "");

            bymx.add(ydxm2);

            // 主菜菜品
            JSONArray mainDish = new JSONArray();
            if (item.has("maindish")) {
                mainDish = item.optJSONArray("maindish");
                for (int k = 0; k < mainDish.size(); k++) {
                    JSONObject gro = mainDish.getJSONObject(k);
//                    Integer groid = orderPrecheckMapper.getIdByCode(gro.optString("dishsno"));
                    TsCmk groCmk = orderPrecheckMapper.getDishJgtxByCode(getItemCodeBySpeccode(gro));
                    if (null == groCmk) {
                        result_ = -2;
                        resultMsg = "《" + gro.optString("name") + "》";
                    }
                    if (result_ != 0) {
                        break;
                    }
                    gro.put("did", groCmk.getCmid());
                    gro.put("localName", groCmk.getCmmc1());
                    gro.put("dishunit",groCmk.getDwbh());
                    gro.put("price",groCmk.getCmdj());
                    
                    BtYdxm2 ydxm3 = jointYdmx(item, outOrderIdInDB, "CMSX_MX", yddcxh, gro, tcid, tcdcxh, tcbh, "ERP_MXLX_SINGLE");
                    bymx.add(ydxm3);
                }
            }

            // 辅助可选菜品
            JSONArray mandatory = new JSONArray();
            if (item.has("mandatory")) {
                mandatory = item.optJSONArray("mandatory");
                for (int k = 0; k < mandatory.size(); k++) {
                    JSONObject gro = mandatory.getJSONObject(k);
//                    Integer gdid = orderPrecheckMapper.getIdByCode(gro.optString("dishsno"));
                    TsCmk gdCmk = orderPrecheckMapper.getDishJgtxByCode(getItemCodeBySpeccode(gro));
                    if (null == gdCmk) {
                        result_ = -2;
                        resultMsg = "《" + gro.optString("name") + "》";
                    }
                    if (result_ != 0) {
                        break;
                    }
                    gro.put("did", gdCmk.getCmid());
                    gro.put("localName", gdCmk.getCmmc1());
                    gro.put("dishunit",gdCmk.getDwbh());
                    gro.put("price",gdCmk.getCmdj());

                    BtYdxm2 ydxm3 = jointYdmx(item, outOrderIdInDB, "CMSX_MX", yddcxh, gro, tcid, tcdcxh, tcbh, "ERP_MXLX_GROUP");
                    bymx.add(ydxm3);
                }
            }

            // 可选菜品
            JSONArray optional = new JSONArray();
            if (item.has("optional")) {
                mandatory = item.optJSONArray("optional");
                for (int k = 0; k < mandatory.size(); k++) {
                    JSONObject gro = mandatory.getJSONObject(k);
//                    Integer gdid = orderPrecheckMapper.getIdByCode(gro.optString("dishsno"));
                    TsCmk gdCmk = orderPrecheckMapper.getDishJgtxByCode(getItemCodeBySpeccode(gro));
                    if (null == gdCmk) {
                        result_ = -2;
                        resultMsg = "《" + gro.optString("name") + "》";
                    }
                    if (result_ != 0) {
                        break;
                    }
                    gro.put("did", gdCmk.getCmid());
                    gro.put("localName", gdCmk.getCmmc1());
                    gro.put("dishunit",gdCmk.getDwbh());
                    gro.put("price",gdCmk.getCmdj());

                    BtYdxm2 ydxm3 = jointYdmx(item, outOrderIdInDB, "CMSX_MX", yddcxh, gro, tcid, tcdcxh, tcbh, "ERP_MXLX_GROUP");
                    bymx.add(ydxm3);
                }
            }

        }

        // 保存菜品数据
        if (bymx.size() > 0) {
            orderPrecheckMapper.insertBtYdxm2(bymx);
        }

        if (result_ == 0 || result_ == 1) {
            resultFlag = "true";
        } else if (result_ == -502) {
            resultFlag = "false";
            resultMsg = "有菜品被沽清，请门店确认";
        } else if (result_ == -2) {
            resultFlag = "false";
            resultMsg = "菜品数据" + resultMsg + "不存在，请重新同步后再下单；";
        } else if (result_ == -3) {
            resultFlag = "false";
        } else if (result_ == -4) {
            resultFlag = "false";
            resultMsg = "餐盒数据不匹配，请重新同步后再下单；";
        } else if (result_ == -5) {
            resultFlag = "false";
            resultMsg = "套餐或套餐明细不存在，请重新同步后再下单；";
        } else if (result_ == -6) {
            resultFlag = "false";
            resultMsg = "套餐分组或分组明细不存在，请重新同步后再下单；";
        } else {
            resultFlag = "false";
            resultMsg = "未知错误";
        }
        resultMap.put("resultFlag", resultFlag);
        resultMap.put("resultMsg", resultMsg);
        return resultMap;
    }

    public boolean firstPay(String outOrderIdInDB, JSONObject payInfos, String kdzdbh, String bbrq, Map<String, String> rMap,JSONObject orderInfo) {

        double payAmount = payInfos.optJSONObject("data").optDouble("amount");
        double amountAll = 0d;
        JSONArray pay_info_array = payInfos.optJSONArray("pay_info");
        JSONArray promotions = orderInfo.optJSONArray("promotions");
        JSONObject dishCouponMap = orderInfo.optJSONObject("dishCouponMap");
        List<Integer> fkfsidList = new ArrayList<Integer>();
//        List<BigDecimal> actualmoneyList = new ArrayList<BigDecimal>();
        ecoOrderMapper.clearEcoOrderdiscount(outOrderIdInDB);
        double payGive = 0;

        for (int i = 0; i < pay_info_array.size(); i++) {
            JSONObject pay_info_obj = pay_info_array.getJSONObject(i);
            String source = pay_info_obj.optString("source", "");
            JSONArray couponIds=pay_info_obj.optJSONArray("coupon_dishs_info");
            String dishno=null;
            String couponId=null;
            if(CollectionUtils.isNotEmpty(couponIds)){
               couponId= (String) couponIds.get(0);
               if(dishCouponMap.containsKey(couponId)){
                   dishno= (String) dishCouponMap.get(couponId);
               }
            }else {
                dishno=pay_info_obj.optString("dishsno");
            }
            // 忽略微生活内部支付方式
            if ("wlife".equals(source)) {
                continue;
            }

//            if ("coupon".equals(source) && payAmount < pay_info_obj.optDouble("amount",0d)) {
//                pay_info_obj.put("amount",payAmount);
//            }

            if ("product".equals(source)) {
                double saleDishMoney = pay_info_obj.optDouble("sale_dish_money", 0d);
                pay_info_obj.put("storepay",saleDishMoney);
            }

            if ("coupon".equals(source)) {
                double saleDishMoney = pay_info_obj.optDouble("sale_deno_money", 0d);
                pay_info_obj.put("storepay",saleDishMoney);
            }

            if (THIRD_COUPON.contains(source)) {
                double saleDishMoney = pay_info_obj.optDouble("buyer_pay_amount", 0d);
                saleDishMoney = ArithUtil.mul(saleDishMoney, 100);

//                if("littleRedBook".equals(source)){
//                    saleDishMoney = ArithUtil.mul(saleDishMoney, 100);
//                }

                pay_info_obj.put("storepay",saleDishMoney);
            }


            //TsFkfssdk fkfs = firstPayMapper.getFkfs(source);

            TsFkfssdk fkfs = null;
            EcoTypeDic etd = firstPayMapper.findEcotypedicByCode("-201", source);
            if (etd == null) {
                fkfs = firstPayMapper.getFkfs(source);
            } else {
                fkfs = firstPayMapper.getFkfsByCode(etd.getFkfsbh());
            }

            if (null == fkfs || "".equals(source)) {
                fkfs = firstPayMapper.getFkfsByCode("2020");
            }
            BigDecimal actualmoney = BigDecimal.valueOf(pay_info_obj.optDouble("amount",0d));


            double storedPay = pay_info_obj.optDouble("amount",0d);
            amountAll = ArithUtil.add(amountAll, storedPay);
            double storedGivePay = ArithUtil.sub(storedPay, ArithUtil.div(pay_info_obj.optDouble("storepay", 0d), 100, 2));
            double giveMoney = jointOrderdiscount(outOrderIdInDB, storedPay, storedGivePay,source, "",dishno,couponId);
            payGive = ArithUtil.add(payGive, giveMoney);
            storedPay = ArithUtil.sub(storedPay, giveMoney);
            actualmoney = BigDecimal.valueOf(storedPay);

            fkfsidList.add(fkfs.getId());
//            actualmoneyList.add(actualmoney);
            // 生成付款记录
//            BtPayments btpay = new BtPayments();
//            btpay.setYddh(outOrderIdInDB);
//            btpay.setPay_channel("微生活小程序点餐");
//            btpay.setPay_name(fkfs.getFkfsmc1());
//            btpay.setPay_no(fkfs.getFkfsbh());
//            btpay.setVcardid("");
//            btpay.setVtele("");
//            btpay.setPay_count(actualmoney.doubleValue());
//            btpay.setPay_bb(0);
//            btpay.setVocount(0);
//            btpay.setFzzhje(0);
//            btpay.setFlhl(0);
//            btpay.setPay_memo("");
//            // 生成预定付款记录
//            firstPayMapper.insertBtPayments(btpay);

            insertPayments(outOrderIdInDB, fkfs, actualmoney.doubleValue(), DateUtil.getNowDateYYDDMMHHMMSS(), "",  "");
        }
        double dsyh =  ArithUtil.sub(payAmount, amountAll);
        if(dsyh < 0){
            jointOrderdiscount(outOrderIdInDB, 0, dsyh, "coupon", "多收",null,null);
            payGive = ArithUtil.add(payGive, dsyh);
        }
        if(0 != payGive){
            firstPayMapper.updateYddToTotalprice(outOrderIdInDB, payGive);
        }
        if (fkfsidList.size() < 1) {
            TsFkfssdk fkfs = firstPayMapper.getFkfsByCode("2020");
            insertPayments(outOrderIdInDB, fkfs, 0, DateUtil.getNowDateYYDDMMHHMMSS(), "", "");
        }

        //保存营销优惠
        joinOrderPromotions(outOrderIdInDB,orderInfo);

        return true;
    }



    private String getOrderMemo(JSONObject orderInfo){
        JSONObject ordermemo=orderInfo.optJSONObject("ordermemo");
        if(null!=ordermemo&&!ordermemo.isEmpty()){
            return ordermemo.optString("text");
        }
        return null;
    }

    public void jointYdd(String kdzdbh, JSONObject orderInfo, String outOrderIdInDB, Map<String, String> rMap, JSONObject member) {
        BtYdd by = new BtYdd();
        Shops shops = shopBaseInfoMapper.findShopsData();
        String cwlxbh = InitDataListener.ggcsMap.get("POS_MEMBER_TYPE");
        
        String ydbcid =  rMap.get("bcid");
        String qch = rMap.get("qch");
        String zwbh = rMap.get("zwbh");

        by.setYddh(outOrderIdInDB);
        by.setYdrs(orderInfo.optInt("people"));
        by.setMen(0);
        by.setWomen(0);
        by.setEldernum(0);
        by.setChildnum(0);
        by.setShops_id(shops.getSid() + "");
        by.setTotalprice(orderInfo.optDouble("real_amount"));
        by.setYl1(orderInfo.optDouble("order_amount"));
        by.setYl2(orderInfo.optDouble("orde_dis_amount"));
        by.setShop_rate(orderInfo.optDouble("orde_dis_amount"));
        by.setYdrq(DateUtil.getNowDateYYDDMM());
        by.setQdsj(DateUtil.getNowDateYYDDMMHHMMSS());
        by.setYdbc(ydbcid);
        by.setDdzt("5");
        by.setYl4("XCX");
        by.setKwxh(getOrderMemo(orderInfo));
        by.setZlbh(zwbh);
        // 就餐时间 invalid_desc
        if (orderInfo.optString("isDelayedMeal", "0").equals("1")) {
            by.setInvalid_desc("1");
            String mealtime = orderInfo.optString("mealtime", "1990-01-01 00:00:00"); // 可以为空的值默认为""
            by.setMealtime(mealtime);
        } else {
            by.setInvalid_desc("0");
            by.setMealtime("2019-01-01 12:00:00");
        }
        // 下单来源 “0”为门店下单；“1”为线上下单；默认为“0”。V1.1新增
        if (orderInfo.containsKey("channel")) {
            int channel = orderInfo.optInt("channel", 0); // 默认为0
            by.setChannel(channel);
        }
        // 销售模式
        if (orderInfo.containsKey("diningWay")) {
            int diningWay = orderInfo.optInt("diningWay", 1); // 默认为"1"堂食,2外带，3外卖
            by.setDiningway("XSMS_TS");
            by.setYl5("1");
            if (diningWay == 2) {
                by.setDiningway("XSMS_WM"); // "XSMS_WM"是外带，不是外卖
                by.setYl5("2");
            }
            if (diningWay == 3) {
                by.setYl5("3");
                if (orderInfo.containsKey("mealtime")) {
                    String mealtime = orderInfo.optString("mealtime", "201901011200"); // 可以为空的值默认为""
                    mealtime = mealtime.substring(0, 4) + "-" + mealtime.substring(4, 6) + "-" + mealtime.substring(6, 8) + " " + mealtime.substring(8, 10) + ":" + mealtime.substring(10, 12) + ":00";
                    by.setMealtime(mealtime);
                } else {
                    by.setMealtime("立即配送");
                }
            }
        }
        // 外卖联系人
        if (orderInfo.containsKey("lxr")) {
            by.setLxr(orderInfo.optString("lxr", ""));
        }
        // 外卖联系电话
//        if (orderInfo.containsKey("lxrdh")) {
//            by.setLxrdh(orderInfo.optString("lxrdh", ""));
//        }
        
        if (null!=member &&member.containsKey("phone")) {
            by.setLxrdh(member.optString("phone", ""));
        }
        
        // 外卖配送地址
        if (orderInfo.containsKey("member_address")) {
            by.setMember_address(orderInfo.optString("member_address", ""));
        }
        //by.setBz(cwlxbh);
        by.setHorseman_name(cwlxbh);
        by.setShrbh(qch);
        by.setFail_type2("catering");
        if (orderInfo.containsKey("commoditytype")) {
            String commoditytype = orderInfo.optString("commoditytype", "catering");
            if ("catering".equals(commoditytype) || "retail".equals(commoditytype)) {
                by.setFail_type2(orderInfo.optString("commoditytype", "catering"));
            }
        }
        by.setBill_num(kdzdbh);
        
        orderPrecheckMapper.insertBtYddByZs(by);
    }


    public BtYdxm2 jointYdmx(JSONObject jo, String outOrderIdInDB, String xmsx, int dcxh, JSONObject mxJo, Integer tcid, int tcdch, String tcbh, String tcfs) {
        BtYdxm2 ydmx2 = new BtYdxm2();

        ydmx2.setIsactive(jo.optInt("isactivity",1)); //是否折扣



        //套餐明细特殊处理
        if ("CMSX_MX".equals(xmsx)) {

            int tcNumber = jo.optInt("number", 1);

            ydmx2.setYddh(outOrderIdInDB);
            ydmx2.setXmid(mxJo.optInt("did"));
            ydmx2.setXmbh(mxJo.optString("dishsno"));
            ydmx2.setXmmc(mxJo.optString("name"));
            if ("1".equals(InitDataListener.ggcsMap.get("XCXITEMNAMETYPE"))) {
                ydmx2.setXmmc(mxJo.optString("localName"));
            }
            ydmx2.setXm1mc(mxJo.optString("name"));
            ydmx2.setXmsx(xmsx);
            // 单价还是菜品原价
            ydmx2.setXmdj(new BigDecimal(mxJo.optString("price", "0")));
            ydmx2.setXmsl(mxJo.optInt("number", 1) * tcNumber); // 小程序请求的参数中, 明细的菜品数量表示的是一份套餐里, 该菜品的数量.
            ydmx2.setZkl(100);

            String dwbh = mxJo.optString("dishunit", "份");
            if ("".equals(dwbh)) {
                dwbh = "份";
            }
            ydmx2.setDwbh(dwbh);
            ydmx2.setKwbh(mxJo.optString("remark"));
            // 菜品金额使用原价*数量
            ydmx2.setCmje(ydmx2.getXmdj().multiply(BigDecimal.valueOf(ydmx2.getXmsl())));
            // 菜品实结金额
            ydmx2.setTotalprice(ydmx2.getCmje());
            ydmx2.setTcbh(tcbh);
            ydmx2.setTcdch(tcdch);
            ydmx2.setFzsl(0);
            ydmx2.setDcxh(dcxh);
            ydmx2.setFzje(new BigDecimal(mxJo.optDouble("aprice", 0)));
            ydmx2.setYl3(mxJo.optString("discountsprice", "0"));
            ydmx2.setTop_item_id(tcid);
            if ("ERP_MXLX_SINGLE".equals(tcfs)) {
                ydmx2.setYl4("0");
            } else {
                ydmx2.setYl4("1");
            }
        } else {
            ydmx2.setYddh(outOrderIdInDB);
            ydmx2.setXmid(jo.optInt("did"));
            ydmx2.setXmbh(jo.optString("dishsno"));
            ydmx2.setXmmc(jo.optString("name"));
            if ("1".equals(InitDataListener.ggcsMap.get("XCXITEMNAMETYPE"))) {
                ydmx2.setXmmc(jo.optString("localName"));
            }
            ydmx2.setXm1mc(jo.optString("name"));
            ydmx2.setXmsx(xmsx);
            // 单价还是菜品原价
            ydmx2.setXmdj(new BigDecimal(jo.optString("price", "0")));
            ydmx2.setXmsl(jo.optInt("number"));
            ydmx2.setZkl(100);
            // 菜品实结金额
            ydmx2.setTotalprice(new BigDecimal(ArithUtil.sub(jo.optDouble("realprice", 0),
                    jo.optDouble("discountsprice", 0))));
            String dwbh = jo.optString("dishunit", "份");
            if ("".equals(dwbh)) {
                dwbh = "份";
            }
            ydmx2.setDwbh(dwbh);
            ydmx2.setKwbh(jo.optString("remark"));
            // 菜品金额使用原价*数量
            ydmx2.setCmje(new BigDecimal(jo.optDouble("realprice", 0)));
            ydmx2.setTcbh(tcbh);
            ydmx2.setTcdch(tcdch);
            ydmx2.setFzsl(0);
            ydmx2.setDcxh(dcxh);
            ydmx2.setFzje(new BigDecimal(jo.optDouble("aprice", 0)));
            ydmx2.setYl3(jo.optString("discountsprice", "0"));
            if (!"CMSX_DP".equals(xmsx)) {
                ydmx2.setTop_item_id(tcid);
            }
            ydmx2.setYl4("");
        }

        JSONArray memo = jo.optJSONArray("memo");
        if (null != memo && !memo.isEmpty()) {
            //取ordermemo
            for(int i=0;i<memo.size();i++){
                JSONObject memoObj = memo.getJSONObject(i);
                JSONArray items = memoObj.optJSONArray("items");
                if (null != items && !items.isEmpty()) {
                    JSONObject itemsObj = items.getJSONObject(0);
                    String ordermemo = itemsObj.optString("ordermemo");
                    ydmx2.setKwbh(ydmx2.getKwbh()+" "+ordermemo);
                }
            }
        }

        return ydmx2;
    }



    /**
     * 创建账单
     * @return
     */
    public Map<String, String> createBill(JSONObject orderInfo, String kdzdbh, String yddh, String bbrq, BillNoData billNoData) {
        Map<String, String> rMap = new HashMap<String, String>();

        String jtbh = "99";// 机台号

        int bcid = 0; // 班次id
        TqJtztk jtzt = firstPayMapper.getJtZtk(DateUtil.parseDate(bbrq));
        String ygdlcs = jtzt.getYgdlcs();
        String czybh = jtzt.getRybh();// 操作员编号

        TsGgcsk yyms = firstPayMapper.getGgcsToWs("MDYYMS");
//        String zwbh = orderInfo.optString("tableno", "");
        String zwbh = orderInfo.optString("tableName", "");
//		String qch = "";
        String qch = billNoData.getQch();
        if (null != yyms && "3".equals(yyms.getSdnr())) {
            bcid = firstPayMapper.getBcid(DateUtil.parseDate(bbrq));
        } else {
            bcid = Integer.parseInt(jtzt.getYl1());
        }

        if (null == zwbh || "".equals(zwbh)) {
//			qch = billNoData.getQch();
            zwbh = "";
        }

        String lsdh = billNoData.getLsdh();

        int ktbcid = bcid;

        TqZdk tqZdk = new TqZdk();
        tqZdk.setKdzdbh(kdzdbh);
        tqZdk.setLsdh(lsdh);
        tqZdk.setJzcs(0);
        tqZdk.setKtskjh(jtbh);
        tqZdk.setFwyh(czybh);
        tqZdk.setKtczry(czybh);
        tqZdk.setKtsj(new Date());
        tqZdk.setKdbbrq(DateUtil.parseDate(bbrq));
        tqZdk.setJzsx("ZDSX_WJ");
        tqZdk.setSource("XCX");
        tqZdk.setCbid(-1);
        tqZdk.setXfks(1);
        tqZdk.setDyzdcs(1);
        tqZdk.setYddh(yddh);
        // 处理是否预约标记
        if (orderInfo.optString("isDelayedMeal", "0").equals("1")) {
            tqZdk.setYl1("1");
            String yysj = orderInfo.optString("mealtime", "1990-01-01 00:00:00");
            tqZdk.setYl2(yysj);
        } else {
            tqZdk.setYl1("0");
            tqZdk.setYl2("2019-01-01 12:00:00");
        }
        if (orderInfo.optInt("diningWay", 1) == 2) {
            tqZdk.setXsms("XSMS_WM");
        } else {
            tqZdk.setXsms("XSMS_TS");
        }

        tqZdk.setYgdlcs(ygdlcs + "");
        tqZdk.setZkl(100);
        tqZdk.setYhfsbh(null);
        tqZdk.setKtbcid(ktbcid);
        tqZdk.setZdzt("");
        tqZdk.setQch(qch);
        tqZdk.setZwbh(zwbh);

        rMap.put("zwbh", zwbh);
        rMap.put("qch", qch);
        rMap.put("bcid", bcid + "");
        rMap.put("czybh", czybh);
        rMap.put("jtbh", jtbh);
        rMap.put("bbrq", bbrq);
        rMap.put("ygdlcs", ygdlcs);

        return rMap;
    }


    @Transactional
    public Data orderPrecheckBefore(JSONObject orderData) {
        // 创建返回数据对象
        Data data = new Data();
        data.setYddbh(orderData.optString("out_order_id"));
        data.setSuccess(1);
        data.setMsg("验证通过！");
        JSONObject orderInfo = orderData.optJSONObject("order_info");
        JSONObject payInfos = orderData.optJSONObject("pay_info");

        List<JSONObject> payInfoAddList=new ArrayList<>();

        try {
            double orderAmount = orderInfo.optDouble("total", 0); // 账单金额  orderAmount = realAmount + ordeDisAmount
            double realAmount = orderInfo.optDouble("cost", 0); // 实结金额payAmount
            double ordeDisAmount = 0d; // 优惠金额
            double boxTotal= ArithUtil.div(orderInfo.optDouble("box_price",0),100); //餐盒费
            List<JSONObject> boxItems=new ArrayList<>();//餐盒菜品

            double payAmount = 0;
            double dishRealAmount = 0;

            double promotionCouponsAmount=0d;

            JSONArray payInfoArr = payInfos.optJSONArray("pay_info");
            JSONArray promotions = orderInfo.optJSONArray("promotions");
            JSONArray discounts = orderInfo.optJSONArray("discounts");

            Map<String,Double> itemDiscountMap=new HashMap<>();
            Map<String,String> ruleNameMap=new HashMap<>();
            Map<String,String> dishCouponMap=new HashMap<>();
            Map<String,String> dishNoKeyMap=new HashMap<>();

            JSONObject member=orderInfo.optJSONObject("member");
            if(null!=member){
                JSONArray coupons=member.optJSONArray("coupons");
                if(CollectionUtils.isNotEmpty(coupons)){
                    for(int i=0;i<coupons.size();i++){
                        JSONObject coupon=coupons.getJSONObject(i);
                        String dishesno=coupon.optString("dishesno");
                        if(StringUtils.isNotEmpty(dishesno)){
                            dishCouponMap.put(coupon.optString("coupon_id"),dishesno);
                        }
                    }
                }
            }
            orderInfo.put("dishCouponMap", dishCouponMap);

            JSONObject executeInfo=orderInfo.optJSONObject("executeInfo");
            if(null!=executeInfo){
                JSONArray executeList = executeInfo.optJSONArray("executeList");
                if(CollectionUtils.isNotEmpty(executeList)){
                    for(int i=0;i<executeList.size();i++){
                        JSONObject executeInfoMapItem=executeList.getJSONObject(i);
                        String key=executeInfoMapItem.optString("groupFlag");
                        Double dicountAmount=0d;

                        if(itemDiscountMap.containsKey(key)){
                            dicountAmount=itemDiscountMap.get(key);
                        }

                        JSONArray items = executeInfoMapItem.optJSONArray("ruleList");
                        if(CollectionUtils.isNotEmpty(items)){
                            for(int j=0;j<items.size();j++){
                                JSONObject item=items.getJSONObject(j);

                                String categoryFlag=item.optString("categoryFlag");

                                if(INGORE_RULE_CATEGORY.contains(categoryFlag)){
                                    continue;
                                }

                                dicountAmount=ArithUtil.add(dicountAmount,ArithUtil.div(item.optDouble("preferentialAmount",0d),100));
                                ruleNameMap.put(item.optString("ruleId"),item.optString("ruleName"));
                            }
                        }

                        itemDiscountMap.put(key,dicountAmount);
                    }
                }
            }

            String diningWay = orderInfo.optString("diningWay","1"); //1为堂食,2为外带

            //营销版需记录每种优惠，不能合并到整单优惠
//            if(CollectionUtils.isNotEmpty(promotions)){
//                for(int i = 0; i < promotions.size(); i++){
//                    ordeDisAmount = ArithUtil.add(ordeDisAmount, promotions.getJSONObject(i).optDouble("money",0d));
//                }
//            }

            if(CollectionUtils.isNotEmpty(discounts)){
                for(int i = 0; i < discounts.size(); i++){
                    ordeDisAmount = ArithUtil.add(ordeDisAmount, discounts.getJSONObject(i).optDouble("money",0d));
                }
            }

            /*if("1".equals(orderInfo.optString("isUsePromotion"))&&CollectionUtils.isNotEmpty(promotionCoupons)){
                for(int i=0;i<promotionCoupons.size();i++){
                    promotionCouponsAmount = ArithUtil.add(promotionCouponsAmount,
                            ArithUtil.sub(promotionCoupons.getJSONObject(i).optDouble("promotion_discount_amount",0d), promotionCoupons.getJSONObject(i).optDouble("promotion_sale_money",0d)));
                }
                promotionCouponsAmount=ArithUtil.div(promotionCouponsAmount,100);
                ordeDisAmount=ArithUtil.add(ordeDisAmount,promotionCouponsAmount);
                realAmount=ArithUtil.sub(realAmount,promotionCouponsAmount);
            }*/

            String welifePayInfoStr=orderData.optString("welife_pay_info");
            if(StringUtils.isNotEmpty(welifePayInfoStr)){
                try {
                    JSONObject welifePayInfo = JSONObject.fromObject(welifePayInfoStr);
                    promotionCouponsAmount = ArithUtil.sub(0,ArithUtil.div(welifePayInfo.optDouble("coupon_sale_money",0d),100));
                    ordeDisAmount = ArithUtil.add(ordeDisAmount,promotionCouponsAmount);
                    realAmount = ArithUtil.sub(realAmount,promotionCouponsAmount);
                }catch (JSONException e){
                    LOGGER.error("welife_pay_info不是有效的JSON",welifePayInfoStr);
                }

            }

            for (int i = 0; i < payInfoArr.size(); i++) {
                JSONObject payInfo = payInfoArr.getJSONObject(i);
                double pAmount = payInfo.optDouble("amount");
                if(pAmount>realAmount&&"source".equals(payInfo.optString("coupon"))) {
                    payAmount=realAmount;
                    payInfo.put("amount",payAmount);
                }else {
                    payAmount = ArithUtil.add(payAmount, pAmount);
                }
            }
//            for (int i = 0; i < payInfoArr.size(); i++) {
//                JSONObject payInfo = payInfoArr.getJSONObject(i);
//                double pAmount = payInfo.optDouble("amount");
//                if("coupon".equals(payInfo.optString("source")) || "product".equals(payInfo.optString("source"))) {
//                    double saleDishMoney = payInfo.optDouble("sale_dish_money");
//                    double dishMoney = ArithUtil.sub(pAmount, saleDishMoney);
//                    payAmount = ArithUtil.add(payAmount, saleDishMoney);
//                    ordeDisAmount = ArithUtil.add(ordeDisAmount, dishMoney);
//                    realAmount = ArithUtil.sub(realAmount, dishMoney);
//                }else {
//                    payAmount = ArithUtil.add(payAmount, pAmount);
//                }
//            }

            orderInfo.put("order_amount", orderAmount); // 账单金额
            orderInfo.put("real_amount", realAmount); // 实结金额
            orderInfo.put("orde_dis_amount", ordeDisAmount); // 优惠金额
            orderInfo.put("ruleNameMap", ruleNameMap);

//			if (orderAmount != ArithUtil.add(realAmount, ordeDisAmount)) {
//				data.setSuccess(0);
//				data.setMsg("“账单金额”不等于“实结金额”+“优惠金额”，请联系管理员！");
//				data.setData(new HashMap<String, Object>());
//				return data;
//			}
//
//			if (payAmount < realAmount) {
//				data.setSuccess(0);
//				data.setMsg("“支付金额”小于“应付金额”，请联系管理员！");
//				data.setData(new HashMap<String, Object>());
//				return data;
//			}

            if (orderInfo.has("normalitems")) {
                JSONArray normalitems = new JSONArray();
                normalitems = orderInfo.getJSONArray("normalitems");
                for (int i = 0; i < normalitems.size(); i++) {
                    JSONObject item = normalitems.getJSONObject(i);

                    final String itemCodeBySpeccode = getItemCodeBySpeccode(item);

                    dishNoKeyMap.put(item.optString("key"),itemCodeBySpeccode);

                    TsCmk cmk = orderPrecheckMapper.getDishByCode(itemCodeBySpeccode);
                    if (null == cmk) {
                        data.setSuccess(0);
                        data.setMsg("菜品“" + item.optString("name") + "”不存在，请联系管理员！");
                        data.setData(new HashMap<String, Object>());
                        return data;
                    }
                    if (!"CMSX_DP".equals(cmk.getCmsx())) {


                        data.setSuccess(0);
                        data.setMsg("菜品“" + item.optString("name") + "”不是单品菜品，请联系管理员！");
                        data.setData(new HashMap<String, Object>());
                        return data;
                    }

                    item.put("foodboxset",cmk.getFoodboxset());
                    Map<String, String> resultMap = new HashMap<String, String>();
                    JSONObject boxItem = setItemRealPrice(resultMap, item, diningWay,itemDiscountMap);
                    if("-1".equals(resultMap.get("code"))){
                        data.setSuccess(0);
                        data.setMsg("菜品“" + item.optString("name") + "”未绑定餐盒或无餐盒数据，请联系管理员！");
                        data.setData(new HashMap<String, Object>());
                        return data;
                    }
                    if(boxItem.size()>0){
                        boxItems.add(boxItem);
                    }
                    dishRealAmount = ArithUtil.add(dishRealAmount, item.optDouble("realprice", 0));
                }
            }

            if (orderInfo.has("setmeal")) {
                JSONArray setmeal = new JSONArray();
                setmeal = orderInfo.getJSONArray("setmeal");
                for (int i = 0; i < setmeal.size(); i++) {
                    JSONObject item = setmeal.getJSONObject(i);

                    final String itemCodeBySpeccode = getItemCodeBySpeccode(item);

                    dishNoKeyMap.put(item.optString("key"),itemCodeBySpeccode);

                    TsCmk cmk = orderPrecheckMapper.getDishByCode(itemCodeBySpeccode);
                    if (null == cmk) {
                        data.setSuccess(0);
                        data.setMsg("菜品“" + item.optString("name") + "”不存在，请联系管理员！");
                        data.setData(new HashMap<String, Object>());
                        return data;
                    }
                    if (!"CMSX_TC".equals(cmk.getCmsx())) {
                        data.setSuccess(0);
                        data.setMsg("菜品“" + item.optString("name") + "”不是套餐菜品，请联系管理员！");
                        data.setData(new HashMap<String, Object>());
                        return data;
                    }

                    item.put("foodboxset",cmk.getFoodboxset());
                    Map<String, String> resultMap = new HashMap<String, String>();
                    JSONObject boxItem = setItemRealPrice(resultMap, item,diningWay,itemDiscountMap);
                    if("-1".equals(resultMap.get("code"))){
                        data.setSuccess(0);
                        data.setMsg("菜品“" + item.optString("name") + "”未绑定餐盒或无餐盒数据，请联系管理员！");
                        data.setData(new HashMap<String, Object>());
                        return data;
                    }
                    if(boxItem.size()>0){
                        boxItems.add(boxItem);
                    }

                    double setmealRealprice = item.optDouble("realprice", 0); //套餐主项实收
                    double mmRealprice = 0; //明细实收之和
                    JSONObject maxPriceItem=new JSONObject(); //最大价格单品

                    if (item.has("maindish")) {
                        JSONArray mainDish = new JSONArray();
                        mainDish = item.optJSONArray("maindish");
                        for (int j = 0; j < mainDish.size(); j++) {
                            JSONObject md = mainDish.getJSONObject(j);
                            Map<String, String> resultMap1 = new HashMap<String, String>();
                            setItemRealPrice(resultMap1, md, diningWay);
                            mmRealprice = ArithUtil.add(mmRealprice, md.optDouble("realprice", 0));
                            if(maxPriceItem.optDouble("realprice",0d)<md.optDouble("realprice", 0)){
                               maxPriceItem=md;
                            }
                        }
                    }

                    if (item.has("mandatory")) {
                        JSONArray mandatory = new JSONArray();
                        mandatory = item.optJSONArray("mandatory");
                        for (int j = 0; j < mandatory.size(); j++) {
                            JSONObject md = mandatory.getJSONObject(j);
                            Map<String, String> resultMap2 = new HashMap<String, String>();
                            setItemRealPrice(resultMap2, md, diningWay);
                            mmRealprice = ArithUtil.add(mmRealprice, md.optDouble("realprice", 0));
                            if(maxPriceItem.optDouble("realprice",0d)<md.optDouble("realprice", 0)){
                                maxPriceItem=md;
                            }
                        }
                    }

                    if (item.has("optional")) {
                        JSONArray optional =item.optJSONArray("optional");
                        for (int j = 0; j < optional.size(); j++) {
                            JSONObject md = optional.getJSONObject(j);
                            Map<String, String> resultMap3 = new HashMap<String, String>();
                            setItemRealPrice(resultMap3,md,diningWay);
                            mmRealprice = ArithUtil.add(mmRealprice, md.optDouble("realprice", 0));
                            if(maxPriceItem.optDouble("realprice",0d)<md.optDouble("realprice", 0)){
                                maxPriceItem=md;
                            }
                        }
                    }

//					if (setmealRealprice != mmRealprice) {
//
//                        maxPriceItem.put("realprice",ArithUtil.sub(maxPriceItem.optDouble("realprice"),
//                                ArithUtil.sub(mmRealprice,setmealRealprice)));
//
//						data.setSuccess(0);
//						data.setMsg("套餐菜品“" + item.optString("name") + "”金额与明细金额不符，请联系管理员！");
//						data.setData(new HashMap<String, Object>());
//						return data;
//					}
                    dishRealAmount = ArithUtil.add(dishRealAmount, setmealRealprice);
                }
            }

            if(CollectionUtils.isNotEmpty(boxItems)){
                JSONArray normalitems=null;
                if (orderInfo.has("normalitems")) {
                     normalitems = orderInfo.getJSONArray("normalitems");
                     normalitems.addAll(boxItems);
                }else {
                     normalitems=new JSONArray();
                     normalitems.add(boxItems);
                     orderInfo.put("normalitems",normalitems);
                }
            }

            orderInfo.put("dishNoKeyMap",dishNoKeyMap);

            JSONObject executeinfo=orderInfo.optJSONObject("executeInfo");
            if(null!=executeinfo){
                JSONArray executeList = executeinfo.optJSONArray("executeList");
                if(CollectionUtils.isNotEmpty(executeList)){
                    for(int i=0;i<executeList.size();i++){
                        JSONObject executeInfoMapItem=executeList.getJSONObject(i);
                        String key=executeInfoMapItem.optString("groupFlag");

                        JSONArray items = executeInfoMapItem.optJSONArray("ruleList");
                        if(CollectionUtils.isNotEmpty(items)){
                            for(int j=0;j<items.size();j++){
                                JSONObject item=items.getJSONObject(j);

                                String categoryFlag=item.optString("categoryFlag");

                               /* {
                                    "source": "product",
                                        "amount": "10.80",
                                        "storepay": 1080,
                                        "sale_dish_money": 0,
                                        "serilNo": "300000005645053_8510",
                                        "coupon_dishs_info": ["1821188983096889365"]
                                }*/

                                if(NOPAYINFO_CATEGORY.contains(categoryFlag)){
                                    JSONObject pay=new JSONObject();
                                    pay.put("source", "product");
                                    pay.put("amount",ArithUtil.div(item.optDouble("preferentialAmount",0d),100));
                                    pay.put("storepay",item.optDouble("preferentialAmount",0d));
                                    pay.put("sale_dish_money",0);
                                    pay.put("dishno",dishNoKeyMap.get(key));
                                    payInfoAddList.add(pay);
                                }
                            }
                        }

                    }
                }
            }

            if(CollectionUtils.isNotEmpty(payInfoAddList)){
                payInfoArr.addAll(payInfoAddList);
            }


//			if (ArithUtil.add(dishRealAmount,boxTotal) != orderAmount) {
//				data.setSuccess(0);
//				data.setMsg("“账单金额”不等于“明细金额”，请联系管理员！");
//				data.setData(new HashMap<String, Object>());
//				return data;
//			}

            return data;
        } catch (Exception e) {
            LOGGER.error("Ignore this exception", e);
            data.setSuccess(0);
            data.setMsg("系统错误:" + e);
            data.setData(new HashMap<String, Object>());
            return data;
        }
    }

    private JSONObject setItemRealPrice(Map<String, String> resultMap, JSONObject item,String diningWay) {
        return  setItemRealPrice(resultMap, item,diningWay,null);
    }

    /***
     * 计算单品或套餐主项实收价格
     */
    private JSONObject setItemRealPrice(Map<String, String> resultMap, JSONObject item,String diningWay,Map<String,Double> itemDiscountMap) {
        resultMap.put("code", "0");
        resultMap.put("msg", "");
        JSONObject boxItem = new JSONObject();
        //计算单品或套餐主项realprice = price*number+aprice
        Double realPrice = ArithUtil.add(item.optDouble("aprice",0d),ArithUtil.mul(item.optDouble("price",0d), item.optDouble("number",0d)));
        //口味加价
        realPrice = ArithUtil.add(realPrice, ArithUtil.div( item.optDouble("toppingTotal",0d),100d));

        //口味
        JSONArray toppings = item.optJSONArray("toppings");
        if(CollectionUtils.isNotEmpty(toppings)){
            String remark = "";
            for(int i = 0; i < toppings.size(); i++){
                JSONObject top=toppings.getJSONObject(i);
                double addprice = ArithUtil.div(top.optDouble("addprice", 0d),100d);
                remark+=" " + top.optString("tpname","") + "(+" +  addprice + "元) ";
            }
            item.put("remark",remark);
        }

        //餐盒费
        if("2".equals(diningWay)){
            LOGGER.info("外带处理餐盒");
            JSONObject norms= item.optJSONObject("norms");
            if(null!=norms&&!norms.isEmpty()){
                double boxNum = ArithUtil.mul(norms.optDouble("box_num", 0d),item.optDouble("number", 0d)); //菜品餐盒总数=规格餐盒数x菜品数量
                double boxPrice=norms.optDouble("box_price",0d);
                double boxTotal=ArithUtil.mul(boxNum,boxPrice);
                if(0 < boxTotal){
                    //查询餐盒信息
                    TsCmk cmk = orderPrecheckMapper.getTsCmk(item.optInt("foodboxset",-1));
                    if (null == cmk) {
                        cmk = orderPrecheckMapper.getSendAmount("餐盒");
                    }
                    if (null != cmk) {
                        resultMap.put("code", "0");
                        resultMap.put("msg", "");
                        boxItem.put("did",cmk.getDlid());
                        boxItem.put("dishsno",cmk.getCmbh());
                        boxItem.put("name",cmk.getCmmc1());
                        boxItem.put("localName",cmk.getCmmc1());
                        boxItem.put("price", boxPrice);
                        boxItem.put("number",boxNum);
                        boxItem.put("realprice", boxTotal);
                        boxItem.put("dishunit", cmk.getDwbh());
                        boxItem.put("isactivity", 0);
                    } else {
                        resultMap.put("code", "-1");
                        resultMap.put("msg", "菜品未绑定餐盒或无餐盒数据！");
                        return boxItem;
                    }
                }
//                realPrice = ArithUtil.add(realPrice,boxTotalPrice);
            }
        }
        item.put("realprice",realPrice);

        if(null!=itemDiscountMap){
            String groupFlag=item.optString("key");
            if(itemDiscountMap.containsKey(groupFlag)){ //??
                item.put("discountsprice",itemDiscountMap.get(groupFlag));
            }
        }

        return boxItem;
    }

    //处理speccode
    private String getItemCodeBySpeccode(JSONObject item) {
        String dishno = item.optString("dishsno");

        String speccode = item.optString("speccode","");
        if(StringUtils.isEmpty(speccode)){
            JSONObject norms = item.optJSONObject("norms");
            if(null != norms && !norms.isEmpty()){
                speccode = norms.optString("speccode");
            }
        }

        LOGGER.info("dishsno=" + dishno+",speccode=" + speccode);

        if(StringUtils.isNotEmpty(speccode)  && !"null".equalsIgnoreCase(speccode)){
            return speccode;
        }
        return dishno;

    }

    public void jointDealdetails(String kdzdbh, JSONObject member, String bbrq, String skjh) {
        try {
            acewillCouponTempMapper.delAcewilDealdetails(kdzdbh);
            if(null==member||member.isEmpty()) {
                return;
            }
            String cardno = member.optString("cno", "");
            double balance = member.optDouble("bala", 0);
            double credit = member.optDouble("consumenu", 0);
            acewillCouponTempMapper.insertAcewilDealdetails(cardno, kdzdbh, 0, balance, credit, DateUtil.parseDate(bbrq), "0000", skjh, 1,null);
        } catch (Exception e) {
            LOGGER.error("Ignore this exception", e);
        }
    }

    public double jointOrderdiscount(String yddh, double storedPay, double storedGivePay, String type, String name,String dishno,String couponId) {
        try {
            double giveMoney = 0;
            if ("balance".equals(type)) {
                if ("1".equals(InitDataListener.ggcsMap.get("POS_MEMBALANCE_USEDIS")) || "3".equals(InitDataListener.ggcsMap.get("POS_MEMBALANCE_USEDIS"))) {
                    DishVo dsfyhB = new DishVo();
                    if ("1".equals(InitDataListener.ggcsMap.get("POS_MEMBALANCE_USEDIS"))) {
                        dsfyhB = firstPayMapper.getDsfyh(WLIFE_YHSX_MEMBER_GIVE); //63
                    } else if ("3".equals(InitDataListener.ggcsMap.get("POS_MEMBALANCE_USEDIS"))) {
                        dsfyhB = firstPayMapper.getDsfyh(WLIFE_YHSX_MEMBER_GIVE_SHARE); //73
                    }
                    if (null != dsfyhB) {
//						firstPayMapper.insertBtYddPayActive(yddh, "", storedGivePay, "", "BALANC", Integer.parseInt(dsfyhB.getYhfsid()));
                        List<EcoOrderdiscount> ecoOrderdiscounts = new ArrayList<EcoOrderdiscount>();
                        EcoOrderdiscount eod = new EcoOrderdiscount();
                        eod.setOrder_code(yddh);
                        eod.setActivity_id(dsfyhB.getYhfsbh());
                        eod.setDiscount_desc(dsfyhB.getItem_name());
                        eod.setShop_rate(BigDecimal.valueOf(storedGivePay));
                        eod.setDiscount_fee(BigDecimal.valueOf(storedGivePay));
                        ecoOrderdiscounts.add(eod);
                        ecoOrderMapper.insertEcoOrderdiscount(ecoOrderdiscounts);

                        giveMoney = storedGivePay;
                    } else {
                        LOGGER.info("储值未拆分:未设置赠送账户优惠");
                    }
                }
                if ("2".equals(InitDataListener.ggcsMap.get("POS_MEMBALANCE_USEDIS"))) {
                    String acewillDiscount = InitDataListener.ggcsMap.get("ACEWILLDISCOUNT");
                    if (null != acewillDiscount) {
                        if (isNumeric(acewillDiscount) && Integer.parseInt(acewillDiscount) > 0 && Integer.parseInt(acewillDiscount) <= 100) {
                            DishVo dsfyhB = firstPayMapper.getDsfyh(WLIFE_YHSX_MEMBER_GIVE_SHARE);//73
                            double scale = Integer.parseInt(acewillDiscount);
                            double scale_pay = ArithUtil.sub(storedPay, ArithUtil.mul(storedPay, ArithUtil.div(scale, 100, 2)));
                            scale_pay = ArithUtil.round(scale_pay, 2);
                            if (null != dsfyhB) {
//								firstPayMapper.insertBtYddPayActive(yddh, "", scale_pay, "", "BALANC", Integer.parseInt(dsfyhB.getYhfsid()));
                                List<EcoOrderdiscount> ecoOrderdiscounts = new ArrayList<EcoOrderdiscount>();
                                EcoOrderdiscount eod = new EcoOrderdiscount();
                                eod.setOrder_code(yddh);
                                eod.setActivity_id(dsfyhB.getYhfsbh());
                                eod.setDiscount_desc(dsfyhB.getItem_name());
                                eod.setShop_rate(BigDecimal.valueOf(scale_pay));
                                eod.setDiscount_fee(BigDecimal.valueOf(scale_pay));
                                ecoOrderdiscounts.add(eod);
                                ecoOrderMapper.insertEcoOrderdiscount(ecoOrderdiscounts);

                                giveMoney = scale_pay;
                            } else {
                                LOGGER.info("储值未拆分:未设置拆分优惠");
                            }
                        } else {
                            LOGGER.info("储值未拆分:拆分比例=" + acewillDiscount);
                        }
                    } else {
                        LOGGER.info("储值未拆分:未设置拆分比例");
                    }
                }
            } else if ("credit".equals(type)) {
                if ("3".equals(InitDataListener.ggcsMap.get("POS_MEMBALANCE_USEDIS")) || "1".equals(InitDataListener.ggcsMap.get("POS_MEMCREDIT_USEDIS"))) {
                    DishVo dsfyhC = firstPayMapper.getDsfyh(WLIFE_YHSX_MEMBER_CREDIT);//67
                    if (null != dsfyhC) {
                        firstPayMapper.insertBtYddPayActive(yddh, "", storedGivePay, "", "CREDIT", Integer.parseInt(dsfyhC.getYhfsid()));
                        List<EcoOrderdiscount> ecoOrderdiscounts = new ArrayList<EcoOrderdiscount>();
                        EcoOrderdiscount eod = new EcoOrderdiscount();
                        eod.setOrder_code(yddh);
                        eod.setActivity_id(dsfyhC.getYhfsbh());
                        eod.setDiscount_desc(dsfyhC.getItem_name());
                        eod.setShop_rate(BigDecimal.valueOf(storedGivePay));
                        eod.setDiscount_fee(BigDecimal.valueOf(storedGivePay));
                        ecoOrderdiscounts.add(eod);
                        ecoOrderMapper.insertEcoOrderdiscount(ecoOrderdiscounts);

                        giveMoney = storedGivePay;
                    } else {
                        LOGGER.info("积分未拆分:未设置积分优惠");
                    }
                }
            } else if ("coupon".equals(type) || "product".equals(type)) {
                DishVo dsfyhCP = firstPayMapper.getDsfyh(WLIFE_YHSX_PROMOTION);//6
                if (null != dsfyhCP) {
                    firstPayMapper.insertBtYddPayActive(yddh, "", storedGivePay, "", "COUPON", Integer.parseInt(dsfyhCP.getYhfsid()));
                    List<EcoOrderdiscount> ecoOrderdiscounts = new ArrayList<EcoOrderdiscount>();
                    EcoOrderdiscount eod = new EcoOrderdiscount();
                    eod.setOrder_code(yddh);
                    eod.setActivity_id(dsfyhCP.getYhfsbh());
                    eod.setDiscount_desc(dsfyhCP.getItem_name() + name);
                    eod.setShop_rate(BigDecimal.valueOf(storedGivePay));
                    eod.setDiscount_fee(BigDecimal.valueOf(storedGivePay));
                    ecoOrderdiscounts.add(eod);

                    if(StringUtils.isNotEmpty(dishno)){
                        eod.setDiscount_type(couponId);
                        eod.setPay_no(dishno);
                    }

                    ecoOrderMapper.insertEcoOrderdiscount(ecoOrderdiscounts);

                    giveMoney = storedGivePay;
                } else {
                    LOGGER.info("会员券优惠:未设置会员券优惠");
                }
            } else if (THIRD_COUPON.contains(type)) {
                TsYhfssdk dsfyhCP = firstPayMapper.getDsfyhByYhfsmc2(type);//6
                if (null != dsfyhCP) {
                    firstPayMapper.insertBtYddPayActive(yddh, "", storedGivePay, "", "COUPON", dsfyhCP.getId());
                    List<EcoOrderdiscount> ecoOrderdiscounts = new ArrayList<EcoOrderdiscount>();
                    EcoOrderdiscount eod = new EcoOrderdiscount();
                    eod.setOrder_code(yddh);
                    eod.setActivity_id(dsfyhCP.getYhfsbh());
                    eod.setDiscount_desc(dsfyhCP.getYhfsmc1() + name);
                    eod.setShop_rate(BigDecimal.valueOf(storedGivePay));
                    eod.setDiscount_fee(BigDecimal.valueOf(storedGivePay));
                    ecoOrderdiscounts.add(eod);
                    ecoOrderMapper.insertEcoOrderdiscount(ecoOrderdiscounts);

                    giveMoney = storedGivePay;
                } else {
                    LOGGER.info("未设置三方券优惠："+type);
                }
            } else {
                return 0;
            }
            return giveMoney;
        } catch (Exception e) {
            LOGGER.error("Ignore this exception", e);
            return 0;
        }
    }

    /**
     * 营销优惠
     * @param yddh
     * @param orderInfo
     */
    public void joinOrderPromotions(String yddh, JSONObject orderInfo) {
        // 活动列表
        List<EcoOrderdiscount> ecoOrderdiscounts = new ArrayList<EcoOrderdiscount>();
        JSONArray promotions = orderInfo.optJSONArray("promotions");
        JSONObject ruleNameMap = orderInfo.optJSONObject("ruleNameMap");
        if(CollectionUtils.isNotEmpty(promotions)){
            for (int j = 0; j < promotions.size(); j++) {
                JSONObject promotion = promotions.getJSONObject(j);
                EcoOrderdiscount item = new EcoOrderdiscount();
                item.setOrder_code(yddh);
                item.setDiscount_type(promotion.optString("id"));
                item.setActivity_id(PROMOTION_YHFSBH);
                String desc=promotion.optString("title");
                if(StringUtils.isEmpty(desc)){
                    desc=ruleNameMap.optString(item.getDiscount_type());
                }
                item.setDiscount_desc(desc);
                item.setDiscount_fee(new BigDecimal(promotion.optDouble("money")));
                item.setShop_rate(item.getDiscount_fee());
                ecoOrderdiscounts.add(item);
            }
            ecoOrderMapper.insertEcoOrderdiscount(ecoOrderdiscounts);
        }

    }

    public boolean isNumeric(String str) {
        for (int i = str.length(); --i >= 0;) {
            if (!Character.isDigit(str.charAt(i))) {
                return false;
            }
        }
        return true;
    }

    public void insertPayments(String outOrderIdInDB, TsFkfssdk fkfs, double actualmoney, String addtime, String tradeno, String outtradeno) {
        BtPayments btpay = new BtPayments();
        btpay.setYddh(outOrderIdInDB);
        btpay.setPay_channel("XCX");
        btpay.setPay_name(fkfs.getFkfsmc1());
        btpay.setPay_no(fkfs.getFkfsbh());
        btpay.setVcardid("");
        btpay.setVtele(tradeno);
        btpay.setPay_count(actualmoney);
        btpay.setPay_bb(0);
        btpay.setVocount(0);
        btpay.setFzzhje(0);
        btpay.setFlhl(0);
        btpay.setPay_memo(outtradeno);
        btpay.setOpttime(addtime);
        // 生成预定付款记录
        firstPayMapper.insertBtPayments(btpay);
    }

    /**
     * 分摊会员优惠
     * @param kdzdbh
     */
    private void shareMemberPromotion(String kdzdbh){

       //查询微生活会员优惠的总金额
        double promotionCouponsAmount=acewillCouponTempMapper.getMemberPromotionAmount(kdzdbh);

        if (0 == promotionCouponsAmount) {
            return;
        }

        List<com.tzx.miniapp.rest.entity.TqWdk> tqWdks = tqWdkMapper.selectByPrimaryKey(kdzdbh);

        List<com.tzx.miniapp.rest.entity.TqWdk>  tqWdkList = tqWdks.stream().filter(t -> !CMSX_MX.equals(t.getCmsx()) && "Y".equals(t.getZkzt()))
                .sorted(Comparator.comparing(TqWdk::getSjje)).collect(Collectors.toList());

        final double totalSjje =tqWdkList.stream().mapToDouble(TqWdk::getSjje).sum();

        if(0==totalSjje){
            return;
        }

        double radio=BigDecimalUtil.divide(promotionCouponsAmount,totalSjje,4);
        Iterator<TqWdk> iterator = tqWdkList.iterator();

        double sumSjje=0d;

        while (iterator.hasNext()){

            TqWdk wdk=iterator.next();

            if (!iterator.hasNext()) {
                wdk.setSjje(ArithUtil.sub(ArithUtil.sub(totalSjje,promotionCouponsAmount),sumSjje));
            } else {
               wdk.setSjje(ArithUtil.sub(wdk.getSjje(),BigDecimalUtil.multiply(wdk.getSjje(),radio,2)));
               if(wdk.getSjje()<0){
                   wdk.setSjje(0d);
               }
               sumSjje=ArithUtil.add(sumSjje,wdk.getSjje());
            }
            wdk.setZrje(ArithUtil.sub(wdk.getCmje(),ArithUtil.add(wdk.getSjje(),wdk.getDpzkje())));

            tqWdkMapper.updateByPrimaryKeySelective(wdk);
        }

    }

    /**
     * 分摊套餐明细
     * @param kdzdbh
     */
    private void shareBillDetails(String kdzdbh) {
        //重新均摊tq_wdk套餐明细
        ////套餐主项实结金额
        Map<String,Double> tcSjje=new HashMap<>();
        //套餐明细分摊比例
        Map<String, Double> tcRadio = new HashMap<>();
        //套餐明细计数器
        Map<String, Integer> count = new HashMap<>();
        //明细合计
        Map<String, Double> mxSum = new HashMap<>();

        List<com.tzx.miniapp.rest.entity.TqWdk> tqWdks = tqWdkMapper.selectByPrimaryKey(kdzdbh);
        tqWdks.forEach(t -> {
            String key = t.getTcid() + "#" + t.getTcdch();
            if (CMSX_TC.equals(t.getCmsx())) {
                tcRadio.put(key, BigDecimalUtil.divide(t.getSjje(), t.getCmje(),10));
                tcSjje.put(key,t.getSjje());
            }
            if (CMSX_MX.equals(t.getCmsx())) {
                count.compute(key,(k, v) -> v == null ? 1 : v + 1);
            }
        });

        tqWdks.forEach(t -> {
            if (CMSX_MX.equals(t.getCmsx())) {
                String key = t.getTcid() + "#" + t.getTcdch();
                if (count.compute(key, (k, v) -> v - 1) == 0) {
                    if(!mxSum.containsKey(key)){
                        mxSum.put(key,0d);
                    }
                    t.setSjje(ArithUtil.sub(tcSjje.get(key), mxSum.get(key)));
                    mxSum.compute(key, (k, v) -> v == null ? t.getSjje() : ArithUtil.add(v, t.getSjje()));
                    assert (Double.compare(mxSum.get(key),tcSjje.get(key))==0);
                }else {
                    Double radio = tcRadio.get(key);
                    t.setSjje(BigDecimalUtil.multiply(radio, t.getCmje(),2));
                    mxSum.compute(key, (k, v) -> v == null ? t.getSjje() : ArithUtil.round(ArithUtil.add(v, t.getSjje()),2));
                }
            }
            t.setZrje(ArithUtil.sub(t.getCmje(),ArithUtil.add(t.getSjje(),t.getDpzkje())));
            tqWdkMapper.updateByPrimaryKeySelective(t);
        });
    }

}
