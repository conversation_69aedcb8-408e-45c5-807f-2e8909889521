package com.tzx.mobilepos.rest.service.impl;

import com.tzx.mobilepos.common.Constant;
import com.tzx.mobilepos.common.Data;
import com.tzx.mobilepos.common.enums.BasicDataMnum;
import com.tzx.mobilepos.rest.mapper.*;
import com.tzx.mobilepos.rest.model.TsGgcsk;
import com.tzx.mobilepos.rest.model.TsJthyzlk;
import com.tzx.mobilepos.rest.service.IMobilePosBasicDataService;
import com.tzx.mobilepos.rest.vo.*;
import com.tzx.publics.listener.InitDataListener;
import com.tzx.publics.util.DateUtil;
import com.tzx.publics.util.GsonUtil;
import com.tzx.publics.util.ParamUtil;
import com.tzx.publics.util.ReqDataUtil;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class MobilePosBasicDataServiceImpl implements IMobilePosBasicDataService {

	private final static Logger LOGGER = LoggerFactory.getLogger(MobilePosBasicDataServiceImpl.class);

	@Autowired
	private MobilePosTqClsdkMapper tqClsdkMapper;
	@Autowired
	private MobilePosTsCmkMapper tsCmkMapper;
	@Autowired
	private MobilePosTsTcmxkMapper tsTcmxkMapper;
	@Autowired
	private MobilePosTsTcfzmxkMapper tsTcfzmxkMapper;
	@Autowired
	private MobilePosTsFkfssdkMapper tsFkfssdkMapper;
	@Autowired
	private MobilePosTsKwsdkMapper tsKwsdkMapper;
	@Autowired
	private MobilePosClearDishMapper clearDishMapper;
	@Autowired
	private MobilePosCommonParamMapper commonParamMapper;
	@Autowired
	private MobilePosPartyMemberMapper partyMemberMapper;

	/**
	 * 数据同步
	 * 
	 * @param param
	 * @param result
	 * @return
	 */
	public String findBasicData(Data param, Data result) {
		Map<String, Object> paramMap = ReqDataUtil.getDataMap(param);
		String dataType = ParamUtil.getStringValue(paramMap, "data_type", false, null);

		List<BasicDataBack> bdList = new ArrayList<BasicDataBack>();
		BasicDataBack basicDataBack = new BasicDataBack();
		BasicDataMnum dataMnum = null;
		dataMnum = BasicDataMnum.valueOf(dataType.toUpperCase());
		switch (dataMnum) {
		case ALL:
			 basicDataBack.setDISH(getDishList("CPYSMXLB_CPXM"));
			 basicDataBack.setITEM_CLASS(getItemClassList());
			 basicDataBack.setCOMBO_DETAILS(getTsTcmxkList());
			 basicDataBack.setCOMBO_GROUP(getTsTcfzmxkList());
			 basicDataBack.setSPEC_DISH(getSpecDishList());
			 basicDataBack.setPAYMENT_WAY(getTsFkfssdkList());
			 basicDataBack.setDISCOUNT(getDishList("CPYSMXLB_YHFS"));
			 basicDataBack.setTASTE(getTasteList());
			 basicDataBack.setCLEAR_DISH(getClearDish());
			 basicDataBack.setCOMMON_PARAM(getCommonParam());
			 basicDataBack.setPARTY_MEMBER(getPartyMember());
			break;
		case DISH:
			 basicDataBack.setDISH(getDishList("CPYSMXLB_CPXM"));
			break;
		case ITEM_CLASS:
			basicDataBack.setITEM_CLASS(getItemClassList());
			break;
		case COMBO_DETAILS:
			basicDataBack.setCOMBO_DETAILS(getTsTcmxkList());
			break;
		case COMBO_GROUP:
			basicDataBack.setCOMBO_GROUP(getTsTcfzmxkList());
			break;
		case PAYMENT_WAY:
			basicDataBack.setPAYMENT_WAY(getTsFkfssdkList());
			break;
		case DISCOUNT:
			basicDataBack.setDISCOUNT(getDishList("CPYSMXLB_YHFS"));
			break;
		case TASTE:
			basicDataBack.setTASTE(getTasteList());
			break;
		case CLEAR_DISH:
			basicDataBack.setCLEAR_DISH(getClearDish());
			break;
		case COMMON_PARAM:
			basicDataBack.setCOMMON_PARAM(getCommonParam());
			break;
		case PARTY_MEMBER:
			basicDataBack.setPARTY_MEMBER(getPartyMember());
			break;
			
		default:
			break;
		}

		bdList.add(basicDataBack);
		result.setCode(Constant.CODE_SUCCESS);
		result.setMsg(Constant.BASICDATA_SUCCESS);
		result.setSuccess(true);
		GsonUtil.asyncResponseData(result, bdList);
		return GsonUtil.getResponseString();
	}

	/**
	 * 查询菜品
	 * 
	 * @return
	 */
	@Transactional
	public List<Dish> getDishList(String clmxlb) {
		int week = DateUtil.dayForWeekToDate(new Date(), 0);

		List<Dish> list = tsCmkMapper.findDishBasicData(clmxlb, week);
		//暂时不支持有项目组的套餐，所以这里做处理，凡是有项目组的套餐都不同步给app
//		Iterator<Dish> it = list.iterator();
//		of:while (it.hasNext()) {
//			Dish dish = it.next();
//			if ("CMSX_TC".equals(dish.getIs_combo())) {
//				List<TsTcmxk> tsTcmxk = tsCmkMapper.findTcmxByXmid(Integer.parseInt(dish.getItem_id()));
//				tf:for(TsTcmxk tcmx : tsTcmxk){
//					if("ERP_MXLX_GROUP".equals(tcmx.getMxlx())){
//						it.remove();
//						break tf;
//					}
//				}
//			}
//		}
//		
		return list;
	}

	/**
	 * 查询菜类
	 * 
	 * @return
	 */
	@Transactional
	public List<ItemClass> getItemClassList() {
		List<ItemClass> list = tqClsdkMapper.findItemClassBasicData();
		return list;
	}
	
	/**
	 * 查询套餐
	 * 
	 * @return
	 */
	@Transactional
	public List<ComboDetails> getTsTcmxkList() {
		List<ComboDetails> list = tsTcmxkMapper.findTsTcmxkBasicData();
		return list;
	}
	
	/**
	 * 查询套餐分组
	 * 
	 * @return
	 */
	@Transactional
	public List<ComboGroup> getTsTcfzmxkList() {
		List<ComboGroup> list = tsTcfzmxkMapper.findTsTcfzmxkBasicData();
		return list;
	}
	
	/**
	 * 查询付款方式
	 * 
	 * @return
	 */
	@Transactional
	public List<PaymentWay> getTsFkfssdkList() {
		String posMemberType = InitDataListener.ggcsMap.get("POS_MEMBER_TYPE");
		if(null != InitDataListener.ggcsMap.get("QIMAITYPE") && "2".equals(InitDataListener.ggcsMap.get("QIMAITYPE"))){
			posMemberType = "0";
		}
		List<PaymentWay> list = tsFkfssdkMapper.findTsFkfssdkBasicData(posMemberType);
		PaymentWay pw = new PaymentWay();
		pw.setId("-1");
		pw.setPayno("000");
		pw.setPayment_name1("扫码支付(微信/支付宝)");
		pw.setPayment_class("FKSX_SMZF");
		pw.setShowxh("0");
		pw.setPayment_mark("FKSX_SMZF");
		list.add(pw);
		return list;
	}
	
	/**
	 * 查询口味备注
	 * 
	 * @return
	 */
	@Transactional
	public List<Taste> getTasteList() {
		List<Taste> list = tsKwsdkMapper.findTsKwsdkBasicData();
		return list;
	}
	
	/**
	 * 查询估清菜品
	 * @return
	 */
	@Transactional
	public List<ClearDish> getClearDish(){
		return clearDishMapper.findClearDish();
	}
	
	/**
	 * 查询公共参数
	 * @return
	 */
	@Transactional
	public List<TsGgcsk> getCommonParam(){
		return commonParamMapper.findCommonParam();
	}
	
	/**
	 * 查询集团会员
	 * @return
	 */
	@Transactional
	public List<TsJthyzlk> getPartyMember(){
		return partyMemberMapper.findPartyMember();
	}
	
	/**
	 * 查询多规格菜品
	 * 
	 * @return
	 */
	@Transactional
	public List<Dish> getSpecDishList() {
		List<Dish> list = tsCmkMapper.findSpecBasicData();
		return list;
	}
}
