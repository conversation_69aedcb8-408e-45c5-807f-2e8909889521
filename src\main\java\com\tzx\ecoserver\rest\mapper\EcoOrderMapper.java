package com.tzx.ecoserver.rest.mapper;

import com.tzx.ecoserver.rest.vo.*;
import com.tzx.miniapp.rest.model.TqJtztk;
import com.tzx.miniapp.rest.vo.TqYyddylsk;
import com.tzx.publics.base.MyMapper;

import org.apache.ibatis.annotations.Param;
import org.joda.time.DateTime;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by Zhoux<PERSON> on 2019-10-26.
 */
public interface EcoOrderMapper extends MyMapper<EcoBtYdd> {
    public void clearYdd(@Param("yddh") String yddh);

    public void clearYdxm1(@Param("yddh") String yddh);

    public void clearYdxm2(@Param("yddh") String yddh);

    public void clearYddTcSelectMx(@Param("yddh") String yddh);

    public void clearYddActive(@Param("yddh") String yddh);

    public void clearEcoOrderdiscount(@Param("yddh") String yddh);


    public int insertBtYdd(@Param("by") EcoBtYdd by);
    public int insertBtYdxm2(List<EcoBtYdxm2 > byxm2List);
    public int insertEcoOrderdiscount(List<EcoOrderdiscount> ecoOrderdiscounts);
    public String getbillid(@Param("skjh") String skjh,@Param("tname") String tname,@Param("fname") String fname);
    public int ecoOrderToBill(@Param("syydh") String syydh, @Param("akdzdbh") String akdzdbh, @Param("ajzzdbh") String ajzzdbh,
                              @Param("alsdh") String alsdh, @Param("abbbc") Integer abbbc, @Param("abbrq") Date abbrq, @Param("aczry") String aczry,
                              @Param("askjh") String askjh, @Param("aygdlcs") Integer aygdlcs);
    public int getYddCountByYddh(@Param("yddh") String yddh);
    public List<Map<String,String>> findDishInfos(List<String> wherestr);
    public List<Map<String,String>> findDishTcInfos(List<String> wherestr);
    public TqJtztk getJtZtk(@Param("bbrq") Date bbrq);
    public List<Map<String,String>> getBtYdd(@Param("yddh") String yddh);
    public int cancelecoBill(@Param("syydh") String syydh, @Param("akdzdbh") String akdzdbh, @Param("ajzzdbh") String ajzzdbh,
                              @Param("alsdh") String alsdh, @Param("abbbc") Integer abbbc, @Param("abbrq") Date abbrq, @Param("aczry") String aczry,
                              @Param("askjh") String askjh, @Param("aygdlcs") Integer aygdlcs, @Param("acancelreason") String acancelreason);
    public List<Map<String,String>> getGGCS(@Param("sdbt") String sdbt);
    public List<Map<String,String>> getZdByYddh(@Param("yddh") String yddh);
    public int singlecancelecobill(@Param("syydh") String syydh, @Param("akdzdbh") String akdzdbh, @Param("ajzzdbh") String ajzzdbh,
                             @Param("alsdh") String alsdh, @Param("abbbc") Integer abbbc, @Param("abbrq") Date abbrq, @Param("aczry") String aczry,
                             @Param("askjh") String askjh, @Param("aygdlcs") Integer aygdlcs, @Param("acancelreason") String acancelreason);
    public int insertBtYdxm1(List<EcoBtYdxm1> byxm1List);
    public List<Map<String,String>> getPreZDKByYddh(@Param("yddh") String yddh);
    public List<Map<String,String>> getPreZDLSKByYddh(@Param("yddh") String yddh);
    public int cancelecoBillhis(@Param("syydh") String syydh, @Param("akdzdbh") String akdzdbh, @Param("ajzzdbh") String ajzzdbh,
                             @Param("alsdh") String alsdh, @Param("abbbc") Integer abbbc, @Param("abbrq") Date abbrq, @Param("aczry") String aczry,
                             @Param("askjh") String askjh, @Param("aygdlcs") Integer aygdlcs, @Param("acancelreason") String acancelreason);
    public int singlecancelecobillhis(@Param("syydh") String syydh, @Param("akdzdbh") String akdzdbh, @Param("ajzzdbh") String ajzzdbh,
                                   @Param("alsdh") String alsdh, @Param("abbbc") Integer abbbc, @Param("abbrq") Date abbrq, @Param("aczry") String aczry,
                                   @Param("askjh") String askjh, @Param("aygdlcs") Integer aygdlcs, @Param("acancelreason") String acancelreason);
    public List<Map<String,String>> getZdByYddhhis(@Param("yddh") String yddh);
    public List<Map<String,Object>> getYdxm2ByYddh(@Param("yddh") String yddh);
    public List<Map<String,Object>> findDishTcInfosObj(List<String> wherestr);

    public int getErrorYddCountByYddh(@Param("yddh") String yddh);
    public int insertErrorYdd(@Param("by") EcoErrorYdd by);
    public int insertErrorYdxm2(List<EcoErrorYdxm2 > byxm2List);
    public int insertErrorOrderdiscount(List<EcoErrorOrderdiscount> ecoOrderdiscounts);
    public int sendErrorPrint(@Param("yddh") String yddh);
    public List<Map<String,String>> getErrorYdd(@Param("yddh") String yddh);
    public int cancelEcoErrorBill(@Param("syydh") String syydh);
    public int insertErrorYdxm1(List<EcoErrorYdxm1> byxm1List);
    public int singleCancelEcoErrorBill(@Param("syydh") String syydh);
    public int updateYddPackageBoxFee(@Param("PackageBoxFee") Double PackageBoxFee,@Param("yddh") String yddh);
    public int insertBtPayments(List<EcoBtPayments> btPayments);
    public int insertBtPaymentsPart(List<EcoBtPayments> btPayments);
    public int insertErrBtPayments(List<EcoBtPayments> btPayments);

    public String callP_SendKVSData(@Param("szdbh") String szdbh);
    public int deleteKvsMXK(@Param("szdbh") String szdbh);
    public int updateKvsMxkTime();
    public int updateKvsChangeTime();
    public int updateKdsChangeTime();

    public int ecoOrderToBillNoItem(@Param("syydh") String syydh, @Param("akdzdbh") String akdzdbh, @Param("ajzzdbh") String ajzzdbh,
                              @Param("alsdh") String alsdh, @Param("abbbc") Integer abbbc, @Param("abbrq") Date abbrq, @Param("aczry") String aczry,
                              @Param("askjh") String askjh, @Param("aygdlcs") Integer aygdlcs);

    public int singlecancelecobillNoItem(@Param("syydh") String syydh, @Param("akdzdbh") String akdzdbh, @Param("ajzzdbh") String ajzzdbh,
                                   @Param("alsdh") String alsdh, @Param("abbbc") Integer abbbc, @Param("abbrq") Date abbrq, @Param("aczry") String aczry,
                                   @Param("askjh") String askjh, @Param("aygdlcs") Integer aygdlcs, @Param("acancelreason") String acancelreason);

    public int singlecancelecobillNoItemHis(@Param("syydh") String syydh, @Param("akdzdbh") String akdzdbh, @Param("ajzzdbh") String ajzzdbh,
                                      @Param("alsdh") String alsdh, @Param("abbbc") Integer abbbc, @Param("abbrq") Date abbrq, @Param("aczry") String aczry,
                                      @Param("askjh") String askjh, @Param("aygdlcs") Integer aygdlcs, @Param("acancelreason") String acancelreason);

    public String getkdzdbhByYddh(@Param("yddh") String yddh);

    public int insertTqYyddylsk(@Param("yyddyls") TqYyddylsk yyddyls);

    /**
     * 批量插入套餐选择明细
     */
    int insertBtTcSelectMx(@Param("list") List<BtTcSelectMx> list);
}
