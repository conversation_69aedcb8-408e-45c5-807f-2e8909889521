package com.tzx.miniapp.rest.vo;

import java.io.Serializable;

public class ZsDishPackDetailsEntity implements Serializable {

	private static final long serialVersionUID = 1L;

	private Integer type;// 1:套餐单品 2 套餐分组
	private String detailsId;// 菜品/分组ID
	private String unitId;// 商品规格id
	private Integer changeNumState;// 是否可选，0：否，1：是
	private Integer prodCount;// 数量
	private Integer groupnum;// -1：必选，-2：不限， > 0 分组
	private Integer selnum;// 最多可选份数

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public String getDetailsId() {
		return detailsId;
	}

	public void setDetailsId(String detailsId) {
		this.detailsId = detailsId;
	}

	public String getUnitId() {
		return unitId;
	}

	public void setUnitId(String unitId) {
		this.unitId = unitId;
	}

	public Integer getChangeNumState() {
		return changeNumState;
	}

	public void setChangeNumState(Integer changeNumState) {
		this.changeNumState = changeNumState;
	}

	public Integer getProdCount() {
		return prodCount;
	}

	public void setProdCount(Integer prodCount) {
		this.prodCount = prodCount;
	}

	public Integer getGroupnum() {
		return groupnum;
	}

	public void setGroupnum(Integer groupnum) {
		this.groupnum = groupnum;
	}

	public Integer getSelnum() {
		return selnum;
	}

	public void setSelnum(Integer selnum) {
		this.selnum = selnum;
	}

}