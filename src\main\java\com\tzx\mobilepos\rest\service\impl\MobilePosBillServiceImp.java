package com.tzx.mobilepos.rest.service.impl;

import com.alibaba.druid.util.Base64;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.exceptions.UnirestException;
import com.tzx.commapi.common.CommApiException;
import com.tzx.commapi.rest.service.ICommApiService;
import com.tzx.commapi.rest.service.ISaleOutApiService;
import com.tzx.commapi.rest.service.IUseYhfsApiService;
import com.tzx.commapi.rest.vo.CommApiData;
import com.tzx.commapi.rest.vo.ReqParam;
import com.tzx.commapi.rest.vo.UseYhfsParam;
import com.tzx.mobilepos.common.Constant;
import com.tzx.mobilepos.common.Data;
import com.tzx.mobilepos.common.SysDictionary;
import com.tzx.mobilepos.common.enums.PaymentType;
import com.tzx.mobilepos.rest.mapper.*;
import com.tzx.mobilepos.rest.model.*;
import com.tzx.mobilepos.rest.service.IMobilePosBillService;
import com.tzx.mobilepos.rest.vo.*;
import com.tzx.publics.common.BillNoData;
import com.tzx.publics.listener.InitDataListener;
import com.tzx.publics.util.*;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.codehaus.plexus.util.StringUtils;
import org.jdom2.Document;
import org.jdom2.Element;
import org.jdom2.input.SAXBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.xml.sax.InputSource;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;
import java.io.StringReader;
import java.math.BigDecimal;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Service
@Scope("prototype")
public class MobilePosBillServiceImp implements IMobilePosBillService {
    private final static Logger LOGGER = LoggerFactory.getLogger(MobilePosBillServiceImp.class);

    @Autowired
    private MobilePosTqZdkMapper tqZdkMapper;
    @Autowired
    private MobilePosTqJtztkMapper tqJtztkMapper;
    @Autowired
    private MobilePosTqThirdExceptOrderMapper tqThirdExceptOrderMapper;
    @Autowired
    private MobilePosTqYhMtCouponsMapper tqYhMtCouponsMapper;
    @Autowired
    private MobilePosTqYhMtCouponsTempMapper tqYhMtCouponsTempMapper;
    @Autowired
    private MobilePosAcewillCouponTempMapper acewillCouponTempMapper;
    @Autowired
    private MobilePosAcewillMemberinfoMapper acewillMemberinfoMapper;
    @Autowired
    private MobilePosTsFkfssdkMapper tsFkfssdkMapper;
    // 实时沽清
    @Autowired
    private ISaleOutApiService saleOutApiService;
    // 不计收入付款
    @Autowired
    private IUseYhfsApiService useYhfsApiService;
    @Autowired
    private ICommApiService commApiService;

    private static Map<String, Integer> numberMap = new ConcurrentHashMap<String, Integer>();

    static {
        Unirest.setTimeouts(3000, 5000);
    }

    /**
     * 创建账单
     */
//	@Transactional
    public void createBill(Data data, Data result, BillNoData billNoData) throws SystemException {
        Map<String, Object> map = ReqDataUtil.getDataMap(data);
        List<JSONObject> dataList = new ArrayList<JSONObject>();

        String jtbh = ParamUtil.getStringValue(map, "jtbh", false, null);// 机台号
        // String jtbm = ParamUtil.getStringValue(map, "jtbm", false, null);//
        // 机台编码
        String czybh = ParamUtil.getStringValue(map, "czybh", false, null);// 操作员编号
        String bcid = ParamUtil.getStringValue(map, "bcid", false, null);// 班次id
//		String kdzdbh = "0";
//		String lsdh = "0";
//		TqZdk tqZdks = tqZdkMapper.getZdCount(jtbh);
        // int ktbcid = tqZdkMapper.getBcid();
        int ktbcid = Integer.parseInt(bcid);

        Map<String, String> bbrqMap = tqJtztkMapper.findBbrq();
        String bbrq = DateUtil.getNowDateYYDDMM();
        if (null != bbrqMap && bbrqMap.size() != 0) {
            bbrq = bbrqMap.get("bbrq");
        }
        String appLogin = SysDictionary.LOGIN_STATE_ON;
        TsGgcsk ggcs = tqZdkMapper.getGgcs("MDYYMS");
        if (null != ggcs && "3".equals(ggcs.getSdnr())) {
            ktbcid = Integer.parseInt(bcid);
            appLogin = SysDictionary.APP_LOGIN;
        } else {
            TsBck bck = tqZdkMapper.getBcid();
            ktbcid = bck.getId();
            appLogin = SysDictionary.LOGIN_STATE_ON;
        }

        int ygdlcs = tqJtztkMapper.getYgdlcs(DateUtil.parseDate(bbrq), jtbh, czybh, appLogin);
//		if (null != tqZdks) {
//			kdzdbh = tqZdks.getKdzdbh();
//			lsdh = tqZdks.getLsdh();
//			tqThirdExceptOrderMapper.updateByRefund(kdzdbh, "8");
//			tqYhMtCouponsMapper.updateYhMtCouponsMapperToLogin("8", "n", kdzdbh);
//			tqZdkMapper.initZdk(kdzdbh + "", ktbcid, czybh, ygdlcs + "");
//			tqZdkMapper.delWdk(kdzdbh + "");
//			tqZdkMapper.delFkls(kdzdbh + "");
//			tqZdkMapper.delTpd(kdzdbh + "", 0, "");
//			acewillCouponTempMapper.delTwct(kdzdbh);
//			acewillCouponTempMapper.delTmi(kdzdbh);
//			acewillCouponTempMapper.delDsfls(kdzdbh, "ACEWILL");
//
//		} else {
        List<TqZdk> tqZdkList = tqZdkMapper.getZdWjList(jtbh);
        for (TqZdk tqZdks : tqZdkList) {
            List<ReqParam> reqParams = new ArrayList<ReqParam>();
            List<WdDishVo> asoList = tqZdkMapper.getAddSaleOutData(tqZdks.getKdzdbh());
            for (WdDishVo wdk : asoList) {
                ReqParam reqParam = new ReqParam();
                reqParam.setItem_id(wdk.getCmbh());
                reqParam.setCount(new BigDecimal(wdk.getCmsl()));
                reqParams.add(reqParam);
            }
            if (reqParams.size() > 0) {
                AddSaleOut(reqParams);
            }

            tqZdkMapper.delZdk(tqZdks.getKdzdbh());
            tqZdkMapper.delWdk(tqZdks.getKdzdbh());
            tqZdkMapper.delFkls(tqZdks.getKdzdbh());
        }
//			kdzdbh = createBh(jtbh, "TQ_ZDK", "KDZDBH");
//			lsdh = createBh(jtbh, "TQ_ZDK", "LSDH");

        TqZdk tqZdk = new TqZdk();
        tqZdk.setKdzdbh(billNoData.getKdzdbh());
        tqZdk.setLsdh(billNoData.getLsdh());
        tqZdk.setQch(billNoData.getQch());
        tqZdk.setJzzdbh(billNoData.getJzzdbh());
        tqZdk.setJzcs(0);
        tqZdk.setKtskjh(jtbh);
        tqZdk.setFwyh(czybh);
        tqZdk.setKtczry(czybh);
        tqZdk.setKtsj(new Date());
        tqZdk.setKdbbrq(DateUtil.parseDate(bbrq));
        tqZdk.setJzsx("ZDSX_WJ");
        tqZdk.setSource("APP");
        tqZdk.setCbid(-1);
        tqZdk.setXfks(1);
        tqZdk.setDyzdcs(1);
        tqZdk.setXsms("XSMS_TS");
        tqZdk.setYgdlcs(ygdlcs + "");
        tqZdk.setZkl(100);
        tqZdk.setYhfsbh(null);
        tqZdk.setKtbcid(ktbcid);
        tqZdk.setZdzt("");
        tqZdk.setCwlxbh("0");

        tqZdkMapper.insert(tqZdk);
//		}

        Map<String, String> mapr = new HashMap<String, String>();
        mapr.put("kdzdbh", billNoData.getKdzdbh());
        mapr.put("lsdh", billNoData.getLsdh());

        dataList.add(JSONObject.fromObject(GsonUtil.GsonString(mapr)));
        result.setData(dataList);
        result.setCode(Constant.CODE_SUCCESS);
        result.setMsg("生成账单成功");
        result.setSuccess(true);

    }

    public String subBh(String jtbh, long newBh) {
        String bhStr = newBh + "";
        return bhStr.substring(jtbh.length());
    }

    /**
     * 菜品/套餐下单
     */
    @Transactional
    public void ordering(Data param, Data result) throws Exception {
        int result_ = 0;
        try {
            List<JSONObject> dataList = new ArrayList<JSONObject>();
            Map<String, String> bbrqMap = tqJtztkMapper.findBbrq();
            List<BillItemVo> billItemList = new ArrayList<BillItemVo>();
            String bbrq = DateUtil.getNowDateYYDDMM();
            GroupDetails gd = new GroupDetails();
            List<ReqParam> reqParams = new ArrayList<ReqParam>();

            String appLogin = SysDictionary.LOGIN_STATE_ON;
            TsGgcsk ggcs = tqZdkMapper.getGgcs("MDYYMS");
            if (null != ggcs && "3".equals(ggcs.getSdnr())) {
                appLogin = SysDictionary.APP_LOGIN;
            } else {
                appLogin = SysDictionary.LOGIN_STATE_ON;
            }

            if (null != bbrqMap && bbrqMap.size() != 0) {
                bbrq = bbrqMap.get("bbrq");
            }
            for (JSONObject obj : ReqDataUtil.getDataJsonList(param)) {
                OrderVo orderVo = (OrderVo) JSONObject.toBean(obj, OrderVo.class);
                JSONArray item = obj.getJSONArray("item");
                JSONArray comb = null;
                int ygdlcs = tqJtztkMapper.getYgdlcs(DateUtil.parseDate(bbrq), orderVo.getJtbh(), orderVo.getWaiter_num(), appLogin);
                tqZdkMapper.updateKtczry(orderVo.getBill_num(), orderVo.getWaiter_num(), ygdlcs);
                for (int i = 0; i < item.size(); i++) {
                    JSONObject v = item.getJSONObject(i);

                    ReqParam reqParam = new ReqParam();
                    reqParam.setItem_id(v.optString("item_no"));
                    reqParam.setCount(new BigDecimal(v.optDouble("item_count")));
                    reqParams.add(reqParam);

                    if ("CMSX_TC".equals(v.optString("is_combo"))) {
                        result_ = tqZdkMapper.addTc(orderVo.getBill_num(), v.optInt("item_id"), v.optInt("item_count"));
                        tqJtztkMapper.deletetcdcxzlsk(orderVo.getBill_num(), null, null, null);
                        comb = v.optJSONArray("comboDetails");
                        if (comb.size() > 0) {
                            for (int j = 0; j < comb.size(); j++) {
                                JSONObject gro = comb.getJSONObject(j);
                                ComboDetails grd = GsonUtil.GsonToBean(gro.toString(), ComboDetails.class);
                                if (grd.getDetails_type().equals("ERP_MXLX_GROUP")) {
                                    if (null != grd.getGroupDetails() && grd.getGroupDetails().size() > 0) {
                                        for (GroupDetails gr : grd.getGroupDetails()) {
//											Integer tcid = tqZdkMapper.findTcInfo(v.optInt("item_id"));
                                            Integer tcid = 0;
                                            if ("Y".equals(v.optString("ifspec", "N"))) {
                                                tcid = v.optInt("item_id");
                                            } else {
                                                tcid = tqZdkMapper.findTcInfo(v.optInt("item_id"));
                                            }
                                            BeanUtils.copyProperties(tqZdkMapper.findItem(gr.getId(), tcid, Integer.parseInt(gro.optString("group_id"))), gd);
                                            gd.setTcid(tcid);
                                            gd.setTcxh(gro.optString("group_id"));
                                            gd.setXcsl("1");
                                            gd.setItem_count(gr.getItem_count());
                                            tqZdkMapper.addTcdcxzlsk(orderVo.getBill_num(), gd.getTcid(), Integer.parseInt(gd.getTcxh()), Integer.parseInt(gr.getItem_id()), Double.parseDouble(gd.getItem_count()), Integer.parseInt(gd.getXcsl()), Double.parseDouble(gd.getSjjg()), Double.parseDouble(gd.getSjjg()), Double.parseDouble(gd.getFzje()), Integer.parseInt(gd.getMxlxid()));
                                            // 为套餐可选项增加餐盒,目前无需求，暂时屏蔽
//											if ("XSMS_WM".equals(orderVo.getSale_mode())) {
//												tqZdkMapper.addCm(orderVo.getBill_num(), gr.getFoodboxid(), 1, orderVo.getJtbh(), "", "", Math.random() * 10000 + "", "", 6);
//											}

                                            ReqParam reqParamG = new ReqParam();
                                            reqParamG.setItem_id(gr.getItem_no());
                                            reqParamG.setCount(new BigDecimal(ArithUtil.mul(Double.parseDouble(gr.getItem_count()), v.optDouble("item_count"))));
                                            reqParams.add(reqParamG);
                                        }
                                    }
                                } else {
                                    // 为套餐固定项增加餐盒,目前无需求，暂时屏蔽
//									if ("XSMS_WM".equals(orderVo.getSale_mode()) && grd.getDetails_type().equals("ERP_MXLX_SINGLE")) {
//										tqZdkMapper.addCm(orderVo.getBill_num(), gro.optInt("foodboxid"), gro.optInt("item_count"), orderVo.getJtbh(), "", "", Math.random() * 10000 + "", "", 6);
//									}

                                    ReqParam reqParamS = new ReqParam();
                                    reqParamS.setItem_id(gro.optString("item_no"));
                                    reqParamS.setCount(new BigDecimal(ArithUtil.mul(gro.optDouble("item_count"), v.optDouble("item_count"))));
                                    reqParams.add(reqParamS);
                                }
                            }
                        }
                    }
                    int atype = 0;
                    if ("Y".equals(v.optString("ifspec", "N"))) {
                        atype = 6;
                    } else {
                        atype = 0;
                    }
                    result_ = tqZdkMapper.addCm(orderVo.getBill_num(), v.optInt("item_id"), v.optInt("item_count"), orderVo.getJtbh(), "", "", Math.random() * 10000 + "", v.optString("item_taste"), atype);
//					result_ = tqZdkMapper.addCm(orderVo.getBill_num(), v.optInt("item_id"), v.optInt("item_count"), orderVo.getJtbh(), "", "", Math.random() * 10000 + "", v.optString("item_taste"), 0);

                    // 为单品增加餐盒,目前无需求，暂时屏蔽
//					if("XSMS_WM".equals(orderVo.getSale_mode()) && !"CMSX_TC".equals(v.optString("is_combo"))){
//						tqZdkMapper.addCm(orderVo.getBill_num(), v.optInt("foodboxid"), v.optInt("item_count"), orderVo.getJtbh(), "", "", Math.random() * 10000 + "", "", 6);
//					}

                    if (result_ != 0) {
                        break;
                    }
                }

                Data dataDSO = new Data();
                DecSaleOut(dataDSO, reqParams);
                if (!dataDSO.isSuccess()) {
                    result_ = -3;
                    tqZdkMapper.delWdk(orderVo.getBill_num());
                    result.setMsg("下单失败:" + dataDSO.getMsg());
                }

                // 此处计算了抹零金额，所以把计算提前到查询账单之前
                BillMoney bm = getBillMoney1(orderVo.getBill_num());

                BilledOrderVo billOrder = tqZdkMapper.queryBillInfo(orderVo.getJtbh(), orderVo.getBill_num()).get(0);
                for (BillItemVo biv : tqZdkMapper.queryBillDetailByKdzdbh(orderVo.getBill_num(), null, "", 0, 0)) {
                    if (!"Y".equals(biv.getSfxsmx())) {
                        biv.setBillItemVo(tqZdkMapper.queryBillDetailByKdzdbh(orderVo.getBill_num(), biv.getClmxid(), "Y", biv.getDcxh(), biv.getSyyhfkfsid()));
                    }
                    billItemList.add(biv);
                }
                tqZdkMapper.updateXsms(orderVo.getBill_num(), orderVo.getSale_mode(), orderVo.getQch());
                tqZdkMapper.updateJjcrwid(orderVo.getBill_num());

                if (bm.getZt() == 0) {
                    bm.setBilledOrderVo(billOrder);
                    billOrder.setBillItemVo(billItemList);
                    billOrder.setActualAmount(bm.getActual_money());
                    billOrder.setZkje(bm.getDiscount_money());
                    dataList.add(JSONObject.fromObject(GsonUtil.GsonString(billOrder)));
                } else {
                    result_ = -1;
                }

                if (result_ == 0 || result_ == 1) {
                    result.setData(dataList);
                } else if (result_ == -502) {
                    String gqcmmc = tqZdkMapper.getGqplk(orderVo.getBill_num());
                    result.setMsg("账单中包含已被沽清菜品《" + gqcmmc + "》，请重新选择！");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            result_ = -1;
            result.setMsg("系统异常");
            LOGGER.error("下单报错:{}", e);
        } finally {
            result.setCode(-1);
            if (result_ == 0 || result_ == 1) {
                result.setCode(Constant.CODE_SUCCESS);
                result.setMsg(Constant.ORDER_DISH_SUCCESS);
            } else if (result_ == -1) {
                result.setMsg("没有未结账单记录");
            } else if (result_ == -2) {
                result.setMsg("菜品不存在，请重新同步菜品后重新下单");
            } else if (result_ == -3) {

            } else if (result_ == -6) {
                result.setMsg("账单结账属性不正确");
            } else if (result_ == -502) {
                result.setCode(result_);
            } else if (result_ == -300) {
                result.setMsg("套餐无明细，请确认后重新下单");
            }
        }
    }

    /**
     * 优惠活动校验
     */
    @Transactional
    public void checkDiscount(Data param, Data result) throws Exception {
        for (JSONObject obj : ReqDataUtil.getDataJsonList(param)) {
            String kdzdbh = obj.optString("bill_num");
            int discount_id = obj.optInt("discount_id");
            String yhsx = obj.optString("yhsx");
            Data result1 = checkDiscount1(kdzdbh, discount_id, yhsx);

            result.setData(result1.getData());
            result.setCode(result1.getCode());
            result.setMsg(result1.getMsg());
            result.setSuccess(result1.isSuccess());
        }

//		int result_ = -1;
//		JSONObject robj = new JSONObject();
//		List<JSONObject> dataList = new ArrayList<JSONObject>();
//		try {
//			for (JSONObject obj : ReqDataUtil.getDataJsonList(param)) {
//				String kdzdbh = obj.optString("bill_num");
//				int discount_id = obj.optInt("discount_id");
//				String yhsx = obj.optString("yhsx");
//				Integer icbid = tqZdkMapper.findIcibd("TS");
//				String sjgtxbh = tqZdkMapper.findJgtxbh(kdzdbh);
//				TqZdk zdk = tqZdkMapper.getZdk(kdzdbh);
//				List<BuywdandYhitems> buywd = tqZdkMapper.getBuywdandYhitems(kdzdbh, discount_id, yhsx, icbid, zdk.getXsms(), sjgtxbh);
//				List<BuywdandYhitems> yhneed = tqZdkMapper.getYhneedChoitems(discount_id, yhsx, icbid, zdk.getXsms(), sjgtxbh);
//
//				int buydishrel =  tqZdkMapper.getBuydishrel(discount_id);
//
//				if (buydishrel == 1) {
//					for (BuywdandYhitems buywds : buywd) {
//						double nac = Double.parseDouble(buywds.getNeedaddcmsl());
//						if (nac != 0) {
//							result_ = -1;
//							break;
//						} else {
//							result_ = 0;
//						}
//
//					}
//				} else if (buydishrel == 0) {
//					if (null == buywd) {
//						result_ = -1;
//					} else {
//						result_ = 0;
//					}
//				}
//
//				if ("16".equals(yhsx) && null != buywd) {
//					yhneed = buywd;
//					result_ = 0;
//				}
//
//				if (result_ == 0) {
//					robj.put("discount_id", discount_id);
//					robj.put("discounttypeid", yhsx);
//					robj.put("buydishrel", buydishrel);
//					robj.put("buywd", buywd);
//					robj.put("yhneed", yhneed);
//					dataList.add(JSONObject.fromObject(GsonUtil.GsonString(robj)));
//				}
//			}
//			result.setData(dataList);
//			result.setCode(result_);
//			result.setMsg(result_ == 0 ? "优惠可用" : "优惠方式不满足限制条件！");
//			result.setSuccess(result_ == 0);
//		} catch (Exception e) {
//			e.printStackTrace();
//			result_ = -1;
//			result.setMsg("系统异常");
//			LOGGER.error("验证优惠方式错误:{}", e.getMessage());
//		}
    }

    /**
     * 使用优惠活动
     */
    @Transactional
    public void discountOrder(Data param, Data result) throws Exception {
        int result_ = 0;
        try {
            List<BillItemVo> billItemList = new ArrayList<BillItemVo>();
            List<JSONObject> dataList = new ArrayList<JSONObject>();
            Data result1 = new Data();
            for (JSONObject obj : ReqDataUtil.getDataJsonList(param)) {
                OrderVo orderVo = (OrderVo) JSONObject.toBean(obj, OrderVo.class);
                JSONArray item = obj.getJSONArray("item");
                List<ItemVo> items = (List<ItemVo>) JSONArray.toCollection(item, ItemVo.class);
                String kdzdbh = orderVo.getBill_num();
                boolean isDis = true;
                for (ItemVo v : items) {
                    if (v.getItem_count() > 0) {
                        result1 = checkDiscount1(kdzdbh, v.getDiscount_id(), v.getDiscounttypeid());
                        if (result1.getCode() == 0) {
                            List<JSONObject> data1 = (List<JSONObject>) result1.getData();
                            for (JSONObject disJo : data1) {
                                JSONArray buywd = disJo.optJSONArray("buywd");
                                if (buywd.size() == 0) {
                                    isDis = false;
                                }
                            }
                        }
                    }
                }
                if (isDis) {
                    for (ItemVo v : items) {
                        if (-110 == v.getDiscount_id()) {
                            String yhje = obj.getString("yhje");
                            tqZdkMapper.delMantualZrAmount(kdzdbh);
                            tqZdkMapper.insertMantualZrAmount(kdzdbh, Double.parseDouble(yhje));
                        }
                        if ("21".equals(v.getDiscounttypeid()) || "11".equals(v.getDiscounttypeid())
                                || "10".equals(v.getDiscounttypeid()) || "18".equals(v.getDiscounttypeid())
                                || "19".equals(v.getDiscounttypeid()) || "17".equals(v.getDiscounttypeid())
                                || "22".equals(v.getDiscounttypeid()) || "16".equals(v.getDiscounttypeid())
                                || "23".equals(v.getDiscounttypeid())) {
                            JSONArray wdandyh = obj.getJSONArray("wdandyh");
                            JSONArray yhmain = obj.getJSONArray("yhmain");
                            JSONArray yhitem = obj.getJSONArray("yhitem");
                            tqZdkMapper.delWdandYhTempItem(kdzdbh);
                            tqZdkMapper.delYhMainTempItem(kdzdbh);
                            tqZdkMapper.delYhTempItem(kdzdbh);
                            for (int i = 0; i < wdandyh.size(); i++) {
                                JSONObject whObj = wdandyh.getJSONObject(i);
                                int cmsl = whObj.optInt("cmsl");
                                if (v.getBuydishrel() == 1) {
                                    List<TqWdk> tqWdkList = tqZdkMapper.queryWdkByCmid(kdzdbh, whObj.optInt("clmxid"));
//                                    List<TqWdk> tqWdkList = tqZdkMapper.queryWdkByCmid(kdzdbh, whObj.optInt("rwid"));
                                    for (TqWdk t : tqWdkList) {
                                        if (t.getCmsl() >= whObj.optInt("cmsl")) {
                                            tqZdkMapper.insertWdandYhTempItem(v.getDiscount_id(), whObj.optInt("clmxid"), kdzdbh, whObj.optString("isneedadd"), cmsl, whObj.optInt("usetag"), t.getRwid());
                                        } else {
                                            cmsl = cmsl - t.getCmsl();
                                            tqZdkMapper.insertWdandYhTempItem(v.getDiscount_id(), whObj.optInt("clmxid"), kdzdbh, whObj.optString("isneedadd"), t.getCmsl(), whObj.optInt("usetag"), t.getRwid());
                                        }
                                    }
                                } else {
                                    tqZdkMapper.insertWdandYhTempItem(v.getDiscount_id(), whObj.optInt("clmxid"), kdzdbh, whObj.optString("isneedadd"), cmsl, whObj.optInt("usetag"), whObj.optInt("rwid"));
                                }
                            }
                            for (int i = 0; i < yhmain.size(); i++) {
                                JSONObject ymObj = yhmain.getJSONObject(i);
                                tqZdkMapper.insertYhMainTempItem(v.getDiscount_id(), kdzdbh, ymObj.optInt("yhtimes"), ymObj.optInt("paramtype"));
                            }
                            for (int i = 0; i < yhitem.size(); i++) {
                                JSONObject yiObj = yhitem.getJSONObject(i);
                                tqZdkMapper.insertYhTempItem(v.getDiscount_id(), yiObj.optInt("clmxid"), kdzdbh, yiObj.optString("isgift"), yiObj.optInt("cmsl"));
                            }
                        }

                        if (v.getItem_count() > 0) {
                            result_ = tqZdkMapper.addCm(kdzdbh, v.getItem_id(), v.getItem_count(), orderVo.getJtbh(), "", "", Math.random() * 10000 + "", v.getItem_taste(), 0);
                            if ("51".equals(v.getDiscounttypeid())) {
                                String cno = obj.getString("cno");
                                acewillMemberinfoMapper.updateUsaByCardcode(kdzdbh, cno, 1);
                            }
                        } else {
                            TqWdk tqWdk = tqZdkMapper.queryWdkByRwid(v.getRwid());
                            if (!"81".equals(tqWdk.getYhfs())) {
                                result_ = tqZdkMapper.cancelJd(kdzdbh, v.getRwid(), v.getItem_count(), orderVo.getJtbh(), "", 1);
                                if ("51".equals(tqWdk.getYhfs())) {
                                    acewillMemberinfoMapper.updateUsaByCardcode(kdzdbh, "", 0);
                                }
                            } else {
                                result_ = -3;
                            }
                        }

                        tqZdkMapper.zRtr(kdzdbh);        //分摊优惠
                        //调用均摊存储过程
                        if (result_ != 0) {
                            break;
                        }
                    }
                    if (result_ == 0) {
                        // 此处计算了抹零金额，所以把计算提前到查询账单之前
                        BillMoney bm = getBillMoney1(kdzdbh);

                        BilledOrderVo billOrder = tqZdkMapper.queryBillInfo(orderVo.getJtbh(), kdzdbh).get(0);
                        for (BillItemVo biv : tqZdkMapper.queryBillDetailByKdzdbh(kdzdbh, null, "", 0, 0)) {
                            if (!"Y".equals(biv.getSfxsmx())) {
                                biv.setBillItemVo(tqZdkMapper.queryBillDetailByKdzdbh(kdzdbh, biv.getClmxid(), "Y", biv.getDcxh(), biv.getSyyhfkfsid()));
                            }
                            billItemList.add(biv);
                        }

                        if (bm.getZt() == 0) {
                            bm.setBilledOrderVo(billOrder);
                            billOrder.setBillItemVo(billItemList);
                            billOrder.setActualAmount(bm.getActual_money());
                            billOrder.setZkje(bm.getDiscount_money());
                            dataList.add(JSONObject.fromObject(GsonUtil.GsonString(billOrder)));
                            dataList.add(JSONObject.fromObject(GsonUtil.GsonString(bm)));
                        } else {
                            result_ = -2;
                        }
                    }
                } else {
                    result_ = -4;
                }
            }
            if (result_ == 0) {
                result.setData(dataList);
                result.setMsg(Constant.DISCOUNT_SUCCESS);
            } else if (result_ == -1) {
                result.setMsg("没有该优惠方式");
            } else if (result_ == -2) {
                result.setMsg("没有未结账单记录");
            } else if (result_ == -3) {
                result.setMsg("抹零不可取消");
            } else if (result_ == -4) {
                result.setMsg("网络异常，优惠数据错误，请重新下单");
            } else if (result_ == -9990) {
                result.setMsg("该营销活动不在活动可用日期时间范围, 请确认总部设置！");
            } else if (result_ == -9991) {
                result.setMsg("该营销活动不在活动可用星期范围, 请确认总部设置！");
            } else if (result_ == -9999) {
                result.setMsg("该营销活动不满足当下条件，需要更新！");
            } else if (result_ == -9998) {
                result.setMsg("该营销活动未设置任何限制条件, 请确认总部设置！");
            } else if (result_ == -9997) {
                result.setMsg("该营销活动限制条件设置过多, 请确认总部设置！");
            } else if (result_ == -9996) {
                result.setMsg("该营销活动折扣方式设置错误, 请确认总部设置！");
            } else if (result_ == -9995) {
                result.setMsg("该营销活动未找到, 请确认总部设置！");
            } else if (result_ == -9994) {
                result.setMsg("账单非未结账单, 请刷新点菜界面重试！");
            } else if (result_ == -9901) {
                result.setMsg("该营销活动限制金额设置错误, 请确认总部设置！");
            } else if (result_ == -9902) {
                result.setMsg("该营销活动账单金额不满足活动限制金额条件, 请确认总部设置！");
            } else if (result_ == -9903) {
                result.setMsg("该营销活动未设置赠品或加价购菜品, 请确认总部设置！");
            } else if (result_ == -9904) {
                result.setMsg("该营销活动赠品或加价购菜品餐类明细不全, 请确认总部设置！");
            } else if (result_ == -9905) {
                result.setMsg("该营销活动加价购加价金额设置错误, 请确认总部设置！");
            } else if (result_ == -9906) {
                result.setMsg("该营销活动最低限额设置错误, 请确认总部设置！");
            } else if (result_ == -9907) {
                result.setMsg("该营销活动最高限额设置错误, 请确认总部设置！");
            } else if (result_ == -9908) {
                result.setMsg("该营销活动概率设置错误, 请确认总部设置！");
            } else if (result_ == -9909) {
                result.setMsg("该营销活动最低限额不能大于最高限额, 请确认总部设置！");
            } else if (result_ == -9941) {
                result.setMsg("该营销活动未找到赠品缓存, 请确认数据库连接是否正常！");
            } else if (result_ == -9942) {
                result.setMsg("该营销活动未找到加价购菜品品缓存, 请确认数据库连接是否正常！");
            } else if (result_ == -9943) {
                result.setMsg("折让金额缓存错误！");
            } else if (result_ == -9944) {
                result.setMsg("折让金额不能大于尚未使用活动的菜品的金额！");
            } else if (result_ == -9801) {
                result.setMsg("该营销活动活动菜品餐类明细不全, 请确认总部设置！");
            } else if (result_ == -9802) {
                result.setMsg("该营销活动活动菜品数量设置错误, 请确认总部设置！");
            } else if (result_ == -9803) {
                result.setMsg("该营销活动未设置任何活动菜品, 请确认总部设置！");
            } else if (result_ == -9804) {
                result.setMsg("该营销活动活动菜品设置过多, 最多只能设置一份, 请确认总部设置！");
            } else if (result_ == -9805) {
                result.setMsg("该营销活动活动活动菜品数量设置太少, 至少三份, 请确认总部设置！");
            } else if (result_ == -9806) {
                result.setMsg("该营销活动活动优惠金额设置错误, 请确认总部设置！");
            } else if (result_ == -9807) {
                result.setMsg("该营销活动活动折扣率设置错误, 请确认总部设置！");
            } else if (result_ == -9808) {
                result.setMsg("该营销活动赠品或加价购菜品餐类明细不全, 请确认总部设置！");
            } else if (result_ == -9809) {
                result.setMsg("该营销活动未设置赠品或加价购菜品, 请确认总部设置！");
            } else if (result_ == -9810) {
                result.setMsg("该营销活动加价购加价金额设置错误, 请确认总部设置！");
            } else if (result_ == -9811) {
                result.setMsg("该营销活动不能有重复的活动菜品, 请确认总部设置！");
            } else if (result_ == -9841) {
                result.setMsg("该营销活动未找到活动主表缓存, 请确认数据库连接是否正常！");
            } else if (result_ == -9842) {
                result.setMsg("该营销活动未找到活动明细表缓存, 请确认数据库连接是否正常！");
            } else if (result_ == -9843) {
                result.setMsg("该营销活动未找到赠品或加价购菜品明细表缓存, 请确认数据库连接是否正常！");
            } else if (result_ == -9844) {
                result.setMsg("该营销活动菜目总金额错误, 请确认数据库连接是否正常！");
            } else if (result_ == -9845) {
                result.setMsg("该营销活动未找到要免的菜品, 请确认总部设置！");
            } else if (result_ == -9701) {
                result.setMsg("该营销活动国定折扣的折扣率设置错误, 请确认总部设置！");
            } else if (result_ == -9702) {
                result.setMsg("该营销活动折扣方式设置错误, 请确认总部设置！");
            } else if (result_ == -9703) {
                result.setMsg("该营销活动折扣方案的折扣方案编号错误, 请确认总部设置！");
            } else if (result_ == -9704) {
                result.setMsg("该营销活动折扣方案的对应的折扣率部分设置错误, 请确认总部设置！");
            } else if (result_ == -9705) {
                result.setMsg("该营销活动未找到对应的折扣方案, 请确认总部设置！");
            } else if (result_ == -9706) {
                result.setMsg("该营销活动没有满足该打折活动的菜品, 请先点菜再使用该营销活动！");
            }

        } catch (Exception e) {
            e.printStackTrace();
            result_ = -1;
            result.setMsg("系统异常");
            LOGGER.error("优惠报错:{}", e.getMessage());
        } finally {
            result.setCode(result_ == 0 ? Constant.CODE_SUCCESS : -1);
            result.setSuccess(result_ == 0);
        }
    }

    /**
     * 支付
     *
     * @param param
     * @param result
     * @return
     */
    public void payment(Data param, Data result) {
        Map<String, Object> paramMap = ReqDataUtil.getDataMap(param);
        String dataType = ParamUtil.getStringValue(paramMap, "data_type", false, null);// 支付类型
        String payNum = ParamUtil.getStringValue(paramMap, "pay_num", false, null);// 付款支付码
        String zdbh = ParamUtil.getStringValue(paramMap, "zdbh", false, null);// 账单编号
        String jtbh = ParamUtil.getStringValue(paramMap, "jtbh", false, null);// 机台编号
        String date_time = ParamUtil.getStringValue(paramMap, "date_time", false, null);
        String skyh = ParamUtil.getStringValue(paramMap, "skyh", false, null);// 收款人账号
        String fkje = ParamUtil.getStringValue(paramMap, "fkje", false, null);// 付款金额
        int jzid = ParamUtil.getIntegerValue(paramMap, "jzid", false, null);// 付款方式id
        String pay_type = ParamUtil.getStringValue(paramMap, "pay_type", false, null);// 付款方式类型
        String payment_mark = ParamUtil.getStringValue(paramMap, "payment_mark", false, null);// 付款方式来源
        String fklsid = ParamUtil.getStringValue(paramMap, "fklsid", false, null);// 付款流水id
        String yzm = ParamUtil.getStringValue(paramMap, "yzm", false, null);// 美团验证码
        String fkbz = ParamUtil.getStringValue(paramMap, "fkbz", false, null);// 付款标志
        double qt_open_amount = ParamUtil.getDoubleValue(paramMap, "open_amount", false, null);// 前台返回的未收金额
        String optype = ParamUtil.getStringValue(paramMap, "optype", false, null);// 第三方优惠类型
        String outtradenoOld = ParamUtil.getStringValue(paramMap, "outtradeno", false, null);// 抖音券第三方编码

        PaymentWay pw = tqZdkMapper.getPaymentWay(jzid);
        PayResultData payResultData = new PayResultData();
        PaymentType pt = null;
        pt = PaymentType.valueOf(dataType.toUpperCase());
        double sjzdje = 0d;
        double sjyfje = 0d;
        sjzdje = tqZdkMapper.getZdje(zdbh);
        sjyfje = tqZdkMapper.getFkje(zdbh);
        TqZdk zdk = tqZdkMapper.getZdk(zdbh);
        double sj_open_amount = DoubleUtil.sub(sjzdje, sjyfje);
        int zdmxc = tqZdkMapper.getZdmx(zdbh);

        if (null == skyh || "".equals(skyh)) {
            result.setCode(-1);
            result.setMsg("登录人员丢失，请返回重新登录！");
            result.setSuccess(false);
        } else if (sjzdje == sjyfje && !"CANCEL".equals(pt.toString())) {
            payResultData.setOpen_amount("0");
            payResultData.setStatus("0");
            payResultData.setFkls(tqZdkMapper.getFklsList(zdbh));
            List<CouponRunningWater> crw = new ArrayList<CouponRunningWater>();
            List<CouponRunningWater> crw4 = tqZdkMapper.getCouponlsList(zdbh, "4");
            List<CouponRunningWater> crw6 = tqZdkMapper.getCouponlsList(zdbh, "6");
            crw.addAll(crw4);
            crw.addAll(crw6);
            payResultData.setCouponls(crw);
            List<JSONObject> dataList = new ArrayList<JSONObject>();
            dataList.add(JSONObject.fromObject(GsonUtil.GsonString(payResultData)));
            result.setData(dataList);
            result.setCode(0);
            result.setMsg("支付成功！");
            result.setSuccess(true);
        } else if ("ZDSX_YJ".equals(zdk.getJzsx())) {
            result.setCode(-102);
            result.setMsg("账单《" + zdbh + "》已关闭，请返回首页查询该帐单！");
            result.setSuccess(false);
        } else if (zdmxc == 0) {
            result.setCode(-101);
            result.setMsg("账单明细数据异常，请返回首页重新下单！");
            result.setSuccess(false);
        } else if (sj_open_amount != qt_open_amount) {
            payResultData.setOpen_amount(sj_open_amount + "");
            payResultData.setFkls(tqZdkMapper.getFklsList(zdbh));
            List<CouponRunningWater> crw = new ArrayList<CouponRunningWater>();
            List<CouponRunningWater> crw4 = tqZdkMapper.getCouponlsList(zdbh, "4");
            List<CouponRunningWater> crw6 = tqZdkMapper.getCouponlsList(zdbh, "6");
            crw.addAll(crw4);
            crw.addAll(crw6);
            payResultData.setCouponls(crw);
            List<JSONObject> dataList = new ArrayList<JSONObject>();
            dataList.add(JSONObject.fromObject(GsonUtil.GsonString(payResultData)));
            result.setData(dataList);
            result.setCode(0);
            result.setMsg("账单数据变动，已刷新账单金额与付款列表，请确认！");
            result.setSuccess(true);
        } else {
            Boolean fkfalg = false;
            Boolean tkfalg = true;
            if (null == fkje) {
                fkje = "0";
            }
            BigDecimal actualmoney = new BigDecimal(fkje);
            int fksl = 1;
            String fkfsid = jzid + "";
            String mtYhfsid = null;
            String outtradeno = "";
            String lxdh = "";

            if ("CANCELCOUPON".equals(pt.toString())) {
                tqZdkMapper.delFkjeByFkhm(zdbh, yzm);
                // 恢复券码使用状态
                acewillCouponTempMapper.updateUsaByUseok(zdbh, yzm, 0);
                // 取消菜品券优惠
                tqZdkMapper.cancelThirdYhfs(zdbh, yzm, jzid);

                String wdrwid = acewillCouponTempMapper.getDiscountRwid(zdbh, Integer.parseInt(fklsid));
                if (null != wdrwid) {
                    tqZdkMapper.cancelJd(zdbh, Integer.parseInt(wdrwid), 1, jtbh, "", 1);
                }
                tqZdkMapper.findCalcMoney(zdbh);

                result.setCode(0);
                result.setMsg("取消成功！");
                result.setSuccess(true);
            } else if ("FKSX_SMZF".equals(pay_type) || "ERP_FKFS_MTZH".equals(payment_mark)) { //微信/支付宝 扫码支付
                String dsfyhMsg = "";
                String xmlStr = "";
                String yhsx = "-1";
                PaymentWay paymentWay = null;
                if ("ERP_FKFS_MTZH".equals(payment_mark)) {
                    paymentWay = checkType(payNum, "ERP_FKFS_MTZH");
                } else {
                    // 判断具体付款方式,微信 or 支付宝
                    paymentWay = checkType(payNum);
                }
                fkfsid = paymentWay.getId();

                Dish dsfyh = new Dish();
                boolean dsfyhF = false;
                if ("ERP_FKFS_ZFB".equals(paymentWay.getPayment_mark())) {
                    yhsx = "61";
                    dsfyhMsg = "请先添加“支付宝商家优惠”优惠方式！";
                    dsfyh = tqZdkMapper.getDsfyh(yhsx);
                    dsfyhF = true;
                } else if ("ERP_FKFS_WX".equals(paymentWay.getPayment_mark())) {
                    yhsx = "62";
                    dsfyhMsg = "请先添加“微信商家优惠”优惠方式！";
                    dsfyh = tqZdkMapper.getDsfyh(yhsx);
                    dsfyhF = true;
                } else if ("ERP_FKFS_MTZH".equals(paymentWay.getPayment_mark())) {
                    yhsx = "5";
                    dsfyhMsg = "请先添加“美团券”优惠方式！";
                    dsfyh = tqZdkMapper.getDsfyh(yhsx);
                    dsfyhF = true;
                }

                //判断是否启用支核一体
                boolean isEnablePv=checkIsEnablePv(paymentWay.getPayment_mark(), zdk);

                if(isEnablePv){

                    //请求总部支付
                    try {
//                        String url = "http://127.0.0.1:9595/zhi";
                        String url = InitDataListener.ggcsMap.get("URL_TZXINTERFACE")+"tzxPosSmartRestaurantOrder/tzxpay";

                        JSONObject bodyJson=new JSONObject();
                        JSONArray details=new JSONArray();
                        List<WdDishVo> wdvList = tqZdkMapper.getWdDishNew(zdbh);
                        TsPsjgsdk tsPsjgsdk = tqZdkMapper.getJg();
                        String jzbbrq = DateUtil.getNowDateYYDDMM();
                        Map<String, String> bbrqMap = tqJtztkMapper.findBbrq();
                        if (null != bbrqMap && bbrqMap.size() != 0) {
                            jzbbrq = bbrqMap.get("bbrq");
                        }

                        TsBck bck = tqZdkMapper.getBcid();
                        Integer bcid = bck.getId();

                        outtradeno =createOuttradeno("tbxf", tsPsjgsdk.getJgxh(), jtbh, zdbh, date_time);

                        if(null!=wdvList&&!wdvList.isEmpty()){
                            for(WdDishVo wdv:wdvList){
                                JSONObject detail=new JSONObject();
                                detail.put("itemcode",wdv.getCmbh());
                                detail.put("itemname",wdv.getCmmc1());
                                detail.put("quantity",wdv.getCmsl());
                                detail.put("price",wdv.getCmdj());
                                details.add(detail);
                            }
                        }
                        bodyJson.put("detail",details);
                        String type="";
                        switch (pt) {
                            case PAY:
                                type="setorder";
                                break;
                            case CANCEL:
                                type="cancel";
                                break;
                            case QUERY:
                                type="queryorder";
                                break;
                            default:
                                break;
                        }
                        bodyJson.put("type",type);
                        bodyJson.put("storecode", InitDataListener.ggcsMap.get("tzx_kcwy_storecode"));
                        bodyJson.put("storeid",tsPsjgsdk.getJgxh() );
                        bodyJson.put("channel","TS01" );
                        bodyJson.put("deviceno", jtbh);
                        bodyJson.put("authcode", payNum);
                        bodyJson.put("thirdorderno",  outtradeno);
                        bodyJson.put("totalamount",  fkje);
                        bodyJson.put("call_time",  DateUtil.getNowDateYYDDMMHHMMSS());
                        bodyJson.put("bbrq", jzbbrq);

                        String body = bodyJson.toString();
                        HttpResponse<String> response=  Unirest.post(url)
                                .header("Content-Type", "application/json")
                                .body(body).asString();
                        LOGGER.info("调用支核一体:{} ,\n请求参数：{},\n返回内容：{}",url,body,response.getBody());
                        if(200==response.getStatus()){
                            org.json.JSONObject jsonBody =new org.json.JSONObject(response.getBody());

                            if(0==jsonBody.getInt("codeType")){ //成功
                                String dataInfo=jsonBody.optString("dataInfo");
                                if("setorder".equals(type)||"queryorder".equals(type)){
                                    org.json.JSONArray jsonArray=new org.json.JSONArray(dataInfo);
                                    acewillCouponTempMapper.insertAcewilDealdetails(jsonBody.optString("cno"), zdk.getKdzdbh(),
                                            0, jsonBody.optDouble("balance"), 0, DateUtil.parseDate(jzbbrq), zdk.getFwyh(), zdk.getKtskjh(), 1,null);
                                    for(int i=0;i<jsonArray.length();i++) {
                                        org.json.JSONObject payJson = jsonArray.getJSONObject(i);

                                        fkfsid = payJson.optString("payid");
                                        String userid=payJson.optString("userid");
                                        double sjje = payJson.optDouble("sjje");
                                        double yhje = payJson.optDouble("yhje");

                                        UseYhfsParam useYhfsParam = new UseYhfsParam();
                                        useYhfsParam.setBillid(zdk.getKdzdbh());
                                        useYhfsParam.setOpType(1002);
                                        useYhfsParam.setYhfsId(payJson.optInt("yhid"));
//                                    useYhfsParam.setFklsid(Integer.parseInt(fklsid));
                                        useYhfsParam.setSkjh(jtbh);
                                        useYhfsParam.setJgtxbh(InitDataListener.organVo.getJgtxbh());// 价格体系编号
                                        useYhfsParam.setDisAmount(new BigDecimal(yhje));
                                        useYhfsParam.setBbrq(DateUtil.parseDate(jzbbrq));

                                        if(yhje>0){
                                            useYhfsApiService.CommUseYhfs(useYhfsParam);
                                        }

                                        tqZdkMapper.accountsOrder(zdbh, Integer.parseInt(fkfsid), new BigDecimal(sjje), fksl, payNum, "", lxdh, outtradeno, jtbh, skyh);

                                        tqZdkMapper.updatePqhm(zdbh,-1,outtradeno);
                                        tqZdkMapper.insertTqBillOtherInfo(zdbh,userid,outtradeno,skyh,jtbh,DateUtil.parseDate(jzbbrq));
                                        tqZdkMapper.insertTqCommThirdOrder(zdbh,DateUtil.parseDate(jzbbrq),payNum,fkfsid,outtradeno,sjje);

//                                      closeBill(zdbh, fkfsid, actualmoney, payNum, lxdh, outtradeno, jtbh, skyh, bcid);
                                    }

                                    //当 couponcode 不为空 ，且 yhid  对应企迈会员优惠券 时候 ，会记录 成优惠券的优惠信息，写到明细表  ，且记录  tq_memberinfo信息
                                    if("1".equals(jsonBody.getString("memberType"))&&"4".equals(InitDataListener.ggcsMap.get("POS_MEMBER_TYPE"))){ //是企迈会员
                                        tqZdkMapper.insertTqMemberInfo(zdbh,payNum,jsonBody.optString("cno"),jsonBody.optString("phone"));
                                    }

                                    if("1".equals(jsonBody.getString("memberType"))){
                                        acewillMemberinfoMapper.updateZdkByCwlxbh(zdbh, InitDataListener.ggcsMap.get("POS_MEMBER_TYPE"));
                                    }

                                }
                                double yfje =  tqZdkMapper.getFkje(zdbh);
                                double zdje =tqZdkMapper.getZdje(zdbh);
                                payResultData.setOpen_amount(DoubleUtil.sub(zdje, yfje) + "");
                                payResultData.setFkls(tqZdkMapper.getFklsList(zdbh));
                                List<CouponRunningWater> crw = new ArrayList<CouponRunningWater>();
                                List<CouponRunningWater> crw4 = tqZdkMapper.getCouponlsList(zdbh, "4");
                                List<CouponRunningWater> crw6 = tqZdkMapper.getCouponlsList(zdbh, "6");
                                crw.addAll(crw4);
                                crw.addAll(crw6);
                                payResultData.setCouponls(crw);
                                payResultData.setStatus("0");
                                payResultData.setMsg("付款成功！");
                                List<JSONObject> dataList = new ArrayList<JSONObject>();
                                dataList.add(JSONObject.fromObject(GsonUtil.GsonString(payResultData)));
                                result.setData(dataList);

                                result.setCode(0);
                                result.setMsg("支付成功！");
                                result.setSuccess(true);
                            } /*else if (1 == jsonBody.getInt("codeType")) { //失败
                            } */
                            //处理中
                            else if (2 == jsonBody.getInt("codeType")) {
                                payResultData.setStatus("2");
                                payResultData.setMsg("支付结果查询中，请等待...");

                                List<JSONObject> dataList = new ArrayList<JSONObject>();
                                dataList.add(JSONObject.fromObject(GsonUtil.GsonString(payResultData)));

                                result.setData(dataList);
                                result.setCode(0);
                                result.setMsg("支付结果查询中，请等待...");
                                result.setSuccess(true);

                            } else {
                                throw new CommApiException(jsonBody.getString("codeErrorInfo"));
                            }
                        }else {
                            throw new CommApiException("总部接口响应失败,Status="+response.getStatus());
                        }

                    } catch (Exception e) {
                        if(e instanceof CommApiException){
                            payResultData.setStatus("1");
                            payResultData.setMsg(e.getMessage());
                            result.setCode(0);
                            result.setMsg(payResultData.getMsg());
                            result.setSuccess(false);
                        }
                        if(e instanceof UnirestException){
                            result.setCode(0);
                            result.setMsg("网络请求失败，请重试！\n"+e.getMessage());
                            result.setSuccess(true);
                        }
                        LOGGER.error("支核一体支付失败!",e);
                    }
                    return;
                }


                List<WdDishVo> wdvList = new ArrayList<WdDishVo>();
                if (null != dsfyh || !dsfyhF) {
                    String rifUrl = tqZdkMapper.getRifUrl();
                    if ("0".equals(fkfsid)) {

                    } else if (!"-1".equals(fkfsid) && !"0".equals(fkfsid)) {
                        TsPsjgsdk tsPsjgsdk = tqZdkMapper.getJg();
                        if ("ERP_FKFS_MTZH".equals(paymentWay.getPayment_mark())) {
                            wdvList = tqZdkMapper.getWdDishMtZh(zdbh);
                        } else {
                            // 因为之前传菜品实结单价错误传成了  菜品实结金总价，总部根据客户序号（李先生=88）做了单独处理，
                            // 现在需要更正，但是李先生不好更新，所以做了单独处理
                            if ("88".equals(InitDataListener.ggcsMap.get("KHXH"))) {
                                wdvList = tqZdkMapper.getWdDish(zdbh);
                            } else {
                                wdvList = tqZdkMapper.getWdDishNew(zdbh); //查询账单明细
                            }
                        }
                        switch (pt) {
                            // 只有支付且每次支付，都会创建一条新纪录
                            case PAY:
                                fkfalg = false;
                                xmlStr = jointXML("01", paymentWay, zdbh, payNum, jtbh, date_time, tsPsjgsdk, fkje, wdvList); //组装请求参数 TODO
                                insertTteo(paymentWay, zdbh, payNum, jtbh, date_time, tsPsjgsdk, fkje); //保存付款记录到tq_third_except_order
                                break;
                            // 取消不需要直接调用接口了，写库就可以pos端会自动轮询第三方支付表，自动退款
                            case CANCEL:
                                fkfalg = false;
                                xmlStr = "02";
                                break;
                            // 查询后，如果是支付成功，需要更新状态 状态 为空：未知 1：支付中 0：成功
                            case QUERY:
                                fkfalg = false;
                                xmlStr = jointXML("03", paymentWay, zdbh, payNum, jtbh, date_time, tsPsjgsdk, fkje, wdvList);
                                break;
                            default:
                                break;
                        }
                        LOGGER.info("requestXml:" + xmlStr);
//						outtradeno = tsPsjgsdk.getJgxh() + zdbh + date_time;
                        outtradeno = createOuttradeno("tbxf", tsPsjgsdk.getJgxh(), jtbh, zdbh, date_time);
                        String rxmlStr = "";
                        if ("02".equals(xmlStr)) {
                            rxmlStr = "-2";
                        } else {
                            rxmlStr = SendRequest.sendPost(rifUrl, xmlStr); //发送支付请求 TODO
                        }
                        LOGGER.info("responseXml:" + rxmlStr);
                        if ("-1".equals(rxmlStr)) {
                            payResultData.setStatus("-1");
                            payResultData.setMsg("总部接口异常！");
                            result.setCode(Constant.CODE_PARAM_FAILURE);
                            result.setSuccess(false);
                        } else if ("timedout".equals(rxmlStr)) {
                            payResultData.setStatus("2");
                            payResultData.setMsg("网络请求超时，请稍后再试！");
                            result.setCode(0);
                            result.setMsg("网络请求超时，请稍后再试！");
                            result.setSuccess(true);
                        } else if ("-2".equals(rxmlStr)) {  //取消付款
                            if (null == date_time || "".equals(date_time)) {
                                outtradeno = tqZdkMapper.getOuttradeno(fklsid);
                            }

                            tqThirdExceptOrderMapper.updateToStatus(outtradeno, "8");
                            // 取消第三方优惠
//							tqYhMtCouponsTempMapper.cancelThirdYhfs(zdbh, outtradeno);
                            if (dsfyhF) {
                                tqZdkMapper.cancelThirdYhfs(zdbh, outtradeno, Integer.parseInt(dsfyh.getYhfsid()));
                            }
                            tqZdkMapper.findCalcMoney(zdbh);
                            payResultData.setStatus("0");
                            payResultData.setMsg("退款成功！");
                            result.setCode(Constant.CODE_SUCCESS);
                            result.setSuccess(true);
                        } else {
                            try {
                                readStringXmlOut(rxmlStr);  //解析付款请求结果 TODO
                            } catch (Exception e) {
                                payResultData.setStatus("-1");
                                payResultData.setMsg("返回数据异常！");
                                List<JSONObject> dataList = new ArrayList<JSONObject>();
                                dataList.add(JSONObject.fromObject(GsonUtil.GsonString(payResultData)));
                                result.setData(dataList);
                                result.setCode(Constant.CODE_PARAM_FAILURE);
                                result.setSuccess(false);
                                e.printStackTrace();
                            }
                            payResultData = tlPrd.get();
                            result.setCode(Constant.CODE_SUCCESS);
                            result.setSuccess(true);

                            lxdh = payResultData.getTradeno();
                            // 处理结果：0成功，其他失败(1：交易出现错误，2：下单成功，支付处理中3：该交易已关闭)
                            if ("0".equals(payResultData.getStatus())) { //处理付款请求结果 TODO
                                String yhfsid = payResultData.getYhfsid();
                                if ("ERP_FKFS_WX".equals(paymentWay.getPayment_mark())) {
                                    yhfsid = payResultData.getWxyhfsid();
                                } else if ("ERP_FKFS_MTZH".equals(paymentWay.getPayment_mark())) {
                                    yhfsid = dsfyh.getYhfsid();
                                }
                                fkfalg = true;
                                if (null != yhfsid && !"".equals(yhfsid) && !"null".equals(yhfsid) && !"NULL".equals(yhfsid)) {
                                    BigDecimal pa = new BigDecimal(payResultData.getPrivilegeamount());
                                    tqZdkMapper.delYhtemp(zdbh);
                                    TqYhMtCouponsTemp tymct = new TqYhMtCouponsTemp();
                                    tymct.setYhfsid(Integer.parseInt(dsfyh.getYhfsid()));
                                    tymct.setClmxid(-1);
                                    tymct.setKdzdbh(zdbh);
                                    tymct.setPayableamt(new BigDecimal(-1));
                                    tymct.setPqlx("2");
                                    tymct.setYzm(outtradeno);
                                    tymct.setJzid(-1);
                                    tymct.setOptype(2);//美团21
                                    tymct.setSjje(new BigDecimal(-1));
                                    tymct.setCmid(-1);
                                    tymct.setBuyprice(pa.abs());
                                    if ("ERP_FKFS_MTZH".equals(paymentWay.getPayment_mark())) {
                                        fkfalg = false;
                                        // 先处理买单的支付
                                        tqZdkMapper.accountsOrder(zdbh, Integer.parseInt(fkfsid), new BigDecimal(payResultData.getFkje()), fksl, payNum, "", lxdh, outtradeno, jtbh, skyh);
                                        // 再处理券产生的支付与优惠
                                        tymct.setOptype(21);//美团21
                                        List<PayResultCoupon> couponlist = payResultData.getCouponlist();
                                        for (PayResultCoupon coupon : couponlist) {
                                            String couponYzm = coupon.getYzm();
                                            //COUPONBUYPRICE	购买价
                                            //DEALPRICE		面值
                                            //PAYABLEAMT	应付
                                            if ("true".equals(coupon.getVoucher())) {
                                                BigDecimal buyprice = new BigDecimal(coupon.getCouponBuyPrice());
                                                tymct.setPqlx("1");//0:美团菜品券  1:美团现金券
                                                tymct.setClmxid(-1);
                                                tymct.setCmid(-1);
                                                tymct.setBuyprice(buyprice.abs());
                                                tymct.setSjje(new BigDecimal(0));
                                            } else {
                                                for (WdDishVo wdv : wdvList) {
                                                    if (coupon.getCmbh().equals(wdv.getCmbh()) && wdv.getIcmsl() > 0) {
                                                        tymct.setPqlx("0");
                                                        tymct.setClmxid(Integer.parseInt(wdv.getClmxid()));
                                                        tymct.setCmid(Integer.parseInt(wdv.getCmid()));
                                                        tymct.setWdrwid(wdv.getWdrwid());
                                                        tymct.setSjje(new BigDecimal(wdv.getSjje()));
                                                        int cmsl = wdv.getIcmsl() - 1;
                                                        wdv.setCmsl(cmsl + "");
                                                    }
                                                }
                                            }
                                            String couponbuyprice = coupon.getCouponBuyPrice();
                                            if (null == couponbuyprice || "null".equals(couponbuyprice)) {
                                                couponbuyprice = "0";
                                            }
                                            tymct.setBuyprice(new BigDecimal(couponbuyprice));
                                            tymct.setPayableamt(new BigDecimal(coupon.getPayableamt()));
                                            tymct.setYzm(couponYzm);

                                            tqYhMtCouponsTempMapper.deleteByZdbh(zdbh);
                                            tqYhMtCouponsTempMapper.insert(tymct);
                                            tqZdkMapper.addCm(zdbh, -1, 1, jtbh, "", "", Math.random() * 10000 + "", dsfyh.getYhfsid(), 0);
                                            tqZdkMapper.zRtr(zdbh);
                                            tqZdkMapper.findCalcMoney(zdbh);
                                            PaymentWay mtPw = tsFkfssdkMapper.getFkfssdkByType("FKSX_DSF", "ERP_FKFS_MT");
                                            tqZdkMapper.accountsOrder(zdbh, Integer.parseInt(mtPw.getId()), new BigDecimal(couponbuyprice), fksl, couponYzm, "", "", outtradeno, jtbh, skyh);

                                        }
                                    } else {
                                        tqYhMtCouponsTempMapper.deleteByZdbh(zdbh);
                                        tqYhMtCouponsTempMapper.insert(tymct);
                                        tqZdkMapper.addCm(zdbh, -1, 1, jtbh, "", "", Math.random() * 10000 + "", dsfyh.getYhfsid(), 0);
                                        tqZdkMapper.zRtr(zdbh);
                                        tqZdkMapper.findCalcMoney(zdbh);
                                    }
                                    actualmoney = new BigDecimal(payResultData.getFkje());
                                }
                                tqThirdExceptOrderMapper.updateToStatus(outtradeno, "0"); //更新tq_third_except_order
                                // 微信支付宝成功后，调用集点接口；
                                if ("1".equals(InitDataListener.ggcsMap.get("ISUSEJKB"))) {

                                }
                            } else if ("2".equals(payResultData.getStatus())) {
                                tqThirdExceptOrderMapper.updateToStatus(outtradeno, "1");
                                if (null == payResultData.getMsg()
                                        || "null".equals(payResultData.getMsg())
                                        || "Null".equals(payResultData.getMsg())
                                        || "NULL".equals(payResultData.getMsg())) {
                                    result.setMsg("请等待...");
                                    payResultData.setMsg("请等待...");
                                } else {
                                    result.setMsg(payResultData.getMsg());
                                }
                            } else if ("1".equals(payResultData.getStatus()) || "3".equals(payResultData.getStatus())) {
                                result.setCode(-1);
                                result.setMsg(payResultData.getMsg());
                                result.setSuccess(false);
                                tqThirdExceptOrderMapper.updateToStatus(outtradeno, "3");
                            } else {
                                tqThirdExceptOrderMapper.updateToStatus(outtradeno, "");
                            }
                            payResultData.setJzid(fkfsid);
                            List<JSONObject> dataList = new ArrayList<JSONObject>();
                            dataList.add(JSONObject.fromObject(GsonUtil.GsonString(payResultData)));
                            result.setData(dataList);
                        }
                    } else {
                        payResultData.setStatus("-1");
                        payResultData.setMsg("没有此支付方式！");
                        //这里删除美团券流水会有问题
//						tqYhMtCouponsMapper.deleteYhMtCouponsMapper(zdbh, null);
                        result.setCode(-1);
                        result.setMsg("没有此支付方式！");
                        result.setSuccess(false);
                    }
                } else {
                    result.setCode(-1);
                    result.setMsg(dsfyhMsg);
                    result.setSuccess(false);
                }
            } else if ("FKSX_DSF".equals(pay_type) && "ERP_FKFS_MT".equals(payment_mark)) {
                String xmlStr = "";
                mtYhfsid = tqZdkMapper.findYhfsid("5"); // 美团券优惠属性为5
                List<WdDishVo> wdv = tqZdkMapper.getWdMtDish(zdbh);
                String rifUrl = tqZdkMapper.getRifUrl();
                TsPsjgsdk tsPsjgsdk = tqZdkMapper.getJg();
                if (StringUtils.isNotBlank(mtYhfsid)) {
                    switch (pt) {
                        case PAY:
                            xmlStr = this.getMeituanXml(wdv, mtYhfsid, tsPsjgsdk.getJgxh() + "", yzm, "01", tsPsjgsdk, zdbh, "");
                            insetTqYhMtCoupons("n", null, new BigDecimal(0), Integer.parseInt(mtYhfsid), -1, zdbh, yzm, "0", jzid, null, "");
                            mtQuan(wdv, rifUrl, xmlStr, result, zdbh, dataType.toUpperCase(), Integer.parseInt(mtYhfsid), jtbh, yzm, skyh, fksl, pay_type, fkfsid, fklsid, jzid, "5", ""); // 美团验券相关
                            break;
                        case CANCEL:
                            // yzm = tqYhMtCouponsMapper.findMtYzm(new BigDecimal(fkje),Integer.parseInt(mtYhfsid),"0",zdbh).get(0);
                            if (!StringUtils.isNotBlank(yzm)) {
                                result.setCode(-1);
                                result.setMsg("此订单不能反核销美团券！");
                                result.setSuccess(false);
                            } else {
                                xmlStr = this.getMeituanXml(wdv, mtYhfsid, tsPsjgsdk.getJgxh() + "", yzm, "02", tsPsjgsdk, zdbh, "");
                                mtQuan(wdv, rifUrl, xmlStr, result, zdbh, dataType.toUpperCase(), Integer.parseInt(mtYhfsid), jtbh, yzm, skyh, fksl, pay_type, fkfsid, fklsid, jzid, "5", ""); // 美团验券相关
                            }
                            break;
                        default:
                            break;
                    }
                } else {
                    result.setCode(-1);
                    result.setMsg("暂不支持美团验券，请先添加美团券优惠方式！");
                    result.setSuccess(false);
                }
            } else if ("FKSX_DSF".equals(pay_type) && "ERP_FKFS_DYQ".equals(payment_mark)) {
                String xmlStr = "";
                mtYhfsid = tqZdkMapper.findYhfsid("96"); // 抖音券优惠属性为5
                List<WdDishVo> wdv = tqZdkMapper.getWdMtDish(zdbh);
                String rifUrl = tqZdkMapper.getRifUrl();
                TsPsjgsdk tsPsjgsdk = tqZdkMapper.getJg();
                if (StringUtils.isNotBlank(mtYhfsid)) {
                    switch (pt) {
                        case PAY:
//							possys.EnvirVars.Values['SYSFDJGXH'] + InitParamList.Values['BillId'] + FormatDateTime('yyyyMMddHHmmss', Now);
                            String outtradenoNew = tsPsjgsdk.getJgxh() + zdbh + DateUtil.getYYYYMMDDHHMMSS(new Date());
                            xmlStr = this.getMeituanXml(wdv, mtYhfsid, tsPsjgsdk.getJgxh() + "", yzm, "01", tsPsjgsdk, zdbh, outtradenoNew);
                            insetTqYhMtCoupons("n", null, new BigDecimal(0), Integer.parseInt(mtYhfsid), -1, zdbh, yzm, "0", jzid, null, outtradenoNew);
                            dyQuan(wdv, rifUrl, xmlStr, result, zdbh, dataType.toUpperCase(), Integer.parseInt(mtYhfsid), jtbh, yzm, skyh, fksl, pay_type, fkfsid, fklsid, jzid, "96", outtradenoNew); // 美团验券相关
                            break;
                        case CANCEL:
                            // yzm = tqYhMtCouponsMapper.findMtYzm(new BigDecimal(fkje),Integer.parseInt(mtYhfsid),"0",zdbh).get(0);
                            if (!StringUtils.isNotBlank(yzm)) {
                                result.setCode(-1);
                                result.setMsg("此订单不能反核销抖音券！");
                                result.setSuccess(false);
                            } else {
                                outtradenoOld = yzm;
                                xmlStr = this.getMeituanXml(wdv, mtYhfsid, tsPsjgsdk.getJgxh() + "", yzm, "02", tsPsjgsdk, zdbh, outtradenoOld);
                                dyQuan(wdv, rifUrl, xmlStr, result, zdbh, dataType.toUpperCase(), Integer.parseInt(mtYhfsid), jtbh, yzm, skyh, fksl, pay_type, fkfsid, fklsid, jzid, "96", outtradenoOld); // 美团验券相关
                            }
                            break;
                        default:
                            break;
                    }
                } else {
                    result.setCode(-1);
                    result.setMsg("暂不支持抖音验券，请先添加抖音券优惠方式！");
                    result.setSuccess(false);
                }
            } else if ("FKSX_HYK".equals(pay_type) && ("2010".equals(pw.getPayno()) || "2001".equals(pw.getPayno())
                    || "2002".equals(pw.getPayno()) || "1971".equals(pw.getPayno()) || "1972".equals(pw.getPayno())
                    || "1973".equals(pw.getPayno()))) {
                switch (pt) {
                    case PAY:
                        // 企迈预消费，每次涉及企迈消费都要做一次预消费
                        if ("1971".equals(pw.getPayno())) {
                            TqAcewilCouponCache tzcc = acewillCouponTempMapper.getCouponByCode(zdbh, yzm);
                            Map<String, String> ccMap = checkCouponQm(tzcc);
                            if ("0".equals(ccMap.get("code"))) {
                                JSONObject ua = qimaiPreview(zdbh, yzm, 0);
                                if ("0".equals(ua.optString("code"))) {
                                    tqZdkMapper.delTqWdkCouponTemp(zdbh, "");
                                    JSONObject resObj = ua.optJSONObject("data");
                                    double couponDiscount = resObj.optDouble("coupon_discount");
                                    double financePayment = resObj.optDouble("finance_payment", 0);
//									couponDiscount = ArithUtil.sub(couponDiscount, financePayment);
//									if (couponDiscount > sj_open_amount) {
//										result.setCode(-1);
//										result.setMsg("折扣金额大于应付金额，请先取消部分付款！");
//										result.setSuccess(false);
//									}

                                    if (ArithUtil.sub(couponDiscount, financePayment) < 0) {
                                        result.setCode(-1);
                                        result.setMsg("结算金额大于优惠金额，此券不可用！");
                                        result.setSuccess(false);
                                    } else {
                                        couponDiscount = ArithUtil.sub(couponDiscount, financePayment);
                                        fkfsid = acewillCouponTempMapper.getFkfsid(pw.getPayno()) + "";
                                        tqZdkMapper.insertTqWdkCouponTempQm(zdbh, yzm, couponDiscount, financePayment);
                                        actualmoney = new BigDecimal(financePayment);
                                        int amr = tqZdkMapper.addCm(zdbh, -1, 1, jtbh, "", yzm, Math.random() * 10000 + "", tzcc.getYhfsid() + "", 0);
                                        if (amr == 0) {
                                            tqZdkMapper.zRtr(zdbh);
                                            tqZdkMapper.findCalcMoney(zdbh);
                                            payNum = yzm;
                                            fkfalg = true;
                                            // 使用完成后更改使用状态
                                            acewillCouponTempMapper.updateUsaByUseok(zdbh, yzm, 1);
                                            // 预核销成功后，把该券所属卡以外的所有卡包含的券，全部置为不可用,卡可用状态也同样修改
                                            acewillCouponTempMapper.updateUsaByCardcode(zdbh, tzcc.getCardcode(), 1);
                                            acewillMemberinfoMapper.updateUsaByCardcode(zdbh, tzcc.getCardcode(), 1);
                                            result.setCode(0);
                                            result.setMsg("支付成功");
                                            result.setSuccess(true);
                                        } else {
                                            result.setCode(-1);
                                            result.setMsg("支付失败");
                                            result.setSuccess(false);
                                        }
                                    }
                                } else {
                                    result.setCode(-1);
                                    result.setMsg(ua.optString("message"));
                                    result.setSuccess(false);
                                }
                            } else {
                                result.setCode(-1);
                                result.setMsg(ccMap.get("msg"));
                                result.setSuccess(false);
                            }
                        } else if ("2010".equals(pw.getPayno())) {
                            TqAcewilCouponCache tzcc = acewillCouponTempMapper.getCouponByCode(zdbh, yzm);
                            Map<String, String> ccMap = ccMap = checkCoupon(tzcc);
                            if ("0".equals(ccMap.get("code"))) {
                                // 先清除在插入
                                tqZdkMapper.delTqWdkCouponTemp(zdbh, yzm);
                                double couponprice = tzcc.getCouponprice();
                                double couponsale = tzcc.getCouponsale();
                                if (!"DISHDISCOUNTCOUPON".equals(tzcc.getCoupontype())) {
                                    couponprice = ArithUtil.sub(couponprice, couponsale);
                                }
                                tqZdkMapper.insertTqWdkCouponTemp(zdbh, yzm, couponprice);
//								tqZdkMapper.delWdandYhTempItem(kdzdbh);
//								tqZdkMapper.insertWdandYhTempItem(v.getDiscount_id(), whObj.optInt("clmxid"), kdzdbh, whObj.optString("isneedadd"), cmsl, whObj.optInt("usetag"), t.getRwid());
                                actualmoney = new BigDecimal(tzcc.getCouponsale());
                                int amr = tqZdkMapper.addCm(zdbh, -1, 1, jtbh, "", yzm, Math.random() * 10000 + "", tzcc.getYhfsid() + "", 0);
                                if (amr == 0) {
                                    tqZdkMapper.zRtr(zdbh);
                                    tqZdkMapper.findCalcMoney(zdbh);
//									if ("CASHCOUPON".equals(tzcc.getCoupontype())) {
//										double yfje1 = tqZdkMapper.getFkje(zdbh);
//										double zdje1 = tqZdkMapper.getZdje(zdbh);
//										double open_amount1 = DoubleUtil.sub(zdje1, yfje1);
//										if (tzcc.getCouponprice() > open_amount1) {
//											tzcc.setCouponprice(open_amount1);
//										}
//									}
//									actualmoney = new BigDecimal(tzcc.getCouponprice());
                                    payNum = yzm;
                                    fkfalg = true;
                                    // 使用完成后更改使用状态
                                    acewillCouponTempMapper.updateUsaByUseok(zdbh, yzm, 1);
                                    // 预核销成功后，把该券所属卡以外的所有卡包含的券，全部置为不可用,卡可用状态也同样修改
                                    acewillCouponTempMapper.updateUsaByCardcode(zdbh, tzcc.getCardcode(), 1);
                                    acewillMemberinfoMapper.updateUsaByCardcode(zdbh, tzcc.getCardcode(), 1);
                                    result.setCode(0);
                                    result.setMsg("支付成功");
                                    result.setSuccess(true);
                                } else {
                                    result.setCode(-1);
                                    result.setMsg("支付失败");
                                    result.setSuccess(false);
                                }
                            } else {
                                result.setCode(-1);
                                result.setMsg(ccMap.get("msg"));
                                result.setSuccess(false);
                            }
                        }

                        if ("2001".equals(pw.getPayno()) || "2002".equals(pw.getPayno()) || "1972".equals(pw.getPayno())
                                || "1973".equals(pw.getPayno())) {
                            payNum = yzm;
                            List<TqMemberInfo> amis = acewillMemberinfoMapper.getAcewillMembers(zdbh, yzm);
                            TqMemberInfo tmi = new TqMemberInfo();
                            if (amis.size() > 0) {
                                tmi = amis.get(0);
                            } else {
                                result.setCode(-1);
                                result.setMsg("会员卡不存在，请重新查询");
                                result.setSuccess(false);
                            }
                            if ("2001".equals(pw.getPayno()) || "1973".equals(pw.getPayno())) {
                                fkbz = "balance";
                                double dfkje = ArithUtil.mul(Double.parseDouble(fkje), 100);
                                double balance = tmi.getBalance();
                                if (balance >= dfkje && dfkje != 0) {
                                    balance = ArithUtil.sub(balance, dfkje);
                                    acewillMemberinfoMapper.updateToBalance(zdbh, tmi.getScancode(), balance, -1);
                                    acewillMemberinfoMapper.updateUsaByCardcode(zdbh, tmi.getScancode(), 1);

                                    fkfsid = acewillCouponTempMapper.getFkfsid(pw.getPayno()) + "";

                                    actualmoney = new BigDecimal(fkje);
                                    fkfalg = true;
                                    result.setCode(0);
                                    result.setMsg("支付成功");
                                    result.setSuccess(true);
                                } else {
                                    result.setCode(-1);
                                    result.setMsg("会员卡余额不足或支付金额为0");
                                    result.setSuccess(false);
                                }
                            }
                            if ("2002".equals(pw.getPayno()) || "1972".equals(pw.getPayno())) {
                                fkbz = "credit";
                                double dfkje = Double.parseDouble(fkje);
                                double credit = tmi.getCredit();
                                JSONObject res = JSONObject.fromObject(tmi.getRemark());
                                if ("2002".equals(pw.getPayno()) && res.containsKey("credit_deduct")) {
                                    dfkje = ArithUtil.mul(dfkje, res.optInt("credit_deduct"));
                                }

                                if ("1972".equals(pw.getPayno()) && res.containsKey("pointsRule")) {
                                    JSONObject pointsRule = res.optJSONObject("pointsRule");
                                    if (null != pointsRule && pointsRule.containsKey("points_rule")) {
                                        dfkje = ArithUtil.mul(dfkje, pointsRule.optInt("points_rule"));
                                    }
                                }

                                if (credit >= dfkje) {
                                    credit = ArithUtil.sub(credit, dfkje);
                                    acewillMemberinfoMapper.updateToBalance(zdbh, tmi.getScancode(), -1, credit);
                                    acewillMemberinfoMapper.updateUsaByCardcode(zdbh, tmi.getScancode(), 1);

                                    fkfsid = acewillCouponTempMapper.getFkfsid(pw.getPayno()) + "";

                                    actualmoney = new BigDecimal(fkje);
                                    fkfalg = true;
                                    result.setCode(0);
                                    result.setMsg("支付成功");
                                    result.setSuccess(true);
                                } else {
                                    result.setCode(-1);
                                    result.setMsg("会员卡积分不足");
                                    result.setSuccess(false);
                                }
                            }
                        }
                        break;
                    case CANCEL:
                        if ("2010".equals(pw.getPayno()) || "1971".equals(pw.getPayno())) {
                            tkfalg = false;
                            result.setCode(1);
                            result.setMsg("该支付方式无法取消，请取消对应的优惠方式");
                            result.setSuccess(false);
                        }
                        if ("2001".equals(pw.getPayno()) || "2002".equals(pw.getPayno()) || "1972".equals(pw.getPayno()) || "1973".equals(pw.getPayno())) {
                            // 恢复卡余额
                            List<TqMemberInfo> amis = acewillMemberinfoMapper.getAcewillMembers(zdbh, yzm);
                            TqMemberInfo tmi = new TqMemberInfo();
                            if (amis.size() > 0) {
                                tmi = amis.get(0);
                            } else {
                                result.setCode(-1);
                                result.setMsg("会员卡不存在，请重新查询");
                                result.setSuccess(false);
                            }
//							if ("balance".equals(fkbz)) {
                            if ("2001".equals(pw.getPayno()) || "1973".equals(pw.getPayno())) {
                                // 获取取消流水的金额，转换成分
                                double dfkje = ArithUtil.mul(Double.parseDouble(fkje), 100);
                                // 获取卡余额
                                double balance = tmi.getBalance();
                                // 将取消的支付金额加回到卡余额
                                balance = ArithUtil.add(balance, dfkje);
                                acewillMemberinfoMapper.updateToBalance(zdbh, tmi.getScancode(), balance, -1);
                            }
                            if ("2002".equals(pw.getPayno()) || "1972".equals(pw.getPayno())) {
                                // 获取取消流水的金额，转换成分
                                double dfkje = Double.parseDouble(fkje);
                                // 获取卡余额
                                double credit = tmi.getCredit();
                                JSONObject res = JSONObject.fromObject(tmi.getRemark());
                                if (res.containsKey("credit_deduct")) {
                                    dfkje = ArithUtil.mul(dfkje, res.optInt("credit_deduct"));
                                }
                                // 将取消的支付金额加回到卡余额
                                credit = ArithUtil.add(credit, dfkje);
                                acewillMemberinfoMapper.updateToBalance(zdbh, tmi.getScancode(), -1, credit);
                            }

                            actualmoney = new BigDecimal(fkje);
                            result.setCode(0);
                            result.setMsg("取消成功");
                            result.setSuccess(true);
//							}
                        }

                    default:
                        break;
                }

            } else {
                fkfalg = true;
                result.setCode(0);
                result.setMsg("支付成功！");
                result.setSuccess(true);
            }
            // 美团券不走
            if (!"ERP_FKFS_MT".equals(payment_mark) && !"ERP_FKFS_DYQ".equals(payment_mark)) {
                double open_amount = 0.00;
                switch (pt) {
                    case PAY:
                        // 走付款过程
                        if (fkfalg) {
                            int cdf = tqZdkMapper.countDsfFkls(zdbh, Integer.parseInt(fkfsid), "");
                            String sfzhm = "";
                            if ("FKSX_SMZF".equals(pay_type) || "ERP_FKFS_MTZH".equals(payment_mark)) {
                                if (cdf == 0) {
//									实付:47.00\r\n优惠:0.00\r\n付款账户:odBIcuHTAL6fw06u_SMrPicPExI8\r\nAPPID:wx34ba1caa66f7a237
                                    sfzhm = "实付:" + payResultData.getFkje() + "\\r\\n优惠:" + payResultData.getDiscountamount() + "\\r\\n付款账户:" + payResultData.getUserid() + "\\r\\nAPPID:" + payResultData.getAppid();
                                    tqZdkMapper.accountsOrder(zdbh, Integer.parseInt(fkfsid), actualmoney, fksl, payNum, sfzhm, lxdh, outtradeno, jtbh, skyh);
                                }
                            } else {
                                // AccountsOrder ao =
                                tqZdkMapper.accountsOrder(zdbh, Integer.parseInt(fkfsid), actualmoney, fksl, payNum, sfzhm, "", fkbz, jtbh, skyh);
                            }
                        }
                        // open_amount = ao.getC();
                        break;
                    case CANCEL:
                        if (tkfalg) {
                            String pay_types = pay_type;
                            // 取消，删掉付款流水库记录
                            // if(!"FKSX_PQ".equals(pay_type)){
                            // fklsid = "0";
                            // }
                            String fkhm = "0";
                            if ("FKSX_SMZF".equals(pay_type)) {
                                pay_types = "FKSX_DSF";
                                if (null == fklsid || "".equals(fklsid)) {
                                    fklsid = "0";
                                    fkhm = payNum;
                                }
                            } else {
                                List<TqThirdExceptOrder> tteolist = tqZdkMapper.findRetryTteoList(zdbh);
                                if (tteolist.size() > 0) {
                                    result.setCode(-1);
                                    result.setSuccess(false);
                                    result.setMsg("已有第三方付款发生，如需退款请移至“异常账单”处理！");
                                    break;
                                }
                            }
                            tqZdkMapper.delFkje(zdbh, pay_types, Integer.parseInt(fklsid), fkhm, outtradeno);
                        }
                        break;
                    case QUERY:
                        // 查询，暂时无动作
                        if (fkfalg) {
                            int cdf = tqZdkMapper.countDsfFkls(zdbh, Integer.parseInt(fkfsid), "");
                            if (cdf == 0) {
                                // AccountsOrder ao =
                                tqZdkMapper.accountsOrder(zdbh, Integer.parseInt(fkfsid), actualmoney, fksl, payNum, "", lxdh, outtradeno, jtbh, skyh);
                            }
                        }
                        break;
                    default:
                        break;
                }
                double yfje = 0d;
                double zdje = 0d;
                try {
                    yfje = tqZdkMapper.getFkje(zdbh);
                    zdje = tqZdkMapper.getZdje(zdbh);
                } catch (Exception e) {
                    e.printStackTrace();
                    yfje = 0d;
                    zdje = 0d;
                } finally {
                    tlPrd.set(null);
                }
                open_amount = DoubleUtil.sub(zdje, yfje);
                payResultData.setOpen_amount(open_amount + "");
                payResultData.setFkls(tqZdkMapper.getFklsList(zdbh));
                List<CouponRunningWater> crw = new ArrayList<CouponRunningWater>();
                List<CouponRunningWater> crw4 = tqZdkMapper.getCouponlsList(zdbh, "4");
                List<CouponRunningWater> crw6 = tqZdkMapper.getCouponlsList(zdbh, "6");
                crw.addAll(crw4);
                crw.addAll(crw6);
                payResultData.setCouponls(crw);
                List<JSONObject> dataList = new ArrayList<JSONObject>();
                dataList.add(JSONObject.fromObject(GsonUtil.GsonString(payResultData)));
                result.setData(dataList);
            }
        }
        // payResultData = new PayResultData();
    }

    /***
     * 判断是否开启支核一体
     * @param paymentMark
     * @return
     */
    private boolean checkIsEnablePv(String paymentMark,TqZdk zdk) {
        // 如果已经使用了会员，包括仅查询，不允许使用支核一体
        // 查询出非支核一体会员缓存
        List<TqMemberInfo> notPvM = acewillMemberinfoMapper.getIsPvMembers(zdk.getKdzdbh(), "0");
        if (null != notPvM && notPvM.size() > 0) {
            return false;
        }

        //优惠后或混付是否允许使用支核一体
        if(!"Y".equals(InitDataListener.ggcsMap.get("POS_PVCHECKMONEY"))){
            if(0!=zdk.getYhje()||CollectionUtils.isNotEmpty(tqZdkMapper.getFklsList(zdk.getKdzdbh()))){
                return false;
            }
        }
        return "1".equals(InitDataListener.ggcsMap.get("POS_OPENPV"))&&tqZdkMapper.checkIsEnablePv(paymentMark)>0?true:false;
    }

    /**
     * 结账
     */
    @Transactional
    public void accountsOrder(Data param, Data result) throws Exception {
        Map<String, Object> map = ReqDataUtil.getDataMap(param);

        String zdbh = ParamUtil.getStringValue(map, "zdbh", false, null);// 账单号
//		String fkje = ParamUtil.getStringValue(map, "fkje", false, null);//付款金额
        String skjh = ParamUtil.getStringValue(map, "skjh", false, null);// 收款机号，机台号
        String skyh = ParamUtil.getStringValue(map, "skyh", false, null);// 收款人账号
        String zwbh = ParamUtil.getStringValue(map, "qch", false, null);//餐牌号/桌位号
        String zzbz = ParamUtil.getStringValue(map, "zzbz", false, null);//整单备注
        String bcid = ParamUtil.getStringValue(map, "bcid", false, null);// 班次id
        String xfks = ParamUtil.getStringValue(map, "xfks", false, null);// 消费客数

        BillMoney billmoney = getBillMoney(zdbh);
        double fkje = tqZdkMapper.getFkjeNoZl(zdbh);
        TqZdk zdlsk = tqZdkMapper.getZdk(zdbh);
        AccountsReturnVo arv = new AccountsReturnVo();
        List<JSONObject> dataList = new ArrayList<JSONObject>();
        if (billmoney.getZt() == 0) {
            Date ksjzsj = new Date();
            String jzzdbhl = "0";
            String qchTitle = "桌台号";
            String qch = "";
            int jzbcid = Integer.parseInt(bcid);
//			TsGgcsk ggcs = tqZdkMapper.getGgcs("MDYYMS");
            String yyms = InitDataListener.ggcsMap.get("MDYYMS");
//			if (null != ggcs && "3".equals(ggcs.getSdnr())) {
            if ("3".equals(yyms)) {
                jzbcid = Integer.parseInt(bcid);
            } else {
                TsBck bck = tqZdkMapper.getBcid();
                jzbcid = bck.getId();
//				TsGgcsk isTable = tqZdkMapper.getGgcs("JZSFXSZP");
                String isTable = InitDataListener.ggcsMap.get("JZSFXSZP");
//				if (null != isTable && "N".equals(isTable.getSdnr())) {
                if ("N".equals(isTable)) {
                    qchTitle = "取餐号";
//					qch = createBh(skjh, "TQ_ZDK", "QCH");
                    qch = zdlsk.getQch();
                    zwbh = "";
                }
            }

//			jzzdbhl = createBh(skjh, "TQ_ZDK", "JZZDBH");
            jzzdbhl = zdlsk.getJzzdbh();
            String jzzdbh = jzzdbhl;

            //更新 tq_zdk tq_wdk tq_fklslsk
            Map<String, String> bbrqMap = tqJtztkMapper.findBbrq();
            String jzbbrq = DateUtil.getNowDateYYDDMM();
            if (null != bbrqMap && bbrqMap.size() != 0) {
                jzbbrq = bbrqMap.get("bbrq");
            }
            Date jzjssj = new Date();
            TqZdk zdk = tqZdkMapper.getZdk(zdbh);
            // 拆分不计收入付款
            if ("1".equals(InitDataListener.ggcsMap.get("POS_PAYWITHOUT_TODIS"))) {
                List<TqFklslsk> fklslskList = tqZdkMapper.getFklslsk(zdbh);
                commUseYhfs(zdk, fklslskList, skjh, jzbbrq);
            }
            //添加更新整单备注，取餐号
            tqZdkMapper.updateZdk(qch, zzbz, zwbh, zdbh, jzzdbh, DateUtil.parseDate(jzbbrq), jzjssj, 1, skjh, skyh, "ZDSX_YJ", ksjzsj, jzjssj, jzbcid, Integer.parseInt(xfks));
            tqZdkMapper.updateWdk(zdbh, jzzdbh, skjh, DateUtil.parseDate(jzbbrq), jzbcid);
            tqZdkMapper.updateFklslsk(zdbh, jzzdbh, jzbcid);

            List<ItemVo> discounts = tqZdkMapper.getDiscountR(zdbh);
            List<ItemVo> items = tqZdkMapper.getItemR(zdbh);
            List<PaymentRunningWater> payments = tqZdkMapper.getFklsList(zdbh);
            for (ItemVo item : items) {
                if ("CMSX_TC".equals(item.getIs_combo())) {
                    List<ComboDetails> comboDetails = tqZdkMapper.getComboDetailsR(zdbh, item.getItem_id());
                    item.setComboDetails(comboDetails);
                }
//				if(null != item.getItem_taste() && !"".equals(item.getItem_taste())){
//					String itemtastes = convertStr(item.getItem_taste());
//					String tasteNames = tqZdkMapper.getTasteNames(itemtastes);
//					item.setTaste_name(tasteNames);
//				}
            }
            String change_mone = tqZdkMapper.changeMone(zdbh);
            arv.setKdzdbh(billmoney.getKdzdbh());
            arv.setCope_with_money(billmoney.getCope_with_money());
            arv.setActual_money(billmoney.getActual_money());
            arv.setDiscount_money(billmoney.getDiscount_money());
            arv.setPayment_money(fkje + "");
            arv.setChange_money(change_mone);
            arv.setDiscounts(discounts);
            arv.setItems(items);
            arv.setThird_part_offer(tqZdkMapper.getZdjeAndDsf2(zdbh) + "");
            TsPsjgsdk tsPsjgsdk = tqZdkMapper.getJg();
            arv.setShop_name(tsPsjgsdk.getJgmc1());
            arv.setJzsj((new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).format(jzjssj));
            arv.setQch_title(qchTitle);
            if ("取餐号".equals(qchTitle)) {
                arv.setQch(qch);
                String isUserlsdh = InitDataListener.ggcsMap.get("ISUSERLSDH");
                if ("1".equals(isUserlsdh)) {
                    arv.setQch(zdlsk.getLsdh());
                }
            } else {
                arv.setQch(zwbh);
            }
            arv.setXmxjje(zdk.getXmxjje() + "");
            arv.setPayments(payments);
            // 生成电子发票链接字符串
            String invoiceStr = createEInvoice(zdbh, jzjssj, DateUtil.parseDate(jzbbrq));
            arv.setInvoice(invoiceStr);

            result.setCode(Constant.CODE_SUCCESS);
            result.setMsg(Constant.PAYMENT_SUCCESS);
            result.setSuccess(true);

            try {
                //20181112 下面的代码，暂时注释掉，要求不接结帐单打印，没时间处理，直接注释
                String isPrint = "1";
                TsGgcsk ggcsp = tqZdkMapper.getGgcs("POSISPRINTAPPBILL");
                if (null != ggcsp && !"".equals(ggcsp.getSdnr())) {
                    isPrint = ggcsp.getSdnr();
                }
                if ("0".equals(isPrint)) {
                    String printStr = "桌位名称='';账单编号='" + zdbh + "';操作员='" + skyh + "';报表日期='" + jzbbrq + "';查询类型='1';渠道='APP'";
                    int printId = 10103;
                    PosPrintJna print = new PosPrintJna();
                    print.tzxReportlib(printId, printStr, skjh);
                    LOGGER.info("结账单打印完成：" + zdbh);
                }

                String isKichenPrint = "N";
                TsGgcsk ggcskp = tqZdkMapper.getGgcs("SFSCD");
                if (null != ggcskp && !"".equals(ggcskp.getSdnr())) {
                    isKichenPrint = ggcskp.getSdnr();
                }

                if ("Y".equals(isKichenPrint)) {
                    String printKichenStr = "账单号='" + zdbh + "';渠道='APP'";
                    PosPrintKichenJna printKichen = new PosPrintKichenJna();
                    printKichen.tzxReportlib(skjh, printKichenStr);
                    LOGGER.info("厨房打印完成：" + zdbh);
                }

                if ("Y".equals(InitDataListener.ggcsMap.get("SFQYKVS"))) {
                    String kvs = tqZdkMapper.callP_SendKVSData(zdbh);
                }

            } catch (Exception e) {
                e.printStackTrace();
                LOGGER.error("Ignore this exception", e);
            }
            // 结账成功后清除掉会员缓存信息
            tqZdkMapper.delTqAcewilCouponCache(zdbh);
        } else if (billmoney.getZt() == -1) {
            result.setCode(-1);
            result.setMsg(Constant.PAYMENT_FAILURE);
            result.setSuccess(false);
        } else if (billmoney.getZt() == -2) {
            String change_mone = tqZdkMapper.changeMone(zdbh);
            TqZdk zdk = tqZdkMapper.getZdk(zdbh);
            arv.setKdzdbh(zdbh);
            arv.setCope_with_money(zdk.getZdje() + "");
            arv.setActual_money(zdk.getFkje() + "");
            arv.setDiscount_money(zdk.getZrje() + "");
            arv.setPayment_money(fkje + "");
            arv.setChange_money(change_mone);
            arv.setThird_part_offer(tqZdkMapper.getZdjeAndDsf2(zdbh) + "");

            // 结账成功后清除掉会员缓存信息
            tqZdkMapper.delTqAcewilCouponCache(zdbh);

            result.setCode(Constant.CODE_SUCCESS);
            result.setMsg(Constant.PAYMENT_SUCCESS);
            result.setSuccess(true);
        }
        dataList.add(JSONObject.fromObject(GsonUtil.GsonString(arv)));
        result.setData(dataList);

    }

    /**
     * @param @param rifUrl
     * @param @param xmlStr
     * @param @param result
     * @return void
     * @throws Exception
     * @throws
     * @Description: 美团验券调用接口
     * <AUTHOR>
     * @email <EMAIL>
     * @date 2018-11-21
     */
    public void mtQuan(List<WdDishVo> wdv, String rifUrl, String xmlStr, Data result, String zdbh, String payType, Integer yhfsid, String jtbh, String yzm, String skyh, Integer fksl, String pay_type, String fkfsid, String fklsid, int jzid, String yhsx, String outtradeno) {
        String responseXml = SendRequest.sendPost(rifUrl, xmlStr);
        LOGGER.info("美团券返回报文为:" + responseXml);
        BigDecimal actualmoney = null;
        String status = null;
        //String voucher = null;
        Integer xmid = null;
        String xmids = null;

        DATAPACKET dataPacker = null;
        String wdrwid = null;
        String pqlx = null;
        String sjje = null;
        String sfkbz = "";
        String couponbuyprice = null;
        TqYhMtCouponsTemp tqYhMtCouponsTemp = new TqYhMtCouponsTemp();
        try {
            readStringXmlOut(responseXml);
            if (null == responseXml || "".equals(responseXml)) {
                result.setCode(-1);
                result.setMsg(Constant.MEITUAN_EXCEPTION);
                LOGGER.error(Constant.MEITUAN_EXCEPTION);
                result.setSuccess(false);
            } else {
                dataPacker = (DATAPACKET) convertXmlStrToObject(DATAPACKET.class, responseXml);
                result.setCode(Constant.CODE_SUCCESS);
                result.setMsg(dataPacker.getMSG().getDATAS().get(0).getROWDATA().getROW().getMSG());
                result.setSuccess(true);
                status = dataPacker.getMSG().getDATAS().get(0).getROWDATA().getROW().getSTATUS();
                //	voucher = dataPacker.getMSG().getDATAS().get(0).getROWDATA().getROW().getVOUCHER();
                wdrwid = dataPacker.getMSG().getDATAS().get(0).getROWDATA().getROW().getWDRWID();
                pqlx = dataPacker.getMSG().getDATAS().get(0).getROWDATA().getROW().getPQLX();
                tqYhMtCouponsTemp.setYhfsid(yhfsid);
                tqYhMtCouponsTemp.setKdzdbh(zdbh);
                tqYhMtCouponsTemp.setPqlx(pqlx);
                tqYhMtCouponsTemp.setYzm(yzm);
                tqYhMtCouponsTemp.setOptype(21);
                if ("0".equals(status)) {
                    result.setCode(0);
                    result.setSuccess(true);
                    xmids = dataPacker.getMSG().getDATAS().get(0).getROWDATA().getROW().getXMID();
                    sjje = dataPacker.getMSG().getDATAS().get(0).getROWDATA().getROW().getSJJE();
                    couponbuyprice = dataPacker.getMSG().getDATAS().get(0).getROWDATA().getROW().getCOUPONBUYPRICE();
                    sfkbz = xmids;
                    if (null == couponbuyprice || "null".equals(couponbuyprice)) {
                        couponbuyprice = "0";
                    }
                    tqYhMtCouponsTemp.setBuyprice(new BigDecimal(couponbuyprice));
                    if (null != xmids && !"null".equals(xmids)) {
                        xmid = Integer.parseInt(xmids);
                    } else {
                        xmid = -1;
                    }
                    if (null == sjje || "null".equals(sjje)) {
                        sjje = "0";
                    }
                    if ("PAY".equals(payType)) {
                        tqYhMtCouponsTemp.setSjje(new BigDecimal(sjje));
                        if ("0".equals(pqlx)) {        //0:美团菜品券  1:美团现金券
                            //insetTqYhMtCoupons(Integer.parseInt(wdrwid),new BigDecimal(0),yhfsid, -1, zdbh, yzm, "0",jzid,new BigDecimal(sjje));
                            actualmoney = new BigDecimal(dataPacker.getMSG().getDATAS().get(0).getROWDATA().getROW().getSJJE());
                            BigDecimal payableamt = new BigDecimal(dataPacker.getMSG().getDATAS().get(0).getROWDATA().getROW().getPAYABLEAMT());
//                            tqYhMtCouponsMapper.updateYhMtCouponsMapper(new BigDecimal(sjje), yzm, "y", "0", new Date(), zdbh, new BigDecimal(couponbuyprice), pqlx, xmid, Integer.parseInt(wdrwid), new BigDecimal(couponbuyprice));
                            tqYhMtCouponsMapper.updateYhMtCouponsMapper(new BigDecimal(sjje), yzm, "y", "0", new Date(), zdbh, payableamt, pqlx, xmid, Integer.parseInt(wdrwid), new BigDecimal(couponbuyprice));

                            //tqYhMtCouponsTempMapper.insert(tqYhMtCouponsTemp);
                            //查看clmxid
                            tqYhMtCouponsTemp.setClmxid(tqYhMtCouponsTempMapper.findClmxid(xmid));
                            tqYhMtCouponsTemp.setPayableamt(actualmoney);
                            tqYhMtCouponsTemp.setCmid(xmid);
                            tqYhMtCouponsTemp.setWdrwid(Integer.parseInt(wdrwid));
                        }
                        if ("1".equals(pqlx)) {
                            //删除相同账单,防止重单 菜品券可重复，现金券不能重复
                            tqYhMtCouponsMapper.deleteYhMtCouponsMapper(zdbh, yzm);
                            insetTqYhMtCoupons("n", null, new BigDecimal(0), yhfsid, -1, zdbh, yzm, "0", jzid, new BigDecimal(sjje), outtradeno);
                            actualmoney = new BigDecimal(dataPacker.getMSG().getDATAS().get(0).getROWDATA().getROW().getPAYABLEAMT());
                            tqYhMtCouponsTemp.setClmxid(-1);
                            tqYhMtCouponsTemp.setPayableamt(actualmoney);
                            tqYhMtCouponsTemp.setCmid(-1);
//                            tqYhMtCouponsMapper.updateYhMtCouponsMapper(null, yzm, "y", "0", new Date(), zdbh, new BigDecimal(couponbuyprice), pqlx, xmid, null, new BigDecimal(couponbuyprice));
                            tqYhMtCouponsMapper.updateYhMtCouponsMapper(null, yzm, "y", "0", new Date(), zdbh, actualmoney, pqlx, xmid, null, new BigDecimal(couponbuyprice));
                            tqYhMtCouponsMapper.findClid(yhsx);
                        }
                        tqYhMtCouponsTempMapper.deleteByZdbh(zdbh);
                        tqYhMtCouponsTempMapper.insert(tqYhMtCouponsTemp);
                        tqZdkMapper.addCm(zdbh, tqYhMtCouponsMapper.findClid(yhsx), 1, jtbh, skyh, yzm, null, null, 0);
                        tqZdkMapper.zRtr(zdbh);
                        tqZdkMapper.findCalcMoney(zdbh);
                    }
                    if ("CANCEL".equals(payType)) {
                        if (StringUtils.isNotBlank(yzm)) {
                            //根据验证码更新数据
                            tqYhMtCouponsMapper.updateYhMtCouponsByYzm("5", yzm);
                            //取消第三方优惠
//							tqYhMtCouponsTempMapper.cancelThirdYhfs(zdbh,yzm);
                            tqZdkMapper.cancelThirdYhfs(zdbh, yzm, yhfsid);
                            tqZdkMapper.findCalcMoney(zdbh);
                        }
                    }
                } else if ("1".equals(status)) {
                    result.setCode(-1);
                    result.setSuccess(false);
                    tqYhMtCouponsMapper.updateYhMtCouponsMapper1(null, yzm, "y", "1", new Date(), zdbh, null, pqlx, xmid, null, null);
                } else {
                    result.setCode(-1);
                    result.setSuccess(false);
                }
            }
            //添加流水
            if ("PAY".equals(payType)) {
                if ("0".equals(status)) {
                    AccountsOrder ao = tqZdkMapper.accountsOrder(zdbh, Integer.parseInt(fkfsid), new BigDecimal(couponbuyprice), fksl, yzm, "", "", sfkbz, jtbh, skyh);
                    result.setCode(0);
                    result.setSuccess(true);
                }
            }
            if ("CANCEL".equals(payType)) {
                if ("0".equals(status)) {
                    if (StringUtils.isNotBlank(yzm)) {
                        tqZdkMapper.delFkje(zdbh, pay_type, Integer.parseInt(fklsid), yzm, "");
                    }
                    result.setCode(0);
                    result.setSuccess(true);
                }
            }
            double open_amount = 0.00;
            if ("0".equals(status)) {
//				if("PAY".equals(payType)) {
                double yfje = tqZdkMapper.getFkje(zdbh);
                double zdje = tqZdkMapper.getZdje(zdbh);
                open_amount = DoubleUtil.sub(zdje, yfje);
//				}
            }
            PayResultData payResultData1 = new PayResultData();
            payResultData1.setOpen_amount(open_amount + "");
            payResultData1.setFkls(tqZdkMapper.getFklsList(zdbh));
            List<CouponRunningWater> crw = new ArrayList<CouponRunningWater>();
            List<CouponRunningWater> crw4 = tqZdkMapper.getCouponlsList(zdbh, "4");
            List<CouponRunningWater> crw6 = tqZdkMapper.getCouponlsList(zdbh, "6");
            crw.addAll(crw4);
            crw.addAll(crw6);
            payResultData1.setCouponls(crw);
            List<JSONObject> dataList = new ArrayList<JSONObject>();
            dataList.add(JSONObject.fromObject(GsonUtil.GsonString(payResultData1)));
            result.setData(dataList);
        } catch (Exception e) {
            e.printStackTrace();
            result.setCode(-1);
            result.setMsg(Constant.MEITUAN_EXCEPTION);
            LOGGER.error(Constant.MEITUAN_EXCEPTION);
            LOGGER.error("Ignore this exception", e);
            result.setSuccess(false);
        }
    }

    /**
     * 将String类型的xml转换成对象
     */
    public static Object convertXmlStrToObject(Class clazz, String xmlStr) {
        Object xmlObject = null;
        try {
            JAXBContext context = JAXBContext.newInstance(clazz);
            // 进行将Xml转成对象的核心接口
            Unmarshaller unmarshaller = context.createUnmarshaller();
            StringReader sr = new StringReader(xmlStr);
            xmlObject = unmarshaller.unmarshal(sr);
        } catch (JAXBException e) {
            e.printStackTrace();
        }
        return xmlObject;
    }

    /*
     * 拼接美团验券参数
     */
    @Transactional
    public String getMeituanXml(List<WdDishVo> wdv, String yhfsid, String fkjgxh, String yzm, String czlx, TsPsjgsdk tsPsjgsdk, String zdbh, String outtradeno) {
        StringBuilder reqXml = new StringBuilder();
        reqXml.append("\n<?xml version=\"1.0\" encoding=\"GB2312\" ?>\n");
        reqXml.append("<DATAPACKET>\n");
        reqXml.append("	<HEAD>\n");
        reqXml.append("		<Version>1.0</Version>\n");
        reqXml.append("		<SRC>" + tsPsjgsdk.getJgxh() + "</SRC>\n");
        reqXml.append("		<DES>00</DES>\n");
        reqXml.append("		<DEVID></DEVID>\n");
        reqXml.append("		<APP>TZX-StoreBusinessSystem</APP>\n");
        reqXml.append("		<TID>TXDTASHUOCHW35DG39SG0LHHAW04YSDFGH</TID>\n");
        reqXml.append("		<MSGID>{77A35ECD-E589-4FC5-8A39-C272AF7FB1C0}</MSGID>\n");
        reqXml.append("		<CORID>20051024092733000440</CORID>\n");
        reqXml.append("		<WORKDATE>" + DateUtil.getNowDateYYDDMM() + "</WORKDATE>\n");
        reqXml.append("		<PERSONNEL>0001</PERSONNEL>\n");
        reqXml.append("		<RESERVE>STRING</RESERVE>\n");
        reqXml.append("	</HEAD>\n");
        reqXml.append("	<MSG>\n");
        reqXml.append("		<SUBSYSTEM>CRM</SUBSYSTEM>\n");
        reqXml.append("		<ACTION>QUERYDISCOUNTCODE</ACTION>\n");
        reqXml.append("		<DATAS>\n");
        reqXml.append("			<DATA Name=\"HYK\">\n");
        reqXml.append("				<ROWDATA>\n");
        reqXml.append("					<ROW YHFSID=\"" + yhfsid + "\" FKJGXH=\"" + fkjgxh + "\" CZLX=\"" + czlx + "\" YZM=\"" + yzm + "\" OUTTRADENO=\"" + outtradeno + "\"/>\n");
        reqXml.append("				</ROWDATA>\n");
        reqXml.append("			</DATA>\n");
        reqXml.append("			<DATA Name=\"HYXX\">\n");
        reqXml.append("				<ROWDATA>\n");
        reqXml.append("					<ROW SFGS=\"\" ZHYID=\"\" ZHYGX=\"1\" ZFBJ=\"1\" HYSFLX=\"\" XSRY=\"\" FZRY=\"\" HYLX=\"\" HYXB=\"\" ZJLX=\"\" ZJHM=\"\" HYMZ=\"\" HYJG=\"\" CSRQ=\"\" WHCD=\"\" YSR=\"\" ZCMC=\"\" SJHM=\"\" JTDH=\"\" EMAIL=\"\" ADDRS=\"\" YZBM=\"\" HYZK=\"\" ZNZK=\"\" YYAH=\"\" PIC1=\"\" CPHM=\"\" CAR=\"\" SFGSCY=\"\" GSID=\"0\" ZWMC=\"\" BMMC=\"\" GSMC=\"\" GSDH=\"\" GSCZ=\"\" GSDZ=\"\" GSYZBM=\"\" KHYH=\"\" YHZH=\"\" YWFW=\"\" SSHY=\"\" HYDW=\"\" WWW=\"\" SWZH=\"\" GMRS=\"\" SZDQ=\"\" SFXYDW=\"\" XYDH=\"\" XYRQ=\"\"/>\n");
        reqXml.append("				</ROWDATA>\n");
        reqXml.append("			</DATA>\n");
        reqXml.append("			<DATA Name=\"FKLS\">\n");
        reqXml.append("				<ROWDATA/>\n");
        reqXml.append("			</DATA>\n");
        reqXml.append("			<DATA Name=\"GOODSDETAIL\">\n");
        reqXml.append("				<ROWDATA>\n");
        if (null != wdv & wdv.size() > 0) {
            for (WdDishVo dish : wdv) {
                reqXml.append("				<ROW GOODSID=\"" + dish.getCmbh() + "\" GOODSNAME=\"" + dish.getCmmc1() + "\" QUANTITY=\"" + dish.getCmsl() + "\" PRICE=\"" + dish.getPrice() + "\" WDRWID=\"" + dish.getWdrwid() + "\"/>\n");
            }
        }
        reqXml.append("				</ROWDATA>\n");
        reqXml.append("			</DATA>\n");
        reqXml.append("		</DATAS>\n");
        reqXml.append("	</MSG>\n");
        reqXml.append("</DATAPACKET>\n");
        LOGGER.info("美团券请求报文为:\n" + reqXml);
        return reqXml.toString();
    }

    /**
     * 组装xml数据
     *
     * @return
     */
    @Transactional
    public String jointXML(String czlx, PaymentWay paymentWay, String zdbh, String dynamicid, String jtbh, String date_time, TsPsjgsdk tsPsjgsdk, String fkje, List<WdDishVo> wdv) {

        String fkfsid = paymentWay.getId();
//		String outtradeno = tsPsjgsdk.getJgxh() + zdbh + date_time;
        String outtradeno = createOuttradeno("tbxf", tsPsjgsdk.getJgxh(), jtbh, zdbh, date_time);
        String requestXml = toJointXML(tsPsjgsdk, czlx, fkfsid, outtradeno, dynamicid, jtbh, fkje, wdv);
        return requestXml;
    }

    /**
     * 组装xml数据
     *
     * @return
     */
    @Transactional
    public String toJointXML(TsPsjgsdk tsPsjgsdk, String czlx, String fkfsid, String outtradeno, String dynamicid, String jtbh, String fkje, List<WdDishVo> wdv) {
        StringBuilder reqXml = new StringBuilder();
        String date_time = outtradeno.substring(outtradeno.length() - 14);
        reqXml.append("<?xml version=\"1.0\" encoding=\"UTF-8\" ?>");
        reqXml.append("<DATAPACKET>");
        reqXml.append("<HEAD>");
        reqXml.append("<Version>1.0</Version>");
        reqXml.append("<SRC>" + tsPsjgsdk.getJgxh() + "</SRC>");
        reqXml.append("<DES>00</DES>");
        reqXml.append("<DEVID/>");
        reqXml.append("<APP>TZX-StoreBusinessSystem</APP>");
        reqXml.append("<TID>TXDTASHUOCHW35DG39SG0LHHAW04YSDFGH</TID>");
        reqXml.append("<MSGID>{" + java.util.UUID.randomUUID() + "}</MSGID>");
        reqXml.append("<CORID>20051024092733000440</CORID>");
        reqXml.append("<WORKDATE>" + DateUtil.getNowDateYYDDMM() + "</WORKDATE>");
        reqXml.append("<PERSONNEL>0001</PERSONNEL>");
        reqXml.append("<RESERVE>STRING</RESERVE>");
        reqXml.append("</HEAD>");
        reqXml.append("<MSG>");
        reqXml.append("<SUBSYSTEM>CRM</SUBSYSTEM>");
        reqXml.append("<ACTION>ONLINEPAYMENT</ACTION>");
        reqXml.append("<DATAS>");
        reqXml.append("<DATA Name=\"HYK\">");
        reqXml.append("<ROWDATA>");
        reqXml.append("<ROW FKFSID=\"" + fkfsid + "\" FKJGXH=\"" + tsPsjgsdk.getJgxh() + "\" CZLX=\"" + czlx + "\" OUTTRADENO=\"" + outtradeno + "\" SUBJECT=\"" + tsPsjgsdk.getJgmc1() + "\" TOTAL=\"" + fkje + "\" PRODUCTCODE=\"BARCODE_PAY_OFFLINE\" DYNAMICIDTYPE=\"barcode\" DYNAMICID=\"" + dynamicid + "\" STOREID=\"" + tsPsjgsdk.getJgbh() + "\" TERMINALID=\"" + jtbh + "\" TXNTIME=\"" + date_time + "\" ORDERID=\"TK" + outtradeno + "\" QUERYID=\"\"/>");
        reqXml.append("</ROWDATA>");
        reqXml.append("</DATA>");
        reqXml.append("<DATA Name=\"HYXX\">");
        reqXml.append("<ROWDATA>");
        reqXml.append("<ROW SFGS=\"\" ZHYID=\"\" ZHYGX=\"1\" ZFBJ=\"1\" HYSFLX=\"\" XSRY=\"\" FZRY=\"\" HYLX=\"\" HYXB=\"\" ZJLX=\"\" ZJHM=\"\" HYMZ=\"\" HYJG=\"\" CSRQ=\"\" WHCD=\"\" YSR=\"\" ZCMC=\"\" SJHM=\"\" JTDH=\"\" EMAIL=\"\" ADDRS=\"\" YZBM=\"\" HYZK=\"\" ZNZK=\"\" YYAH=\"\" PIC1=\"\" CPHM=\"\" CAR=\"\" SFGSCY=\"\" GSID=\"0\" ZWMC=\"\" BMMC=\"\" GSMC=\"\" GSDH=\"\" GSCZ=\"\" GSDZ=\"\" GSYZBM=\"\" KHYH=\"\" YHZH=\"\" YWFW=\"\" SSHY=\"\" HYDW=\"\" WWW=\"\" SWZH=\"\" GMRS=\"\" SZDQ=\"\" SFXYDW=\"\" XYDH=\"\" XYRQ=\"\"/>");
        reqXml.append("</ROWDATA>");
        reqXml.append("</DATA>");
        reqXml.append("<DATA Name=\"FKLS\"> ");
        reqXml.append("<ROWDATA/>");
        reqXml.append("</DATA>");
        reqXml.append("<DATA Name=\"GOODSDETAIL\">");
        reqXml.append("<ROWDATA>");
        if (null != wdv) {
            for (int i = 0; i < wdv.size(); i++) {
                reqXml.append("<ROW GOODSID=\"" + wdv.get(i).getCmbh() + "\" GOODSNAME=\"" + wdv.get(i).getCmmc1() + "\" QUANTITY=\"" + wdv.get(i).getCmsl() + "\" PRICE=\"" + wdv.get(i).getSjje() + "\"/>");
            }
        }
        reqXml.append("</ROWDATA>");
        reqXml.append("</DATA>");
        reqXml.append("</DATAS>");
        reqXml.append("</MSG>");
        reqXml.append("</DATAPACKET>");

//		reqXml.append("<?xml version=\"1.0\" encoding=\"UTF-8\" ?>");
//		reqXml.append("<req version=\"1.0\">");
//		reqXml.append("<disk command=\"list\" >");
//		reqXml.append("<vpath name=\"" + fileName + "\" dir=\"/\"/>");
//		reqXml.append("</disk>");
//		reqXml.append("</req>");
        String requestXml = reqXml.toString();
        return requestXml;
    }

    @Transactional
    public PaymentWay checkType(String dynamicid) {
        TsGgcsk ggcsWx = tqZdkMapper.getGgcs("ERP_FKFS_WX"); // 微信支付标识
        TsGgcsk ggcsZfb = tqZdkMapper.getGgcs("ERP_FKFS_ZFB"); // 支付宝支付标识
        TsGgcsk ggcsXnk = tqZdkMapper.getGgcs("ERP_FKFS_YSZXXNK"); // 虚拟卡支付标识
        TsGgcsk ggcsYsf = tqZdkMapper.getGgcs("ERP_FKFS_YL"); // 云闪付支付标识
        TsGgcsk ggcsZs = tqZdkMapper.getGgcs("CMB_YSZXXNK_RULE"); // 招商支付标识

        String wx = "";
        String zfb = "";
        String xnk = "";
        String ysf = "";
        String zs = "";

        if (null != ggcsWx && !"".equals(ggcsWx.getSdnr())) {
            wx = ggcsWx.getSdnr();
        }
        if (null != ggcsZfb && !"".equals(ggcsZfb.getSdnr())) {
            zfb = ggcsZfb.getSdnr();
        }
        if (null != ggcsXnk && !"".equals(ggcsXnk.getSdnr())) {
            xnk = ggcsXnk.getSdnr();
        }
        if (null != ggcsYsf && !"".equals(ggcsYsf.getSdnr())) {
            ysf = ggcsYsf.getSdnr();
        }
        if (null != ggcsZs && !"".equals(ggcsZs.getSdnr())) {
            zs = ggcsZs.getSdnr();
        }

        PaymentWay paymentWay = new PaymentWay();
        paymentWay.setId("-1");
        String dynamicids = dynamicid.substring(0, 2);
        if (wx.indexOf(dynamicids) > -1) {
            paymentWay = tqZdkMapper.getFkfsid("ERP_FKFS_WX");
        } else if (zfb.indexOf(dynamicids) > -1) {
            paymentWay = tqZdkMapper.getFkfsid("ERP_FKFS_ZFB");
        } else if (xnk.indexOf(dynamicids) > -1) {
            paymentWay = tqZdkMapper.getFkfsid("ERP_FKFS_YSZXXNK");
        } else if (ysf.indexOf(dynamicids) > -1) {
            boolean ifYl = true;
            String[] zsArray = zs.split(",");
            for (int i = 0; i < zsArray.length; i++) {
                String zsStr = zsArray[i];
                if (zsStr.length() <= 0) {
                    continue;
                }
                String dynamicidsZs = dynamicid.substring(0, zsStr.length());
                if (zsStr.equals(dynamicidsZs)) {
                    paymentWay = tqZdkMapper.getFkfsid("ERP_FKFS_YSZXXNK");
                    ifYl = false;
                    break;
                }
            }
            if (ifYl) {
                paymentWay = tqZdkMapper.getFkfsid("ERP_FKFS_YL");
            }
        }
        if (null == paymentWay) {
            paymentWay = new PaymentWay();
            paymentWay.setId("-1");
        }
        return paymentWay;
    }

    @Transactional
    public PaymentWay checkType(String dynamicid, String paymentMark) {
        String wx = "10,11,12,13,14,15";
        String zfb = "25,26,27,28,29,30";
        PaymentWay paymentWay = new PaymentWay();
        paymentWay.setId("-1");
        String dynamicids = dynamicid.substring(0, 2);
        if ("ERP_FKFS_MTZH".equals(paymentMark)) {
            paymentWay = tqZdkMapper.getFkfsid("ERP_FKFS_MTZH");
        } else if (wx.indexOf(dynamicids) > -1) {
            paymentWay = tqZdkMapper.getFkfsid("ERP_FKFS_WX");
        } else if (zfb.indexOf(dynamicids) > -1) {
            paymentWay = tqZdkMapper.getFkfsid("ERP_FKFS_ZFB");
        }
        return paymentWay;
    }

    //	static PayResultData payResultDatas = new PayResultData();
    public static void readStringXmlOut(String xml) throws Exception {
        StringReader read = new StringReader(xml);
        InputSource source = new InputSource(read);
        SAXBuilder sb = new SAXBuilder();
        Document doc = (Document) sb.build(source);
        Element root = doc.getRootElement();
        PayResultData payResultDatas = new PayResultData();
        tlPrd.set(parse(root, payResultDatas));
    }

    private final static ThreadLocal<PayResultData> tlPrd = new ThreadLocal<>();

    public static PayResultData parse(Element root, PayResultData payResultDatas) {
        List nodes = root.getChildren();
        int len = nodes.size();
        if (len != 0) {
            for (int i = 0; i < len; i++) {
                Element element = (Element) nodes.get(i);// 循环依次得到子元素
                if ("ROW".equals(element.getName())) {
                    if (null != element.getAttributeValue("VOUCHER")) {
                        List<PayResultCoupon> couponlist = payResultDatas.getCouponlist();
                        PayResultCoupon prc = new PayResultCoupon();
                        prc.setCouponBuyPrice(element.getAttributeValue("COUPONBUYPRICE"));
                        prc.setDealPrice(element.getAttributeValue("DEALPRICE"));
                        prc.setPayableamt(element.getAttributeValue("PAYABLEAMT"));
                        prc.setPqlx(element.getAttributeValue("PQLX"));
                        prc.setVoucher(element.getAttributeValue("VOUCHER"));
//						prc.setXmid(element.getAttributeValue("XMID"));
                        prc.setCmbh(element.getAttributeValue("CMBH"));
                        prc.setYzm(element.getAttributeValue("YZM"));
                        if (null == couponlist) {
                            couponlist = new ArrayList<PayResultCoupon>();
                        }
                        couponlist.add(prc);
                        payResultDatas.setCouponlist(couponlist);
                    } else {
                        if (null == element.getAttributeValue("AMOUNT")
                                || "null".equals(element.getAttributeValue("AMOUNT"))
                                || "Null".equals(element.getAttributeValue("AMOUNT"))
                                || "NULL".equals(element.getAttributeValue("AMOUNT"))) {
                            payResultDatas.setFkje("0");
                        } else {
                            payResultDatas.setFkje(element.getAttributeValue("AMOUNT"));
                        }
                        payResultDatas.setDiscountamount(element.getAttributeValue("DISCOUNTAMOUNT"));
                        payResultDatas.setFkfs(element.getAttributeValue("FKFS"));
                        if ("order not exist".equals(element.getAttributeValue("MSG"))) {
                            payResultDatas.setMsg("订单不存在!");
                        } else if ("Business Failed".equals(element.getAttributeValue("MSG"))) {
                            payResultDatas.setMsg("交易失败!");
                        } else {
                            payResultDatas.setMsg(element.getAttributeValue("MSG"));
                        }
                        payResultDatas.setPrivilegeamount(element.getAttributeValue("PRIVILEGEAMOUNT"));
                        payResultDatas.setStatus(element.getAttributeValue("STATUS"));
                        payResultDatas.setTotal(element.getAttributeValue("TOTAL"));
                        payResultDatas.setTradeno(element.getAttributeValue("TRADENO"));
                        payResultDatas.setAppid(element.getAttributeValue("APPID"));
                        payResultDatas.setUserid(element.getAttributeValue("USERID"));
                        payResultDatas.setYhfsid(element.getAttributeValue("YHFSID"));
                        payResultDatas.setWxyhfsid(element.getAttributeValue("WXYHFSID"));
                    }
                }
                parse(element, payResultDatas);
            }
        }
        return payResultDatas;
    }

    public String convertStr(String item_taste) {
        if (item_taste.indexOf(",") > -1) {
            item_taste = item_taste.replaceAll(",", "','");
        }
        item_taste = "'" + item_taste + "'";
        return item_taste;
    }

    public BillMoney getBillMoney(String zdbh) {
        BillMoney bm = new BillMoney();
        CalcMoney cm = tqZdkMapper.findCalcMoney(zdbh);
        if (cm.getZt() == 0) {
            bm.setKdzdbh(zdbh);
            bm.setCope_with_money(cm.getYsje().setScale(2) + "");
            bm.setActual_money(cm.getZdje().setScale(2) + "");
            bm.setDiscount_money(cm.getYsje().subtract(cm.getZdje()).setScale(2).toString());
        } else if (cm.getZt() == -1 || cm.getZt() == -2) {
            bm.setZt(cm.getZt());
        }
        return bm;
    }

    public BillMoney getBillMoney1(String zdbh) {
        BillMoney bm = new BillMoney();

//		CalcMoney cm = tqZdkMapper.findCalcMoney(zdbh);

        CalcMoney cm = new CalcMoney();
        tqZdkMapper.findCancleMoling(zdbh, 0);
        tqZdkMapper.zRtr(zdbh);
        CalcMoney cm1 = tqZdkMapper.findCalcMoney(zdbh);
        CalcMoney cm2 = tqZdkMapper.findMoling(zdbh, 0);

        if (cm2.getZt() == 0) {
            cm = cm2;
        } else {
            cm = cm1;
        }

        if (cm.getZt() == 0) {
            bm.setKdzdbh(zdbh);
            bm.setCope_with_money(cm.getYsje().setScale(2) + "");
            bm.setActual_money(cm.getZdje().setScale(2) + "");
            bm.setDiscount_money(cm.getYsje().subtract(cm.getZdje()).setScale(2).toString());
        } else if (cm.getZt() == -1 || cm.getZt() == -2) {
            bm.setZt(cm.getZt());
        }
        return bm;
    }

    public static String formatJgxh(int jgxh) {
        String str_xh = String.valueOf(jgxh);
        String str = "000000";
        str_xh = str.substring(0, 6 - str_xh.length()) + str_xh;
        return str_xh;
    }

    public String createBh(String jtbh, String bmc, String zdmc) {
        TsBmkzk tsBmkzKdzdbh = tqZdkMapper.getBh(bmc, zdmc); // 获取当前编码对象
        String oldBh = tsBmkzKdzdbh.getNr(); // 当前编码
        int bhLength = 10; // 默认编码长度
        if ("LSDH".equals(zdmc)) {
            bhLength = 4; // 流水单号默认长度4
        }
        if ("QCH".equals(zdmc)) {
            bhLength = 3; // 取餐号默认长度3
            // 如果是取餐号，达到最大值就从0开始，其他编号不用管
            TsGgcsk ggcsQchCsh = tqZdkMapper.getGgcs("QCHCSH");
            if (Integer.parseInt(oldBh) >= Integer.parseInt(ggcsQchCsh.getSdnr())) {
                oldBh = "0";
            }
        }
        if ("KDZDBH".equals(zdmc) || "JZZDBH".equals(zdmc)) {
            bhLength = 10; // 账单号，结帐单号默认长度10
        }
        if (null != tsBmkzKdzdbh.getCdzd() && !"".equals(tsBmkzKdzdbh.getCdzd())) {
            TsGgcsk ggcsws = tqZdkMapper.getGgcs(tsBmkzKdzdbh.getCdzd()); // 根据编码长度获取编码长度
            if (null != ggcsws.getSdnr() && !"".equals(ggcsws.getSdnr())) {
                bhLength = Integer.parseInt(ggcsws.getSdnr()); // 实际编码长度
            }
        }

        String newBh = (Long.parseLong(oldBh) + 1) + ""; // 新编码
        tqZdkMapper.updateBh(newBh, bmc, zdmc); // 更新编码
        // 补全编码
        NumberFormat nf = NumberFormat.getInstance();
        nf.setGroupingUsed(false);
        nf.setMaximumIntegerDigits(bhLength);
        nf.setMinimumIntegerDigits(bhLength);
        newBh = jtbh + nf.format(Integer.parseInt(newBh));
        return newBh;
    }

    /**
     * 查询账单
     */
    @Override
    public void queryOrder(Data param, Data result) throws Exception {
        int result_ = 0;
        try {
            List<JSONObject> dataList = new ArrayList<JSONObject>();
            String czyh = null;
            for (JSONObject obj : ReqDataUtil.getDataJsonList(param)) {
                czyh = obj.optString("czyh");
                for (BilledOrderVo tqzdk : tqZdkMapper.queryOrderByKtskjh(czyh, "ZDSX_YJ", "")) {
                    dataList.add(JSONObject.fromObject(tqzdk));
                }
            }
            if (result_ == 0) {
                result.setData(dataList);
                result.setMsg(Constant.FIND_BILL_SUCCESS);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result_ = -1;
            result.setMsg("系统异常");
            LOGGER.error("查询账单报错:{}", e.getMessage());
        } finally {
            result.setCode(result_ == 0 ? Constant.CODE_SUCCESS : -1);
            result.setSuccess(result_ == 0);
        }
    }

    /**
     * 退单
     */
    @Override
    public void refundOrder(Data param, Data result) throws Exception {
        int result_ = 0;
        Map<String, Object> map = ReqDataUtil.getDataMap(param);
        String jtbh = ParamUtil.getStringValue(map, "jtbh", false, null);// 机台号.
        String zdbh = ParamUtil.getStringValue(map, "zdbh", false, null);// 帐单号
        String czybh = ParamUtil.getStringValue(map, "czybh", false, null);// 操作员编号
        List<JSONObject> dataList = new ArrayList<JSONObject>();

        Map<String, String> bbrqMap = tqJtztkMapper.findBbrq();
        String bbrq = DateUtil.getNowDateYYDDMM();
        if (null != bbrqMap && bbrqMap.size() != 0) {
            bbrq = bbrqMap.get("bbrq");
        }
        try {
            for (JSONObject obj : ReqDataUtil.getDataJsonList(param)) {
                // 先退钱，轮询付款方式表
                List<PayMentVo> pmlist = tqZdkMapper.getPayMent(zdbh);
                for (PayMentVo pm : pmlist) {
                    dataList.add(JSONObject.fromObject(GsonUtil.GsonString(pm)));
                }
                tqThirdExceptOrderMapper.updateByRefund(zdbh, "8");
                // 退单过程，传入帐单号，当前报表日期，收款机号，操作人员
                tqZdkMapper.cancelBill(zdbh, DateUtil.parseDate(bbrq), jtbh, czybh);

                String printStr = "桌位名称='';账单编号='" + zdbh + "';操作员='" + czybh + "';报表日期='" + bbrq + "';查询类型='1';渠道='APP'";
                int printId = 10105;
                PosPrintJna print = new PosPrintJna();
                print.tzxReportlib(printId, printStr, jtbh);
                LOGGER.info("取消单打印完成" + zdbh);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result_ = -1;
            result.setMsg("系统异常");
            LOGGER.error("查询账单报错:{}", e.getMessage());
        } finally {
            result.setCode(result_ == 0 ? Constant.CODE_SUCCESS : result_);
            if (result_ == 0) {
                result.setData(dataList);
                result.setMsg(Constant.WHOLE_CANC_BILL_SUCCESS);
            }
            result.setSuccess(result_ == 0);
        }
    }

    /**
     * 查看账单详情
     */
    @Override
    public void queryBillDetail(Data param, Data result) {
        int result_ = 0;
        List<JSONObject> dataList = new ArrayList<JSONObject>();
        List<BillItemVo> billItemList = new ArrayList<BillItemVo>();
        String kdzdbh = null;
        try {
            for (JSONObject obj : ReqDataUtil.getDataJsonList(param)) {
                kdzdbh = obj.optString("orderNo");
                if ("".equals(kdzdbh)) {
                    result_ = -1;
                    break;
                }
                BilledOrderVo billOrder = tqZdkMapper.queryBillInfo("", kdzdbh).get(0);
                for (BillItemVo biv : tqZdkMapper.queryBillDetailByKdzdbh(kdzdbh, null, "", 0, 0)) {
                    if (!"Y".equals(biv.getSfxsmx())) {
                        biv.setBillItemVo(tqZdkMapper.queryBillDetailByKdzdbh(kdzdbh, biv.getClmxid(), "Y", biv.getDcxh(), biv.getSyyhfkfsid()));
                    }
                    billItemList.add(biv);
                }
                billOrder.setBillItemVo(billItemList);
                dataList.add(JSONObject.fromObject(GsonUtil.GsonString(billOrder)));
            }
        } catch (Exception e) {
            e.printStackTrace();
            result_ = -1;
            result.setMsg("系统异常");
            LOGGER.error("查询账单报错:{}", e.getMessage());
        } finally {
            result.setCode(result_ == 0 ? Constant.CODE_SUCCESS : result_);
            result.setSuccess(result_ == 0);
            if ("".equals(kdzdbh)) {
                result.setMsg("账单号不能为空");
            }
            if (result_ == 0) {
                result.setData(dataList);
                result.setMsg(Constant.FIND_BILL_SUCCESS);
            }
        }
    }

    /**
     * 查询优惠活动
     */
    @Override
    public void queryDiscount(Data param, Data result) throws Exception {
        int result_ = 0;
        List<JSONObject> dataList = new ArrayList<JSONObject>();
        Integer icbid = null;
        String sjgtxbh = null;// 价格体系编号
        try {
            String kdzdbh = null;
            for (JSONObject obj : ReqDataUtil.getDataJsonList(param)) {
                kdzdbh = obj.optString("kdzdbh");
                int group_id = obj.optInt("group_id");
                // 查询
                icbid = tqZdkMapper.findIcibd("TS");
                sjgtxbh = tqZdkMapper.findJgtxbh(kdzdbh);
                if (null == icbid) {
                    result_ = -2;
                } else if (null == sjgtxbh) {
                    result_ = -3;
                } else {
                    List<DiscountVo> discountList = tqZdkMapper.queryDiscount(kdzdbh, icbid, "", sjgtxbh);
                    for (DiscountVo vo : discountList) {
                        List<DiscountsRoot> dr = tqZdkMapper.queryDiscountsRoot(Integer.parseInt(vo.getDiscounttypeid()));
                        if (null != dr && dr.size() > 0) {
                            for (DiscountsRoot droot : dr) {
                                if (droot.getGroupid() == group_id) {
                                    dataList.add(JSONObject.fromObject(vo));
                                }
                            }
                        } else {
                            dataList.add(JSONObject.fromObject(vo));
                        }
                    }
                }
            }

        } catch (Exception e) {
            result_ = -1;
            result.setMsg("系统异常");
            LOGGER.error("查询优惠方式报错:{}", e.getMessage());
        } finally {
            result.setCode(result_ == 0 ? Constant.CODE_SUCCESS : result_);
            if (result_ == 0) {
                result.setData(dataList);
                result.setMsg(Constant.DISCOUNT_QUERY_SUCCESS);
            }
            if (result_ == 2) {
                result.setMsg(icbid + "不能为空");
            }
            if (result_ == 3) {
                result.setMsg(sjgtxbh + "不能为空");
            }
            result.setSuccess(result_ == 0);
        }
    }

    /**
     * @param @param yhfsid
     * @param @param xmid
     * @param @param kdzdbh
     * @param @param yzm
     * @param @param status
     * @param @param jzid
     * @return void
     * @throws
     * @Description: 保存美团券
     * <AUTHOR>
     * @email <EMAIL>
     * @date 2018-11-21
     */
    public void insetTqYhMtCoupons(String isDone, Integer wdrwid, BigDecimal payableamt, Integer yhfsid, Integer xmid, String kdzdbh, String yzm, String status, Integer jzid, BigDecimal sjje, String outtradeno) {
        TqYhMtCoupons yhMtCoupons = new TqYhMtCoupons();
        yhMtCoupons.setYhfsid(yhfsid);        //"-102"
        yhMtCoupons.setXmid(xmid);            //"-1"
        yhMtCoupons.setKdzdbh(kdzdbh);
        yhMtCoupons.setYzm(yzm);
        yhMtCoupons.setStatus(status);
        yhMtCoupons.setJzid(jzid);
        yhMtCoupons.setCreatetime(new Date());
        yhMtCoupons.setUpdatetime(new Date());
        yhMtCoupons.setFirststatus(status);
        yhMtCoupons.setPqlx("0");        //1表示现金券,0表示菜品券
        yhMtCoupons.setLaststatus(status);
        yhMtCoupons.setPayableamt(payableamt);
        yhMtCoupons.setIsdone(isDone);
        yhMtCoupons.setSjje(sjje);
        yhMtCoupons.setOuttradeno(outtradeno);
        if (null != wdrwid) {
            yhMtCoupons.setWdrwid(wdrwid);
        }
        tqYhMtCouponsMapper.insert(yhMtCoupons);
    }

    public void insertTteo(PaymentWay paymentWay, String zdbh, String dynamicid, String jtbh, String date_time, TsPsjgsdk tsPsjgsdk, String fkje) {
        String fkfsid = paymentWay.getId();
//		String outtradeno = tsPsjgsdk.getJgxh() + zdbh + date_time;
        String outtradeno = createOuttradeno("tbxf", tsPsjgsdk.getJgxh(), jtbh, zdbh, date_time);

        TqThirdExceptOrder tteo = new TqThirdExceptOrder();
        tteo.setBillid(zdbh);
        tteo.setCreatetime(new Date());
        tteo.setUpdatetime(new Date());
        tteo.setScan_code(dynamicid);
        tteo.setPaytypeid(fkfsid);
        tteo.setPaytime(date_time);
        tteo.setOrderno(outtradeno);
        tteo.setRefund_orderno("TK" + outtradeno);
        tteo.setProductcode("BARCODE_PAY_OFFLINE");
        tteo.setDynamicid_type("barcode");
        tteo.setCashamount(Double.parseDouble(fkje));
        tteo.setOrdermc(tsPsjgsdk.getJgmc1());
        tteo.setDataname("HYK");
        tteo.setUpdatecount(1);
        tteo.setFirst_pay_status("1");
        tteo.setLast_pay_status("");
        tteo.setPay_status("1");
        tteo.setPosno(jtbh);
        tteo.setPayname(paymentWay.getPayment_name1());
        tteo.setErrcount(0);

        tqThirdExceptOrderMapper.insert(tteo);
    }

    @Transactional
    public void posPrintJna(Data data, Data result) throws SystemException {
        Map<String, Object> map = ReqDataUtil.getDataMap(data);

        String zdbh = ParamUtil.getStringValue(map, "zdbh", false, null);// 账单号
        String czybh = ParamUtil.getStringValue(map, "czybh", false, null);// 操作员编号
        String ptype = ParamUtil.getStringValue(map, "ptype", false, null);// 打印类型   结帐单：jzd  整单取消：zdqx
        String jtbh = ParamUtil.getStringValue(map, "jtbh", false, null);// 机台编号

        Map<String, String> bbrqMap = tqJtztkMapper.findBbrq();
        String bbrq = DateUtil.getNowDateYYDDMM();
        if (null != bbrqMap && bbrqMap.size() != 0) {
            bbrq = bbrqMap.get("bbrq");
        }

        String printStr = "";
        int printId = 0;

        switch (ptype) {
            case "jzd":
                printStr = "桌位名称='';账单编号='" + zdbh + "';操作员='" + czybh + "';报表日期='" + bbrq + "';查询类型='1';渠道='APP'";
                printId = 10103;
                break;
            case "zdqx":
                printStr = "桌位名称='';账单编号='" + zdbh + "';操作员='" + czybh + "';报表日期='" + bbrq + "';查询类型='1';渠道='APP'";
                printId = 10105;
                break;
            default:
                printStr = "";
                printId = 0;
                break;
        }

        try {
            PosPrintJna print = new PosPrintJna();
            print.tzxReportlib(printId, printStr, jtbh);
            LOGGER.info("结账单打印完成：" + zdbh);
            result.setCode(Constant.CODE_SUCCESS);
            result.setMsg("打印成功");
            result.setSuccess(true);
        } catch (Exception e) {
            result.setCode(Constant.CODE_PARAM_FAILURE);
            result.setMsg("打印失败");
            result.setSuccess(false);
            e.printStackTrace();
        }
    }


    /**
     * 校验交现金，班结信息
     */
    @Transactional
    public void checkLimit(Data data, Data result) {
        Map<String, Object> map = ReqDataUtil.getDataMap(data);
        String czybh = ParamUtil.getStringValue(map, "czybh", false, null);// 操作员编号
        String jtbh = ParamUtil.getStringValue(map, "jtbh", false, null);// 机台编码
        String bcid = ParamUtil.getStringValue(map, "bcid", false, null);// 班次id

        result.setCode(-1);
        result.setMsg("系统异常！");
        result.setSuccess(false);

        Map<String, String> bbrqMap = tqJtztkMapper.findBbrq();
        String bbrq = DateUtil.getNowDateYYDDMM();
        if (null != bbrqMap && bbrqMap.size() != 0) {
            bbrq = bbrqMap.get("bbrq");
        }

        String appLogin = SysDictionary.LOGIN_STATE_ON;
        String appLogout = SysDictionary.LOGIN_STATE_OFF;
        TsGgcsk mdyems = tqZdkMapper.getGgcs("MDYYMS");
        int mdyymsi = 1;
        if (null != mdyems && "3".equals(mdyems.getSdnr())) {
            mdyymsi = 3;
            appLogin = SysDictionary.APP_LOGIN;
            appLogout = SysDictionary.APP_LOGOUT;
        } else {
            mdyymsi = 1;
            appLogin = SysDictionary.LOGIN_STATE_ON;
            appLogout = SysDictionary.LOGIN_STATE_OFF;
        }

        TqJtztk jtzt = tqJtztkMapper.getJtztJh(DateUtil.parseDate(bbrq), "99", "");
        TqJtztk appjtzt = tqJtztkMapper.getJtztNew(DateUtil.parseDate(bbrq), jtbh, appLogin, appLogout);
        TsGgcsk il = tqZdkMapper.getGgcs("ISLOGINOUTING");


        if (null == appjtzt || appLogout.equals(appjtzt.getCznr())) {
            result.setCode(101);
            result.setMsg("登录状态异常，点击“确认”返回登录页！！");
            result.setSuccess(false);
        } else if (checkDysj(bbrq) > 0) {
            result.setCode(101);
            result.setMsg("系统日期已经大于营业日期，请做打烊后再进行当前操作，点击“确认”返回登录页！");
            result.setSuccess(false);
//		} else if (null == jtzt || "YYTC".equals(jtzt.getCznr())) {
        } else if ((null == jtzt || "YYTC".equals(jtzt.getCznr())) && mdyymsi == 3) {
            result.setCode(101);
            result.setMsg("server人员未登录，点击“确认”返回登录页！");
            result.setSuccess(false);
        } else if (null != il && "Y".equals(il.getSdnr())) {
            result.setCode(101);
            result.setMsg("当前班次已班结或正在班结，请前往交款，点击“确认”返回登录页！");
            result.setSuccess(false);
        } else {
            if (mdyymsi == 3) {
                //没收款的金额不能大于限额
                String noCrossedMoneys = tqZdkMapper.getNoCrossedMoney(jtbh, DateUtil.parseDate(bbrq), Integer.parseInt(bcid), czybh, bbrq);
                double noCrossedMoney = Double.parseDouble(noCrossedMoneys);
                TsGgcsk ggcs = tqZdkMapper.getGgcs("CASH_LIMIT_AMOUNT");
                double restrictMoney = Double.parseDouble(ggcs.getSdnr());
                double isCrossed = restrictMoney - noCrossedMoney;
                if (isCrossed <= 0) {
                    result.setCode(152);
                    result.setMsg("您当前现金收款金额已经超过限制，请前往交款后才可继续点餐！");
                    result.setSuccess(false);
                } else if (isCrossed > 0 && isCrossed <= 100) {
                    result.setCode(151);
                    result.setMsg("您当前现金收款金还有" + (new Double(Math.ceil(isCrossed))).intValue() + "元将达到现金收款限额，请尽快前往交款，以免影响后续点餐！");
                    result.setSuccess(true);
                } else {
                    result.setCode(0);
                    result.setMsg("继续点餐，这条返回不显示出来");
                    result.setSuccess(true);
                }
            } else {
                result.setCode(0);
                result.setMsg("继续点餐，这条返回不显示出来");
                result.setSuccess(true);
            }
        }
    }

    public int checkDysj(String bbrq) {
        Date bbrqD = DateUtil.parseDateAll(bbrq + " 00:00:00");
        int day = DateUtil.daysBetween(bbrqD, DateUtil.parseDateAll(DateUtil.getNowDateYYDDMM() + " 00:00:00"));
        return day;
    }

    /**
     * @Transactional public void checkLimit(Data data, Data result) {
     * Map<String, Object> map = ReqDataUtil.getDataMap(data);
     * String czybh = ParamUtil.getStringValue(map, "czybh", false, null);// 操作员编号
     * String jtbh = ParamUtil.getStringValue(map, "jtbh", false, null);// 机台编码
     * String bcid = ParamUtil.getStringValue(map, "bcid", false, null);// 班次id
     * String dlcs = ParamUtil.getStringValue(map, "dlcs", false, null);// 当前登录次数
     * <p>
     * result.setCode(-1);
     * result.setMsg("系统异常！");
     * result.setSuccess(false);
     * <p>
     * Map<String, String> bbrqMap = tqJtztkMapper.findBbrq();
     * String bbrq = DateUtil.getNowDateYYDDMM();
     * if (null != bbrqMap && bbrqMap.size() != 0) {
     * bbrq = bbrqMap.get("bbrq");
     * }
     * TqJtztk jtzt = tqJtztkMapper.getJtztJh(DateUtil.parseDate(bbrq), "99", dlcs);
     * TqJtztk jtzt2 = tqJtztkMapper.getJtztJh(DateUtil.parseDate(bbrq), "99", "");
     * //		TqJtztk jtzt = tqJtztkMapper.getJtztJh(DateUtil.parseDate(bbrq), "99");
     * TsGgcsk il = tqZdkMapper.getGgcs("ISLOGINOUTING");
     * if(!bcid.equals(jtzt2.getYl1())){
     * result.setCode(101);
     * result.setMsg("设备班次与实际班次不匹配，点击“确认”返回登录页！");
     * result.setSuccess(false);
     * } else if("YYTC".equals(jtzt.getCznr()) || (null != il && "Y".equals(il.getSdnr()))){
     * result.setCode(101);
     * result.setMsg("当前班次已班结或正在班结，请前往交款，点击“确认”返回登录页！");
     * result.setSuccess(false);
     * } else {
     * //没收款的金额不能大于限额
     * //			String fullMoneys = tqZdkMapper.getFullMoney(jtbh, DateUtil.parseDate(bbrq), Integer.parseInt(bcid), czybh);
     * //			String crossedMoneys = tqZdkMapper.getCrossedMoney(DateUtil.parseDate(bbrq), Integer.parseInt(bcid), czybh);
     * //			double fullMoney = Double.parseDouble(fullMoneys);
     * //			double crossedMoney = Double.parseDouble(crossedMoneys);
     * //			double noCrossedMoney = fullMoney - crossedMoney;
     * String noCrossedMoneys = tqZdkMapper.getNoCrossedMoney(jtbh, DateUtil.parseDate(bbrq), Integer.parseInt(bcid), czybh, bbrq);
     * double noCrossedMoney = Double.parseDouble(noCrossedMoneys);
     * TsGgcsk ggcs = tqZdkMapper.getGgcs("CASH_LIMIT_AMOUNT");
     * double restrictMoney = Double.parseDouble(ggcs.getSdnr());
     * double isCrossed = restrictMoney - noCrossedMoney;
     * if(isCrossed <= 0){
     * result.setCode(152);
     * result.setMsg("您当前现金收款金额已经超过限制，请前往交款后才可继续点餐！");
     * result.setSuccess(false);
     * } else if(isCrossed > 0 && isCrossed <= 100){
     * result.setCode(151);
     * result.setMsg("您当前现金收款金还有" + (new Double(Math.ceil(isCrossed))).intValue() + "元将达到现金收款限额，请尽快前往交款，以免影响后续点餐！");
     * result.setSuccess(true);
     * } else {
     * result.setCode(0);
     * result.setMsg("继续点餐，这条返回不显示出来");
     * result.setSuccess(true);
     * }
     * }
     * }
     */

    @Transactional
    public void addTaste(Data data, Data result) {

        Map<String, Object> map = ReqDataUtil.getDataMap(data);

        String zdbh = ParamUtil.getStringValue(map, "bill_num", false, null);// 账单编号
        String rwid = ParamUtil.getStringValue(map, "rwid", false, null);// 疑似主键
        String kwbz = ParamUtil.getStringValue(map, "item_taste", false, null);// 口味备注

        try {
            tqZdkMapper.updateTaste(Integer.parseInt(rwid), zdbh, kwbz);
            Map<String, String> zdbhm = new HashMap<String, String>();
            zdbhm.put("orderNumber", "zdbh");
            List<JSONObject> dataList = new ArrayList<JSONObject>();
            dataList.add(JSONObject.fromObject(zdbhm));

            result.setData(dataList);
            result.setCode(Constant.CODE_SUCCESS);
            result.setMsg("添加口味成功！");
            result.setSuccess(true);
        } catch (Exception e) {
            result.setCode(-1);
            result.setMsg("添加口味失败！");
            result.setSuccess(false);
            e.printStackTrace();
        }

    }

    /**
     * 校验当前券是否可用
     * 1.校验是否与其他券共用
     * 2.校验最多使用几张
     * 3.校验启用金额，满/每满
     * 4.校验菜品券 菜品是否可用
     *
     * @param tacc
     * @return
     */
    public Map<String, String> checkCoupon(TqAcewilCouponCache tacc) {
        Map<String, String> ccMap = new HashMap<String, String>();

        String zdbh = tacc.getZdbh();
        String type = tacc.getCoupontype();
        String templateid = tacc.getTemplateid();
        String couponcode = tacc.getCouponcode();
        JSONObject couponobj = JSONObject.fromObject(tacc.getRemark());

        ccMap.put("code", "-1");
        if (null == tacc) { // 不存在或不符合条件
            ccMap.put("msg", "该券不可用！");
            return ccMap;
        }
        if (tacc.getUseok() == 1) { // 该券在本地的使用情况 1：已使用，0：未使用
            ccMap.put("msg", "该券已被使用！");
            return ccMap;
        }
        // 1.1 mix_use 校验是否与其他券共用， 可以就不管了， 不可以的话 根据当前模板id，与已核销券模板id做对比
//		int cuc1 = ;
        List<Map<String, Object>> cucList1 = acewillCouponTempMapper.getCouponUseCount(zdbh, templateid, "1", "ACEWILL");
        if (!couponobj.optBoolean("mix_use")) {
            if (cucList1.size() > 0) {
                ccMap.put("msg", "该券不可与其他券混用！");
                return ccMap;
            }
        }
        // 1.2  还需要验证本单已经使用过的券的mix_use属性，不可共用的话，也要返回本张券不可用；
        for (Map<String, Object> cucMap : cucList1) {
            JSONObject cucremark = JSONObject.fromObject(cucMap.get("remark").toString());
            if (!cucremark.optBoolean("mix_use")) {
                ccMap.put("msg", "已使用券不可与当前券混用！");
                return ccMap;
            }
        }

        // 2.max_use 同一种券一次最多可以使用几张 ，根据当前模板id查询已经核销了几张
        int icounts = 0;
        List<Map<String, Object>> cucList2 = acewillCouponTempMapper.getCouponUseCount(zdbh, templateid, "2", "ACEWILL");
        for (Map<String, Object> cucMap : cucList2) {
            String scounts = cucMap.get("counts").toString();
            icounts = icounts + Integer.parseInt(scounts);
        }
        String max_use = couponobj.optString("max_use");
        if (!"0".equals(max_use) && Integer.parseInt(max_use) <= icounts) {
            ccMap.put("msg", "本单中该券最多可用" + couponobj.optString("max_use") + "张！");
            return ccMap;
        }

        // 3.校验启用金额，满/每满
        double enable_amount = couponobj.optDouble("enable_amount");
        if (enable_amount != 0) {// 启用金额，0为不限制
            double fkje = acewillCouponTempMapper.getZdje(zdbh); // 付款金额
            if (1 == couponobj.optInt("limit_type")) { // 启用金额限制类型，0:满多少金额可用 ,1:每满多少金额可用
                fkje = ArithUtil.sub(fkje, ArithUtil.mul(icounts, enable_amount)); // icounts * enable_amount
            }
            if (couponobj.optDouble("enable_amount") > fkje) {
                ccMap.put("msg", "账单金额不满足该券启用金额！");
                return ccMap;
            }
        }
        // 4.校验菜品券 菜品是否可用
        if ("DISHCOUPON".equals(type)) { // DISHCOUPON是菜品券，代金券不用判断此项
            JSONArray products = couponobj.optJSONArray("products"); // 获取菜品券绑定的菜品编码
            if (products.size() > 0) {
                int wdrwid = 0;
                String wdcmbh = "";
                double maxCmdj = 0;
                int rc = 0;
                for (int i = 0; products.size() > i; i++) {
                    String cmbh = products.optString(i); // 支持一个券绑定多个编码
                    WdDishVo wdv = acewillCouponTempMapper.getWdDish(zdbh, cmbh); // 根据账单和菜品编码查询下单数据中是否包含此券的菜品
                    if (null != wdv) {
                        double cmdj = Double.parseDouble(wdv.getCmdj());
//						if(tacc.getCouponsale() <= cmdj){暂时不验证购买金额
                        rc = rc + 1;
                        if (maxCmdj < cmdj) {
                            maxCmdj = cmdj;
                            wdrwid = wdv.getWdrwid();
                            wdcmbh = wdv.getCmbh();
                        }
//						}暂时不验证购买金额
                    }
                }
                if (rc == 0) {
                    ccMap.put("msg", "账单没有符合该券的菜品！");
                    return ccMap;
                }
                acewillCouponTempMapper.updateRwid(zdbh, couponcode, wdrwid, wdcmbh);
            }
        }
        // 5.校验折扣券是否可用
        if ("DISHDISCOUNTCOUPON".equals(type)) { // DISHDISCOUNTCOUPON是折扣券，代金券不用判断此项
            List<String> products = JSONArray.toList(couponobj.optJSONArray("products")); // 获取菜品券绑定的菜品编码
            List<String> productsExt = JSONArray.toList(couponobj.optJSONArray("products_ext")); // 菜品编码，例外菜品，不参与打折
            List<WdDishVo> wdv = acewillCouponTempMapper.getWdDishDiscount(zdbh, products, productsExt);
            if (wdv.size() == 0 && products.size() > 0) {
                ccMap.put("msg", "账单没有符合该券的菜品！");
                return ccMap;
            } else {
                tqZdkMapper.delWdandYhTempItem(zdbh);
                acewillCouponTempMapper.insertWdAndYh(zdbh, products, productsExt, tacc.getYhfsid());
                int YhSize = acewillCouponTempMapper.getWdAndYhSize(zdbh);
                if (YhSize == 0) {
                    ccMap.put("msg", "账单没有符合该券的菜品！");
                    return ccMap;
                }
            }
        }
        /**  暂时不验证购买金额
         if ("CASHCOUPON".equals(tacc.getCoupontype())) {
         double yfje1 = tqZdkMapper.getFkje(zdbh);
         double zdje1 = tqZdkMapper.getZdje(zdbh);
         double open_amount1 = DoubleUtil.sub(zdje1, yfje1);
         if (tacc.getCouponsale() > open_amount1) {
         ccMap.put("msg", "应付金额小于该券购买金额！");
         return ccMap;
         }
         }*/

        ccMap.put("code", "0");
        ccMap.put("msg", "OK");
        return ccMap;
    }

    /**
     * 查询异常账单
     */
    @Override
    public void queryExceptionBill(Data param, Data result) throws Exception {
        int result_ = 0;
        try {
            List<JSONObject> dataList = new ArrayList<JSONObject>();
            String czyh = null;
            for (JSONObject obj : ReqDataUtil.getDataJsonList(param)) {
                czyh = obj.optString("czyh");
                for (BilledOrderVo tqzdk : tqZdkMapper.queryOrderToWj(czyh)) {
                    if (null == tqzdk.getBillid()) {
                        // 清除账单
                        String kdzdbh = tqzdk.getOrderNumber() + "";
//						tqYhMtCouponsMapper.updateYhMtCouponsMapperToLogin("8", "n", kdzdbh);
                        tqZdkMapper.delZdk(kdzdbh);
                        tqZdkMapper.delFkls(kdzdbh);
                        tqZdkMapper.delWdk(kdzdbh + "");

//						acewillCouponTempMapper.delTwct(kdzdbh + "");
//						acewillCouponTempMapper.delTmi(kdzdbh + "");
//						acewillCouponTempMapper.delDsfls(kdzdbh, "ACEWILL");
                    } else {
                        dataList.add(JSONObject.fromObject(tqzdk));
                    }
                }
            }
            if (result_ == 0) {
                result.setData(dataList);
                result.setMsg(Constant.FIND_BILL_SUCCESS);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result_ = -1;
            result.setMsg("系统异常");
            LOGGER.error("查询账单报错:{}", e.getMessage());
        } finally {
            result.setCode(result_ == 0 ? Constant.CODE_SUCCESS : -1);
            result.setSuccess(result_ == 0);
        }
    }

    /*
     * 异常账单处理，只有取消和重试两种状态
     *
     */
    @Transactional
    public void exceptionBillHandler(Data data, Data result) {
        Map<String, Object> map = ReqDataUtil.getDataMap(data);
        String zdbh = ParamUtil.getStringValue(map, "zdbh", false, null);// 帐单编号
        String dataType = ParamUtil.getStringValue(map, "ptype", false, null);// 支付类型
        String skyh = ParamUtil.getStringValue(map, "czybh", false, null);// 操作员编号
        String bcid = ParamUtil.getStringValue(map, "bcid", false, null);// 班次id
        PaymentType pt = null;
        pt = PaymentType.valueOf(dataType.toUpperCase());

        switch (pt) {
            case CANCEL: // 取消，删除相应的账单，第三方支付/验券退掉
                tqThirdExceptOrderMapper.constraintByRefund(zdbh, "8");
                tqYhMtCouponsMapper.updateYhMtCouponsMapperToLogin("8", "n", zdbh);

                acewillCouponTempMapper.delTwct(zdbh + "");
                acewillCouponTempMapper.delTmi(zdbh + "");
                acewillCouponTempMapper.delDsfls(zdbh, "ACEWILL");

                tqZdkMapper.delTpd(zdbh + "", 0, "");
                tqZdkMapper.delFkls(zdbh);
                tqZdkMapper.delWdk(zdbh + "");
                tqZdkMapper.delZdk(zdbh);

                result.setMsg("删除账单成功，已将该账单第三方付款返还！");
                result.setCode(SysDictionary.SUCCESS);
                result.setSuccess(true);

                break;
            // 查询后，如果是支付成功，需要更新状态 状态 为空：未知 1：支付中 0：成功
            case QUERY: // 重试，包括第三方付款重新查询和微生活会员的重新预消费，消费

                List<TqThirdExceptOrder> tteolist = tqZdkMapper.findRetryTteoList(zdbh);
                List<WdDishVo> wdv = tqZdkMapper.getWdDish(zdbh);
                TsPsjgsdk tsPsjgsdk = tqZdkMapper.getJg();

                List<TqMemberInfo> amis = acewillMemberinfoMapper.getAcewillMembers(zdbh, "");
                if (amis.size() > 0) {
                    result.setCode(Constant.CODE_PARAM_FAILURE);
                    result.setSuccess(false);
                    result.setMsg("本单包含会员相关信息，无法重新支付，请取消后重新下单！");
                } else {
                    for (TqThirdExceptOrder tteo : tteolist) {
                        PayResultData payResultData = new PayResultData();

                        String outtradeno = tteo.getOrderno();
                        String jtbh = tteo.getPosno();
                        PaymentWay paymentWay = null;
                        String payNum = tteo.getScan_code();
                        paymentWay = checkType(payNum);
                        String fkfsid = tteo.getPaytypeid();
                        String lxdh = "";
                        BigDecimal actualmoney = new BigDecimal(tteo.getCashamount());
                        String yhsx = "-1";
                        if ("ERP_FKFS_ZFB".equals(paymentWay.getPayment_mark())) {
                            yhsx = "61";
                        } else if ("ERP_FKFS_WX".equals(paymentWay.getPayment_mark())) {
                            yhsx = "62";
                        }
                        Dish dsfyh = tqZdkMapper.getDsfyh(yhsx);

                        if ("0".equals(tteo.getPay_status())) {
                            // 关单
                            closeBill(zdbh, fkfsid, actualmoney, payNum, lxdh, outtradeno, jtbh, skyh, Integer.parseInt(bcid));
                            result.setCode(SysDictionary.SUCCESS);
                            result.setMsg("支付重试成功，已关闭账单");
                            result.setSuccess(true);
                            break;
                        } else {
                            String rifUrl = tqZdkMapper.getRifUrl();
                            String xmlStr = toJointXML(tsPsjgsdk, "03", fkfsid, outtradeno, tteo.getScan_code(), jtbh, tteo.getCashamount() + "", wdv);
                            LOGGER.info("requestXml:" + xmlStr);
                            String rxmlStr = "";
                            rxmlStr = SendRequest.sendPost(rifUrl, xmlStr);
                            LOGGER.info("responseXml:" + rxmlStr);

                            if ("-1".equals(rxmlStr)) {
                                result.setCode(Constant.CODE_PARAM_FAILURE);
                                result.setSuccess(false);
                                result.setMsg("总部接口异常！");
                                continue;
                            } else if ("timedout".equals(rxmlStr)) {
                                result.setCode(Constant.CODE_PARAM_FAILURE);
                                result.setSuccess(false);
                                result.setMsg("网络请求超时，请稍后再试！");
                                continue;
                            } else {
                                try {
                                    readStringXmlOut(rxmlStr);
                                } catch (Exception e) {
                                    result.setCode(Constant.CODE_PARAM_FAILURE);
                                    result.setMsg("返回数据异常！");
                                    result.setSuccess(false);
                                    e.printStackTrace();
                                    continue;
                                }
                                payResultData = tlPrd.get();

                                lxdh = payResultData.getTradeno();
                                // 处理结果：0成功，其他失败(1：交易出现错误，2：下单成功，支付处理中3：该交易已关闭)
                                if ("0".equals(payResultData.getStatus())) {
                                    String yhfsid = payResultData.getYhfsid();
                                    if ("ERP_FKFS_WX".equals(paymentWay.getPayment_mark())) {
                                        yhfsid = payResultData.getWxyhfsid();
                                    }
                                    if (null != yhfsid && !"".equals(yhfsid) && !"null".equals(yhfsid) && !"NULL".equals(yhfsid)) {
                                        BigDecimal pa = new BigDecimal(payResultData.getPrivilegeamount());
                                        tqZdkMapper.delYhtemp(zdbh);
                                        TqYhMtCouponsTemp tymct = new TqYhMtCouponsTemp();
                                        tymct.setYhfsid(Integer.parseInt(dsfyh.getYhfsid()));
                                        tymct.setClmxid(-1);
                                        tymct.setKdzdbh(zdbh);
                                        tymct.setPayableamt(new BigDecimal(-1));
                                        tymct.setPqlx("2");
                                        tymct.setYzm(outtradeno);
                                        tymct.setJzid(-1);
                                        tymct.setOptype(2);
                                        tymct.setSjje(new BigDecimal(-1));
                                        tymct.setCmid(-1);
                                        tymct.setBuyprice(pa.abs());
                                        tqYhMtCouponsTempMapper.deleteByZdbh(zdbh);
                                        tqYhMtCouponsTempMapper.insert(tymct);
                                        tqZdkMapper.addCm(zdbh, -1, 1, jtbh, "", "", Math.random() * 10000 + "", dsfyh.getYhfsid(), 0);
                                        tqZdkMapper.zRtr(zdbh);
                                        CalcMoney cm = tqZdkMapper.findCalcMoney(zdbh);
                                        actualmoney = new BigDecimal(payResultData.getFkje());
                                    }

                                    tqThirdExceptOrderMapper.updateToStatus(outtradeno, "0");

                                    closeBill(zdbh, fkfsid, actualmoney, payNum, lxdh, outtradeno, jtbh, skyh, Integer.parseInt(bcid));

                                    payResultData.setJzid(fkfsid);
                                    List<JSONObject> dataList = new ArrayList<JSONObject>();
                                    dataList.add(JSONObject.fromObject(GsonUtil.GsonString(payResultData)));
                                    result.setData(dataList);
                                    result.setCode(SysDictionary.SUCCESS);
                                    result.setMsg("支付重试成功，已关闭账单");
                                    result.setSuccess(true);
                                    break;
                                } else {
                                    result.setCode(Constant.CODE_PARAM_FAILURE);
                                    result.setMsg(payResultData.getMsg());
                                    result.setSuccess(false);
                                }
                            }
                        }
                    }
                }

                break;
            default:
                break;
        }

    }

    public void closeBill(String zdbh, String fkfsid, BigDecimal actualmoney, String payNum, String lxdh, String outtradeno, String jtbh, String skyh, int bcid) {
        int cdf = tqZdkMapper.countDsfFkls(zdbh, Integer.parseInt(fkfsid), "");
        if (cdf == 0) {
            tqZdkMapper.accountsOrder(zdbh, Integer.parseInt(fkfsid), actualmoney, 1, payNum, "", lxdh, outtradeno, jtbh, skyh);
        }

        String jzzdbhl = "0";
        int jzbcid = bcid;
        jzzdbhl = createBh(jtbh, "TQ_ZDK", "JZZDBH");
        String jzzdbh = jzzdbhl;

        // 更新 tq_zdk tq_wdk tq_fklslsk
        Map<String, String> bbrqMap = tqJtztkMapper.findBbrq();
        String jzbbrq = DateUtil.getNowDateYYDDMM();
        if (null != bbrqMap && bbrqMap.size() != 0) {
            jzbbrq = bbrqMap.get("bbrq");
        }
        Date jzjssj = new Date();
        tqZdkMapper.updateZdk("", "", "", zdbh, jzzdbh, DateUtil.parseDate(jzbbrq), jzjssj, 1, jtbh, skyh, "ZDSX_YJ", jzjssj, jzjssj, jzbcid, 1);
        tqZdkMapper.updateWdk(zdbh, jzzdbh, jtbh, DateUtil.parseDate(jzbbrq), jzbcid);
        tqZdkMapper.updateFklslsk(zdbh, jzzdbh, jzbcid);
    }

    @Transactional
    public synchronized Data billSynchronized(String number, int operType) throws Exception {
        // 创建返回数据对象
        Data data = new Data();
        // operType=1：表示申请锁定；非1：表示处理完成，需要解锁
        if (operType == 1) {
            // 检查是否正在处理
            if (numberMap.containsKey(number)) {
                // 正在处理，返回2
                data.setSuccess(false);
                data.setMsg("处理中，请稍后再试！");
                return data;
            }
            // 加锁完成
            numberMap.put(number, 1);
            data.setMsg("锁定成功！");
            data.setSuccess(true);
            return data;
        } else {
            // 删除已经处理完成账单，清除列表中的对应订单数据
            numberMap.remove(number);
            data.setSuccess(true);
            data.setMsg("解锁成功！");
            return data;
        }
    }

    public String createEInvoice(String kdzdbh, Date jzsj, Date bbrq) {
        try {
            String organId = InitDataListener.organVo.getJgxh() + ""; // 机构id
            String tenancyId = InitDataListener.ggcsMap.get("tenancy_id"); // 发票商户id？
            String billId = kdzdbh; // 账单号
            double amount = tqZdkMapper.getKpje(kdzdbh); // 开票金额
            String ddrq = DateUtil.getYYYYMMDDFromDate(jzsj);// 结账时间 YYYYMMDD
            String time = DateUtil.gethhmmss(); // 当前时间HHMMSS
            String serviceType = "1"; // ?
            String url = InitDataListener.ggcsMap.get("KFPUrl"); // 电子发票请求地址（乡村基）
            String posInvoiceChanel = InitDataListener.ggcsMap.get("POS_INVOICE_CHANEL"); // 开发票渠道类型
            String dzfpqqdz = InitDataListener.ggcsMap.get("DZFPQQDZ");// 电子发票请求地址（通用）
            String sl = InitDataListener.organVo.getSl() + ""; // 电子发票税率
            String key = InitDataListener.organVo.getDzfpmy(); // 电子发票密钥
            String tssfkfp = InitDataListener.organVo.getTssfkfp();

            String lvNewUrl = "";

            LOGGER.info("开具电子发票：堂食是否开发票= " + tssfkfp + ",类型= " + posInvoiceChanel);

            if ("Y".equals(tssfkfp) && amount != 0) {
                if ("1".equals(posInvoiceChanel)) {
                    // 乡村基
                    if ("".equals(url)) {
                        LOGGER.info("没有配置总部电子发票请求地址，请正确设置后再重试。");
                        lvNewUrl = "";
                    } else {
                        String lvDH = billId + "@" + tenancyId + organId + ddrq + time; // 这个要保存到数据库中，取消用
                        String lvUrlPara = tenancyId + "#" + organId + "#" + ddrq + "#" + billId + "@" + time + "#" + serviceType + "#" + sl + "#" + amount;
                        LOGGER.info("开具电子发票：明文APlaintext= " + lvUrlPara + ",AKey = " + key);

                        String aCiphertext = AESUtil.aesEncodeNew(lvUrlPara, key);
                        aCiphertext = delBlankStr(aCiphertext); // 删除空格
                        String aMD5Value = MD5Util.md5(aCiphertext).toLowerCase();
                        aMD5Value = aMD5Value.substring(0, 4);
                        lvUrlPara = Base64.byteArrayToBase64((delBlankStr(lvUrlPara) + "#" + aMD5Value).getBytes());
                        lvUrlPara = delBlankStr(lvUrlPara);
                        LOGGER.info("开具电子发票：密文= " + lvUrlPara); // 记录操作日志
                        lvNewUrl = url + lvUrlPara; // 返回的字符串
                        LOGGER.info("开具电子发票：二维码内容= " + lvNewUrl);
                        // 乡村基电子发票 更新账单数据
                        tqZdkMapper.updateByInvoice(billId, lvDH, amount, "Y");
                    }
                } else {
                    // 通用
                    if ("".equals(dzfpqqdz)) {
                        LOGGER.info("没有配置总部电子发票请求地址，请正确设置后再重试。");
                        lvNewUrl = "";
                    } else {
                        String bbrqs = DateUtil.format(bbrq, "yyyy-MM-dd");
                        String lvUrlPara = tenancyId + "#" + organId + "#" + bbrqs + "#" + billId + "#" + serviceType + "#" + 0 + "#" + amount;
                        LOGGER.info("开具电子发票：明文APlaintext= " + lvUrlPara + ",AKey = TZXSAASZFP2222I9");
                        lvUrlPara = AESUtil.aesEncodeNew(lvUrlPara, "TZXSAASZFP2222I9");
                        lvUrlPara = Base64.byteArrayToBase64(lvUrlPara.getBytes());
                        LOGGER.info("开具电子发票：密文= " + lvUrlPara); // 记录操作日志
                        lvNewUrl = dzfpqqdz + "/elecinvoice/get?para=" + lvUrlPara;
                        LOGGER.info("开具电子发票：二维码内容= " + lvNewUrl);
                        tqZdkMapper.updateByInvoice(billId, "", amount, "Y");
                    }
                }
            }

            return lvNewUrl;
        } catch (Exception e) {
            LOGGER.error("创建电子发票报错:", e);
            return "";
        }

    }

    public String delBlankStr(String str) {
        str = str.replace(" ", "").replaceAll("[\\t\\n\\r]", "");
        return str;
    }

    public String createOuttradeno(String identification, int jgxh, String jtbh, String zdbh, String date_time) {
        // 第一部分 4位开发标识 tbxf + 4位 OrganID + 2位机号 = 10位
        // 第二部分 8位 lvPartBillNo 账单号的后8位 = 8位
        // 第三部分 14位 lvPaytime 付款时间 = 14位
        // 当第一部分大于10位的时候，先扣除时间的前两位来补，如果不够扣除账单编号来补
        String outtradeno = "";
        int lengthDiff = 0; // 长度差异
        outtradeno = identification + jgxh + jtbh;
        lengthDiff = 10 - outtradeno.length();
        // 第一部分 <=10， 长度不足，用账单号来补位
        if (lengthDiff >= 0) {
            zdbh = zdbh.substring((zdbh.length() - (8 + lengthDiff)), zdbh.length());
            outtradeno = outtradeno + zdbh + date_time;
        }
        // 第一部分 >10
        if (lengthDiff < 0) {
            // 如果超长位数少于或等于2位，只减少日志前两位
            lengthDiff = Math.abs(lengthDiff);
            if (lengthDiff <= 2) {
                zdbh = zdbh.substring((zdbh.length() - 8), zdbh.length());
                date_time = date_time.substring(lengthDiff, date_time.length());
            }
            // 如果超长位数大于2，减少日期前两位，剩下的从账单号扣除
            if (lengthDiff > 2) {
                zdbh = zdbh.substring((zdbh.length() - (8 - lengthDiff + 2)), zdbh.length());
                date_time = date_time.substring(2, date_time.length());
            }
            outtradeno = outtradeno + zdbh + date_time;
        }
        return outtradeno;
    }

    // 实时沽清
    private Data DecSaleOut(Data data, List<ReqParam> reqParams) {
        if (!(null != InitDataListener.ggcsMap.get("ISREALTIMEDISHCOUNT") && InitDataListener.ggcsMap.get("ISREALTIMEDISHCOUNT").equals("Y"))) {
            LOGGER.info("未启用实时沽清参数ISREALTIMEDISHCOUNT");
            data.setSuccess(true);
            return data;
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("dishinfo", GsonUtil.GsonString(reqParams));
        CommApiData commApiData = new CommApiData();
        saleOutApiService.SaleOutDec(commApiData, jsonObject);

        if (commApiData.getCode().equals(0)) {
            data.setSuccess(true);
        } else {
            data.setSuccess(false);
            data.setMsg(commApiData.getMsg());
        }
        return data;
    }

    private void AddSaleOut(List<ReqParam> reqParams) {
        if (!(null != InitDataListener.ggcsMap.get("ISREALTIMEDISHCOUNT") && InitDataListener.ggcsMap.get("ISREALTIMEDISHCOUNT").equals("Y"))) {
            LOGGER.info("未启用实时沽清参数ISREALTIMEDISHCOUNT");
            return;
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("dishinfo", GsonUtil.GsonString(reqParams));
        CommApiData commApiData = new CommApiData();
        saleOutApiService.SaleOutAdd(commApiData, jsonObject);
    }

    public void commUseYhfs(TqZdk zdk, List<TqFklslsk> fklslskList, String skjh, String jzbbrq) {
        for (int i = 0; i < fklslskList.size(); i++) {
            TqFklslsk fklslsk = fklslskList.get(i);
            if ("N".equals(fklslsk.getSfsr())) {
                TsYhfssdk yhfssdk = tqZdkMapper.getBjyhYh("BJSR" + fklslsk.getFkfsbh());
                if (null != yhfssdk) {
                    UseYhfsParam useYhfsParam = new UseYhfsParam();
                    useYhfsParam.setBillid(zdk.getKdzdbh());
                    useYhfsParam.setOpType(91003);
                    useYhfsParam.setYhfsId(yhfssdk.getId());
                    useYhfsParam.setFklsid(fklslsk.getId());
                    useYhfsParam.setSkjh(skjh);// 机号
                    useYhfsParam.setJgtxbh(InitDataListener.organVo.getJgtxbh());// 价格体系编号
                    useYhfsParam.setDisAmount(new BigDecimal(fklslsk.getFkje()));
                    useYhfsParam.setBbrq(DateUtil.parseDate(jzbbrq));
                    if ((i + 1) == fklslskList.size() && 0 != zdk.getDslj()) {
                        useYhfsParam.setDslj(new BigDecimal(zdk.getDslj()));
                        useYhfsParam.setHasDslj(true);
                    }
                    useYhfsParam.setInnerCalMoney(true);// 由接口内部使用均摊及账单金额重计算

                    useYhfsApiService.CommUseYhfs(useYhfsParam);
                }
            }
        }
    }

    public Data checkDiscount1(String kdzdbh, int discount_id, String yhsx) throws Exception {
        Data result = new Data();
        int result_ = -1;
        JSONObject robj = new JSONObject();
        List<JSONObject> dataList = new ArrayList<JSONObject>();
        try {
            Integer icbid = tqZdkMapper.findIcibd("TS");
            String sjgtxbh = tqZdkMapper.findJgtxbh(kdzdbh);
            TqZdk zdk = tqZdkMapper.getZdk(kdzdbh);
            List<BuywdandYhitems> buywd = tqZdkMapper.getBuywdandYhitems(kdzdbh, discount_id, yhsx, icbid, zdk.getXsms(), sjgtxbh);
            List<BuywdandYhitems> yhneed = tqZdkMapper.getYhneedChoitems(discount_id, yhsx, icbid, zdk.getXsms(), sjgtxbh);

            int buydishrel = tqZdkMapper.getBuydishrel(discount_id);

            if (buydishrel == 1) {
                for (BuywdandYhitems buywds : buywd) {
                    double nac = Double.parseDouble(buywds.getNeedaddcmsl());
                    if (nac != 0) {
                        result_ = -1;
                        break;
                    } else {
                        result_ = 0;
                    }
                }
            } else if (buydishrel == 0) {
                if (null == buywd) {
                    result_ = -1;
                } else {
                    result_ = 0;
                }
            }

            if ("16".equals(yhsx) && null != buywd) {
                yhneed = buywd;
                result_ = 0;
            }

            if (result_ == 0) {
                robj.put("discount_id", discount_id);
                robj.put("discounttypeid", yhsx);
                robj.put("buydishrel", buydishrel);
                robj.put("buywd", buywd);
                robj.put("yhneed", yhneed);
                dataList.add(JSONObject.fromObject(GsonUtil.GsonString(robj)));
            }

            result.setData(dataList);
            result.setCode(result_);
            result.setMsg(result_ == 0 ? "优惠可用" : "优惠方式不满足限制条件！");
            result.setSuccess(result_ == 0);
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            result_ = -1;
            result.setMsg("系统异常");
            LOGGER.error("验证优惠方式错误:{}", e.getMessage());
            return result;
        }
    }

    /**
     * 校验当前券是否可用
     * 1.校验是否与其他券共用
     * 2.校验最多使用几张
     * 3.校验启用金额，满/每满
     * 4.校验菜品券 菜品是否可用
     *
     * @param tacc
     * @return
     */
    public Map<String, String> checkCouponQm(TqAcewilCouponCache tacc) {
        Map<String, String> ccMap = new HashMap<String, String>();

        String zdbh = tacc.getZdbh();
        String type = tacc.getCoupontype();
        String templateid = tacc.getTemplateid();
        String couponcode = tacc.getCouponcode();
        JSONObject couponobj = JSONObject.fromObject(tacc.getRemark());

        ccMap.put("code", "-1");
        if (null == tacc) { // 不存在或不符合条件
            ccMap.put("msg", "该券不可用！");
            return ccMap;
        }
        if (tacc.getUseok() == 1) { // 该券在本地的使用情况 1：已使用，0：未使用
            ccMap.put("msg", "该券已被使用！");
            return ccMap;
        }
        // 1.1 校验券使用数量
        List<Map<String, Object>> cucList1 = acewillCouponTempMapper.getCouponUseCount(zdbh, templateid, "3", "QIMAI");
        if (cucList1.size() > 0) {
            ccMap.put("msg", "每单最多使用一张优惠券！");
            return ccMap;
        }

        ccMap.put("code", "0");
        ccMap.put("msg", "OK");
        return ccMap;
    }


    public JSONObject qimaiPreview(String zdbh, String userCouponId, double balance) {
        JSONObject ua = new JSONObject();
        ua.put("code", -1);
        ua.put("message", "预览请求失败");
        List<TqMemberInfo> amis = acewillMemberinfoMapper.getAcewillMembers(zdbh, "");
        if (null != amis && amis.size() > 0) {
            TqMemberInfo ami = amis.get(0);
            JSONObject res = JSONObject.fromObject(ami.getRemark());
            JSONObject detailInfo = res.optJSONObject("detailInfo");

            String couponId = "";
            TqAcewilCouponCache tacc = acewillCouponTempMapper.getCouponByCode(zdbh, userCouponId);

            if (null != tacc) {
                couponId = tacc.getTemplateid();
            }

            String qmType = "1";
            if (null != InitDataListener.ggcsMap.get("QIMAITYPE")) {
                qmType = InitDataListener.ggcsMap.get("QIMAITYPE");
            }

            if (null != InitDataListener.ggcsMap.get("QIMAIDISHTTYPE")) {
                if ("0".equals(InitDataListener.ggcsMap.get("QIMAIDISHTTYPE"))) {
                    qmType = "2";
                }
                if ("1".equals(InitDataListener.ggcsMap.get("QIMAIDISHTTYPE"))) {
                    qmType = "1";
                }
            }

            List<QmWdDishVo> qmWd = tqZdkMapper.getWdQmDish(zdbh, qmType);

            double useBalance = acewillMemberinfoMapper.findBalanceAmount(zdbh, "1973");
            useBalance = ArithUtil.add(useBalance, balance);

            JSONObject params = new JSONObject();
            params.put("user_coupon_id", userCouponId);
            params.put("user_id", detailInfo.optString("cardNo"));
            params.put("items", qmWd);
            params.put("multi_mark", InitDataListener.organVo.getJgbh() + "");
            params.put("coupon_id", couponId);
            params.put("type_cate", "1");
            params.put("saleType", "1");
            params.put("discount_price", "0");
            params.put("type", "0");
            if (0 == useBalance) {
                params.put("is_use_balance", "0");
            } else {
                params.put("is_use_balance", "1");
            }
            params.put("phone", detailInfo.optString("mobilePhone"));
            params.put("use_balance", useBalance);

            JSONObject obj = new JSONObject();
//			obj.put("Action", "communal.user.consume-preview");
            obj.put("Action", "user.consume-preview");
            obj.put("Params", params);

            CommApiData dataR = new CommApiData();
            commApiService.qmMemberRequest(dataR, obj);
            String rStr = "";
            if (0 == dataR.getCode()) {
                rStr = dataR.getData().toString();
                ua = JSONObject.fromObject(rStr);
            }
        }
        return ua;
    }

    /**
     * @param @param rifUrl
     * @param @param xmlStr
     * @param @param result
     * @return void
     * @throws Exception
     * @throws
     * @Description: 抖音验券调用接口
     * <AUTHOR>
     * @email <EMAIL>
     * @date 2021-10-19
     */
    public void dyQuan(List<WdDishVo> wdv, String rifUrl, String xmlStr, Data result, String zdbh, String payType, Integer yhfsid, String jtbh, String yzm, String skyh, Integer fksl, String pay_type, String fkfsid, String fklsid, int jzid, String yhsx, String outtradeno) {
        String responseXml = SendRequest.sendPost(rifUrl, xmlStr);
        LOGGER.info("抖音券返回报文为:" + responseXml);
        BigDecimal actualmoney = null;
        String status = null;
        //String voucher = null;
        Integer xmid = null;
        String xmids = null;

        DATAPACKET dataPacker = null;
        String wdrwid = null;
        String pqlx = null;
        String sjje = null;
        String sfkbz = "";
        String couponbuyprice = null;
        TqYhMtCouponsTemp tqYhMtCouponsTemp = new TqYhMtCouponsTemp();
        try {
            readStringXmlOut(responseXml);
            if (null == responseXml || "".equals(responseXml)) {
                result.setCode(-1);
                result.setMsg(Constant.MEITUAN_EXCEPTION);
                LOGGER.error(Constant.MEITUAN_EXCEPTION);
                result.setSuccess(false);
            } else {
                dataPacker = (DATAPACKET) convertXmlStrToObject(DATAPACKET.class, responseXml);
                result.setCode(Constant.CODE_SUCCESS);
                result.setMsg(dataPacker.getMSG().getDATAS().get(0).getROWDATA().getROW().getMSG());
                result.setSuccess(true);
                status = dataPacker.getMSG().getDATAS().get(0).getROWDATA().getROW().getSTATUS();
                //	voucher = dataPacker.getMSG().getDATAS().get(0).getROWDATA().getROW().getVOUCHER();
                wdrwid = dataPacker.getMSG().getDATAS().get(0).getROWDATA().getROW().getWDRWID();
                pqlx = dataPacker.getMSG().getDATAS().get(0).getROWDATA().getROW().getPQLX();
                tqYhMtCouponsTemp.setYhfsid(yhfsid);
                tqYhMtCouponsTemp.setKdzdbh(zdbh);
                tqYhMtCouponsTemp.setPqlx(pqlx);
                tqYhMtCouponsTemp.setYzm(outtradeno);
                tqYhMtCouponsTemp.setOptype(21);
                if ("0".equals(status)) {
                    result.setCode(0);
                    result.setSuccess(true);
                    xmids = dataPacker.getMSG().getDATAS().get(0).getROWDATA().getROW().getXMID();
                    sjje = dataPacker.getMSG().getDATAS().get(0).getROWDATA().getROW().getSJJE();
                    couponbuyprice = dataPacker.getMSG().getDATAS().get(0).getROWDATA().getROW().getCOUPONBUYPRICE();
                    sfkbz = xmids;
                    if (null == couponbuyprice || "null".equals(couponbuyprice)) {
                        couponbuyprice = "0";
                    }
                    tqYhMtCouponsTemp.setBuyprice(new BigDecimal(couponbuyprice));
                    if (null != xmids && !"null".equals(xmids)) {
                        xmid = Integer.parseInt(xmids);
                    } else {
                        xmid = -1;
                    }
                    if (null == sjje || "null".equals(sjje) || "NaN".equals(sjje)) {
                        sjje = "0";
                    }
                    if ("PAY".equals(payType)) {
                        tqYhMtCouponsTemp.setSjje(new BigDecimal(sjje));
                        if ("0".equals(pqlx)) {        //0菜品券  1:现金券
                            actualmoney = new BigDecimal(dataPacker.getMSG().getDATAS().get(0).getROWDATA().getROW().getSJJE());
                            tqYhMtCouponsMapper.updateYhDyCouponsMapper(new BigDecimal(sjje), yzm, "y", "0", new Date(), zdbh, new BigDecimal(couponbuyprice), pqlx, xmid, Integer.parseInt(wdrwid), new BigDecimal(couponbuyprice), outtradeno);
                            //查看clmxid
                            tqYhMtCouponsTemp.setClmxid(tqYhMtCouponsTempMapper.findClmxid(xmid));
                            tqYhMtCouponsTemp.setPayableamt(actualmoney);
                            tqYhMtCouponsTemp.setCmid(xmid);
                            tqYhMtCouponsTemp.setWdrwid(Integer.parseInt(wdrwid));
                        }
                        if ("1".equals(pqlx)) {
                            //删除相同账单,防止重单 菜品券可重复，现金券不能重复
                            tqYhMtCouponsMapper.deleteYhDyCouponsMapper(zdbh, outtradeno);
                            insetTqYhMtCoupons("n", null, new BigDecimal(0), yhfsid, -1, zdbh, yzm, "0", jzid, new BigDecimal(sjje), outtradeno);
                            actualmoney = new BigDecimal(dataPacker.getMSG().getDATAS().get(0).getROWDATA().getROW().getPAYABLEAMT());
                            tqYhMtCouponsTemp.setClmxid(-1);
                            tqYhMtCouponsTemp.setPayableamt(actualmoney);
                            tqYhMtCouponsTemp.setCmid(-1);
                            tqYhMtCouponsMapper.updateYhDyCouponsMapper(null, yzm, "y", "0", new Date(), zdbh, new BigDecimal(couponbuyprice), pqlx, xmid, null, new BigDecimal(couponbuyprice), outtradeno);
                            tqYhMtCouponsMapper.findClid(yhsx);
                        }
                        tqYhMtCouponsTempMapper.deleteByZdbh(zdbh);
                        tqYhMtCouponsTempMapper.insert(tqYhMtCouponsTemp);
                        tqZdkMapper.addCm(zdbh, tqYhMtCouponsMapper.findClid(yhsx), 1, jtbh, skyh, outtradeno, null, null, 0);
                        tqZdkMapper.zRtr(zdbh);
                        tqZdkMapper.findCalcMoney(zdbh);
                    }
                    if ("CANCEL".equals(payType)) {
                        if (StringUtils.isNotBlank(yzm)) {
                            //根据验证码更新数据
                            tqYhMtCouponsMapper.updateDyMtCouponsByYzm("5", yzm);
                            //取消第三方优惠
                            tqZdkMapper.cancelThirdYhfs(zdbh, yzm, yhfsid);
                            tqZdkMapper.findCalcMoney(zdbh);
                        }
                    }
                } else if ("1".equals(status)) {
                    result.setCode(-1);
                    result.setSuccess(false);
                    tqYhMtCouponsMapper.updateYhMtCouponsMapper1(null, yzm, "y", "1", new Date(), zdbh, null, pqlx, xmid, null, null);
                } else {
                    result.setCode(-1);
                    result.setSuccess(false);
                }
            }
            //添加流水
            if ("PAY".equals(payType)) {
                if ("0".equals(status)) {
                    AccountsOrder ao = tqZdkMapper.accountsOrder(zdbh, Integer.parseInt(fkfsid), new BigDecimal(couponbuyprice), fksl, outtradeno, "", "", sfkbz, jtbh, skyh);
                    result.setCode(0);
                    result.setSuccess(true);
                }
            }
            if ("CANCEL".equals(payType)) {
                if ("0".equals(status)) {
                    if (StringUtils.isNotBlank(yzm)) {
                        tqZdkMapper.delFkje(zdbh, pay_type, Integer.parseInt(fklsid), yzm, "");
                    }
                    result.setCode(0);
                    result.setSuccess(true);
                }
            }
            double open_amount = 0.00;
            if ("0".equals(status)) {
//				if("PAY".equals(payType)) {
                double yfje = tqZdkMapper.getFkje(zdbh);
                double zdje = tqZdkMapper.getZdje(zdbh);
                open_amount = DoubleUtil.sub(zdje, yfje);
//				}
            }
            PayResultData payResultData1 = new PayResultData();
            payResultData1.setOpen_amount(open_amount + "");
            payResultData1.setFkls(tqZdkMapper.getFklsList(zdbh));
            List<CouponRunningWater> crw = new ArrayList<CouponRunningWater>();
            List<CouponRunningWater> crw4 = tqZdkMapper.getCouponlsList(zdbh, "4");
            List<CouponRunningWater> crw6 = tqZdkMapper.getCouponlsList(zdbh, "6");
            crw.addAll(crw4);
            crw.addAll(crw6);
            payResultData1.setCouponls(crw);
            List<JSONObject> dataList = new ArrayList<JSONObject>();
            dataList.add(JSONObject.fromObject(GsonUtil.GsonString(payResultData1)));
            result.setData(dataList);
        } catch (Exception e) {
            e.printStackTrace();
            result.setCode(-1);
            result.setMsg(Constant.MEITUAN_EXCEPTION);
            LOGGER.error(Constant.MEITUAN_EXCEPTION);
            LOGGER.error("Ignore this exception", e);
            result.setSuccess(false);
        }
    }
}
