---本SQL文件用于维护门店升级需要执行的脚本---

---2023-02-04 总部添加添加系统参数:微生活小程序接口版本
DELETE FROM  ts_ggcsk where  sdbt ='';
INSERT INTO "public"."ts_ggcsk" ( "sdbt", "sdnr", "syfw", "bzsm", "qybh", "jhid", "yl1", "yl2", "yl3")
SELECT 'deliveryFeeBindDishCode', '1061099991', '系统设定', '外卖配送费绑定菜品编码', '', '', '', '', ''
    WHERE NOT EXISTS ( SELECT 0 FROM ts_ggcsk WHERE sdbt = 'deliveryFeeBindDishCode');

---添加抖音外卖付款---
INSERT INTO "public"."tq_ecotypedic"("fkfsbh", "thirdcode", "tzxcode", "tzxname") VALUES ('1934', '30', 'DY', '抖音外卖');


--付款方式表中 增加 partialrefundornot
select p_addfield('ts_fkfssdk','partialrefundornot','varchar(10) default '''' ');
select p_addfield('ts_fkfssdk_xf','partialrefundornot','varchar(10) default '''' ');

---添加系统参数，控制ECO菜品是否按名称匹配(Y:启用,N:未启用（默认值）)
INSERT INTO "public"."ts_ggcsk" ("sdbt", "sdnr", "syfw", "bzsm", "qybh", "jhid", "yl1", "yl2", "yl3")
SELECT 'EcoItemMapByName',
       'N',
       '系统设定',
       'ECO外卖菜品是否按名称匹配',
       '',
       '',
       '',
       '',
       ''
WHERE NOT EXISTS (SELECT 0 FROM ts_ggcsk WHERE sdbt = 'EcoItemMapByName');

---添加系统参数，ECO外卖实收是否包含平台佣金(Y:包含,N:不包含（默认值）)
INSERT INTO "public"."ts_ggcsk" ("sdbt", "sdnr", "syfw", "bzsm", "qybh", "jhid", "yl1", "yl2", "yl3")
SELECT 'EcoActualPayIncludeCommission',
       'N',
       '系统设定',
       'ECO外卖实收是否包含平台佣金',
       '',
       '',
       '',
       '',
       ''
WHERE NOT EXISTS (SELECT 0 FROM ts_ggcsk WHERE sdbt = 'EcoActualPayIncludeCommission');

---添加京东秒送渠道和付款---
INSERT INTO "tq_ecotypedic"("fkfsbh", "thirdcode", "tzxcode", "tzxname") VALUES ( '1992', '31', 'JD31', '京东秒送外卖');
INSERT INTO "public"."ts_fkfssdk"("fkfzid","id", "fkfsbh", "fkfsmc1", "fkfsmc2", "fklxsx", "pxbh", "sfyx", "sfsr", "sfbb", "sfkfp", "sffq", "sfxtfkfs", "hl", "sfjf", "syye", "fkme", "cwkmbh", "cwkmmc", "memo", "yl1", "yl2", "yl3", "thirdsource", "partialrefundornot") VALUES (1,956,'1992', 'ECO京东秒送外卖付款', '', 'FKSX_DSF', 0, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', NULL, 'N', '0.00', NULL, '', '', '', '', '', '', '', '');

---2024-07-16 tq_wdk添加优惠券结算金额字段
select p_addfield('tq_wdk','yhqjsje','numeric(12)');

--2024-11-28 bt_tcselectmx添加菜品名称和ECO套餐id字段
select p_addfield('bt_tcselectmx','cmmc','varchar(255)');
select p_addfield('bt_tcselectmx','bsetmealid','varchar(255)');
--2024-11-28 bt_ydd添加ECO套餐id字段
select p_addfield('bt_ydxm2','bsetmealid','varchar(255)');

---添加系统参数，ECO外卖可选套餐强制转换成单品
INSERT INTO "public"."ts_ggcsk" ("sdbt", "sdnr", "syfw", "bzsm", "qybh", "jhid", "yl1", "yl2", "yl3")
SELECT 'EcoTcToDp',
       'Y',
       '系统设定',
       'ECO外卖可选套餐强制转换成单品',
       '',
       '',
       '',
       '',
       ''
WHERE NOT EXISTS (SELECT 0 FROM ts_ggcsk WHERE sdbt = 'EcoTcToDp');

---添加系统参数，微生活菜品渠道来源
INSERT INTO "public"."ts_ggcsk" ("sdbt", "sdnr", "syfw", "bzsm", "qybh", "jhid", "yl1", "yl2", "yl3")
SELECT 'WlifeDishSource',
       'TS',
       '系统设定',
       '微生活小程序菜品渠道来源',
       '',
       '',
       '',
       '',
       ''
WHERE NOT EXISTS (SELECT 0 FROM ts_ggcsk WHERE sdbt = 'WlifeDishSource');

---添加omp_boh_id_ref表-
CREATE TABLE "public"."omp_boh_id_ref" (
       "name" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
       "brand_id" int4,
       "boh_id" int4,
       "omp_id" int4 NOT NULL,
       "jgxh" int4,
       "omp_brand_id" varchar(100) COLLATE "pg_catalog"."default",
       "id" varchar(255) COLLATE "pg_catalog"."default"
);
ALTER TABLE "public"."omp_boh_id_ref" ADD CONSTRAINT "omp_boh_id_ref_pkey" PRIMARY KEY ("name", "omp_id");
CREATE INDEX "idx_name_brand_boh" ON "public"."omp_boh_id_ref" USING btree (
        "name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
        "brand_id" "pg_catalog"."int4_ops" ASC NULLS LAST,
        "boh_id" "pg_catalog"."int4_ops" ASC NULLS LAST
    );

---ts_cmk下发新增dwid字段
select p_addfield('ts_cmk','dwid','int4');
select p_addfield('ts_cmk_xf','dwid','int4');

DROP VIEW vs_cmk;
CREATE VIEW vs_cmk AS SELECT
dwr.omp_id omp_dnid,
dsr.omp_id omp_dishesid,
C.*
FROM
	ts_cmk
	C LEFT JOIN omp_boh_id_ref dwr ON C.dwid = dwr.boh_id
	LEFT JOIN omp_boh_id_ref dsr ON C.cmid = dsr.boh_id
WHERE
	dwr.NAME = 'dictionary'
	AND dsr.NAME = 'dishes';

select p_addfield('tq_wdk','rule_id','varchar(255)');
select p_addfield('tq_zdmxk','rule_id','varchar(255)');

--TQ_CLSDK新增photos字段

select p_addfield('TQ_CLSDK_XF','photos','varchar(1000)');
select p_addfield('TQ_CLSDK','photos','varchar(1000)');

