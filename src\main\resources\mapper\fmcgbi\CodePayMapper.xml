<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.fmcgbi.rest.mapper.FmcgbiCodePayMapper">
	<select id="queryItemInfo" resultType="com.tzx.fmcgbi.rest.model.TsCmk">
		select cm.* from ts_cmk cm where cm.cmbh in 
		<foreach item="cmbh" index="index" collection="itemCodeList" open="(" separator="," close=")">
			#{cmbh}
		</foreach>
	</select>
	
	<select id="getBcid" resultType="java.lang.String">
		select * from p_getbcmc(CAST(now() AS DATETIME))
	</select>
	
	<select id="getBmkzk" resultType="com.tzx.fmcgbi.rest.model.TsBmkzk">
		select * from ts_bmkzk where 1 = 1 
		<if test="bmc != null and bmc != ''">
			and bmc = #{bmc} 
		</if>
		<if test="zdmc != null and zdmc != ''">
			and zdmc = #{zdmc} 
		</if>
		limit 1
	</select>
	
	<select id="getJtZtk" resultType="com.tzx.fmcgbi.rest.model.TqJtztk">
		select * from tq_jtztk where bbrq = #{bbrq} and jhid = '99' and cznr in ('YYDL', 'YYTC') order by czsj desc limit 1
	</select>
	
	<select id="addCmNew" useCache="false" resultType="java.lang.Integer">
		select * from p_addcmnew(#{szdbh}, #{aitemid}, #{ixmsl}, #{sskjh}, #{sxsyh}, #{skwbh}, #{sggbh},  #{atype}, #{ssysdc}, #{sjgxh}, #{sjgtxbh}, 'cmjg', -1, -1, #{skwbz})
	</select>
	
	<select id="zRtr" resultType="java.lang.Integer">
		select * from P_ZRTR(#{zdbh});
	</select>
	
	<select id="calcMoney" resultType="com.tzx.fmcgbi.rest.vo.CalcMoney" >
		select * from p_calcmoney(#{zdbh});
	</select>
	
	<insert id="insertThirdExceptOrder" parameterType="com.tzx.fmcgbi.rest.model.TqThirdExceptOrder"  >
		insert into tq_third_except_order(billid, createtime, updatetime, scan_code, paytypeid, 
			paytime, orderno, refund_orderno, productcode, dynamicid_type, 
			cashamount, ordermc, dataname, updatecount, first_pay_status, 
			last_pay_status, pay_status, posno, payname, errcount)
		values (#{tteo.billid},#{tteo.createtime},#{tteo.updatetime},#{tteo.scan_code},#{tteo.paytypeid},
			#{tteo.paytime},#{tteo.orderno},#{tteo.refund_orderno},#{tteo.productcode},#{tteo.dynamicid_type},
			#{tteo.cashamount},#{tteo.ordermc},#{tteo.dataname},#{tteo.updatecount},#{tteo.first_pay_status},
			#{tteo.last_pay_status},#{tteo.pay_status},#{tteo.posno},#{tteo.payname},#{tteo.errcount})
	</insert>
	
	<update id="updateToStatus">
		update tq_third_except_order set pay_status = #{paystatus} where orderno = #{outtradeno}
	</update>
	
	<select id="accountsOrder" resultType="com.tzx.fmcgbi.rest.vo.AccountsOrder" >
		select * from p_payment(#{szdbh}, #{ijzid}, #{ifkje}, #{ifksl}, #{sfkhm}, #{ssfzhm}, #{slxdh}, #{sfkbz}, #{sskjh}, #{sskyh});
	</select>
	
	<update id="updateZdk">
		update tq_zdk set jzzdbh = #{jzzdbh}, jzbbrq = #{jzbbrq}, jzsj = #{jzsj}, jzcs = #{jzcs}, jzskjh = #{jzskjh}, jzczry = #{jzczry}, jzsx = #{jzsx}, ksjzsj = #{ksjzsj}, jzjssj = #{jzjssj}, jzbcid = #{jzbcid}, xfks = #{xfks} 
		<if test="zwbh != null and zwbh != ''">
			,zwbh = #{zwbh} 
		</if>
		<if test="qch != null and qch != ''">
			,qch = #{qch} 
		</if>
		<if test="zzbz != null and zzbz != ''">
			,zzbz = #{zzbz} 
		</if>
		where kdzdbh = #{kdzdbh} 
	</update>
	
	<update id="updateWdk">
		update tq_wdk set jzzdbh = #{jzzdbh}, jzbbrq = #{jzbbrq}, jzskjh = #{jzskjh}, jzbcid = #{jzbcid}, jjcrwid = rwid where kdzdbh = #{kdzdbh} 
	</update>
	
	<update id="updateFklslsk">
		update tq_fklslsk set jzzdbh = #{jzzdbh}, jzbcid = #{jzbcid} where kdzdbh = #{kdzdbh} 
	</update>
	
</mapper>
