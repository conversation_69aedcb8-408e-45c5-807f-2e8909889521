package com.tzx.miniapp.rest.vo;

import java.io.Serializable;

public class MainDish extends OmpDish implements Serializable {

	private String name;
	private int xmid;
	private int id;
	private int duid;
	private int number;
	private String dishsno;
	private int practiceid;

	private int dishToppingId;

	public int getXmid() {
		return xmid;
	}

	public void setXmid(int xmid) {
		this.xmid = xmid;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public int getDuid() {
		return duid;
	}

	public void setDuid(int duid) {
		this.duid = duid;
	}

	public int getNumber() {
		return number;
	}

	public void setNumber(int number) {
		this.number = number;
	}

	public String getDishsno() {
		return dishsno;
	}

	public void setDishsno(String dishsno) {
		this.dishsno = dishsno;
	}

	public int getPracticeid() {
		return practiceid;
	}

	public void setPracticeid(int practiceid) {
		this.practiceid = practiceid;
	}

	public int getDishToppingId() {
		return dishToppingId;
	}

	public void setDishToppingId(int dishToppingId) {
		this.dishToppingId = dishToppingId;
	}
}
