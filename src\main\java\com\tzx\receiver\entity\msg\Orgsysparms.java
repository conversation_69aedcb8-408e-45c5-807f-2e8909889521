package com.tzx.receiver.entity.msg;

import java.io.Serializable;

public class Orgsysparms
  implements Serializable
{
  private Integer id;
  private Integer orgid;
  private String parmdis;
  private String yt;
  private String module;
  private String modulemc;
  private String win;
  private String winmc;
  private String control;
  private String controlmc;
  private String memo;

  public Integer getId()
  {
    return this.id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public Integer getOrgid() {
    return this.orgid;
  }

  public void setOrgid(Integer orgid) {
    this.orgid = orgid;
  }

  public String getParmdis() {
    return this.parmdis;
  }

  public void setParmdis(String parmdis) {
    this.parmdis = parmdis;
  }

  public String getYt() {
    return this.yt;
  }

  public void setYt(String yt) {
    this.yt = yt;
  }

  public String getModule() {
    return this.module;
  }

  public void setModule(String module) {
    this.module = module;
  }

  public String getModulemc() {
    return this.modulemc;
  }

  public void setModulemc(String modulemc) {
    this.modulemc = modulemc;
  }

  public String getWin() {
    return this.win;
  }

  public void setWin(String win) {
    this.win = win;
  }

  public String getWinmc() {
    return this.winmc;
  }

  public void setWinmc(String winmc) {
    this.winmc = winmc;
  }

  public String getControl() {
    return this.control;
  }

  public void setControl(String control) {
    this.control = control;
  }

  public String getControlmc() {
    return this.controlmc;
  }

  public void setControlmc(String controlmc) {
    this.controlmc = controlmc;
  }

  public String getMemo() {
    return this.memo;
  }

  public void setMemo(String memo) {
    this.memo = memo;
  }
}