package com.tzx.commapi.common;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 *
 * @author: andy.li
 * @date: 2011-9-23
 */
public class DateUtil
{

	private static SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	private static SimpleDateFormat simpleTimeFormat = new SimpleDateFormat("HH:mm:ss");
	public static SimpleDateFormat simpleyyyyMMddFormat = new SimpleDateFormat("yyyy-MM-dd");
	private static SimpleDateFormat simpleHHmmTimeFormat = new SimpleDateFormat("HH:mm");

	public static String DATETIME_FORMAT = "yyyy-MM-dd HH:mm";
	public static String DATE_FORMAT = "yyyy-MM-dd";
	public static String DATETIME_FORMAT2 = "yyyy-MM-dd HH:mm:ss";
	public static String TIME_FORMAT = "HH:mm";

	private static final DateTimeFormatter INPUT_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
	private static final DateTimeFormatter OUTPUT_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

	/**
	 * 返回 yyyyMMddHHmmss 格式的日期
	 * @param inputDate
	 * @return
	 */
	public static String format(String inputDate) {
		LocalDateTime dateTime = LocalDateTime.parse(inputDate, INPUT_FORMATTER);
		return dateTime.format(OUTPUT_FORMATTER);
	}

	/**
	 * Get the previous time, from how many days to now.
	 *
	 * @param days
	 *            How many days.
	 * @return The new previous time.
	 */
	public static Date previous(int days) {
		return new Date(System.currentTimeMillis() - days * 3600000L * 24L);
	}

	/**
	 * Convert date and time to string like "yyyy-MM-dd HH:mm".
	 */
	public static String formatDateTime(Date d) {
		return new SimpleDateFormat(DATETIME_FORMAT2).format(d);
	}

	/**
	 * Convert date and time to string like "yyyy-MM-dd HH:mm".
	 */
	public static String formatDateTime(long d) {
		return new SimpleDateFormat(DATETIME_FORMAT).format(d);
	}

	/**
	 * Convert date to String like "yyyy-MM-dd".
	 */
	public static String formatDate(Date d) {
		if (null != d) {
			return new SimpleDateFormat(DATE_FORMAT).format(d);
		}
		return null;
	}

	public static String formatDate(Date d, String format) {
		if (null != d) {
			return new SimpleDateFormat(format).format(d);
		}
		return null;
	}

	/**
	 * Parse date like "yyyy-MM-dd".
	 */
	public static Date parseDate(String d) {
		try {
			return new SimpleDateFormat(DATE_FORMAT).parse(d);
		} catch (Exception e) {
		}
		return null;
	}

	/**
	 * Parse date and time like "yyyy-MM-dd hh:mm".
	 */
	public static Date parseDateTime(String dt) {
		try {
			return new SimpleDateFormat(DATETIME_FORMAT2).parse(dt);
		} catch (Exception e) {
		}
		return null;
	}

	public static Date parseDate(String date, String formater) {
		Date result = null;
		formater = StringUtils.defaultIfEmpty(formater, DATETIME_FORMAT2);
		if (StringUtils.isNotBlank(date)) {
			SimpleDateFormat simpleDateFormat = new SimpleDateFormat(formater);
			try {
				result = simpleDateFormat.parse(date);
			} catch (ParseException e) {
				Logger.getLogger(DateUtil.class).error(e);
			}
		}
		return result;
	}

	/**
	 *
	 * 计算两个时间相差多少小时
	 */
	public static Double compareTime(String start, String end) {
		Date start_time = DateUtil.parseDate(start, TIME_FORMAT);
		Date end_time = DateUtil.parseDate(end, TIME_FORMAT);
		if ("00:00".equals(end)) {
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(end_time);
			calendar.add(Calendar.DATE, 1);
			end_time = calendar.getTime();
		}
		long start_mis = start_time.getTime();
		long end_mis = end_time.getTime();
		if (end_mis > start_mis) {
			long chart_mis = end_mis - start_mis;
			BigDecimal temp = new BigDecimal(chart_mis);
			BigDecimal divs = new BigDecimal(3600000);
			return temp.divide(divs, 2, RoundingMode.HALF_UP).doubleValue();
		}
		return 0d;
	}

	public static String getLogDate(Date date) {
		Calendar cal = Calendar.getInstance();// 使用日历类
		int year = cal.get(Calendar.YEAR);// 得到年
		int month = cal.get(Calendar.MONTH) + 1;// 得到月，因为从0开始的，所以要加1
		int day = cal.get(Calendar.DAY_OF_MONTH);// 得到天
		String str = year + "年" + month + "月" + day + "日  ";

		String[] weekDays =
				{ "星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六" };
		cal = Calendar.getInstance();
		cal.setTime(date);

		int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
		if (w < 0) w = 0;

		return str + weekDays[w];
	}

	public static void main(String[] args) {
		try {


			String sss = "2023-09-14 00:00:00";
			System.out.println(sss.substring(0, 10));
			System.out.println(sss);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			Logger.getLogger(DateUtil.class).error(e);
		}

	}

	/**
	 * 计算出时间段内的所有天
	 *
	 * @param begintime
	 * @param endtime
	 * @return
	 */
	public static List<String> periodOfDays(String begintime, String endtime) {
		List<String> list = new ArrayList<String>();
		try {
			Date date1 = simpleyyyyMMddFormat.parse(begintime.substring(0, 10));
			Date date2 = simpleyyyyMMddFormat.parse(endtime.substring(0, 10));
			if (date1.getTime() > date2.getTime()) {
				return list;
			}
			Calendar cal1 = Calendar.getInstance();
			Calendar cal2 = Calendar.getInstance();
			cal1.setTime(date1);
			cal2.setTime(date2);
			String datestr = simpleyyyyMMddFormat.format(cal1.getTime());
			list.add(datestr);
			while (cal1.before(cal2)) {
				cal1.add(Calendar.DAY_OF_MONTH, 1);
				datestr = simpleyyyyMMddFormat.format(cal1.getTime());
				list.add(datestr);
			}
			return list;
		} catch (Exception e) {
			return list;
		}
	}

	public static String getCurrentTime(String format) {
		// 获取开始时间
		Date day = new Date();
		SimpleDateFormat df = new SimpleDateFormat(format);
		return df.format(day);
	}

	public static boolean isTimeOut(long historyTime, long currentTimeMillis, Integer minutes){
		// 创建一个 Calendar 实例，并设置它的时间为当前时间
		Calendar calendar = Calendar.getInstance();
		calendar.setTimeInMillis(historyTime);

		calendar.add(Calendar.MINUTE, minutes);

		long minutesLaterMillis = calendar.getTimeInMillis();

		if (minutesLaterMillis >= currentTimeMillis) {
			return true;
		} else {
			return false;
		}
	}

}
