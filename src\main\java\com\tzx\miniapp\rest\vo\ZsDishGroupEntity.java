package com.tzx.miniapp.rest.vo;

import java.util.List;

public class ZsDishGroupEntity {

	private String itemGroupCode;// 分组编码
	private String groupId;// 分组ID
	private String merGroupId;// 集团分组ID
	private String itemGroupName;// 分组名称
	private String fiveCode;// 五笔码
	private String phoneticCode;// 拼音
	private Integer itemGroupPrice;// 价格
	private String remark;// 说明
	private Integer validState;// 是否有效
	private List<ZsDishGroupDetailsEntity> groupDetailList; // 分组明细列表

	public String getItemGroupCode() {
		return itemGroupCode;
	}

	public void setItemGroupCode(String itemGroupCode) {
		this.itemGroupCode = itemGroupCode;
	}

	public String getGroupId() {
		return groupId;
	}

	public void setGroupId(String groupId) {
		this.groupId = groupId;
	}

	public String getMerGroupId() {
		return merGroupId;
	}

	public void setMerGroupId(String merGroupId) {
		this.merGroupId = merGroupId;
	}

	public String getItemGroupName() {
		return itemGroupName;
	}

	public void setItemGroupName(String itemGroupName) {
		this.itemGroupName = itemGroupName;
	}

	public String getFiveCode() {
		return fiveCode;
	}

	public void setFiveCode(String fiveCode) {
		this.fiveCode = fiveCode;
	}

	public String getPhoneticCode() {
		return phoneticCode;
	}

	public void setPhoneticCode(String phoneticCode) {
		this.phoneticCode = phoneticCode;
	}

	public Integer getItemGroupPrice() {
		return itemGroupPrice;
	}

	public void setItemGroupPrice(Integer itemGroupPrice) {
		this.itemGroupPrice = itemGroupPrice;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Integer getValidState() {
		return validState;
	}

	public void setValidState(Integer validState) {
		this.validState = validState;
	}

	public List<ZsDishGroupDetailsEntity> getGroupDetailList() {
		return groupDetailList;
	}

	public void setGroupDetailList(List<ZsDishGroupDetailsEntity> groupDetailList) {
		this.groupDetailList = groupDetailList;
	}

}
