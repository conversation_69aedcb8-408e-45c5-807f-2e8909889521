package com.tzx.test.rest.controller;

import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.tzx.publics.base.BaseController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/tzxtest")
public class TestController extends BaseController {
	private final static Logger LOGGER = LoggerFactory.getLogger(TestController.class);

	@RequestMapping(value = "/api/order/refundOrder", method = RequestMethod.POST)
	public void test() {
		Integer sleepTime = 50000;
		LOGGER.info("************超时测试，接口开始休眠,休眠时间" + sleepTime + "**************");
		try {
			Thread.sleep(50000);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
		LOGGER.info("************超时测试，接口休眠结束,休眠时间" + sleepTime + "**************");
	}

	@RequestMapping(value = "/exc/login", method = RequestMethod.POST)
	public String excLogin(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) {
		LOGGER.info("json:" + json);
		try {
			Thread.sleep(1000 * 60 * 60);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
		JSONObject obj = new JSONObject();
		obj.put("retCode","0");
		obj.put("retMsg","");
		obj.put("timestamp","2021-06-09 11:43:02");
		obj.put("sign","0");
		JSONObject data = new JSONObject();
		obj.put("User_Token","CB99B3D7D3799F5E93A241BAE5258C47");
		obj.put("data",data);

		return obj.toString();
	}

	@RequestMapping(value = "/login", method = RequestMethod.POST)
	public String login(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) {
		LOGGER.info("json:" + json);

		JSONObject obj = new JSONObject();
		obj.put("retCode","0");
		obj.put("retMsg","");
		obj.put("timestamp","2021-06-09 11:43:02");
		obj.put("sign","0");
		JSONObject data = new JSONObject();
		obj.put("User_Token","CB99B3D7D3799F5E93A241BAE5258C47");
		obj.put("data",data);

		return obj.toString();
	}

	@RequestMapping(value = "/10301997/SaveXsjl", method = RequestMethod.POST)
	public String saveXsjl(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) {
		LOGGER.info("json:" + json);

		JSONObject obj = new JSONObject();
		obj.put("retCode","0");
		obj.put("retMsg","");
		obj.put("timestamp","2021-06-09 11:43:02");
		obj.put("sign","0");
		obj.put("data",null);

		return obj.toString();
	}
}
