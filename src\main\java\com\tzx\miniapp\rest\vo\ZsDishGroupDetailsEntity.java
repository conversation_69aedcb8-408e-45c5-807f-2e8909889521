package com.tzx.miniapp.rest.vo;

import java.io.Serializable;
import java.util.List;

public class ZsDishGroupDetailsEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	private Integer isdefault;// 是否默认
	private String dishId;// 菜品ID
	private String unitId;// 规格ID
	private Integer makeupMoney;// 金额
	private Integer quantityLimit;// 数量

	public Integer getIsdefault() {
		return isdefault;
	}

	public void setIsdefault(Integer isdefault) {
		this.isdefault = isdefault;
	}

	public String getDishId() {
		return dishId;
	}

	public void setDishId(String dishId) {
		this.dishId = dishId;
	}

	public String getUnitId() {
		return unitId;
	}

	public void setUnitId(String unitId) {
		this.unitId = unitId;
	}

	public Integer getMakeupMoney() {
		return makeupMoney;
	}

	public void setMakeupMoney(Integer makeupMoney) {
		this.makeupMoney = makeupMoney;
	}

	public Integer getQuantityLimit() {
		return quantityLimit;
	}

	public void setQuantityLimit(Integer quantityLimit) {
		this.quantityLimit = quantityLimit;
	}

}
