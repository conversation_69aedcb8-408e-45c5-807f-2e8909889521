package com.tzx.miniapp.rest.model;

import com.tzx.publics.base.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2018-05-18
 */
@Table(name = "TQ_CLSDK")
public class TqClsdk extends BaseEntity implements Serializable {
	private static final long serialVersionUID = 1L;
	@Id
	@Column(name = "ID")
	private int id;
	@Column(name = "CBID")
	private int cbid;
	@Column(name = "CLBH")
	private String clbh;
	@Column(name = "CLMC1")
	private String clmc1;
	@Column(name = "CLMC2")
	private String clmc2;
	@Column(name = "KSRQ")
	private String ksrq;
	@Column(name = "JSRQ")
	private String jsrq;
	@Column(name = "KSSJ")
	private String kssj;
	@Column(name = "JSSJ")
	private String jssj;
	@Column(name = "CLCOLOR")
	private String clcolor;
	@Column(name = "SHOWXH")
	private int showxh;
	@Column(name = "SFXS")
	private String sfxs;
	@Column(name = "CLSX")
	private String clsx;
	@Column(name = "XSMSSX")
	private String xsmssx;
	@Column(name = "MEMO")
	private String memo;
	@Column(name = "YL1")
	private String yl1;
	@Column(name = "YL2")
	private String yl2;
	@Column(name = "YL3")
	private String yl3;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public int getCbid() {
		return cbid;
	}

	public void setCbid(int cbid) {
		this.cbid = cbid;
	}

	public String getClbh() {
		return clbh;
	}

	public void setClbh(String clbh) {
		this.clbh = clbh;
	}

	public String getClmc1() {
		return clmc1;
	}

	public void setClmc1(String clmc1) {
		this.clmc1 = clmc1;
	}

	public String getClmc2() {
		return clmc2;
	}

	public void setClmc2(String clmc2) {
		this.clmc2 = clmc2;
	}

	public String getKsrq() {
		return ksrq;
	}

	public void setKsrq(String ksrq) {
		this.ksrq = ksrq;
	}

	public String getJsrq() {
		return jsrq;
	}

	public void setJsrq(String jsrq) {
		this.jsrq = jsrq;
	}

	public String getKssj() {
		return kssj;
	}

	public void setKssj(String kssj) {
		this.kssj = kssj;
	}

	public String getJssj() {
		return jssj;
	}

	public void setJssj(String jssj) {
		this.jssj = jssj;
	}

	public String getClcolor() {
		return clcolor;
	}

	public void setClcolor(String clcolor) {
		this.clcolor = clcolor;
	}

	public int getShowxh() {
		return showxh;
	}

	public void setShowxh(int showxh) {
		this.showxh = showxh;
	}

	public String getSfxs() {
		return sfxs;
	}

	public void setSfxs(String sfxs) {
		this.sfxs = sfxs;
	}

	public String getClsx() {
		return clsx;
	}

	public void setClsx(String clsx) {
		this.clsx = clsx;
	}

	public String getXsmssx() {
		return xsmssx;
	}

	public void setXsmssx(String xsmssx) {
		this.xsmssx = xsmssx;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public String getYl1() {
		return yl1;
	}

	public void setYl1(String yl1) {
		this.yl1 = yl1;
	}

	public String getYl2() {
		return yl2;
	}

	public void setYl2(String yl2) {
		this.yl2 = yl2;
	}

	public String getYl3() {
		return yl3;
	}

	public void setYl3(String yl3) {
		this.yl3 = yl3;
	}

}
