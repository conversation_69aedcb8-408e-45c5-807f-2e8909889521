package com.tzx.dyndatasource;

import com.alibaba.druid.pool.xa.DruidXADataSource;
import org.springframework.boot.jta.atomikos.AtomikosDataSourceBean;

import java.util.Properties;
import java.util.ResourceBundle;

/**
 * <AUTHOR>
 * @Date 2020-03-16
 * @Descption
 **/
public class DruidXADataSourceBuilder {
    public static DruidXADataSource create(String configPath, String prefix){
        ResourceBundle resourceBundle = ResourceBundle.getBundle(configPath);
        DruidXADataSource ds = new DruidXADataSource();
        ds.setDriverClassName(getProValue(resourceBundle,prefix+".driver-class-name","org.postgresql.Driver"));
        ds.setUrl(getProValue(resourceBundle,prefix+".url",""));
        ds.setUsername(getProValue(resourceBundle,prefix+".username",""));
        ds.setPassword(getProValue(resourceBundle,prefix+".password",""));
        ds.setMaxActive(getProValue(resourceBundle,prefix+".maxActive",20));
        ds.setInitialSize(getProValue(resourceBundle,prefix+".minIdle",10));
        ds.setMaxWait(getProValue(resourceBundle,prefix+".maxWait",1000*60*5));
        ds.setMinIdle(getProValue(resourceBundle,prefix+".minIdle",10));
        ds.setValidationQuery(getProValue(resourceBundle,prefix+".validationQuery",""));
        ds.setTestWhileIdle(getProValue(resourceBundle,prefix+".testWhileIdle",false));
        ds.setTestOnBorrow(getProValue(resourceBundle,prefix+".testOnBorrow",false));
        ds.setTestOnReturn(getProValue(resourceBundle,prefix+".testOnReturn",false));
        return  ds;
    }
    private static String getProValue(ResourceBundle resourceBundle,String key,String defaultvalue){
        String res = null;
        try{
            res = resourceBundle.getString(key);
        }catch (Exception e){

        }
        if(res==null){
            res = defaultvalue;
        }
        return res;
    }
    private static Integer getProValue(ResourceBundle resourceBundle,String key,Integer defaultvalue){
        Integer res = null;
        try{
            res = Integer.parseInt(resourceBundle.getString(key));
        }catch (Exception e){

        }
        if(res==null){
            res = defaultvalue;
        }
        return res;
    }
    private static Boolean getProValue(ResourceBundle resourceBundle,String key,Boolean defaultvalue){
        Boolean res = null;
        try{
            res = Boolean.valueOf(resourceBundle.getString(key));
        }catch (Exception e){

        }
        if(res==null){
            res = defaultvalue;
        }
        return res;
    }
}
