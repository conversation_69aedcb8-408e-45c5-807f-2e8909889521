package com.tzx.mobilepos.rest.vo;

import java.io.Serializable;

public class PaymentRunningWater implements Serializable {
	private Integer fklsid;
	private String fklxsx;
	private String fkfsmc;
	private String fkje;
	private String jzid;
	private String payment_mark;
	private String yzm;
	private String fkfsbh;

	public Integer getFklsid() {
		return fklsid;
	}

	public void setFklsid(Integer fklsid) {
		this.fklsid = fklsid;
	}

	public String getFklxsx() {
		return fklxsx;
	}

	public void setFklxsx(String fklxsx) {
		this.fklxsx = fklxsx;
	}

	public String getFkfsmc() {
		return fkfsmc;
	}

	public void setFkfsmc(String fkfsmc) {
		this.fkfsmc = fkfsmc;
	}

	public String getFkje() {
		return fkje;
	}

	public void setFkje(String fkje) {
		this.fkje = fkje;
	}

	public String getJzid() {
		return jzid;
	}

	public void setJzid(String jzid) {
		this.jzid = jzid;
	}

	public String getPayment_mark() {
		return payment_mark;
	}

	public void setPayment_mark(String payment_mark) {
		this.payment_mark = payment_mark;
	}

	public String getYzm() {
		return yzm;
	}

	public void setYzm(String yzm) {
		this.yzm = yzm;
	}

	public String getFkfsbh() {
		return fkfsbh;
	}

	public void setFkfsbh(String fkfsbh) {
		this.fkfsbh = fkfsbh;
	}

}
