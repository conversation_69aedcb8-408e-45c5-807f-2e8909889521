package com.tzx.publics.util;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLContextBuilder;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.SSLContext;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;
import java.net.ConnectException;
import java.net.HttpURLConnection;
import java.net.SocketTimeoutException;
import java.net.URL;
import java.net.URLConnection;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.HashMap;
import java.util.Map;

public class SendRequest {
	private final static Logger LOGGER = LoggerFactory.getLogger(SendRequest.class);

	public static final int CONNECTION_TIMEOUT = 10000;// 连接超时
	public static final int READDATA_TIMEOUT = 15000;// 数据读取等待超时
	public static final String UTF8 = "UTF-8";// 编码格式
	public static final String PLAIN_TEXT_TYPE = "application/json"; // text/html   text/plain application/json
	
	public static String sendPost(String url, String param) {
		PrintWriter out = null;
		BufferedReader in = null;
		String result = "";
		try {
			URL realUrl = new URL(url);
			// 打开和URL之间的连接
			URLConnection conn = realUrl.openConnection();
			// 设置通用的请求属性
			conn.setRequestProperty("accept", "*/*");
			conn.setRequestProperty("connection", "Keep-Alive");
			conn.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
			// 设置连接主机超时（单位：毫秒）
			conn.setConnectTimeout(5000);
			// 设置从主机读取数据超时（单位：毫秒）
			conn.setReadTimeout(15000);
			// 发送POST请求必须设置如下两行
			conn.setDoOutput(true);
			conn.setDoInput(true);
			// 获取URLConnection对象对应的输出流
			out = new PrintWriter(conn.getOutputStream());
			// 发送请求参数
			out.print(param);
			// flush输出流的缓冲
			out.flush();
			// 定义BufferedReader输入流来读取URL的响应
			in = new BufferedReader(new InputStreamReader(conn.getInputStream(), "UTF-8"));
			String line;
			while ((line = in.readLine()) != null) {
				result += line;
			}
		} catch (SocketTimeoutException e) {
			LOGGER.error("请求超时", e);
			e.printStackTrace();
			return "timedout";
		} catch (Exception e) {
			LOGGER.info("发送 POST 请求出现异常！" + e);
			e.printStackTrace();
			return "-1";
		}
		// 使用finally块来关闭输出流、输入流
		finally {
			try {
				if (out != null) {
					out.close();
				}
				if (in != null) {
					in.close();
				}
			} catch (IOException ex) {
				ex.printStackTrace();
			}
		}
		return result;
	}
	
	
	
	
	
	public static String zsPost(String urls, String json) throws Exception {
		try {
			LOGGER.info(json);
			URL url = new URL(urls);
			// 建立http连接
			HttpURLConnection conn = (HttpURLConnection) url.openConnection();
			// 设置允许输出
			conn.setDoOutput(true);
			conn.setDoInput(true);
			// 设置不用缓存
			conn.setUseCaches(false);
			// 设置传递方式
			conn.setRequestMethod("POST");
			// 设置维持长连接
			conn.setRequestProperty("Connection", "Keep-Alive");
			// 设置文件字符集:
			conn.setRequestProperty("Charset", "UTF-8");
			// 转换为字节数组
			byte[] data = (json).getBytes("UTF-8");
			// 设置文件长度
			conn.setRequestProperty("Content-Length", String.valueOf(data.length));
			// 设置文件类型:
			conn.setRequestProperty("content-type", "application/json");
			// 设置文件类型:
			conn.setRequestProperty("accept", "application/json");
			// 开始连接请求
			conn.connect();
			OutputStream out = conn.getOutputStream();
			// 写入请求的字符串
			out.write((json).getBytes("UTF-8"));
			
			out.flush();
			out.close();
			// 请求返回的状态
			if (conn.getResponseCode() == 200) {
				// 请求返回的数据
				StringBuffer stingbuff = new StringBuffer();
				try {
					BufferedReader buffRed = new BufferedReader(new InputStreamReader(conn.getInputStream(), "UTF-8"));
					String line = "";
					while ((line = buffRed.readLine()) != null) {
						stingbuff.append(line);
					}

					return stingbuff.toString();
				} catch (Exception e1) {
					LOGGER.error(e1.getMessage(), e1);
				}
			} else {
				LOGGER.error("no++返回状态码:" + conn.getResponseCode());
			}

		} catch (Exception e) {
			LOGGER.error(e.getMessage(), e);
		}
		return null;
	}
	
	public static String qmPost(String urls, String json) throws Exception {
		String result = "";
		try {
			LOGGER.info(json);
			URL url = new URL(urls);
			HttpURLConnection conn = (HttpURLConnection) url.openConnection();
			conn.setDoOutput(true);
			conn.setDoInput(true);
			conn.setUseCaches(false);
			conn.setConnectTimeout(10000);
			conn.setReadTimeout(15000);
			conn.setRequestMethod("POST");
			conn.setRequestProperty("Connection", "Keep-Alive");
			conn.setRequestProperty("Charset", "UTF-8");
			byte[] data = (json).getBytes("UTF-8");
			conn.setRequestProperty("Content-Length", String.valueOf(data.length));
			conn.setRequestProperty("content-type", "application/json");
			conn.setRequestProperty("accept", "application/json");
			conn.connect();
			OutputStream out = conn.getOutputStream();
			out.write((json).getBytes("UTF-8"));
			out.flush();
			out.close();
			if (conn.getResponseCode() == 200) {
				StringBuffer stingbuff = new StringBuffer();
				BufferedReader buffRed = new BufferedReader(new InputStreamReader(conn.getInputStream(), "UTF-8"));
				String line = "";
				while ((line = buffRed.readLine()) != null) {
					stingbuff.append(line);
				}
				result = stingbuff.toString();
//				return stingbuff.toString();
			} else {
				LOGGER.error("no++返回状态码:" + conn.getResponseCode());
				result = "-2";
			}
		} catch (SocketTimeoutException e) {
			LOGGER.error("请求超时", e);
			e.printStackTrace();
			result = "timedout";
		} catch (ConnectException e) {
			LOGGER.error("连接超时", e);
			e.printStackTrace();
			result = "timedout";
		}catch (Exception e) {
			LOGGER.info("发送 POST 请求出现异常！" + e);
			e.printStackTrace();
			result = "-1";
		}
		return result;
	}

	public static String sendGeneralPost(String url, String param) {
		PrintWriter out = null;
		BufferedReader in = null;
		String result = "";
		try {
			URL realUrl = new URL(url);
			// 打开和URL之间的连接
			URLConnection conn = realUrl.openConnection();
			// 设置通用的请求属性
			conn.setRequestProperty("accept", "*/*");
			conn.setRequestProperty("connection", "Keep-Alive");
			conn.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
			conn.setRequestProperty("content-type", "application/json");
			// 设置连接主机超时（单位：毫秒）
			conn.setConnectTimeout(5000);
			// 设置从主机读取数据超时（单位：毫秒）
			conn.setReadTimeout(60000);
			// 发送POST请求必须设置如下两行
			conn.setDoOutput(true);
			conn.setDoInput(true);
			// 获取URLConnection对象对应的输出流
			out = new PrintWriter(conn.getOutputStream());
			// 发送请求参数
			out.print(param);
			// flush输出流的缓冲
			out.flush();
			// 定义BufferedReader输入流来读取URL的响应
			in = new BufferedReader(new InputStreamReader(conn.getInputStream(), "UTF-8"));
			String line;
			while ((line = in.readLine()) != null) {
				result += line;
			}
		} catch (SocketTimeoutException e) {
			LOGGER.error("请求超时", e);
			e.printStackTrace();
			return "timedout";
		} catch (Exception e) {
			LOGGER.info("发送 POST 请求出现异常！" + e);
			e.printStackTrace();
			return "-1";
		}
		// 使用finally块来关闭输出流、输入流
		finally {
			try {
				if (out != null) {
					out.close();
				}
				if (in != null) {
					in.close();
				}
			} catch (IOException ex) {
				ex.printStackTrace();
			}
		}
		return result;
	}

	/**
	 * 无需本地证书keyStore的SSL https带参数请求
	 * @param url
	 * @param json
	 * @return
	 */
	public static Map<String, String> postSSLPost(String url, String json) {
		Map<String, String> map = new HashMap<String, String>();
		map.put("success", "-1");
		map.put("msg", "请求异常！");
		CloseableHttpClient httpClient = HttpClientUtil.createSSLInsecureClient();
		HttpPost post = new HttpPost(url);
		// 添加参数
//		MultipartEntityBuilder builder = MultipartEntityBuilder.create();
		// 决中文乱码
//		ContentType contentType = ContentType.create(PLAIN_TEXT_TYPE, UTF8);
//		Charset chars = Charset.forName("utf8"); // Setting up the encoding
//		builder.setCharset(chars);
//		builder.setMode(HttpMultipartMode.BROWSER_COMPATIBLE);
//		if (reqMap != null && reqMap.keySet().size() > 0) {
//			Iterator<Map.Entry<String, String>> iter = reqMap.entrySet().iterator();
//			while (iter.hasNext()) {
//				Map.Entry<String, String> entity = iter.next();
//				builder.addTextBody(entity.getKey(), entity.getValue().toString(), contentType);
//			}
//		}
		StringBuilder sb = new StringBuilder();
		BufferedReader br = null;
		try {
			// 设置客户端请求的头参数getParams已经过时,现在用requestConfig对象替换
			// httpClient.getParams().setParameter(CoreConnectionPNames.CONNECTION_TIMEOUT,CONNECTION_TIMEOUT);
			RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(CONNECTION_TIMEOUT).setSocketTimeout(READDATA_TIMEOUT).build();
			post.setConfig(requestConfig);
			post.setHeader("Content-Type","application/json;charset=utf-8");
			//HttpEntity entity = builder.build();
			StringEntity entity = new StringEntity(json, "utf-8");
			post.setEntity(entity);

			HttpResponse response = httpClient.execute(post);
			HttpEntity httpEntity = response.getEntity();
			br = new BufferedReader(new InputStreamReader(httpEntity.getContent(), UTF8));
			String s = null;
			while ((s = br.readLine()) != null) {
				sb.append(s);
			}
			//LOGGER.info("返回数据：" + sb.toString());
			if(sb.length() > 0){
				map.put("success", "0");
				map.put("msg", "OK");
				map.put("data", sb.toString());
			} else {
				map.put("msg", "服务器链接异常！");
			}
		} catch (SocketTimeoutException e) {
			LOGGER.error("请求超时", e);
			map.put("msg", "网络请求超时，请稍后再试！");
			// throw new RuntimeException("请求超时异常", e);
		} catch (UnsupportedEncodingException e) {
			LOGGER.error("编码格式错误", e);
			map.put("msg", "编码格式错误！");
			// throw new RuntimeException("指定的编码集不对,您目前指定的编码集是:" + UTF8);
		} catch (ClientProtocolException e) {
			LOGGER.error("客户端协议异常", e);
			map.put("msg", "客户端协议异常！");
		} catch (IOException e) {
			LOGGER.error("读取流文件异常", e);
			map.put("msg", "读取流文件异常！");
			// throw new RuntimeException("读取流文件异常", e);
		} catch (Exception e) {
			LOGGER.error("通讯未知系统异常", e);
			map.put("msg", "通讯未知系统异常！");
			// throw new RuntimeException("通讯未知系统异常", e);
		} finally {
			// 确保所有资源都被正确关闭
			try {
				if (br != null) br.close();
				if (httpClient != null) httpClient.close();
			} catch (IOException e) {
				LOGGER.error("关闭资源异常", e);
			}
		}
		return map;
	}
	/**
	 * 创建一个SSL信任所有证书的httpClient对象
	 */
	public static CloseableHttpClient createSSLInsecureClient() {
		try {
			SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustStrategy() {
				// 默认信任所有证书
				public boolean isTrusted(X509Certificate[] arg0, String arg1) throws CertificateException {
					return true;
				}
			}).build();
			// AllowAllHostnameVerifier: 这种方式不对主机名进行验证，验证功能被关闭，是个空操作(域名验证)
			SSLConnectionSocketFactory sslcsf = new SSLConnectionSocketFactory(sslContext, SSLConnectionSocketFactory.ALLOW_ALL_HOSTNAME_VERIFIER);
			return HttpClients.custom().setSSLSocketFactory(sslcsf).build();
		} catch (KeyManagementException e) {
			e.printStackTrace();
		} catch (NoSuchAlgorithmException e) {
			e.printStackTrace();
		} catch (KeyStoreException e) {
			e.printStackTrace();
		}
		return HttpClients.createDefault();
	}
}
