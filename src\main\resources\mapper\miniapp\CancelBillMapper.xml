<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.miniapp.rest.mapper.MiniAppCancelBillMapper">
	<select id="findBill" resultType="com.tzx.miniapp.rest.model.TqZdk">
		select * from tq_zdk where yddh = #{yddh}
	</select>
	<select id="cancelBill" resultType="java.lang.Integer">
		select * from p_cancelyydzd(#{ayddh},#{aydrq}, #{askjh}, #{iygdlcs}, #{sczry})
	</select>

</mapper>
