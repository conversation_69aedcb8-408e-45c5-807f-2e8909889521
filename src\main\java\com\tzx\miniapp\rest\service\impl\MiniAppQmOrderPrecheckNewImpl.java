package com.tzx.miniapp.rest.service.impl;

import java.math.BigDecimal;
import java.util.*;

import com.tzx.commapi.rest.service.ICommApiService;
import com.tzx.commapi.rest.service.impl.CommApiService;
import com.tzx.commapi.rest.vo.CommApiData;
import com.tzx.receiver.entity.msg.EcoTypeDic;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.tzx.commapi.rest.service.IUseYhfsApiService;
import com.tzx.commapi.rest.vo.UseYhfsParam;
import com.tzx.ecoserver.rest.mapper.EcoOrderMapper;
import com.tzx.ecoserver.rest.vo.EcoOrderdiscount;
import com.tzx.miniapp.common.Constant;
import com.tzx.miniapp.common.Data;
import com.tzx.miniapp.rest.mapper.MiniAppFirstPayMapper;
import com.tzx.miniapp.rest.mapper.MiniAppOrderPrecheckMapper;
import com.tzx.miniapp.rest.mapper.MiniAppShopBaseInfoMapper;
import com.tzx.miniapp.rest.mapper.MiniAppShopStatusMapper;
import com.tzx.miniapp.rest.mapper.MiniAppTqWdkMapper;
import com.tzx.miniapp.rest.mapper.MiniAppTqZdkMapper;
import com.tzx.miniapp.rest.mapper.MiniAppTsPsjgsdkMapper;
import com.tzx.miniapp.rest.model.TqFklslsk;
import com.tzx.miniapp.rest.model.TqJtztk;
import com.tzx.miniapp.rest.model.TqWdk;
import com.tzx.miniapp.rest.model.TqZdk;
import com.tzx.miniapp.rest.model.TsCmk;
import com.tzx.miniapp.rest.model.TsFkfssdk;
import com.tzx.miniapp.rest.model.TsGgcsk;
import com.tzx.miniapp.rest.model.TsPsjgsdk;
import com.tzx.miniapp.rest.model.TsYhfssdk;
import com.tzx.miniapp.rest.service.IMiniAppQmOrderPrecheckNew;
import com.tzx.miniapp.rest.vo.BtPayments;
import com.tzx.miniapp.rest.vo.BtYdd;
import com.tzx.miniapp.rest.vo.BtYdxm2;
import com.tzx.miniapp.rest.vo.Dish;
import com.tzx.miniapp.rest.vo.DishVo;
import com.tzx.miniapp.rest.vo.Shops;
import com.tzx.mobilepos.rest.mapper.MobilePosAcewillCouponTempMapper;
import com.tzx.publics.common.BillNoData;
import com.tzx.publics.listener.InitDataListener;
import com.tzx.publics.util.ArithUtil;
import com.tzx.publics.util.DateUtil;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

@Service
public class MiniAppQmOrderPrecheckNewImpl implements IMiniAppQmOrderPrecheckNew {

	private final static Logger LOGGER = LoggerFactory.getLogger(MiniAppQmOrderPrecheckNewImpl.class);

	@Autowired
	private MiniAppOrderPrecheckMapper orderPrecheckMapper;
	@Autowired
	private MiniAppFirstPayMapper firstPayMapper;
	@Autowired
	private MiniAppShopStatusMapper shopStatusMapper;
	@Autowired
	private MiniAppTqZdkMapper tqZdkMapper;
	@Autowired
	private MiniAppTqWdkMapper tqWdk;
	@Autowired
	private MiniAppShopBaseInfoMapper shopBaseInfoMapper;
	// 机构配置信息查询接口注入对象
	@Autowired
	private MiniAppTsPsjgsdkMapper tsPsjgsdkMapper;
	// 不计收入拆分
	@Autowired
	private IUseYhfsApiService useYhfsApiService;
	
	@Autowired
	private MobilePosAcewillCouponTempMapper acewillCouponTempMapper;
	
	@Autowired
    private EcoOrderMapper ecoOrderMapper;
    @Autowired
    private ICommApiService commApiService;;

    @Transactional
	public Data orderPrecheckCode(JSONObject orderData, BillNoData billNoData) {
		// 创建返回数据对象
		Data data = new Data();
		// 小程序订单号
		String outOrderId = orderData.optString("out_order_id");
		// 持久化使用第预订单号字段，为了快速区分，小程序增加了  TS 前缀
		String outOrderIdInDB = Constant.BILL_PREFIX + outOrderId;
		
		// 报表日期
		Map<String, String> bbrqMap = shopStatusMapper.findBbrq();
		// 生成默认报表日期
		String bbrq = DateUtil.getNowDateYYDDMM();
		if (null != bbrqMap && bbrqMap.size() != 0) {
			bbrq = bbrqMap.get("bbrq");
		}
		// 判断营业状态，因为推送异常账单时不判断营业状态，所以这里控制一下
		TqJtztk jtztk = shopStatusMapper.checkShopOpenStart(DateUtil.parseDate(bbrq));
		if (null == jtztk || "JSSY".equals(jtztk.getCznr())) {
			data.setSuccess(0);
			data.setMsg("门店已打烊，请开店后重试！");
			data.setData(new HashMap<String, Object>());
			return data;
		}
		
		TqZdk tqzdk = orderPrecheckMapper.getZdk(outOrderIdInDB);
		TqZdk tqzdlsk = orderPrecheckMapper.getZdlsk(outOrderIdInDB);
		
		if ((null != tqzdk && "ZDSX_YJ".equals(tqzdk.getJzsx())) || (null != tqzdlsk && "ZDSX_YJ".equals(tqzdlsk.getJzsx()))) {
			JSONObject joData = new JSONObject();
			joData.put("identify", outOrderId);
			if(null != tqzdk){
				joData.put("meal_number", tqzdk.getQch());
			}
			if(null != tqzdlsk){
                joData.put("meal_number", tqzdlsk.getQch());
            }
            data.setData(joData);
            data.setSuccess(1);
            data.setCode(2);
            data.setMsg("订单已同步");
            return data;
        }
		
		// 如果本预订单号是重复落单，直接清掉老数据
        orderPrecheckMapper.clearYdd(outOrderIdInDB);
        orderPrecheckMapper.clearYdxm1(outOrderIdInDB);
        orderPrecheckMapper.clearYdxm2(outOrderIdInDB);
		orderPrecheckMapper.clearYddTcSelectMx(outOrderIdInDB);
		orderPrecheckMapper.clearYddPayments(outOrderIdInDB);
		
        JSONObject orderInfo = orderData.optJSONObject("order_info");
        JSONObject payInfo = orderData.optJSONObject("pay_info");
        JSONObject member = orderData.optJSONObject("member");
        // cardr_type 会员卡类型（1：众赏会员，2：百福会员）  ， pos数据库  cwlxbh 1：微生活，2：众赏，3：百福，4：企迈，5：冥晨
        
        // 默认失败
        data.setSuccess(0);
        // 保存平台预定账单编号
        data.setYddbh(outOrderId);
        try {
            String kdzdbh = billNoData.getKdzdbh();
            Map<String, String> rMap = createBill(orderInfo, kdzdbh, outOrderIdInDB, bbrq, billNoData);
            // 写入 与订单表 bt_ydd
            jointYdd(kdzdbh, orderInfo, outOrderIdInDB, rMap, member);
            //落单
            Map<String, String> orderingR = ordering(orderInfo, kdzdbh, outOrderIdInDB, rMap);
            if ("true".equals(orderingR.get("resultFlag"))) {
                boolean aoflag = firstPay(outOrderIdInDB, payInfo, kdzdbh, bbrq, rMap, orderInfo);
                if (aoflag) {
                	// 这里可以转账单了
                	int ytzR = firstPayMapper.yddToZdXcx(outOrderIdInDB, billNoData.getLsdh(), kdzdbh, billNoData.getJzzdbh(), Integer.parseInt(rMap.get("bcid")), DateUtil.parseDate(bbrq), rMap.get("czybh"), Integer.parseInt(rMap.get("ygdlcs")));
                	LOGGER.info("预订单转订单执行完成，返回代码：" + ytzR);
					if (ytzR == 0) {
					    // 成功后，需要处理一下加价菜
                        List<BtYdxm2> jjcList = firstPayMapper.getQmJjcidList(outOrderIdInDB);
                        for (BtYdxm2 jjc : jjcList) {
                            firstPayMapper.updateYdJjcrwid(kdzdbh, jjc.getYdxm2id(), jjc.getJjcydxm2id());
                        }


                        //更新账单库金额
                        acewillCouponTempMapper.updateTqZdkAfterShare(kdzdbh);
                        //校验实结金额和付款金额是否一致
                        Map<String,BigDecimal> resultMap=acewillCouponTempMapper.selectFkjeAndSjjeByKzdzbh(kdzdbh);
                        if(null!=resultMap&& !resultMap.isEmpty()){
                            BigDecimal fkje=resultMap.get("fkje");
                            BigDecimal sjje=resultMap.get("sjje");
                            if(fkje.compareTo(sjje)!=0){
                                LOGGER.error("实结金额和付款金额不一致，实结金额："+sjje+"，付款金额："+fkje);
                                clearOrder(outOrderIdInDB);
                                data.setSuccess(0);
                                data.setMsg("实结金额和付款金额不一致");
                                data.setData(new HashMap<String, Object>());
                                return data;
                            }
                        }


                		JSONObject joData = new JSONObject();
                        joData.put("identify", outOrderId);
                        joData.put("meal_number", rMap.get("qch"));
                        data.setData(joData);
                        data.setSuccess(1);
                        data.setMsg("付款成功");
                        // 写 tq_acewil_dealdetails c
                        jointDealdetails(kdzdbh, member, bbrq, "99");
                	} else if (ytzR == -100) {
                		LOGGER.info("预订单转订单失败， 预订单数据不存在,平台订单编号：" + outOrderIdInDB + "，错误代码：" + ytzR);
                        data.setSuccess(0);
                        data.setMsg("转订单失败， 预订单数据不存在，错误代码：" + ytzR);
                        data.setData(new HashMap<String, Object>());
                	} else if (ytzR == -200) {
                		LOGGER.info("预订单转订单失败,账单已存在：" + outOrderIdInDB + "，错误代码：" + ytzR);
                        data.setSuccess(0);
                        data.setMsg("转订单失败，账单已存在，错误代码：" + ytzR);
                        data.setData(new HashMap<String, Object>());
                	} else if (ytzR == -800) {
                		LOGGER.info("预订单转订单失败，收银已交班，请重新开班后再试,平台订单编号：" + outOrderIdInDB + "，错误代码：" + ytzR);
                        data.setSuccess(0);
                        data.setMsg("转订单失败， 收银已交班，请重新开班后再试，错误代码：" + ytzR);
                        data.setData(new HashMap<String, Object>());
                	} else {
        				LOGGER.info("预订单转订单失败,平台订单编号：" + outOrderIdInDB + "，错误代码：" + ytzR);
        				clearOrder(outOrderIdInDB);
                        data.setSuccess(0);
                        data.setMsg("转订单失败，错误代码：" + ytzR);
                        data.setData(new HashMap<String, Object>());
        			}
                } else {
                	clearOrder(outOrderIdInDB);
                    data.setSuccess(0);
                    data.setMsg("下单失败");
                    data.setData(new HashMap<String, Object>());
                }
            } else {
            	clearOrder(outOrderIdInDB);
                data.setSuccess(0);
                data.setMsg("下单失败:" + orderingR.get("resultMsg"));
                data.setData(new HashMap<String, Object>());
            }
            return data;
        } catch (Exception e) {
            LOGGER.error("Ignore this exception", e);
            clearOrder(outOrderIdInDB);
            data.setSuccess(0);
            data.setMsg("系统错误:" + e);
            data.setData(new HashMap<String, Object>());
            return data;
        }
    }
	
	public void clearOrder(String yddh) {
        if("true".equals(System.getProperty("debugMode"))){
           return;
        }
		tqZdkMapper.delWdkByYddh(yddh);
		tqZdkMapper.delFklslskByYddh(yddh);
		tqZdkMapper.delZdkByYddh(yddh);
		orderPrecheckMapper.clearYdd(yddh);
		orderPrecheckMapper.clearYdxm1(yddh);
		orderPrecheckMapper.clearYdxm2(yddh);
		orderPrecheckMapper.clearYddTcSelectMx(yddh);
		orderPrecheckMapper.clearYddPayments(yddh);
	}

	public Map<String, String> ordering(JSONObject orderInfo, String kdzdbh, String outOrderIdInDB, Map<String, String> rMap) {
        String resultFlag = "true";
        String resultMsg = "落单成功；";
        Map<String, String> resultMap = new HashMap<String, String>();
        int result_ = 0;
        JSONArray normalitems = new JSONArray();
        if (orderInfo.has("normalitems")) {
            normalitems = orderInfo.getJSONArray("normalitems");
        }

        JSONArray setmeal = new JSONArray();
        if (orderInfo.has("setmeal")) {
            setmeal = orderInfo.getJSONArray("setmeal");
        }
        List<BtYdxm2> bymx = new ArrayList<BtYdxm2>();
        int yddcxh = 0;
        int tcdcxh = 0;
        int jjcdcxh = 0;
//        int fdnumber = 0;
//        double foodboxAmount = 0;

        //单品
		for (int i = 0; i < normalitems.size(); i++) {
			yddcxh = yddcxh + 1;
            jjcdcxh = jjcdcxh + 1;
			JSONObject item = normalitems.getJSONObject(i);
//			Integer did = orderPrecheckMapper.getIdByCode(item.optString("dishsno"));
            TsCmk cmk = orderPrecheckMapper.getDishByCode(item.optString("dishsno"));
            if (null == cmk) {
                resultMsg = "《" + item.optString("name") + "》";
                result_ = -2;
            }
			if (result_ != 0) {
				break;
			}
			item.put("did", cmk.getCmid());
            item.put("localName", cmk.getCmmc1());
//			if (orderInfo.optInt("diningWay", 1) == 2 && 0 != item.optDouble("foodbox_amount", 0)) {
//				fdnumber = fdnumber + item.optInt("number");
//				double fbTemp = ArithUtil.mul(item.optDouble("foodbox_amount", 0), item.optInt("number"));
//				foodboxAmount = ArithUtil.add(foodboxAmount, fbTemp);
//			}
			
			BtYdxm2 ydxm2 = jointYdmx(item, outOrderIdInDB, "CMSX_DP", yddcxh, null, 0, 0, "", "", jjcdcxh);
			bymx.add(ydxm2);

            JSONArray toppings = new JSONArray();
            if (item.has("toppings")) {
                toppings = item.getJSONArray("toppings");
                for (int j = 0; j < toppings.size(); j++) {
                    JSONObject jjcItem = toppings.getJSONObject(j);
                    TsCmk jjcCmk = orderPrecheckMapper.getDishByCode(jjcItem.optString("code"));
                    if (null == jjcCmk) {
                        resultMsg = "《" + jjcItem.optString("name") + "》";
                        result_ = -2;
                    }
                    if (result_ != 0) {
                        break;
                    }
                    jjcItem.put("did", jjcCmk.getCmid());
                    jjcItem.put("localName", jjcCmk.getCmmc1());
                    jjcItem.put("dishsno", jjcCmk.getCmbh());
                    BtYdxm2 jjcYdxm2 = jointYdmx(jjcItem, outOrderIdInDB, "CMSX_JJC", yddcxh, null, 0, 0, "", "", jjcdcxh);
                    bymx.add(jjcYdxm2);
                }
            }
		}

        //套餐
        for (int i = 0; i < setmeal.size(); i++) {
            if (result_ != 0) {
                break;
            }
            yddcxh = yddcxh + 1;
            tcdcxh = tcdcxh + 1;
            JSONObject item = setmeal.getJSONObject(i);
//            Integer did = orderPrecheckMapper.getIdByCode(item.optString("dishsno"));
            TsCmk cmk = orderPrecheckMapper.getDishByCode(item.optString("dishsno"));
            if (null == cmk) {
                result_ = -2;
                resultMsg = "《" + item.optString("name") + "》";
            }
            if (result_ != 0) {
                break;
            }
            item.put("did", cmk.getCmid());
            item.put("localName", cmk.getCmmc1());
            Integer tcid = cmk.getCmid();
            String tcbh = item.optString("dishsno");

            double tcAddPriceSum=0d;



            // 主菜菜品
            JSONArray mainDish = new JSONArray();
            if (item.has("maindish")) {
                mainDish = item.optJSONArray("maindish");
                for (int k = 0; k < mainDish.size(); k++) {
                    jjcdcxh = jjcdcxh + 1;
                    JSONObject gro = mainDish.getJSONObject(k);
//                    Integer groid = orderPrecheckMapper.getIdByCode(gro.optString("dishsno"));
                    TsCmk groCmk = orderPrecheckMapper.getDishByCode(gro.optString("dishsno"));
                    if (null == groCmk) {
                        result_ = -2;
                        resultMsg = "《" + gro.optString("name") + "》";
                    }
                    if (result_ != 0) {
                        break;
                    }
                    gro.put("did", groCmk.getCmid());
                    gro.put("localName", groCmk.getCmmc1());

                    tcAddPriceSum=ArithUtil.add(tcAddPriceSum,gro.optDouble("aprice",0d));

                    BtYdxm2 ydxm3 = jointYdmx(item, outOrderIdInDB, "CMSX_MX", yddcxh, gro, tcid, tcdcxh, tcbh, "ERP_MXLX_SINGLE", jjcdcxh);
                    bymx.add(ydxm3);

                    JSONArray toppings = new JSONArray();
                    if (gro.has("toppings")) {
                        toppings = gro.getJSONArray("toppings");
                        for (int j = 0; j < toppings.size(); j++) {
                            JSONObject jjcItem = toppings.getJSONObject(j);
                            TsCmk jjcCmk = orderPrecheckMapper.getDishByCode(jjcItem.optString("code"));
                            if (null == jjcCmk) {
                                resultMsg = "《" + jjcItem.optString("name") + "》";
                                result_ = -2;
                            }
                            if (result_ != 0) {
                                break;
                            }
                            jjcItem.put("did", jjcCmk.getCmid());
                            jjcItem.put("localName", jjcCmk.getCmmc1());
                            jjcItem.put("dishsno", jjcCmk.getCmbh());
                            BtYdxm2 jjcYdxm2 = jointYdmx(jjcItem, outOrderIdInDB, "CMSX_JJC", yddcxh, gro, tcid, tcdcxh, tcbh, "", jjcdcxh);
                            bymx.add(jjcYdxm2);
                        }
                    }
                }
            }

            // 辅助可选菜品
            JSONArray mandatory = new JSONArray();
            if (item.has("mandatory")) {
                mandatory = item.optJSONArray("mandatory");
                for (int k = 0; k < mandatory.size(); k++) {
                    jjcdcxh = jjcdcxh + 1;
                    JSONObject gro = mandatory.getJSONObject(k);
//                    Integer gdid = orderPrecheckMapper.getIdByCode(gro.optString("dishsno"));
                    TsCmk gdCmk = orderPrecheckMapper.getDishByCode(gro.optString("dishsno"));
                    if (null == gdCmk) {
                        result_ = -2;
                        resultMsg = "《" + gro.optString("name") + "》";
                    }
                    if (result_ != 0) {
                        break;
                    }
                    gro.put("did", gdCmk.getCmid());
                    gro.put("localName", gdCmk.getCmmc1());

                    tcAddPriceSum=ArithUtil.add(tcAddPriceSum,gro.optDouble("aprice",0d));

                    BtYdxm2 ydxm3 = jointYdmx(item, outOrderIdInDB, "CMSX_MX", yddcxh, gro, tcid, tcdcxh, tcbh, "ERP_MXLX_GROUP", jjcdcxh);
                    bymx.add(ydxm3);

                    JSONArray toppings = new JSONArray();
                    if (gro.has("toppings")) {
                        toppings = gro.getJSONArray("toppings");
                        for (int j = 0; j < toppings.size(); j++) {
                            JSONObject jjcItem = toppings.getJSONObject(j);
                            TsCmk jjcCmk = orderPrecheckMapper.getDishByCode(jjcItem.optString("code"));
                            if (null == jjcCmk) {
                                resultMsg = "《" + jjcItem.optString("name") + "》";
                                result_ = -2;
                            }
                            if (result_ != 0) {
                                break;
                            }
                            jjcItem.put("did", jjcCmk.getCmid());
                            jjcItem.put("localName", jjcCmk.getCmmc1());
                            jjcItem.put("dishsno", jjcCmk.getCmbh());
                            BtYdxm2 jjcYdxm2 = jointYdmx(jjcItem, outOrderIdInDB, "CMSX_JJC", yddcxh, gro, tcid, tcdcxh, tcbh, "", jjcdcxh);
                            bymx.add(jjcYdxm2);
                        }
                    }
                }
            }

            if(0!=tcAddPriceSum&&0==item.optDouble("aprice",0d)){
                item.put("aprice",tcAddPriceSum);
            }

            BtYdxm2 ydxm2 = jointYdmx(item, outOrderIdInDB, "CMSX_TC", yddcxh, null, tcid, tcdcxh, tcbh, "", 0);
            bymx.add(0,ydxm2);

//            if(orderInfo.optInt("diningWay", 1) == 2 && 0 != item.optDouble("foodbox_amount", 0)) {
//                fdnumber = fdnumber + item.optInt("number");
//                double fbTemp = ArithUtil.mul(item.optDouble("foodbox_amount", 0), item.optInt("number"));
//                foodboxAmount = ArithUtil.add(foodboxAmount, fbTemp);
//            }
        }

//        if (orderInfo.optInt("diningWay", 1) == 2) {
//            TsCmk fbCmk = orderPrecheckMapper.getFoodBoxInfo("单品餐盒");
//            if (null != fbCmk) {
//    			
//            	
//            } else {
//            	result_ = -4;
//            }
//            
//            yddcxh = yddcxh + 1;
//			BtYdxm2 ydxm2 = jointYdmx(item, outOrderIdInDB, "CMSX_DP", yddcxh, null);
//			bymx.add(ydxm2);
//        }

        if (orderInfo.optInt("diningWay", 1) == 3) {
            result_ = insertSendAmount(kdzdbh, orderInfo.optDouble("send_amount", 0));
            if (result_ == -1) {
                resultMsg = "未添加小程序配送费，请添加后重新下单！";
            }
        }

        // 保存菜品数据
        if (bymx.size() > 0) {
            orderPrecheckMapper.insertBtYdxm2(bymx);
        }

        if (result_ == 0 || result_ == 1) {
            resultFlag = "true";
        } else if (result_ == -502) {
            resultFlag = "false";
            resultMsg = "有菜品被沽清，请门店确认";
        } else if (result_ == -2) {
            resultFlag = "false";
            resultMsg = "菜品数据" + resultMsg + "不存在，请重新同步后再下单；";
        } else if (result_ == -3) {
            resultFlag = "false";
        } else if (result_ == -4) {
            resultFlag = "false";
            resultMsg = "餐盒数据不匹配，请重新同步后再下单；";
        } else if (result_ == -5) {
            resultFlag = "false";
            resultMsg = "套餐或套餐明细不存在，请重新同步后再下单；";
        } else if (result_ == -6) {
            resultFlag = "false";
            resultMsg = "套餐分组或分组明细不存在，请重新同步后再下单；";
        } else {
            resultFlag = "false";
            resultMsg = "未知错误";
        }
        resultMap.put("resultFlag", resultFlag);
        resultMap.put("resultMsg", resultMsg);
        return resultMap;
    }

	public boolean firstPay(String outOrderIdInDB, JSONObject payInfos, String kdzdbh, String bbrq, Map<String, String> rMap, JSONObject orderInfo) {
		JSONArray payInfoArr = payInfos.optJSONArray("pay_info");
		boolean aoflag = true;
		ecoOrderMapper.clearEcoOrderdiscount(outOrderIdInDB);
		double payGive = 0;
		for (int i = 0; i < payInfoArr.size(); i++) {
			JSONObject payInfo = payInfoArr.getJSONObject(i);
			String source = payInfo.optString("source");
			String fkfsmcs2 = "";
			String yl3 = "";
			BigDecimal actualmoney = new BigDecimal("0.00");
			actualmoney = BigDecimal.valueOf(payInfo.optDouble("amount"));
			Map<String, String> payMap = new HashMap<String, String>();

			if ("5".equals(InitDataListener.ggcsMap.get("POS_MEMBER_TYPE"))) {
				payMap.put("byhc", "91");
				payMap.put("byhz", "93");
				payMap.put("cyh", "92");

				if (source.equals("balance")) {
					yl3 = "ERP_FKFS_PUBAPP";
				} else if (source.equals("credit")) {
					yl3 = "ERP_FKFS_PUBAPPCRE";
				} else {
					fkfsmcs2 = source;
				}	
			} else {
				payMap.put("byhc", "85");
				payMap.put("byhz", "87");
				payMap.put("cyh", "86");

				if (source.equals("balance")) {
					yl3 = "ERP_FKFS_QMCZXF";
				} else if (source.equals("credit")) {
					yl3 = "ERP_FKFS_QMJFXF";
				} else if (source.equals("coupon") || source.equals("product")) {
					yl3 = "ERP_FKFS_QMYHQ";
				} else {
					fkfsmcs2 = source;
				}	
			}

            final double storepay = payInfo.optDouble("storepay",0); //储值支付
            double storedGivePay=0d; //储值优惠
            if(0!=storepay){
                 storedGivePay = ArithUtil.sub(payInfo.optDouble("amount"), storepay);
            }
            double storedPay = payInfo.optDouble("amount");
            double giveMoney = jointOrderdiscount(outOrderIdInDB, storedPay, storedGivePay, payMap, source);
            payGive = ArithUtil.add(payGive, giveMoney);
            storedPay = ArithUtil.sub(storedPay, giveMoney);
            actualmoney = BigDecimal.valueOf(storedPay);

            //TsFkfssdk fkfs = firstPayMapper.getFkfsByZs(fkfsmcs2, yl3);
            TsFkfssdk fkfs = null;
            EcoTypeDic etd = firstPayMapper.findEcotypedicByCode("-201", source);
            if (etd == null) {
                fkfs = firstPayMapper.getFkfsByZs(fkfsmcs2, yl3);
            } else {
                fkfs = firstPayMapper.getFkfsByCode(etd.getFkfsbh());
            }

			if (fkfs == null || ("".equals(fkfsmcs2) && "".equals(yl3))) {
				fkfs = firstPayMapper.getFkfsByCode("2020");
			}
            String outtradeno = "";
            if (payInfo.containsKey("outtradeno")) {
                outtradeno = payInfo.optString("outtradeno");
            }

            String tradeno = "";
            if (payInfo.containsKey("tradeno")) {
                tradeno = payInfo.optString("tradeno");
            }

            String promotionSource = null;
            if(payInfo.containsKey("promotion_source")&&"promotion".equals(source)){

                promotionSource=payInfo.optString("promotion_source")+"_"+source;

                final TsYhfssdk tsYhfssdk = firstPayMapper.getDsfyhByYhfsmc2(promotionSource);
                if(null==tsYhfssdk){
                    LOGGER.warn("未绑定的优惠："+promotionSource);
                    promotionSource=null;
                }else {
                    EcoOrderdiscount eod = new EcoOrderdiscount();
                    eod.setOrder_code(outOrderIdInDB);
                    eod.setActivity_id(tsYhfssdk.getYhfsbh());
                    eod.setDiscount_desc(tsYhfssdk.getYhfsmc1());
                    eod.setShop_rate(actualmoney);
                    eod.setDiscount_fee(actualmoney);
                    ecoOrderMapper.insertEcoOrderdiscount(Arrays.asList(eod));
                }
            }
            if(null==promotionSource){
                insertPayments(outOrderIdInDB, fkfs, actualmoney.doubleValue(), orderInfo.optString("addtime", DateUtil.getNowDateYYDDMMHHMMSS()), tradeno,  outtradeno);
            }

		}
		
		firstPayMapper.updateYddToTotalprice(outOrderIdInDB, payGive);
		
		if (payInfoArr.size() == 0) {
			TsFkfssdk fkfs = firstPayMapper.getFkfsByCode("2020");
			insertPayments(outOrderIdInDB, fkfs, 0, orderInfo.optString("addtime", DateUtil.getNowDateYYDDMMHHMMSS()), "", "");
			aoflag = true;
		}
		
		return aoflag;
	}
	
    public void accountsOrder(JSONObject orderInfo, String kdzdbh, String bbrq, String bcid, String czybh, BillNoData billNoData, String cwlxbh) {
        String jzzdbh = billNoData.getJzzdbh();
        Date jzjssj = new Date();
        JSONObject ordermemo = orderInfo.optJSONObject("ordermemo");
        String zdbz = "";
		if(null != ordermemo){
            zdbz = ordermemo.optString("text");
        }
        firstPayMapper.updateZdkZs(zdbz, kdzdbh, jzzdbh, DateUtil.parseDate(bbrq), jzjssj, 1, "99", czybh, "ZDSX_YJ", jzjssj, jzjssj, Integer.parseInt(bcid), orderInfo.optInt("people"), cwlxbh);
        firstPayMapper.updateWdkZs(kdzdbh, jzzdbh, "99", DateUtil.parseDate(bbrq), Integer.parseInt(bcid));
        firstPayMapper.updateFklslskZs(kdzdbh, jzzdbh, Integer.parseInt(bcid));
    }
	
    public void insertDiscount(String kdzdbh, double yhje, String bbrq, String bcid, String skjh, TsYhfssdk yhfs) {
        TqWdk tqwdk = orderPrecheckMapper.getNewWdk(kdzdbh);
        TqWdk tqwdkIn = new TqWdk();
        tqwdkIn.setKdzdbh(kdzdbh);
        tqwdkIn.setClmxid(-1);
        tqwdkIn.setCmbh("YH" + yhfs.getYhfsbh());
        tqwdkIn.setCmmc1(yhfs.getYhfsmc1());
        tqwdkIn.setCmmc2(yhfs.getYhfsmc2());
        tqwdkIn.setDwbh("");
        tqwdkIn.setYzwbh("");
        tqwdkIn.setZwbh("");
        tqwdkIn.setTcfs("");
        tqwdkIn.setTcbl(0);
        tqwdkIn.setTcje(0);
        tqwdkIn.setFzsl(0);
        tqwdkIn.setFzje(0);
        tqwdkIn.setZdsj(0);
        tqwdkIn.setXdh("");
        tqwdkIn.setXdhshry("");
        tqwdkIn.setFwyh("");
        tqwdkIn.setCbdj(new BigDecimal(0));
        tqwdkIn.setCmdj(new BigDecimal(0));
        tqwdkIn.setCmsl(1);
        tqwdkIn.setCmje(new BigDecimal(ArithUtil.mul(yhje, -1)));
        tqwdkIn.setSjje(new BigDecimal(0));
        tqwdkIn.setYhje(new BigDecimal(0));
        tqwdkIn.setDpzkje(new BigDecimal(0));
        tqwdkIn.setZrje(new BigDecimal(yhje));
        tqwdkIn.setZkzt("N");
        tqwdkIn.setXlzkzt("N");
        tqwdkIn.setZkl(100);
        tqwdkIn.setYhfsid(yhfs.getId());
        tqwdkIn.setYhfsbh(yhfs.getYhfsbh());
        tqwdkIn.setYhfs(yhfs.getYhsx());
        tqwdkIn.setTszt("N");
        tqwdkIn.setQczt("N");
        tqwdkIn.setTcdch(0);
        tqwdkIn.setTmbj(new Date());
        tqwdkIn.setDcxh(tqwdk.getDcxh() + 1);
        tqwdkIn.setFsbbrq(DateUtil.parseDate(bbrq));
        tqwdkIn.setFsbcid(Integer.parseInt(bcid));
        tqwdkIn.setFsskjh(skjh);
        tqwdkIn.setSyyhfkfsid(tqwdk.getSyyhfkfsid() + 1);
        tqwdkIn.setScbj(0);
        tqwdkIn.setYongjje(0);
        tqwdkIn.setYongjzkl(100);
        tqwdkIn.setYhxh(0);
        tqwdkIn.setWdbz("");
        tqwdkIn.setKwbz("");
        tqWdk.insert(tqwdkIn);
    }
	
	
	
    public int getClmxid(JSONObject item) {
        String clmxid = orderPrecheckMapper.getClmxidByXmbh(item.optString("did"));
        int clmxidi = 0;
        if (null != clmxid) {
            clmxidi = Integer.parseInt(clmxid);
        }
        return clmxidi;
    }
	
    public void jointYdd(String kdzdbh, JSONObject orderInfo, String outOrderIdInDB, Map<String, String> rMap, JSONObject member) {
        BtYdd by = new BtYdd();
        Shops shops = shopBaseInfoMapper.findShopsData();
        String cwlxbh = InitDataListener.ggcsMap.get("POS_MEMBER_TYPE");
        
        String ydbcid =  rMap.get("bcid");
        String qch = rMap.get("qch");
        String zwbh = rMap.get("zwbh");

        by.setYddh(outOrderIdInDB);
        by.setYdrs(orderInfo.optInt("people"));
        by.setMen(0);
        by.setWomen(0);
        by.setEldernum(0);
        by.setChildnum(0);
        by.setShops_id(shops.getSid() + "");
        by.setTotalprice(orderInfo.optDouble("real_amount"));
        by.setYl1(orderInfo.optDouble("order_amount"));
        by.setYl2(orderInfo.optDouble("orde_dis_amount"));
        by.setShop_rate(orderInfo.optDouble("orde_dis_amount"));
        by.setYdrq(DateUtil.getNowDateYYDDMM());
        by.setQdsj(DateUtil.getNowDateYYDDMMHHMMSS());
        by.setYdbc(ydbcid);
        by.setDdzt("5");
        by.setYl4("XCX");
        by.setKwxh(orderInfo.optString("ordermemo"));
        by.setZlbh(zwbh);
        // 就餐时间 invalid_desc
        if (orderInfo.optString("isDelayedMeal", "0").equals("1")) {
            by.setInvalid_desc("1");
            String mealtime = orderInfo.optString("mealtime", "1990-01-01 00:00:00"); // 可以为空的值默认为""
            by.setMealtime(mealtime);
        } else {
            by.setInvalid_desc("0");
            by.setMealtime("2019-01-01 12:00:00");
        }
        // 下单来源 “0”为门店下单；“1”为线上下单；默认为“0”。V1.1新增
        if (orderInfo.containsKey("channel")) {
            int channel = orderInfo.optInt("channel", 0); // 默认为0
            by.setChannel(channel);
        }
        // 销售模式
        if (orderInfo.containsKey("diningWay")) {
            int diningWay = orderInfo.optInt("diningWay", 1); // 默认为"1"堂食,2外带，3外卖
            by.setDiningway("XSMS_TS");
            by.setYl5("1");
            if (diningWay == 2) {
                by.setDiningway("XSMS_WM"); // "XSMS_WM"是外带，不是外卖
                by.setYl5("2");
            }
            if (diningWay == 3) {
                by.setYl5("3");
                if (orderInfo.containsKey("mealtime")) {
                    String mealtime = orderInfo.optString("mealtime", "201901011200"); // 可以为空的值默认为""
                    mealtime = mealtime.substring(0, 4) + "-" + mealtime.substring(4, 6) + "-" + mealtime.substring(6, 8) + " " + mealtime.substring(8, 10) + ":" + mealtime.substring(10, 12) + ":00";
                    by.setMealtime(mealtime);
                } else {
                    by.setMealtime("立即配送");
                }
            }
        }
        // 外卖联系人
        if (orderInfo.containsKey("lxr")) {
            by.setLxr(orderInfo.optString("lxr", ""));
        }
        // 外卖联系电话
//        if (orderInfo.containsKey("lxrdh")) {
//            by.setLxrdh(orderInfo.optString("lxrdh", ""));
//        }
        
        if (member.containsKey("mobile")) {
			by.setLxrdh(member.optString("mobile", ""));
		}
        
        // 外卖配送地址
        if (orderInfo.containsKey("member_address")) {
            by.setMember_address(orderInfo.optString("member_address", ""));
		}
        //by.setBz(cwlxbh);
        by.setHorseman_name(cwlxbh);
        by.setShrbh(qch);
        by.setFail_type2("catering");
		if (orderInfo.containsKey("commoditytype")) {
			String commoditytype = orderInfo.optString("commoditytype", "catering");
			if ("catering".equals(commoditytype) || "retail".equals(commoditytype)) {
				by.setFail_type2(orderInfo.optString("commoditytype", "catering"));
			}
		}
        by.setBill_num(kdzdbh);
        
        orderPrecheckMapper.insertBtYddByZs(by);
    }
	
	
    public BtYdxm2 jointYdmx(JSONObject jo, String outOrderIdInDB, String xmsx, int dcxh, JSONObject mxJo, Integer tcid, int tcdch, String tcbh, String tcfs, int jjcdcxh) {
        BtYdxm2 ydmx2 = new BtYdxm2();

        //套餐明细特殊处理
		if ("CMSX_MX".equals(xmsx)) {
            ydmx2.setIsactive(0);
            ydmx2.setYddh(outOrderIdInDB);
            ydmx2.setXmid(mxJo.optInt("did"));
            ydmx2.setXmbh(mxJo.optString("dishsno"));
            ydmx2.setXmmc(mxJo.optString("name"));
            if ("1".equals(InitDataListener.ggcsMap.get("XCXITEMNAMETYPE"))) {
                ydmx2.setXmmc(mxJo.optString("localName"));
            }
            ydmx2.setXm1mc(mxJo.optString("name"));
            ydmx2.setXmsx(xmsx);
            // 单价还是菜品原价
            ydmx2.setXmdj(new BigDecimal(mxJo.optString("originalprice", "0")));
            ydmx2.setXmsl(mxJo.optInt("number", 1));
            ydmx2.setZkl(100);
            // 菜品实结金额
            double realprice = mxJo.optDouble("realprice", 0);
            double aprice = mxJo.optDouble("aprice", 0);
            ydmx2.setTotalprice(new BigDecimal(ArithUtil.sub(realprice, aprice)));
            String dwbh = mxJo.optString("dishunit", "份");
            if("".equals(dwbh)){
                dwbh = "份";
            }
            ydmx2.setDwbh(dwbh);
            ydmx2.setKwbh(mxJo.optString("remark"));
            double dishprice = mxJo.optDouble("dishprice", 0);
            ydmx2.setCmje(new BigDecimal(ArithUtil.sub(dishprice, aprice)));
            ydmx2.setTcbh(tcbh);
            ydmx2.setTcdch(tcdch);
            ydmx2.setFzsl(0);
            ydmx2.setDcxh(dcxh);
            ydmx2.setFzje(new BigDecimal(0));
            ydmx2.setYl3(mxJo.optString("discountsprice", "0"));
            ydmx2.setTop_item_id(tcid);
			if ("ERP_MXLX_SINGLE".equals(tcfs)) {
				ydmx2.setYl4("0");
			} else {
				ydmx2.setYl4("1");
			}
            ydmx2.setYl5(xmsx);
            ydmx2.setYl2(jjcdcxh);
        } else if ("CMSX_JJC".equals(xmsx)) {
            ydmx2.setIsactive(0);
            ydmx2.setYddh(outOrderIdInDB);
            ydmx2.setXmid(jo.optInt("did"));
            ydmx2.setXmbh(jo.optString("dishsno"));
            ydmx2.setXmmc(jo.optString("name"));
            if ("1".equals(InitDataListener.ggcsMap.get("XCXITEMNAMETYPE"))) {
                ydmx2.setXmmc(jo.optString("localName"));
            }
            ydmx2.setXm1mc(jo.optString("name"));
            ydmx2.setXmsx("CMSX_DP");
            ydmx2.setXmdj(new BigDecimal(jo.optString("originalprice", "0")));
            ydmx2.setXmsl(jo.optInt("number", 1));
            ydmx2.setZkl(100);
            // 菜品实结金额
            double realprice = jo.optDouble("realprice", 0);
            double aprice = jo.optDouble("aprice", 0);
            ydmx2.setTotalprice(new BigDecimal(ArithUtil.sub(realprice, aprice)));
            String dwbh = jo.optString("dishunit", "份");
            if("".equals(dwbh)){
                dwbh = "份";
            }
            ydmx2.setDwbh(dwbh);
            ydmx2.setKwbh(jo.optString("remark"));
            ydmx2.setCmje(new BigDecimal(ArithUtil.sub(realprice, aprice)));
            ydmx2.setTcbh(tcbh);
            ydmx2.setTcdch(tcdch);
            ydmx2.setFzsl(0);
            ydmx2.setDcxh(dcxh);
            ydmx2.setFzje(new BigDecimal(0));
            ydmx2.setYl3("0");
            ydmx2.setTop_item_id(tcid);
            ydmx2.setYl4("");
            ydmx2.setYl5(xmsx);
            ydmx2.setYl2(jjcdcxh);
        } else {
            ydmx2.setIsactive(0);
            ydmx2.setYddh(outOrderIdInDB);
            ydmx2.setXmid(jo.optInt("did"));
            ydmx2.setXmbh(jo.optString("dishsno"));
            ydmx2.setXmmc(jo.optString("name"));
            if ("1".equals(InitDataListener.ggcsMap.get("XCXITEMNAMETYPE"))) {
                ydmx2.setXmmc(jo.optString("localName"));
            }
            ydmx2.setXm1mc(jo.optString("name"));
            ydmx2.setXmsx(xmsx);
            // 单价还是菜品原价
            ydmx2.setXmdj(new BigDecimal(jo.optString("originalprice", "0")));
            ydmx2.setXmsl(jo.optInt("number"));
            ydmx2.setZkl(100);
            // 菜品实结金额
            double realprice = jo.optDouble("realprice", 0);
            double aprice = jo.optDouble("aprice", 0);
            ydmx2.setTotalprice(new BigDecimal(ArithUtil.sub(realprice, aprice)));
            String dwbh = jo.optString("dishunit", "份");
            if("".equals(dwbh)){
                dwbh = "份";
            }
            ydmx2.setDwbh(dwbh);
            ydmx2.setKwbh(jo.optString("remark"));
            double dishprice = jo.optDouble("dishprice", 0);
            ydmx2.setCmje(new BigDecimal(ArithUtil.sub(dishprice, aprice)));
            ydmx2.setTcbh(tcbh);
            ydmx2.setTcdch(tcdch);
            ydmx2.setFzsl(0);
            ydmx2.setDcxh(dcxh);
            ydmx2.setFzje(new BigDecimal(0));
            ydmx2.setYl3(jo.optString("discountsprice", "0"));
            if (!"CMSX_DP".equals(xmsx)) {
            	 ydmx2.setTop_item_id(tcid);
            }
            ydmx2.setYl4("");
            ydmx2.setYl5(xmsx);
            ydmx2.setYl2(jjcdcxh);
        }

        return ydmx2;
    }

    public void insertPayments(String outOrderIdInDB, TsFkfssdk fkfs, double actualmoney, String addtime, String tradeno, String outtradeno) {
        BtPayments btpay = new BtPayments();
        btpay.setYddh(outOrderIdInDB);
        btpay.setPay_channel("XCX");
        btpay.setPay_name(fkfs.getFkfsmc1());
        btpay.setPay_no(fkfs.getFkfsbh());
        btpay.setVcardid("");
        btpay.setVtele(tradeno);
        btpay.setPay_count(actualmoney);
        btpay.setPay_bb(0);
        btpay.setVocount(0);
        btpay.setFzzhje(0);
        btpay.setFlhl(0);
        btpay.setPay_memo(outtradeno);
        btpay.setOpttime(addtime);
        // 生成预定付款记录
        firstPayMapper.insertBtPayments(btpay);
    }
	
    public boolean takeoutPackagingFee(JSONObject item, String kdzdbh, int number, String xsyh) {
        TsPsjgsdk psjgsdk = tsPsjgsdkMapper.findLocalShopConfig();
        String cmid = orderPrecheckMapper.getFoodBoxSetByCmid(item.optInt("did"));
        if (null != cmid && !"".equals(cmid)) {
            orderPrecheckMapper.addCmNewByZs(kdzdbh, Integer.parseInt(cmid), number, "99", xsyh, "", Math.random() * 10000 + "", "", psjgsdk.getJgxh() + "", psjgsdk.getJgtxbh(), "", 6);
            return true;
        } else {
            return false;
        }
	}
	
    @Transactional
    public Data checkClmx(JSONObject orderData) {
        // 创建返回数据对象
        Data data = new Data();
        // 小程序订单号
        String outOrderId = orderData.optString("out_order_id");
		JSONObject orderInfo = orderData.optJSONObject("order_info");		
        // 保存平台预定账单编号
        data.setYddbh(outOrderId);
        Map<Integer, Double> dishPrice = new HashMap<Integer, Double>();
        try {
            List<Integer> itemIdList = new ArrayList<Integer>();
            JSONArray normalitems = new JSONArray();
            if (orderInfo.has("normalitems")) {
                normalitems = orderInfo.getJSONArray("normalitems");
            }
            JSONArray setmeal = new JSONArray();
            if (orderInfo.has("setmeal")) {
                setmeal = orderInfo.getJSONArray("setmeal");
            }
            for (int i = 0; i < normalitems.size(); i++) {
                JSONObject item = normalitems.getJSONObject(i);
                itemIdList.add(item.optInt("did"));
                dishPrice.put(item.optInt("did"), item.optDouble("price"));
				
            }
            for (int i = 0; i < setmeal.size(); i++) {
                JSONObject item = setmeal.getJSONObject(i);
                itemIdList.add(item.optInt("did"));
                dishPrice.put(item.optInt("did"), item.optDouble("price"));
            }
            List<String> cmmcList = orderPrecheckMapper.checkClmx(itemIdList);
            if (cmmcList.size() > 0) {
                String itemName = "";
				for(String cmmc : cmmcList){
                    itemName = itemName + "《" + cmmc + "》";
                }
                data.setSuccess(0);
                data.setMsg("菜品：" + itemName + "不在餐谱内，请重新同步基础数据;");
                data.setData(new HashMap<String, Object>());
            } else {
                List<Dish> dishList = orderPrecheckMapper.checkPrice(itemIdList);
                String priceMsg = "";
				for(Dish dish : dishList){
                    double a = dishPrice.get(dish.getXmid());
                    float b = dish.getPrice();
					if ((float)a != b) {
                        priceMsg = priceMsg + "《" + dish.getName() + "->pos:" + dish.getPrice() + ",xcx:" + dishPrice.get(dish.getXmid()) + "》";
                    }
                }
                if (priceMsg.length() > 0) {
                    data.setSuccess(0);
                    data.setMsg("小程序菜品价格与pos不一致，请重新同步：" + priceMsg);
                    data.setData(new HashMap<String, Object>());
                } else {
                    data.setSuccess(1);
                    data.setMsg("菜品无误");
                    data.setData(new HashMap<String, Object>());
                }
            }
			
            return data;
        } catch (Exception e) {
            LOGGER.error("Ignore this exception", e);
            data.setSuccess(0);
            data.setMsg("系统错误:" + e);
            data.setData(new HashMap<String, Object>());
            return data;
        }
    }
	
	
    public int insertSendAmount(String kdzdbh, double sendAmount) {
        TqWdk tqwdk = orderPrecheckMapper.getNewWdk(kdzdbh);
        TsCmk saCmk = orderPrecheckMapper.getSendAmount("小程序外卖配送费");
        if (null != saCmk && null != tqwdk) {
            TqWdk tqwdkIn = new TqWdk();
            tqwdkIn.setKdzdbh(kdzdbh);
            tqwdkIn.setClmxid(-1);
            tqwdkIn.setCmid(saCmk.getCmid());
            tqwdkIn.setCmbh(saCmk.getCmbh());
            tqwdkIn.setCmmc1("小程序外卖配送费");
            tqwdkIn.setDwbh(saCmk.getDwbh());
            tqwdkIn.setYzwbh("");
            tqwdkIn.setZwbh("");
            tqwdkIn.setTcfs("");
            tqwdkIn.setTcbl(0);
            tqwdkIn.setTcje(0);
            tqwdkIn.setFzsl(0);
            tqwdkIn.setFzje(0);
            tqwdkIn.setZdsj(0);
            tqwdkIn.setXdh("");
            tqwdkIn.setXdhshry("");
            tqwdkIn.setFwyh("");
            tqwdkIn.setCbdj(new BigDecimal(0));
            tqwdkIn.setCmdj(new BigDecimal(sendAmount));
            tqwdkIn.setCmsl(1);
            tqwdkIn.setCmje(new BigDecimal(sendAmount));
            tqwdkIn.setSjje(new BigDecimal(sendAmount));
            tqwdkIn.setYhje(new BigDecimal(0));
            tqwdkIn.setDpzkje(new BigDecimal(0));
            tqwdkIn.setZrje(new BigDecimal(0));
            tqwdkIn.setYhfs("");
            tqwdkIn.setZkzt(saCmk.getSfzk());
            tqwdkIn.setXlzkzt(saCmk.getSfzk());
            tqwdkIn.setZkl(100);
            tqwdkIn.setTszt("N");
            tqwdkIn.setQczt("N");
            tqwdkIn.setCmsx("CMSX_DP");
            tqwdkIn.setCdbj("*");
            tqwdkIn.setXlid(saCmk.getXlid());
            tqwdkIn.setXlbh(saCmk.getXlbh());
            tqwdkIn.setTcdch(0);
            tqwdkIn.setTmbj(new Date());
            tqwdkIn.setDcxh(tqwdk.getDcxh() + 1);
            tqwdkIn.setFsbbrq(tqwdk.getFsbbrq());
            tqwdkIn.setFsbcid(tqwdk.getFsbcid());
            tqwdkIn.setFsskjh(tqwdk.getFsskjh());
            tqwdkIn.setScbj(1);
            tqwdkIn.setYongjje(0);
            tqwdkIn.setYongjzkl(100);
            tqwdkIn.setSyyhfkfsid(tqwdk.getSyyhfkfsid() + 1);
            tqwdkIn.setWdbz("");
            tqwdkIn.setKwbz("");
            tqWdk.insert(tqwdkIn);
            return 0;
        } else {
            return -1;
        }
    }
	
    public void commUseYhfs(TqZdk zdk, List<TqFklslsk> fklslskList, String skjh, String jzbbrq) {
        for (int i = 0; i < fklslskList.size(); i++) {
            TqFklslsk fklslsk = fklslskList.get(i);
            if ("N".equals(fklslsk.getSfsr())) {
                TsYhfssdk yhfssdk = firstPayMapper.getBjyhYh("BJSR" + fklslsk.getFkfsbh());
                if (null != yhfssdk) {
                    UseYhfsParam useYhfsParam = new UseYhfsParam();
                    useYhfsParam.setBillid(zdk.getKdzdbh());
                    useYhfsParam.setOpType(91003);
                    useYhfsParam.setYhfsId(yhfssdk.getId());
                    useYhfsParam.setFklsid(fklslsk.getId());
                    useYhfsParam.setSkjh(skjh);// 机号
                    useYhfsParam.setJgtxbh(InitDataListener.organVo.getJgtxbh());// 价格体系编号
                    useYhfsParam.setDisAmount(new BigDecimal(fklslsk.getFkje()));
                    useYhfsParam.setBbrq(DateUtil.parseDate(jzbbrq));
                    if ((i + 1) == fklslskList.size() && 0 != zdk.getDslj()) {
                        useYhfsParam.setDslj(new BigDecimal(zdk.getDslj()));
                        useYhfsParam.setHasDslj(true);
                    }
                    useYhfsParam.setInnerCalMoney(true);// 由接口内部使用均摊及账单金额重计算

					useYhfsApiService.CommUseYhfs(useYhfsParam);	
                }
            }
        }
    }
	
	
    public void acewillDiscount(String zdbh, DishVo dsfyh, List<TqFklslsk> fklsList, double storedGivePays) {
        // 拆分部分
        for (TqFklslsk fkls : fklsList) {
            UseYhfsParam useYhfsParam = new UseYhfsParam();
            useYhfsParam.setBillid(zdbh);
            useYhfsParam.setOpType(91002);
            useYhfsParam.setYhfsId(Integer.parseInt(dsfyh.getYhfsid()));
            useYhfsParam.setJzid(fkls.getJzid());
            useYhfsParam.setSkjh(fkls.getSkjh());// 机号
            useYhfsParam.setJgtxbh(InitDataListener.organVo.getJgtxbh());// 价格体系编号
            useYhfsParam.setDisAmount(new BigDecimal(storedGivePays));
            useYhfsParam.setBbrq(fkls.getJzbbrq());
            useYhfsParam.setInnerCalMoney(true);// 由接口内部使用均摊及账单金额重计算
            useYhfsApiService.CommUseYhfs(useYhfsParam);
        }
    }
	
    /**
     * 创建账单
     * @return
     */
    public Map<String, String> createBill(JSONObject orderInfo, String kdzdbh, String yddh, String bbrq, BillNoData billNoData) {
        Map<String, String> rMap = new HashMap<String, String>();
		
        String jtbh = "99";// 机台号

        int bcid = 0; // 班次id
        TqJtztk jtzt = firstPayMapper.getJtZtk(DateUtil.parseDate(bbrq));
        String ygdlcs = jtzt.getYgdlcs();
        String czybh = jtzt.getRybh();// 操作员编号

        TsGgcsk yyms = firstPayMapper.getGgcsToWs("MDYYMS");
        String zwbh = orderInfo.optString("tableno", "");
//		String qch = "";
        String qch = orderInfo.optString("meal_number", "");
        if (null != yyms && "3".equals(yyms.getSdnr())) {
            bcid = firstPayMapper.getBcid(DateUtil.parseDate(bbrq));
        } else {
            bcid = Integer.parseInt(jtzt.getYl1());
        }
		
		if(null == zwbh || "".equals(zwbh)){
//			qch = billNoData.getQch();
            zwbh = "";
        }

        String lsdh = billNoData.getLsdh();

        int ktbcid = bcid;

        TqZdk tqZdk = new TqZdk();
        tqZdk.setKdzdbh(kdzdbh);
        tqZdk.setLsdh(lsdh);
        tqZdk.setJzcs(0);
        tqZdk.setKtskjh(jtbh);
        tqZdk.setFwyh(czybh);
        tqZdk.setKtczry(czybh);
        tqZdk.setKtsj(new Date());
        tqZdk.setKdbbrq(DateUtil.parseDate(bbrq));
        tqZdk.setJzsx("ZDSX_WJ");
        tqZdk.setSource("XCX");
        tqZdk.setCbid(-1);
        tqZdk.setXfks(1);
        tqZdk.setDyzdcs(1);
        tqZdk.setYddh(yddh);
        // 处理是否预约标记
        if (orderInfo.optString("isDelayedMeal", "0").equals("1")) {
            tqZdk.setYl1("1");
            String yysj = orderInfo.optString("mealtime", "1990-01-01 00:00:00");
            tqZdk.setYl2(yysj);
        } else {
            tqZdk.setYl1("0");
            tqZdk.setYl2("2019-01-01 12:00:00");
        }
        if (orderInfo.optInt("diningWay", 1) == 2) {
            tqZdk.setXsms("XSMS_WM");
        } else {
            tqZdk.setXsms("XSMS_TS");
        }

        tqZdk.setYgdlcs(ygdlcs + "");
        tqZdk.setZkl(100);
        tqZdk.setYhfsbh(null);
        tqZdk.setKtbcid(ktbcid);
        tqZdk.setZdzt("");
        tqZdk.setQch(qch);
        tqZdk.setZwbh(zwbh);

//        tqZdkMapper.insert(tqZdk);

        rMap.put("zwbh", zwbh);
        rMap.put("qch", qch);
        rMap.put("bcid", bcid + "");
        rMap.put("czybh", czybh);
        rMap.put("jtbh", jtbh);
        rMap.put("bbrq", bbrq);
        rMap.put("ygdlcs", ygdlcs);
		
        return rMap;
    }
	
    public boolean isNumeric(String str) {
		for (int i = str.length(); --i >= 0;) {
            if (!Character.isDigit(str.charAt(i))) {
                return false;
            }
        }
        return true;
    }
	
    public int insertFoodBoxAmount(String kdzdbh, TsCmk fbCmk, int number, double foodBoxAmount, Map<String, String> rMap) {
//		TqWdk tqwdk = orderPrecheckMapper.getNewWdk(kdzdbh);
        if (null != fbCmk) {
            TqWdk tqwdkIn = new TqWdk();
            tqwdkIn.setKdzdbh(kdzdbh);
            tqwdkIn.setClmxid(-1);
            tqwdkIn.setCmid(fbCmk.getCmid());
            tqwdkIn.setCmbh(fbCmk.getCmbh());
            tqwdkIn.setCmmc1(fbCmk.getCmmc1());
            tqwdkIn.setDwbh(fbCmk.getDwbh());
            tqwdkIn.setYzwbh("");
            tqwdkIn.setZwbh("");
            tqwdkIn.setTcfs("");
            tqwdkIn.setTcbl(0);
            tqwdkIn.setTcje(0);
            tqwdkIn.setFzsl(0);
            tqwdkIn.setFzje(0);
            tqwdkIn.setZdsj(0);
            tqwdkIn.setXdh("");
            tqwdkIn.setXdhshry("");
            tqwdkIn.setFwyh("");
            tqwdkIn.setCbdj(new BigDecimal(0));
            tqwdkIn.setCmdj(new BigDecimal(foodBoxAmount));
            tqwdkIn.setCmsl(number);
            tqwdkIn.setCmje(new BigDecimal(foodBoxAmount));
            tqwdkIn.setSjje(new BigDecimal(foodBoxAmount));
            tqwdkIn.setYhje(new BigDecimal(0));
            tqwdkIn.setDpzkje(new BigDecimal(0));
            tqwdkIn.setZrje(new BigDecimal(0));
            tqwdkIn.setYhfs("");
            tqwdkIn.setZkzt(fbCmk.getSfzk());
            tqwdkIn.setXlzkzt(fbCmk.getSfzk());
            tqwdkIn.setZkl(100);
            tqwdkIn.setTszt("N");
            tqwdkIn.setQczt("N");
            tqwdkIn.setCmsx("CMSX_DP");
            tqwdkIn.setCdbj("*");
            tqwdkIn.setXlid(fbCmk.getXlid());
            tqwdkIn.setXlbh(fbCmk.getXlbh());
            tqwdkIn.setTcdch(0);
            tqwdkIn.setTmbj(new Date());
            tqwdkIn.setDcxh(999);
            tqwdkIn.setFsbbrq(DateUtil.parseDate(rMap.get("bbrq")));
            tqwdkIn.setFsbcid(Integer.parseInt(rMap.get("bcid")));
            tqwdkIn.setFsskjh(rMap.get("jtbh"));
            tqwdkIn.setScbj(1);
            tqwdkIn.setYongjje(0);
            tqwdkIn.setYongjzkl(100);
            tqwdkIn.setSyyhfkfsid(1);
            tqwdkIn.setWdbz("");
            tqwdkIn.setKwbz("");
            tqWdk.insert(tqwdkIn);
            return 0;
        } else {
            return -1;
        }
    }
    
    @Transactional
	public Data orderPrecheckBefore(JSONObject orderData) {
		// 创建返回数据对象
		Data data = new Data();
		data.setSuccess(1);
		data.setMsg("验证通过！");
		JSONObject orderInfo = orderData.optJSONObject("order_info");
		JSONObject payInfos = orderData.optJSONObject("pay_info");

		try {
			double orderAmount = orderInfo.optDouble("order_amount", 0); // 账单金额  orderAmount = realAmount + ordeDisAmount
			double realAmount = orderInfo.optDouble("real_amount", 0); // 实结金额payAmount
			double ordeDisAmount = orderInfo.optDouble("orde_dis_amount", 0); // 优惠金额
			double payAmount = 0;
			double dishRealAmount = 0;
			JSONArray payInfoArr = payInfos.optJSONArray("pay_info");

			for (int i = 0; i < payInfoArr.size(); i++) {
				JSONObject payInfo = payInfoArr.getJSONObject(i);
				double pAmount = payInfo.optDouble("amount");
				payAmount = ArithUtil.add(payAmount, pAmount);
			}

			if (orderAmount != ArithUtil.add(realAmount, ordeDisAmount)) {
				data.setSuccess(0);
				data.setMsg("“账单金额”不等于“实结金额”+“优惠金额”，请联系管理员！");
				data.setData(new HashMap<String, Object>());
				return data;
			}

			if (realAmount != payAmount) {
				data.setSuccess(0);
				data.setMsg("“实结金额”不等于“支付金额”，请联系管理员！");
				data.setData(new HashMap<String, Object>());
				return data;
			}

			if (orderInfo.has("normalitems")) {
				JSONArray normalitems = new JSONArray();
				normalitems = orderInfo.getJSONArray("normalitems");
				for (int i = 0; i < normalitems.size(); i++) {
					JSONObject item = normalitems.getJSONObject(i);
					TsCmk cmk = orderPrecheckMapper.getDishByCode(item.optString("dishsno"));
					if (null == cmk) {
						data.setSuccess(0);
						data.setMsg("菜品“" + item.optString("name") + "”不存在，请联系管理员！");
						data.setData(new HashMap<String, Object>());
						return data;
					}
					if (!"CMSX_DP".equals(cmk.getCmsx())) {
						data.setSuccess(0);
						data.setMsg("菜品“" + item.optString("name") + "”不是单品菜品，请联系管理员！");
						data.setData(new HashMap<String, Object>());
						return data;
					}
					dishRealAmount = ArithUtil.add(dishRealAmount, item.optDouble("realprice", 0));
				}
			}

			if (orderInfo.has("setmeal")) {
				JSONArray setmeal = new JSONArray();
				setmeal = orderInfo.getJSONArray("setmeal");
				for (int i = 0; i < setmeal.size(); i++) {
					JSONObject item = setmeal.getJSONObject(i);
					double setmealRealprice = item.optDouble("realprice", 0);
					double mmRealprice = 0;
					TsCmk cmk = orderPrecheckMapper.getDishByCode(item.optString("dishsno"));
					if (null == cmk) {
						data.setSuccess(0);
						data.setMsg("菜品“" + item.optString("name") + "”不存在，请联系管理员！");
						data.setData(new HashMap<String, Object>());
						return data;
					}
					if (!"CMSX_TC".equals(cmk.getCmsx())) {
						data.setSuccess(0);
						data.setMsg("菜品“" + item.optString("name") + "”不是套餐菜品，请联系管理员！");
						data.setData(new HashMap<String, Object>());
						return data;
					}

					if (item.has("maindish")) {
						JSONArray mainDish = new JSONArray();
						mainDish = item.optJSONArray("maindish");
						for (int j = 0; j < mainDish.size(); j++) {
							JSONObject md = mainDish.getJSONObject(j);
							mmRealprice = ArithUtil.add(mmRealprice, md.optDouble("realprice", 0));
						}
					}

					if (item.has("mandatory")) {
						JSONArray mandatory = new JSONArray();
						mandatory = item.optJSONArray("mandatory");
						for (int j = 0; j < mandatory.size(); j++) {
							JSONObject md = mandatory.getJSONObject(j);
							mmRealprice = ArithUtil.add(mmRealprice, md.optDouble("realprice", 0));
						}
					}

					if (setmealRealprice != mmRealprice) {
						data.setSuccess(0);
						data.setMsg("套餐菜品“" + item.optString("name") + "”金额与明细金额不符，请联系管理员！");
						data.setData(new HashMap<String, Object>());
						return data;
					}
					dishRealAmount = ArithUtil.add(dishRealAmount, setmealRealprice);
				}
			}

			if (dishRealAmount != realAmount) {
				data.setSuccess(0);
				data.setMsg("“实结金额”不等于“明细金额”，请联系管理员！");
				data.setData(new HashMap<String, Object>());
				return data;
			}

			if (dishRealAmount != payAmount) {
				data.setSuccess(0);
				data.setMsg("“明细金额”不等于“支付金额”，请联系管理员！");
				data.setData(new HashMap<String, Object>());
				return data;
			}

			return data;
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			data.setSuccess(0);
			data.setMsg("系统错误:" + e);
			data.setData(new HashMap<String, Object>());
			return data;
		}
	}

    @Override
	public void jointDealdetails(String kdzdbh, JSONObject member, String bbrq, String skjh) {
		try {
			acewillCouponTempMapper.delAcewilDealdetails(kdzdbh);
			String cardno = member.optString("cno", "");
			double balance = member.optDouble("bala", 0);
			double credit = member.optDouble("consumenu", 0);

            String dtl="";
            try{
                 dtl = getMemberDtl(kdzdbh, member);
            }catch (Exception e){
                LOGGER.error("获取会员消费信息异常",e);
            }

			acewillCouponTempMapper.insertAcewilDealdetails(cardno, kdzdbh, 0, balance, credit, DateUtil.parseDate(bbrq), "0000", skjh, 1,dtl);
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
		}
	}

    private String getMemberDtl(String kdzdbh,JSONObject member) {

        String idType="5";
        String id="";
        String cno = member.optString("cno");
        String mobile = member.optString("mobile");
        if(StringUtils.isNotEmpty(mobile)){
            id=mobile;
        }else {
            if(StringUtils.isNotEmpty(cno)){
                idType="9";
                id=cno;
            }else {
                LOGGER.warn("未取得会员手机号和卡号,无法查询会员消费信息:"+member);
                return "";
            }
        }


        CommApiData data=new CommApiData();
        data.setCode(0);

        JSONObject param=new JSONObject();
        param.put("jtbh","99");
        param.put("url","/api/member/getConsumeNumAndBalaByIdType");
        param.put("orderNo", InitDataListener.thirdMap.get("MINGCHEN_MERNO")+InitDataListener.organVo.getJgbh()+kdzdbh);
        param.put("transCode","C805");
        param.put("deviceDate",DateUtil.getNowDateYYDDMMHHMMSS());

        JSONObject dataJson=new JSONObject();
        dataJson.put("idType",idType);
        dataJson.put("id",id);
        dataJson.put("days",30);
        param.put("data",dataJson);

        commApiService.merchantRequest(data, param);

        if(0!=data.getCode()){
            commApiService.merchantRequest(data, param);
        }

        if(0==data.getCode()){
            final Object result = data.getData();
            if(null!=result){
                JSONObject resultJson=JSONObject.fromObject(result);
                if("0".equals(resultJson.optString("errcode"))){

                    final JSONArray detail = resultJson.optJSONObject("data").optJSONArray("detail");
                    if(CollectionUtils.isNotEmpty(detail)){
                        JSONObject dtl= (JSONObject) detail.get(0);
                        return dtl.optString("detail");
                    }
                }
            }
        }
        return "";
    }

    public double jointOrderdiscount(String yddh, double storedPay, double storedGivePay, Map<String, String> payMap, String type) {
		try {
			double giveMoney = 0;
			if ("balance".equals(type)) {
				if ("1".equals(InitDataListener.ggcsMap.get("POS_MEMBALANCE_USEDIS")) || "3".equals(InitDataListener.ggcsMap.get("POS_MEMBALANCE_USEDIS"))) {
					DishVo dsfyhB = new DishVo();
                    if ("1".equals(InitDataListener.ggcsMap.get("POS_MEMBALANCE_USEDIS"))) {
                        dsfyhB = firstPayMapper.getDsfyh(payMap.get("byhz"));
                    } else if ("3".equals(InitDataListener.ggcsMap.get("POS_MEMBALANCE_USEDIS"))) {
                        dsfyhB = firstPayMapper.getDsfyh(payMap.get("byhc"));
                    }
					if (null != dsfyhB) {
//						firstPayMapper.insertBtYddPayActive(yddh, "", storedGivePay, "", "BALANC", Integer.parseInt(dsfyhB.getYhfsid()));
						List<EcoOrderdiscount> ecoOrderdiscounts = new ArrayList<EcoOrderdiscount>();
						EcoOrderdiscount eod = new EcoOrderdiscount();
						eod.setOrder_code(yddh);
						eod.setActivity_id(dsfyhB.getYhfsbh());
						eod.setDiscount_desc(dsfyhB.getItem_name());
						eod.setShop_rate(BigDecimal.valueOf(storedGivePay));
						eod.setDiscount_fee(BigDecimal.valueOf(storedGivePay));
						ecoOrderdiscounts.add(eod);
				        ecoOrderMapper.insertEcoOrderdiscount(ecoOrderdiscounts);

						giveMoney = storedGivePay;
					} else {
						LOGGER.info("储值未拆分:未设置赠送账户优惠");
					}
				}
				if ("2".equals(InitDataListener.ggcsMap.get("POS_MEMBALANCE_USEDIS"))) {
					String acewillDiscount = InitDataListener.ggcsMap.get("ACEWILLDISCOUNT");
					if (null != acewillDiscount) {
						if (isNumeric(acewillDiscount) && Integer.parseInt(acewillDiscount) > 0 && Integer.parseInt(acewillDiscount) <= 100) {
							DishVo dsfyhB = firstPayMapper.getDsfyh(payMap.get("byhc"));
							double scale = Integer.parseInt(acewillDiscount);
							double scale_pay = ArithUtil.sub(storedPay, ArithUtil.mul(storedPay, ArithUtil.div(scale, 100, 2)));
							scale_pay = ArithUtil.round(scale_pay, 2);
							if (null != dsfyhB) {
//								firstPayMapper.insertBtYddPayActive(yddh, "", scale_pay, "", "BALANC", Integer.parseInt(dsfyhB.getYhfsid()));
								List<EcoOrderdiscount> ecoOrderdiscounts = new ArrayList<EcoOrderdiscount>();
								EcoOrderdiscount eod = new EcoOrderdiscount();
								eod.setOrder_code(yddh);
								eod.setActivity_id(dsfyhB.getYhfsbh());
								eod.setDiscount_desc(dsfyhB.getItem_name());
								eod.setShop_rate(BigDecimal.valueOf(scale_pay));
								eod.setDiscount_fee(BigDecimal.valueOf(scale_pay));
								ecoOrderdiscounts.add(eod);
						        ecoOrderMapper.insertEcoOrderdiscount(ecoOrderdiscounts);

								giveMoney = scale_pay;
							} else {
								LOGGER.info("储值未拆分:未设置拆分优惠");
							}
						} else {
							LOGGER.info("储值未拆分:拆分比例=" + acewillDiscount);
						}
					} else {
						LOGGER.info("储值未拆分:未设置拆分比例");
					}
				}
			} else if ("credit".equals(type)) {
				if ("3".equals(InitDataListener.ggcsMap.get("POS_MEMBALANCE_USEDIS")) || "1".equals(InitDataListener.ggcsMap.get("POS_MEMCREDIT_USEDIS"))) {
					DishVo dsfyhC = firstPayMapper.getDsfyh(payMap.get("cyh"));
					if (null != dsfyhC) {
						firstPayMapper.insertBtYddPayActive(yddh, "", storedGivePay, "", "CREDIT", Integer.parseInt(dsfyhC.getYhfsid()));
						List<EcoOrderdiscount> ecoOrderdiscounts = new ArrayList<EcoOrderdiscount>();
						EcoOrderdiscount eod = new EcoOrderdiscount();
						eod.setOrder_code(yddh);
						eod.setActivity_id(dsfyhC.getYhfsbh());
						eod.setDiscount_desc(dsfyhC.getItem_name());
						eod.setShop_rate(BigDecimal.valueOf(storedGivePay));
						eod.setDiscount_fee(BigDecimal.valueOf(storedGivePay));
						ecoOrderdiscounts.add(eod);
				        ecoOrderMapper.insertEcoOrderdiscount(ecoOrderdiscounts);

						giveMoney = storedGivePay;
					} else {
						LOGGER.info("积分未拆分:未设置积分优惠");
					}
				}
			} else {
				return 0;
			}
			return giveMoney;
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			return 0;
		}
	}
}
