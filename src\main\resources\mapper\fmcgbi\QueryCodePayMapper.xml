<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.fmcgbi.rest.mapper.FmcgbiQueryCodePayMapper">

	<select id="getTteo" resultType="com.tzx.fmcgbi.rest.vo.ThirdExceptOrderVo">
		select tteo.*, zd.lsdh,zd.ktczry, zd.kdbbrq, zd.ktbcid
		from tq_zdk zd 
		left join tq_third_except_order tteo on tteo.billid = zd.kdzdbh
		where zd.yddh = #{tradeNo} order by tteo.createtime desc limit 1
	</select>

</mapper>
