package com.tzx.ecoserver.common;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019-11-08.
 */
import com.alibaba.druid.util.Base64;
import com.tzx.publics.util.DatagramUtil;
import com.tzx.publics.util.DateUtil;
import sun.audio.AudioPlayer;

import javax.sound.sampled.*;
import java.io.*;
import java.net.URLEncoder;

public class PlayWavUtil {
    private static  int refundNum = 0;
    private static  int newOrderNum = 0;
    FileInputStream fileInputStream = null;
    public PlayWavUtil(String file) {
//        try {
//            this.fileInputStream = new FileInputStream(file);
//            AudioPlayer.player.start(new FileInputStream(file));
//        } catch (FileNotFoundException e) {
//            e.printStackTrace();
//        }

    }
    public void play(){

    }
    public void desplay(){
        if(null!=fileInputStream){
            try {
                fileInputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
    public static void playNewOrder(){
//        if(!playNoThread.isAlive()){
//            playNoThread.start();
//        }

    }

    public static void playRefundOrder(){
        try {
            FileInputStream inRef = new FileInputStream("D:\\cd\\svn\\NewPos2.0\\资源\\取消订单wav.wav");
            AudioPlayer.player.start(inRef);
//            AudioPlayer.player.stop(inRef);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }
    }

    public String playVoiceFile(String filePath,int val) throws Exception {
        if (!filePath.equals("")) {
            //Get audio input stream
            AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(new File(filePath));
            //Get audio coding object
            AudioFormat audioFormat = audioInputStream.getFormat();
            //Set data entry
            DataLine.Info dataLineInfo = new DataLine.Info(SourceDataLine.class, audioFormat,
                    AudioSystem.NOT_SPECIFIED);
            SourceDataLine sourceDataLine = (SourceDataLine) AudioSystem.getLine(dataLineInfo);
            sourceDataLine.open(audioFormat);
            FloatControl volctrl=(FloatControl)sourceDataLine.getControl(FloatControl.Type.MASTER_GAIN);
            volctrl.setValue(val);// newVal - the value of volume slider

            sourceDataLine.start();
            //Read from the data sent to the mixer input stream
            int count;
            byte tempBuffer[] = new byte[1024];
            while ((count = audioInputStream.read(tempBuffer, 0, tempBuffer.length)) != -1) {
                if (count > 0) {
                    sourceDataLine.write(tempBuffer, 0, count);
                }
            }
            //Empty the data buffer, and close the input
            sourceDataLine.drain();
            sourceDataLine.close();
        }

        return null;
    }
    public static void main(String[] args) throws UnsupportedEncodingException {
//        String msgBody = "ACTION="+"showmsg"+"|FDJGXH=6" ;
//        String partMsgBody =  "有一笔历史账单部分退单失败，请注意...";
//        msgBody = msgBody + "|msg="+ Base64.byteArrayToBase64(URLEncoder.encode(partMsgBody,"utf-8").getBytes());
//        System.out.println(msgBody);
//        DatagramUtil.sendUdpMsg(msgBody, Integer.valueOf("5633").intValue(),"127.0.0.1");
//
//        System.out.println(new String(Base64.base64ToByteArray("09DSu7HKwPrKt9XLtaXNy7Wlyqew3KOsx+vXotLiLi4u")));
//        System.out.println(new String(Base64.base64ToByteArray("MjAyMC0wMS0wOSAxMzo0MzoyNOaIkOWKn+a1i+ivlQ==")));
        PlayWavUtil playWavUtil =   new PlayWavUtil("D:\\cd\\svn\\NewPos2.0\\资源\\订单wav.wav");
        System.out.println("开始播放");
        int val = 0;
        val = Math.round(51 * 86 / 100 - 80) ;
        if(val > 6){
            val = 6;
        }
        else if(val < -80)
        {
            val = -80;
        }
        System.out.println(val);
        try {
            playWavUtil.playVoiceFile("D:\\cd\\svn\\NewPos2.0\\资源\\订单wav.wav",val);
        } catch (Exception e) {
            e.printStackTrace();
        }
//
//        try {
//            playWavUtil.playVoiceFile("D:\\cd\\svn\\NewPos2.0\\资源\\订单wav.wav");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
////        playWavUtil.play();
        System.out.println("结束播放");
//        //playWavUtil.desplay();
    }

    public static int getRefundNum() {
        return refundNum;
    }
    public static int getNewOrderNum() {
        return newOrderNum;
    }

    public static synchronized void setRefundNum(int refundNum) {
        PlayWavUtil.refundNum = refundNum;
    }

    public static synchronized void addNewOrderNum(int val) {
        PlayWavUtil.newOrderNum = PlayWavUtil.newOrderNum  + val;
    }
}

