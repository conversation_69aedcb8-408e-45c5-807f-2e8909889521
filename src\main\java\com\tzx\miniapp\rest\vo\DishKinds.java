package com.tzx.miniapp.rest.vo;

import javax.persistence.Entity;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.List;

@Entity
public class DishKinds implements Serializable {

	private int id;
	private String name;
	private int seq;
	private String dishkindsno;
	private int must;
	private int must_seq;
	private int suggest;
	private String dkSaleEnd;
	private String dkSaleStart;
	private int dkHide; // 是否隐藏：1显示，2隐藏
	private int dkOrder; // 排序值，升序排列
	private String icon;

	// private int border;
	// private String icon;
	// private int dish_count;
	// private int pdkid;
	// private int pkid;

	@Transient
	private List<String> children;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public int getSeq() {
		return seq;
	}

	public void setSeq(int seq) {
		this.seq = seq;
	}

	public String getDishkindsno() {
		return dishkindsno;
	}

	public void setDishkindsno(String dishkindsno) {
		this.dishkindsno = dishkindsno;
	}

	public int getMust() {
		return must;
	}

	public void setMust(int must) {
		this.must = must;
	}

	public int getMust_seq() {
		return must_seq;
	}

	public void setMust_seq(int must_seq) {
		this.must_seq = must_seq;
	}

	public int getSuggest() {
		return suggest;
	}

	public void setSuggest(int suggest) {
		this.suggest = suggest;
	}

	public List<String> getChildren() {
		return children;
	}

	public void setChildren(List<String> children) {
		this.children = children;
	}

	public String getDkSaleEnd() {
		return dkSaleEnd;
	}

	public void setDkSaleEnd(String dkSaleEnd) {
		this.dkSaleEnd = dkSaleEnd;
	}

	public String getDkSaleStart() {
		return dkSaleStart;
	}

	public void setDkSaleStart(String dkSaleStart) {
		this.dkSaleStart = dkSaleStart;
	}

	public int getDkHide() {
		return dkHide;
	}

	public void setDkHide(int dkHide) {
		this.dkHide = dkHide;
	}

	public int getDkOrder() {
		return dkOrder;
	}

	public void setDkOrder(int dkOrder) {
		this.dkOrder = dkOrder;
	}

	public String getIcon() {
		return icon;
	}

	public void setIcon(String icon) {
		this.icon = icon;
	}
}
