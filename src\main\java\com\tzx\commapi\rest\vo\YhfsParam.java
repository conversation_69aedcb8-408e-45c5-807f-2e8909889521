package com.tzx.commapi.rest.vo;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by <PERSON>x<PERSON> on 2020-05-11.
 */
public class YhfsParam {
    private String zdbh;
    private Integer yhfsid;
    private String yhsx;
    private String sguid;
    private Date bbrq;
    private String sparam1;
    private String sparam2;
    private String sparam5;
    private Integer optype;
    private BigDecimal nparam1;

    public String getZdbh() {
        return zdbh;
    }

    public void setZdbh(String zdbh) {
        this.zdbh = zdbh;
    }

    public Integer getYhfsid() {
        return yhfsid;
    }

    public void setYhfsid(Integer yhfsid) {
        this.yhfsid = yhfsid;
    }

    public String getYhsx() {
        return yhsx;
    }

    public void setYhsx(String yhsx) {
        this.yhsx = yhsx;
    }

    public String getSguid() {
        return sguid;
    }

    public void setSguid(String sguid) {
        this.sguid = sguid;
    }

    public Date getBbrq() {
        return bbrq;
    }

    public void setBbrq(Date bbrq) {
        this.bbrq = bbrq;
    }

    public String getSparam1() {
        return sparam1;
    }

    public void setSparam1(String sparam1) {
        this.sparam1 = sparam1;
    }

    public String getSparam2() {
        return sparam2;
    }

    public void setSparam2(String sparam2) {
        this.sparam2 = sparam2;
    }

    public String getSparam5() {
        return sparam5;
    }

    public void setSparam5(String sparam5) {
        this.sparam5 = sparam5;
    }

    public Integer getOptype() {
        return optype;
    }

    public void setOptype(Integer optype) {
        this.optype = optype;
    }

    public BigDecimal getNparam1() {
        return nparam1;
    }

    public void setNparam1(BigDecimal nparam1) {
        this.nparam1 = nparam1;
    }
}
