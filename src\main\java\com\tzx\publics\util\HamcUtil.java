package com.tzx.publics.util;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

import com.tzx.commapi.rest.service.impl.CommApiService;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class HamcUtil {
	/**
	 * hmac+签名算法 加密
	 * 
	 * @param content 内容
	 * @param charset 字符编码
	 * @param key 加密秘钥
	 * @param hamaAlgorithm hamc签名算法名称:例如HmacMD5,HmacSHA1,HmacSHA256
	 * @return
	 */
	private final static Logger LOGGER = LoggerFactory.getLogger(HamcUtil.class);
	public static String getHmacSign(String content, String charset, String key, String hamaAlgorithm) {
		byte[] result = null;
		try {
			LOGGER.info("getHmacSign-1:content=" + content + "#charset=" + charset + "#key=" + key + "#hamaAlgorithm=" + hamaAlgorithm);
			// 根据给定的字节数组构造一个密钥,第二参数指定一个密钥算法的名称
			SecretKeySpec signinKey = new SecretKeySpec(key.getBytes(), hamaAlgorithm);
			LOGGER.info("getHmacSign-2:signinKey=" + signinKey);
			// 生成一个指定 Mac 算法 的 Mac 对象
			Mac mac = Mac.getInstance(hamaAlgorithm);
			LOGGER.info("getHmacSign-3:mac=" + mac);
			// 用给定密钥初始化 Mac 对象
			mac.init(signinKey);
			LOGGER.info("getHmacSign-4:mac.init=mac.init");
			// 完成 Mac 操作
			byte[] rawHmac;
			LOGGER.info("getHmacSign-5:rawHmac=rawHmac");
			rawHmac = mac.doFinal(content.getBytes(charset));
			LOGGER.info("getHmacSign-6:rawHmac=" + rawHmac);
			result = Base64.encodeBase64(rawHmac);
			LOGGER.info("getHmacSign-7:result=" + result);

		} catch (NoSuchAlgorithmException e) {
			LOGGER.info(e.getMessage());
//			System.err.println(e.getMessage());
		} catch (InvalidKeyException e) {
			LOGGER.info(e.getMessage());
//			System.err.println(e.getMessage());
		} catch (IllegalStateException | UnsupportedEncodingException e) {
			LOGGER.info(e.getMessage());
//			System.err.println(e.getMessage());
		} catch (Exception e) {
			LOGGER.info(e.getMessage());
		}
		if (null != result) {
			LOGGER.info("getHmacSign-8.1:result=" + result);
			return new String(result);
		} else {
			LOGGER.info("getHmacSign-8.2:result=" + result);
			return null;
		}
	}
	
	public static void main(String[] args) {
		String token = "";
		String grantCode = "ba67d4fa46";
		String nonce = "11886";
		String openId = "d14c1559e87b747d577c834b275a4310";
		String timestamp = "1465185768";
		String openKey = "LyvrkvkxRkG2R6aM55bXpPwjYAbkEXTbVnKwfDYvVHjNwNFAmx";
		// GrantCode=ba67d4fa46&Nonce=11886&OpenId=d14c1559e87b747d577c834b275a4310&Timestamp=1465185768
		// HmacSHA1
		// rQ861PA1ledQ4VhZXY6WAjlZfD0=
		// rQ861PA1ledQ4VhZXY6WAjlZfD0=
		String srcStr = "GrantCode=" + grantCode + "&Nonce=" + nonce + "&OpenId=" + openId + "&Timestamp=" + timestamp;
		String qmSign = getHmacSign(srcStr, "UTF-8", openKey, "HmacSHA1");
		System.out.println(qmSign);

		// rQ861PA1ledQ4VhZXY6WAjlZfD0%3D
		// rQ861PA1ledQ4VhZXY6WAjlZfD0%3D
		try {
			System.out.println(URLEncoder.encode(qmSign, "UTF-8" ));
		} catch (UnsupportedEncodingException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
}
