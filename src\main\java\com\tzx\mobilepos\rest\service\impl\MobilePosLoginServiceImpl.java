package com.tzx.mobilepos.rest.service.impl;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.tzx.mobilepos.common.Data;
import com.tzx.mobilepos.common.SysDictionary;
import com.tzx.mobilepos.rest.mapper.MobilePosAcewillCouponTempMapper;
import com.tzx.mobilepos.rest.mapper.MobilePosTqJtztkMapper;
import com.tzx.mobilepos.rest.mapper.MobilePosTqThirdExceptOrderMapper;
import com.tzx.mobilepos.rest.mapper.MobilePosTqYhMtCouponsMapper;
import com.tzx.mobilepos.rest.mapper.MobilePosTqZdkMapper;
import com.tzx.mobilepos.rest.mapper.MobilePosTsCzykMapper;
import com.tzx.mobilepos.rest.mapper.MobilePosTsJtsdkMapper;
import com.tzx.mobilepos.rest.model.TqJtztk;
import com.tzx.mobilepos.rest.model.TqZdk;
import com.tzx.mobilepos.rest.model.TsBck;
import com.tzx.mobilepos.rest.model.TsBmkzk;
import com.tzx.mobilepos.rest.model.TsCzyk;
import com.tzx.mobilepos.rest.model.TsGgcsk;
import com.tzx.mobilepos.rest.model.TsJtsdk;
import com.tzx.mobilepos.rest.service.IMobilePosLoginService;
import com.tzx.mobilepos.rest.vo.AccountsOrder;
import com.tzx.mobilepos.rest.vo.PayMentVo;
import com.tzx.publics.listener.InitDataListener;
import com.tzx.publics.util.DateUtil;
import com.tzx.publics.util.GsonUtil;
import com.tzx.publics.util.ParamUtil;
import com.tzx.publics.util.ReqDataUtil;

import net.sf.json.JSONObject;

@Service
public class MobilePosLoginServiceImpl implements IMobilePosLoginService {
	private final static Logger LOGGER = LoggerFactory.getLogger(MobilePosLoginServiceImpl.class);

	@Autowired
	private MobilePosTsCzykMapper tsCzykMapper;
	@Autowired
	private MobilePosTsJtsdkMapper tsJtsdkMapper;
	@Autowired
	private MobilePosTqJtztkMapper tqJtztkMapper;
	@Autowired
	private MobilePosTqZdkMapper tqZdkMapper;
	@Autowired
	private MobilePosTqThirdExceptOrderMapper tqThirdExceptOrderMapper;
	@Autowired
	private MobilePosTqYhMtCouponsMapper tqYhMtCouponsMapper;
	@Autowired
	private MobilePosAcewillCouponTempMapper acewillCouponTempMapper;
	
	/**
	 * 登录
	 */
	@Transactional
	public void login(Data data, Data result) {
		Map<String, Object> map = ReqDataUtil.getDataMap(data);

		String czybh = ParamUtil.getStringValue(map, "czybh", false, null);// 操作员编号
		String czymm = ParamUtil.getStringValue(map, "czymm", false, null);// 密码
		String jtbm = ParamUtil.getStringValue(map, "jtbm", false, null);// 机台编码
		Boolean isOpenOrder = false; // 是否需要处理结账结一半的未结帐单

		int bcid = 0;
		String bcmc = "";
		String ygdlcs = "1";
		// 根据用户名密码查询
		TsCzyk czyk = tsCzykMapper.findLogin(czybh, czymm);
		TsJtsdk jtsdk = tsJtsdkMapper.findByIpdz(jtbm);

		Map<String, String> bbrqMap = tqJtztkMapper.findBbrq();
		String bbrq = DateUtil.getNowDateYYDDMM();
		if (null != bbrqMap && bbrqMap.size() != 0) {
			bbrq = bbrqMap.get("bbrq");
		}

		result.setCode(-1);
		result.setMsg(SysDictionary.LOGIN_FAILURE_01);
		result.setSuccess(false);

		int mdyyms = 1;
		String appLogin = SysDictionary.LOGIN_STATE_ON;
		String appLogout = SysDictionary.LOGIN_STATE_OFF;
		TsGgcsk ggcs = tqZdkMapper.getGgcs("MDYYMS");
		if (null != ggcs && "3".equals(ggcs.getSdnr())) {
			mdyyms = 3;
			appLogin = SysDictionary.APP_LOGIN;
			appLogout = SysDictionary.APP_LOGOUT;
		} else {
			mdyyms = 1;
			appLogin = SysDictionary.LOGIN_STATE_ON;
			appLogout = SysDictionary.LOGIN_STATE_OFF;
		}
		
		TqJtztk loginCheck = tqJtztkMapper.getJtztNew(DateUtil.parseDate(bbrq), jtsdk.getJtbh(), appLogin, appLogout);
		Map<String, Object> loginMsg = new HashMap<>();
		
		if (czyk != null && jtsdk != null) {
			if (null != loginCheck && appLogin.equals(loginCheck.getCznr()) && !czybh.equals(loginCheck.getRybh())) {
				result.setCode(-1);
				result.setMsg("员工“" + loginCheck.getRyxm() + "”未登出！");
				result.setSuccess(false);
			} else if (null != loginCheck && appLogin.equals(loginCheck.getCznr()) && czybh.equals(loginCheck.getRybh())) {
				// 已登录的返回当前班次信息
				loginMsg.put("bcid", loginCheck.getYl1());
				loginMsg.put("bcmc", loginCheck.getYl2());
				loginMsg.put("dlcs", loginCheck.getYgdlcs());
				loginMsg.put("czymc", loginCheck.getRyxm());
				loginMsg.put("group_id", czyk.getGroup_id());
				loginMsg.put("czy_id", czyk.getCzy_id());
				loginMsg.put("czybh", czyk.getCzybh());
				if (!loginMsg.isEmpty()) {
					result.setData(Collections.singletonList(GsonUtil.GsonToBean(GsonUtil.GsonString(loginMsg), JSONObject.class)));
				}
				isOpenOrder = true;
				ygdlcs = loginCheck.getYgdlcs();
				bcid = Integer.parseInt(loginCheck.getYl1());
				result.setCode(SysDictionary.LOGIN_AGAIN);
				result.setMsg(SysDictionary.LOGIN_SUCCESS);
				result.setSuccess(true);
			} else {
				int start = tqJtztkMapper.checkOpen(DateUtil.parseDate(bbrq), "KSSY");
				int end = tqJtztkMapper.checkOpen(DateUtil.parseDate(bbrq), "JSSY");
				TqJtztk jtzt = tqJtztkMapper.getJtzt(DateUtil.parseDate(bbrq), jtsdk.getJtbh(), czybh, appLogin, appLogout);
				TqJtztk jtzt1 = tqJtztkMapper.getJtzt(DateUtil.parseDate(bbrq), jtsdk.getJtbh(), czybh, "YYDL", "YYTC");
				if (0 == start || 0 < end) {
					result.setCode(-1);
					result.setMsg("门店未营业，请开店后登录！");
					result.setSuccess(false);
				} else if (null != jtzt && appLogin.equals(jtzt.getCznr())) {
					result.setCode(-1);
					result.setMsg("该操作员未在" + jtzt.getJhid() + "机台登出！");
					result.setSuccess(false);
				} else if (null != jtzt1 && "YYDL".equals(jtzt1.getCznr())) {
					result.setCode(-1);
					result.setMsg("该操作员未在" + jtzt1.getJhid() + "机台登出！");
					result.setSuccess(false);
				} else {
					TqJtztk jtzt99 = tqJtztkMapper.getJtztJh(DateUtil.parseDate(bbrq), "99", "");
					if ((null != jtzt99 && "YYDL".equals(jtzt99.getCznr())) || mdyyms != 3) {
						int maxcs = Integer.parseInt(jtzt99.getYgdlcs());
						if (null == jtzt99.getYl1() && mdyyms == 3) {
							result.setCode(-1);
							result.setMsg("server人员未选择班次！");
							result.setSuccess(false);
						} else {
							if (mdyyms == 3) {
								//落座快餐，需要班结的操作
								maxcs = Integer.parseInt(jtzt99.getYgdlcs());
								bcid = Integer.parseInt(jtzt99.getYl1());
								bcmc = jtzt99.getYl2();
							} else {
								maxcs = tqJtztkMapper.getMaxYgdlcs(DateUtil.parseDate(bbrq), jtsdk.getJtbh()) + 1;
								TsBck bck = tqZdkMapper.getBcid();
								bcid = bck.getId();
								bcmc = bck.getBcmc1();
							}
							
							insertTqJtztk(appLogin, czyk, jtsdk.getJtbh(), bbrq, maxcs, bcid + "", bcmc);// 写入tq_jtztk机台状态库
							loginMsg.put("bcid", bcid + "");
							loginMsg.put("bcmc", bcmc);
							loginMsg.put("dlcs", maxcs + "");
							loginMsg.put("czymc", czyk.getCzymc1());
							loginMsg.put("group_id", czyk.getGroup_id());
							loginMsg.put("czy_id", czyk.getCzy_id());
							loginMsg.put("czybh", czyk.getCzybh());
							isOpenOrder = true;
							ygdlcs = maxcs + "";
							if (!loginMsg.isEmpty()) {
								result.setData(Collections.singletonList(GsonUtil.GsonToBean(GsonUtil.GsonString(loginMsg), JSONObject.class)));
							}
							result.setCode(SysDictionary.SUCCESS);
							result.setMsg(SysDictionary.LOGIN_SUCCESS);
							result.setSuccess(true);
						}
					} else {
						result.setCode(-1);
						result.setMsg("server人员未登录！");
						result.setSuccess(false);
					}
				}
			}
		} else {
			result.setCode(-1);
			result.setMsg("账号或密码错误！");
			result.setSuccess(false);
		}

		if (isOpenOrder) {
			delBill(jtsdk.getJtbh());
		}
	}

	/**
	* 登出
	*/
	@Transactional
	public void logout(Data data, Data result) {
		Map<String, Object> map = ReqDataUtil.getDataMap(data);

		String czybh = ParamUtil.getStringValue(map, "czybh", false, null);// 操作员编号
		String jtbm = ParamUtil.getStringValue(map, "jtbm", false, null);// 机台编码
		String bcid = ParamUtil.getStringValue(map, "bcid", false, null);// 班次id
		String bcmc = ParamUtil.getStringValue(map, "bcmc", false, null);// 班次名称

		String appLogin = SysDictionary.LOGIN_STATE_ON;
		String appLogout = SysDictionary.LOGIN_STATE_OFF;
		TsGgcsk ggcs = tqZdkMapper.getGgcs("MDYYMS");
		if (null != ggcs && "3".equals(ggcs.getSdnr())) {
			appLogin = SysDictionary.APP_LOGIN;
			appLogout = SysDictionary.APP_LOGOUT;
		} else {
			TsBck bck = tqZdkMapper.getBcid();
			bcid = bck.getId() + "";
			bcmc = bck.getBcmc1();
			appLogin = SysDictionary.LOGIN_STATE_ON;
			appLogout = SysDictionary.LOGIN_STATE_OFF;
		}

		// 根据用户名密码查询
		TsCzyk czyk = tsCzykMapper.findByCzybh(czybh);
		TsJtsdk jtsdk = tsJtsdkMapper.findByIpdz(jtbm);

		Map<String, String> bbrqMap = tqJtztkMapper.findBbrq();
		String bbrq = DateUtil.getNowDateYYDDMM();
		if (null != bbrqMap && bbrqMap.size() != 0) {
			bbrq = bbrqMap.get("bbrq");
		}

		result.setCode(-1);
		result.setMsg(SysDictionary.LOGOUT_FAILURE_01);
		result.setSuccess(false);

		if (czyk != null && jtsdk != null) {
			TqJtztk jtzt = tqJtztkMapper.getJtztNew(DateUtil.parseDate(bbrq), jtsdk.getJtbh(), appLogin, appLogout);
			if (null != jtzt && !appLogout.equals(jtzt.getCznr())) {
				// 未结帐单处理
				delBill(jtsdk.getJtbh());
				insertTqJtztk(appLogout, czyk, jtsdk.getJtbh(), bbrq, Integer.parseInt(jtzt.getYgdlcs()), bcid, bcmc);// 写入tq_jtztk机台状态库
				if (null != ggcs && !"3".equals(ggcs.getSdnr())) {
					tsJtsdkMapper.pTjskjqj(DateUtil.parseDate(bbrq), jtsdk.getJtbh());
				}
				result.setCode(SysDictionary.SUCCESS);
				result.setMsg(SysDictionary.LOGOUT_SUCCESS);
				result.setSuccess(true);
			} else {
				result.setMsg("未检测到登录，请强制退出！");
			}
		}
	}

	private void openOrderDispose(String jtbh, String skyh, int bcid, String ygdlcs) {
		TqZdk tqZdks = tqZdkMapper.getZdCount(jtbh);

		if (null != tqZdks) {
			int ktbcid = bcid;
			String kdzdbh = tqZdks.getKdzdbh();

			PayMentVo pm = tqZdkMapper.getPayMentToJtbh(jtbh);
			if (null != pm) {
				tqThirdExceptOrderMapper.updateByRefund(kdzdbh, "8");
				tqYhMtCouponsMapper.updateYhMtCouponsMapperToLogin("8", "n", kdzdbh);
			}

			tqZdkMapper.initZdk(kdzdbh + "", ktbcid, skyh, ygdlcs);
			tqZdkMapper.delFkls(kdzdbh);
			tqZdkMapper.delWdk(kdzdbh + "");
			tqZdkMapper.delTpd(kdzdbh + "", 0, "");
			acewillCouponTempMapper.delTwct(kdzdbh + "");
			acewillCouponTempMapper.delTmi(kdzdbh + "");
			if ("4".equals(InitDataListener.ggcsMap.get("POS_MEMBER_TYPE"))) {
				acewillCouponTempMapper.delDsfls(kdzdbh, "QIMAI");
			} else {
				acewillCouponTempMapper.delDsfls(kdzdbh, "ACEWILL");
			}
			tqZdkMapper.zRtr(kdzdbh);
			tqZdkMapper.findCalcMoney(kdzdbh);
			AccountsOrder ao = tqZdkMapper.accountsOrder(kdzdbh, 0, new BigDecimal(0), 1, "", "", "", "", jtbh, skyh);
			String jzzdbhl = "0";
			if (ao != null && "0".equals(ao.getA())) {
				int jzbcid = bcid;
				jzzdbhl = createBh(jtbh, "TQ_ZDK", "JZZDBH");
				String jzzdbh = jzzdbhl;

				// 更新 tq_zdk tq_wdk tq_fklslsk
				Map<String, String> bbrqMap = tqJtztkMapper.findBbrq();
				String jzbbrq = DateUtil.getNowDateYYDDMM();
				if (null != bbrqMap && bbrqMap.size() != 0) {
					jzbbrq = bbrqMap.get("bbrq");
				}
				Date jzjssj = new Date();
				tqZdkMapper.updateZdk("", "", "", kdzdbh, jzzdbh, DateUtil.parseDate(jzbbrq), jzjssj, 1, jtbh, skyh, "ZDSX_YJ", jzjssj, jzjssj, jzbcid, 1);
				tqZdkMapper.updateWdk(kdzdbh, jzzdbh, jtbh, DateUtil.parseDate(jzbbrq), jzbcid);
				tqZdkMapper.updateFklslsk(kdzdbh, jzzdbh, jzbcid);
			}
		}

	}

	private void insertTqJtztk(String cznr, TsCzyk czyk, String jtbh, String bbrq, int cs, String yl1, String yl2) {

		TqJtztk tqJtztk = new TqJtztk();
		tqJtztk.setJhid(jtbh);
		tqJtztk.setCznr(cznr);
		tqJtztk.setRybh(czyk.getCzybh());
		tqJtztk.setRyxm(czyk.getCzymc1());
		tqJtztk.setCzsj(new Date());
		tqJtztk.setBbrq(DateUtil.parseDate(bbrq));
		tqJtztk.setClbz("0");
		tqJtztk.setYgdlcs(cs + "");
		tqJtztk.setYl1(yl1);
		tqJtztk.setYl2(yl2);

		tqJtztkMapper.insert(tqJtztk);
	}

	public String subBh(String jtbh, long newBh) {

		String bhStr = newBh + "";

		return bhStr.substring(jtbh.length());
	}

	public String createBh(String jtbh, String bmc, String zdmc) {
		TsBmkzk tsBmkzKdzdbh = tqZdkMapper.getBh(bmc, zdmc);
		String oldBh = tsBmkzKdzdbh.getNr();
		String newBh = (Long.parseLong(oldBh) + 1) + "";
		tqZdkMapper.updateBh(newBh, bmc, zdmc);
		int bhLength = 10;
		if ("LSDH".equals(zdmc)) {
			bhLength = 4;
		} else {
			bhLength = 10;
		}
		if (null != tsBmkzKdzdbh.getCdzd() && !"".equals(tsBmkzKdzdbh.getCdzd())) {
			TsGgcsk ggcs = tqZdkMapper.getGgcs(tsBmkzKdzdbh.getCdzd());
			if (null != ggcs.getSdnr() && !"".equals(ggcs.getSdnr())) {
				bhLength = Integer.parseInt(ggcs.getSdnr());
			}
		}

		NumberFormat nf = NumberFormat.getInstance();
		nf.setGroupingUsed(false);
		nf.setMaximumIntegerDigits(bhLength);
		nf.setMinimumIntegerDigits(bhLength);
		newBh = jtbh + nf.format(Integer.parseInt(newBh));
		return newBh;
	}

	/**
	 * 更新登录的班次
	 *
	 * @param data
	 * @param result
	 */
	@Transactional
	public void updateLoginShiftState(Data data, Data result) {
		Map<String, Object> map = ReqDataUtil.getDataMap(data);
		String czybh = ParamUtil.getStringValue(map, "czybh", false, null);// 操作员编号
		String jtbh = ParamUtil.getStringValue(map, "jtbh", false, null);// 机台编码
		String bcid = ParamUtil.getStringValue(map, "bcid", false, null);// 班次id
		String bcmc = ParamUtil.getStringValue(map, "bcmc", false, null);// 班次名称
		String dlcs = ParamUtil.getStringValue(map, "dlcs", false, null);// 登陆次数
		result.setCode(-1);
		result.setSuccess(false);
		tqJtztkMapper.updataLoginShiftState(bcid, bcmc, jtbh, czybh, dlcs, SysDictionary.LOGIN_STATE_ON);
		result.setCode(SysDictionary.SUCCESS);
		result.setSuccess(true);
	}

	public void refundOrder(String jtbh, int bcid, String czybh, String ygdlcs) {
		try {
			TqZdk tqZdk = tqZdkMapper.getZdWj(jtbh);
			PayMentVo pm = tqZdkMapper.getPayMentToJtbh(jtbh);
			if (null != pm) {
				tqThirdExceptOrderMapper.updateByRefund(tqZdk.getKdzdbh(), "8");
				tqYhMtCouponsMapper.updateYhMtCouponsMapperToLogin("8", "n", tqZdk.getKdzdbh());
			}
			if (null != tqZdk) {
				tqZdkMapper.initZdk(tqZdk.getKdzdbh(), bcid, czybh, ygdlcs);
				tqZdkMapper.delWdk(tqZdk.getKdzdbh());
				tqZdkMapper.delFkls(tqZdk.getKdzdbh());
				tqZdkMapper.delTpd(tqZdk.getKdzdbh(), 0, "");
				acewillCouponTempMapper.delTwct(tqZdk.getKdzdbh());
				acewillCouponTempMapper.delTmi(tqZdk.getKdzdbh());
				if ("4".equals(InitDataListener.ggcsMap.get("POS_MEMBER_TYPE"))) {
					acewillCouponTempMapper.delDsfls(tqZdk.getKdzdbh(), "QIMAI");
				} else {
					acewillCouponTempMapper.delDsfls(tqZdk.getKdzdbh(), "ACEWILL");
				}
				
				
				
			}

		} catch (Exception e) {
			LOGGER.error("登录退未结完的付款失败:{}", e);
		}
	}

	/**
	 * 修改密码
	 */
	@Transactional
	@Override
	public void updatePassword(Data data, Data result) {
		Map<String, Object> map = ReqDataUtil.getDataMap(data);
		String czybh = ParamUtil.getStringValue(map, "czybh", false, null);// 操作员编号
		String xczymm = ParamUtil.getStringValue(map, "xczymm", false, null);// 新操作员密码
		String jczymm = ParamUtil.getStringValue(map, "jczymm", false, null);// 旧操作员密码
		LOGGER.info("操作员编号:" + czybh);
		LOGGER.info("新操作员密码:" + xczymm);
		LOGGER.info("旧操作员密码:" + jczymm);
		try {
			TsCzyk czy = tsCzykMapper.findByCzybh(czybh);
			if (null != czy) {
				if (!jczymm.equals(czy.getCzymm())) {
					result.setCode(-1);
					result.setSuccess(false);
					result.setMsg("原密码输入错误!");
				} else {
					int i = tsCzykMapper.updatePassword(czybh, xczymm);
					int j = tsCzykMapper.updatePasswordXf(czybh, xczymm);
					if (i != 0 && j != 0) {
						result.setCode(0);
						result.setSuccess(true);
						result.setMsg("密码修改成功!");
					} else {
						result.setCode(-1);
						result.setSuccess(true);
						result.setMsg("密码修改失败!");
					}
				}
			} else {
				result.setCode(-1);
				result.setSuccess(true);
				result.setMsg("用户不存在!");
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.setCode(-1);
			result.setSuccess(false);
			result.setMsg("系统异常!");
		}
	}
	
	public void delBill(String jtbh) {
		List<TqZdk> tqZdkList = tqZdkMapper.getZdWjList(jtbh);
		try {
			for(TqZdk tqZdk : tqZdkList){
				tqZdkMapper.delZdk(tqZdk.getKdzdbh());
				tqZdkMapper.delWdk(tqZdk.getKdzdbh());
				tqZdkMapper.delFkls(tqZdk.getKdzdbh());
			}

		} catch (Exception e) {
			LOGGER.error("删除异常单失败:{机台号：" + jtbh + "}", e);
		}
	}

}
