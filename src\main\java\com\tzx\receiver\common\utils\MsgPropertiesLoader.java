package com.tzx.receiver.common.utils;

import java.io.IOException;
import java.io.InputStream;
import java.util.NoSuchElementException;
import java.util.Properties;

import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Component;

@Component
public class MsgPropertiesLoader {
	private static Logger			logger			= LoggerFactory.getLogger(MsgPropertiesLoader.class);

	private static ResourceLoader	resourceLoader	= new DefaultResourceLoader();

	private static Properties		properties		= null;

	public static final String[]	resourcesPaths	=
													{ "tzxmqconfig.properties"};

	static{
		
		properties = loadProperties(resourcesPaths);
	}
	
	/**
	 * 取出Property。
	 */
	private static String getValue(String key)
	{
		return properties.getProperty(key);
	}

	/**
	 * 取出String类型的Property,如果都為Null则抛出异常.
	 */
	public static String getProperty(String key)
	{
		String value = getValue(key);
		if (value == null)
		{
			throw new NoSuchElementException();
		}
		return value;
	}
	
	/**
	 * 载入多个文件, 文件路径使用Spring Resource格式.
	 */
	private static Properties loadProperties(String... resourcesPaths)
	{
		Properties props = new Properties();

		for (String location : resourcesPaths)
		{

			logger.debug("Loading properties file from path:{}", location);

			InputStream is = null;
			try
			{
				Resource resource = resourceLoader.getResource(location);
				is = resource.getInputStream();
				props.load(is);
			}
			catch (IOException ex)
			{
				ex.printStackTrace();
				logger.info("Could not load properties from path:{}, {} ", location, ex.getMessage());
			}
			finally
			{
				/*
				try
				{
					is.close();
				}catch (IOException e){
					e.printStackTrace();
				}
				*/
				IOUtils.closeQuietly(is);
			}
		}
		return props;
	}
}
