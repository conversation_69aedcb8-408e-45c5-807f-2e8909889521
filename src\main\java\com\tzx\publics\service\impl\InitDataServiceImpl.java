package com.tzx.publics.service.impl;

import java.io.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tzx.publics.common.BillNoData;
import com.tzx.receiver.common.upload.UpLoadParamsList;
import com.tzx.receiver.common.upload.UploadGloVar;
import com.tzx.receiver.entity.msg.EcoTypeDic;

import net.sf.json.JSONObject;

import org.jdom2.Document;
import org.jdom2.Element;
import org.jdom2.input.SAXBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.tzx.publics.listener.InitDataListener;
import com.tzx.publics.mapper.InitDataMapper;
import com.tzx.publics.service.IInitDataService;
import com.tzx.publics.vo.DeviceVo;
import com.tzx.publics.vo.GgcskVo;
import com.tzx.publics.vo.OrganVo;
import com.tzx.publics.vo.ThirdInfoVO;

@Service
public class InitDataServiceImpl implements IInitDataService {
	private final static Logger LOGGER = LoggerFactory.getLogger(InitDataServiceImpl.class);
	@Autowired
	private InitDataMapper initDataMapper;
	@Autowired
    UpLoadParamsList upLoadParamsList;

	@Transactional
	public Map<String, String> getGgcsMap() {
		Map<String, String> ggcsMap = new HashMap<String, String>();
		List<GgcskVo> ggcsList = initDataMapper.getGgcs();
		for (GgcskVo ggcs : ggcsList) {
			ggcsMap.put(ggcs.getSdbt(), ggcs.getSdnr());
		}
		LOGGER.info("初始化公共参数完成...");
		return ggcsMap;
	}
	
	@Transactional
	public Map<String, String> getThirdMap() {
		Map<String, String> thirdMap = new HashMap<String, String>();
		List<ThirdInfoVO> thirdList = initDataMapper.getThird();
		for (ThirdInfoVO third : thirdList) {
			thirdMap.put(third.getThirdtype() + "_" + third.getThirdcode() , third.getThirdvalue());
		}
		LOGGER.info("初始化ts_third_info完成...");
		return thirdMap;
	}
	
	@Transactional
	public OrganVo getOrganVo() {
		OrganVo organ = initDataMapper.getOrgan();
		LOGGER.info("初始化门店信息完成...");
		return organ;
	}


	@Transactional
	public void refreshInternalStorage() {
		InitDataListener.ggcsMap = this.getGgcsMap();
		InitDataListener.thirdMap = this.getThirdMap();
		InitDataListener.organVo = this.getOrganVo();
		InitDataListener.ecoTypeDicMap = this.getEcoTypeDicMap();
		InitDataListener.deviceMap = this.getDeviceMap();
		InitDataListener.wyObj = this.getWuYeObj();
		
		upLoadParamsList.clearParams();

		OrganVo organTemp = InitDataListener.organVo;
		Map<String, String> ggcsMapTemp = this.getGgcsMap();
		UploadGloVar.setGloVar(organTemp.getJgxh() + "", organTemp.getJgbh(), organTemp.getJgmc1(),
				ggcsMapTemp.get("MQUrl"), ggcsMapTemp.get("MqUser"), ggcsMapTemp.get("MqPassword"),
				ggcsMapTemp.get("Downmq.name"), ggcsMapTemp.get("Upmq.name"));
	}

	@Override
	@Transactional
	public BillNoData getBillNoData(String jtbh, String preChar) throws Exception {
		BillNoData billNoData = new BillNoData();
		LOGGER.info("获取新账单号开始"+ this);
		if(preChar.length() != 1){
			throw  new Exception("前缀必须为1");
		}
		StringBuffer kdzdbh = new StringBuffer(initDataMapper.getbillid(jtbh,"TQ_ZDK","KDZDBH"));
		StringBuffer jzzdbh = new StringBuffer(initDataMapper.getbillid(jtbh,"TQ_ZDK","JZZDBH"));
		StringBuffer lsdh = new StringBuffer(initDataMapper.getbillid(jtbh,"TQ_ZDK","LSDH"));
		StringBuffer qch = null;
		if(!preChar.equals("8")){
			qch = new StringBuffer(initDataMapper.getbillid(jtbh,"TQ_ZDK","QCH"));
		}
		else{
			qch = new StringBuffer("");
		}
		kdzdbh.replace(2,3,preChar);
		jzzdbh.replace(2,3,preChar);
		lsdh.replace(2,3,preChar);
		billNoData.setKdzdbh(kdzdbh.toString());
		billNoData.setJzzdbh(jzzdbh.toString());
		billNoData.setLsdh(lsdh.toString());
		billNoData.setQch(qch.toString());
		LOGGER.info("获取新账单号成功" + billNoData.toString());
		return  billNoData;
	}

	@Transactional
	public Map<String, EcoTypeDic> getEcoTypeDicMap() {
		Map<String, EcoTypeDic> ecoTypeDicMap = new HashMap<String, EcoTypeDic>();
		List<EcoTypeDic> ecoTypeDics = initDataMapper.getEcoTypeDic();
		for (EcoTypeDic ecoTypeDic : ecoTypeDics) {
			ecoTypeDicMap.put(ecoTypeDic.getThirdcode(), ecoTypeDic);
		}
		return ecoTypeDicMap;
	}
	
	@Transactional
	public void initMq() {
		// D:\Program Files\TZXPOS\TzxMsgReceive\tzxcomwin\conf\activemq.xml
	    File file = new File(System.getProperty("user.dir"));
	    String path = file.getParent() + File.separator + "TzxMsgReceive" + File.separator + "tzxcomwin" + File.separator + "conf" + File.separator + "activemq.xml";
		try {
			readStringXmlOut(path);
			
			GgcskVo ggcsUser = initDataMapper.getGgcsBySdbt("MqUser");
			if(null == ggcsUser){
				ggcsUser = new GgcskVo();
				ggcsUser.setSdbt("MqUser");
				ggcsUser.setSdnr("");
				ggcsUser.setSyfw("总部MQ用户名");
				ggcsUser.setBzsm("总部MQ用户名");
				ggcsUser.setJhid("MQUser");
				initDataMapper.insertGgcs(ggcsUser);
				LOGGER.info("TS_GGCSK写入mq相关参数:MqUser已写入...");
			} else {
				LOGGER.info("TS_GGCSK写入mq相关参数:MqUser已存在，跳过...");
			}
			GgcskVo ggcsPassword = initDataMapper.getGgcsBySdbt("MqPassword");
			if(null == ggcsPassword){
				ggcsPassword = new GgcskVo();
				ggcsPassword.setSdbt("MqPassword");
				ggcsPassword.setSdnr("");
				ggcsPassword.setSyfw("总部MQ密码");
				ggcsPassword.setBzsm("总部MQ密码");
				ggcsPassword.setJhid("MQUser");
				initDataMapper.insertGgcs(ggcsPassword);
				LOGGER.info("TS_GGCSK写入mq相关参数:MqPassword已写入...");
			} else {
				LOGGER.info("TS_GGCSK写入mq相关参数:MqPassword已存在，跳过...");
			}
		} catch (Exception e) {
			LOGGER.error("TS_GGCSK写入mq相关参数失败", e);
			e.printStackTrace();
		}
	}
	
	public void readStringXmlOut(String path) throws Exception {
		SAXBuilder sb = new SAXBuilder();
		Document doc = (Document) sb.build(new File(path));
		Element root = doc.getRootElement();
		parse(root);
	}

	public void parse(Element root) {
		List<Element> nodes = root.getChildren();
		int len = nodes.size();
		if (len != 0) {
			for (int i = 0; i < len; i++) {
				Element element = (Element) nodes.get(i);// 循环依次得到子元素
				if ("inboundQueueBridge".equals(element.getName())) {
					if (null != element.getAttributeValue("inboundQueueName")) {
						// 下发队列
						GgcskVo ggcs = initDataMapper.getGgcsBySdbt("Downmq.name");
						if(null == ggcs){
							ggcs = new GgcskVo();
							ggcs.setSdbt("Downmq.name");
							ggcs.setSdnr(element.getAttributeValue("inboundQueueName"));
							ggcs.setSyfw("总部MQ下发队列名称");
							ggcs.setBzsm("总部MQ下发队列名称");
							ggcs.setJhid("MQUser");
							initDataMapper.insertGgcs(ggcs);
							LOGGER.info("TS_GGCSK写入mq相关参数:Downmq.name已写入...");
						} else {
							LOGGER.info("TS_GGCSK写入mq相关参数:Downmq.name已存在，跳过...");
						}
					}
				}
				if ("outboundQueueBridge".equals(element.getName())) {
					if (null != element.getAttributeValue("outboundQueueName")) {
						// 上传队列
						GgcskVo ggcs = initDataMapper.getGgcsBySdbt("Upmq.name");
						if(null == ggcs){
							ggcs = new GgcskVo();
							ggcs.setSdbt("Upmq.name");
							ggcs.setSdnr(element.getAttributeValue("outboundQueueName"));
							ggcs.setSyfw("总部MQ上传队列名称");
							ggcs.setBzsm("总部MQ上传队列名称");
							ggcs.setJhid("MQUser");
							initDataMapper.insertGgcs(ggcs);
							LOGGER.info("TS_GGCSK写入mq相关参数:Upmq.name已写入...");
						} else {
							LOGGER.info("TS_GGCSK写入mq相关参数:Upmq.name已存在，跳过...");
						}
					}
				}
				if ("property".equals(element.getName())) {
					if (null != element.getAttributeValue("name") && null != element.getAttributeValue("value") && "brokerURL".equals(element.getAttributeValue("name"))) {
						// 总部mq地址
						GgcskVo ggcs = initDataMapper.getGgcsBySdbt("MQUrl");
						if (null == ggcs) {
							ggcs = new GgcskVo();
							ggcs.setSdbt("MQUrl");
							ggcs.setSdnr(element.getAttributeValue("value"));
							ggcs.setSyfw("总部MQ地址");
							ggcs.setBzsm("总部MQ地址");
							ggcs.setJhid("MQUser");
							initDataMapper.insertGgcs(ggcs);
							LOGGER.info("TS_GGCSK写入mq相关参数:MQUrl已写入...");
						} else {
							LOGGER.info("TS_GGCSK写入mq相关参数:MQUrl已存在，跳过...");
						}
					}
				}
				parse(element);
			}
		}
	}
	
	@Transactional
	public Map<String, DeviceVo> getDeviceMap() {
		Map<String, DeviceVo> deviceMap = new HashMap<String, DeviceVo>();
		List<DeviceVo> deviceList = initDataMapper.getDevice();
		for (DeviceVo device : deviceList) {
			deviceMap.put(device.getJtbh(), device);
		}
		LOGGER.info("初始化机台参数完成...");
		return deviceMap;
	}
	
	@Transactional
	public JSONObject getWuYeObj() {
		JSONObject wyObj = new JSONObject();
		try {
			File file = new File(System.getProperty("user.dir"));
			String path = file.getParent() + File.separator + "Data" + File.separator + "wuyeconfig.json";
			String wyStr = fileRead(path);
			wyObj = JSONObject.fromObject(wyStr);
			LOGGER.info("初始化wuyeconfig成功...");
		} catch (Exception e) {
			LOGGER.info("初始化wuyeconfig失败...");
		}

		return wyObj;
	}
	
	public String fileRead(String path) throws Exception {
		File file = new File(path);
//		FileReader reader = new FileReader(file);
//		BufferedReader bReader = new BufferedReader(reader);
		BufferedReader bReader = new BufferedReader(new InputStreamReader(new FileInputStream(file), "UTF-8"));
		StringBuilder sb = new StringBuilder();
		String s = "";
		while ((s = bReader.readLine()) != null) {
			sb.append(s);
		}
		bReader.close();
		String str = sb.toString();
		return str;
	}
	
}
