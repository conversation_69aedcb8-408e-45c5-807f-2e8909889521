package com.tzx.receiver.service;

import com.tzx.publics.common.Version;
import com.tzx.receiver.common.utils.DBUtils;
import com.tzx.receiver.entity.MessageBody;
import com.tzx.receiver.entity.UploadLog;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2019-05-10
 * @Descption
 **/
@Service
public class ReceiverManagerService {
    public MessageBody uploadloglist(){
        MessageBody ret = new MessageBody();
        List<UploadLog> data = DBUtils.getUploadLogList();
        if(data==null){
            ret.setSuccess(false);
        }else{
            ret.setSuccess(true);
            ret.setData(data);
        }
        return ret;

    }
    public MessageBody version(){
        MessageBody ret = new MessageBody();
        Map<String,String> map = new HashMap<>();
        map.put("MINIAPP_VERSION",Version.MINIAPP_VERSION);
        map.put("MINIAPP_VERSION_LOG",Version.MINIAPP_VERSION_LOG);
        map.put("MOBILEPOS_VERSION",Version.MOBILEPOS_VERSION);
        map.put("MOBILEPOS_VERSION_LOG",Version.MOBILEPOS_VERSION_LOG);
        map.put("RECEVIER_VERSION",Version.RECEVIER_VERSION);
        map.put("RECEVIER_VERSION_LOG",Version.RECEVIER_VERSION_LOG);
        map.put("BASE_VERSION",Version.BASE_VERSION);
        map.put("BASE_VERSION_LOG",Version.BASE_VERSION_LOG);
        map.put("ECO_VERSION",Version.ECO_VERSION);
        map.put("ECO_VERSION_LOG",Version.ECO_VERSION_LOG);
        map.put("COMMAPI_VERSION",Version.COMMAPI_VERSION);
        map.put("COMMAPI_VERSION_LOG",Version.COMMAPI_VERSION_LOG);
        ret.setData(map);
        ret.setSuccess(true);
        return ret;

    }
}
