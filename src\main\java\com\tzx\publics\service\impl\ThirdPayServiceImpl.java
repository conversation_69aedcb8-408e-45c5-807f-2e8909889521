package com.tzx.publics.service.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.tzx.publics.listener.InitDataListener;
import com.tzx.publics.mapper.ThirdPayMapper;
import com.tzx.publics.service.IThirdPayService;
import com.tzx.publics.util.GsonUtil;
import com.tzx.publics.vo.FkfssdkVo;

@Service
public class ThirdPayServiceImpl implements IThirdPayService {
	private final static Logger LOGGER = LoggerFactory.getLogger(ThirdPayServiceImpl.class);
	@Autowired
	private ThirdPayMapper thirdPayMapper;

	@Override
	public FkfssdkVo getFkfsByNum(String dynamicid) {
		LOGGER.info("获取第三方支付方式-支付码={}", dynamicid);
		FkfssdkVo fkfs = new FkfssdkVo();

		String ggcsWx = InitDataListener.ggcsMap.get("ERP_FKFS_WX"); // 微信支付标识
		String ggcsZfb = InitDataListener.ggcsMap.get("ERP_FKFS_ZFB"); // 支付宝支付标识
		String ggcsXnk = InitDataListener.ggcsMap.get("ERP_FKFS_YSZXXNK"); // 虚拟卡支付标识
		String ggcsYsf = InitDataListener.ggcsMap.get("ERP_FKFS_YL"); // 云闪付支付标识
		String ggcsZs = InitDataListener.ggcsMap.get("CMB_YSZXXNK_RULE"); // 招商支付标识
		
		String wx = "";
		String zfb = "";
		String xnk = "";
		String ysf = "";
		String zs = "";

		if (null != ggcsWx && !"".equals(ggcsWx)) {
			wx = ggcsWx;
		}
		if (null != ggcsZfb && !"".equals(ggcsZfb)) {
			zfb = ggcsZfb;
		}
		if (null != ggcsXnk && !"".equals(ggcsXnk)) {
			xnk = ggcsXnk;
		}
		if (null != ggcsYsf && !"".equals(ggcsYsf)) {
			ysf = ggcsYsf;
		}
		if (null != ggcsZs && !"".equals(ggcsZs)) {
			zs = ggcsZs;
		}
		LOGGER.info("获取第三方支付方式-区分标识-微信={},\t 支付宝={},\t 虚拟卡={},\t 云闪付={},\t 招商卡={}", wx, zfb, xnk, ysf, zs);
		
		fkfs.setId(-1);
		String dynamicids = dynamicid.substring(0, 2);
		if (wx.indexOf(dynamicids) > -1) {
			fkfs = thirdPayMapper.getFkfsid("ERP_FKFS_WX");
		} else if (zfb.indexOf(dynamicids) > -1) {
			fkfs = thirdPayMapper.getFkfsid("ERP_FKFS_ZFB");
		} else if (xnk.indexOf(dynamicids) > -1) {
			fkfs = thirdPayMapper.getFkfsid("ERP_FKFS_YSZXXNK");
		} else if (ysf.indexOf(dynamicids) > -1) {
			boolean ifYl = true;
			String[] zsArray = zs.split(",");
			for (int i = 0; i < zsArray.length; i++) {
				String zsStr = zsArray[i];
				if (zsStr.length() <= 0) {
					continue;
				}
				String dynamicidsZs = dynamicid.substring(0, zsStr.length());
				if (zsStr.equals(dynamicidsZs)) {
					fkfs = thirdPayMapper.getFkfsid("ERP_FKFS_YSZXXNK");
					ifYl = false;
					break;
				}
			}
			if (ifYl) {
				fkfs = thirdPayMapper.getFkfsid("ERP_FKFS_YL");
			}
		}
		LOGGER.info("获取第三方支付方式-fkfs={}", GsonUtil.GsonString(fkfs));
		if (null == fkfs) {
			fkfs = new FkfssdkVo();
			fkfs.setId(-1);
		}

		return fkfs;
	}

}
