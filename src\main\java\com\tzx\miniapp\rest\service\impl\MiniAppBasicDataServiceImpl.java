package com.tzx.miniapp.rest.service.impl;

import com.tzx.miniapp.common.Constant;
import com.tzx.miniapp.common.Data;
import com.tzx.miniapp.common.enums.BasicDataMnum;
import com.tzx.miniapp.rest.mapper.*;
import com.tzx.miniapp.rest.service.IMiniAppBasicDataService;
import com.tzx.miniapp.rest.vo.*;
import com.tzx.publics.util.GsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class MiniAppBasicDataServiceImpl implements IMiniAppBasicDataService {

	private final static Logger LOGGER = LoggerFactory.getLogger(MiniAppBasicDataServiceImpl.class);

	@Autowired
	private MiniAppTqClsdkMapper tqClsdkMapper;
	@Autowired
	private MiniAppTsCmkMapper tsCmkMapper;
	@Autowired
	private MiniAppTsTcmxkMapper tsTcmxkMapper;
	@Autowired
	private MiniAppTsTcfzmxkMapper tsTcfzmxkMapper;
	@Autowired
	private MiniAppTsFkfssdkMapper tsFkfssdkMapper;
	@Autowired
	private MiniAppTsKwsdkMapper tsKwsdkMapper;
	

	/**
	 * 数据同步
	 * 
	 * @param param
	 * @param result
	 * @return
	 */
	public String findBasicData(Data param, Data result) {
//		Map<String, Object> paramMap = ReqDataUtil.getDataMap(param);
//		String dataType = ParamUtil.getStringValue(paramMap, "data_type", false, null);

		List<BasicDataBack> bdList = new ArrayList<BasicDataBack>();
		BasicDataBack basicDataBack = new BasicDataBack();
		BasicDataMnum dataMnum = null;
//		dataMnum = BasicDataMnum.valueOf(dataType.toUpperCase());
		switch (dataMnum) {

		case DISH:
			 basicDataBack.setDISH(getDishList("CPYSMXLB_CPXM"));
			break;
		case ITEM_CLASS:
			basicDataBack.setITEM_CLASS(getItemClassList());
			break;
		case COMBO_DETAILS:
			basicDataBack.setCOMBO_DETAILS(getTsTcmxkList());
			break;
		case COMBO_GROUP:
			basicDataBack.setCOMBO_GROUP(getTsTcfzmxkList());
			break;
		case PAYMENT_WAY:
			basicDataBack.setPAYMENT_WAY(getTsFkfssdkList());
			break;
		case DISCOUNT:
			 basicDataBack.setDISCOUNT(getDishList("CPYSMXLB_YHFS"));
			break;
		case TASTE:
			 basicDataBack.setTASTE(getTasteList());
			break;
			
		default:
			break;
		}

		bdList.add(basicDataBack);
//		result.setCode(Constant.CODE_SUCCESS);
		result.setMsg(Constant.BASICDATA_SUCCESS);
//		result.setSuccess(true);
		GsonUtil.asyncResponseData(result, bdList);
		return GsonUtil.getResponseString();
	}

	/**
	 * 查询菜品
	 * 
	 * @return
	 */
	@Transactional
	public List<Dish> getDishList(String clmxlb) {
		List<Dish> list = tsCmkMapper.findDishBasicData(clmxlb);
		//暂时不支持有项目组的套餐，所以这里做处理，凡是有项目组的套餐都不同步给app
//		Iterator<Dish> it = list.iterator();
//		of:while (it.hasNext()) {
//			Dish dish = it.next();
//			if ("CMSX_TC".equals(dish.getIs_combo())) {
//				List<TsTcmxk> tsTcmxk = tsCmkMapper.findTcmxByXmid(Integer.parseInt(dish.getItem_id()));
//				tf:for(TsTcmxk tcmx : tsTcmxk){
//					if("ERP_MXLX_GROUP".equals(tcmx.getMxlx())){
//						it.remove();
//						break tf;
//					}
//				}
//			}
//		}
//		
		return list;
	}

	/**
	 * 查询菜类
	 * 
	 * @return
	 */
	@Transactional
	public List<ItemClass> getItemClassList() {
		List<ItemClass> list = tqClsdkMapper.findItemClassBasicData();
		return list;
	}
	
	/**
	 * 查询套餐
	 * 
	 * @return
	 */
	@Transactional
	public List<ComboDetails> getTsTcmxkList() {
		List<ComboDetails> list = tsTcmxkMapper.findTsTcmxkBasicData();
		return list;
	}
	
	/**
	 * 查询套餐分组
	 * 
	 * @return
	 */
	@Transactional
	public List<ComboGroup> getTsTcfzmxkList() {
		List<ComboGroup> list = tsTcfzmxkMapper.findTsTcfzmxkBasicData();
		return list;
	}
	
	/**
	 * 查询付款方式
	 * 
	 * @return
	 */
	@Transactional
	public List<PaymentWay> getTsFkfssdkList() {
		List<PaymentWay> list = tsFkfssdkMapper.findTsFkfssdkBasicData();
		return list;
	}
	
	/**
	 * 查询口味备注
	 * 
	 * @return
	 */
	@Transactional
	public List<Taste> getTasteList() {
		List<Taste> list = tsKwsdkMapper.findTsKwsdkBasicData();
		return list;
	}
	
}
