package com.tzx.mobilepos.rest.service.impl;

import com.tzx.mobilepos.common.Constant;
import com.tzx.mobilepos.common.Data;
import com.tzx.mobilepos.rest.mapper.MobilePosTqZdkMapper;
import com.tzx.mobilepos.rest.mapper.MobilePosTsJtsdkMapper;
import com.tzx.mobilepos.rest.model.TsGgcsk;
import com.tzx.mobilepos.rest.model.TsJtsdk;
import com.tzx.mobilepos.rest.service.IMobilePosRegisterService;
import com.tzx.publics.util.GsonUtil;
import com.tzx.publics.util.ParamUtil;
import com.tzx.publics.util.ReqDataUtil;
import com.tzx.publics.util.SystemException;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class MobilePosRegisterServiceImpl implements IMobilePosRegisterService {
	private final static Logger LOGGER = LoggerFactory.getLogger(MobilePosRegisterServiceImpl.class);

	@Autowired
	private MobilePosTsJtsdkMapper tsJtsdkMapper;
	@Autowired
	private MobilePosTqZdkMapper tqZdkMapper;

	/**
	 * 注册
	 */
	@Transactional
	public void register(Data data, Data result) throws SystemException {
		Map<String, Object> map = ReqDataUtil.getDataMap(data);
		List<JSONObject> dataList = new ArrayList<JSONObject>();

		String ipdz = ParamUtil.getStringValue(map, "ipdz", false, null);// ip地址
		String jtbm = ParamUtil.getStringValue(map, "jtbm", false, null);// 机台编码
		String jtsx = ParamUtil.getStringValue(map, "jtsx", false, null);// 机台属性

		TsJtsdk jtsdk = tsJtsdkMapper.findByIpdz(jtbm);
		TsGgcsk ggcsk = tqZdkMapper.getGgcs("FDJGXH");
		TsGgcsk rif_server = tqZdkMapper.getGgcs("RIFSERVER");
		JSONObject temp = new JSONObject();
		
		if (null != jtsdk) {
			tsJtsdkMapper.updataIpdz(ipdz, jtbm);
			temp = JSONObject.fromObject(GsonUtil.GsonString(jtsdk));
			
			result.setCode(Constant.CODE_PARAM_FAILURE);
			result.setMsg(Constant.REGISTER_YES);
		} else {
			int jtbhNew = 51;
			TsJtsdk jtbhOld = tsJtsdkMapper.getMaxJtbh(jtsx);
			if (null != jtbhOld) {
				jtbhNew = Integer.parseInt(jtbhOld.getJtbh()) + 1;
			}
			TsJtsdk entity = new TsJtsdk();
			entity.setJtbh(jtbhNew + "");
			entity.setIpdz(ipdz);
			entity.setJtbm(jtbm);
			entity.setJtsx(jtsx);
			tsJtsdkMapper.insert(entity);
			temp = JSONObject.fromObject(GsonUtil.GsonString(entity));
			
			result.setCode(Constant.CODE_SUCCESS);
			result.setMsg(Constant.REGISTER_SUCCESS);
		}
		
		if (null != ggcsk) {
			temp.put("shopId", ggcsk.getSdnr());
		}
		if (null != rif_server) {
			temp.put("rif_server", rif_server.getSdnr());
		} else {
			temp.put("rif_server", "");
		}
		dataList.add(temp);
		
		result.setData(dataList);
		result.setSuccess(true);

	}

}
