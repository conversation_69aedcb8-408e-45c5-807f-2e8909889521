package com.tzx.miniapp.rest.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 配料信息表
 * @TableName erp_ingredient_info
 */
public class ErpIngredientInfo implements Serializable {
    /**
     * 主键ID
     */
    private Object id;

    /**
     * 配料组名称
     */
    private String ingredientName;

    /**
     * 配料组编码
     */
    private String ingredientCode;

    /**
     * 操作人
     */
    private String optName;

    /**
     * 最后操作时间
     */
    private Date lastUploadTime;

    /**
     * 状态 Y:启用，N:停用 (逻辑删除)
     */
    private String status;

    /**
     * 配料组排序
     */
    private Object ingredientSort;

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    public Object getId() {
        return id;
    }

    /**
     * 主键ID
     */
    public void setId(Object id) {
        this.id = id;
    }

    /**
     * 配料组名称
     */
    public String getIngredientName() {
        return ingredientName;
    }

    /**
     * 配料组名称
     */
    public void setIngredientName(String ingredientName) {
        this.ingredientName = ingredientName;
    }

    /**
     * 配料组编码
     */
    public String getIngredientCode() {
        return ingredientCode;
    }

    /**
     * 配料组编码
     */
    public void setIngredientCode(String ingredientCode) {
        this.ingredientCode = ingredientCode;
    }

    /**
     * 操作人
     */
    public String getOptName() {
        return optName;
    }

    /**
     * 操作人
     */
    public void setOptName(String optName) {
        this.optName = optName;
    }

    /**
     * 最后操作时间
     */
    public Date getLastUploadTime() {
        return lastUploadTime;
    }

    /**
     * 最后操作时间
     */
    public void setLastUploadTime(Date lastUploadTime) {
        this.lastUploadTime = lastUploadTime;
    }

    /**
     * 状态 Y:启用，N:停用 (逻辑删除)
     */
    public String getStatus() {
        return status;
    }

    /**
     * 状态 Y:启用，N:停用 (逻辑删除)
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * 配料组排序
     */
    public Object getIngredientSort() {
        return ingredientSort;
    }

    /**
     * 配料组排序
     */
    public void setIngredientSort(Object ingredientSort) {
        this.ingredientSort = ingredientSort;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ErpIngredientInfo other = (ErpIngredientInfo) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getIngredientName() == null ? other.getIngredientName() == null : this.getIngredientName().equals(other.getIngredientName()))
            && (this.getIngredientCode() == null ? other.getIngredientCode() == null : this.getIngredientCode().equals(other.getIngredientCode()))
            && (this.getOptName() == null ? other.getOptName() == null : this.getOptName().equals(other.getOptName()))
            && (this.getLastUploadTime() == null ? other.getLastUploadTime() == null : this.getLastUploadTime().equals(other.getLastUploadTime()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getIngredientSort() == null ? other.getIngredientSort() == null : this.getIngredientSort().equals(other.getIngredientSort()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getIngredientName() == null) ? 0 : getIngredientName().hashCode());
        result = prime * result + ((getIngredientCode() == null) ? 0 : getIngredientCode().hashCode());
        result = prime * result + ((getOptName() == null) ? 0 : getOptName().hashCode());
        result = prime * result + ((getLastUploadTime() == null) ? 0 : getLastUploadTime().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getIngredientSort() == null) ? 0 : getIngredientSort().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", ingredientName=").append(ingredientName);
        sb.append(", ingredientCode=").append(ingredientCode);
        sb.append(", optName=").append(optName);
        sb.append(", lastUploadTime=").append(lastUploadTime);
        sb.append(", status=").append(status);
        sb.append(", ingredientSort=").append(ingredientSort);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}