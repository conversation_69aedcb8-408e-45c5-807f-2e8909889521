package com.tzx.miniapp.rest.service.impl;

import java.util.List;
import java.util.concurrent.Executor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.tzx.miniapp.common.Data;
import com.tzx.miniapp.common.SysDictionary;
import com.tzx.miniapp.rest.mapper.MiniAppClearDishMapper;
import com.tzx.miniapp.rest.mapper.MiniAppFirstPayMapper;
import com.tzx.miniapp.rest.service.IMiniAppQmCancelBillService;
import com.tzx.miniapp.rest.vo.ClearDishQm;
import com.tzx.publics.listener.InitDataListener;
import com.tzx.publics.util.Const;
import com.tzx.publics.util.Util;
import com.tzx.publics.vo.OrganVo;

import net.sf.json.JSONObject;

@Service
public class MiniAppQmCancelBillServiceImpl implements IMiniAppQmCancelBillService {
	private final static Logger LOGGER = LoggerFactory.getLogger(MiniAppFirstPayServiceImpl.class);

	@Autowired
	private MiniAppFirstPayMapper firstPayMapper;
	
	@Autowired
	private MiniAppClearDishMapper clearDishMapper;
	
	@Autowired
    Executor executor;

	@Transactional
	public Data posCancelBill(JSONObject obj) {
		Data data = new Data();
		data.setSuccess(1);
		data.setMsg("退单失败");
		try {
			String retundApi = "/api/order/refundOrder";
            
            String url = InitDataListener.thirdMap.get("QIMAI_QIMAI_URL");
    		String openKey = InitDataListener.thirdMap.get("QIMAI_OPENKEY");
    		String callbackShopName = InitDataListener.thirdMap.get("QIMAI_CALLBACK_SHOP_NAME");
    		
			String apiRefund = url + retundApi;
			String tsyddbh = obj.optString("yddbh");
			String yddh = tsyddbh.replaceFirst("TS", "");

			if (openKey == null) {
				data.setMsg("商户密钥id未配置！退款失败！");
				return data;
			}

			if (url == null) {
				data.setMsg("企迈退款接口地址未配置！退款失败！");
				return data;
			}
			String sign = Util.miniappSign(openKey, yddh);
			String result = Util.refundQm(apiRefund, callbackShopName, yddh, sign);
			if (Const.CONNECT_ERROR.equals(result)) {
				data.setMsg("连接错误，退款失败！");
			} else if (Const.CONNECT_TIMEOUT.equals(result)) {
				data.setMsg("请求第三方超时，退款失败！");
			} else {
				LOGGER.info("企迈退款返回：" + result);
				JSONObject resultJson = JSONObject.fromObject(result);
				int code = resultJson.optInt("code", 1);
				String message = resultJson.optString("message", "");
				if (code == 0) {
					firstPayMapper.updateBtYddToCancel(tsyddbh, "7");
					data.setSuccess(0);
					data.setMsg("企迈小程序退款成功！");
				} else {
					data.setMsg("企迈小程序退款失败: " + message);
				}
			}

			return data;
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			data.setSuccess(1);
			data.setMsg("系统错误");
			return data;
		}
	}
	
	
	@Transactional
	public Data posSoldOut(JSONObject obj) {
		Data data = new Data();
		data.setSuccess(1);
		data.setMsg("同步沽清信息失败");
		try {
			String soldOutApi = "/api/sync/stock";
			String url = InitDataListener.thirdMap.get("QIMAI_QIMAI_URL");
			String callbackShopName = InitDataListener.thirdMap.get("QIMAI_CALLBACK_SHOP_NAME");
			OrganVo organVo = InitDataListener.organVo;
			String shopId = organVo.getJgbh(); // 实际为编号

			final String notificationDish = url + soldOutApi;
			final String callbackShopName_ = callbackShopName;
			final String shopId_ = shopId;
			
			List<ClearDishQm> cleardishlist = clearDishMapper.findClearDishQm();
			
			final List<ClearDishQm> cleardishlist_ = cleardishlist;
			
//			executor.execute(new Runnable() {
//				@Override
//				public void run() {
//					qmRequestSoldOutApi(notificationDish, callbackShopName_, shopId_, cleardishlist_);
//				}
//			});

			String result = qmRequestSoldOutApi(notificationDish, callbackShopName_, shopId_, cleardishlist_);

			if (Const.CONNECT_ERROR.equals(result)) {
				data.setMsg("连接错误，沽清同步失败！");
			} else if (Const.CONNECT_TIMEOUT.equals(result)) {
				data.setMsg("请求第三方超时，沽清同步失败！");
			} else {
				JSONObject resultJson = JSONObject.fromObject(result);
				int code = resultJson.optInt("code", 1);
				String message = resultJson.optString("msg", "");
				if (code == 0) {
					data.setSuccess(SysDictionary.SUCCESS);
					data.setMsg("同步沽清信息成功！");
				} else {
					data.setMsg("同步沽清信息失败 : " + message);
				}
			}

			return data;
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			data.setSuccess(1);
			data.setMsg("系统错误");
			return data;
		}
	}

	public String qmRequestSoldOutApi(String url, String callbackShopName, String shopcode, List<ClearDishQm> cleardishlist) {
		LOGGER.info("商户标识:" + callbackShopName + " 门店编号:" + shopcode);
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("callbackShopName", callbackShopName);
		jsonObject.put("shopcode", shopcode);
		jsonObject.put("goodsList", cleardishlist);
		LOGGER.info("url:" + url + "  request  " + jsonObject.toString());
		String resp = Util.jsonPostRequest(url, jsonObject.toString());
		LOGGER.info("url:" + url + "  response  " + resp);

		return resp;
	}
}
