package com.tzx.miniapp.rest.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.tzx.miniapp.common.Data;
import com.tzx.miniapp.rest.mapper.MiniAppShopBaseInfoMapper;
import com.tzx.miniapp.rest.service.IMiniAppShopBaseInfoService;
import com.tzx.miniapp.rest.vo.ComboItems;
import com.tzx.miniapp.rest.vo.Dish;
import com.tzx.miniapp.rest.vo.DishKinds;
import com.tzx.miniapp.rest.vo.ItemInfoSpec;
import com.tzx.miniapp.rest.vo.ItemTaste;
import com.tzx.miniapp.rest.vo.ItemTasteGroup;
import com.tzx.miniapp.rest.vo.MainDish;
import com.tzx.miniapp.rest.vo.Mandatory;
import com.tzx.miniapp.rest.vo.Marketing;
import com.tzx.miniapp.rest.vo.Norms;
import com.tzx.miniapp.rest.vo.Optional;
import com.tzx.miniapp.rest.vo.Setmeals;
import com.tzx.miniapp.rest.vo.Shops;
import com.tzx.miniapp.rest.vo.Taste;
import com.tzx.miniapp.rest.vo.Toppings;
import com.tzx.publics.listener.InitDataListener;
import com.tzx.publics.util.DateUtil;
import com.tzx.publics.util.PropertiesUtil;
import com.tzx.publics.util.StringUtil;
import com.tzx.receiver.common.utils.DBUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

@Service
public class MiniAppShopBaseInfoServiceImpl implements IMiniAppShopBaseInfoService {
	private final static Logger LOGGER = LoggerFactory.getLogger(MiniAppShopBaseInfoServiceImpl.class);


	private static final String DICTIONARY_KEY = "dictionary";
	private static final String SPEC_KEY = "spec";
	private static final String DISH_CATEGORY_KEY = "reportcategory";
	private static final String COMBO_CATEGORY_KEY = "packcategory";
	private static final String PRACTICE_KEY = "practice";
	private static final String DISHES_KEY = "dishes";
	private static final String MENU_KEY = "menu";

	private static Map<String,Integer> ompIdMap=new HashMap<>();

	private Lock syncLock=new ReentrantLock();
	private static final Map<String, String> wlifeBaseInfoMap=new ConcurrentHashMap<>();
	public static final String wlifeBaseInfoKey="wlifeBaseInfo";

	@Autowired
	private MiniAppShopBaseInfoMapper shopBaseInfoMapper;

	/**
	 * 同步门店基础数据
	 */
	public String shopBaseInfo() {
		LOGGER.info("开始准备同步数据...");
        // 创建返回数据对象
        Data data = new Data();
        String enableOMP = DBUtils.getGGCSK("WlifeMiniEnableOMP"); ;
        if("1".equals(enableOMP)) {
            LOGGER.info("已启用OMP，不进行数据同步!");
            return JSONObject.toJSONString(data);
        }

		syncLock.lock();

		try {

			if(wlifeBaseInfoMap.containsKey(wlifeBaseInfoKey)) {
				LOGGER.info("同步数据(cache)...");
				return wlifeBaseInfoMap.get(wlifeBaseInfoKey);
			}

			// 创建业务数据对象
			Map<String, Object> sbiv = new HashMap<String, Object>();

			// **************************************************
			// 第一步：处理菜品口味备注数据
			// **************************************************
			LOGGER.info("处理菜品口味备注数据...");
			// 菜品口味备注
			Map<String, List<Taste>> kinds_memo = new HashMap<String, List<Taste>>();
			// 口味备注分类
			List<DishKinds> kindsList = shopBaseInfoMapper.findDishKindsData("TS");
			// 口味备注
			List<Taste> tastelist = shopBaseInfoMapper.findTasteData();
			// 分类存储口味备注
			// {"kinds_memo":{"12":[{"ordermemo":"中辣","omid":16,"pkid":12},{"ordermemo":"一碗微辣","omid":40,"pkid":12}],"15":[{"ordermemo":"两碗不辣","omid":44,"pkid":15}]}}
			for (DishKinds dishKinds : kindsList) {
				List<Taste> tastelist1 = new ArrayList<Taste>();
				for (Taste taste : tastelist) {
					if (taste.getPkid() == dishKinds.getId()) {
						tastelist1.add(taste);
					}
				}
				if (tastelist1.size() > 0) {
					kinds_memo.put(dishKinds.getId() + "", tastelist1);
				}
			}
			sbiv.put("kinds_memo", kinds_memo);

			// **************************************************
			// 第二部分：处理桌台数据
			// **************************************************
			LOGGER.info("处理桌台数据...");
			List<Object> tables = new ArrayList<Object>();
			Integer zwsl = shopBaseInfoMapper.findTableCount();
			if (null == zwsl || 0 == zwsl) {
				// 增加一个默认桌位
				Map<String, String> table = new HashMap<String, String>();
				table.put("tid", "999");
				table.put("sno", "999");
				table.put("mealfee", "0");
				table.put("tablename", "999");
				tables.add(table);
			} else {
				for (int i = 1; i <= zwsl; i++) {
					Map<String, String> table = new HashMap<String, String>();
					table.put("tid", i + "");
					table.put("sno", i + "");
					table.put("mealfee", "0");
					table.put("tablename", i + "");
					tables.add(table);
				}
				// 增加一个默认桌位
				Map<String, String> tableDefault = new HashMap<String, String>();
				tableDefault.put("tid", "999");
				tableDefault.put("sno", "999");
				tableDefault.put("mealfee", "0");
				tableDefault.put("tablename", "999");
				tables.add(tableDefault);
			}
			sbiv.put("tables", tables);

			// **************************************************
			// 第三步：处理菜品信息数据
			// **************************************************
			LOGGER.info("处理菜品信息数据...");
			List<MainDish> setMealFixedList = shopBaseInfoMapper.findMainDishDataExt();

			List<Mandatory> setMealSelectList = shopBaseInfoMapper.findMandatoryDishDataExt();

			List<ItemInfoSpec> setDishUnitList = shopBaseInfoMapper.findItemInfoSpecDataExt();

			//获取菜品口味备注缓存 , 需要兼容老版本，先判断 ts_kwlxsdk 是否存在，不存在走老版本
			Map<Integer, List<ItemTasteGroup>> tasteGroupMap = new HashMap<Integer, List<ItemTasteGroup>>();
//			boolean pht = shopBaseInfoMapper.pgHasTable("ts_kwlxsdk");
//			if(!pht){
//				tasteGroupMap = getItemTasteGroup();
//			} else {
//				tasteGroupMap = getItemTasteGroupNew();
//			}
			if ("1".equals(InitDataListener.ggcsMap.get("SFSYJJCGN"))) {
				tasteGroupMap = getItemTasteGroupNew();
			} else {
				tasteGroupMap = getItemTasteGroup();
			}

			String cbh=InitDataListener.ggcsMap.get("WlifeDishSource");
			if (StringUtils.isEmpty(InitDataListener.ggcsMap.get("WlifeDishSource"))) {
				cbh="TS";
			}

			List<ComboItems> setMealSelectDetailList = shopBaseInfoMapper.findComboItemsDataExt(cbh); // 必选分组下明细列表
			List<Optional> setMealOptionalList = shopBaseInfoMapper.selectOptionsItem(cbh); // 可选分组下明细

			String imageBaseUrl=InitDataListener.ggcsMap.get("TZXIMGWEBADD");

			// {"dishs":{"12010001":{"dSaleEnd":"23:55:45","cooks":[],"setmeals":null,"type":1,"priceName":"份","min_unit":1,"info":"","id":243,"dishimg":"http://*************:9080/","name":"全麦汉堡","dOrder":1,"leftamount":-1,"min_count":1,"icon":"","dwname":"份","wxDishs":true,"xmid":243,"norms":[{"orgprice":18,"limitCount":0,"price":18,"duid":243,"name":"份","bargainprice":0,"memberprice":18,"min_unit":0,"membergid":[]}],"did":0,"describ":"","image":"","number":99,"dishunit":"","jsrq":"2099-01-01","daysOfWeek":"1,2,3,4,5,6,0","dSaleStart":"00:00:00","isWeigh":0,"ksrq":"2000-01-01","price":18,"vipPrice":18,"dishsno":"12010001","pkid":54,"dHide":1,"min_reduce":1,"dishkind":[54],"soldout":0},
			// "12010002":{"dSaleEnd":"23:55:45","cooks":[],"setmeals":null,"type":1,"priceName":"份","min_unit":1,"info":"","id":244,"dishimg":"http://*************:9080/","name":"荞麦汉堡","dOrder":2,"leftamount":-1,"min_count":1,"icon":"","dwname":"份","wxDishs":true,"xmid":244,"norms":[{"orgprice":17,"limitCount":0,"price":17,"duid":244,"name":"份","bargainprice":0,"memberprice":17,"min_unit":0,"membergid":[]}],"did":0,"describ":"","image":"","number":99,"dishunit":"","jsrq":"2099-01-01","daysOfWeek":"1,2,3,4,5,6,0","dSaleStart":"00:00:00","isWeigh":0,"ksrq":"2000-01-01","price":17,"vipPrice":17,"dishsno":"12010002","pkid":54,"dHide":1,"min_reduce":1,"dishkind":[54],"soldout":0}}}
			Map<String, Dish> dishs = new HashMap<String, Dish>();
			List<Dish> dishList = shopBaseInfoMapper.findDishData(cbh);
			for (Dish dish : dishList) {

				//菜品id使用ompid
				dish.setId(dish.getOmp_dishesid());
				dish.setDuid(dish.getOmp_dnid());

				// 是否隐藏
				// 1显示，2隐藏
				if (dish.getdHide() == 1) {
					// 是否可以售卖
					if (!ifSale(dish)) {
						dish.setdHide(2);
					}
//					if (dish.getPrice() == 0) {
//						dish.setdHide(2);
//					}
				}

				// 处理菜品分类列表属性，一个菜品可以归属多个分类
				List<Integer> dishkind = new ArrayList<Integer>();
				dishkind.add(dish.getPkid());
				if (null != dishs.get(dish.getDishsno())) {
					for (int dishKindID : dishs.get(dish.getDishsno()).getDishkind()) {
						dishkind.add(dishKindID);
					}
				}
				dish.setNumber(99);
				dish.setDishkind(dishkind);

				// 如果这个菜品属于微生活附加分类菜品，需要替换餐谱分类菜品中的分类列表属性，其它属性全部都是使用餐谱中的默认设置
				if (dish.getPkid() > 1000000) {
					if (dishs.containsKey(dish.getDishsno())) {
						Dish clmxDish = dishs.get(dish.getDishsno());
						clmxDish.setDishkind(dish.getDishkind());
						continue;
					}
				}

				// 处理菜品规格，一个菜品可能存在多种规格化
				List<Norms> normsList = new ArrayList<Norms>();
				if ("Y".equals(dish.getIfspec())) {
					for (ItemInfoSpec iis : setDishUnitList) {
						if (iis.getItemId() == dish.getId()) {
							Norms dishUnit = new Norms();
							dishUnit.setDuid(iis.getDuid());
							dishUnit.setName(iis.getName());
							dishUnit.setPrice(iis.getPrice());
							dishUnit.setOrgprice(iis.getPrice());
							dishUnit.setBargainprice(0);
							dishUnit.setMemberprice(iis.getMemberPrice());
							dishUnit.setMembergid(new ArrayList<Integer>());
							dishUnit.setMin_unit(0);
							dishUnit.setLimitCount(0);
							dishUnit.setBox_price(dish.getBox_price());
							dishUnit.setBox_num(1);
							normsList.add(dishUnit);
						}
					}
				}
				if (normsList.size() == 0) {
					Norms dishUnit = new Norms();
					dishUnit.setDuid(dish.getOmp_dnid());
					dishUnit.setName(dish.getDwname());
					dishUnit.setPrice(dish.getPrice());
					dishUnit.setOrgprice(dish.getPrice());
					dishUnit.setBargainprice(0);
					dishUnit.setMemberprice(dish.getVipPrice());
					dishUnit.setMembergid(new ArrayList<Integer>());
					dishUnit.setMin_unit(0);
					dishUnit.setLimitCount(0);
					dishUnit.setBox_price(dish.getBox_price());
					dishUnit.setBox_num(1);
					normsList.add(dishUnit);
				}

				dish.setNorms(normsList);
				dish.setCooks(new ArrayList<Object>());

				if (dish.getType() == 2) {
					if (dish.getPrice() == 0) {
						dish.setdHide(2);
					}
					// 拼装套餐明细数据
					Setmeals setmealDish = new Setmeals();
					setmealDish.setPrice(dish.getPrice());
					setmealDish.setOrgprice(dish.getPrice());
					setmealDish.setBargainprice(0);
					setmealDish.setMemberprice(dish.getVipPrice());
					setmealDish.setMembergid(new ArrayList<Integer>());
					setmealDish.setLimitCount(0);
					setmealDish.setDuid(dish.getOmp_dnid());

					// 20190427，改成一次查出所有套餐固定选项、可选分组、可选明细
					// 固定明细数据
//					List<MainDish> mainDishList = shopBaseInfoMapper.findMainDishData(dish.getXmid());
//					setmealDish.setMaindish(mainDishList);
//					
//					// 可选套餐明细数据
//					List<Mandatory> mandatoryList = shopBaseInfoMapper.findMandatoryDishData(dish.getXmid());
//					for (Mandatory selectDish : mandatoryList) {
//						List<ComboItems> itemslist = shopBaseInfoMapper.findComboItemsData(selectDish.getId(), selectDish.getSelnum());
//						selectDish.setItems(itemslist);
//					}
//					setmealDish.setMandatory(mandatoryList);

					List<MainDish> mainDishList =new ArrayList<MainDish>();
					for (MainDish mainDish: setMealFixedList) {
						if (mainDish.getXmid() == dish.getXmid()) {
							mainDish.setId(mainDish.getOmp_dishesid());
							mainDish.setDuid(mainDish.getOmp_dnid());
							mainDishList.add(mainDish);
						}
					}
					setmealDish.setMaindish(mainDishList);

					// 必选套餐分组明细数据
					List<Mandatory> mandatoryList = new ArrayList<Mandatory>();
					for (Mandatory selectDish: setMealSelectList) {
						if (selectDish.getXmid() == dish.getXmid()) {
							mandatoryList.add(selectDish);
							List<ComboItems> itemslist =new ArrayList<ComboItems>();
//							for (ComboItems itemDish : setMealSelectDetailList) {
//								  if (itemDish.getRpdid() ==selectDish.getId()) {
//									  itemDish.setMaxnum(selectDish.getSelnum());
//									  itemslist.add(itemDish);
//								  }
//							}
							for (ComboItems itemDishs : setMealSelectDetailList) {
								if (itemDishs.getRpdid() == selectDish.getId()) {
									int maxnum = selectDish.getSelnum();
									ComboItems itemDish = new ComboItems();
									itemDish.setName(itemDishs.getName());
									itemDish.setId(itemDishs.getOmp_dishesid());
									itemDish.setDuid(itemDishs.getOmp_dnid());
									itemDish.setDishsno(itemDishs.getDishsno());
									itemDish.setMaxnum(maxnum);
									itemDish.setAprice(itemDishs.getAprice());
									itemDish.setRpdid(itemDishs.getRpdid());
									itemDish.setCountx(itemDishs.getCountx());
									/*itemDish.setDishToppingId(itemDishs.getDishToppingId());*/
									itemslist.add(itemDish);
								}
							}
							selectDish.setItems(itemslist);
						}
					}
					setmealDish.setMandatory(mandatoryList);

					//非必选项菜品
					List<Optional>  optionals=new ArrayList<>();
					for(Optional op:setMealOptionalList){
						if(op.getCmid().equals( dish.getXmid())){
							optionals.add(op);
						}

					}

					setmealDish.setOptional(optionals);

					dish.setSetmeals(setmealDish);
					// 以下部分屏蔽了套餐的加价菜
					if ("1".equals(InitDataListener.ggcsMap.get("SFSYJJCGN"))) {
						List<ItemTasteGroup> itgs = tasteGroupMap.get(dish.getId());
						if (null != itgs && itgs.size() != 0) {
//							for (ItemTasteGroup itg : itgs) {
							Iterator<ItemTasteGroup> itgi = itgs.iterator();
							while(itgi.hasNext()){
								ItemTasteGroup itg = (ItemTasteGroup)itgi.next();
								List<ItemTaste> itemsTc = new ArrayList<ItemTaste>();
								for (ItemTaste it : itg.getItems()) {
									if ("0".equals(it.getDetailid())) {
										itemsTc.add(it);
									}
								}
								if (itemsTc.size() == 0) {
									itgi.remove();
								} else {
									itg.setItems(itemsTc);
								}
							}
						}
					}
				}
				dish.setMemo(tasteGroupMap.get(dish.getId()));
				dishs.put(dish.getDishsno(), dish);
			}
			sbiv.put("dishs", dishs);

			// **************************************************
			// 第四步：是否使用我们系统内部营销活动
			// **************************************************
			LOGGER.info("是否使用我们系统内部营销活动...");
			List<Marketing> marketing = new ArrayList<>();
			String isDiscountStr = PropertiesUtil.readValueForClasses("/application.properties", "isDiscount");
			if (StringUtil.getBoolean(isDiscountStr, false)) {
				marketing = shopBaseInfoMapper.findMarketingData();
			}
			sbiv.put("marketing", marketing);

			// **************************************************
			// 第五步：菜类分类
			// **************************************************
			LOGGER.info("菜类分类...");
			// 默认增加两种分类
			List<DishKinds> dish_kinds = shopBaseInfoMapper.findDishKindsData(cbh);
			// 所有菜品分类，微生活显示分类列表使用
			DishKinds dishKindsShow = new DishKinds();
			dishKindsShow.setId(-2);
			dishKindsShow.setName("菜类分类");
			dishKindsShow.setSeq(1);
			dishKindsShow.setDishkindsno("-2");
			dishKindsShow.setMust(0);
			dishKindsShow.setMust_seq(1);
			dishKindsShow.setSuggest(0);
			dishKindsShow.setDkHide(1);
			dishKindsShow.setDkOrder(-2);

			List<String> totalDishKindsList = new ArrayList<String>();
			for (DishKinds dishKinds : dish_kinds) {
				if(StringUtils.isNotEmpty(dishKinds.getIcon())){
					dishKinds.setIcon(imageBaseUrl+dishKinds.getIcon());
				}
				totalDishKindsList.add(dishKinds.getId() + "");
			}
			dishKindsShow.setChildren(totalDishKindsList);
			dish_kinds.add(dishKindsShow);

			// 隐藏分类
			DishKinds dishKindsHide = new DishKinds();
			dishKindsHide.setId(-1);
			dishKindsHide.setName("隐藏分类");
			dishKindsHide.setSeq(0);
			dishKindsHide.setDishkindsno("-1");
			dishKindsHide.setMust(0);
			dishKindsHide.setMust_seq(0);
			dishKindsHide.setSuggest(0);
			dishKindsHide.setDkHide(2);
			dishKindsHide.setDkOrder(-1);
			dish_kinds.add(dishKindsHide);
			sbiv.put("dish_kinds", dish_kinds);

			// **************************************************
			// 其它必须回传数据
			// **************************************************
			LOGGER.info("其它必须回传数据...");
			// 会员折上折
			Map<String, List<Object>> activity_rules = new HashMap<String, List<Object>>();
			sbiv.put("activity_rules", activity_rules);
			// 备注
			List<Object> memo = new ArrayList<Object>();
			sbiv.put("memo", memo);
			// 门店资料
			Shops shops = shopBaseInfoMapper.findShopsData();
			sbiv.put("shops", shops);
			// 公告信息
			Map<String, List<Object>> head_activity = new HashMap<String, List<Object>>();
			sbiv.put("head_activity", head_activity);

			//配料数组
//			JSONObject topping=new JSONObject();
//			topping.put("tpid", 16); //配料id
//			topping.put("tpno","013"); //配料编码
//			topping.put("remarkid",5); //配料类别id
//			topping.put("addprice",2.0); //配料单份加价
//			topping.put("order",1); //排序
//			topping.put("tpname","测试配料"); //配料名称
//			topping.put("rcidList",Arrays.asList("20")); //所属菜类List
//			topping.put("remarktypename","饮品加料"); //配料类型名称
//			topping.put("selectflag","2"); //点选规则 0-必选且多选 1-必选只单选 2-自由选
//			topping.put("selectcount","5"); //配料最多选择份数
//			topping.put("remarrkexplain","配料说明"); //配料说明


			sbiv.put("toppings",getTopingsList());

			//转换bohid为ompid
//			convertBohidToOmpid(sbiv);

			data.setData(sbiv);
			data.setSuccess(1);
			data.setMsg("操作成功");

			String dataStr = JSONObject.toJSONString(data);
			wlifeBaseInfoMap.put(wlifeBaseInfoKey, dataStr);

			LOGGER.info("结束同步数据过程...");
			return dataStr;

		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			data.setSuccess(0);
			data.setMsg("系统错误");
			data.setData(new HashMap<String, Object>());
			return JSONObject.toJSONString(data);
		}finally {
			syncLock.unlock();
		}

	}

	@Override
	public void updateCache() {
		wlifeBaseInfoMap.clear();
		shopBaseInfo();
	}

	private void convertBohidToOmpid(Map<String, Object> sbiv) {
		//做法
	/*	Map<String, List<Taste>> kindsMemo = (	Map<String, List<Taste>>) sbiv.get("kinds_memo");
		Map<String,List<Taste>> newKindsMemo=new HashMap<>();
		for (Map.Entry<String, List<Taste>> entry : kindsMemo.entrySet()) {
			 Integer bohId = Integer.valueOf(entry.getKey());
			 Integer ompId= getOmpIdByBohId(DISH_CATEGORY_KEY,bohId);

			 List<Taste> tastes=entry.getValue();
			 for(Taste taste:tastes){
				 taste.setPkid(getOmpIdByBohId(DISH_CATEGORY_KEY,taste.getPkid()));
				 taste.setOmid(getOmpIdByBohId(PRACTICE_KEY,taste.getOmid()));
			 }

			 newKindsMemo.put(String.valueOf(ompId),tastes);
		}
		sbiv.put("kinds_memo",newKindsMemo);*/

		//菜品分类
		List<DishKinds>  dishKinds = (List<DishKinds>) sbiv.get("dish_kinds");
		for(DishKinds kinds:dishKinds){
			kinds.setId(getOmpIdByBohId(DISH_CATEGORY_KEY,kinds.getId()));
		}

		//配料 TODO

		//菜品
		List<Dish> dishes = (List<Dish>) sbiv.get("dishes");
		for(Dish dish:dishes){
			dish.setId(getOmpIdByBohId(DISHES_KEY,dish.getId()));
			List<Integer> dishkinds=dish.getDishkind();

			for (int i = 0; i < dishkinds.size(); i++) {
				dishkinds.set(i,getOmpIdByBohId(DISH_CATEGORY_KEY, dishkinds.get(i)));
			}

			dish.setPkid(getOmpIdByBohId(DISH_CATEGORY_KEY,dish.getPkid()));

			List<Norms> norms=dish.getNorms();
			for(Norms n:norms){
				n.setDuid(getOmpIdByBohId(SPEC_KEY,n.getDuid()));
			}
			//菜品做法cooks TODO

		}




	}

	/**
	 * 检查菜品是否可以售卖
	 *
	 * @param dish
	 * @return
	 */
	public boolean ifSale(Dish dish) {
		boolean b = false;
		// 获取当前日期所属周数
		int week = DateUtil.dayForWeekToDate(new Date(), 0);
		boolean bis = DateUtil.ifSale(dish.getKsrq() + " 00:00:00", dish.getJsrq() + " 23:59:59");
		if (bis && dish.getDaysOfWeek().indexOf(week + "") != -1) {
			b = true;
		}
		return b;
	}


	// 查询组装对应接口格式的菜品备注信息数据图
	public Map<Integer, List<ItemTasteGroup>> getItemTasteGroup() {
		Map<Integer, List<ItemTasteGroup>> itgMap = new HashMap<Integer, List<ItemTasteGroup>>();
		// 查询所有有备注的菜品。菜品id为key
		List<Taste> tasteList = shopBaseInfoMapper.findItemTasteList();
		for (Taste taste : tasteList) {
			List<ItemTasteGroup> itemTasteGroupList = new ArrayList<ItemTasteGroup>();
			List<ItemTaste> itemTasteList = new ArrayList<ItemTaste>();
			// 拆分多个口味备注
			if (null != taste.getOrdermemo() && !"".equals(taste.getOrdermemo())) {
				String[] oms = taste.getOrdermemo().split(",");
				for (int i = 0; i < oms.length; i++) {
					// 拆分口味id与口味名称
					String[] idOm = oms[i].split("_");
					ItemTaste itemTaste = new ItemTaste();
					itemTaste.setOmid(Integer.parseInt(idOm[0]));
					itemTaste.setOmkid(1);
					itemTaste.setSoldout(1);
					itemTaste.setOrdermemo(idOm[1]);
					itemTaste.setAprice(0);
					itemTasteList.add(itemTaste);
				}
				ItemTasteGroup itemTasteGroup = new ItemTasteGroup();
				itemTasteGroup.setOmkid(1);
				itemTasteGroup.setTitle("口味");
				itemTasteGroup.setIs_must(2);
				itemTasteGroup.setIs_radio(2);
				itemTasteGroup.setMin(0);
				itemTasteGroup.setMax(0);
				itemTasteGroup.setItems(itemTasteList);
				itemTasteGroupList.add(itemTasteGroup);
				itgMap.put(taste.getPkid(), itemTasteGroupList);
			}
		}
		return itgMap;
	}

	public Map<Integer, List<ItemTasteGroup>> getItemTasteGroupNew() {
		Map<Integer, List<ItemTasteGroup>> itgMap = new HashMap<Integer, List<ItemTasteGroup>>();
		// 查询所有有备注的菜品。菜品id为key
		List<Taste> tasteList = shopBaseInfoMapper.findItemTasteListNew();
		for (Taste taste : tasteList) {
			List<ItemTasteGroup> itemTasteGroupList = new ArrayList<ItemTasteGroup>();
//			List<ItemTaste> itemTasteList = new ArrayList<ItemTaste>();
			// 拆分多个口味备注
			if (null != taste.getOrdermemo() && !"".equals(taste.getOrdermemo())) {
				String[] oms = taste.getOrdermemo().split(",");
				Map<String, List<ItemTaste>> itemTasteMap = new LinkedHashMap<String, List<ItemTaste>>();
				for (int i = 0; i < oms.length; i++) {
					// 拆分口味id与口味名称
					String[] idOm = oms[i].split("_");
					ItemTaste itemTaste = new ItemTaste();
					itemTaste.setOmid(Integer.parseInt(idOm[1]));
					itemTaste.setOmkid(Integer.parseInt(idOm[4]));
					itemTaste.setSoldout(1);
					itemTaste.setOrdermemo(idOm[2]);
					itemTaste.setAprice(Double.parseDouble(idOm[3]));
					if("1".equals(idOm[5])){
						itemTaste.setSelected(true);
					} else {
						itemTaste.setSelected(false);
					}
					itemTaste.setDetailid(idOm[10]);

					String itmKey = idOm[0] + "_" + idOm[4] + "_" + idOm[6] + "_" + idOm[7] + "_" + idOm[8] + "_" + idOm[9];
					List<ItemTaste> itemTasteList = new ArrayList<ItemTaste>();
					if (null != itemTasteMap.get(itmKey)) {
						itemTasteList = itemTasteMap.get(itmKey);
					}
					itemTasteList.add(itemTaste);
					itemTasteMap.put(itmKey, itemTasteList);
				}

				for (Map.Entry<String, List<ItemTaste>> entry : itemTasteMap.entrySet()) {
					ItemTasteGroup itemTasteGroup = new ItemTasteGroup();
					String[] groupKey = entry.getKey().split("_");
					int isMust = Integer.parseInt(groupKey[2]);
					int isRadio = Integer.parseInt(groupKey[3]);
					int imin = Integer.parseInt(groupKey[4]);
					int imax = Integer.parseInt(groupKey[5]);
					if(imin == 1 && imax == 1){
						isRadio = 1;
					}
					if (imin > 0) {
						isMust = 1;
					}

					itemTasteGroup.setOmkid(Integer.parseInt(groupKey[1]));
					itemTasteGroup.setTitle(groupKey[0]);
					itemTasteGroup.setIs_must(isMust);
					itemTasteGroup.setIs_radio(isRadio);
					itemTasteGroup.setMin(imin);
					itemTasteGroup.setMax(imax);
					itemTasteGroup.setItems(entry.getValue());
					itemTasteGroupList.add(itemTasteGroup);
				}
				itgMap.put(taste.getPkid(), itemTasteGroupList);
			}
		}
		return itgMap;
	}


	public List<Toppings> getTopingsList() {

		//查询所有配料
		List<Toppings> tasteList = shopBaseInfoMapper.findToppingsData();
		Map<Integer, Toppings> map = new HashMap<>();

		Iterator<Toppings> iterator = tasteList.iterator();
		while (iterator.hasNext()) {
			Toppings toppings = iterator.next();
			Integer id = toppings.getTpid();
			String rcid = toppings.getRcid();

			Toppings t = map.get(id);

			if (null != t) {
				List<String> rcidList = t.getRcidList();
				rcidList.add(rcid);
				iterator.remove();
			} else {
				List<String> rcidList = new LinkedList<>();
				rcidList.add(rcid);
				toppings.setRcidList(rcidList);
				map.put(id, toppings);
			}
		}

		return tasteList;

	}

	/**
	 * 根据name和bohId获取ompId
	 * @param name
	 * @param bohId
	 * @return
	 */
	public Integer getOmpIdByBohId(String name,Integer bohId) {
		String key=name+"_"+bohId;

		if(ompIdMap.containsKey(key)){
			return ompIdMap.get(key);
		}else {
			Integer ompId = shopBaseInfoMapper.getOmpIdByBohId(name, bohId);
			//如果不存在返回bohId
			if(null == ompId){
				ompId = bohId;
			}
			ompIdMap.put(key, ompId);
			return ompId;
		}
	}

}
