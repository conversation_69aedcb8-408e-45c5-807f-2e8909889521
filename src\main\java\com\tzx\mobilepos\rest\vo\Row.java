package com.tzx.mobilepos.rest.vo;

import javax.xml.bind.annotation.XmlAttribute;


public class Row{
	
	private String CZLX;
	private String FKFS;
	private String FKJGXH;
	private String MSG;
	private String PAYABLEAMT;
	private String PQLX;
	private String STATUS;
	private String XMID;
	private String YHFSID;
	private String YZM;
	private String VOUCHER;
	private String SJJE;
	private String WDRWID;
	private String COUPONBUYPRICE;			//票券购买价格
	
	public Row() {
		super();
	}

	@XmlAttribute(name = "WDRWID")  
	public String getWDRWID() {
		return WDRWID;
	}

	public void setWDRWID(String wDRWID) {
		WDRWID = wDRWID;
	}

	@XmlAttribute(name = "CZLX")  
	public String getCZLX() {
		return CZLX;
	}
	public void setCZLX(String cZLX) {
		CZLX = cZLX;
	}
	
	@XmlAttribute(name = "FKFS")  
	public String getFKFS() {
		return FKFS;
	}
	public void setFKFS(String fKFS) {
		FKFS = fKFS;
	}
	
	@XmlAttribute(name = "FKJGXH")  
	public String getFKJGXH() {
		return FKJGXH;
	}
	public void setFKJGXH(String fKJGXH) {
		FKJGXH = fKJGXH;
	}
	
	@XmlAttribute(name = "MSG")  
	public String getMSG() {
		return MSG;
	}
	public void setMSG(String mSG) {
		MSG = mSG;
	}
	
	@XmlAttribute(name = "PAYABLEAMT")  
	public String getPAYABLEAMT() {
		return PAYABLEAMT;
	}
	public void setPAYABLEAMT(String pAYABLEAMT) {
		PAYABLEAMT = pAYABLEAMT;
	}
	
	@XmlAttribute(name = "PQLX")  
	public String getPQLX() {
		return PQLX;
	}
	public void setPQLX(String pQLX) {
		PQLX = pQLX;
	}
	
	@XmlAttribute(name = "STATUS")  
	public String getSTATUS() {
		return STATUS;
	}
	public void setSTATUS(String sTATUS) {
		STATUS = sTATUS;
	}
	
	@XmlAttribute(name = "XMID")  
	public String getXMID() {
		return XMID;
	}
	public void setXMID(String xMID) {
		XMID = xMID;
	}
	
	@XmlAttribute(name = "YHFSID")  
	public String getYHFSID() {
		return YHFSID;
	}
	public void setYHFSID(String yHFSID) {
		YHFSID = yHFSID;
	}
	
	@XmlAttribute(name = "YZM")  
	public String getYZM() {
		return YZM;
	}
	public void setYZM(String yZM) {
		YZM = yZM;
	}

	@XmlAttribute(name = "VOUCHER")  
	public String getVOUCHER() {
		return VOUCHER;
	}

	public void setVOUCHER(String vOUCHER) {
		VOUCHER = vOUCHER;
	}

	@XmlAttribute(name = "SJJE")  
	public String getSJJE() {
		return SJJE;
	}

	public void setSJJE(String sJJE) {
		SJJE = sJJE;
	}

	@XmlAttribute(name = "COUPONBUYPRICE")  
	public String getCOUPONBUYPRICE() {
		return COUPONBUYPRICE;
	}

	public void setCOUPONBUYPRICE(String cOUPONBUYPRICE) {
		COUPONBUYPRICE = cOUPONBUYPRICE;
	}
	
}