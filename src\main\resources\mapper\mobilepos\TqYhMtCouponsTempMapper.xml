<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.mobilepos.rest.mapper.MobilePosTqYhMtCouponsTempMapper">
	
	<select id="findClmxid" resultType="Integer">
		SELECT
		    m.clid
		FROM
		    tq_clmxk m
		LEFT JOIN
		    tq_clsdk c
		ON
		    c.id = m.clid
		RIGHT JOIN
		    tq_cbsdk b
		ON
		    b.id = c.cbid
		AND b.yl1 = 'TS'
		WHERE
		    m.xmid = #{xmid}
	</select>
	
	<select id="cancelThirdYhfs" resultType="Integer">
		select * from P_CANCELTHIRDYHFS(#{zdbh},#{yzm})
	</select>
	
	<delete id="deleteByZdbh">
		delete from tq_yhmtcouponstemp where kdzdbh = #{zdbh}
	</delete>
	
</mapper>
