<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.miniapp.rest.mapper.MiniAppTsKwsdkMapper">
	<select id="findTsKwsdkBasicData" resultType="com.tzx.miniapp.rest.vo.Taste" >
		select id,kwbh as taste_code,kwnr1 as taste_content,bzfl as taste_type from ts_kwsdk
	</select>
</mapper>
