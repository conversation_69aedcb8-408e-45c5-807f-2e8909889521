<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.mobilepos.rest.mapper.MobilePosTqThirdExceptOrderMapper">
	<update id="updateToStatus">
		update tq_third_except_order set pay_status = #{paystatus} where orderno = #{outtradeno}
	</update>
	
	<update id="updateByRefund">
		update tq_third_except_order set pay_status = #{paystatus} where billid = #{billid} and pay_status in ('', '0', '1')
	</update>
	
	<update id="constraintByRefund">
		update tq_third_except_order set pay_status = #{paystatus} where billid = #{billid} 
	</update>
</mapper>
