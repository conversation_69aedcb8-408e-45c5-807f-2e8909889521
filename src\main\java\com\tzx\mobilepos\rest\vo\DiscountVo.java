package com.tzx.mobilepos.rest.vo;

/**
 * @ClassName: DiscountVo
 * <AUTHOR>
 * @date 2018-10-17
 * @email <EMAIL>
 * @Description: 优惠活动
 */
public class DiscountVo {

	private Integer clid;
	private Integer dishid;
	private String dishno;
	private String dishname;
	private String color;
	private String discounttypeid;
	private String showxh;
	private String qxid;
	private String yhsx;

	public DiscountVo() {
		super();
	}

	public DiscountVo(Integer clid, Integer dishid, String dishno, String dishname, String color, String discounttypeid,
			String showxh, String qxid, String yhsx) {
		super();
		this.clid = clid;
		this.dishid = dishid;
		this.dishno = dishno;
		this.dishname = dishname;
		this.color = color;
		this.discounttypeid = discounttypeid;
		this.showxh = showxh;
		this.qxid = qxid;
		this.yhsx = qxid;
	}

	public Integer getClid() {
		return clid;
	}

	public void setClid(Integer clid) {
		this.clid = clid;
	}

	public Integer getDishid() {
		return dishid;
	}

	public void setDishid(Integer dishid) {
		this.dishid = dishid;
	}

	public String getDishno() {
		return dishno;
	}

	public void setDishno(String dishno) {
		this.dishno = dishno;
	}

	public String getDishname() {
		return dishname;
	}

	public void setDishname(String dishname) {
		this.dishname = dishname;
	}

	public String getColor() {
		return color;
	}

	public void setColor(String color) {
		this.color = color;
	}

	public String getDiscounttypeid() {
		return discounttypeid;
	}

	public void setDiscounttypeid(String discounttypeid) {
		this.discounttypeid = discounttypeid;
	}

	public String getShowxh() {
		return showxh;
	}

	public void setShowxh(String showxh) {
		this.showxh = showxh;
	}

	public String getQxid() {
		return qxid;
	}

	public void setQxid(String qxid) {
		this.qxid = qxid;
	}

	public String getYhsx() {
		return yhsx;
	}

	public void setYhsx(String yhsx) {
		this.yhsx = yhsx;
	}

}
