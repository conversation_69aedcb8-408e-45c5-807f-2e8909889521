package com.tzx;

import com.tzx.miniapp.rest.service.IMiniAppClearDishService;
import com.tzx.miniapp.rest.service.IMiniAppNotification;
import com.tzx.miniapp.rest.service.IMiniAppZsSyncData;
import com.tzx.publics.listener.InitDataListener;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.util.concurrent.Executor;

/**
 * 这里通过 ApplicationRunner 来这只开机启动
 */
@Component
public class TzxDataSyncRunner implements ApplicationRunner {

	private final static Logger LOGGER = LoggerFactory.getLogger(TzxDataSyncRunner.class);
	@Autowired
	private IMiniAppNotification notification;
	@Autowired
	private IMiniAppZsSyncData zsSynchroData;
	@Autowired
	private IMiniAppClearDishService clearDishService;


	@Autowired
	private Executor executor;

	@Override
	public void run(ApplicationArguments var1) throws Exception {
		executor.execute(new Runnable() {
			@Override
			public void run() {
				//等5分钟
				try {
					Thread.sleep(1000 * 60 * 5);
				} catch (InterruptedException e) {
				    e.printStackTrace();
				}
				LOGGER.info("request(DataSyncRunner)" + InitDataListener.ggcsMap.get("POS_MEMBER_TYPE") + "准备执行");
				if ("3".equals(InitDataListener.ggcsMap.get("POS_MEMBER_TYPE"))) {
					JSONObject json = new JSONObject();
					// LOGGER.info("request(DataSyncRunner_3)：" + json.toString());
					zsSynchroData.synchrodata(json);
					// LOGGER.info("response(DataSyncRunner_3)：" + json.toString());
				}

				if ("1".equals(InitDataListener.ggcsMap.get("POS_MEMBER_TYPE"))) {
					JSONObject json = new JSONObject();
					json.put("t", "");
					json.put("secret", "0");
					json.put("type", "all");
					json.put("force", "1");
					// LOGGER.info("request(DataSyncRunner_1)：" + json.toString());
					notification.synchrodataDish(json);
					// LOGGER.info("response(DataSyncRunner_1)：" + json.toString());
					// 初始加载沽清缓存
					clearDishService.initClearDishCache();
				}
				LOGGER.info("request(DataSyncRunner)" + InitDataListener.ggcsMap.get("POS_MEMBER_TYPE") + "执行完毕");
			}
		});
	}
}