package com.tzx.miniapp.rest.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tzx.miniapp.rest.mapper.MiniAppShopBaseInfoMapper;
import com.tzx.miniapp.rest.service.IData;
import com.tzx.miniapp.rest.vo.Shops;
import com.tzx.miniapp.rest.vo.ZsRefundOrdeVo;
import com.tzx.miniapp.rest.vo.ZsStoreIsOpenVo;

/**
 * MiniAppZsSyncDish
 * 
 * <AUTHOR> 2019年06月17日
 */
public class MiniAppZsSyncRefundOrder extends IData<ZsRefundOrdeVo> {

	MiniAppShopBaseInfoMapper shopBaseInfoMapper;
	String orderNo = "";
	int operType = 4;

	public MiniAppZsSyncRefundOrder(MiniAppShopBaseInfoMapper shopBaseInfoMapper, String yddh, int operType) {
		this.shopBaseInfoMapper = shopBaseInfoMapper;
		this.orderNo = yddh;
		this.operType = operType;
		
	}
	
	@Override
	public String getParams() throws JsonProcessingException {
		ZsRefundOrdeVo ro = new ZsRefundOrdeVo();
		ro.setOrderNo(orderNo);
		ro.setOperType(operType);
		return buildReturn(ro, 1, shopBaseInfoMapper);
	}

	public String getUrl() {
		return "refundOrder";
	}

}
