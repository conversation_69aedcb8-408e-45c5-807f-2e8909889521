<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.miniapp.rest.mapper.MiniAppTqClsdkMapper">
	<select id="findItemClassBasicData" resultType="com.tzx.miniapp.rest.vo.ItemClass" >
		select id, clbh as itemclass_code, clmc1 as itemclass_name, showxh as number from tq_clsdk where sfxs = 'Y' and clsx &lt;&gt; 'CPYSSX_YH' 
	</select>
</mapper>
