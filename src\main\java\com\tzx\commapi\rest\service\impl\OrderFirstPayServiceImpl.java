package com.tzx.commapi.rest.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.tzx.commapi.common.CommApiException;
import com.tzx.commapi.rest.service.IOrderFirstPayService;
import com.tzx.commapi.rest.vo.CommApiData;
import com.tzx.ecoserver.rest.mapper.EcoOrderMapper;
import com.tzx.ecoserver.rest.vo.EcoOrderdiscount;
import com.tzx.miniapp.common.Constant;
import com.tzx.miniapp.rest.mapper.MiniAppFirstPayMapper;
import com.tzx.miniapp.rest.mapper.MiniAppOrderPrecheckMapper;
import com.tzx.miniapp.rest.mapper.MiniAppShopStatusMapper;
import com.tzx.miniapp.rest.model.TqJtztk;
import com.tzx.miniapp.rest.model.TqZdk;
import com.tzx.miniapp.rest.model.TsCmk;
import com.tzx.miniapp.rest.model.TsFkfssdk;
import com.tzx.miniapp.rest.model.TsGgcsk;
import com.tzx.miniapp.rest.model.TsYhfssdk;
import com.tzx.miniapp.rest.vo.BtPayments;
import com.tzx.miniapp.rest.vo.BtYdd;
import com.tzx.miniapp.rest.vo.BtYdxm2;
import com.tzx.publics.common.BillNoData;
import com.tzx.publics.listener.InitDataListener;
import com.tzx.publics.util.ArithUtil;
import com.tzx.publics.util.DateUtil;
import com.tzx.publics.vo.OrganVo;
import com.tzx.receiver.entity.msg.EcoTypeDic;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

@Service
public class OrderFirstPayServiceImpl implements IOrderFirstPayService {
	private final static Logger LOGGER = LoggerFactory.getLogger(OrderFirstPayServiceImpl.class);

	@Autowired
	private MiniAppShopStatusMapper shopStatusMapper;
	@Autowired
	private MiniAppOrderPrecheckMapper orderPrecheckMapper;
	@Autowired
	private MiniAppFirstPayMapper firstPayMapper;
	@Autowired
    private EcoOrderMapper ecoOrderMapper;

	@Transactional
	public void orderPrecheckBefore(CommApiData data, JSONObject orderData) {
		// 创建返回数据对象
		data.setCode(0);
		data.setMsg("验证通过！");
		JSONObject orderInfo = orderData.optJSONObject("order_info");
		JSONArray payInfoArr = orderData.optJSONArray("pay_info");
		JSONArray disInfoArr = orderData.optJSONArray("dis_info");
		
		double orderAmount = orderInfo.optDouble("order_amount", 0);
		double payAmount = 0;
		double disAmount = 0;
		double orderRealAmount = 0;
		
		for (int i = 0; i < payInfoArr.size(); i++) {
			JSONObject payInfo = payInfoArr.getJSONObject(i);
			double pAmount = payInfo.optDouble("pay_amount");
			double rAmount = payInfo.optDouble("pay_real_amount");
			payAmount = ArithUtil.add(payAmount, pAmount);
			orderRealAmount = ArithUtil.add(orderRealAmount, rAmount);
		}

		for (int i = 0; i < disInfoArr.size(); i++) {
			JSONObject disInfo = disInfoArr.getJSONObject(i);
			double dAmount = disInfo.optDouble("dis_amount");
			disAmount = ArithUtil.add(disAmount, dAmount);
		}
		LOGGER.info("账单金额=" + orderAmount + "，支付金额=" + payAmount + "，优惠金额=" + disAmount);
		
		orderInfo.put("order_real_amount", orderRealAmount);
		orderInfo.put("orde_dis_amount", disAmount);

		if (orderAmount != ArithUtil.add(payAmount, disAmount)) {
			data.setCode(1);
			throw new CommApiException("接单失败:" + "“账单金额”与明细不符，请联系管理员！");
		}

	}

	@Transactional
	public void orderPrecheck(CommApiData data, JSONObject orderData, BillNoData billNoData) {
		// 订单号
		String outOrderId = orderData.optString("out_order_id");
		// 持久化使用第预订单号字段，为了快速区分，小程序增加了 TS 前缀
		String outOrderIdInDB = Constant.BILL_PREFIX + outOrderId;

		 OrganVo organ = InitDataListener.organVo;
		// 报表日期
		Map<String, String> bbrqMap = shopStatusMapper.findBbrq();
		// 生成默认报表日期
		String bbrq = DateUtil.getNowDateYYDDMM();
		if (null != bbrqMap && bbrqMap.size() != 0) {
			bbrq = bbrqMap.get("bbrq");
		}
		// 判断营业状态，因为推送异常账单时不判断营业状态，所以这里控制一下
		TqJtztk jtztk = shopStatusMapper.checkShopOpenStart(DateUtil.parseDate(bbrq));
		if (null == jtztk || "JSSY".equals(jtztk.getCznr())) {
			data.setCode(1);
			data.setMsg("下单失败:门店已打烊，请开店后重试！");
			data.setData(new HashMap<String, Object>());
			throw new CommApiException("接单失败:门店已打烊，请开店后重试！");
		}

		TqZdk tqzdk = orderPrecheckMapper.getZdk(outOrderIdInDB);
		TqZdk tqzdlsk = orderPrecheckMapper.getZdlsk(outOrderIdInDB);

		if ((null != tqzdk && "ZDSX_YJ".equals(tqzdk.getJzsx())) || (null != tqzdlsk && "ZDSX_YJ".equals(tqzdlsk.getJzsx()))) {
			JSONObject joData = new JSONObject();

			if (null != tqzdk) {
				joData.put("out_order_id", outOrderId);
				joData.put("pos_order_id", organ.getJgxh() + "_" + tqzdk.getKdzdbh());
			}
			if (null != tqzdlsk) {
				joData.put("out_order_id", outOrderId);
				joData.put("pos_order_id", organ.getJgxh() + "_" + tqzdlsk.getKdzdbh());
			}
			data.setData(joData);
			data.setCode(101);
			data.setMsg("订单已同步");
		} else {
			orderPrecheckMapper.clearYdd(outOrderIdInDB);
			orderPrecheckMapper.clearYdxm1(outOrderIdInDB);
			orderPrecheckMapper.clearYdxm2(outOrderIdInDB);
//			orderPrecheckMapper.clearYddTcSelectMx(outOrderIdInDB);
			orderPrecheckMapper.clearYddPayments(outOrderIdInDB);
//			orderPrecheckMapper.clearYddActive(outOrderIdInDB);
//			orderPrecheckMapper.clearYddPayActive(outOrderIdInDB);
				
			JSONObject orderInfo = orderData.optJSONObject("order_info");
			JSONArray payInfoArr = orderData.optJSONArray("pay_info");
			JSONArray disInfoArr = orderData.optJSONArray("dis_info");
			
			data.setCode(1);
			
            String kdzdbh = billNoData.getKdzdbh();
            Map<String, String> rMap = createBill(orderInfo, kdzdbh, outOrderIdInDB, bbrq, billNoData);
            // 写入 与订单表 bt_ydd
            jointYdd(kdzdbh, orderInfo, outOrderIdInDB, rMap);
            //落单
            ordering(orderInfo, kdzdbh, outOrderIdInDB, rMap);
			// 支付与优惠
            firstPay(outOrderIdInDB, payInfoArr, disInfoArr, kdzdbh, bbrq, rMap);
            // 开始转账单
            int ytzR = firstPayMapper.yddToZdXcx(outOrderIdInDB, billNoData.getLsdh(), kdzdbh, billNoData.getJzzdbh(), Integer.parseInt(rMap.get("bcid")), DateUtil.parseDate(bbrq), rMap.get("czybh"), Integer.parseInt(rMap.get("ygdlcs")));
			if (ytzR == 0) {
				LOGGER.info("预订单转订单执行完成，返回代码：" + ytzR);
				JSONObject joData = new JSONObject();
				joData.put("out_order_id", outOrderId);
				joData.put("pos_order_id", organ.getJgxh() + "_" + kdzdbh);
				data.setData(joData);
				data.setCode(0);
				data.setMsg("付款成功");
			} else if (ytzR == -100) {
				LOGGER.info("预订单转订单失败， 预订单数据不存在,平台订单编号：" + outOrderIdInDB + "，错误代码：" + ytzR);
				throw new CommApiException("接单失败:转订单失败， 预订单数据不存在，错误代码：" + ytzR);
			} else if (ytzR == -200) {
				LOGGER.info("预订单转订单失败,账单已存在：" + outOrderIdInDB + "，错误代码：" + ytzR);
				throw new CommApiException("接单失败:转订单失败，账单已存在，错误代码：" + ytzR);
			} else if (ytzR == -800) {
				LOGGER.info("预订单转订单失败，收银已交班，请重新开班后再试,平台订单编号：" + outOrderIdInDB + "，错误代码：" + ytzR);
				throw new CommApiException("接单失败:转订单失败， 收银已交班，请重新开班后再试，错误代码：" + ytzR);
			} else {
				LOGGER.info("预订单转订单失败,平台订单编号：" + outOrderIdInDB + "，错误代码：" + ytzR);
				throw new CommApiException("接单失败:转订单失败，错误代码：" + ytzR);
			}
		}
	}
	
	public Map<String, String> createBill(JSONObject orderInfo, String kdzdbh, String yddh, String bbrq, BillNoData billNoData) {
		Map<String, String> rMap = new HashMap<String, String>();
		String jtbh = "99";// 机台号
		int bcid = 0; // 班次id
		TqJtztk jtzt = firstPayMapper.getJtZtk(DateUtil.parseDate(bbrq));
		String ygdlcs = jtzt.getYgdlcs();
		String czybh = jtzt.getRybh();// 操作员编号

		TsGgcsk yyms = firstPayMapper.getGgcsToWs("MDYYMS");
		String zwbh = "";
		String qch = billNoData.getQch();
		if (null != yyms && "3".equals(yyms.getSdnr())) {
			bcid = firstPayMapper.getBcid(DateUtil.parseDate(bbrq));
		} else {
			bcid = Integer.parseInt(jtzt.getYl1());
		}

		rMap.put("zwbh", zwbh);
		rMap.put("qch", qch);
		rMap.put("bcid", bcid + "");
		rMap.put("czybh", czybh);
		rMap.put("jtbh", jtbh);
		rMap.put("bbrq", bbrq);
		rMap.put("ygdlcs", ygdlcs);

		return rMap;
	}
	
	public void jointYdd(String kdzdbh, JSONObject orderInfo, String outOrderIdInDB, Map<String, String> rMap) {
        BtYdd by = new BtYdd();
        OrganVo organ = InitDataListener.organVo;
        String cwlxbh = InitDataListener.ggcsMap.get("POS_MEMBER_TYPE");
        
        String ydbcid =  rMap.get("bcid");
        String qch = rMap.get("qch");
        String zwbh = rMap.get("zwbh");

        by.setYddh(outOrderIdInDB);
        by.setYdrs(orderInfo.optInt("people"));
        by.setMen(0);
        by.setWomen(0);
        by.setEldernum(0);
        by.setChildnum(0);
        by.setShops_id(organ.getJgxh() + "");
        by.setTotalprice(orderInfo.optDouble("order_real_amount"));
        by.setYl1(orderInfo.optDouble("order_amount"));
//        by.setYl2(orderInfo.optDouble("orde_dis_amount"));
//        by.setShop_rate(orderInfo.optDouble("orde_dis_amount"));
        by.setYl2(0);
        by.setShop_rate(0);
        by.setYdrq(DateUtil.getNowDateYYDDMM());
        by.setQdsj(DateUtil.getNowDateYYDDMMHHMMSS());
        by.setYdbc(ydbcid);
        by.setDdzt("5");
        by.setYl4("XCX");
        by.setKwxh(orderInfo.optString("ordermemo"));
        by.setZlbh(zwbh);
        by.setMealtime("");
        by.setChannel(0);
        // 销售模式
        if (orderInfo.containsKey("diningWay")) {
            int diningWay = orderInfo.optInt("diningWay", 1); // 默认为"1"堂食,2外带，3外卖
            by.setDiningway("XSMS_TS");
            by.setYl5("1");
            if (diningWay == 2) {
                by.setDiningway("XSMS_WM"); // "XSMS_WM"是外带，不是外卖
                by.setYl5("2");
            }
        }
        by.setBz(cwlxbh);
        by.setShrbh(qch);
        by.setFail_type2("catering");
        by.setBill_num(kdzdbh);
        
        orderPrecheckMapper.insertBtYddByZs(by);
    }
	
	public void ordering(JSONObject orderInfo, String kdzdbh, String outOrderIdInDB, Map<String, String> rMap) {

		List<BtYdxm2> bymx = new ArrayList<BtYdxm2>();

		TsCmk cmk = orderPrecheckMapper.getFoodBoxInfo("硬POS固定菜品");
		if (cmk == null) {
			throw new CommApiException("接单失败:对应菜品不存在，请联系管理员添加！");
		} else {
			BtYdxm2 ydxm2 = jointYdmx(orderInfo, cmk, outOrderIdInDB);
			bymx.add(ydxm2);
		}

		// 保存菜品数据
		if (bymx.size() > 0) {
			orderPrecheckMapper.insertBtYdxm2(bymx);
		}
	}
	
	public BtYdxm2 jointYdmx(JSONObject orderInfo, TsCmk fbCmk, String outOrderIdInDB) {
		BtYdxm2 ydmx2 = new BtYdxm2();
		ydmx2.setIsactive(0);
		ydmx2.setYddh(outOrderIdInDB);
		ydmx2.setXmid(fbCmk.getCmid());
		ydmx2.setXmbh(fbCmk.getCmbh());
		ydmx2.setXmmc(fbCmk.getCmmc1());
		ydmx2.setXmsx("CMSX_DP");
		// 单价还是菜品原价
		ydmx2.setXmdj(new BigDecimal(orderInfo.optString("order_amount", "0")));
		ydmx2.setXmsl(1);
		ydmx2.setZkl(100);
		// 菜品实结金额
		ydmx2.setTotalprice(new BigDecimal(orderInfo.optString("order_real_amount", "0")));
		ydmx2.setDwbh(fbCmk.getDwbh());
		ydmx2.setKwbh(orderInfo.optString("ordermemo"));
		// 菜品金额使用原价*数量
		ydmx2.setCmje(new BigDecimal(orderInfo.optString("order_amount", "0")));
		ydmx2.setTcbh("");
		ydmx2.setTcdch(0);
		ydmx2.setFzsl(0);
		ydmx2.setDcxh(1);
		ydmx2.setFzje(new BigDecimal(0));
//		ydmx2.setYl3(orderInfo.optString("orde_dis_amount", "0"));
		ydmx2.setYl3("0");

		return ydmx2;
	}
	
	public void firstPay(String outOrderIdInDB, JSONArray payInfoArr, JSONArray disInfoArr, String kdzdbh, String bbrq, Map<String, String> rMap) {
		List<EcoOrderdiscount> ecoOrderdiscounts = new ArrayList<EcoOrderdiscount>();
		for (int i = 0; i < payInfoArr.size(); i++) {
			JSONObject payInfo = payInfoArr.getJSONObject(i);
			String payCode = payInfo.optString("pay_code");
			String payName = payInfo.optString("pay_name");
			
			TsFkfssdk fkfs = firstPayMapper.getFkfsByCode(payCode);
			if (fkfs == null) {
				throw new CommApiException("接单失败:支付方式“" + payName + "”不存在！");
			}
			BigDecimal actualmoney = new BigDecimal("0.00");
			actualmoney = BigDecimal.valueOf(payInfo.optDouble("pay_real_amount"));
			insertPayments(outOrderIdInDB, fkfs, actualmoney.doubleValue(), DateUtil.getNowDateYYDDMMHHMMSS());
			double payAmount = payInfo.optDouble("pay_amount");
			double payRealAmount = payInfo.optDouble("pay_real_amount");
			double payDisAmount = ArithUtil.sub(payAmount, payRealAmount);
			if (payDisAmount != 0) {
				EcoTypeDic etd = firstPayMapper.findEcotypedic(payCode, "-1");
				if (etd == null) {
					throw new CommApiException("接单失败:支付方式“" + payName + "”无优惠关系绑定，请联系管理员！");
				}
				TsYhfssdk yhfssdk = firstPayMapper.getBjyhYh(etd.getTzxcode());
				if (yhfssdk == null) {
					throw new CommApiException("接单失败:支付方式“" + payName + "”绑定的优惠方式不存在，请联系管理员！");
				}
				
				EcoOrderdiscount ecoOrderdiscount = jointEcoOrderdiscount(outOrderIdInDB, payDisAmount, yhfssdk, "PAYDISCOUNT");
				ecoOrderdiscounts.add(ecoOrderdiscount);
			}
			
		}
		
		for (int i = 0; i < disInfoArr.size(); i++) {
			JSONObject disInfo = disInfoArr.getJSONObject(i);
			String disCode = disInfo.optString("dis_code", "");
			String disName = disInfo.optString("dis_name" , "");
			double disAmount = disInfo.optDouble("dis_amount", 0);
			
			TsYhfssdk yhfssdk = firstPayMapper.getBjyhYh(disCode);
			if (yhfssdk == null) {
				throw new CommApiException("接单失败:优惠方式“" + disName + "”不存在，请联系管理员！");
			}
			
			EcoOrderdiscount ecoOrderdiscount = jointEcoOrderdiscount(outOrderIdInDB, disAmount, yhfssdk, "DISCOUNT");
			ecoOrderdiscounts.add(ecoOrderdiscount);
		}
		
		if (ecoOrderdiscounts.size() > 0){
			ecoOrderMapper.insertEcoOrderdiscount(ecoOrderdiscounts);
		}
		
		if (payInfoArr.size() == 0) {
			TsFkfssdk fkfs = firstPayMapper.getFkfsByCode("0001");
			insertPayments(outOrderIdInDB, fkfs, 0, DateUtil.getNowDateYYDDMMHHMMSS());
		}
	}
	
	public void insertPayments(String outOrderIdInDB, TsFkfssdk fkfs, double actualmoney, String addtime) {
        BtPayments btpay = new BtPayments();
        btpay.setYddh(outOrderIdInDB);
        btpay.setPay_channel("XCX");
        btpay.setPay_name(fkfs.getFkfsmc1());
        btpay.setPay_no(fkfs.getFkfsbh());
        btpay.setVcardid("");
        btpay.setVtele("");
        btpay.setPay_count(actualmoney);
        btpay.setPay_bb(0);
        btpay.setVocount(0);
        btpay.setFzzhje(0);
        btpay.setFlhl(0);
        btpay.setPay_memo("");
        btpay.setOpttime(addtime);
        // 生成预定付款记录
        firstPayMapper.insertBtPayments(btpay);
    }
	
	public EcoOrderdiscount jointEcoOrderdiscount(String outOrderIdInDB, double disAmount, TsYhfssdk yhfssdk, String type) {
		EcoOrderdiscount ecoOrderdiscount = new EcoOrderdiscount();
		ecoOrderdiscount.setOrder_code(outOrderIdInDB);
		ecoOrderdiscount.setDiscount_type(type);
		ecoOrderdiscount.setDiscount_desc(yhfssdk.getYhfsmc1());
		ecoOrderdiscount.setDiscount_fee(new BigDecimal(disAmount));
		ecoOrderdiscount.setShop_rate(new BigDecimal(disAmount));
		ecoOrderdiscount.setPlatform_rate(new BigDecimal(0));
		ecoOrderdiscount.setActivity_id(yhfssdk.getYhfsbh());
		
		return ecoOrderdiscount;
	}

}
