package com.tzx.fmcgbi.rest.mapper;

import org.apache.ibatis.annotations.Param;

import com.tzx.fmcgbi.rest.model.TqZdk;
import com.tzx.fmcgbi.rest.vo.ThirdExceptOrderVo;
import com.tzx.publics.base.MyMapper;

/**
 * 查询付款状态接口
 * 
 * <AUTHOR>
 */

public interface FmcgbiQueryCodePayMapper extends MyMapper<TqZdk> {

	// 获取 第三方支付信息
	public ThirdExceptOrderVo getTteo(@Param("tradeNo") String tradeNo);

	
}