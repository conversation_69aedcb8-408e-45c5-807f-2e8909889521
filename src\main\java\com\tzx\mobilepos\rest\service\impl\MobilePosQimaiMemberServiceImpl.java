package com.tzx.mobilepos.rest.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.tzx.commapi.rest.service.ICommApiService;
import com.tzx.commapi.rest.service.IUseYhfsApiService;
import com.tzx.commapi.rest.vo.CommApiData;
import com.tzx.commapi.rest.vo.UseYhfsParam;
import com.tzx.mobilepos.common.Constant;
import com.tzx.mobilepos.common.Data;
import com.tzx.mobilepos.rest.mapper.MobilePosAcewillCouponTempMapper;
import com.tzx.mobilepos.rest.mapper.MobilePosAcewillMemberinfoMapper;
import com.tzx.mobilepos.rest.mapper.MobilePosTqJtztkMapper;
import com.tzx.mobilepos.rest.mapper.MobilePosTqYhMtCouponsTempMapper;
import com.tzx.mobilepos.rest.mapper.MobilePosTqZdkMapper;
import com.tzx.mobilepos.rest.model.TqAcewilCouponCache;
import com.tzx.mobilepos.rest.model.TqFklslsk;
import com.tzx.mobilepos.rest.model.TqMemberInfo;
import com.tzx.mobilepos.rest.model.TqYhMtCouponsTemp;
import com.tzx.mobilepos.rest.model.TqZdk;
import com.tzx.mobilepos.rest.model.TsPsjgsdk;
import com.tzx.mobilepos.rest.service.IMobilePosQimaiMemberService;
import com.tzx.mobilepos.rest.vo.AcewillProductsVo;
import com.tzx.mobilepos.rest.vo.CouponRunningWater;
import com.tzx.mobilepos.rest.vo.Dish;
import com.tzx.mobilepos.rest.vo.PayResultData;
import com.tzx.mobilepos.rest.vo.WdDishVo;
import com.tzx.publics.listener.InitDataListener;
import com.tzx.publics.util.ArithUtil;
import com.tzx.publics.util.DateUtil;
import com.tzx.publics.util.DoubleUtil;
import com.tzx.publics.util.GsonUtil;
import com.tzx.publics.util.HttpClientUtil;
import com.tzx.publics.util.ParamUtil;
import com.tzx.publics.util.ReqDataUtil;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

@Service
public class MobilePosQimaiMemberServiceImpl implements IMobilePosQimaiMemberService {
    private final static Logger LOGGER = LoggerFactory.getLogger(MobilePosQimaiMemberServiceImpl.class);

    @Autowired
    private MobilePosTqZdkMapper tqZdkMapper;
    @Autowired
    private MobilePosAcewillCouponTempMapper acewillCouponTempMapper;
    @Autowired
    private MobilePosAcewillMemberinfoMapper acewillMemberinfoMapper;
    @Autowired
    private MobilePosTqJtztkMapper tqJtztkMapper;
    @Autowired
    private MobilePosTqYhMtCouponsTempMapper tqYhMtCouponsTempMapper;
    @Autowired
    private IUseYhfsApiService useYhfsApiService;
    @Autowired
    private ICommApiService commApiService;

    public void findQimaiMember(Data data, Data result) {
        Map<String, Object> map = ReqDataUtil.getDataMap(data);
        String zdbh = ParamUtil.getStringValue(map, "zdbh", false, null);// 帐单编号
        String cno = ParamUtil.getStringValue(map, "cno", false, null);// 电子卡号、实体卡号或手机号
        String qrtype = ParamUtil.getStringValue(map, "qrtype", false, null);// 号码类型   1手机，2卡号，空为会员id
        qimaiUserAccount(result, zdbh, cno, qrtype);
    }

    /**
     * 根据电子卡号、实体卡号或手机号获取用户的账户信息
     * @param result
     * @param zdbh
     * @param cno
     */
    public void qimaiUserAccount(Data result, String zdbh, String cno, String qrtype) {
        List<JSONObject> dataList = new ArrayList<JSONObject>();

        JSONObject params = new JSONObject();

        if ("1".equals(qrtype) || "2".equals(qrtype)) {
            params.put("type", qrtype);
            params.put("phone", cno);
            params.put("cardNo", cno);// 电子卡号、实体卡号或手机号,动态二维码什么的
        } else {
            params.put("customerId", cno);
        }
        params.put("useStatus", 0);
        params.put("pageNum", 1);
        params.put("pageSize", 100);

        JSONObject obj = new JSONObject();
        obj.put("Action", "communal.user.all-customer-info");
        obj.put("Params", params);

        CommApiData dataR = new CommApiData();
        commApiService.qmMemberRequest(dataR, obj);
        String rStr = "";
        if (0 == dataR.getCode()) {
            rStr = dataR.getData().toString();
        } else {
            result.setCode(-1);
            result.setMsg(dataR.getMsg());
            result.setSuccess(false);
            return;
        }
        int fkfsid = acewillCouponTempMapper.getFkfsid("1973");

        // 只要是重新查询了，不管查询结果如何，先清了临时表再说
        acewillCouponTempMapper.delTmi(zdbh);
        acewillCouponTempMapper.delTwct(zdbh);
        acewillCouponTempMapper.delDsfls(zdbh, "QIMAI");
        tqZdkMapper.delTpd(zdbh, -133, "");
        tqZdkMapper.delTpd(zdbh, -135, "");
        tqZdkMapper.delTpd(zdbh, -136, "");
        tqZdkMapper.delTpd(zdbh, -137, "");

        JSONObject ua = JSONObject.fromObject(rStr);
        if ("0".equals(ua.optString("code"))) {
            JSONObject resObj = ua.optJSONObject("data");
            JSONObject detailInfo = resObj.optJSONObject("detailInfo");
            double fkje = acewillCouponTempMapper.getZdje(zdbh); // 付款金额
            JSONObject card = new JSONObject();
            card.put("cno", detailInfo.optString("cardNo")); // 卡号
            double rechargeBalance = ArithUtil.mul(resObj.optDouble("rechargeBalance", 0), 100);
            double giftBalance = ArithUtil.mul(resObj.optDouble("giftBalance", 0), 100);
            double balance = ArithUtil.add(rechargeBalance, giftBalance);
            Integer balancei = new Double(balance).intValue();
            card.put("balance", balance); // 卡余额
            card.put("name", detailInfo.optString("username")); // 用户名称
            card.put("type", "wx"); // 卡类型，wx:微信卡 dp:点评卡 ph:实体卡 actual:实体卡
            card.put("in_effect", true); // 卡是否有效
            card.put("grade_name", detailInfo.optString("memberLevelName")); // 卡等级名称
//			String isVipPrice = acewillMemberinfoMapper.getIsVipPrice(detailInfo.optInt("memberLevel"));
            String isVipPrice = "Y"; // 企迈不提供等级列表，默认全部会员可用会员价
            card.put("is_vip_price", isVipPrice); // 是否使用会员价
            card.put("credit", resObj.optString("totalPoints")); // 用户积分
            card.put("use_credit", true); // 是否可以使用积分消费

            if (resObj.containsKey("use_max_credit_money")) { //积分可抵扣的最大金额
                card.put("use_max_credit_money", resObj.optInt("use_max_credit_money"));
            }

            card.put("credit_deduct", 1); // 多少积分抵1元,默认1积分1元
            if (resObj.containsKey("pointsRule")) {
                JSONObject pointsRule = resObj.optJSONObject("pointsRule");
                if (null != pointsRule && pointsRule.containsKey("points_rule")) {
                    card.put("credit_deduct", pointsRule.optInt("points_rule"));
                }
            }

            TqMemberInfo tmi = new TqMemberInfo();
            tmi.setBillid(zdbh);
            tmi.setScancode(detailInfo.optString("cardNo"));
            tmi.setRemark(resObj.toString());
            tmi.setCanvipprice(isVipPrice);
            tmi.setBalance(balancei);
            tmi.setIs_usable(0);
            tmi.setCredit(resObj.optInt("totalPoints"));

            acewillMemberinfoMapper.insert(tmi);

            JSONArray coupons = resObj.optJSONArray("CouponsLists");
            JSONArray couponList = new JSONArray();
            for (int j = 0; j < coupons.size(); j++) {
                JSONObject coupon = coupons.optJSONObject(j);
                // 券校验可用情况
                if ("0".equals(coupon.optString("useStatus"))) {
                    TqAcewilCouponCache tacc = new TqAcewilCouponCache();
                    Map<String, Object> ccMap = checkCoupon(zdbh, coupon, fkje);
                    String couponId = coupon.optString("cardId");
                    String coupontype = "";
                    double couponTotalmoney = 0;
                    if(coupon.optInt("couponType") == 0 && coupon.optInt("discountUnit") == 1){
                        coupontype = "CASHCOUPON";
                        couponTotalmoney = coupon.optDouble("discountAmount", 0);
                    }
                    if(coupon.optInt("couponType") == 2){
                        coupontype = "DISHCOUPON";
                    }
                    if(coupon.optInt("couponType") == 0 && coupon.optInt("discountUnit") == 2){
                        coupontype = "DISHDISCOUNTCOUPON";
                        couponTotalmoney = coupon.optDouble("discountAmount", 0);
                    }

                    tacc.setZdbh(zdbh);
                    tacc.setScancode(couponId);
                    tacc.setDatatype("QIMAI");
                    tacc.setUseok(0);
                    tacc.setYhfsid(-133);// 写死id？还是通过编号查询？
                    tacc.setCoupontype(coupontype);
                    tacc.setCouponcode(couponId);
                    tacc.setCouponprice(couponTotalmoney);
                    tacc.setFkfs(fkfsid);
                    tacc.setRemark(coupon.toString());
                    tacc.setIs_usable(Integer.parseInt(ccMap.get("code").toString()));
                    tacc.setCardcode(detailInfo.optString("cardNo"));
                    tacc.setTemplateid(coupon.optString("templateId"));
                    tacc.setUseablemsg(ccMap.get("useablemsg").toString());
                    tacc.setCouponname(coupon.optString("title"));

                    tacc.setCoupon_totalmoney(couponTotalmoney);
                    acewillCouponTempMapper.insert(tacc);

                    JSONObject couponObj = new JSONObject();
                    couponObj.put("title", coupon.optString("title"));
                    couponObj.put("couponid", couponId);
                    couponObj.put("deno", coupon.optDouble("discountAmount", 0)); // 面值
                    couponObj.put("coupontype", coupontype); // 类型
                    couponObj.put("isusable", Integer.parseInt(ccMap.get("code").toString())); // 可用状态
                    couponObj.put("useablemsg", ccMap.get("useablemsg").toString()); // 不可用原因

                    couponList.add(couponObj);

                }
            }
            card.put("coupons", couponList);
            dataList.add(card);

            result.setData(dataList);
            result.setCode(Constant.CODE_SUCCESS);
            result.setMsg("查询会员信息成功");
            result.setSuccess(true);
        } else {
            result.setCode(-1);
            result.setMsg(ua.optString("message"));
            result.setSuccess(false);
        }

    }

    /**
     * 校验券的可用状态
     *
     * 1.启用金额
     * 2.账单是否包含菜品券菜品校验
     * 3.本门店是否可用
     * 4.可用日期校验
     * 5.可用星期校验
     * 6.可用时段
     *
     * @param coupon 券信息json数据
     * @return 0:可用，1：不可用
     */
    public Map<String, Object> checkCoupon(String zdbh, JSONObject coupon, double fkje) {
        Map<String, Object> ccMap = new HashMap<String, Object>();
        ccMap.put("code", 1);

        boolean ifSale = DateUtil.ifSale(coupon.optString("beginAt"), coupon.optString("endAt"));
//		String weekStr = checkWeek2(coupon.optJSONArray("use_week_day"));
//		String timeStr = checkTime(coupon.optJSONArray("valid_use_times"));
        String thresholdType = coupon.optString("thresholdType");
        if ("2".equals(thresholdType) && coupon.optDouble("useThreshold") > fkje) {// 1.校验启用金额
            ccMap.put("useablemsg", "该券启用金额不满足，账单金额需达到" + coupon.optDouble("useThreshold") + "元！");
            return ccMap;
        }
//		if (2 == coupon.optInt("type")) {// 2.校验账单明细中是否有可用菜品
//			JSONArray products = coupon.optJSONArray("products");
//			if (products.size() > 0) {
//				int rc = 0;
//				for (int i = 0; products.size() > i; i++) {
//					String cmbh = products.optString(i);
//					WdDishVo wdv = acewillCouponTempMapper.getWdDish(zdbh, cmbh);
//					if (null != wdv) {
//						rc = rc + 1;
//					}
//				}
//				if (rc == 0) {
//					ccMap.put("useablemsg", "本单无可核销菜品！");
//					return ccMap;
//				}
//			} else {
//				ccMap.put("useablemsg", "该券没有包含任何菜品，请联系相关人员进行确认！");
//				return ccMap;
//			}
//		}
//		if (5 == coupon.optInt("type")) {
//			String productsExtStr = coupon.optString("products_ext", "");
//			String[] productsExtList = productsExtStr.split(",");
//			JSONArray productsExt = new JSONArray();
//			if(productsExtStr.length() > 0){
//				productsExt = JSONArray.fromObject(productsExtList);
//			}
//			JSONArray productsTemp = coupon.optJSONArray("products");
//
//			coupon.put("products", productsExt);
//			coupon.put("products_ext", productsTemp);
//			coupon.put("mix_use", false);
//		}
//		if (!checkSids(coupon, this_shop_id)) {// 3.本门店是否可用
//			ccMap.put("useablemsg", "该券不可在本门店使用！");
//			return ccMap;
//		}
        if (!ifSale) {// 4.可用日期校验
            ccMap.put("useablemsg", "当前日期不在可用日期范围内！");
            return ccMap;
        }
//		if (!weekB) {// 5.可用星期校验
//			return 1;
//		}
//		if (!"0".equals(weekStr)) { // 5.可用星期校验
//			weekStr = weekStr.substring(1, weekStr.length());
//			ccMap.put("msg", "该券仅《" + weekStr + "》可用！");
//			return ccMap;
//		}
//		if (!"0".equals(timeStr)) {// 6.可用时段
//			ccMap.put("useablemsg", "当前时间不在可用时段范围内！");
//			return ccMap;
//		}
        ccMap.put("code", 0);
        ccMap.put("useablemsg", "OK");
        return ccMap;
    }

    // 校验星期是否可用
    public boolean checkWeek1(JSONArray use_week_day) {
        boolean b = false;
        int week = DateUtil.dayForWeekToDate(new Date(), 0);
        List list = JSONArray.toList(use_week_day);
        if (list.indexOf(week + "") != -1) {
            b = true;
        }
        return b;
    }

    public String checkWeek2(JSONArray use_week_day) {
        int week = DateUtil.dayForWeekToDate(new Date(), 0);
        String weekStr = "";
        for (int i = 0; i < use_week_day.size(); i++) {
            if (week == use_week_day.getInt(i)) {
                weekStr = "0";
                break;
            }
            switch (use_week_day.getInt(i)) {
                case 1:
                    weekStr += ",星期一";
                    break;
                case 2:
                    weekStr += ",星期二";
                    break;
                case 3:
                    weekStr += ",星期三";
                    break;
                case 4:
                    weekStr += ",星期四";
                    break;
                case 5:
                    weekStr += ",星期五";
                    break;
                case 6:
                    weekStr += ",星期六";
                    break;
                case 0:
                    weekStr += ",星期日";
                    break;
            }
        }
        return weekStr;
    }

    // 校验时间是否可用
    public String checkTime(JSONArray valid_use_times) {
        String timeStr = "0";
        for (int i = 0; i < valid_use_times.size(); i++) {
            JSONObject timeObj = valid_use_times.optJSONObject(i);
            String start = timeObj.optString("start");
            String end = timeObj.optString("end");
            if (DateUtil.ifUsableTimes(start, end)) {
                timeStr = "0";
                break;
            }
            timeStr += "," + start + "-" + end;
        }
        return timeStr;
    }

    // 校验当前门店是否可用
    public boolean checkSids(JSONObject coupon, String this_shop_id) {
        boolean b = false;
        JSONArray sids = coupon.optJSONArray("sids");
        for (int i = 0; i < sids.size(); i++) {
            if (this_shop_id.equals(sids.getString(i))) {
                b = true;
                break;
            }
        }
        return b;
    }

    /**
     * 根据账单号查询缓存中的微生活会员信息
     *
     * @param
     * @param result
     */
    public void cacheQimaiMember(Data data, Data result) {
        Map<String, Object> map = ReqDataUtil.getDataMap(data);
        String zdbh = ParamUtil.getStringValue(map, "zdbh", false, null);// 帐单编号
        List<JSONObject> dataList = new ArrayList<JSONObject>();

        List<TqMemberInfo> amis = acewillMemberinfoMapper.getAcewillMembers(zdbh, "");
        if(amis.size() == 0){
            result.setCode(-1);
            result.setMsg("无会员缓存信息，请查询会员");
            result.setSuccess(false);
            return;
        }

        for (int i = 0; i < amis.size(); i++) {
            TqMemberInfo ami = amis.get(i);
            JSONObject res = JSONObject.fromObject(ami.getRemark());
            JSONObject detailInfo = res.optJSONObject("detailInfo");
            JSONObject card = new JSONObject();
            card.put("cno", detailInfo.optString("cardNo")); // 卡号
            card.put("balance", ami.getBalance()); // 卡余额
            card.put("name", detailInfo.optString("username")); // 用户名称
            card.put("type", "wx"); // 卡类型，wx:微信卡 dp:点评卡 ph:实体卡 actual:实体卡
            card.put("in_effect", true); // 卡是否有效
            card.put("grade_name", detailInfo.optString("memberLevelName")); // 卡等级名称
            card.put("is_vip_price", ami.getCanvipprice()); // 是否使用会员价
            card.put("credit",ami.getCredit()); // 用户积分
            card.put("use_credit", true); // 是否可以使用积分消费

            if (res.containsKey("use_max_credit_money")) {
                card.put("use_max_credit_money", res.optInt("use_max_credit_money")); // (单位：元) 积分可抵扣的最大金额
            }

            card.put("credit_deduct", 1); // 多少积分抵1元,默认1积分1元
            if (res.containsKey("pointsRule")) {
                JSONObject pointsRule = res.optJSONObject("pointsRule");
                if (null != pointsRule && pointsRule.containsKey("points_rule")) {
                    card.put("credit_deduct", pointsRule.optInt("points_rule"));
                }
            }

            int usecount = 0;

            List<JSONObject> couponList = new ArrayList<JSONObject>();
            List<TqAcewilCouponCache> coupons = acewillCouponTempMapper.getAcewillCoupons(zdbh, detailInfo.optString("cardNo"));
            for (int j = 0; j < coupons.size(); j++) {
                TqAcewilCouponCache coupon = coupons.get(j);
                if(coupon.getUseok() == 0){
//					JSONObject couponJson = JSONObject.fromObject(coupon.getRemark());
                    JSONObject couponObj = new JSONObject();
                    couponObj.put("title", coupon.getCouponname());
                    couponObj.put("couponid", coupon.getCouponcode());
                    couponObj.put("deno", coupon.getCouponprice()); // 面值
                    couponObj.put("coupontype", coupon.getCoupontype()); // 类型
                    couponObj.put("isusable", coupon.getIs_usable()); // 可用状态
                    couponObj.put("useablemsg", coupon.getUseablemsg()); // 不可用原因

                    couponList.add(couponObj);
                }



                if(coupon.getUseok() == 1){
                    usecount = usecount + 1;
                }

            }
            card.put("usecount", usecount);
            card.put("coupons", couponList);
            dataList.add(card);
        }
        result.setData(dataList);
        result.setCode(Constant.CODE_SUCCESS);
        result.setMsg("查询会员信息成功");
        result.setSuccess(true);
    }

    /**
     * 根据账单号清除该账单关联的会员信息
     * @param param
     * @param result
     */
    public void refundQimaiMember(Data param, Data result) {

        Map<String, Object> map = ReqDataUtil.getDataMap(param);
        String zdbh = ParamUtil.getStringValue(map, "zdbh", false, null);// 帐单编号
        String jtbh = ParamUtil.getStringValue(map, "jtbh", false, null);// 机台编号

        result.setMsg("请先取消会员价，再进行操作,本次操作仅清除会员付款信息");

        if (acewillCouponTempMapper.getIsVipPriceCount(zdbh) <= 0) {
            acewillCouponTempMapper.delTwct(zdbh);
            acewillCouponTempMapper.delTmi(zdbh);
            tqZdkMapper.delTqWdkCouponTemp(zdbh, "");
            result.setMsg("清除成功");
        } else {
            List<TqMemberInfo> amis = acewillMemberinfoMapper.getAcewillMembers(zdbh, "");
            TqMemberInfo tmi = new TqMemberInfo();
            if (amis.size() > 0) {
                tmi = amis.get(0);
            } else {
                result.setCode(-1);
                result.setMsg("会员卡不存在，请重新查询");
                result.setSuccess(false);
            }
            double sub_balance = ArithUtil.mul(acewillMemberinfoMapper.findBalanceAmount(zdbh, "1973"), 100);
            double sub_credit = acewillMemberinfoMapper.findBalanceAmount(zdbh, "1972");
            // 获取卡余额
            double balance = tmi.getBalance();
            double credit = tmi.getCredit();
            // 将取消的支付金额加回到卡余额
            balance = ArithUtil.add(balance, sub_balance);
            JSONObject res = JSONObject.fromObject(tmi.getRemark());
            if (res.containsKey("credit_deduct")) {
                sub_credit = ArithUtil.mul(sub_credit, res.optInt("credit_deduct"));
            }
            credit = ArithUtil.add(credit, sub_credit);
            acewillMemberinfoMapper.updateToBalance(zdbh, tmi.getScancode(), balance, credit);

            tqZdkMapper.delTqWdkCouponTemp(zdbh, "");
            acewillCouponTempMapper.updateUsaByUseok(zdbh, "", 0);
        }

        acewillCouponTempMapper.delFkls(zdbh, "FKSX_HYK");
        acewillCouponTempMapper.delDsfls(zdbh, "QIMAI");
        tqZdkMapper.delTpd(zdbh, -133, "");
        tqZdkMapper.delTpd(zdbh, -135, "");
        tqZdkMapper.delTpd(zdbh, -136, "");
        tqZdkMapper.delTpd(zdbh, -137, "");
        List<Integer> rwid3 = acewillCouponTempMapper.getRefundRwid(zdbh, -133);
        List<Integer> rwid5 = acewillCouponTempMapper.getRefundRwid(zdbh, -135);
        List<Integer> rwid6 = acewillCouponTempMapper.getRefundRwid(zdbh, -136);
        List<Integer> rwid7 = acewillCouponTempMapper.getRefundRwid(zdbh, -137);

        List<Integer> rwids = new ArrayList<Integer>();
        rwids.addAll(rwid3);
        rwids.addAll(rwid5);
        rwids.addAll(rwid6);
        rwids.addAll(rwid7);

//		List<Integer> rwids = acewillCouponTempMapper.getRefundRwid(zdbh, -133);

        for (int rwid : rwids) {
            tqZdkMapper.cancelJd(zdbh, rwid, 1, jtbh, "", 1);
        }
        tqZdkMapper.zRtr(zdbh);
        tqZdkMapper.findCalcMoney(zdbh);
        double open_amount = 0.00;
        double yfje = tqZdkMapper.getFkje(zdbh);
        double zdje = tqZdkMapper.getZdje(zdbh);
        open_amount = DoubleUtil.sub(zdje, yfje);
        PayResultData payResultData = new PayResultData();
        payResultData.setOpen_amount(open_amount + "");
        payResultData.setFkls(tqZdkMapper.getFklsList(zdbh));
        List<CouponRunningWater> crw = new ArrayList<CouponRunningWater>();
        List<CouponRunningWater> crw4 = tqZdkMapper.getCouponlsList(zdbh, "4");
        List<CouponRunningWater> crw6 = tqZdkMapper.getCouponlsList(zdbh, "6");
        crw.addAll(crw4);
        crw.addAll(crw6);
        payResultData.setCouponls(crw);
        List<JSONObject> dataList = new ArrayList<JSONObject>();
        dataList.add(JSONObject.fromObject(GsonUtil.GsonString(payResultData)));

        result.setCode(0);
        result.setData(dataList);
        result.setSuccess(true);
    }

    // 预消费
    public void qimaiPreview(Data param, Data result) {
        Map<String, Object> map = ReqDataUtil.getDataMap(param);
        String zdbh = ParamUtil.getStringValue(map, "zdbh", false, null);// 帐单编号
        String skjh = ParamUtil.getStringValue(map, "skjh", false, null);// 收款机号

        TqZdk zdk = tqZdkMapper.getZdk(zdbh);
        if ("ZDSX_YJ".equals(zdk.getJzsx())) {
            result.setCode(-102);
            result.setMsg("账单《" + zdbh + "》已关闭，请返回首页查询该帐单！");
            result.setSuccess(false);
            return;
        }

        TqMemberInfo tmi = acewillMemberinfoMapper.getAcewillScancode(zdbh);
        if (null == tmi) {
            result.setCode(0);
            result.setMsg("无会员使用记录，无需验证");
            result.setSuccess(true);
            return;
        } else {
            List<JSONObject> dataList = new ArrayList<JSONObject>();
            JSONObject remark = new JSONObject();
            remark.put("verify_password", false);
            remark.put("verify_sms", false);
            dataList.add(remark);
            result.setData(dataList);
            result.setCode(1);
            result.setMsg("预消费成功，请根据验证方式进行验证");
            result.setSuccess(true);
            return;
        }
    }

    // 提交消费
    public void qimaiCommit(Data param, Data result) {
        Map<String, Object> map = ReqDataUtil.getDataMap(param);
        String zdbh = ParamUtil.getStringValue(map, "zdbh", false, null);// 帐单编号
        String skjh = ParamUtil.getStringValue(map, "skjh", false, null);// 帐单编号

//		List<JSONObject> dataList = new ArrayList<JSONObject>();
        TsPsjgsdk tsPsjgsdk = tqZdkMapper.getJg();// 机构数据
        List<TqMemberInfo> amis = acewillMemberinfoMapper.getAcewillMembers(zdbh, "");

        Map<String, String> dsflsMap = acewillCouponTempMapper.findZddsflsk(zdbh, "QIMAI", "");
        String bizId = "";
        if (null == dsflsMap || null == dsflsMap.get("qrcode")) {
            bizId = tsPsjgsdk.getJgxh() + "_" + zdbh + "_" + new Date().getTime();
        } else {
            bizId = dsflsMap.get("qrcode");
        }

        if (null != dsflsMap && "COMMITSUCCESS".equals(dsflsMap.get("yl3"))) {
            result.setCode(0);
            result.setMsg("提交消费成功");
            result.setSuccess(true);
            return;
        }

//		String bizId = tsPsjgsdk.getJgxh() + "_" + zdbh + "_" + new Date().getTime();
        TqMemberInfo ami = amis.get(0);
        String customerId = ami.getScancode();

        Map<String, String> bbrqMap = tqJtztkMapper.findBbrq();
        String bbrq = DateUtil.getNowDateYYDDMM();
        if (null != bbrqMap && bbrqMap.size() != 0) {
            bbrq = bbrqMap.get("bbrq");
        }
        acewillCouponTempMapper.delDsfls(zdbh, "QIMAI");
        acewillCouponTempMapper.insertTqZddsflsk(skjh, zdbh, DateUtil.parseDate(bbrq), new Date(), "QIMAI", bizId, "07", 0, "COMMITSTART", "");

        List<TqAcewilCouponCache> taccs = acewillCouponTempMapper.getCouponByUseok(zdbh, 1);
        JSONArray couponsCardList = new JSONArray();
        for (TqAcewilCouponCache tacc : taccs) {
            couponsCardList.add(tacc.getCouponcode());
        }
        double balanceAmount = acewillMemberinfoMapper.findBalanceAmount(zdbh, "1973");
        double balanceYhAmount = acewillMemberinfoMapper.findBalanceYhAmount(zdbh);
        double walletAmount = ArithUtil.add(balanceAmount, balanceYhAmount);


        double pointsAmount = acewillMemberinfoMapper.findBalanceAmount(zdbh, "1972");
        double pointsYhAmount = acewillMemberinfoMapper.findPointsYhAmount(zdbh);
        double points = ArithUtil.add(pointsAmount, pointsYhAmount);

//		double points = pointsAmount;

        if(StringUtils.isNotEmpty(ami.getRemark())){
            JSONObject res = JSONObject.fromObject(ami.getRemark());
            if (res.containsKey("pointsRule")) {
                JSONObject pointsRule = res.optJSONObject("pointsRule");
                if (null != pointsRule && pointsRule.containsKey("points_rule")) {
                    points = ArithUtil.mul(pointsAmount, pointsRule.optInt("points_rule"));
                }
            }
        }

        JSONObject params = new JSONObject();
        params.put("bizId", bizId);
        params.put("customerId", customerId); // 账户名
        params.put("couponsCardList", couponsCardList);
        params.put("type", "");
        params.put("amount", walletAmount + "");
        params.put("points", points);
        params.put("multiMark", tsPsjgsdk.getJgbh());

        JSONObject obj = new JSONObject();
        obj.put("Action", "communal.user.consume");
        obj.put("Params", params);

        CommApiData dataR = new CommApiData();
        commApiService.qmMemberRequest(dataR, obj);
        String rStr = "";
        JSONObject ua = new JSONObject();
        if (0 == dataR.getCode()) {
            rStr = dataR.getData().toString();
            ua = JSONObject.fromObject(rStr);
            if ("0".equals(ua.optString("code"))) {
                JSONObject resObj = ua.optJSONObject("data");
                String deal_id = resObj.optString("bizId");

                double changeRechargeAmount = resObj.optDouble("change_recharge_amount", 0); // 扣减充值金额 单位元
                double changeGiftAmount = resObj.optDouble("change_gift_amount", 0);// 扣减赠送金额 单位元

                double shopBalance = resObj.optDouble("shopBalance", 0); // 门店记账金额/元
                double storeBalance = resObj.optDouble("storeBalance", 0);// 总部记账金额/元,  如果拆分，作为优惠

                double stored_pay = ArithUtil.add(changeRechargeAmount, changeGiftAmount);
                double stored_give_pay = changeGiftAmount;

                acewillCouponTempMapper.updateZddsflsk(zdbh, deal_id, "COMMITSUCCESS", "", "QIMAI");


                if ("1".equals(InitDataListener.ggcsMap.get("POS_MEMBALANCE_USEDIS")) || "3".equals(InitDataListener.ggcsMap.get("POS_MEMBALANCE_USEDIS"))) {
                    Dish dsfyhB = new Dish();
                    if ("3".equals(InitDataListener.ggcsMap.get("POS_MEMBALANCE_USEDIS"))) {
                        dsfyhB = tqZdkMapper.getDsfyh("85");
                        stored_pay = ArithUtil.add(shopBalance, storeBalance);
                        stored_give_pay = storeBalance;
                    } else if ("1".equals(InitDataListener.ggcsMap.get("POS_MEMBALANCE_USEDIS"))) {
                        dsfyhB = tqZdkMapper.getDsfyh("87");
                    }
                    double fklsPay = acewillMemberinfoMapper.findBalanceAmount(zdbh, "1973");
                    if(stored_pay == fklsPay){
                        List<TqFklslsk> balanceList = acewillMemberinfoMapper.findBalanceList(zdbh, "1973");
                        if (null != dsfyhB && balanceList.size() > 0 && stored_give_pay != 0) {
                            acewillDiscount(zdbh, dsfyhB, balanceList, stored_give_pay);
                        }
                    }
                }
                if ("2".equals(InitDataListener.ggcsMap.get("POS_MEMBALANCE_USEDIS"))) {
                    String acewillDiscount = InitDataListener.ggcsMap.get("ACEWILLDISCOUNT");
                    if (null != acewillDiscount) {
                        if (isNumeric(acewillDiscount) && Integer.parseInt(acewillDiscount) > 0 && Integer.parseInt(acewillDiscount) <= 100) {
                            List<TqFklslsk> balanceList = acewillMemberinfoMapper.findBalanceList(zdbh, "1973");
                            Dish dsfyhB = tqZdkMapper.getDsfyh("85");
                            double scale = Integer.parseInt(acewillDiscount);
                            double scale_pay = ArithUtil.sub(stored_pay, ArithUtil.mul(stored_pay, ArithUtil.div(scale, 100, 2)));
                            scale_pay = ArithUtil.round(scale_pay, 2);
                            if (null != dsfyhB && balanceList.size() > 0 && scale_pay != 0) {
                                acewillDiscount(zdbh, dsfyhB, balanceList, scale_pay);
                            }
                        } else {
                            LOGGER.error("企迈储值未拆分:拆分比例=" + acewillDiscount);
                        }
                    } else {
                        LOGGER.error("企迈储值未拆分:未设置拆分比例");
                    }
                }

                if ("1".equals(InitDataListener.ggcsMap.get("POS_MEMCREDIT_USEDIS"))) {
                    List<TqFklslsk> creditList = acewillMemberinfoMapper.findBalanceList(zdbh, "1972");
                    Dish dsfyhC = tqZdkMapper.getDsfyh("86");
                    if (null != dsfyhC && creditList.size() > 0) {
                        double credit_give_pays = acewillMemberinfoMapper.findBalanceAmount(zdbh, "1972");
                        acewillDiscount(zdbh, dsfyhC, creditList, credit_give_pays);
                    }
                }

                acewillMemberinfoMapper.updateZdkByCwlxbh(zdbh, "4");

                double totalAmount = resObj.optDouble("total_amount", 0);// 扣减赠送金额 单位元
                double balance = ArithUtil.add(stored_pay, totalAmount);;
                acewillCouponTempMapper.insertAcewilDealdetails(ami.getScancode(), zdbh, stored_pay, balance, points, DateUtil.parseDate(bbrq), "0000", skjh, 1,null);

                result.setCode(0);
                result.setMsg("提交消费成功");
                result.setSuccess(true);
            } else if ("410".equals(ua.optString("code"))) {
                result.setCode(-1);
                result.setMsg(ua.optString("message"));
                result.setSuccess(false);
            } else {
                result.setCode(-1);
                result.setMsg(ua.optString("message"));
                result.setSuccess(false);
            }
        } else {
            result.setCode(-1);
            result.setMsg("企迈会员消费提交失败:" + dataR.getMsg());
            result.setSuccess(false);
            return;
        }
    }

    public void acewillDiscount(String zdbh, Dish dsfyh, List<TqFklslsk> fklsList, double stored_give_pays) {
        // 拆分部分
        for (TqFklslsk fkls : fklsList) {
            UseYhfsParam useYhfsParam = new UseYhfsParam();
            useYhfsParam.setBillid(zdbh);
            useYhfsParam.setOpType(91002);
            useYhfsParam.setYhfsId(Integer.parseInt(dsfyh.getYhfsid()));
            useYhfsParam.setJzid(fkls.getJzid());
            useYhfsParam.setSkjh(fkls.getSkjh());// 机号
            useYhfsParam.setJgtxbh(InitDataListener.organVo.getJgtxbh());// 价格体系编号
            useYhfsParam.setDisAmount(new BigDecimal(stored_give_pays));
            useYhfsParam.setBbrq(fkls.getJzbbrq());
            useYhfsParam.setInnerCalMoney(true);// 由接口内部使用均摊及账单金额重计算
            useYhfsApiService.CommUseYhfs(useYhfsParam);
        }
    }

    public boolean isNumeric(String str) {
        for (int i = str.length(); --i >= 0;) {
            if (!Character.isDigit(str.charAt(i))) {
                return false;
            }
        }
        return true;
    }


}
