package com.tzx.commapi.rest.vo;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class FdzdmxkInfo implements Serializable {
    private static final long serialVersionUID = -2953908822136125643L;

    @Column(name = "RWID")
    private Integer rwid;

    @Column(name = "YRWID")
    private Integer yrwid;

    @Column(name = "KDZDBH")
    private String kdzdbh;

    @Column(name = "JZZDBH")
    private String jzzdbh;

    @Column(name = "CLMXID")
    private Integer clmxid;

    @Column(name = "CMID")
    private Integer cmid;

    @Column(name = "CMBH")
    private String cmbh;

    @Column(name = "CMMC1")
    private String cmmc1;

    @Column(name = "CMMC2")
    private String cmmc2;

    @Column(name = "DWBH")
    private String dwbh;

    @Column(name = "CMGGID")
    private Integer cmggid;

    @Column(name = "CMGGBH")
    private String cmggbh;

    @Column(name = "YZWBH")
    private String yzwbh;

    @Column(name = "ZWBH")
    private String zwbh;

    @Column(name = "TCFS")
    private String tcfs;

    @Column(name = "TCBL")
    private double tcbl;

    @Column(name = "TCJE")
    private double tcje;

    @Column(name = "FZSL")
    private double fzsl;

    @Column(name = "FZJE")
    private double fzje;

    @Column(name = "ZDSJ")
    private Integer zdsj;

    @Column(name = "XDH")
    private String xdh;

    @Column(name = "XDHSHRY")
    private String xdhshry;

    @Column(name = "FWYH")
    private String fwyh;

    @Column(name = "CBDJ")
    private BigDecimal cbdj;

    @Column(name = "CMDJ")
    private BigDecimal cmdj;

    @Column(name = "CMSL")
    private Integer cmsl;

    @Column(name = "CMJE")
    private BigDecimal cmje;

    @Column(name = "SJJE")
    private BigDecimal sjje;

    @Column(name = "YHJE")
    private BigDecimal yhje;

    @Column(name = "DPZKJE")
    private BigDecimal dpzkje;

    @Column(name = "ZRJE")
    private BigDecimal zrje;

    @Column(name = "ZKZT")
    private String zkzt;

    @Column(name = "XLZKZT")
    private String xlzkzt;

    @Column(name = "ZKL")
    private Integer zkl;

    @Column(name = "YHFSID")
    private Integer yhfsid;

    @Column(name = "YHFSBH")
    private String yhfsbh;

    @Column(name = "YHFS")
    private String yhfs;

    @Column(name = "XLID")
    private Integer xlid;

    @Column(name = "XLBH")
    private String xlbh;

    @Column(name = "CMSX")
    private String cmsx;

    @Column(name = "WDBZ")
    private String wdbz;

    @Column(name = "CDBJ")
    private String cdbj;

    @Column(name = "TSZT")
    private String tszt;

    @Column(name = "QCZT")
    private String qczt;

    @Column(name = "TCID")
    private Integer tcid;

    @Column(name = "TCDCH")
    private Integer tcdch;

    @Column(name = "FZDCH")
    private Integer fzdch;

    @Column(name = "TCSFGH")
    private String tcsfgh;

    @Column(name = "TMBJ")
    private Date tmbj;

    @Column(name = "DCXH")
    private Integer dcxh;

    @Column(name = "FSBBRQ")
    private Date fsbbrq;

    @Column(name = "JZBBRQ")
    private Date jzbbrq;

    @Column(name = "FSBCID")
    private Integer fsbcid;

    @Column(name = "JZBCID")
    private Integer jzbcid;

    @Column(name = "FSSKJH")
    private String fsskjh;

    @Column(name = "JZSKJH")
    private String jzskjh;

    @Column(name = "KWBZ")
    private String kwbz;

    @Column(name = "CPBH")
    private String cpbh;

    @Column(name = "DCBZ")
    private String dcbz;

    @Column(name = "ZWH")
    private String zwh;

    @Column(name = "CSBH")
    private String csbh;

    @Column(name = "PQHM")
    private String pqhm;

    @Column(name = "SYYHFKFSID")
    private Integer syyhfkfsid;

    @Column(name = "SCBJ")
    private Integer scbj;

    @Column(name = "XSMS")
    private String xsms;

    @Column(name = "CJGQBJ")
    private Integer cjgqbj;

    @Column(name = "CPBJ")
    private Integer cpbj;

    @Column(name = "YHYYBH")
    private String yhyybh;

    @Column(name = "SFXSMX")
    private String sfxsmx;

    @Column(name = "MEMO")
    private String memo;

    @Column(name = "SCQRBJ")
    private Integer scqrbj;

    private Integer newRwid;

    public Integer getRwid() {
        return rwid;
    }

    public void setRwid(Integer rwid) {
        this.rwid = rwid;
    }

    public Integer getYrwid() {
        return yrwid;
    }

    public void setYrwid(Integer yrwid) {
        this.yrwid = yrwid;
    }

    public String getKdzdbh() {
        return kdzdbh;
    }

    public void setKdzdbh(String kdzdbh) {
        this.kdzdbh = kdzdbh;
    }

    public String getJzzdbh() {
        return jzzdbh;
    }

    public void setJzzdbh(String jzzdbh) {
        this.jzzdbh = jzzdbh;
    }

    public Integer getClmxid() {
        return clmxid;
    }

    public void setClmxid(Integer clmxid) {
        this.clmxid = clmxid;
    }

    public Integer getCmid() {
        return cmid;
    }

    public void setCmid(Integer cmid) {
        this.cmid = cmid;
    }

    public String getCmbh() {
        return cmbh;
    }

    public void setCmbh(String cmbh) {
        this.cmbh = cmbh;
    }

    public String getCmmc1() {
        return cmmc1;
    }

    public void setCmmc1(String cmmc1) {
        this.cmmc1 = cmmc1;
    }

    public String getCmmc2() {
        return cmmc2;
    }

    public void setCmmc2(String cmmc2) {
        this.cmmc2 = cmmc2;
    }

    public String getDwbh() {
        return dwbh;
    }

    public void setDwbh(String dwbh) {
        this.dwbh = dwbh;
    }

    public Integer getCmggid() {
        return cmggid;
    }

    public void setCmggid(Integer cmggid) {
        this.cmggid = cmggid;
    }

    public String getCmggbh() {
        return cmggbh;
    }

    public void setCmggbh(String cmggbh) {
        this.cmggbh = cmggbh;
    }

    public String getYzwbh() {
        return yzwbh;
    }

    public void setYzwbh(String yzwbh) {
        this.yzwbh = yzwbh;
    }

    public String getZwbh() {
        return zwbh;
    }

    public void setZwbh(String zwbh) {
        this.zwbh = zwbh;
    }

    public String getTcfs() {
        return tcfs;
    }

    public void setTcfs(String tcfs) {
        this.tcfs = tcfs;
    }

    public double getTcbl() {
        return tcbl;
    }

    public void setTcbl(double tcbl) {
        this.tcbl = tcbl;
    }

    public double getTcje() {
        return tcje;
    }

    public void setTcje(double tcje) {
        this.tcje = tcje;
    }

    public double getFzsl() {
        return fzsl;
    }

    public void setFzsl(double fzsl) {
        this.fzsl = fzsl;
    }

    public double getFzje() {
        return fzje;
    }

    public void setFzje(double fzje) {
        this.fzje = fzje;
    }

    public Integer getZdsj() {
        return zdsj;
    }

    public void setZdsj(Integer zdsj) {
        this.zdsj = zdsj;
    }

    public String getXdh() {
        return xdh;
    }

    public void setXdh(String xdh) {
        this.xdh = xdh;
    }

    public String getXdhshry() {
        return xdhshry;
    }

    public void setXdhshry(String xdhshry) {
        this.xdhshry = xdhshry;
    }

    public String getFwyh() {
        return fwyh;
    }

    public void setFwyh(String fwyh) {
        this.fwyh = fwyh;
    }

    public BigDecimal getCbdj() {
        return cbdj;
    }

    public void setCbdj(BigDecimal cbdj) {
        this.cbdj = cbdj;
    }

    public BigDecimal getCmdj() {
        return cmdj;
    }

    public void setCmdj(BigDecimal cmdj) {
        this.cmdj = cmdj;
    }

    public Integer getCmsl() {
        return cmsl;
    }

    public void setCmsl(Integer cmsl) {
        this.cmsl = cmsl;
    }

    public BigDecimal getCmje() {
        return cmje;
    }

    public void setCmje(BigDecimal cmje) {
        this.cmje = cmje;
    }

    public BigDecimal getSjje() {
        return sjje;
    }

    public void setSjje(BigDecimal sjje) {
        this.sjje = sjje;
    }

    public BigDecimal getYhje() {
        return yhje;
    }

    public void setYhje(BigDecimal yhje) {
        this.yhje = yhje;
    }

    public BigDecimal getDpzkje() {
        return dpzkje;
    }

    public void setDpzkje(BigDecimal dpzkje) {
        this.dpzkje = dpzkje;
    }

    public BigDecimal getZrje() {
        return zrje;
    }

    public void setZrje(BigDecimal zrje) {
        this.zrje = zrje;
    }

    public String getZkzt() {
        return zkzt;
    }

    public void setZkzt(String zkzt) {
        this.zkzt = zkzt;
    }

    public String getXlzkzt() {
        return xlzkzt;
    }

    public void setXlzkzt(String xlzkzt) {
        this.xlzkzt = xlzkzt;
    }

    public Integer getZkl() {
        return zkl;
    }

    public void setZkl(Integer zkl) {
        this.zkl = zkl;
    }

    public Integer getYhfsid() {
        return yhfsid;
    }

    public void setYhfsid(Integer yhfsid) {
        this.yhfsid = yhfsid;
    }

    public String getYhfsbh() {
        return yhfsbh;
    }

    public void setYhfsbh(String yhfsbh) {
        this.yhfsbh = yhfsbh;
    }

    public String getYhfs() {
        return yhfs;
    }

    public void setYhfs(String yhfs) {
        this.yhfs = yhfs;
    }

    public Integer getXlid() {
        return xlid;
    }

    public void setXlid(Integer xlid) {
        this.xlid = xlid;
    }

    public String getXlbh() {
        return xlbh;
    }

    public void setXlbh(String xlbh) {
        this.xlbh = xlbh;
    }

    public String getCmsx() {
        return cmsx;
    }

    public void setCmsx(String cmsx) {
        this.cmsx = cmsx;
    }

    public String getWdbz() {
        return wdbz;
    }

    public void setWdbz(String wdbz) {
        this.wdbz = wdbz;
    }

    public String getCdbj() {
        return cdbj;
    }

    public void setCdbj(String cdbj) {
        this.cdbj = cdbj;
    }

    public String getTszt() {
        return tszt;
    }

    public void setTszt(String tszt) {
        this.tszt = tszt;
    }

    public String getQczt() {
        return qczt;
    }

    public void setQczt(String qczt) {
        this.qczt = qczt;
    }

    public Integer getTcid() {
        return tcid;
    }

    public void setTcid(Integer tcid) {
        this.tcid = tcid;
    }

    public Integer getTcdch() {
        return tcdch;
    }

    public void setTcdch(Integer tcdch) {
        this.tcdch = tcdch;
    }

    public Integer getFzdch() {
        return fzdch;
    }

    public void setFzdch(Integer fzdch) {
        this.fzdch = fzdch;
    }

    public String getTcsfgh() {
        return tcsfgh;
    }

    public void setTcsfgh(String tcsfgh) {
        this.tcsfgh = tcsfgh;
    }

    public Date getTmbj() {
        return tmbj;
    }

    public void setTmbj(Date tmbj) {
        this.tmbj = tmbj;
    }

    public Integer getDcxh() {
        return dcxh;
    }

    public void setDcxh(Integer dcxh) {
        this.dcxh = dcxh;
    }

    public Date getFsbbrq() {
        return fsbbrq;
    }

    public void setFsbbrq(Date fsbbrq) {
        this.fsbbrq = fsbbrq;
    }

    public Date getJzbbrq() {
        return jzbbrq;
    }

    public void setJzbbrq(Date jzbbrq) {
        this.jzbbrq = jzbbrq;
    }

    public Integer getFsbcid() {
        return fsbcid;
    }

    public void setFsbcid(Integer fsbcid) {
        this.fsbcid = fsbcid;
    }

    public Integer getJzbcid() {
        return jzbcid;
    }

    public void setJzbcid(Integer jzbcid) {
        this.jzbcid = jzbcid;
    }

    public String getJzskjh() {
        return jzskjh;
    }

    public void setJzskjh(String jzskjh) {
        this.jzskjh = jzskjh;
    }

    public String getKwbz() {
        return kwbz;
    }

    public void setKwbz(String kwbz) {
        this.kwbz = kwbz;
    }

    public String getCpbh() {
        return cpbh;
    }

    public void setCpbh(String cpbh) {
        this.cpbh = cpbh;
    }

    public String getDcbz() {
        return dcbz;
    }

    public void setDcbz(String dcbz) {
        this.dcbz = dcbz;
    }

    public String getZwh() {
        return zwh;
    }

    public void setZwh(String zwh) {
        this.zwh = zwh;
    }

    public String getCsbh() {
        return csbh;
    }

    public void setCsbh(String csbh) {
        this.csbh = csbh;
    }

    public String getPqhm() {
        return pqhm;
    }

    public void setPqhm(String pqhm) {
        this.pqhm = pqhm;
    }

    public Integer getSyyhfkfsid() {
        return syyhfkfsid;
    }

    public void setSyyhfkfsid(Integer syyhfkfsid) {
        this.syyhfkfsid = syyhfkfsid;
    }

    public Integer getScbj() {
        return scbj;
    }

    public void setScbj(Integer scbj) {
        this.scbj = scbj;
    }

    public String getXsms() {
        return xsms;
    }

    public void setXsms(String xsms) {
        this.xsms = xsms;
    }

    public Integer getCjgqbj() {
        return cjgqbj;
    }

    public void setCjgqbj(Integer cjgqbj) {
        this.cjgqbj = cjgqbj;
    }

    public Integer getCpbj() {
        return cpbj;
    }

    public void setCpbj(Integer cpbj) {
        this.cpbj = cpbj;
    }

    public String getYhyybh() {
        return yhyybh;
    }

    public void setYhyybh(String yhyybh) {
        this.yhyybh = yhyybh;
    }

    public String getSfxsmx() {
        return sfxsmx;
    }

    public void setSfxsmx(String sfxsmx) {
        this.sfxsmx = sfxsmx;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getScqrbj() {
        return scqrbj;
    }

    public void setScqrbj(Integer scqrbj) {
        this.scqrbj = scqrbj;
    }

    public String getFsskjh() {
        return fsskjh;
    }

    public void setFsskjh(String fsskjh) {
        this.fsskjh = fsskjh;
    }

    public Integer getNewRwid() {
        return newRwid;
    }

    public void setNewRwid(Integer newRwid) {
        this.newRwid = newRwid;
    }
}
