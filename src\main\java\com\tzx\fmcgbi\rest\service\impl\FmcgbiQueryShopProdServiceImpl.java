package com.tzx.fmcgbi.rest.service.impl;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.tzx.fmcgbi.common.FmcgbiData;
import com.tzx.fmcgbi.rest.mapper.FmcgbiQueryShopProdMapper;
import com.tzx.fmcgbi.rest.service.IFmcgbiQueryShopProdService;
import com.tzx.fmcgbi.rest.vo.ProdVo;
import com.tzx.publics.util.GsonUtil;

import net.sf.json.JSONObject;

@Service
public class FmcgbiQueryShopProdServiceImpl implements IFmcgbiQueryShopProdService {
	private final static Logger LOGGER = LoggerFactory.getLogger(FmcgbiQueryShopProdServiceImpl.class);
	@Autowired
	private FmcgbiQueryShopProdMapper queryShopProdMapper;

	/**
	 * 门店商品查询接口（同步商品到客户端接口）
	 */
	@Transactional
	public FmcgbiData queryShopProd(JSONObject requestJson) {
		FmcgbiData data = new FmcgbiData();
		JSONObject responseJson = new JSONObject();

		List<ProdVo> prodList = queryShopProdMapper.queryShopProd();
		
		responseJson.put("status", "0");
		responseJson.put("msg", "同步成功");
		responseJson.put("ProdList", GsonUtil.GsonString(prodList));
		data.setCode(0);
		data.setMsg("同步成功");
		data.setData(responseJson);
		return data;
	}

}
