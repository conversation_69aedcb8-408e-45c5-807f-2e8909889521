package com.tzx.miniapp.rest.model;

import javax.persistence.Column;
import javax.persistence.Id;

public class TqWdkCouponTemp {
	@Id
	@Column(name = "PID", insertable = false)
	private int pid;
	@Column(name = "ZDBH")
	private String zdbh;
	@Column(name = "DATATYPE")
	private String datatype;
	@Column(name = "COUPONTYPE")
	private String coupontype;
	@Column(name = "COUPONCODE")
	private String couponcode;
	@Column(name = "COUPONDISHCODE")
	private String coupondishcode;
	@Column(name = "IS_USABLE")
	private int is_usable;
	@Column(name = "USEABLEMSG")
	private String useablemsg;
	@Column(name = "COUPON_SALEMONEY")
	private double coupon_salemoney;
	@Column(name = "COUPON_TOTALMONEY")
	private double coupon_totalmoney;
	@Column(name = "USEOK")
	private int useok;

	public int getPid() {
		return pid;
	}

	public void setPid(int pid) {
		this.pid = pid;
	}

	public String getZdbh() {
		return zdbh;
	}

	public void setZdbh(String zdbh) {
		this.zdbh = zdbh;
	}

	public String getDatatype() {
		return datatype;
	}

	public void setDatatype(String datatype) {
		this.datatype = datatype;
	}

	public String getCoupontype() {
		return coupontype;
	}

	public void setCoupontype(String coupontype) {
		this.coupontype = coupontype;
	}

	public String getCouponcode() {
		return couponcode;
	}

	public void setCouponcode(String couponcode) {
		this.couponcode = couponcode;
	}

	public String getCoupondishcode() {
		return coupondishcode;
	}

	public void setCoupondishcode(String coupondishcode) {
		this.coupondishcode = coupondishcode;
	}

	public int getIs_usable() {
		return is_usable;
	}

	public void setIs_usable(int is_usable) {
		this.is_usable = is_usable;
	}

	public String getUseablemsg() {
		return useablemsg;
	}

	public void setUseablemsg(String useablemsg) {
		this.useablemsg = useablemsg;
	}

	public double getCoupon_salemoney() {
		return coupon_salemoney;
	}

	public void setCoupon_salemoney(double coupon_salemoney) {
		this.coupon_salemoney = coupon_salemoney;
	}

	public double getCoupon_totalmoney() {
		return coupon_totalmoney;
	}

	public void setCoupon_totalmoney(double coupon_totalmoney) {
		this.coupon_totalmoney = coupon_totalmoney;
	}

	public int getUseok() {
		return useok;
	}

	public void setUseok(int useok) {
		this.useok = useok;
	}



}
