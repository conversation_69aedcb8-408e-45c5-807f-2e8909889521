package com.tzx.receiver.entity.msg;

import java.io.Serializable;

public class Orgsilist
  implements Serializable
{
  private Integer id;
  private String cmmc;
  private Integer jgxh;
  private String tcbh;
  private String tcfh;
  private String cmbh;
  private String cmfh;
  private Double sjjg;
  private Double cmsl;
  private String cmsx;
  private Integer cmxh;
  private Integer cmph;
  private Integer xmid;
  private Integer mxxmid;
  private String mxlx;
  private String zbxmbh;
  private String zbmxxmbh;
  private Double xcsl;
  private Integer mxtype;

  public Double getXcsl()
  {
    return this.xcsl; }

  public void setXcsl(Double xcsl) {
    this.xcsl = xcsl; }

  public Integer getMxtype() {
    return this.mxtype; }

  public void setMxtype(Integer mxtype) {
    this.mxtype = mxtype; }

  public Integer getId() {
    return this.id; }

  public void setId(Integer id) {
    this.id = id; }

  public String getCmmc() {
    return this.cmmc; }

  public void setCmmc(String cmmc) {
    this.cmmc = cmmc; }

  public Integer getJgxh() {
    return this.jgxh; }

  public void setJgxh(Integer jgxh) {
    this.jgxh = jgxh; }

  public String getTcbh() {
    return this.tcbh; }

  public void setTcbh(String tcbh) {
    this.tcbh = tcbh; }

  public String getTcfh() {
    return this.tcfh; }

  public void setTcfh(String tcfh) {
    this.tcfh = tcfh; }

  public String getCmbh() {
    return this.cmbh; }

  public void setCmbh(String cmbh) {
    this.cmbh = cmbh; }

  public String getCmfh() {
    return this.cmfh; }

  public void setCmfh(String cmfh) {
    this.cmfh = cmfh; }

  public Double getSjjg() {
    return this.sjjg; }

  public void setSjjg(Double sjjg) {
    this.sjjg = sjjg; }

  public Double getCmsl() {
    return this.cmsl; }

  public void setCmsl(Double cmsl) {
    this.cmsl = cmsl; }

  public String getCmsx() {
    return this.cmsx; }

  public void setCmsx(String cmsx) {
    this.cmsx = cmsx; }

  public Integer getCmxh() {
    return this.cmxh; }

  public void setCmxh(Integer cmxh) {
    this.cmxh = cmxh; }

  public Integer getCmph() {
    return this.cmph; }

  public void setCmph(Integer cmph) {
    this.cmph = cmph; }

  public Integer getXmid() {
    return this.xmid; }

  public void setXmid(Integer xmid) {
    this.xmid = xmid; }

  public Integer getMxxmid() {
    return this.mxxmid; }

  public void setMxxmid(Integer mxxmid) {
    this.mxxmid = mxxmid; }

  public String getMxlx() {
    return this.mxlx; }

  public void setMxlx(String mxlx) {
    this.mxlx = mxlx; }

  public String getZbxmbh() {
    return this.zbxmbh; }

  public void setZbxmbh(String zbxmbh) {
    this.zbxmbh = zbxmbh; }

  public String getZbmxxmbh() {
    return this.zbmxxmbh; }

  public void setZbmxxmbh(String zbmxxmbh) {
    this.zbmxxmbh = zbmxxmbh;
  }
}