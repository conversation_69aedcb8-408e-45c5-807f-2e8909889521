package com.tzx.mobilepos.rest.service;

import com.tzx.mobilepos.common.Data;

public interface IMobilePosAcewillMemberService {
	/**
	 * 根据会员动态二维码/手机号/卡号/磁道 信息查询微生活会员信息
	 * 
	 * @param param
	 * @param result
	 */
	public void findAcewillMember(Data param, Data result);
	
	/**
	 * 根据账单号清楚该账单关联的会员信息
	 * @param param
	 * @param result
	 */
	public void refundAcewillMember(Data param, Data result);
	
	/**
	 * 根据账单号查询缓存中的微生活会员信息
	 * 
	 * @param param
	 * @param result
	 */
	public void cacheAcewillMember(Data param, Data result);
	
	/**
	 * 微生活会员，预消费
	 * 
	 * @param param
	 * @param result
	 */
	public void acewillPreview(Data param, Data result);
	
	/**
	 * 微生活会员，提交消费
	 * 
	 * @param param
	 * @param result
	 */
	public void acewillCommit(Data param, Data result);
	
}
