package com.tzx.mobilepos.rest.vo;

public class YhneedChoitems {
	private Integer clid;
	private Integer dishId;
	private String dishNo;
	private String dishName;
	private String color;
	private Integer discountTypeId;
	private Integer showxh;
	private String qxid;
	private String dishPrice;
	private Integer xmid;
	private Integer pqlxId;
	private String dishUnit;
	private String dishType;
	private String yhsx;
	private String yl3;
	private String issaleout;
	private Integer saleoutCount;
	private String dishPrice2;
	private String is_gift;
	private String yhfsmxPrice;

	public Integer getClid() {
		return clid;
	}

	public void setClid(Integer clid) {
		this.clid = clid;
	}

	public Integer getDishId() {
		return dishId;
	}

	public void setDishId(Integer dishId) {
		this.dishId = dishId;
	}

	public String getDishNo() {
		return dishNo;
	}

	public void setDishNo(String dishNo) {
		this.dishNo = dishNo;
	}

	public String getDishName() {
		return dishName;
	}

	public void setDishName(String dishName) {
		this.dishName = dishName;
	}

	public String getColor() {
		return color;
	}

	public void setColor(String color) {
		this.color = color;
	}

	public Integer getDiscountTypeId() {
		return discountTypeId;
	}

	public void setDiscountTypeId(Integer discountTypeId) {
		this.discountTypeId = discountTypeId;
	}

	public Integer getShowxh() {
		return showxh;
	}

	public void setShowxh(Integer showxh) {
		this.showxh = showxh;
	}

	public String getQxid() {
		return qxid;
	}

	public void setQxid(String qxid) {
		this.qxid = qxid;
	}

	public String getDishPrice() {
		return dishPrice;
	}

	public void setDishPrice(String dishPrice) {
		this.dishPrice = dishPrice;
	}

	public Integer getXmid() {
		return xmid;
	}

	public void setXmid(Integer xmid) {
		this.xmid = xmid;
	}

	public Integer getPqlxId() {
		return pqlxId;
	}

	public void setPqlxId(Integer pqlxId) {
		this.pqlxId = pqlxId;
	}

	public String getDishUnit() {
		return dishUnit;
	}

	public void setDishUnit(String dishUnit) {
		this.dishUnit = dishUnit;
	}

	public String getDishType() {
		return dishType;
	}

	public void setDishType(String dishType) {
		this.dishType = dishType;
	}

	public String getYhsx() {
		return yhsx;
	}

	public void setYhsx(String yhsx) {
		this.yhsx = yhsx;
	}

	public String getYl3() {
		return yl3;
	}

	public void setYl3(String yl3) {
		this.yl3 = yl3;
	}

	public String getIssaleout() {
		return issaleout;
	}

	public void setIssaleout(String issaleout) {
		this.issaleout = issaleout;
	}

	public Integer getSaleoutCount() {
		return saleoutCount;
	}

	public void setSaleoutCount(Integer saleoutCount) {
		this.saleoutCount = saleoutCount;
	}

	public String getDishPrice2() {
		return dishPrice2;
	}

	public void setDishPrice2(String dishPrice2) {
		this.dishPrice2 = dishPrice2;
	}

	public String getIs_gift() {
		return is_gift;
	}

	public void setIs_gift(String is_gift) {
		this.is_gift = is_gift;
	}

	public String getYhfsmxPrice() {
		return yhfsmxPrice;
	}

	public void setYhfsmxPrice(String yhfsmxPrice) {
		this.yhfsmxPrice = yhfsmxPrice;
	}

}
