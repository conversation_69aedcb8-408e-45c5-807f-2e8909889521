package com.tzx.publics.util;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.tzx.publics.common.BaseData;
import net.sf.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class GsonUtil {
	private static String responseString = null;
	public static String getResponseString() {
		return responseString;
	}
    private static Gson gson = null;
    static {
        if (gson == null) {
            gson = new Gson();
        }
    }

    private GsonUtil() {
    }

    /**
     * 转成json
     *
     * @param object
     * @return
     */
    public static String GsonString(Object object) {
        String gsonString = null;
        if (gson != null) {
            gsonString = gson.toJson(object);
        }
        return gsonString;
    }

    /**
     * 转成bean
     *
     * @param gsonString
     * @param cls
     * @return
     */
    public static <T> T GsonToBean(String gsonString, Class<T> cls) {
        T t = null;
        if (gson != null) {
            t = gson.fromJson(gsonString, cls);
        }
        return t;
    }

    /**
     * 转成list
     *
     * @param gsonString
     * @param cls
     * @return
     */
    public static <T> List<T> GsonToList(String gsonString, Class<T> cls) {
        List<T> list = null;
        if (gson != null) {
            list = gson.fromJson(gsonString, new TypeToken<List<T>>() {
            }.getType());
        }
        return list;
    }

    /**
     * 转成list中有map的
     *
     * @param gsonString
     * @return
     */
    public static <T> List<Map<String, T>> GsonToListMaps(String gsonString) {
        List<Map<String, T>> list = null;
        if (gson != null) {
            list = gson.fromJson(gsonString,
                    new TypeToken<List<Map<String, T>>>() {
                    }.getType());
        }
        return list;
    }

    /**
     * 转成map的
     *
     * @param gsonString
     * @return
     */
    public static <T> Map<String, T> GsonToMaps(String gsonString) {
        Map<String, T> map = null;
        if (gson != null) {
            map = gson.fromJson(gsonString, new TypeToken<Map<String, T>>() {
            }.getType());
        }
        return map;
    }
    public static <T> void asyncResponseData(BaseData data, List<T> list) {
		Gson gson = GsonHelper.getGson();
		String json = gson.toJson(list, new TypeToken<List<T>>() {
		}.getType());
		List<JSONObject> jsons = gson.fromJson(json,
				new TypeToken<List<JSONObject>>() {
				}.getType());
		data.setData(jsons);
		
		asyncResponseData(gson.toJson(data, BaseData.class));
	}
    public static void asyncResponseData(String respStr) {
		responseString = respStr;
	}
	public void asyncResponseData(BaseData data) {
		Gson gson = GsonHelper.getGson();
		//data.setStore_id(mapEnv.get("FDJGXH"));
		asyncResponseData(gson.toJson(data, BaseData.class));
	}
	public void asyncResponseData(BaseData data, Object obj) {
		List<JSONObject> list = new ArrayList();
		Gson gson = GsonHelper.getGson();
		
		list.add(gson.fromJson(gson.toJson(obj, obj.getClass()),
				JSONObject.class));
		data.setData(list);
		asyncResponseData(data);
	}
}