package com.tzx.ecoserver.common;

import org.springframework.stereotype.Service;

import javax.sound.sampled.*;
import java.io.File;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019-11-10.
 */
@Service
public class PlayNewOrderThread extends Thread {
    public volatile boolean exit = false;
    public volatile boolean runpay = false;
    public volatile boolean runorderback = false;
    public volatile boolean runwaitorderback = false;
    public volatile boolean runwaitorderhandorderback = false;
    public volatile boolean isstart = false;
    public volatile boolean istestvoice = false;
    public volatile int soundVoice = 0;
    private String wavPath = "";
    public void playVoiceFile(String filePath ) throws Exception {
        if (!filePath.equals("")) {

            File file=new File(filePath);
            if(!file.exists()){
               return;
            }

            SourceDataLine sourceDataLine = null;
            AudioInputStream audioInputStream = null;
            try {
                //Get audio input stream
                 audioInputStream = AudioSystem.getAudioInputStream(new File(filePath));
                //Get audio coding object
                AudioFormat audioFormat = audioInputStream.getFormat();
                //Set data entry
                DataLine.Info dataLineInfo = new DataLine.Info(SourceDataLine.class, audioFormat,
                        AudioSystem.NOT_SPECIFIED);
                sourceDataLine = (SourceDataLine) AudioSystem.getLine(dataLineInfo);
                sourceDataLine.open(audioFormat);
                FloatControl volctrl=(FloatControl)sourceDataLine.getControl(FloatControl.Type.MASTER_GAIN);
                volctrl.setValue(soundVoice);// newVal - the value of volume slider

                sourceDataLine.start();
                //Read from the data sent to the mixer input stream
                int count;
                byte tempBuffer[] = new byte[1024];
                while ((count = audioInputStream.read(tempBuffer, 0, tempBuffer.length)) != -1) {
                    if (count > 0) {
                        sourceDataLine.write(tempBuffer, 0, count);
                    }
                }
                //Empty the data buffer, and close the input

            }
            finally {
                if(null!=audioInputStream){
                    audioInputStream.close();
                }
                if(null!=sourceDataLine) {
                    sourceDataLine.drain();
                    sourceDataLine.close();
                }
            }
        }
    }
    public void initPath() {
        if (wavPath.equals("")) {
            String path = System.getProperty("user.dir");
            File file = new File(path);
            String fileName = null;
            this.wavPath = file.getParent() + File.separator + "wavs" + File.separator;
            System.out.println("wavPath" + wavPath);
        }
    }

    @Override
    public void run() {
        while (!exit) {
            boolean hasrunpay = false;
            if(PlayWavUtil.getNewOrderNum() > 0||istestvoice){
                try {
                    this.playVoiceFile(wavPath + File.separator + "neworder.wav");
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if(!istestvoice){
                    PlayWavUtil.addNewOrderNum(-1);
                }
                else
                {
                    istestvoice = false;
                }
                hasrunpay = true;
            }

            if (runpay && !hasrunpay) {
                try {
                    this.playVoiceFile(wavPath + File.separator + "neworder.wav");
                } catch (Exception e) {
                    e.printStackTrace();
                }
                hasrunpay = true;
//                try {
//
//                    FileInputStream inNew = new FileInputStream(wavPath + File.separator + "neworder.wav");
//                    AudioPlayer.player.start(inNew);
//                    //            AudioPlayer.player.stop(inNew);
//                } catch (FileNotFoundException e) {
//                    e.printStackTrace();
//                }
//                hasrunpay = true;
            }
            if (runorderback) {
                try {
                    this.playVoiceFile(wavPath + File.separator + "cancelorder.wav");
                } catch (Exception e) {
                    e.printStackTrace();
                }
//                try {
//                    FileInputStream inRef = new FileInputStream(wavPath + File.separator + "cancelorder.wav");
//                    AudioPlayer.player.start(inRef);
////            AudioPlayer.player.stop(inRef);
//                } catch (FileNotFoundException e) {
//                    e.printStackTrace();
//                }
                runorderback = false;
            }
            if (runwaitorderback) {
                try {
                    this.playVoiceFile(wavPath + File.separator + "waitcancelorder.wav");
                } catch (Exception e) {
                    e.printStackTrace();
                }
                runwaitorderback = false;
            }
            if (runwaitorderhandorderback) {
                try {
                    this.playVoiceFile(wavPath + File.separator + "waitcancelorder.wav");
                } catch (Exception e) {
                    e.printStackTrace();
                }
                runwaitorderhandorderback = false;
            }
            if(hasrunpay){
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
            {
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
    }

}
