package com.tzx.miniapp.rest.model;

import com.tzx.publics.base.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2018-05-21
 */
@Table(name = "TS_TCMXK")
public class TsTcmxk extends BaseEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	@Id
	@Column(name = "ID")
	private int id;
	@Column(name = "XMID")
	private int xmid;
	@Column(name = "MXXMID")
	private int mxxmid;
	@Column(name = "CMBH")
	private String cmbh;
	@Column(name = "CMSL")
	private double cmsl;
	@Column(name = "CMGG")
	private String cmgg;
	@Column(name = "SJJG")
	private double sjjg;
	@Column(name = "CMMC")
	private String cmmc;
	@Column(name = "CMSX")
	private String cmsx;
	@Column(name = "XCSL")
	private int xcsl;
	@Column(name = "CMXH")
	private int cmxh;
	@Column(name = "CMPH")
	private int cmph;
	@Column(name = "MXLXID")
	private int mxlxid;
	@Column(name = "MXLX")
	private String mxlx;
	@Column(name = "YL1")
	private String yl1;
	@Column(name = "YL2")
	private String yl2;
	@Column(name = "YL3")
	private String yl3;
	@Column(name = "ISREQUIRE")
	private int isrequire;
	@Column(name = "MAXCOUNT")
	private int maxcount;
	@Column(name = "DESCRIPTION")
	private String description;
	@Column(name = "YWMC")
	private String ywmc;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public int getXmid() {
		return xmid;
	}

	public void setXmid(int xmid) {
		this.xmid = xmid;
	}

	public int getMxxmid() {
		return mxxmid;
	}

	public void setMxxmid(int mxxmid) {
		this.mxxmid = mxxmid;
	}

	public String getCmbh() {
		return cmbh;
	}

	public void setCmbh(String cmbh) {
		this.cmbh = cmbh;
	}

	public double getCmsl() {
		return cmsl;
	}

	public void setCmsl(double cmsl) {
		this.cmsl = cmsl;
	}

	public String getCmgg() {
		return cmgg;
	}

	public void setCmgg(String cmgg) {
		this.cmgg = cmgg;
	}

	public double getSjjg() {
		return sjjg;
	}

	public void setSjjg(double sjjg) {
		this.sjjg = sjjg;
	}

	public String getCmmc() {
		return cmmc;
	}

	public void setCmmc(String cmmc) {
		this.cmmc = cmmc;
	}

	public String getCmsx() {
		return cmsx;
	}

	public void setCmsx(String cmsx) {
		this.cmsx = cmsx;
	}

	public int getXcsl() {
		return xcsl;
	}

	public void setXcsl(int xcsl) {
		this.xcsl = xcsl;
	}

	public int getCmxh() {
		return cmxh;
	}

	public void setCmxh(int cmxh) {
		this.cmxh = cmxh;
	}

	public int getCmph() {
		return cmph;
	}

	public void setCmph(int cmph) {
		this.cmph = cmph;
	}

	public int getMxlxid() {
		return mxlxid;
	}

	public void setMxlxid(int mxlxid) {
		this.mxlxid = mxlxid;
	}

	public String getMxlx() {
		return mxlx;
	}

	public void setMxlx(String mxlx) {
		this.mxlx = mxlx;
	}

	public String getYl1() {
		return yl1;
	}

	public void setYl1(String yl1) {
		this.yl1 = yl1;
	}

	public String getYl2() {
		return yl2;
	}

	public void setYl2(String yl2) {
		this.yl2 = yl2;
	}

	public String getYl3() {
		return yl3;
	}

	public void setYl3(String yl3) {
		this.yl3 = yl3;
	}

	public int getIsrequire() {
		return isrequire;
	}

	public void setIsrequire(int isrequire) {
		this.isrequire = isrequire;
	}

	public int getMaxcount() {
		return maxcount;
	}

	public void setMaxcount(int maxcount) {
		this.maxcount = maxcount;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getYwmc() {
		return ywmc;
	}

	public void setYwmc(String ywmc) {
		this.ywmc = ywmc;
	}

}
