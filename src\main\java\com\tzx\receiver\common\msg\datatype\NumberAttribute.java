package com.tzx.receiver.common.msg.datatype;


/**
 * 数字数据类型的类型属性类。
 * 
 * <AUTHOR>
 * 
 */
public class NumberAttribute extends DataTypeAttribute {

	private int precision;

	private int scale;

	public NumberAttribute() {
		precision = DataTypeUnit.DefaultPrecision;
		scale = DataTypeUnit.DefaultScale;
	}

	/**
	 * 获取字段精度。
	 * 
	 * @return 字段精度的值。
	 */
	public int getPrecision() {
		return precision;
	}

	/**
	 * 设置字段精度。
	 * 
	 * @param precision
	 *            字段精度的值。
	 */
	public void setPrecision(int precision) {
		this.precision = precision;
	}

	/**
	 * 获取字段小数位。
	 * 
	 * @return 字段小数位的值。
	 */
	public int getScale() {
		return scale;
	}

	/**
	 * 设置字段小数位。
	 * 
	 * @param scale
	 *            字段小数位的值。
	 */

	public void setScale(int scale) {
		this.scale = scale;
	}

	public String toString() {
		return "NumberAttribute(" + "precision=" + precision + ", scale=" + scale
				+ ")";
	}

}
