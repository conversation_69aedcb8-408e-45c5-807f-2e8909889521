package com.tzx.receiver.common.upload;

import org.apache.commons.lang3.time.DateUtils;

import java.lang.reflect.Array;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * <AUTHOR>
 * @Date 2019-04-20
 * @Descption  单个上传任务的信息
 **/
public class UploadParam {
    private int transTypeId;
    private String transTypeName;
    private String configName;
    private String totalCommand;
    private String command;
    private String actionName;
    private String sendUrl;
    private boolean sendEmpty;
    private boolean isRunFinal;
    private String finalCommand;
    private int waitSecond;
    private int minWaitSecond;
    private int maxWaiteSecond;
    private Date nextRunTime;
    private boolean isTotal;
    private UploadDataType msgType ;
    private String signKeys;//签名字段
    private Map<String,String> tables = new HashMap<String,String>();


    public int getTransTypeId() {
        return transTypeId;
    }

    public void setTransTypeId(int transTypeId) {
        this.transTypeId = transTypeId;
    }

    public String getTransTypeName() {
        return transTypeName;
    }

    public void setTransTypeName(String transTypeName) {
        this.transTypeName = transTypeName;
    }

    public String getConfigName() {
        return configName;
    }

    public void setConfigName(String configName) {
        this.configName = configName;
    }

    public String getTotalCommand() {
        return totalCommand;
    }

    public void setTotalCommand(String totalCommand) {
        this.totalCommand = totalCommand;
    }

    public String getCommand() {
        return command;
    }

    public void setCommand(String command) {
        this.command = command;
    }

    public String getActionName() {
        return actionName;
    }

    public void setActionName(String actionName) {
        this.actionName = actionName;
    }

    public String getSendUrl() {
        return sendUrl;
    }

    public void setSendUrl(String sendUrl) {
        this.sendUrl = sendUrl;
    }

    public boolean getIsSendEmpty() {
        return sendEmpty;
    }

    public void setIsSendEmpty(boolean sendEmpty) {
        this.sendEmpty = sendEmpty;
    }

    public UploadDataType getMsgType() {
        return msgType;
    }

    public boolean getIsRunFinal() {
        return isRunFinal;
    }

    public void setIsRunFinal(boolean runFinal) {
        isRunFinal = runFinal;
    }

    public String getFinalCommand() {
        return finalCommand;
    }

    public void setFinalCommand(String finalCommand) {
        this.finalCommand = finalCommand;
    }

    public int getWaitSecond() {
        return waitSecond;
    }

    public void setWaitSecond(int waitSecond) {
        this.waitSecond = waitSecond;
    }

    public int getMinWaitSecond() {
        return minWaitSecond;
    }

    public void setMinWaitSecond(int minWaitSecond) {
        this.minWaitSecond = minWaitSecond;
    }

    public int getMaxWaiteSecond() {
        return maxWaiteSecond;
    }

    public void setMaxWaiteSecond(int maxWaiteSecond) {
        this.maxWaiteSecond = maxWaiteSecond;
    }

    public Date getNextRunTime() {
        return nextRunTime;
    }

    public void setNextRunTime(Date nextRunTime) {
        this.nextRunTime = nextRunTime;
    }

    public boolean getIsTotal() {
        return isTotal;
    }

    public void setIsTotal(boolean total) {
        isTotal = total;
    }

    public void setMsgType(UploadDataType msgType) {
        this.msgType = msgType;
    }

    public boolean isSendEmpty() {
        return sendEmpty;
    }

    public void setSendEmpty(boolean sendEmpty) {
        this.sendEmpty = sendEmpty;
    }

    public boolean isRunFinal() {
        return isRunFinal;
    }

    public void setRunFinal(boolean runFinal) {
        isRunFinal = runFinal;
    }

    public boolean isTotal() {
        return isTotal;
    }

    public void setTotal(boolean total) {
        isTotal = total;
    }

    public String getSignKeys() {
        return signKeys;
    }

    public void setSignKeys(String signKeys) {
        this.signKeys = signKeys;
    }

    @Override
    public String toString() {
        return "UploadParam{" +
                "transTypeId=" + transTypeId +
                ", transTypeName='" + transTypeName + '\'' +
                ", configName='" + configName + '\'' +
                ", totalCommand='" + totalCommand + '\'' +
                ", command='" + command + '\'' +
                ", actionName='" + actionName + '\'' +
                ", sendUrl='" + sendUrl + '\'' +
                ", sendEmpty=" + sendEmpty +
                ", isRunFinal=" + isRunFinal +
                ", finalCommand='" + finalCommand + '\'' +
                ", waitSecond=" + waitSecond +
                ", minWaitSecond=" + minWaitSecond +
                ", maxWaiteSecond=" + maxWaiteSecond +
                ", nextRunTime=" + nextRunTime +
                ", isTotal=" + isTotal +
                ", msgType=" + msgType +
                ", tables=" + tables +
                '}';
    }

    public void restNextRunTime(){
        if(minWaitSecond>0&&maxWaiteSecond>0&&maxWaiteSecond>minWaitSecond){
            Random random = new Random();//默认构造方法
            waitSecond = random.nextInt(maxWaiteSecond) - minWaitSecond + 1; //因为集合形式为[0,N)，所以要再+1
            nextRunTime = DateUtils.addSeconds(new Date(),waitSecond);
        }else if (waitSecond>0){
            nextRunTime = DateUtils.addSeconds(new Date(),waitSecond);
        }else{
            //如果没有显示的设置waitetime,且没有配置随机上下线，那么就任务不定时，那么下次执行时间，就设置一个特别大的值
            nextRunTime = DateUtils.addYears(new Date(),100);
        }
    }

    public Map<String, String> getTables() {
        return tables;
    }

    public void setTables(Map<String, String> tables) {
        this.tables = tables;
    }
    public void setTables(String str){
        tables.clear();
        String[] array = str.split(",");
        for (String s : array) {
            String[] kv = s.split("=");
            String k ,v;
            if(kv.length==2){
                k = kv[0];
                v = kv[1];
                tables.put(k,v);
            }
        }

    }
}
