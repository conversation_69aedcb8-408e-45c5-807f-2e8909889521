package com.tzx.ecoserver.rest.vo;

import javax.persistence.Entity;
import java.io.Serializable;
import java.math.BigDecimal;

@Entity
public class EcoBtYdxm2 implements Serializable {

	private String yddh;//预订单号
	private int xmid;//项目id
	private String xmbh;//项目编号
	private String xmsx;//项目属性
	private BigDecimal xmdj;//项目单价
	private BigDecimal xmsl;//项目数量
	private int zkl;//折扣率
	private BigDecimal totalprice;//实结金额
	private String dwbh;//单位编号
	private String kwbh;//口味编号
	private BigDecimal cmje;//菜品金额
	private String tcbh;//套餐编号
	private int tcdch;//套餐点餐号
	private int fzsl;//分组数量
	private BigDecimal fzje;//分组金额
	private int dcxh;//点餐序号
	private String xmmc;//项目名称
	private String yl3;

	// 新增一个字段，表示是否微生活活动，0：正常菜品，1：优惠赠送菜品，默认：0
	private int isactivity;
	private Integer packid;//打包盒类型
	private Integer top_item_id;//套餐对应的id

	private String nature;

	/**
	 * ECO套餐id
	 */
	private String bsetmealid;

	public String getNature() {
		return nature;
	}

	public void setNature(String nature) {
		this.nature = nature;
	}

	public Integer getTop_item_id() {
		return top_item_id;
	}

	public void setTop_item_id(Integer top_item_id) {
		this.top_item_id = top_item_id;
	}

	public int getIsactivity() {
		return isactivity;
	}

	public void setIsactive(int isactivity) {
		this.isactivity = isactivity;
	}

	public String getYddh() {
		return yddh;
	}

	public void setYddh(String yddh) {
		this.yddh = yddh;
	}

	public String getXmbh() {
		return xmbh;
	}

	public void setXmbh(String xmbh) {
		this.xmbh = xmbh;
	}

	public String getXmsx() {
		return xmsx;
	}

	public void setXmsx(String xmsx) {
		this.xmsx = xmsx;
	}

	public BigDecimal getXmdj() {
		return xmdj;
	}

	public void setXmdj(BigDecimal xmdj) {
		this.xmdj = xmdj;
	}

	public BigDecimal getXmsl() {
		return xmsl;
	}

	public void setXmsl(BigDecimal xmsl) {
		this.xmsl = xmsl;
	}

	public int getZkl() {
		return zkl;
	}

	public void setZkl(int zkl) {
		this.zkl = zkl;
	}

	public BigDecimal getTotalprice() {
		return totalprice;
	}

	public void setTotalprice(BigDecimal totalprice) {
		this.totalprice = totalprice;
	}

	public String getDwbh() {
		return dwbh;
	}

	public void setDwbh(String dwbh) {
		this.dwbh = dwbh;
	}

	public String getKwbh() {
		return kwbh;
	}

	public void setKwbh(String kwbh) {
		this.kwbh = kwbh;
	}

	public BigDecimal getCmje() {
		return cmje;
	}

	public void setCmje(BigDecimal cmje) {
		this.cmje = cmje;
	}

	public String getTcbh() {
		return tcbh;
	}

	public void setTcbh(String tcbh) {
		this.tcbh = tcbh;
	}

	public int getTcdch() {
		return tcdch;
	}

	public void setTcdch(int tcdch) {
		this.tcdch = tcdch;
	}

	public int getFzsl() {
		return fzsl;
	}

	public void setFzsl(int fzsl) {
		this.fzsl = fzsl;
	}

	public BigDecimal getFzje() {
		return fzje;
	}

	public void setFzje(BigDecimal fzje) {
		this.fzje = fzje;
	}

	public int getDcxh() {
		return dcxh;
	}

	public void setDcxh(int dcxh) {
		this.dcxh = dcxh;
	}

	public String getXmmc() {
		return xmmc;
	}

	public void setXmmc(String xmmc) {
		this.xmmc = xmmc;
	}

	public int getXmid() {
		return xmid;
	}

	public void setXmid(int xmid) {
		this.xmid = xmid;
	}

	public String getYl3() {
		return yl3;
	}

	public void setYl3(String yl3) {
		this.yl3 = yl3;
	}

	public void setIsactivity(int isactivity) {
		this.isactivity = isactivity;
	}

	public Integer getPackid() {
		return packid;
	}

	public void setPackid(Integer packid) {
		this.packid = packid;
	}

	public String getBsetmealid() {
		return bsetmealid;
	}

	public void setBsetmealid(String bsetmealid) {
		this.bsetmealid = bsetmealid;
	}
}
