package com.tzx.miniapp.rest.vo;

import javax.persistence.Transient;
import java.io.Serializable;
import java.util.List;

public class Mandatory implements Serializable {
	private String title;
	private int selnum;
	private int id;
	private int xmid;

	private int dishToppingId;

	public int getXmid() {
		return xmid;
	}

	public void setXmid(int xmid) {
		this.xmid = xmid;
	}

	@Transient
	private List<ComboItems> items;

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public int getSelnum() {
		return selnum;
	}

	public void setSelnum(int selnum) {
		this.selnum = selnum;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public List<ComboItems> getItems() {
		return items;
	}

	public void setItems(List<ComboItems> items) {
		this.items = items;
	}

	public int getDishToppingId() {
		return dishToppingId;
	}

	public void setDishToppingId(int dishToppingId) {
		this.dishToppingId = dishToppingId;
	}
}
