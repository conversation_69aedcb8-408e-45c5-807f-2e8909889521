package com.tzx.publics.util;

import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetSocketAddress;

public class DatagramUtil {

	public static void sendUdpMsg(String content, int uport, String ip) {
		byte[] buf = content.getBytes();
		DatagramPacket dp;
		DatagramSocket socket = null;
		try {
			dp = new DatagramPacket(buf, buf.length, new InetSocketAddress(ip, uport));

			socket = new DatagramSocket(9999);
			socket.send(dp);

		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (socket != null)
				socket.close();
		}
	}

	public static void sendUdpMsg(String content, String serverIp, int uport) {
		byte[] buf = content.getBytes();
		DatagramPacket dp;
		DatagramSocket socket = null;
		try {
			dp = new DatagramPacket(buf, buf.length, new InetSocketAddress(serverIp, uport));
			socket = new DatagramSocket(9999);
			socket.send(dp);

		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (socket != null)
				socket.close();
		}
	}

}
