package com.tzx.receiver.web;

import com.tzx.receiver.common.mapper.JsonMapper;
import com.tzx.receiver.common.msg.MessageDispatcher;
import com.tzx.receiver.common.upload.UpLoadTaskList;
import com.tzx.receiver.common.utils.SendXML2RifMQ;
import com.tzx.receiver.common.utils.Global;
import com.tzx.receiver.entity.MessageBody;
import com.tzx.receiver.entity.msg.JsonMessageDto;
import com.tzx.receiver.entity.msg.JsonUploadDto;
import com.tzx.receiver.service.ReceiverManagerService;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ResourceBundle;

@Controller
//这里改为用两个TzxMsg  因为之前的监听有的上下文是TzxMsg，现在改为Spring
@RequestMapping(value = "/TzxMsg/TzxMsg/")
public class TzxInitServiceController {

    private static Log log = LogFactory.getLog(TzxInitServiceController.class);

    private ResourceBundle tzxresource = null;
    @Autowired
    private UpLoadTaskList upLoadTaskList;
    @Autowired
    private ReceiverManagerService receiverManagerService;

    @ResponseBody
    @RequestMapping(value = "/is")
    public MessageBody is() throws Exception {
        MessageBody ret = Global.getMessageBody(false);
        boolean isActive = MessageDispatcher.checkActive();
        String msg = isActive ? "活跃" : "停止";
        ret.setSuccess(isActive);
        ret.setMessage(msg);
        return ret;
    }

//	public TzxInitServiceController(ResourceBundle tzxresource) {
//		this.tzxresource = tzxresource;
//	}

    public TzxInitServiceController() {
        super();
        System.out.println("成功加载了TzxInitServiceController");
    }

    /**
     * 上传
     *
     * @param
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "sendMessage")
    public MessageBody sendMessage(@RequestBody String jsonstr) throws Exception {
        MessageBody ret = Global.getMessageBody(true);
        log.info("接收到请求：" + jsonstr);
        jsonstr = new String(jsonstr.getBytes("iso8859-1"), "utf-8");
        JsonMessageDto dto = JsonMapper.nonDefaultMapper().fromJson(jsonstr, JsonMessageDto.class);
        try {
            SendXML2RifMQ.sendFile2MQ(dto.getFileName());
        } catch (Exception e) {
            log.info(e);
            ret.setSuccess(false);
            ret.setErrorInfo(e.getMessage());

        }
        return ret;
    }
    @ResponseBody
    @RequestMapping(value = "uploadbill")
    //传入参数要写出{msg="ACTION=DAYEND..."}形式
    public MessageBody uploadbill(@RequestBody  String msg) throws Exception {
        MessageBody ret = Global.getMessageBody(true);


        JsonUploadDto dto = JsonMapper.nonDefaultMapper().fromJson(msg, JsonUploadDto.class);
        upLoadTaskList.addTask(dto.getMsg());
        //先直接返回成功，此功能与界面显示上传结果同时开发
        return ret;
    }

    @ResponseBody
    @RequestMapping(value = "uploadloglist")
    public  MessageBody uploadloglist(){
        MessageBody ret = receiverManagerService.uploadloglist();
        return ret;

    }
    @ResponseBody
    @RequestMapping(value = "version")
    public  MessageBody version(){
        MessageBody ret = receiverManagerService.version();
        return ret;

    }

}
