package com.tzx.commapi.rest.mapper;

import com.tzx.commapi.rest.vo.PMSOrder;
import com.tzx.commapi.rest.vo.PMSOrderItem;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface PMSOrderMapper {
    PMSOrder findPMSOrderByKdzdbh(@Param("kdzdbh") String kdzdbh);
    PMSOrder findOrderChanel(@Param("kdzdbh") String kdzdbh);
    List<PMSOrderItem> findPMSOrderItems(@Param("kdzdbh") String kdzdbh,@Param("haveDiscount") boolean haveDiscount,@Param("makeTime") Integer makeTime);
}
