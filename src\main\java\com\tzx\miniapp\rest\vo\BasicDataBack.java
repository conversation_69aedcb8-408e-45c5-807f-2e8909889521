package com.tzx.miniapp.rest.vo;

import java.util.List;

public class BasicDataBack {

	private List<ItemClass> ITEM_CLASS;

	private List<Dish> DISH;

	private List<ComboDetails> COMBO_DETAILS;

	private List<ComboGroup> COMBO_GROUP;

	private List<PaymentWay> PAYMENT_WAY;

	private List<Dish> DISCOUNT;

	private List<Taste> TASTE;

	public List<ItemClass> getITEM_CLASS() {
		return ITEM_CLASS;
	}

	public void setITEM_CLASS(List<ItemClass> iTEM_CLASS) {
		ITEM_CLASS = iTEM_CLASS;
	}

	public List<Dish> getDISH() {
		return DISH;
	}

	public void setDISH(List<Dish> dISH) {
		DISH = dISH;
	}

	public List<ComboDetails> getCOMBO_DETAILS() {
		return COMBO_DETAILS;
	}

	public void setCOMBO_DETAILS(List<ComboDetails> cOMBO_DETAILS) {
		COMBO_DETAILS = cOMBO_DETAILS;
	}

	public List<ComboGroup> getCOMBO_GROUP() {
		return COMBO_GROUP;
	}

	public void setCOMBO_GROUP(List<ComboGroup> cOMBO_GROUP) {
		COMBO_GROUP = cOMBO_GROUP;
	}

	public List<PaymentWay> getPAYMENT_WAY() {
		return PAYMENT_WAY;
	}

	public void setPAYMENT_WAY(List<PaymentWay> pAYMENT_WAY) {
		PAYMENT_WAY = pAYMENT_WAY;
	}

	public List<Dish> getDISCOUNT() {
		return DISCOUNT;
	}

	public void setDISCOUNT(List<Dish> dISCOUNT) {
		DISCOUNT = dISCOUNT;
	}

	public List<Taste> getTASTE() {
		return TASTE;
	}

	public void setTASTE(List<Taste> tASTE) {
		TASTE = tASTE;
	}


}
