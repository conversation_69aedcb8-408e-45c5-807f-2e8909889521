package com.tzx.commapi.rest.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PMSOrderItem {
    private String order_id	;//	订单ID	是
    private String order_item_id	;//	订单明细ID	是	唯一键，期望是UUID
    private String order_item_id_short	;//	订单明细ID短值	否
    private String order_item_user_id	;//	点餐用户ID	否
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone ="GMT+8")
    private Date order_time	;//	下单时间	是
    private String product_id	;//	商品ID	是	和基础档案中product_id一致
    private String product_code	;//	商品编码	否
    private String product_name	;//	商品名称	是
    private String en_product_name	;//	商品英文名称	否
    private BigDecimal product_qty	;// 商品数量	是
    private Integer is_hurry	;//	是否加急：0:否；1:是
    private Integer is_add	;//	是否加菜：0:否；1:是
    private Integer is_gift	;//	是否赠菜：0:否；1:是
    private BigDecimal item_total	;//	商品应收金额	否
    private BigDecimal item_cost	;//	商品实收金额	否
    private BigDecimal discount_total	;//	商品折扣金额	否
    private BigDecimal cooker_bonus	;//	厨师提成金额	否
    private BigDecimal waiter_bonus	;//	服务员提测金额	否
    private String spec_id	;//	商品规格ID	否
    private String spec_name	;//	商品规格名称	否
    private String en_spec_name	;//	商品规格英文名称	否
    private BigDecimal price	;//	商品价格	否
    private String promotion_name	;//	活动名称	否
    private BigDecimal promotion_price	;//	活动优惠金额	否
    private BigDecimal promotion_qty	;//	活动菜品数量	否
    private String make_status	;//	商品制作状态	是	待分配-INIT，等叫-WAIT，待制作-WAITMAKE、制作中-MAKING，已完成-MADE，已取餐-TAKEN
    private Integer make_time	;//	商品制作时间（秒）	是
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone ="GMT+8")
    private Date start_make_time	;//	商品开始制作时间	否
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone ="GMT+8")
    private Date make_complet_time	;//	商品制作完成时间	否
    private Date timeout_time	;//	商品超时时间	否
    private String cooker_id	;//	厨师编号	否
    private String cooker_name	;//	厨师姓名	否
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone ="GMT+8")
    private Date serve_time	;//	上菜时间	否
    private String wake_status	;//	叫起状态：WAIT-等待叫起：IN-叫号中，UP-已叫起	是
    private Date wake_time	;//	叫起时间	否
    private String refund_reason	;//	退菜原因
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone ="GMT+8")
    private Date refund_time	;//	退菜时间
    private String item_memo	;//	备注	否	备注可统一放到 condiments中
    private String item_type	;//	菜明细类型	是	SINGLE(单品)/COMBO(套餐)/COMBO(套餐内单品)
    private String main_flag	;//	是否是主明细	是	0:否；1:是默认都是主明细，除了点套餐的时候，套餐本身这个菜作为主明细设置，套餐下的菜明细作为非主明细设置。
    private String main_order_detail_id	;//	主明细id，套餐下的商品对应的主明细id	否
    private BigDecimal package_price	;//	打包盒金额	否
    private Integer checked	;//	商品是否需要检查	否
    private BigDecimal product_weight	;//	商品重量	否
    private BigDecimal product_tareweight	;//	商品毛重	否
    private String dinning_table_id	;//	用餐桌台ID
    private BigDecimal prepcost	;//	商品预付费	否
    private BigDecimal surcharge	;//	商品附加费	否
    private BigDecimal member_price	;//	会员价	否
    private Integer displayonkds	;//	是否在KDS显示	否
    private Integer sort	;//	菜品顺序	否
    private List<PMSCondiment> condiments	;//	菜品口味信息	否	如果有备注、烹饪方式、配料需要传
    private Integer group_id	;//	分组id	否	菜品分组id
    private String group_name	;//	分组名称	否	菜品分组名称
    @JsonIgnore
    private String kwbz;


    public String getOrder_id() {
        return order_id;
    }

    public void setOrder_id(String order_id) {
        this.order_id = order_id;
    }

    public String getOrder_item_id() {
        return order_item_id;
    }

    public void setOrder_item_id(String order_item_id) {
        this.order_item_id = order_item_id;
    }

    public String getOrder_item_id_short() {
        return order_item_id_short;
    }

    public void setOrder_item_id_short(String order_item_id_short) {
        this.order_item_id_short = order_item_id_short;
    }

    public String getOrder_item_user_id() {
        return order_item_user_id;
    }

    public void setOrder_item_user_id(String order_item_user_id) {
        this.order_item_user_id = order_item_user_id;
    }

    public Date getOrder_time() {
        return order_time;
    }

    public void setOrder_time(Date order_time) {
        this.order_time = order_time;
    }

    public String getProduct_id() {
        return product_id;
    }

    public void setProduct_id(String product_id) {
        this.product_id = product_id;
    }

    public String getProduct_code() {
        return product_code;
    }

    public void setProduct_code(String product_code) {
        this.product_code = product_code;
    }

    public String getProduct_name() {
        return product_name;
    }

    public void setProduct_name(String product_name) {
        this.product_name = product_name;
    }

    public String getEn_product_name() {
        return en_product_name;
    }

    public void setEn_product_name(String en_product_name) {
        this.en_product_name = en_product_name;
    }

    public BigDecimal getProduct_qty() {
        return product_qty;
    }

    public void setProduct_qty(BigDecimal product_qty) {
        this.product_qty = product_qty;
    }

    public Integer getIs_hurry() {
        return is_hurry;
    }

    public void setIs_hurry(Integer is_hurry) {
        this.is_hurry = is_hurry;
    }

    public Integer getIs_add() {
        return is_add;
    }

    public void setIs_add(Integer is_add) {
        this.is_add = is_add;
    }

    public Integer getIs_gift() {
        return is_gift;
    }

    public void setIs_gift(Integer is_gift) {
        this.is_gift = is_gift;
    }

    public BigDecimal getItem_total() {
        return item_total;
    }

    public void setItem_total(BigDecimal item_total) {
        this.item_total = item_total;
    }

    public BigDecimal getItem_cost() {
        return item_cost;
    }

    public void setItem_cost(BigDecimal item_cost) {
        this.item_cost = item_cost;
    }

    public BigDecimal getDiscount_total() {
        return discount_total;
    }

    public void setDiscount_total(BigDecimal discount_total) {
        this.discount_total = discount_total;
    }

    public BigDecimal getCooker_bonus() {
        return cooker_bonus;
    }

    public void setCooker_bonus(BigDecimal cooker_bonus) {
        this.cooker_bonus = cooker_bonus;
    }

    public BigDecimal getWaiter_bonus() {
        return waiter_bonus;
    }

    public void setWaiter_bonus(BigDecimal waiter_bonus) {
        this.waiter_bonus = waiter_bonus;
    }

    public String getSpec_id() {
        return spec_id;
    }

    public void setSpec_id(String spec_id) {
        this.spec_id = spec_id;
    }

    public String getSpec_name() {
        return spec_name;
    }

    public void setSpec_name(String spec_name) {
        this.spec_name = spec_name;
    }

    public String getEn_spec_name() {
        return en_spec_name;
    }

    public void setEn_spec_name(String en_spec_name) {
        this.en_spec_name = en_spec_name;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getPromotion_name() {
        return promotion_name;
    }

    public void setPromotion_name(String promotion_name) {
        this.promotion_name = promotion_name;
    }

    public BigDecimal getPromotion_price() {
        return promotion_price;
    }

    public void setPromotion_price(BigDecimal promotion_price) {
        this.promotion_price = promotion_price;
    }

    public BigDecimal getPromotion_qty() {
        return promotion_qty;
    }

    public void setPromotion_qty(BigDecimal promotion_qty) {
        this.promotion_qty = promotion_qty;
    }

    public String getMake_status() {
        return make_status;
    }

    public void setMake_status(String make_status) {
        this.make_status = make_status;
    }

    public Integer getMake_time() {
        return make_time;
    }

    public void setMake_time(Integer make_time) {
        this.make_time = make_time;
    }

    public Date getStart_make_time() {
        return start_make_time;
    }

    public void setStart_make_time(Date start_make_time) {
        this.start_make_time = start_make_time;
    }

    public Date getMake_complet_time() {
        return make_complet_time;
    }

    public void setMake_complet_time(Date make_complet_time) {
        this.make_complet_time = make_complet_time;
    }

    public Date getTimeout_time() {
        return timeout_time;
    }

    public void setTimeout_time(Date timeout_time) {
        this.timeout_time = timeout_time;
    }

    public String getCooker_id() {
        return cooker_id;
    }

    public void setCooker_id(String cooker_id) {
        this.cooker_id = cooker_id;
    }

    public String getCooker_name() {
        return cooker_name;
    }

    public void setCooker_name(String cooker_name) {
        this.cooker_name = cooker_name;
    }

    public Date getServe_time() {
        return serve_time;
    }

    public void setServe_time(Date serve_time) {
        this.serve_time = serve_time;
    }

    public String getWake_status() {
        return wake_status;
    }

    public void setWake_status(String wake_status) {
        this.wake_status = wake_status;
    }

    public Date getWake_time() {
        return wake_time;
    }

    public void setWake_time(Date wake_time) {
        this.wake_time = wake_time;
    }

    public String getRefund_reason() {
        return refund_reason;
    }

    public void setRefund_reason(String refund_reason) {
        this.refund_reason = refund_reason;
    }

    public Date getRefund_time() {
        return refund_time;
    }

    public void setRefund_time(Date refund_time) {
        this.refund_time = refund_time;
    }

    public String getItem_memo() {
        return item_memo;
    }

    public void setItem_memo(String item_memo) {
        this.item_memo = item_memo;
    }

    public String getItem_type() {
        return item_type;
    }

    public void setItem_type(String item_type) {
        this.item_type = item_type;
    }

    public String getMain_flag() {
        return main_flag;
    }

    public void setMain_flag(String main_flag) {
        this.main_flag = main_flag;
    }

    public String getMain_order_detail_id() {
        return main_order_detail_id;
    }

    public void setMain_order_detail_id(String main_order_detail_id) {
        this.main_order_detail_id = main_order_detail_id;
    }

    public BigDecimal getPackage_price() {
        return package_price;
    }

    public void setPackage_price(BigDecimal package_price) {
        this.package_price = package_price;
    }

    public Integer getChecked() {
        return checked;
    }

    public void setChecked(Integer checked) {
        this.checked = checked;
    }

    public BigDecimal getProduct_weight() {
        return product_weight;
    }

    public void setProduct_weight(BigDecimal product_weight) {
        this.product_weight = product_weight;
    }

    public BigDecimal getProduct_tareweight() {
        return product_tareweight;
    }

    public void setProduct_tareweight(BigDecimal product_tareweight) {
        this.product_tareweight = product_tareweight;
    }

    public String getDinning_table_id() {
        return dinning_table_id;
    }

    public void setDinning_table_id(String dinning_table_id) {
        this.dinning_table_id = dinning_table_id;
    }

    public BigDecimal getPrepcost() {
        return prepcost;
    }

    public void setPrepcost(BigDecimal prepcost) {
        this.prepcost = prepcost;
    }

    public BigDecimal getSurcharge() {
        return surcharge;
    }

    public void setSurcharge(BigDecimal surcharge) {
        this.surcharge = surcharge;
    }

    public BigDecimal getMember_price() {
        return member_price;
    }

    public void setMember_price(BigDecimal member_price) {
        this.member_price = member_price;
    }

    public Integer getDisplayonkds() {
        return displayonkds;
    }

    public void setDisplayonkds(Integer displayonkds) {
        this.displayonkds = displayonkds;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public List<PMSCondiment> getCondiments() {
        return condiments;
    }

    public void setCondiments(List<PMSCondiment> condiments) {
        this.condiments = condiments;
    }

    public Integer getGroup_id() {
        return group_id;
    }

    public void setGroup_id(Integer group_id) {
        this.group_id = group_id;
    }

    public String getGroup_name() {
        return group_name;
    }

    public void setGroup_name(String group_name) {
        this.group_name = group_name;
    }

    public String getKwbz() {
        return kwbz;
    }

    public void setKwbz(String kwbz) {
        this.kwbz = kwbz;
    }
}
