package com.tzx.miniapp.rest.vo;

import javax.persistence.Transient;
import java.io.Serializable;
import java.util.List;

public class Setmeals implements Serializable {
	private float price;
	private float orgprice;
	private int bargainprice;
	private float memberprice;
	private int limitCount;

	@Transient
	private List<Integer> membergid;

	@Transient
	private List<MainDish> maindish;
	@Transient
	private List<Mandatory> mandatory;
	@Transient
	private List<Optional> optional;

	private int number;
	private float realprice;

	private Integer duid;

	public Integer getDuid() {
		return duid;
	}

	public void setDuid(Integer duid) {
		this.duid = duid;
	}

	public float getPrice() {
		return price;
	}

	public void setPrice(float price) {
		this.price = price;
	}

	public float getOrgprice() {
		return orgprice;
	}

	public void setOrgprice(float orgprice) {
		this.orgprice = orgprice;
	}

	public int getBargainprice() {
		return bargainprice;
	}

	public void setBargainprice(int bargainprice) {
		this.bargainprice = bargainprice;
	}

	public float getMemberprice() {
		return memberprice;
	}

	public void setMemberprice(float memberprice) {
		this.memberprice = memberprice;
	}

	public int getLimitCount() {
		return limitCount;
	}

	public void setLimitCount(int limitCount) {
		this.limitCount = limitCount;
	}

	public List<Integer> getMembergid() {
		return membergid;
	}

	public void setMembergid(List<Integer> membergid) {
		this.membergid = membergid;
	}

	public List<MainDish> getMaindish() {
		return maindish;
	}

	public void setMaindish(List<MainDish> maindish) {
		this.maindish = maindish;
	}

	public List<Mandatory> getMandatory() {
		return mandatory;
	}

	public void setMandatory(List<Mandatory> mandatory) {
		this.mandatory = mandatory;
	}


	public List<Optional> getOptional() {
		return optional;
	}

	public void setOptional(List<Optional> optional) {
		this.optional = optional;
	}

	public int getNumber() {
		return number;
	}

	public void setNumber(int number) {
		this.number = number;
	}

	public float getRealprice() {
		return realprice;
	}

	public void setRealprice(float realprice) {
		this.realprice = realprice;
	}

}
