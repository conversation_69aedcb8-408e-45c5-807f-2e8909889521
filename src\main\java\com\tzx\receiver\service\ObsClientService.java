package com.tzx.receiver.service;


import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;
import com.tzx.publics.listener.InitDataListener;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Locale;
import java.util.TimeZone;
import java.util.zip.ZipInputStream;

/**
 * ObsClientUtils
 *
 * <AUTHOR>
 * @version 0.0.1
 * @since 2023-06-19
 */
@Service
@DependsOn("initDataListener")
public class ObsClientService {

    static Logger logger = LoggerFactory.getLogger(ObsClientService.class);
    private static final String HTTP_VERB = "GET";

    public static String getContentByObjectUrl(String objectUrl, String key) {

        if(StringUtils.isEmpty(objectUrl)||StringUtils.isEmpty(key)){
            return null;
        }

        String[] skak=key.split("@");
        String sk=skak[0];
        String ak=skak[1];

        String content = null;

        //获取当前GMT时间
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss z", Locale.ENGLISH);
        sdf.setTimeZone(TimeZone.getTimeZone("GMT"));

        String date = sdf.format(calendar.getTime());

        try {
            String authorization = getAuthorization(objectUrl, date, sk, ak);

            HttpResponse<InputStream> response = Unirest.get(objectUrl)
                    .header("Authorization", authorization)
                    .header("Date", date)
                    .header("User-Agent", "tzxpos/1.0.0")
                    .asBinary();

            InputStream inputStream = response.getBody();
            content = decompressZipFileTo(inputStream);

            logger.debug("obs content:{}", content);
        } catch (Exception e) {
            logger.error("访问obs时发生异常", e);
        }

        return content;

    }

    //解压缩zip文件
    public static String decompressZipFileTo(InputStream inputStream) throws IOException {
        StringBuilder stringBuilder = new StringBuilder();

        try (ZipInputStream zipInputStream = new ZipInputStream(inputStream)) {
            while (zipInputStream.getNextEntry() != null) {
                InputStreamReader inputStreamReader = new InputStreamReader(zipInputStream, "UTF-8");
                BufferedReader bufferedReader = new BufferedReader(inputStreamReader);

                String line;
                while ((line = bufferedReader.readLine()) != null) {
                    stringBuilder.append(line).append("\n");
                }

                bufferedReader.close();
                inputStreamReader.close();
                break;
            }
        }

        return stringBuilder.toString();
    }

    private static String getAuthorization(String objectUrl, String date, String sk, String ak) throws NoSuchAlgorithmException, InvalidKeyException {


        //https://test-162a.obs.cn-north-4.myhuaweicloud.com/config/config.json
        //获取CanonicalizedResource
        int start = objectUrl.indexOf("//") + 1;
        int end = objectUrl.indexOf(".");
        String bucketName = objectUrl.substring(start, end);

        start = objectUrl.indexOf("com") + 3;
        String objectName = objectUrl.substring(start);

        StringBuilder stringToSign = new StringBuilder();

        // 1. 计算StringToSign:
        stringToSign.append(HTTP_VERB);
        stringToSign.append("\n");
        stringToSign.append("\n");
        stringToSign.append("\n");
        stringToSign.append(date);
        stringToSign.append("\n");
        stringToSign.append(bucketName);
        stringToSign.append(objectName);

        // 2. 计算Signature: Base64(Hmac-sha1(SK, StringToSign))
        byte[] hmacSha1Bytes = calculateHmacSha1(sk, stringToSign.toString());
        String hmacSha1 = Base64Utils.encodeToString(hmacSha1Bytes);

        // 3. 计算Authorization
        String authorization = "OBS " + ak + ":" + hmacSha1;

        return authorization;
    }


    private static byte[] calculateHmacSha1(String key, String message)
            throws NoSuchAlgorithmException, InvalidKeyException {
        Mac hmacSha1 = Mac.getInstance("HmacSHA1");
        SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(), "HmacSHA1");
        hmacSha1.init(secretKey);
        return hmacSha1.doFinal(message.getBytes());
    }

}
