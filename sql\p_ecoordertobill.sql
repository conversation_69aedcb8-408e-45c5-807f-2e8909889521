create or replace function p_ecoordertobill(syydh character varying, akdzdbh character varying, ajzzdbh character varying, alsdh character varying, abbbc integer, abbrq datetime, aczry character varying, askjh character varying, aygdlcs integer) returns integer
    language plpgsql
as
$$
DECLARE
    -- 预定单明细、账单、预定账单、账单明细
    re_dd record; zdrecord record; re_ydd record;re_zdmx record;
    -- 套餐明细档案、套餐分组明细档案、餐类明细ID、桌位状态
    re_tcmxk record; tcfzmxk record; iCLMXID int; sZWZT varchar(10);
    -- ？、销售模式、批次编号、？
    sKDLXBH varchar(10);sXSMS varchar(10); sPCBH varchar(10); sNLD varchar(10);
    -- ？、？、？、？、来源
    sKDXDH varchar(10); iYCLXID int; iGKQTID int; iZDYLXID int; sSource varchar(20);
    -- 结账账单编号、点餐序号、调用过程返回值、餐别ID、菜品ID、账单明细数量
    sJZZDBH varchar(20); iDCXH integer; re integer; icbid int; iCMID int; iwdcount int;
    -- 账单明细RWID、套餐点菜序号、套餐金额、明细金额
    irwid int; itcDCXH int; fTCJE numeric(19,4); fMXJE numeric(19,4);
    -- 原转桌状态
    fYZZZT int;
    -- 新转桌状态
    fXZZZT int;
    -- ？
    sCloudSource varchar(50);
    -- 门店营业模式
    aMDYYMS varchar(50);
    -- 设定内容
    sdnr varchar(200);
    -- 来源编号
    sSourceBz varchar(1);
    -- 活动排序id
    inewsyyhfkfsid integer;
    azwbh varchar(50);
    iTotalYHJE numeric(19,4);
    ydsdkRec record;
    ifun integer;
    rfun rescalcmoney;
    res resPayment;
    fkfsRec record;
    sfkfsbh varchar(20);
    brt integer;
    re_discount record;
    iSingleYHJE numeric(19,4);
    iTotalYHJEM numeric(19,4);
    isPlatform integer;
    iFKJE numeric(19,4);
    rTC record;
    rMX record;
    iCount integer;
    iHasCount int;
    nSumCmje NUMERIC(19,2);
    nHasSumCmje NUMERIC(19,2);
    nNowCmje NUMERIC(19,2);
    aERRORITEMCODE varchar(50);
    aECORECEIVEPATTERN varchar(50);
    re_eic record;
begin
    -- **************************************************
    -- 第一步：校验预订账单是否存在、账单是否存在(临时表、正式表)
    -- **************************************************
    if not exists (select * from BT_YDD where YDDH = syydh) then
        return -100;
    end if;
    if exists (select kdzdbh from tq_zdk where yddh = syydh) then
        return -200;
    end if;
    if exists (select kdzdbh from tq_zdlsk where yddh = syydh) then
        return -200;
    end if;
    -- **************************************************
    -- 第二步：落座式是以班次为单位，柜台式是以登录次数为单位统计
    -- 1：柜台式；3：落座式
    -- **************************************************
    -- Add by SunFumeng 如果是落座试，则以传入的班次为准不再重新获取
    select a.sdnr into aMDYYMS from ts_ggcsk a where sdbt = 'MDYYMS';
    IF aMDYYMS <> '3' THEN
        abbbc := P_GetBCMC(CAST(now() as datetime));
    ELSE
        select yl1 into abbbc from tq_jtztk where BBRQ = abbrq and jhid='99' and cznr = 'YYDL' ORDER BY cast(ygdlcs as int) desc LIMIT 1;
    end if;
    if abbbc is null THEN
        abbbc = -1;
    end if;
    -- **************************************************
    -- 第三步：查询员工登录次数
    -- **************************************************
    select COALESCE(max(to_number(ygdlcs, '99G999D9S')), '1') into aYGDLCS from tq_jtztk
    where BBRQ = abbrq and cznr = 'YYDL' and jhid = askjh;
    -- **************************************************
    -- 第四步：校验转桌状态
    -- **************************************************
    -- Add By SunFumeng  2018-07-24 记录一下写入数据库前的转桌状态
    fYZZZT = 0; --默认为0 ，即未转桌
    if exists (select ygdlcs from tq_jtztk where BBRQ = abbrq and cznr = 'YYTC'
                                             and jhid = askjh and to_number(ygdlcs, '99G999D9S') = aYGDLCS) then
        fYZZZT = 1; --1表示已经转桌了
    end if;
    if fYZZZT = 1 then
        return -800; --如果已经转桌了，那么就返回错误代码，不再处理了。只控制转桌即可，因为转桌控制住了，打烊必定控制住了
    end if;
    select COALESCE(ydrs, 0) ydrs, COALESCE(men, 0) men, COALESCE(WOMEN, 0)WOMEN,
           COALESCE(ELDERNUM, 0)ELDERNUM, COALESCE(CHILDNUM, 0)CHILDNUM, before_order_source,
           (COALESCE(totalprice, 0)-COALESCE(commission_amount,0)-COALESCE(shop_rate,0))  totalprice,
           (case shippingmethod when -1 then COALESCE(yl3,0) else 0 end ) yl3,
           COALESCE(shop_rate,0) shop_rate, COALESCE(platform_rate,0) platform_rate,COALESCE(commission_amount,0) commission_amount,
           COALESCE(zlje,0)+COALESCE(ysje,0) yhfkje,yl4,kwxh,yl2,khtmtz,khjlbh,kwxh,shippingmethod
            ,(COALESCE(yl1,0) + (case shippingmethod when -1 then 0 else  COALESCE(yl3,0) end)) as ecototalfee,
           (case shippingmethod when -1 then 0 else COALESCE(yl3,0) end ) zpsfje, COALESCE(parkingnum,1) isPlatform,zlbh  into re_ydd from BT_YDD where YDDH = syydh;
    icbid = null;
    insert into tq_zdk(KDZDBH,LSDH,KDBBRQ,ZWBH,XFKS,KTSJ,JZCS,KTSKJH,FWYH,KTCZRY,KTBCID,NLD,DYZDCS,YJJE,XMXJJE,ZDJE,FKJE,FKCE,ZKJE,ZRJE,MLJE,DPZKJE,YHJE,FSJE,DSLJ,MDJE,ZKL,
                       XSMS,JZSX,SCBJ,CBID,PCBH,KDLXBH,KDXDH,YCLXID,GKQTID,ZDYLXID,MEN,WOAMEN,ETRS,LRRS,FWFZID,FWFBH,ZKFAID,ZKFABH,ZDZT,source,YDDH,qch,zdbz,cwlxbh)
    values(akdzdbh,alsdh,aBBRQ,re_ydd.zlbh,re_ydd.ydrs,now(),0,aSKJH,aczry,aczry,aBBBC,'',0,0,0,0,0,0,0,0,0,0,0,0,0,0,100,'XSMS_WS','ZDSX_WJ',0,
           null,'','','',0,0,0, re_ydd.men, re_ydd.WOMEN, re_ydd.CHILDNUM, re_ydd.ELDERNUM,
           null,'',null,'','',re_ydd.yl4,syydh,re_ydd.khtmtz||'#'||re_ydd.khjlbh,re_ydd.kwxh,'0');
    --乡村基外卖的处理
    update tq_zdk set xmxjje=re_ydd.totalprice,zdje=re_ydd.totalprice,fkje=re_ydd.totalprice,xsms='XSMS_WS',ygdlcs=aYGDLCS,
                      psfje=re_ydd.yl3 ,yhyhje=re_ydd.yl2 - re_ydd.platform_rate,ptyhje=re_ydd.platform_rate,pyyjje=re_ydd.commission_amount,
                      yhfkje=re_ydd.yhfkje,delivery_type =re_ydd.shippingmethod,ecototalfee=re_ydd.ecototalfee,zpsfje=re_ydd.zpsfje,
                      wmtype=2 where kdzdbh=akdzdbh;
    -- **************************************************
    -- 第七步：获取账单信息
    -- **************************************************
    select ZKL, ZDZKL, zkfabh, KDBBRQ, KTBCID, ZWBH, KTSKJH, YHFSBH, ZDZT, JZSX into zdrecord
    FROM TQ_ZDK WHERE KDZDBH = akdzdbh; --增加日志
    insert into ts_rzk(czybh, czyxm, xzt, bbrq, czsj, czsx, skjh)
    values(aczry, '', '预定转账单,开单账单编号:'||akdzdbh||'  预定单号:'||syydh, abbrq, now(), '稽核', askjh); --select * from bt_ydxm2  select * from bt_yddk
    -- 计算点菜序号
    select COALESCE(max(DCXH), 0) into iDCXH from TQ_WDK where KDZDBH = akdzdbh;
    iDCXH := iDCXH + 1;
    select COALESCE(max(syyhfkfsid),0) into inewsyyhfkfsid from TQ_WDK where KDZDBH=akdzdbh;
    inewsyyhfkfsid := inewsyyhfkfsid + 1;  --计算活动序号
    brt = 0;
    --校验菜品是否都在门店
    select a.sdnr into aECORECEIVEPATTERN from ts_ggcsk a where sdbt = 'ECO_RECEIVE_PATTERN';
    for re_dd in select b.cmid, b.cmsx, b.cmmc1, a.xmbh, a.xmsx, a.xmdj, a.xmsl, a.zkl, a.totalprice,
                        a.dwbh, a.kwbh, a.cmje, a.tcbh, a.tcdch, a.fzsl, a.fzje, a.dcxh, a.id,b.cmbh from bt_ydxm2 a
                                                                                                              left join ts_cmk b on a.xmbh = b.cmbh where yddh = sYYDH order by a.id asc
        loop
            if (re_dd.cmbh is null) or (re_dd.cmbh = '') then
                brt = 1;
                exit;
            END IF;
        end loop;
    if (brt = 1) and (aECORECEIVEPATTERN <> '3') THEN
        RETURN -121;
    end if;
    -- **************************************************
    -- 第八步：处理账单明细
    -- **************************************************
    if re_ydd.isPlatform = 1 OR re_ydd.isPlatform = 2 then
        if aECORECEIVEPATTERN = '3' then
            select a.sdnr into aERRORITEMCODE from ts_ggcsk a where sdbt = 'ERROR_ITEM_CODE';
            if aERRORITEMCODE is null then
                RETURN -122;
            end if;
            select a.cmid,a.cmbh,a.cmmc1,a.cmsx into re_eic from ts_cmk a where a.cmbh = aERRORITEMCODE;
            if re_eic.cmid is null then
                RETURN -122;
            end if;
            for re_dd in select b.cmid, b.cmsx, b.cmmc1, a.xmbh, a.xmsx, a.xmdj, a.xmsl, a.zkl, a.totalprice,
                                '份' as dwbh, a.kwbh, a.cmje, a.tcbh, a.tcdch, a.fzsl, a.fzje, a.dcxh, a.id,a.packid,a.xmmc,a.xmsx as ydcmsx
                         from bt_ydxm2 a left join ts_cmk b on a.xmbh = b.cmbh where yddh = sYYDH and coalesce(a.isactivity,0)=0 order by a.id asc
                loop
                    -- CMSX_YC  标准异常； CMSX_YC_J 加价菜异常
                    if (re_dd.ydcmsx = 'CMSX_YC' or re_dd.ydcmsx='CMSX_YC_J') then
                        itcDCXH := iDCXH;
                        -- 异常菜品类型
                        select id into iCLMXID from tq_clmxk a left join (select id clid from tq_clsdk where cbid = icbid) b
                                                                         on (a.clid = b.clid) where a.xmid = re_eic.cmid;
                        insert into TQ_WDK(KDZDBH, CLMXID, CMID, CMBH, CMMC1, CMMC2, DWBH, YZWBH, ZWBH, TCFS, TCBL, TCJE, FZSL,
                                           FZJE, CMDJ, ZDSJ, XDH, XDHSHRY, FWYH, CBDJ, CMSL, CMJE, SJJE, YHJE, ZRJE, DPZKJE, ZKZT, XLID, XLBH, XLZKZT,
                                           ZKL, WDBZ, TMBJ, KWBZ, fsskjh, YHFS, yhfsbh, YHFSID, CDBJ, TSZT, QCZT, TCID, TCDCH, DCXH, CPBH, DCBZ, ZWH,
                                           CSBH, FSBBRQ, FSBCID, CMSX, cjgqbj, ydxmid,syyhfkfsid,yhxh,packid)
                        select akdzdbh, iCLMXID, a.CMID, a.cmbh, re_dd.xmmc, CMMC2, a.dwbh, azwbh, azwbh, '', 0, 0, 0, 0,
                               re_dd.xmdj, 0, '', '', '', CBJE, re_dd.xmsl, re_dd.cmje, re_dd.totalprice, 0, 0, 0, SFZK, A.XLID,
                               A.XLBH, B.XLZKZT, re_dd.zkl, '', now(), re_dd.kwbh, askjh, '', '', null, '*', 'N', 'N', null, '0', iDCXH,
                               '', '', '', '', zdrecord.KDBBRQ, zdrecord.KTBCID, 'CMSX_DP', 0, re_dd.id,inewsyyhfkfsid,0,re_dd.packid from TS_CMK A LEFT JOIN TS_XLK B
                                                                                                                                                              ON A.XLBH = B.XLBH WHERE CMBH = re_eic.cmbh;
                        iDCXH := iDCXH + 1;
                        inewsyyhfkfsid := inewsyyhfkfsid + 1;  --计算活动序号
                    elseif re_dd.ydcmsx = 'CMSX_DP' then
                        itcDCXH := iDCXH;
                        -- 单品类型
                        -- 获取餐类明细ID
                        select id into iCLMXID from tq_clmxk left join (select id clid from tq_clsdk where cbid = icbid) b
                                                                       on (tq_clmxk.clid = b.clid) where tq_clmxk.xmid = re_dd.cmid;
                        insert into TQ_WDK(KDZDBH, CLMXID, CMID, CMBH, CMMC1, CMMC2, DWBH, YZWBH, ZWBH, TCFS, TCBL, TCJE, FZSL,
                                           FZJE, CMDJ, ZDSJ, XDH, XDHSHRY, FWYH, CBDJ, CMSL, CMJE, SJJE, YHJE, ZRJE, DPZKJE, ZKZT, XLID, XLBH, XLZKZT,
                                           ZKL, WDBZ, TMBJ, KWBZ, fsskjh, YHFS, yhfsbh, YHFSID, CDBJ, TSZT, QCZT, TCID, TCDCH, DCXH, CPBH, DCBZ, ZWH,
                                           CSBH, FSBBRQ, FSBCID, CMSX, cjgqbj, ydxmid,syyhfkfsid,yhxh,packid)
                        select akdzdbh, iCLMXID, a.CMID, a.CMBH, CMMC1, CMMC2, a.dwbh, azwbh, azwbh, '', 0, 0, 0, 0,
                               re_dd.xmdj, 0, '', '', '', CBJE, re_dd.xmsl, re_dd.cmje, re_dd.totalprice, 0, 0, 0, SFZK, A.XLID,
                               A.XLBH, B.XLZKZT, re_dd.zkl, '', now(), re_dd.kwbh, askjh, '', '', null, '*', 'N', 'N', null, '0', iDCXH,
                               '', '', '', '', zdrecord.KDBBRQ, zdrecord.KTBCID, 'CMSX_DP', 0, re_dd.id,inewsyyhfkfsid,0,re_dd.packid from TS_CMK A LEFT JOIN TS_XLK B
                                                                                                                                                              ON A.XLBH = B.XLBH WHERE CMBH = re_dd.xmbh;
                        iDCXH := iDCXH + 1;
                        inewsyyhfkfsid := inewsyyhfkfsid + 1;  --计算活动序号
                    else
                        -- 套餐类型
                        itcDCXH := iDCXH;
                        -- 获取餐类明细ID
                        select id into iCLMXID from tq_clmxk left join (select id clid from tq_clsdk where cbid=icbid) b
                                                                       on (tq_clmxk.clid=b.clid) where tq_clmxk.xmid=re_dd.cmid;
                        -- 首先插入套餐主项
                        insert into TQ_WDK(KDZDBH, CLMXID, CMID, CMBH, CMMC1, CMMC2, DWBH, YZWBH, ZWBH, TCFS, TCBL, TCJE, FZSL, FZJE,
                                           CMDJ, ZDSJ, XDH, XDHSHRY, FWYH, CBDJ, CMSL, CMJE, SJJE, YHJE, ZRJE, DPZKJE, ZKZT, XLID, XLBH, XLZKZT, ZKL, WDBZ,
                                           TMBJ, KWBZ, fsskjh, YHFS, yhfsbh, YHFSID, CDBJ, TSZT, QCZT, TCID, TCDCH, DCXH, CPBH, DCBZ, ZWH, CSBH, FSBBRQ,
                                           FSBCID, CMSX, cjgqbj, fzdch, ydxmid,syyhfkfsid,yhxh,packid)
                        select akdzdbh, iCLMXID, a.CMID, a.CMBH, CMMC1, CMMC2, a.dwbh, azwbh, azwbh, '', 0, 0, 0, 0,
                               re_dd.xmdj, 0, '', '', '', CBJE, re_dd.xmsl, re_dd.cmje, re_dd.totalprice, 0, 0, 0, SFZK, A.XLID,
                               A.XLBH, B.XLZKZT, re_dd.zkl, '', now(), re_dd.kwbh, askjh, '', '', null, '*', 'N', 'N', re_dd.cmid, iDCXH,
                               iDCXH, '', '', '', '', zdrecord.KDBBRQ, zdrecord.KTBCID, 'CMSX_TC', 0, 0, re_dd.id,inewsyyhfkfsid,0,re_dd.packid from TS_CMK A
                                                                                                                                                         LEFT JOIN TS_XLK B ON A.XLBH = B.XLBH WHERE CMBH = re_dd.xmbh;
                        -- 然后处理明细
                        for re_tcmxk in select xmid, mxxmid, cmsl, sjjg, cmmc, xcsl, cmxh, cmph, mxlxid, mxlx, id from ts_tcmxk
                                        where xmid = re_dd.cmid order by id
                            loop
                                if re_tcmxk.mxlx = 'ERP_MXLX_SINGLE' then
                                    -- 固定项
                                    insert into TQ_WDK(KDZDBH, CLMXID, CMID, CMBH, CMMC1, CMMC2, DWBH, YZWBH, ZWBH, TCFS, TCBL, TCJE, FZSL,
                                                       FZJE, CMDJ, ZDSJ, XDH, XDHSHRY, FWYH, CBDJ, CMSL, CMJE, SJJE, YHJE, ZRJE, DPZKJE, ZKZT, XLID, XLBH, XLZKZT,
                                                       ZKL, WDBZ, TMBJ, KWBZ, fsskjh, YHFS, yhfsbh, YHFSID, CDBJ, TSZT, QCZT, TCID, TCDCH, DCXH, CPBH, DCBZ, ZWH, CSBH,
                                                       FSBBRQ, FSBCID, CMSX, cjgqbj, SFXSMX, fzdch,syyhfkfsid,yhxh,packid)
                                    select akdzdbh, iCLMXID, a.CMID, a.CMBH, CMMC1, CMMC2, a.dwbh, azwbh, azwbh, '', 0, 0, 0, 0, cmdj, 0,
                                           '', '', '', CBJE, re_dd.xmsl * re_tcmxk.cmsl, re_dd.xmsl * re_tcmxk.sjjg, re_dd.xmsl * re_tcmxk.sjjg,
                                           0, 0, 0, SFZK, A.XLID, A.XLBH, B.XLZKZT, 100, '', now(), '', askjh, '', '', null, '*', 'N', 'N', re_dd.cmid,
                                           itcDCXH, iDCXH, '', '', '', '', zdrecord.KDBBRQ, zdrecord.KTBCID, 'CMSX_MX', 0, 'Y', re_tcmxk.id,inewsyyhfkfsid,0,re_dd.packid from TS_CMK A
                                                                                                                                                                                   LEFT JOIN TS_XLK B ON A.XLBH = B.XLBH WHERE CMID = re_tcmxk.mxxmid;
                                end if;
                            end loop;
                        iDCXH := iDCXH + 1;
                        inewsyyhfkfsid := inewsyyhfkfsid + 1;  --计算活动序号
                        -- 处理套餐分摊
                        re := p_yddtcmxtr(akdzdbh,re_dd.cmid,iDCXH,re_dd.xmsl);
                        -- 重新计算账单套菜明细金额
                        re := P_CalTCMX(akdzdbh);
                    end if;
                end loop;

            ---处理可选套餐start
            for re_dd in select b.cmid, b.cmsx, b.cmmc1, a.xmbh, a.xmsx, case a.xmsx when 'CMSX_MX' then b.cmdj else a.xmdj  end as xmdj, a.xmsl, a.zkl,
                                '份' as dwbh, a.kwbh, case a.xmsx when 'CMSX_MX' then b.cmdj*a.xmsl else a.cmje end as cmje, a.tcbh, a.tcdch, a.fzsl, a.fzje, a.dcxh, a.id,a.packid,a.xmmc,b.CBJE
                                 ,c.XLZKZT,b.XLID,b.XLBH,b.sfzk,a.top_item_id,b.cmmc2 from bt_ydxm2 a
                                                                                               left join ts_cmk b on a.xmbh = b.cmbh  left join TS_XLK c   ON b.XLBH = c.XLBH where yddh = sYYDH  and coalesce(a.isactivity,0)=1 order by a.id
                loop
                    insert into TQ_WDK(KDZDBH, CLMXID, CMID, CMBH, CMMC1, CMMC2, DWBH, YZWBH, ZWBH, TCFS, TCBL, TCJE, FZSL,
                                       FZJE, CMDJ, ZDSJ, XDH, XDHSHRY, FWYH, CBDJ, CMSL, CMJE, SJJE, YHJE, ZRJE, DPZKJE, ZKZT, XLID, XLBH, XLZKZT,
                                       ZKL, WDBZ, TMBJ, KWBZ, fsskjh, YHFS, yhfsbh, YHFSID, CDBJ, TSZT, QCZT, TCID, TCDCH, DCXH, CPBH, DCBZ, ZWH,
                                       CSBH, FSBBRQ, FSBCID, CMSX, cjgqbj, ydxmid,syyhfkfsid,sfxsmx,yongjje,yongjzkl,packid)
                    select akdzdbh, -1, re_dd.cmid, re_dd.xmbh, case when re_dd.xmsx ='CMSX_MX' then ' '||re_dd.xmmc else re_dd.xmmc end xmmc, re_dd.CMMC2,
                           re_dd.dwbh, '', '', '', 0, 0, 0, 0,
                           re_dd.xmdj, 0, '', '', aczry, re_dd.CBJE, re_dd.xmsl, re_dd.cmje, re_dd.cmje, 0, 0, 0, re_dd.sfzk, re_dd.XLID,
                           re_dd.XLBH, re_dd.XLZKZT, 100, '', now(), re_dd.kwbh, askjh, '', '', null, '*', 'N', 'N', re_dd.top_item_id, re_dd.tcdch, re_dd.dcxh,
                           '', '', '', '', zdrecord.KDBBRQ, zdrecord.KTBCID, re_dd.xmsx, 0, re_dd.id,inewsyyhfkfsid,'Y' ,0,100,re_dd.packid;

                end loop;
            iDCXH := iDCXH + 1;
            inewsyyhfkfsid := inewsyyhfkfsid + 1;  --计算活动序号
            -- 处理套餐分摊
            --re := p_yddtcmxtr(akdzdbh,re_dd.cmid,iDCXH,re_dd.xmsl);
            -- 重新计算账单套菜明细金额
            re := P_CalTCMX(akdzdbh);
            ---处理可选套餐end
        else
            for re_dd in select b.cmid, b.cmsx, b.cmmc1, a.xmbh, a.xmsx, a.xmdj, a.xmsl, a.zkl, a.totalprice,
                                '份' as dwbh, a.kwbh, a.cmje, a.tcbh, a.tcdch, a.fzsl, a.fzje, a.dcxh, a.id,a.packid from bt_ydxm2 a
                                                                                                                              left join ts_cmk b on a.xmbh = b.cmbh where yddh = sYYDH  and coalesce(a.isactivity,0)=0 order by a.id asc
                loop
                    --  是否需要沽清处理？
                    if re_dd.cmsx = 'CMSX_DP' then
                        itcDCXH := iDCXH;
                        -- 单品类型
                        -- 获取餐类明细ID
                        select id into iCLMXID from tq_clmxk left join (select id clid from tq_clsdk where cbid = icbid) b
                                                                       on (tq_clmxk.clid = b.clid) where tq_clmxk.xmid = re_dd.cmid;
                        insert into TQ_WDK(KDZDBH, CLMXID, CMID, CMBH, CMMC1, CMMC2, DWBH, YZWBH, ZWBH, TCFS, TCBL, TCJE, FZSL,
                                           FZJE, CMDJ, ZDSJ, XDH, XDHSHRY, FWYH, CBDJ, CMSL, CMJE, SJJE, YHJE, ZRJE, DPZKJE, ZKZT, XLID, XLBH, XLZKZT,
                                           ZKL, WDBZ, TMBJ, KWBZ, fsskjh, YHFS, yhfsbh, YHFSID, CDBJ, TSZT, QCZT, TCID, TCDCH, DCXH, CPBH, DCBZ, ZWH,
                                           CSBH, FSBBRQ, FSBCID, CMSX, cjgqbj, ydxmid,syyhfkfsid,yhxh,packid)
                        select akdzdbh, iCLMXID, a.CMID, a.CMBH, CMMC1, CMMC2, a.dwbh, azwbh, azwbh, '', 0, 0, 0, 0,
                               re_dd.xmdj, 0, '', '', '', CBJE, re_dd.xmsl, re_dd.cmje, re_dd.totalprice, 0, 0, 0, SFZK, A.XLID,
                               A.XLBH, B.XLZKZT, re_dd.zkl, '', now(), re_dd.kwbh, askjh, '', '', null, '*', 'N', 'N', null, '0', iDCXH,
                               '', '', '', '', zdrecord.KDBBRQ, zdrecord.KTBCID, 'CMSX_DP', 0, re_dd.id,inewsyyhfkfsid,0,re_dd.packid from TS_CMK A LEFT JOIN TS_XLK B
                                                                                                                                                              ON A.XLBH = B.XLBH WHERE CMBH = re_dd.xmbh;
                        iDCXH := iDCXH + 1;
                        inewsyyhfkfsid := inewsyyhfkfsid + 1;  --计算活动序号
                    else
                        -- 套餐类型
                        itcDCXH := iDCXH;
                        -- 获取餐类明细ID
                        select id into iCLMXID from tq_clmxk left join (select id clid from tq_clsdk where cbid=icbid) b
                                                                       on (tq_clmxk.clid=b.clid) where tq_clmxk.xmid=re_dd.cmid;
                        -- 首先插入套餐主项
                        insert into TQ_WDK(KDZDBH, CLMXID, CMID, CMBH, CMMC1, CMMC2, DWBH, YZWBH, ZWBH, TCFS, TCBL, TCJE, FZSL, FZJE,
                                           CMDJ, ZDSJ, XDH, XDHSHRY, FWYH, CBDJ, CMSL, CMJE, SJJE, YHJE, ZRJE, DPZKJE, ZKZT, XLID, XLBH, XLZKZT, ZKL, WDBZ,
                                           TMBJ, KWBZ, fsskjh, YHFS, yhfsbh, YHFSID, CDBJ, TSZT, QCZT, TCID, TCDCH, DCXH, CPBH, DCBZ, ZWH, CSBH, FSBBRQ,
                                           FSBCID, CMSX, cjgqbj, fzdch, ydxmid,syyhfkfsid,yhxh,packid)
                        select akdzdbh, iCLMXID, a.CMID, a.CMBH, CMMC1, CMMC2, a.dwbh, azwbh, azwbh, '', 0, 0, 0, 0,
                               re_dd.xmdj, 0, '', '', '', CBJE, re_dd.xmsl, re_dd.cmje, re_dd.totalprice, 0, 0, 0, SFZK, A.XLID,
                               A.XLBH, B.XLZKZT, re_dd.zkl, '', now(), re_dd.kwbh, askjh, '', '', null, '*', 'N', 'N', re_dd.cmid, iDCXH,
                               iDCXH, '', '', '', '', zdrecord.KDBBRQ, zdrecord.KTBCID, 'CMSX_TC', 0, 0, re_dd.id,inewsyyhfkfsid,0,re_dd.packid from TS_CMK A
                                                                                                                                                         LEFT JOIN TS_XLK B ON A.XLBH = B.XLBH WHERE CMBH = re_dd.xmbh;
                        -- 然后处理明细
                        for re_tcmxk in select xmid, mxxmid, cmsl, sjjg, cmmc, xcsl, cmxh, cmph, mxlxid, mxlx, id from ts_tcmxk
                                        where xmid = re_dd.cmid order by id
                            loop
                                if re_tcmxk.mxlx = 'ERP_MXLX_SINGLE' then
                                    -- 固定项
                                    insert into TQ_WDK(KDZDBH, CLMXID, CMID, CMBH, CMMC1, CMMC2, DWBH, YZWBH, ZWBH, TCFS, TCBL, TCJE, FZSL,
                                                       FZJE, CMDJ, ZDSJ, XDH, XDHSHRY, FWYH, CBDJ, CMSL, CMJE, SJJE, YHJE, ZRJE, DPZKJE, ZKZT, XLID, XLBH, XLZKZT,
                                                       ZKL, WDBZ, TMBJ, KWBZ, fsskjh, YHFS, yhfsbh, YHFSID, CDBJ, TSZT, QCZT, TCID, TCDCH, DCXH, CPBH, DCBZ, ZWH, CSBH,
                                                       FSBBRQ, FSBCID, CMSX, cjgqbj, SFXSMX, fzdch,syyhfkfsid,yhxh,packid)
                                    select akdzdbh, iCLMXID, a.CMID, a.CMBH, CMMC1, CMMC2, a.dwbh, azwbh, azwbh, '', 0, 0, 0, 0, cmdj, 0,
                                           '', '', '', CBJE, re_dd.xmsl * re_tcmxk.cmsl, re_dd.xmsl * re_tcmxk.sjjg, re_dd.xmsl * re_tcmxk.sjjg,
                                           0, 0, 0, SFZK, A.XLID, A.XLBH, B.XLZKZT, 100, '', now(), '', askjh, '', '', null, '*', 'N', 'N', re_dd.cmid,
                                           itcDCXH, iDCXH, '', '', '', '', zdrecord.KDBBRQ, zdrecord.KTBCID, 'CMSX_MX', 0, 'Y', re_tcmxk.id,inewsyyhfkfsid,0,re_dd.packid from TS_CMK A
                                                                                                                                                                                   LEFT JOIN TS_XLK B ON A.XLBH = B.XLBH WHERE CMID = re_tcmxk.mxxmid;
                                end if;
                            end loop;
                        iDCXH := iDCXH + 1;
                        inewsyyhfkfsid := inewsyyhfkfsid + 1;  --计算活动序号
                        -- 处理套餐分摊
                        re := p_yddtcmxtr(akdzdbh,re_dd.cmid,iDCXH,re_dd.xmsl);
                        -- 重新计算账单套菜明细金额
                        re := P_CalTCMX(akdzdbh);
                    end if;
                end loop;

            ---处理可选套餐start
            for re_dd in select b.cmid, b.cmsx, b.cmmc1, a.xmbh, a.xmsx, case a.xmsx when 'CMSX_MX' then b.cmdj else a.xmdj  end as xmdj, a.xmsl, a.zkl,
                                '份' as dwbh, a.kwbh, case a.xmsx when 'CMSX_MX' then b.cmdj*a.xmsl else a.cmje end as cmje, a.tcbh, a.tcdch, a.fzsl, a.fzje, a.dcxh, a.id,a.packid,a.xmmc,b.CBJE
                                 ,c.XLZKZT,b.XLID,b.XLBH,b.sfzk,a.top_item_id,b.cmmc2 from bt_ydxm2 a
                                                                                               left join ts_cmk b on a.xmbh = b.cmbh  left join TS_XLK c   ON b.XLBH = c.XLBH where yddh = sYYDH  and coalesce(a.isactivity,0)=1 order by a.id
                loop
                    insert into TQ_WDK(KDZDBH, CLMXID, CMID, CMBH, CMMC1, CMMC2, DWBH, YZWBH, ZWBH, TCFS, TCBL, TCJE, FZSL,
                                       FZJE, CMDJ, ZDSJ, XDH, XDHSHRY, FWYH, CBDJ, CMSL, CMJE, SJJE, YHJE, ZRJE, DPZKJE, ZKZT, XLID, XLBH, XLZKZT,
                                       ZKL, WDBZ, TMBJ, KWBZ, fsskjh, YHFS, yhfsbh, YHFSID, CDBJ, TSZT, QCZT, TCID, TCDCH, DCXH, CPBH, DCBZ, ZWH,
                                       CSBH, FSBBRQ, FSBCID, CMSX, cjgqbj, ydxmid,syyhfkfsid,sfxsmx,yongjje,yongjzkl,packid)
                    select akdzdbh, -1, re_dd.cmid, re_dd.xmbh, case when re_dd.xmsx ='CMSX_MX' then ' '||re_dd.xmmc else re_dd.xmmc end xmmc, re_dd.CMMC2,
                           re_dd.dwbh, '', '', '', 0, 0, 0, 0,
                           re_dd.xmdj, 0, '', '', aczry, re_dd.CBJE, re_dd.xmsl, re_dd.cmje, re_dd.cmje, 0, 0, 0, re_dd.sfzk, re_dd.XLID,
                           re_dd.XLBH, re_dd.XLZKZT, 100, '', now(), re_dd.kwbh, askjh, '', '', null, '*', 'N', 'N', re_dd.top_item_id, re_dd.tcdch, re_dd.dcxh,
                           '', '', '', '', zdrecord.KDBBRQ, zdrecord.KTBCID, re_dd.xmsx, 0, re_dd.id,inewsyyhfkfsid,'Y' ,0,100,re_dd.packid;

                end loop;
            iDCXH := iDCXH + 1;
            inewsyyhfkfsid := inewsyyhfkfsid + 1;  --计算活动序号
            -- 处理套餐分摊
            --re := p_yddtcmxtr(akdzdbh,re_dd.cmid,iDCXH,re_dd.xmsl);
            -- 重新计算账单套菜明细金额
            re := P_CalTCMX(akdzdbh);
            ---处理可选套餐end
        end if;
    else
        for re_dd in select b.cmid, b.cmsx, b.cmmc1, a.xmbh, a.xmsx, a.xmdj, a.xmsl, a.zkl, a.totalprice,
                            '份' as dwbh, a.kwbh, a.cmje, a.tcbh, a.tcdch, a.fzsl, a.fzje, a.dcxh, a.id,a.packid,a.xmmc,b.CBJE
                             ,c.XLZKZT,b.XLID,b.XLBH,b.sfzk,a.top_item_id,b.cmmc2 from bt_ydxm2 a
                                                                                           left join ts_cmk b on a.xmbh = b.cmbh  left join TS_XLK c   ON b.XLBH = c.XLBH where yddh = sYYDH order by a.id
            loop
                insert into TQ_WDK(KDZDBH, CLMXID, CMID, CMBH, CMMC1, CMMC2, DWBH, YZWBH, ZWBH, TCFS, TCBL, TCJE, FZSL,
                                   FZJE, CMDJ, ZDSJ, XDH, XDHSHRY, FWYH, CBDJ, CMSL, CMJE, SJJE, YHJE, ZRJE, DPZKJE, ZKZT, XLID, XLBH, XLZKZT,
                                   ZKL, WDBZ, TMBJ, KWBZ, fsskjh, YHFS, yhfsbh, YHFSID, CDBJ, TSZT, QCZT, TCID, TCDCH, DCXH, CPBH, DCBZ, ZWH,
                                   CSBH, FSBBRQ, FSBCID, CMSX, cjgqbj, ydxmid,syyhfkfsid,sfxsmx,yongjje,yongjzkl,packid)
                select akdzdbh, -1, re_dd.cmid, re_dd.xmbh, case when re_dd.xmsx ='CMSX_MX' then ' '||re_dd.xmmc else re_dd.xmmc end xmmc, re_dd.CMMC2,
                       re_dd.dwbh, '', '', '', 0, 0, 0, 0,
                       re_dd.xmdj, 0, '', '', aczry, re_dd.CBJE, re_dd.xmsl, re_dd.cmje, re_dd.cmje, 0, 0, 0, re_dd.sfzk, re_dd.XLID,
                       re_dd.XLBH, re_dd.XLZKZT, 100, '', now(), re_dd.kwbh, askjh, '', '', null, '*', 'N', 'N', re_dd.top_item_id, re_dd.tcdch, re_dd.dcxh,
                       '', '', '', '', zdrecord.KDBBRQ, zdrecord.KTBCID, re_dd.xmsx, 0, re_dd.id,inewsyyhfkfsid,'Y' ,0,100,re_dd.packid;
            end loop;
        FOR rTC IN SELECT tcdch, TCID, DCXH, CMID, SJJE, ZRJE, CMJE FROM TQ_WDK WHERE KDZDBH = akdzdbh AND CMSX = 'CMSX_TC' AND WDBZ = ''
            LOOP
                select count(*), sum(COALESCE(a.cmdj,0) * a.cmsl)  into iCount,nSumCmje from
                    TQ_WDK  a
                WHERE KDZDBH = akdzdbh AND tcid > 0 AND tcid = rTC.CMID AND tcdch = rTC.tcdch AND DCXH = rTC.DCXH AND a.cmsx <> 'CMSX_TC';
                iHasCount := 0 ;
                nNowCmje := 0;
                nHasSumCmje := 0;
                RAISE NOTICE '#TCID:%,nSumCmje:%,iCount:%,nHasSumCmje:%',rTC.TCID,nSumCmje,iCount,nHasSumCmje;
                FOR rMX in select a.rwid,COALESCE(a.cmdj,0) * a.cmsl as CMJE,COALESCE(a.cmdj,0) as cmdj,a.cmsl from
                    tq_wdk a
                           WHERE a.KDZDBH = akdzdbh AND a.tcid > 0 AND a.tcid = rTC.CMID AND a.tcdch = rTC.tcdch AND DCXH = rTC.DCXH AND a.cmsx <> 'CMSX_TC'
                    loop
                        -- x/rTC.CMJE = rMX.CMJE / nSumCmje
                        nNowCmje := rMX.CMJE * rTC.CMJE / nSumCmje;
                        RAISE NOTICE 'iCount :%,rwid:% ,nNowCmje:%', iCount, rMX.rwid, nNowCmje;
                        if iHasCount = iCount - 1 THEN
                            UPDATE tq_wdk SET cmje = rTC.CMJE - nHasSumCmje,cmdj = rMX.cmdj,sjje = rTC.CMJE - nHasSumCmje  WHERE KDZDBH = akdzdbh AND rwid = rMX.rwid;
                        else
                            UPDATE tq_wdk SET cmje = nNowCmje,cmdj = rMX.cmdj,sjje = nNowCmje  WHERE KDZDBH = akdzdbh AND rwid = rMX.rwid;
                        end if;
                        nHasSumCmje := nHasSumCmje + nNowCmje;
                        iHasCount := iHasCount + 1;
                    end loop;
            end loop;
        inewsyyhfkfsid := inewsyyhfkfsid + 1;
    end if;
    -- **************************************************
    -- 第九步：没有订单明细，不能结账，删除账单，返回错误
    -- **************************************************
    select count(*) into iwdcount from tq_wdk where kdzdbh = akdzdbh;
    if iwdcount = 0 then
        return -400;
    end if;
    iTotalYHJE = re_ydd.shop_rate;
    if(iTotalYHJE <> 0) then
        SELECT * INTO ydsdkRec FROM ts_yhfssdk WHERE yhsx = '69';
        IF NOT found THEN
            RETURN -103;
        END IF;
        iTotalYHJEM  = 0;
        for re_discount in select * from bt_order_discount a where order_code = sYYDH order by a.id asc
            loop
                iSingleYHJE = re_discount.shop_rate;
                iTotalYHJEM = iTotalYHJEM + iSingleYHJE;
                --插入优惠记录
                INSERT INTO TQ_WDK(KDZDBH, CLMXID, CMID, CMBH, CMMC1, CMMC2, DWBH, YZWBH, ZWBH, TCFS,
                                   TCBL, TCJE, FZSL, FZJE, CMDJ, ZDSJ, XDH, XDHSHRY, FWYH, CBDJ,
                                   CMSL, CMJE, SJJE, YHJE, ZRJE, DPZKJE, ZKZT, XLBH, XLZKZT, ZKL,
                                   WDBZ, TMBJ, KWBZ, fsskjh, YHFS, yhfsbh, YHFSID, CDBJ, TSZT, QCZT,
                                   TCID, TCDCH, DCXH, CPBH, DCBZ, ZWH, CSBH, FSBBRQ, FSBCID, fzdch,
                                   syyhfkfsid, yongjzkl, yongjje, SCBJ, JZZDBH, JZBBRQ, JZBCID, JZSKJH,yhxh,ecodiscount_fee)
                SELECT akdzdbh, -1, NULL, 'YH' || ydsdkRec.YHFSBH, re_discount.discount_desc, ydsdkRec.YHFSMC2, '', '', '', '',
                       0, 0, 0, 0, 0, 0, '', '', '', 0,
                       1, -iSingleYHJE, 0, 0, iSingleYHJE, 0, 'N', '', 'N', 100,
                       '', now(), '', '99', ydsdkRec.yhsx, '1', ydsdkRec.id, '*', 'N', 'N',
                       null, '0', iDCXH, '', '', '', '', aBBRQ, zdrecord.KTBCID, null,
                       inewsyyhfkfsid, 100, 0, 0, aJZZDBH, aBBRQ, zdrecord.KTBCID, '99',0,re_discount.discount_fee;
                inewsyyhfkfsid = inewsyyhfkfsid + 1;
            end loop;
        if(iTotalYHJEM <> iTotalYHJE) THEN
            RETURN -107;
        end if;
        UPDATE TQ_ZDK SET ZRJE = COALESCE(ZRJE, 0) + iTotalYHJEM
        WHERE KDZDBH = akdzdbh;
        ifun = P_ZRTR(akdzdbh);
        select * into rfun from  p_calcmoney(akdzdbh);
    end if;
    iSingleYHJE = re_ydd.commission_amount;
    if(iSingleYHJE <> 0) then
        SELECT * INTO ydsdkRec FROM ts_yhfssdk WHERE yhsx = '70';
        IF NOT found THEN
            RETURN -108;
        END IF;
        --插入优惠记录
        INSERT INTO TQ_WDK(KDZDBH, CLMXID, CMID, CMBH, CMMC1, CMMC2, DWBH, YZWBH, ZWBH, TCFS,
                           TCBL, TCJE, FZSL, FZJE, CMDJ, ZDSJ, XDH, XDHSHRY, FWYH, CBDJ,
                           CMSL, CMJE, SJJE, YHJE, ZRJE, DPZKJE, ZKZT, XLBH, XLZKZT, ZKL,
                           WDBZ, TMBJ, KWBZ, fsskjh, YHFS, yhfsbh, YHFSID, CDBJ, TSZT, QCZT,
                           TCID, TCDCH, DCXH, CPBH, DCBZ, ZWH, CSBH, FSBBRQ, FSBCID, fzdch,
                           syyhfkfsid, yongjzkl, yongjje, SCBJ, JZZDBH, JZBBRQ, JZBCID, JZSKJH,yhxh)
        SELECT akdzdbh, -1, NULL, 'YH' || ydsdkRec.YHFSBH, ydsdkRec.YHFSMC1, ydsdkRec.YHFSMC2, '', '', '', '',
               0, 0, 0, 0, 0, 0, '', '', '', 0,
               1, -iSingleYHJE, 0, 0, iSingleYHJE, 0, 'N', '', 'N', 100,
               '', now(), '', '99', ydsdkRec.yhsx, '1', ydsdkRec.id, '*', 'N', 'N',
               null, '0', iDCXH, '', '', '', '', aBBRQ, zdrecord.KTBCID, null,
               inewsyyhfkfsid, 100, 0, 0, aJZZDBH, aBBRQ, zdrecord.KTBCID, '99',0;
        inewsyyhfkfsid = inewsyyhfkfsid + 1;
        UPDATE TQ_ZDK SET ZRJE = COALESCE(ZRJE, 0) + iSingleYHJE
        WHERE KDZDBH = akdzdbh;
        ifun = P_ZRTR(akdzdbh);
        select * into rfun from  p_calcmoney(akdzdbh);
    end if;
    -- **************************************************
    -- 第十步：检验账单金额和明细金额，如果不一致，调整明细记录的金额
    --         检验预定账单实结金额、账单明细实结金额合计是否相等
    -- **************************************************
    -- TODO：菜品金额合计未做判断
    select sum(cmje) cmje, sum(sjje)sjje into re_zdmx from tq_wdk
    where kdzdbh = akdzdbh and cmsx <> 'CMSX_MX';
    -- 20190517，杨文彦，原来判断条件是re_ydd.totalprice<>re_zdmx.cmje，这种判断方式存在问题，会员价方式下，肯定不相等，应该换成sjje
    -- 校验逻辑:bt_ydd.totalprice-bt_ydd.commission_amount-bt_ydd.shop_rate <> sum(bt_ydxm2.xmdj * bt_ydxm2.xmsl)-bt_ydd.shop_rate
    if re_ydd.totalprice <> re_zdmx.sjje then
        return -500;
    end if;
    -- **************************************************
    -- 第十一步： 检验套菜主项金额、明细合计金额是否相等，不等不能结账，删除账单，返回错误
    -- **************************************************
    select coalesce(sum(cmje), 0) into fTCJE from tq_wdk where kdzdbh = akdzdbh and cmsx = 'CMSX_TC';
    select coalesce(sum(cmje), 0) into fMXJE from tq_wdk where kdzdbh = akdzdbh and cmsx = 'CMSX_MX';
    if fTCJE <> fMXJE then
        return -600;
    end if;
    if re_ydd.isPlatform = 0 OR re_ydd.isPlatform = 2 then
        iTotalYHJEM  = 0;
        for re_discount in select c.YHFSBH,c.YHFSMC2,c.YHFSMC1 as discount_desc,c.yhsx ,a.pay_count,a.vocount,a.pay_bb as discount_fee,
                                  COALESCE(c.id, -1) as yhfsid from bt_payments a
                                                                        left join tq_ecotypedic b
                                                                                  on a.pay_no = b.fkfsbh and b.thirdcode = '-100'
                                                                        left join ts_yhfssdk c on b.tzxcode =  c.yhfsbh
                           where a.yddh = sYYDH and  COALESCE(a.pay_bb,0) > 0
                           order by a.id asc
            loop
                IF re_discount.yhfsid = -1 THEN
                    RETURN -109;
                END IF;
                iSingleYHJE = re_discount.discount_fee;
                iTotalYHJEM = iTotalYHJEM + iSingleYHJE;
                --插入优惠记录
                INSERT INTO TQ_WDK(KDZDBH, CLMXID, CMID, CMBH, CMMC1, CMMC2, DWBH, YZWBH, ZWBH, TCFS,
                                   TCBL, TCJE, FZSL, FZJE, CMDJ, ZDSJ, XDH, XDHSHRY, FWYH, CBDJ,
                                   CMSL, CMJE, SJJE, YHJE, ZRJE, DPZKJE, ZKZT, XLBH, XLZKZT, ZKL,
                                   WDBZ, TMBJ, KWBZ, fsskjh, YHFS, yhfsbh, YHFSID, CDBJ, TSZT, QCZT,
                                   TCID, TCDCH, DCXH, CPBH, DCBZ, ZWH, CSBH, FSBBRQ, FSBCID, fzdch,
                                   syyhfkfsid, yongjzkl, yongjje, SCBJ, JZZDBH, JZBBRQ, JZBCID, JZSKJH,yhxh,ecodiscount_fee)
                SELECT akdzdbh, -1, NULL, 'YH' || re_discount.YHFSBH, re_discount.discount_desc, re_discount.YHFSMC2, '', '', '', '',
                       0, 0, 0, 0, 0, 0, '', '', '', 0,
                       1, -iSingleYHJE, 0, 0, iSingleYHJE, 0, 'N', '', 'N', 100,
                       '', now(), '', '99', re_discount.yhsx, '1', re_discount.yhfsid, '*', 'N', 'N',
                       null, '0', iDCXH, '', '', '', '', aBBRQ, zdrecord.KTBCID, null,
                       inewsyyhfkfsid, 100, 0, 0, aJZZDBH, aBBRQ, zdrecord.KTBCID, '99',0,re_discount.discount_fee;
                inewsyyhfkfsid = inewsyyhfkfsid + 1;
            end loop;
        UPDATE TQ_ZDK SET ZRJE = COALESCE(ZRJE, 0) + iTotalYHJEM
        WHERE KDZDBH = akdzdbh;
        ifun = P_ZRTR(akdzdbh);
        select * into rfun from  p_calcmoney(akdzdbh);
        select sum(cmje) cmje, sum(sjje)sjje into re_zdmx from tq_wdk
        where kdzdbh = akdzdbh and cmsx <> 'CMSX_MX';
    end if;
    sJZZDBH = aJZZDBH;
    if re_ydd.isPlatform = 1 then
        SELECT * INTO fkfsRec FROM ts_fkfssdk a left join tq_ecotypedic b
                                                          on a.fkfsbh = b.fkfsbh and b.tzxcode =re_ydd.yl4  where b.fkfsbh is not null
                                                                                                              and  b.thirdcode <> '-100';
        IF NOT found THEN
            RETURN -105;
        END IF;
        select * into res from p_payment(akdzdbh, fkfsRec.id,re_ydd.totalprice, 1,'', '', '', '', askjh, askjh);
        if res.a <> 0 THEN
            RETURN -106;
        end if;
    else
        for fkfsRec in  SELECT a.*,b.vocount FROM bt_payments b inner join ts_fkfssdk a
                                                                           on a.fkfsbh = b.pay_no and b.yddh = syydh where  b.pay_no  is not null
            loop
                IF fkfsRec.fkfsbh is null THEN
                    RETURN -105;
                END IF;
                select * into res from p_payment(akdzdbh, fkfsRec.id,fkfsRec.vocount, 1,'', '', '', '', askjh, askjh);
                if res.a <> 0 THEN
                    RETURN -106;
                end if;
            end loop;
        select coalesce(sum(fkje), 0) into iFKJE from tq_fklslsk where kdzdbh = akdzdbh ;
        if re_zdmx.sjje <> iFKJE then
            return -110;
        end if;
    end if;
    -- **************************************************
    UPDATE TQ_ZDK set JZZDBH = sJZZDBH, JZBBRQ = abbrq, JZSKJH = aSKJH, JZCZRY = aCZRY, JZBCID = aBBBC,
                      JZSX = 'ZDSX_YJ', JZCS = JZCS + 1, JZSJ = now(),KSJZSJ = now(), SCBJ = 0 where KDZDBH = akdzdbh;
    update tq_wdk set JZZDBH = sJZZDBH, JZBBRQ = aBBRQ, JZBCID = aBBBC, JZSKJH = aSKJH, scbj = 0 where KDZDBH = aKDZDBH;
    update tq_fklslsk set JZZDBH = sJZZDBH, JZBCID = aBBBC, scbj = 0 where KDZDBH = aKDZDBH; --增加账单明细的日志
    insert into ts_rzk(czybh, czyxm, xzt, bbrq, czsj, czsx, skjh)
    select aczry, '', '预定转账单,开单账单编号:'||akdzdbh||'  菜品编号:'||cmbh||' 菜品金额:'||sjje,
           abbrq, now(), '稽核', askjh from tq_wdk
    where kdzdbh = aKDZDBH and cmsx <> 'CMSX_MX' order by dcxh, fzdch, rwid;
    -- **************************************************
    -- 第十三步：再次检查转桌状态，如果已经转桌，需要删除所有账单信息，返回失败
    -- **************************************************
    -- Add By SunFumeng 2018-07-24 写数据库操作，全部完成了，再看一下转桌状态是否发生变化
    fXZZZT = 0;--默认为0 ，即未转桌
    if exists (select ygdlcs from tq_jtztk where BBRQ = abbrq and cznr = 'YYTC'
                                             and jhid = askjh and to_number(ygdlcs, '99G999D9S') = aYGDLCS) then
        fXZZZT = 1;--1表示已经转桌了
    end if;
    --如果执行写入操作之前是没转桌，写入之后是转桌了，那么说明转桌操作在该存储过程的执行过程
    --时间段内完成的，这种数据不要保留，否则有可能会有问题
    if fYZZZT = 0 and fXZZZT = 1 THEN
        return -850;
    elseif not exists (select kdzdbh from tq_zdk where kdzdbh = akdzdbh) then --还有一种情况是,执行过程中，执行了转桌子过程的删除无付款的处理
        return -875;
    end if;
    -- Add End
    -- **************************************************
    -- 第十四步：处理账单明细加价菜品RWID
    -- **************************************************
    -- Add By SunFumeng 对jjcrwid的处理
    update tq_wdk set jjcrwid = rwid where kdzdbh = akdzdbh;
    -- Add End
    return 0;
end $$;

alter function p_ecoordertobill(varchar, varchar, varchar, varchar, integer, datetime, varchar, varchar, integer) owner to postgres;

