package com.tzx.publics.util;


import com.tzx.publics.common.BaseData;
import net.sf.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 用来处理Data里的list<?>
 * <AUTHOR>
 * 2015年7月14日-上午10:09:53
 */
public class ReqDataUtil
{
	
	
	
	@SuppressWarnings("unchecked")
	/**
	 * 重写By SunFumeng 兼容 旧版小程序和APP  小程序的Data.data是Obejct ，
	 * 而APP 是List<String,Object>  根据  类型分别获取
	 */
	public static Map<String,Object> getDataMap(BaseData param)
	{
		//如果是List，那么就在看是不是Map
		if(param.getData() instanceof List){
			try {
				List<?> objects = (List)param.getData();
				if (objects.size() > 0 && objects.get(0) instanceof  Map)
				{
					return (Map<String, Object>) objects.get(0);
				}
			} catch (Exception e) {
				return  null; //如果转换类型失败，则返回null
			}
		}

		//如果是Map 就直接转换返回，否则范湖null
		if(param.getData() instanceof Map){
			try {
				return (Map<String, Object>) param.getData();
			} catch (Exception e) {
				return  null; //如果转换类型失败，则返回null
			}
		}
        //既不是List，又不是Map，返回为空
		return  null;

	}

	public static List<JSONObject> getDataJsonList(BaseData param){
		List<JSONObject> result = new ArrayList<JSONObject>();
		//如果是List，那么就在看是不是Map
		if(param.getData() instanceof List){
			try {
				List<?> objects = (List)param.getData();
				for(Object obj: objects){
					JSONObject jsonObject = JSONObject.fromObject(obj);
					result.add(jsonObject);
//					JsonObject jsonObject = JsonObject.

				}
			} catch (Exception e) {
			}
		}
		return result;
	}
}
