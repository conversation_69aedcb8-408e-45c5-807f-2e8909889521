package com.tzx.publics.util;


import com.alibaba.fastjson.JSON;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import javax.crypto.Cipher;
import java.io.IOException;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.util.*;
import java.util.stream.Collectors;

public class XingYiRsa {

    /**
     * 通过公钥public_SecretKey加密param参数，并返回请求体
     *
     * @param param             参数 Array,[]或obj,any,map,{}
     * @param authorizationCode 授权码
     * @param public_SecretKey  加密公钥
     * @return 返回Map信息，直接使用post请求将返回信息作为body发送，请求类型为Content-Type:application/json
     */
    public static Map<String, Object> GetRequestDataEncrypt(Object param, String authorizationCode, String public_SecretKey) {
        byte[] content = Base64.getEncoder().encode(JSON.toJSONBytes(param)); // 将param参数转化为JSON字节数组，并进行Base64编码
        int pagesize = content.length / 90; // 计算数据长度除以90得到需要分成多少页
        List<byte[]> data = new ArrayList<>(); // 创建列表用于存储分页数据
        int page = 1; // 当前页数
        int len = 0; // 当前数据长度
        byte[] decode1 = new byte[91]; // 用于存储分页数据的字节数组
        if (pagesize < 1) {
            decode1 = new byte[content.length]; // 如果只有一页，则分配合适大小的字节数组
        }
        for (int i = 0; i < content.length; i++) {
            decode1[len] = content[i]; // 将数据添加到分页字节数组中
            len++;
            if (i == (90 * page)) {
                data.add(decode1); // 添加分页数据到列表中
                len = 0; // 重置数据长度
                if (pagesize == page) {
                    decode1 = new byte[content.length - i - 1]; // 如果是最后一页，则分配合适大小的字节数组
                } else {
                    decode1 = new byte[91]; // 分配新的字节数组
                }
                page++; // 增加页数
            }
        }
        data.add(decode1); // 将最后一页数据添加到列表中
        List<String> list = new ArrayList<>(); // 创建列表用于存储加密后的数据
        for (byte[] datum : data) {
            byte[] decode2 = new byte[0]; // 创建字节数组
            try {
                decode2 = encryptByPublicKey(datum, Base64.getDecoder().decode(public_SecretKey)); // 使用公钥加密数据
            } catch (Exception e) {
                e.printStackTrace();
            }
            list.add(Base64.getEncoder().encodeToString(decode2)); // 将加密后的数据进行Base64编码，并添加到列表中
        }
        String result = list.stream().collect(Collectors.joining("_*_")); // 将加密数据用"_*_"连接成一个字符串
        Map<String, Object> map = new HashMap<>(); // 创建Map对象用于存储请求体数据
        map.put("data", result); // 将加密数据添加到Map中
        map.put("authorizationCode", authorizationCode); // 将授权码添加到Map中
        return map; // 返回请求体Map对象
    }

    /**
     * 公钥加密
     *
     * @param data 待加密数据
     * @param key  密钥
     * @return byte[] 加密数据
     */
    private static byte[] encryptByPublicKey(byte[] data, byte[] key) throws Exception {
        KeyFactory keyFactory = KeyFactory.getInstance("RSA"); // 实例化密钥工厂，用于生成公钥
        X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(key); // 使用密钥材料初始化公钥规格
        PublicKey pubKey = keyFactory.generatePublic(x509KeySpec); // 生成公钥实例

        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm()); // 根据密钥工厂的算法获取密码器实例
        cipher.init(Cipher.ENCRYPT_MODE, pubKey); // 初始化密码器为加密模式，使用公钥加密数据
        return cipher.doFinal(data); // 对数据进行加密，并返回加密后的字节数组
    }

    // 示例
    public static void main(String[] args) {
        // 创建请求体数据
        Map<String, Object> Data = new HashMap<>();
        Data.put("orderNo", "No12330439..."); // 商品编号
        Data.put("name", "老干妈..."); // 商品名称
        // ...添加其他商品信息

        List<Map<String, Object>> RequestData = new ArrayList<>();
        RequestData.add(Data);

        // 授权码
        String code = "9PY4DS20231870607172541";

        // 加密公钥，请勿暴露
        String publicKey = "MIGEMA0GCSqGSIb3DQEBAQUAA3MAMHACaQC5T1xSmCuLClSZLAiNGfQ0vT64XjNzD1xAyV0Ow20351Aha/zLzx1Lm1GmTPbQ8od/uHPYwVZ9lLg99JTBmcn4bSd0CWKpgd5ttEBcJ9V9LB1S56jZoYqF8qOVq6olD+b5R48a1D5JDwIDAQAB";

        // 获取加密后的请求体
        Map<String, Object> stringObjectMap = GetRequestDataEncrypt(RequestData, code, publicKey);

        // 请求URL
        String url = "http://117.174.113.172:8751/api/order";

        // 创建HttpClient对象
        CloseableHttpClient httpClient = HttpClients.createDefault();

        // 创建HttpPost对象
        HttpPost httpPost = new HttpPost(url);

        // 设置请求体数据
        httpPost.setEntity(new StringEntity(JSON.toJSONString(stringObjectMap), ContentType.APPLICATION_JSON));

        try {
            // 执行请求并获取响应
            CloseableHttpResponse response = httpClient.execute(httpPost);

            // 获取响应实体
            HttpEntity entity = response.getEntity();
            String responseString = EntityUtils.toString(entity);

            // 处理响应结果
            System.out.println("---------------"+responseString);

            // 关闭响应
            response.close();

        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                // 关闭HttpClient
                httpClient.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}

