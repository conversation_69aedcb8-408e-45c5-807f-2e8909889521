package com.tzx.miniapp.rest.vo;

import java.util.List;

public class Toppings {
    private Integer tpid;
    private String tpno;
    private Integer remarkid;
    private Double addprice;
    private Integer order;
    private String tpname;
    private String rcid;
    private List<String> rcidList;
    private String remarktypename;
    private String selectflag;
    private String selectcount;
    private String remarrkexplain;

    public Integer getTpid() {
        return tpid;
    }

    public void setTpid(Integer tpid) {
        this.tpid = tpid;
    }

    public String getTpno() {
        return tpno;
    }

    public void setTpno(String tpno) {
        this.tpno = tpno;
    }

    public Integer getRemarkid() {
        return remarkid;
    }

    public void setRemarkid(Integer remarkid) {
        this.remarkid = remarkid;
    }

    public Double getAddprice() {
        return addprice;
    }

    public void setAddprice(Double addprice) {
        this.addprice = addprice;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public String getTpname() {
        return tpname;
    }

    public void setTpname(String tpname) {
        this.tpname = tpname;
    }

    public String getRcid() {
        return rcid;
    }

    public void setRcid(String rcid) {
        this.rcid = rcid;
    }

    public List<String> getRcidList() {
        return rcidList;
    }

    public void setRcidList(List<String> rcidList) {
        this.rcidList = rcidList;
    }

    public String getRemarktypename() {
        return remarktypename;
    }

    public void setRemarktypename(String remarktypename) {
        this.remarktypename = remarktypename;
    }

    public String getSelectflag() {
        return selectflag;
    }

    public void setSelectflag(String selectflag) {
        this.selectflag = selectflag;
    }

    public String getSelectcount() {
        return selectcount;
    }

    public void setSelectcount(String selectcount) {
        this.selectcount = selectcount;
    }

    public String getRemarrkexplain() {
        return remarrkexplain;
    }

    public void setRemarrkexplain(String remarrkexplain) {
        this.remarrkexplain = remarrkexplain;
    }
}
