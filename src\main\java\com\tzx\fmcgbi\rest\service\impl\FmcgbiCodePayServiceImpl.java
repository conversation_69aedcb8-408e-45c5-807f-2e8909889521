package com.tzx.fmcgbi.rest.service.impl;

import java.io.StringReader;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.jdom2.Document;
import org.jdom2.Element;
import org.jdom2.input.SAXBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.xml.sax.InputSource;


import com.tzx.fmcgbi.common.FmcgbiData;
import com.tzx.fmcgbi.common.FmcgbiException;
import com.tzx.fmcgbi.rest.mapper.FmcgbiCalcuFloatAmtMapper;
import com.tzx.fmcgbi.rest.mapper.FmcgbiCodePayMapper;
import com.tzx.fmcgbi.rest.mapper.FmcgbiQueryCodePayMapper;
import com.tzx.fmcgbi.rest.model.TqJtztk;
import com.tzx.fmcgbi.rest.model.TqThirdExceptOrder;
import com.tzx.fmcgbi.rest.model.TqZdk;
import com.tzx.fmcgbi.rest.model.TsBmkzk;
import com.tzx.fmcgbi.rest.model.TsCmk;
import com.tzx.fmcgbi.rest.service.IFmcgbiCodePayService;
import com.tzx.fmcgbi.rest.vo.CalcMoney;
import com.tzx.fmcgbi.rest.vo.PayResultCoupon;
import com.tzx.fmcgbi.rest.vo.PayResultData;
import com.tzx.fmcgbi.rest.vo.ProdVo;
import com.tzx.fmcgbi.rest.vo.ThirdExceptOrderVo;
import com.tzx.publics.common.BillNoData;
import com.tzx.publics.listener.InitDataListener;
import com.tzx.publics.service.IThirdPayService;
import com.tzx.publics.util.ArithUtil;
import com.tzx.publics.util.DateUtil;
import com.tzx.publics.util.GsonUtil;
import com.tzx.publics.util.SendRequest;
import com.tzx.publics.vo.FkfssdkVo;
import com.tzx.publics.vo.OrganVo;


import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

@Service
public class FmcgbiCodePayServiceImpl implements IFmcgbiCodePayService {
	private final static Logger LOGGER = LoggerFactory.getLogger(FmcgbiCodePayServiceImpl.class);

	@Autowired
	private FmcgbiCodePayMapper codePayMapper;
	@Autowired
	private FmcgbiCalcuFloatAmtMapper calcuFloatAmtMapper;
	@Autowired
	private FmcgbiQueryCodePayMapper queryCodePayMapper;
	@Autowired
	private IThirdPayService thirdPayService;

	public static Map<String, BillNoData> billNoMap = new HashMap<String, BillNoData>(); // 账单号内存
	
	/**
	 * 门店商品查询接口（同步商品到客户端接口）
	 */
	@Transactional
	public FmcgbiData checkAmt(JSONObject requestJson) {
		FmcgbiData data = new FmcgbiData();
		double amt = 0;
		// 菜品信息
		JSONArray detailList = requestJson.optJSONArray("DetailList");
		// 订单金额
		double payAmt = requestJson.optDouble("PayAmt", 0);
		
		// 先循环一次，拿到所有已点菜品编码
		List<String> itemCodeList = new ArrayList<String>();
		for (int i = 0; i < detailList.size(); i++) {
			JSONObject item = detailList.getJSONObject(i);
			itemCodeList.add(item.optString("ProdNo", ""));
		}
		// 根据得到的菜品编码获取菜品价格
		List<ProdVo> prodList = new ArrayList<ProdVo>();
		if (itemCodeList.size() > 0) {
			prodList = calcuFloatAmtMapper.queryItemPrice(itemCodeList);
		}
		// 以编号为key转换为map
		Map<String, Double> priceMap = new HashMap<String, Double>();
		for (ProdVo item : prodList) {
			priceMap.put(item.getProdNo(), Double.parseDouble(item.getPrice()));
		}
		
		// 在循环一次传入数据，根据菜品数量计算出账单应付金额
		for (int i = 0; i < detailList.size(); i++) {
			JSONObject item = detailList.getJSONObject(i);
			String itemCode = item.optString("ProdNo", "");
			double itemNum = item.optDouble("Qty", 0);
			double itemPrice = ArithUtil.mul(priceMap.get(itemCode), itemNum);
			amt = ArithUtil.add(amt, itemPrice);
		}
		
		if(payAmt != amt){
			throw new FmcgbiException("账单金额与应付金额不符，请联系服务员确认价格！");
		}

		data.setSuccess(true);
		data.setCode(0);
		data.setMsg("金额无误");
		return data;
	}

	@Transactional
	public FmcgbiData codePay(JSONObject requestJson, BillNoData billNoData) throws Exception {
		FmcgbiData data = new FmcgbiData();
		billNoMap.put(billNoData.getKdzdbh(), billNoData);
		JSONObject responseJson = new JSONObject();

		JSONArray detailList = requestJson.optJSONArray("DetailList"); // 菜品信息
		double payAmt = requestJson.optDouble("PayAmt", 0); // 订单金额
		String tradeNo = requestJson.optString("TradeNo", ""); // 支付订单号
		String payCode = requestJson.optString("PayCode", ""); // 支付码
		String deviceId = requestJson.optString("DeviceId", ""); // 机器编码
		String[] deviceIds = deviceId.split("_");
		String jtbh = "";
		if (deviceIds.length > 1) {
			jtbh = deviceIds[1];
		}
		PayResultData payResultData = new PayResultData();
		String payType = "4"; // 1支付宝，2微信，3会员，4其他
		String kdzdbh = billNoData.getKdzdbh();
		String jzzdbh = billNoData.getJzzdbh();
		String lsdh = billNoData.getLsdh();
		String qch = billNoData.getQch();
		Date ksjzsj = new Date();

		// 1.准备创建账单所需数据
		Map<String, String> billMap = getBillMap();
		int bcid = Integer.parseInt(billMap.get("bcid"));
		Date bbrq = DateUtil.parseDate(billMap.get("bbrq"));
		String czybh = billMap.get("czybh");
		
		// 2.初始化账单数据
		initBill(billMap, jtbh, tradeNo, billNoData);
		// 3.落单
		CalcMoney calcMoney = ordering(billMap, detailList, billNoData);
		if (calcMoney.getZt() != 0) {
			throw new FmcgbiException("账单金额有误，请重新下单或联系管理员核对菜品金额！");
		}
		if (calcMoney.getZdje().doubleValue() != payAmt) {
			throw new FmcgbiException("账单金额与应付金额不符，请联系服务员确认价格！");
		}
		// 支付
		// 4.根据支付码判断支付方式
		LOGGER.info("4.根据支付码判断支付方式");
		FkfssdkVo fkfs = thirdPayService.getFkfsByNum(payCode);
		if (fkfs.getId() == -1) {
			throw new FmcgbiException("该支付方式无法使用，请联系服务员确认！");
		}
		LOGGER.info("4.根据支付码判断支付方式成功");
		
		// 5.组装微信，支付宝 请求xml
		LOGGER.info("5.组装微信，支付宝 请求xml");
		String date_time = DateUtil.getYYYYMMDDHHMMSS(new Date());
		OrganVo organVo = InitDataListener.organVo;
		String outtradeno = createOuttradeno("tbxf", organVo.getJgxh(), jtbh, kdzdbh, date_time);
		LOGGER.info("5.组装微信，支付宝 请求xml-date_time={},outtradeno={}", date_time, outtradeno);
		String xmlStr = jointXML("01", fkfs, kdzdbh, outtradeno, payCode, jtbh, date_time, organVo, payAmt + "");
		LOGGER.info("5.组装微信，支付宝 请求xml成功");
		
		// 6.请求总部，进行第三方支付
		LOGGER.info("6.请求总部，进行第三方支付");
		String rifUrl = InitDataListener.ggcsMap.get("TZXMQWEBADD");
		LOGGER.info("6.请求总部，进行第三方支付-rifUrl={}", rifUrl);
		LOGGER.info("6.请求总部，进行第三方支付-requestXml={}", xmlStr);
		String rxmlStr = SendRequest.sendPost(rifUrl, xmlStr);
		LOGGER.info("6.请求总部，进行第三方支付-responseXml={}", rxmlStr);
		LOGGER.info("6.请求总部，进行第三方支付成功");
		if ("-1".equals(rxmlStr)) {
			throw new FmcgbiException("总部支付接口异常，请稍后再试！");
		} else if ("timedout".equals(rxmlStr)) {
			responseJson.put("status", "1");
			responseJson.put("msg", "INPROCESS：总部接口超时，请重试查询！");
			data.setCode(1);
			data.setMsg("INPROCESS：总部接口超时，请重试查询");
		} else {
			readStringXmlOut(rxmlStr);
		}
		payResultData = tlPrd.get();
		// 处理结果：0成功，其他失败(1：交易出现错误，2：下单成功，支付处理中3：该交易已关闭)
		LOGGER.info("7.处理支付结果");
		LOGGER.info("7.处理支付结果-payResultData={}", GsonUtil.GsonString(payResultData));
		if ("0".equals(payResultData.getStatus())) {
			codePayMapper.updateToStatus(outtradeno, "0");
			codePayMapper.accountsOrder(kdzdbh, fkfs.getId(), new BigDecimal(payAmt), 1, payCode, "", lsdh, outtradeno, jtbh, czybh);
			Date jzjssj = new Date();
			codePayMapper.updateZdk(qch,"","",kdzdbh, jzzdbh, bbrq, jzjssj, 1, jtbh, czybh, "ZDSX_YJ", ksjzsj, jzjssj, bcid, 1);
			codePayMapper.updateWdk(kdzdbh, jzzdbh, jtbh, bbrq, bcid);
			codePayMapper.updateFklslsk(kdzdbh, jzzdbh, bcid);
			billNoMap.remove(kdzdbh);
			LOGGER.info("7.处理支付结果-扣款成功，清台成功");
			responseJson.put("status", "0");
			responseJson.put("msg", "支付成功");
			data.setCode(0);
			data.setMsg("支付成功");
		}
		if ("1".equals(payResultData.getStatus())) {
			billNoMap.remove(kdzdbh);
			LOGGER.info("3.处理查询结果-失败-状态={}，Msg={}", payResultData.getStatus(), payResultData.getMsg());
			throw new FmcgbiException("交易失败，请重新支付或联系服务员确认:" + payResultData.getMsg());
		}
		if ("2".equals(payResultData.getStatus())) {
			responseJson.put("status", "1");
			responseJson.put("msg", "INPROCESS：等待用户输入密码");
			data.setCode(1);
			data.setMsg("NPROCESS：等待用户输入密码:" + payResultData.getMsg());
		}
		if ("3".equals(payResultData.getStatus())) {
			billNoMap.remove(kdzdbh);
			LOGGER.info("3.处理查询结果-失败-状态={}，Msg={}", payResultData.getStatus(), payResultData.getMsg());
			throw new FmcgbiException("该交易已关闭，请重新支付或联系服务员确认:" + payResultData.getMsg());
		}
		
		// 判断支付类型
		switch (fkfs.getYl3()) {
		case "ERP_FKFS_ZFB":
			payType = "1";
			break;
		case "ERP_FKFS_WX":
			payType = "2";
			break;
		default:
			payType = "4";
			break;
		}
		
		responseJson.put("PayType", payType);// 支付方式 1支付宝，2微信，3会员，4其他
		responseJson.put("CardNo", "");// 会员卡号
		data.setData(responseJson);

		return data;
	}
	
	@Transactional
	public FmcgbiData queryCodePay(JSONObject requestJson) throws Exception {
		FmcgbiData data = new FmcgbiData();
		JSONObject responseJson = new JSONObject();
		
		String tradeNo = requestJson.optString("TradeNo", ""); // 支付订单号
		String payCode = requestJson.optString("PayCode", ""); // 支付码
		PayResultData payResultData = new PayResultData();
		Date ksjzsj = new Date();
	
		// 查询支付状态
		// 1.组装微信，支付宝 请求xml
		LOGGER.info("1.组装微信，支付宝 查询请求xml");
		ThirdExceptOrderVo tteo = queryCodePayMapper.getTteo(tradeNo);
		LOGGER.info("1.组装微信，支付宝 查询请求xml-outtradeno={}", GsonUtil.GsonString(tteo));
		String kdzdbh = tteo.getBillid();
		String outtradeno = tteo.getOrderno();
		String fkfsid = tteo.getPaytypeid();
		String jtbh = tteo.getPosno();
		String payAmt = tteo.getCashamount() + "";
		String czry = tteo.getKtczry();
		int bcid = tteo.getKtbcid();
		Date bbrq = DateUtil.parseDate(tteo.getKdbbrq());
		
		OrganVo organVo = InitDataListener.organVo;
		String xmlStr = toJointXML(organVo, "03", fkfsid, outtradeno, payCode, jtbh, payAmt);
		LOGGER.info("1.组装微信，支付宝 查询请求xml成功");
		
		// 2.请求总部，进行第三方支付
		LOGGER.info("2.请求总部，进行第三方支付查询");
		String rifUrl = InitDataListener.ggcsMap.get("TZXMQWEBADD");
		LOGGER.info("2.请求总部，进行第三方支付查询-rifUrl={}", rifUrl);
		LOGGER.info("2.请求总部，进行第三方支付查询-requestXml={}", xmlStr);
		String rxmlStr = SendRequest.sendPost(rifUrl, xmlStr);
		LOGGER.info("2.请求总部，进行第三方支付查询-responseXml={}", rxmlStr);
		LOGGER.info("2.请求总部，进行第三方支付查询成功");
		if ("-1".equals(rxmlStr)) {
			throw new FmcgbiException("总部支付接口异常，请稍后再试！");
		} else if ("timedout".equals(rxmlStr)) {
			responseJson.put("status", "1");
			responseJson.put("msg", "INPROCESS：总部接口超时，请重试查询！");
			data.setCode(1);
			data.setMsg("INPROCESS：总部接口超时，请重试查询");
		} else {
			readStringXmlOut(rxmlStr);
		}
		payResultData = tlPrd.get();
		// 处理结果：0成功，其他失败(1：交易出现错误，2：下单成功，支付处理中3：该交易已关闭)
		LOGGER.info("3.处理查询结果");
		LOGGER.info("3.处理查询结果-payResultData={}", GsonUtil.GsonString(payResultData));
		if ("0".equals(payResultData.getStatus())) {
			LOGGER.info("3.处理查询结果-kdzdbh={},billNoMap={}", kdzdbh, GsonUtil.GsonString(billNoMap));
			BillNoData billNoData = billNoMap.get(kdzdbh);
			LOGGER.info("3.处理查询结果-billNoData={}", GsonUtil.GsonString(billNoData));
			String jzzdbh = billNoData.getJzzdbh();
			String lsdh = billNoData.getLsdh();
			String qch = billNoData.getQch();
			
			codePayMapper.updateToStatus(outtradeno, "0");
			codePayMapper.accountsOrder(kdzdbh, Integer.parseInt(fkfsid), new BigDecimal(payAmt), 1, payCode, "", lsdh, outtradeno, jtbh, czry);
			Date jzjssj = new Date();
			codePayMapper.updateZdk(qch,"","",kdzdbh, jzzdbh, bbrq, jzjssj, 1, jtbh, czry, "ZDSX_YJ", ksjzsj, jzjssj, bcid, 1);
			codePayMapper.updateWdk(kdzdbh, jzzdbh, jtbh, bbrq, bcid);
			codePayMapper.updateFklslsk(kdzdbh, jzzdbh, bcid);
			billNoMap.remove(kdzdbh);
			LOGGER.info("3.处理支付结果-扣款成功，清台成功");
			responseJson.put("status", "0");
			responseJson.put("msg", "支付成功");
			data.setCode(0);
			data.setMsg("支付成功");
		}
		if ("1".equals(payResultData.getStatus())) {
			billNoMap.remove(kdzdbh);
			LOGGER.info("3.处理查询结果-失败-状态={}，Msg={}", payResultData.getStatus(), payResultData.getMsg());
			throw new FmcgbiException("交易失败，请重新支付或联系服务员确认:" + payResultData.getMsg());
		}
		if ("2".equals(payResultData.getStatus())) {
			responseJson.put("status", "1");
			responseJson.put("msg", "INPROCESS：等待用户输入密码");
			data.setCode(1);
			data.setMsg("NPROCESS：等待用户输入密码:" + payResultData.getMsg());
		}
		if ("3".equals(payResultData.getStatus())) {
			billNoMap.remove(kdzdbh);
			LOGGER.info("3.处理查询结果-失败-状态={}，Msg={}", payResultData.getStatus(), payResultData.getMsg());
			throw new FmcgbiException("该交易已关闭，请重新支付或联系服务员确认:" + payResultData.getMsg());
		}
		
		data.setData(responseJson);

		return data;
	}
	
	public Map<String, String> getBillMap() {
		Map<String, String> billMap = new HashMap<String, String>();
		LOGGER.info("1.准备创建账单所需数据");
		// 获取班次id
		String bcid = codePayMapper.getBcid();
		if (null == bcid) {
			bcid = "0";
		}
		LOGGER.info("1.准备创建账单所需数据-bcid={}", bcid);
		// 获取报表日期
		TsBmkzk bmkzk = codePayMapper.getBmkzk("", "BBRQ");
		String bbrq = DateUtil.getNowDateYYDDMM();
		if (null != bmkzk) {
			bbrq = bmkzk.getNr();
		}
		LOGGER.info("1.准备创建账单所需数据-bbrq={}", bbrq);
		// 获取员工登录次数
		String ygdlcs = "";
		TqJtztk jtzt = codePayMapper.getJtZtk(DateUtil.parseDate(bbrq));
		if (null != jtzt && "YYDL".equals(jtzt.getCznr())) {
			ygdlcs = jtzt.getYgdlcs();
		} else {
			throw new FmcgbiException("门店server未登录，请登录后重试！");
		}
		LOGGER.info("1.准备创建账单所需数据-jtzt：{}", GsonUtil.GsonString(jtzt));

		billMap.put("bcid", bcid);
		billMap.put("bbrq", bbrq);
		billMap.put("ygdlcs", ygdlcs);
		billMap.put("czybh", jtzt.getRybh());
		LOGGER.info("1.准备创建账单所需数据成功：{}", billMap.toString());
		return billMap;
	}
	
	public TqZdk initBill(Map<String, String> billMap, String jtbh, String tradeNo, BillNoData billNoData) {
		LOGGER.info("2.初始化账单数据");
		TqZdk tqZdk = new TqZdk();
		tqZdk.setKdzdbh(billNoData.getKdzdbh());
		tqZdk.setLsdh(billNoData.getLsdh());
		tqZdk.setYddh(tradeNo);
		tqZdk.setJzcs(0);
		tqZdk.setKtskjh(jtbh);
		tqZdk.setFwyh(billMap.get("czybh"));
		tqZdk.setKtczry(billMap.get("czybh"));
		tqZdk.setKtsj(new Date());
		tqZdk.setKdbbrq(DateUtil.parseDate(billMap.get("bbrq")));
		tqZdk.setJzsx("ZDSX_WJ");
		tqZdk.setSource("ZZSM");
		tqZdk.setCbid(-1);
		tqZdk.setXfks(1);
		tqZdk.setDyzdcs(1);
		tqZdk.setXsms("XSMS_TS");
		tqZdk.setYgdlcs(billMap.get("ygdlcs"));
		tqZdk.setZkl(100);
		tqZdk.setYhfsbh(null);
		tqZdk.setKtbcid(Integer.parseInt(billMap.get("bcid")));
		tqZdk.setZdzt("");
		tqZdk.setCwlxbh("0");
		LOGGER.info("2.初始化账单数据-tqZdk：{}", GsonUtil.GsonString(tqZdk));
		// 创建账单
		codePayMapper.insert(tqZdk);
		LOGGER.info("2.初始化账单数据成功");
		return tqZdk;
	}
	
	public CalcMoney ordering(Map<String, String> billMap, JSONArray detailList, BillNoData billNoData) {
		LOGGER.info("3.落单");
		CalcMoney calcMoney = new CalcMoney();
		calcMoney.setZt(-1);
		// 准备订单菜品明细数据map
		Map<String, TsCmk> itemMap = getItemMap(detailList);
		String jgxh = InitDataListener.organVo.getJgxh() + "";
		String jgtxbh = InitDataListener.organVo.getJgtxbh();
		LOGGER.info("3.落单-jgxh={},jgtxbh={}", jgxh.toString(), jgtxbh);
		// 在循环一次传入数据，根据菜品数量分别入库 tq_wdk
		for (int i = 0; i < detailList.size(); i++) {
			JSONObject item = detailList.getJSONObject(i);
			String itemCode = item.optString("ProdNo", "");
			int itemNum = item.optInt("Qty", 0);
			int itemId = itemMap.get(itemCode).getCmid();
			codePayMapper.addCmNew(billNoData.getKdzdbh(), itemId, itemNum, "99", billMap.get("czybh"), "", Math.random() * 10000 + "", 6, "", jgxh, jgtxbh, "");
		}
		codePayMapper.zRtr(billNoData.getKdzdbh());
		calcMoney = codePayMapper.calcMoney(billNoData.getKdzdbh());
		LOGGER.info("3.落单成功：{}", GsonUtil.GsonString(calcMoney));
		return calcMoney;
	}
	
	public Map<String, TsCmk> getItemMap(JSONArray detailList) {
		// 先循环一次，拿到所有已点菜品编码
		List<String> itemCodeList = new ArrayList<String>();
		for (int i = 0; i < detailList.size(); i++) {
			JSONObject item = detailList.getJSONObject(i);
			itemCodeList.add(item.optString("ProdNo", ""));
		}
		LOGGER.info("3.落单-itemCodeList：{}", itemCodeList.toString());
		// 根据得到的菜品编码获取菜品数据
		List<TsCmk> prodList = new ArrayList<TsCmk>();
		if (itemCodeList.size() > 0) {
			prodList = codePayMapper.queryItemInfo(itemCodeList);
		}
		LOGGER.info("3.落单-prodList：{}", GsonUtil.GsonString(prodList));
		// 以编号为key转换为map
		Map<String, TsCmk> itemMap = new HashMap<String, TsCmk>();
		for (TsCmk item : prodList) {
			itemMap.put(item.getCmbh(), item);
		}
		LOGGER.info("3.落单-itemMap：{}", GsonUtil.GsonString(itemMap));

		return itemMap;
	}
	
	public String jointXML(String czlx, FkfssdkVo fkfs, String zdbh, String outtradeno, String dynamicid, String jtbh, String date_time, OrganVo organVo, String fkje) {
		String fkfsid = fkfs.getId() + "";
		String requestXml = toJointXML(organVo, czlx, fkfsid, outtradeno, dynamicid, jtbh, fkje);
		insertTteo(fkfs, outtradeno, zdbh, dynamicid, jtbh, date_time, organVo, fkje);
		
		return requestXml;
	}
	
	public String createOuttradeno(String identification, int jgxh, String jtbh, String zdbh, String date_time) {
		// 第一部分 4位开发标识 tbxf + 4位 OrganID + 2位机号 = 10位
		// 第二部分 8位 lvPartBillNo 账单号的后8位 = 8位
		// 第三部分 14位 lvPaytime 付款时间 = 14位
		// 当第一部分大于10位的时候，先扣除时间的前两位来补，如果不够扣除账单编号来补
		String outtradeno = "";
		int lengthDiff = 0; // 长度差异
		outtradeno = identification + jgxh + jtbh;
		lengthDiff = 10 - outtradeno.length();
		// 第一部分 <=10， 长度不足，用账单号来补位
		if (lengthDiff >= 0) {
			zdbh = zdbh.substring((zdbh.length() - (8 + lengthDiff)), zdbh.length());
			outtradeno = outtradeno + zdbh + date_time;
		}
		// 第一部分 >10
		if (lengthDiff < 0) {
			// 如果超长位数少于或等于2位，只减少日志前两位
			lengthDiff = Math.abs(lengthDiff);
			if (lengthDiff <= 2) {
				zdbh = zdbh.substring((zdbh.length() - 8), zdbh.length());
				date_time = date_time.substring(lengthDiff, date_time.length());
			}
			// 如果超长位数大于2，减少日期前两位，剩下的从账单号扣除
			if (lengthDiff > 2) {
				zdbh = zdbh.substring((zdbh.length() - (8 - lengthDiff + 2)), zdbh.length());
				date_time = date_time.substring(2, date_time.length());
			}
			outtradeno = outtradeno + zdbh + date_time;
		}

		return outtradeno;
	}
	
	public String toJointXML(OrganVo organVo, String czlx, String fkfsid, String outtradeno, String dynamicid, String jtbh, String fkje) {
		StringBuilder reqXml = new StringBuilder();
		reqXml.append("<?xml version=\"1.0\" encoding=\"UTF-8\" ?>");
		reqXml.append("<DATAPACKET>");
		reqXml.append("<HEAD>");
		reqXml.append("<Version>1.0</Version>");
		reqXml.append("<SRC>" + organVo.getJgxh() + "</SRC>");
		reqXml.append("<DES>00</DES>");
		reqXml.append("<DEVID/>");
		reqXml.append("<APP>TZX-StoreBusinessSystem</APP>");
		reqXml.append("<TID>TXDTASHUOCHW35DG39SG0LHHAW04YSDFGH</TID>");
		reqXml.append("<MSGID>{" + java.util.UUID.randomUUID() + "}</MSGID>");
		reqXml.append("<CORID>20051024092733000440</CORID>");
		reqXml.append("<WORKDATE>" + DateUtil.getNowDateYYDDMM() + "</WORKDATE>");
		reqXml.append("<PERSONNEL>0001</PERSONNEL>");
		reqXml.append("<RESERVE>STRING</RESERVE>");
		reqXml.append("</HEAD>");
		reqXml.append("<MSG>");
		reqXml.append("<SUBSYSTEM>CRM</SUBSYSTEM>");
		reqXml.append("<ACTION>ONLINEPAYMENT</ACTION>");
		reqXml.append("<DATAS>");
		reqXml.append("<DATA Name=\"HYK\">");
		reqXml.append("<ROWDATA>");
		reqXml.append("<ROW FKFSID=\"" + fkfsid + "\" FKJGXH=\"" + organVo.getJgxh() + "\" CZLX=\"" + czlx + "\" OUTTRADENO=\"" + outtradeno + "\" SUBJECT=\"" + organVo.getJgmc1() + "\" TOTAL=\"" + fkje + "\" PRODUCTCODE=\"BARCODE_PAY_OFFLINE\" DYNAMICIDTYPE=\"barcode\" DYNAMICID=\"" + dynamicid + "\" STOREID=\"" + organVo.getJgbh() + "\" TERMINALID=\"" + jtbh + "\"/>");
		reqXml.append("</ROWDATA>");
		reqXml.append("</DATA>");
		reqXml.append("<DATA Name=\"HYXX\">");
		reqXml.append("<ROWDATA>");
		reqXml.append("<ROW SFGS=\"\" ZHYID=\"\" ZHYGX=\"1\" ZFBJ=\"1\" HYSFLX=\"\" XSRY=\"\" FZRY=\"\" HYLX=\"\" HYXB=\"\" ZJLX=\"\" ZJHM=\"\" HYMZ=\"\" HYJG=\"\" CSRQ=\"\" WHCD=\"\" YSR=\"\" ZCMC=\"\" SJHM=\"\" JTDH=\"\" EMAIL=\"\" ADDRS=\"\" YZBM=\"\" HYZK=\"\" ZNZK=\"\" YYAH=\"\" PIC1=\"\" CPHM=\"\" CAR=\"\" SFGSCY=\"\" GSID=\"0\" ZWMC=\"\" BMMC=\"\" GSMC=\"\" GSDH=\"\" GSCZ=\"\" GSDZ=\"\" GSYZBM=\"\" KHYH=\"\" YHZH=\"\" YWFW=\"\" SSHY=\"\" HYDW=\"\" WWW=\"\" SWZH=\"\" GMRS=\"\" SZDQ=\"\" SFXYDW=\"\" XYDH=\"\" XYRQ=\"\"/>");
		reqXml.append("</ROWDATA>");
		reqXml.append("</DATA>");
		reqXml.append("<DATA Name=\"FKLS\"> ");
		reqXml.append("<ROWDATA/>");
		reqXml.append("</DATA>");
		reqXml.append("<DATA Name=\"GOODSDETAIL\">");
		reqXml.append("<ROWDATA>");
		reqXml.append("</ROWDATA>");
		reqXml.append("</DATA>");
		reqXml.append("</DATAS>");
		reqXml.append("</MSG>");
		reqXml.append("</DATAPACKET>");
		
		String requestXml = reqXml.toString();
		return requestXml;
	}
	
	public void insertTteo(FkfssdkVo fkfs, String outtradeno, String zdbh, String dynamicid, String jtbh, String date_time, OrganVo organVo, String fkje) {
		String fkfsid = fkfs.getId() + "";

		TqThirdExceptOrder tteo = new TqThirdExceptOrder();
		tteo.setBillid(zdbh);
		tteo.setCreatetime(new Date());
		tteo.setUpdatetime(new Date());
		tteo.setScan_code(dynamicid);
		tteo.setPaytypeid(fkfsid);
		tteo.setPaytime(date_time);
		tteo.setOrderno(outtradeno);
		tteo.setRefund_orderno("TK" + outtradeno);
		tteo.setProductcode("BARCODE_PAY_OFFLINE");
		tteo.setDynamicid_type("barcode");
		tteo.setCashamount(Double.parseDouble(fkje));
		tteo.setOrdermc(organVo.getJgmc1());
		tteo.setDataname("HYK");
		tteo.setUpdatecount(1);
		tteo.setFirst_pay_status("1");
		tteo.setLast_pay_status("");
		tteo.setPay_status("1");
		tteo.setPosno(jtbh);
		tteo.setPayname(fkfs.getFkfsmc1());
		tteo.setErrcount(0);
		codePayMapper.insertThirdExceptOrder(tteo);
	}
	
	private final static ThreadLocal<PayResultData> tlPrd = new ThreadLocal<>();
	public static void readStringXmlOut(String xml) throws Exception {
		StringReader read = new StringReader(xml);
		InputSource source = new InputSource(read);
		SAXBuilder sb = new SAXBuilder();
		Document doc = (Document) sb.build(source);
		Element root = doc.getRootElement();
		PayResultData payResultDatas = new PayResultData();
		tlPrd.set(parse(root, payResultDatas));
	}

	public static PayResultData parse(Element root, PayResultData payResultDatas) {
		List<Element> nodes = root.getChildren();
		int len = nodes.size();
		if (len != 0) {
			for (int i = 0; i < len; i++) {
				Element element = (Element) nodes.get(i);// 循环依次得到子元素
				if ("ROW".equals(element.getName())) {
					if (null != element.getAttributeValue("VOUCHER")) {
						List<PayResultCoupon> couponlist = payResultDatas.getCouponlist();
						PayResultCoupon prc = new PayResultCoupon();
						prc.setCouponBuyPrice(element.getAttributeValue("COUPONBUYPRICE"));
						prc.setDealPrice(element.getAttributeValue("DEALPRICE"));
						prc.setPayableamt(element.getAttributeValue("PAYABLEAMT"));
						prc.setPqlx(element.getAttributeValue("PQLX"));
						prc.setVoucher(element.getAttributeValue("VOUCHER"));
//						prc.setXmid(element.getAttributeValue("XMID"));
						prc.setCmbh(element.getAttributeValue("CMBH"));
						prc.setYzm(element.getAttributeValue("YZM"));
						if (null == couponlist) {
							couponlist = new ArrayList<PayResultCoupon>();
						}
						couponlist.add(prc);
						payResultDatas.setCouponlist(couponlist);
					} else {
						if (null == element.getAttributeValue("AMOUNT")
								|| "null".equals(element.getAttributeValue("AMOUNT"))
								|| "Null".equals(element.getAttributeValue("AMOUNT"))
								|| "NULL".equals(element.getAttributeValue("AMOUNT"))) {
							payResultDatas.setFkje("0");
						} else {
							payResultDatas.setFkje(element.getAttributeValue("AMOUNT"));
						}
						payResultDatas.setDiscountamount(element.getAttributeValue("DISCOUNTAMOUNT"));
						payResultDatas.setFkfs(element.getAttributeValue("FKFS"));
						if ("order not exist".equals(element.getAttributeValue("MSG"))) {
							payResultDatas.setMsg("订单不存在!");
						} else if ("Business Failed".equals(element.getAttributeValue("MSG"))) {
							payResultDatas.setMsg("交易失败!");
						} else {
							payResultDatas.setMsg(element.getAttributeValue("MSG"));
						}
						payResultDatas.setPrivilegeamount(element.getAttributeValue("PRIVILEGEAMOUNT"));
						payResultDatas.setStatus(element.getAttributeValue("STATUS"));
						payResultDatas.setTotal(element.getAttributeValue("TOTAL"));
						payResultDatas.setTradeno(element.getAttributeValue("TRADENO"));
						payResultDatas.setUserid(element.getAttributeValue("USERID"));
						payResultDatas.setYhfsid(element.getAttributeValue("YHFSID"));
						payResultDatas.setWxyhfsid(element.getAttributeValue("WXYHFSID"));
					}
				}
				parse(element, payResultDatas);
			}
		}
		return payResultDatas;
	}
}
