package com.tzx.mobilepos.rest.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName: BillItemVo
 * <AUTHOR>
 * @date 2018-10-15
 * @email <EMAIL>
 * @Description: 账单详情
 */
public class BillItemVo implements Serializable {

	private static final long serialVersionUID = 1L;

	private Integer rwid; // 卧单库主键
	private String itemName; // 菜品名称
	private double cmsl; // 菜品数量
	private BigDecimal cmje; // 金额
	private Integer cmid; // 菜品id
	private Integer clmxid; // 明细id
	private List<BillItemVo> billItemVo;
	private String sfxsmx;
	private Integer dcxh; // 点餐序号
	private String item_taste;
	private int syyhfkfsid;//优惠活动序号
	private String yhfs;

	public BillItemVo() {
		super();
	}

	public Integer getRwid() {
		return rwid;
	}

	public void setRwid(Integer rwid) {
		this.rwid = rwid;
	}

	public String getSfxsmx() {
		return sfxsmx;
	}

	public void setSfxsmx(String sfxsmx) {
		this.sfxsmx = sfxsmx;
	}

	public Integer getClmxid() {
		return clmxid;
	}

	public void setClmxid(Integer clmxid) {
		this.clmxid = clmxid;
	}

	public Integer getCmid() {
		return cmid;
	}

	public void setCmid(Integer cmid) {
		this.cmid = cmid;
	}

	public String getItemName() {
		return itemName;
	}

	public void setItemName(String itemName) {
		this.itemName = itemName;
	}

	public double getCmsl() {
		return cmsl;
	}

	public void setCmsl(double cmsl) {
		this.cmsl = cmsl;
	}

	public BigDecimal getCmje() {
		return cmje;
	}

	public void setCmje(BigDecimal cmje) {
		this.cmje = cmje;
	}

	public List<BillItemVo> getBillItemVo() {
		return billItemVo;
	}

	public void setBillItemVo(List<BillItemVo> billItemVo) {
		this.billItemVo = billItemVo;
	}

	public Integer getDcxh() {
		return dcxh;
	}

	public void setDcxh(Integer dcxh) {
		this.dcxh = dcxh;
	}

	public String getItem_taste() {
		return item_taste;
	}

	public void setItem_taste(String item_taste) {
		this.item_taste = item_taste;
	}

	public int getSyyhfkfsid() {
		return syyhfkfsid;
	}

	public void setSyyhfkfsid(int syyhfkfsid) {
		this.syyhfkfsid = syyhfkfsid;
	}

	public String getYhfs() {
		return yhfs;
	}

	public void setYhfs(String yhfs) {
		this.yhfs = yhfs;
	}
	
	
}
