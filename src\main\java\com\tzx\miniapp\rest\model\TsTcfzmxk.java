package com.tzx.miniapp.rest.model;

import com.tzx.publics.base.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2018-05-21
 */
@Table(name = "TS_TCFZMXK")
public class TsTcfzmxk extends BaseEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	@Id
	@Column(name = "ID")
	private int id;
	@Column(name = "MXID")
	private int mxid;
	@Column(name = "FZID")
	private int fzid;
	@Column(name = "FZSL")
	private double fzsl;
	@Column(name = "FZJE")
	private double fzje;
	@Column(name = "SFMR")
	private String sfmr;
	@Column(name = "JGTXBH")
	private String jgtxbh;
	@Column(name = "YL1")
	private String yl1;
	@Column(name = "YL2")
	private String yl2;
	@Column(name = "YL3")
	private String yl3;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public int getMxid() {
		return mxid;
	}

	public void setMxid(int mxid) {
		this.mxid = mxid;
	}

	public int getFzid() {
		return fzid;
	}

	public void setFzid(int fzid) {
		this.fzid = fzid;
	}

	public double getFzsl() {
		return fzsl;
	}

	public void setFzsl(double fzsl) {
		this.fzsl = fzsl;
	}

	public double getFzje() {
		return fzje;
	}

	public void setFzje(double fzje) {
		this.fzje = fzje;
	}

	public String getSfmr() {
		return sfmr;
	}

	public void setSfmr(String sfmr) {
		this.sfmr = sfmr;
	}

	public String getJgtxbh() {
		return jgtxbh;
	}

	public void setJgtxbh(String jgtxbh) {
		this.jgtxbh = jgtxbh;
	}

	public String getYl1() {
		return yl1;
	}

	public void setYl1(String yl1) {
		this.yl1 = yl1;
	}

	public String getYl2() {
		return yl2;
	}

	public void setYl2(String yl2) {
		this.yl2 = yl2;
	}

	public String getYl3() {
		return yl3;
	}

	public void setYl3(String yl3) {
		this.yl3 = yl3;
	}

}
