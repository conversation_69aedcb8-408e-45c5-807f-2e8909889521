package com.tzx.receiver.entity.msg;

import java.io.Serializable;
import java.util.Date;

public class Iteminfo
        implements Serializable {
    private Integer id;
    private String bmbh;
    private String yydbh;
    private String sjxmbh;
    private String xmbh;
    private Integer jgxh;
    private String cmmc;
    private Integer xmid;
    private Integer lbid;
    private String xlbh;
    private String dlbh;
    private Integer dwbh;
    private String dwmc;
    private String xmgg;
    private String bhdm;
    private String pydm;
    private String wbdm;
    private Double bzdj;
    private Double xsdj;
    private Double csjga;
    private Double cxjgb;
    private String jgsx;
    private String cyxl;
    private String cmpxlbh;
    private String cmpbh;
    private String zkzt;
    private String shr;
    private String xwmc;
    private Date lrrq;
    private Date xgrq;
    private String sfsj;
    private String sfxgsdjg;
    private Double cbdj;
    private Double mll;
    private Integer ylzs;
    private String sftc;
    private Double tcbl;
    private String sfxgmc;
    private String sfxgsl;
    private String sfdd;
    private String sfkd;
    private String xmtm;
    private String sfzdxf;
    private String sffwf;
    private String sfyye;
    private String sffwxm;
    private String fwxmlx;
    private String sfsd;
    private String sfsdts;
    private Integer zdsj;
    private String sfhx;
    private String sfyx;
    private String xfbz;
    private String lbsx;
    private String sfcd;
    private String fddlbh;
    private String xmsx;
    private String tslxbh;
    private String sfkgh;
    private String sfbxxz;
    private String sffz;
    private String sfgd;
    private String cwkmbh;
    private Integer cddl;
    private Double cbl;
    private String tczt;
    private Integer cmfh;
    private String tszt;
    private String sfqy;
    private String sffzsl;
    private String tcsfdz;
    private String zdcp;
    private Integer xlid;
    private Integer dlid;
    private String kvssfxs;
    private String sfxsmx;
    private String memo10;
    private String biurl;
    private String miurl;
    private String siurl;
    private String ifFoodBox;        //是否餐盒
    private Integer foodBoxSet;        //餐盒设定
    private String IfSpec;
    private String selfOrderPhoto;
    private String sfcz;
    private String shorthandCode;
    private String ifchangeableprice;

    public String getIfFoodBox() {
        return ifFoodBox;
    }

    public void setIfFoodBox(String ifFoodBox) {
        this.ifFoodBox = ifFoodBox;
    }

    public Integer getFoodBoxSet() {
        return foodBoxSet;
    }

    public void setFoodBoxSet(Integer foodBoxSet) {
        this.foodBoxSet = foodBoxSet;
    }

    public String getBiurl() {
        return biurl;
    }

    public void setBiurl(String biurl) {
        this.biurl = biurl;
    }

    public String getMiurl() {
        return miurl;
    }

    public void setMiurl(String miurl) {
        this.miurl = miurl;
    }

    public String getSiurl() {
        return siurl;
    }

    public void setSiurl(String siurl) {
        this.siurl = siurl;
    }

    public String getMemo10() {
        return this.memo10;
    }

    public void setMemo10(String memo10) {
        this.memo10 = memo10;
    }

    public String getTcsfdz() {
        return this.tcsfdz;
    }

    public void setTcsfdz(String tcsfdz) {
        this.tcsfdz = "N";
    }

    public String getTszt() {
        return this.tszt;
    }

    public void setTszt(String tszt) {
        this.tszt = tszt;
    }

    public Integer getCmfh() {
        return this.cmfh;
    }

    public void setCmfh(Integer cmfh) {
        this.cmfh = cmfh;
    }

    public String getCwkmbh() {
        return this.cwkmbh;
    }

    public void setCwkmbh(String cwkmbh) {
        this.cwkmbh = cwkmbh;
    }

    public Integer getId() {
        return this.xmid;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getBmbh() {
        return this.bmbh;
    }

    public void setBmbh(String bmbh) {
        this.bmbh = bmbh;
    }

    public String getYydbh() {
        return this.yydbh;
    }

    public void setYydbh(String yydbh) {
        this.yydbh = yydbh;
    }

    public String getSjxmbh() {
        return this.sjxmbh;
    }

    public void setSjxmbh(String sjxmbh) {
        this.sjxmbh = sjxmbh;
    }

    public String getXmbh() {
        return this.xmbh;
    }

    public void setXmbh(String xmbh) {
        this.xmbh = xmbh;
    }

    public Integer getJgxh() {
        return this.jgxh;
    }

    public void setJgxh(Integer jgxh) {
        this.jgxh = jgxh;
    }

    public String getCmmc() {
        return this.cmmc;
    }

    public void setCmmc(String cmmc) {
        this.cmmc = cmmc;
    }

    public Integer getLbid() {
        return this.lbid;
    }

    public void setLbid(Integer lbid) {
        this.lbid = lbid;
    }

    public Integer getCddl() {
        return this.cddl;
    }

    public void setCddl(Integer cddl) {
        this.cddl = cddl;
    }

    public Integer getXmid() {
        return this.xmid;
    }

    public void setXmid(Integer xmid) {
        this.xmid = xmid;
    }

    public String getXlbh() {
        return this.xlbh;
    }

    public void setXlbh(String xlbh) {
        this.xlbh = xlbh;
    }

    public String getDlbh() {
        return this.dlbh;
    }

    public void setDlbh(String dlbh) {
        this.dlbh = dlbh;
    }

    public String getDwmc() {
        return this.dwmc;
    }

    public void setDwmc(String dwmc) {
        this.dwmc = dwmc;
    }

    public String getXmgg() {
        return this.xmgg;
    }

    public void setXmgg(String xmgg) {
        this.xmgg = xmgg;
    }

    public String getBhdm() {
        return this.bhdm;
    }

    public void setBhdm(String bhdm) {
        this.bhdm = bhdm;
    }

    public String getPydm() {
        return this.pydm;
    }

    public void setPydm(String pydm) {
        this.pydm = pydm;
    }

    public String getWbdm() {
        return this.wbdm;
    }

    public void setWbdm(String wbdm) {
        this.wbdm = wbdm;
    }

    public Double getBzdj() {
        return this.bzdj;
    }

    public void setBzdj(Double bzdj) {
        this.bzdj = bzdj;
    }

    public Double getXsdj() {
        return this.xsdj;
    }

    public void setXsdj(Double xsdj) {
        this.xsdj = xsdj;
    }

    public Double getCsjga() {
        return this.csjga;
    }

    public void setCsjga(Double csjga) {
        this.csjga = csjga;
    }

    public Double getCxjgb() {
        return this.cxjgb;
    }

    public void setCxjgb(Double cxjgb) {
        this.cxjgb = cxjgb;
    }

    public String getJgsx() {
        return this.jgsx;
    }

    public void setJgsx(String jgsx) {
        this.jgsx = jgsx;
    }

    public String getCyxl() {
        return this.cyxl;
    }

    public void setCyxl(String cyxl) {
        this.cyxl = cyxl;
    }

    public String getCmpxlbh() {
        return this.cmpxlbh;
    }

    public void setCmpxlbh(String cmpxlbh) {
        this.cmpxlbh = cmpxlbh;
    }

    public String getCmpbh() {
        return this.cmpbh;
    }

    public void setCmpbh(String cmpbh) {
        this.cmpbh = cmpbh;
    }

    public String getZkzt() {
        return this.zkzt;
    }

    public void setZkzt(String zkzt) {
        this.zkzt = zkzt;
    }

    public String getShr() {
        return this.shr;
    }

    public void setShr(String shr) {
        this.shr = shr;
    }

    public String getXwmc() {
        return this.xwmc;
    }

    public void setXwmc(String xwmc) {
        this.xwmc = xwmc;
    }

    public Date getLrrq() {
        return this.lrrq;
    }

    public void setLrrq(Date lrrq) {
        this.lrrq = lrrq;
    }

    public Date getXgrq() {
        return this.xgrq;
    }

    public void setXgrq(Date xgrq) {
        this.xgrq = xgrq;
    }

    public String getSfsj() {
        return this.sfsj;
    }

    public void setSfsj(String sfsj) {
        this.sfsj = sfsj;
    }

    public String getSfxgsdjg() {
        return this.sfxgsdjg;
    }

    public void setSfxgsdjg(String sfxgsdjg) {
        this.sfxgsdjg = sfxgsdjg;
    }

    public Double getCbdj() {
        return this.cbdj;
    }

    public void setCbdj(Double cbdj) {
        this.cbdj = cbdj;
    }

    public Double getMll() {
        return this.mll;
    }

    public void setMll(Double mll) {
        this.mll = mll;
    }

    public Integer getYlzs() {
        return this.ylzs;
    }

    public void setYlzs(Integer ylzs) {
        this.ylzs = ylzs;
    }

    public String getSftc() {
        return this.sftc;
    }

    public void setSftc(String sftc) {
        this.sftc = sftc;
    }

    public Double getCbl() {
        return this.cbl;
    }

    public void setCbl(Double cbl) {
        this.cbl = cbl;
    }

    public String getTczt() {
        return this.tczt;
    }

    public void setTczt(String tczt) {
        this.tczt = tczt;
    }

    public Double getTcbl() {
        return this.tcbl;
    }

    public void setTcbl(Double tcbl) {
        this.tcbl = tcbl;
    }

    public String getSfxgmc() {
        return this.sfxgmc;
    }

    public void setSfxgmc(String sfxgmc) {
        this.sfxgmc = sfxgmc;
    }

    public String getSfxgsl() {
        return this.sfxgsl;
    }

    public void setSfxgsl(String sfxgsl) {
        this.sfxgsl = sfxgsl;
    }

    public String getSfdd() {
        return this.sfdd;
    }

    public void setSfdd(String sfdd) {
        this.sfdd = sfdd;
    }

    public String getSfkd() {
        return this.sfkd;
    }

    public void setSfkd(String sfkd) {
        this.sfkd = sfkd;
    }

    public String getXmtm() {
        return this.xmtm;
    }

    public void setXmtm(String xmtm) {
        this.xmtm = xmtm;
    }

    public String getSfzdxf() {
        return this.sfzdxf;
    }

    public void setSfzdxf(String sfzdxf) {
        this.sfzdxf = sfzdxf;
    }

    public String getSffwf() {
        return this.sffwf;
    }

    public void setSffwf(String sffwf) {
        this.sffwf = sffwf;
    }

    public String getSfyye() {
        return this.sfyye;
    }

    public void setSfyye(String sfyye) {
        this.sfyye = sfyye;
    }

    public String getSffwxm() {
        return this.sffwxm;
    }

    public void setSffwxm(String sffwxm) {
        this.sffwxm = sffwxm;
    }

    public String getFwxmlx() {
        return this.fwxmlx;
    }

    public void setFwxmlx(String fwxmlx) {
        this.fwxmlx = fwxmlx;
    }

    public String getSfsd() {
        return this.sfsd;
    }

    public void setSfsd(String sfsd) {
        this.sfsd = sfsd;
    }

    public String getSfsdts() {
        return this.sfsdts;
    }

    public void setSfsdts(String sfsdts) {
        this.sfsdts = sfsdts;
    }

    public Integer getZdsj() {
        return this.zdsj;
    }

    public void setZdsj(Integer zdsj) {
        this.zdsj = zdsj;
    }

    public String getSfhx() {
        return this.sfhx;
    }

    public void setSfhx(String sfhx) {
        this.sfhx = sfhx;
    }

    public String getSfyx() {
        return this.sfyx;
    }

    public void setSfyx(String sfyx) {
        this.sfyx = sfyx;
    }

    public String getXfbz() {
        return this.xfbz;
    }

    public void setXfbz(String xfbz) {
        this.xfbz = xfbz;
    }

    public String getLbsx() {
        return this.lbsx;
    }

    public void setLbsx(String lbsx) {
        this.lbsx = lbsx;
    }

    public String getSfcd() {
        return this.sfcd;
    }

    public void setSfcd(String sfcd) {
        this.sfcd = sfcd;
    }

    public String getFddlbh() {
        return this.fddlbh;
    }

    public void setFddlbh(String fddlbh) {
        this.fddlbh = fddlbh;
    }

    public String getXmsx() {
        return this.xmsx;
    }

    public void setXmsx(String xmsx) {
        this.xmsx = xmsx;
    }

    public String getTslxbh() {
        return this.tslxbh;
    }

    public void setTslxbh(String tslxbh) {
        this.tslxbh = tslxbh;
    }

    public String getSfkgh() {
        return this.sfkgh;
    }

    public void setSfkgh(String sfkgh) {
        this.sfkgh = sfkgh;
    }

    public String getSfbxxz() {
        return this.sfbxxz;
    }

    public void setSfbxxz(String sfbxxz) {
        this.sfbxxz = sfbxxz;
    }

    public String getSffz() {
        return this.sffz;
    }

    public void setSffz(String sffz) {
        this.sffz = sffz;
    }

    public String getSfgd() {
        return this.sfgd;
    }

    public void setSfgd(String sfgd) {
        this.sfgd = sfgd;
    }

    public Integer getDwbh() {
        return this.dwbh;
    }

    public void setDwbh(Integer dwbh) {
        this.dwbh = dwbh;
    }

    public String getSfqy() {
        return this.sfqy;
    }

    public void setSfqy(String sfqy) {
        this.sfqy = sfqy;
    }

    public String getSffzsl() {
        return this.sffzsl;
    }

    public void setSffzsl(String sffzsl) {
        this.sffzsl = sffzsl;
    }

    public String getZdcp() {
        return this.zdcp;
    }

    public void setZdcp(String zdcp) {
        this.zdcp = zdcp;
    }

    public Integer getXlid() {
        return this.xlid;
    }

    public void setXlid(Integer xlid) {
        this.xlid = xlid;
    }

    public Integer getDlid() {
        return this.dlid;
    }

    public void setDlid(Integer dlid) {
        this.dlid = dlid;
    }

    public String getKvssfxs() {
        return this.kvssfxs;
    }

    public void setKvssfxs(String kvssfxs) {
        this.kvssfxs = kvssfxs;
    }

    public String getSfxsmx() {
        return this.sfxsmx;
    }

    public void setSfxsmx(String sfxsmx) {
        this.sfxsmx = sfxsmx;
    }

    public String getIfSpec() {
        return IfSpec;
    }

    public void setIfSpec(String ifSpec) {
        IfSpec = ifSpec;
    }

    public String getSelfOrderPhoto() {
        return selfOrderPhoto;
    }

    public void setSelfOrderPhoto(String selfOrderPhoto) {
        this.selfOrderPhoto = selfOrderPhoto;
    }

    public String getSfcz() {
        return sfcz;
    }

    public void setSfcz(String sfcz) {
        this.sfcz = sfcz;
    }

    public String getShorthandCode() {
        return shorthandCode;
    }

    public void setShorthandCode(String shorthandCode) {
        this.shorthandCode = shorthandCode;
    }

    public String getIfchangeableprice() {
        return ifchangeableprice;
    }

    public void setIfchangeableprice(String ifchangeableprice) {
        this.ifchangeableprice = ifchangeableprice;
    }
}