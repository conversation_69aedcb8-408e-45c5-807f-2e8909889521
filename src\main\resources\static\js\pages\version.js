$(function(){
    loadversion();
});
function loadversion() {
    $.ajax({
        url : "/TzxMsg/TzxMsg/version",
        type : "POST",
        async:false,
        cache:false,
        success : function(data) {
            if (data.success){
                $("#baseversion").text(data.data["BASE_VERSION"]);
                $("#baseversion").attr("onclick","showbox('服务主程序历史版本记录',"+
                    "'服务主版历史版本记录','"+data.data["BASE_VERSION_LOG"]+"')");
                $("#miniappversion").text(data.data["MINIAPP_VERSION"]);
                $("#miniappversion").attr("onclick","showbox('APP服务历史版本记录',"+
                    "'APP服务历史版本记录','"+data.data["MINIAPP_VERSION_LOG"]+"')");
                $("#mobileposversion").text(data.data["MOBILEPOS_VERSION"]);
                $("#mobileposversion").attr("onclick","showbox('小程序服务历史版本记录',"+
                    "'小程序服务历史版本记录','"+data.data["MOBILEPOS_VERSION_LOG"]+"')");
                $("#recevierversion").text(data.data["RECEVIER_VERSION"]);
                $("#recevierversion").attr("onclick","showbox('监听服务历史版本记录',"+
                    "'监听服务历史版本记录','"+data.data["RECEVIER_VERSION_LOG"]+"')");

            }else{
                showmessage($("#versionmsg"));
            }
        },
        error : function() {
            showmessage($("#versionmsg"));
        }
    });
    
}
window.onclick = function close(e) {
    if (e.target == $("background")) {
        closebox();
    }
}

function showbox(caption,title,detail) {
    $("popcaption").text(caption);
    $("poptitle").text(title);
    $("popdetail").text(detail);
    $("background").css("display","block");
}


function closebox() {
    $("background").css("display","nonoe");
}
