package com.tzx.miniapp.rest.model;

import com.tzx.publics.base.BaseEntity;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2018-05-21
 */

public class TsCmk extends BaseEntity implements Serializable {

	private static final long serialVersionUID = 1L;

	private int id;
	private String dlid;
	private String dlbh;
	private int xlid;
	private String xlbh;
	private int cmid;
	private String cmbh;
	private String cmmc1;
	private String cmmc2;
	private String dwbh;
	private String pydm;
	private String wbdm;
	private double cmdj;
	private double cbje;
	private double mll;
	private String sfsj;
	private String sftc;
	private String tcfs;
	private double tcbl;
	private String tcsfdz;
	private String sfhx;
	private String sffzsl;
	private String sfxgmc;
	private String sfxgsl;
	private String sfxgdj;
	private String cmtm;
	private String lrczry;
	private Date lrrq;
	private String xgczry;
	private Date xgrq;
	private String shczry;
	private Date shrq;
	private String sfzk;
	private String sfzdxf;
	private String sffwf;
	private String sfzx;
	private int dydlid;
	private String dydlbh;
	private String cmsx;
	private double cmrl;
	private String sfkgh;
	private String spbh;
	private String sfxtcp;
	private String sfygc;
	private String sfzs;
	private String cmtp;
	private String cwxlbh;
	private String tcsx;
	private String wyjkxsbh;
	private String sfzycp;
	private String kvssfxs;
	private String sfxsmx;
	private String srsx;
	private String yl1;
	private String yl2;
	private String yl3;
	private double yl4;
	private double yl5;
	private String chbh;

    private String foodboxset;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getDlid() {
		return dlid;
	}

	public void setDlid(String dlid) {
		this.dlid = dlid;
	}

	public String getDlbh() {
		return dlbh;
	}

	public void setDlbh(String dlbh) {
		this.dlbh = dlbh;
	}

	public int getXlid() {
		return xlid;
	}

	public void setXlid(int xlid) {
		this.xlid = xlid;
	}

	public String getXlbh() {
		return xlbh;
	}

	public void setXlbh(String xlbh) {
		this.xlbh = xlbh;
	}

	public int getCmid() {
		return cmid;
	}

	public void setCmid(int cmid) {
		this.cmid = cmid;
	}

	public String getCmbh() {
		return cmbh;
	}

	public void setCmbh(String cmbh) {
		this.cmbh = cmbh;
	}

	public String getCmmc1() {
		return cmmc1;
	}

	public void setCmmc1(String cmmc1) {
		this.cmmc1 = cmmc1;
	}

	public String getCmmc2() {
		return cmmc2;
	}

	public void setCmmc2(String cmmc2) {
		this.cmmc2 = cmmc2;
	}

	public String getDwbh() {
		return dwbh;
	}

	public void setDwbh(String dwbh) {
		this.dwbh = dwbh;
	}

	public String getPydm() {
		return pydm;
	}

	public void setPydm(String pydm) {
		this.pydm = pydm;
	}

	public String getWbdm() {
		return wbdm;
	}

	public void setWbdm(String wbdm) {
		this.wbdm = wbdm;
	}

	public double getCmdj() {
		return cmdj;
	}

	public void setCmdj(double cmdj) {
		this.cmdj = cmdj;
	}

	public double getCbje() {
		return cbje;
	}

	public void setCbje(double cbje) {
		this.cbje = cbje;
	}

	public double getMll() {
		return mll;
	}

	public void setMll(double mll) {
		this.mll = mll;
	}

	public String getSfsj() {
		return sfsj;
	}

	public void setSfsj(String sfsj) {
		this.sfsj = sfsj;
	}

	public String getSftc() {
		return sftc;
	}

	public void setSftc(String sftc) {
		this.sftc = sftc;
	}

	public String getTcfs() {
		return tcfs;
	}

	public void setTcfs(String tcfs) {
		this.tcfs = tcfs;
	}

	public double getTcbl() {
		return tcbl;
	}

	public void setTcbl(double tcbl) {
		this.tcbl = tcbl;
	}

	public String getTcsfdz() {
		return tcsfdz;
	}

	public void setTcsfdz(String tcsfdz) {
		this.tcsfdz = tcsfdz;
	}

	public String getSfhx() {
		return sfhx;
	}

	public void setSfhx(String sfhx) {
		this.sfhx = sfhx;
	}

	public String getSffzsl() {
		return sffzsl;
	}

	public void setSffzsl(String sffzsl) {
		this.sffzsl = sffzsl;
	}

	public String getSfxgmc() {
		return sfxgmc;
	}

	public void setSfxgmc(String sfxgmc) {
		this.sfxgmc = sfxgmc;
	}

	public String getSfxgsl() {
		return sfxgsl;
	}

	public void setSfxgsl(String sfxgsl) {
		this.sfxgsl = sfxgsl;
	}

	public String getSfxgdj() {
		return sfxgdj;
	}

	public void setSfxgdj(String sfxgdj) {
		this.sfxgdj = sfxgdj;
	}

	public String getCmtm() {
		return cmtm;
	}

	public void setCmtm(String cmtm) {
		this.cmtm = cmtm;
	}

	public String getLrczry() {
		return lrczry;
	}

	public void setLrczry(String lrczry) {
		this.lrczry = lrczry;
	}

	public Date getLrrq() {
		return lrrq;
	}

	public void setLrrq(Date lrrq) {
		this.lrrq = lrrq;
	}

	public String getXgczry() {
		return xgczry;
	}

	public void setXgczry(String xgczry) {
		this.xgczry = xgczry;
	}

	public Date getXgrq() {
		return xgrq;
	}

	public void setXgrq(Date xgrq) {
		this.xgrq = xgrq;
	}

	public String getShczry() {
		return shczry;
	}

	public void setShczry(String shczry) {
		this.shczry = shczry;
	}

	public Date getShrq() {
		return shrq;
	}

	public void setShrq(Date shrq) {
		this.shrq = shrq;
	}

	public String getSfzk() {
		return sfzk;
	}

	public void setSfzk(String sfzk) {
		this.sfzk = sfzk;
	}

	public String getSfzdxf() {
		return sfzdxf;
	}

	public void setSfzdxf(String sfzdxf) {
		this.sfzdxf = sfzdxf;
	}

	public String getSffwf() {
		return sffwf;
	}

	public void setSffwf(String sffwf) {
		this.sffwf = sffwf;
	}

	public String getSfzx() {
		return sfzx;
	}

	public void setSfzx(String sfzx) {
		this.sfzx = sfzx;
	}

	public int getDydlid() {
		return dydlid;
	}

	public void setDydlid(int dydlid) {
		this.dydlid = dydlid;
	}

	public String getDydlbh() {
		return dydlbh;
	}

	public void setDydlbh(String dydlbh) {
		this.dydlbh = dydlbh;
	}

	public String getCmsx() {
		return cmsx;
	}

	public void setCmsx(String cmsx) {
		this.cmsx = cmsx;
	}

	public double getCmrl() {
		return cmrl;
	}

	public void setCmrl(double cmrl) {
		this.cmrl = cmrl;
	}

	public String getSfkgh() {
		return sfkgh;
	}

	public void setSfkgh(String sfkgh) {
		this.sfkgh = sfkgh;
	}

	public String getSpbh() {
		return spbh;
	}

	public void setSpbh(String spbh) {
		this.spbh = spbh;
	}

	public String getSfxtcp() {
		return sfxtcp;
	}

	public void setSfxtcp(String sfxtcp) {
		this.sfxtcp = sfxtcp;
	}

	public String getSfygc() {
		return sfygc;
	}

	public void setSfygc(String sfygc) {
		this.sfygc = sfygc;
	}

	public String getSfzs() {
		return sfzs;
	}

	public void setSfzs(String sfzs) {
		this.sfzs = sfzs;
	}

	public String getCmtp() {
		return cmtp;
	}

	public void setCmtp(String cmtp) {
		this.cmtp = cmtp;
	}

	public String getCwxlbh() {
		return cwxlbh;
	}

	public void setCwxlbh(String cwxlbh) {
		this.cwxlbh = cwxlbh;
	}

	public String getTcsx() {
		return tcsx;
	}

	public void setTcsx(String tcsx) {
		this.tcsx = tcsx;
	}

	public String getWyjkxsbh() {
		return wyjkxsbh;
	}

	public void setWyjkxsbh(String wyjkxsbh) {
		this.wyjkxsbh = wyjkxsbh;
	}

	public String getSfzycp() {
		return sfzycp;
	}

	public void setSfzycp(String sfzycp) {
		this.sfzycp = sfzycp;
	}

	public String getKvssfxs() {
		return kvssfxs;
	}

	public void setKvssfxs(String kvssfxs) {
		this.kvssfxs = kvssfxs;
	}

	public String getSfxsmx() {
		return sfxsmx;
	}

	public void setSfxsmx(String sfxsmx) {
		this.sfxsmx = sfxsmx;
	}

	public String getSrsx() {
		return srsx;
	}

	public void setSrsx(String srsx) {
		this.srsx = srsx;
	}

	public String getYl1() {
		return yl1;
	}

	public void setYl1(String yl1) {
		this.yl1 = yl1;
	}

	public String getYl2() {
		return yl2;
	}

	public void setYl2(String yl2) {
		this.yl2 = yl2;
	}

	public String getYl3() {
		return yl3;
	}

	public void setYl3(String yl3) {
		this.yl3 = yl3;
	}

	public double getYl4() {
		return yl4;
	}

	public void setYl4(double yl4) {
		this.yl4 = yl4;
	}

	public double getYl5() {
		return yl5;
	}

	public void setYl5(double yl5) {
		this.yl5 = yl5;
	}

	public String getChbh() {
		return chbh;
	}

	public void setChbh(String chbh) {
		this.chbh = chbh;
	}

    public String getFoodboxset() {
        return foodboxset;
    }

    public void setFoodboxset(String foodboxset) {
        this.foodboxset = foodboxset;
    }
}
