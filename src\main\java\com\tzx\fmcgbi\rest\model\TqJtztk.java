package com.tzx.fmcgbi.rest.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @since 2020-01-09
 */
@Table(name = "TQ_JTZTK")
public class TqJtztk implements Serializable {

	private static final long serialVersionUID = 1L;
	@Id
	@Column(name = "ID", insertable = false)
	private int id;
	@Column(name = "JHID")
	private String jhid;
	@Column(name = "CZNR")
	private String cznr;// YYDL营业登录，YYTC营业退出
	@Column(name = "RYBH")
	private String rybh;
	@Column(name = "RYXM")
	private String ryxm;
	@Column(name = "SQCZRYBH")
	private String sqczrybh;
	@Column(name = "CZSJ")
	private Date czsj;// 操作时间
	@Column(name = "BBRQ")
	private Date bbrq;// 报表日期
	@Column(name = "CLBZ")
	private String clbz;
	@Column(name = "YGDLCS")
	private String ygdlcs;
	@Column(name = "MEMO")
	private String memo;
	@Column(name = "YL1")
	private String yl1;
	@Column(name = "YL2")
	private String yl2;
	@Column(name = "YL3")
	private String yl3;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getJhid() {
		return jhid;
	}

	public void setJhid(String jhid) {
		this.jhid = jhid;
	}

	public String getCznr() {
		return cznr;
	}

	public void setCznr(String cznr) {
		this.cznr = cznr;
	}

	public String getRybh() {
		return rybh;
	}

	public void setRybh(String rybh) {
		this.rybh = rybh;
	}

	public String getRyxm() {
		return ryxm;
	}

	public void setRyxm(String ryxm) {
		this.ryxm = ryxm;
	}

	public String getSqczrybh() {
		return sqczrybh;
	}

	public void setSqczrybh(String sqczrybh) {
		this.sqczrybh = sqczrybh;
	}

	public Date getCzsj() {
		return czsj;
	}

	public void setCzsj(Date czsj) {
		this.czsj = czsj;
	}

	public Date getBbrq() {
		return bbrq;
	}

	public void setBbrq(Date bbrq) {
		this.bbrq = bbrq;
	}

	public String getClbz() {
		return clbz;
	}

	public void setClbz(String clbz) {
		this.clbz = clbz;
	}

	public String getYgdlcs() {
		return ygdlcs;
	}

	public void setYgdlcs(String ygdlcs) {
		this.ygdlcs = ygdlcs;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public String getYl1() {
		return yl1;
	}

	public void setYl1(String yl1) {
		this.yl1 = yl1;
	}

	public String getYl2() {
		return yl2;
	}

	public void setYl2(String yl2) {
		this.yl2 = yl2;
	}

	public String getYl3() {
		return yl3;
	}

	public void setYl3(String yl3) {
		this.yl3 = yl3;
	}

}
