package com.tzx.publics.service;

import java.util.Map;

import com.tzx.publics.common.BillNoData;
import com.tzx.publics.vo.DeviceVo;
import com.tzx.publics.vo.OrganVo;
import com.tzx.receiver.entity.msg.EcoTypeDic;

import net.sf.json.JSONObject;

public interface IInitDataService {

	public Map<String, String> getGgcsMap();
	
	public Map<String, String> getThirdMap();
	
	public OrganVo getOrganVo();

	public void refreshInternalStorage();

	public BillNoData getBillNoData(String jtbh, String preChar) throws Exception;

	public Map<String,EcoTypeDic> getEcoTypeDicMap();

	public void initMq();
	
	public Map<String, DeviceVo> getDeviceMap();
	
	public JSONObject getWuYeObj();
	
}
