<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.commapi.rest.mapper.CommApiMapper">

	<update id="updateSetGq" parameterType="com.tzx.commapi.rest.vo.ReqParam">
		update ts_gqk set gqsl = #{reqParam.count}, yl1 = #{reqParam.type}
		where cmbh = #{reqParam.item_id}
	</update>
	<insert id="insertSetGq" parameterType="com.tzx.commapi.rest.vo.ReqParam">
		insert into ts_gqk(cmid,gqsl,cmbh,cmmc1,yl1) VALUES (#{reqParam.cmid}
		,#{reqParam.count},#{reqParam.item_id},#{reqParam.item_name},#{reqParam.type})
	</insert>
	<delete id="deleteSetGq" parameterType="com.tzx.commapi.rest.vo.ReqParam">
		delete from ts_gqk
		where cmbh = #{reqParam.item_id}
	</delete>

	<select id="getGQDish" resultType="com.tzx.commapi.rest.vo.GqkVo" parameterType="com.tzx.commapi.rest.vo.ReqParam">
		select cmid,id,gqsl,cmbh,cmmc1,yl1 from TS_GQK where cmbh = #{reqParam.item_id}
	</select>
	<select id="getGQDishs" resultType="com.tzx.commapi.rest.vo.GqkVo" parameterType="java.util.List">
		select cmid,id,gqsl,cmbh,cmmc1,yl1 from TS_GQK where cmbh in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>


	<update id="addGqs" parameterType="java.util.List">
		<foreach collection="list" item="item" index="index" open="" close="" separator=";">
			update ts_gqk
			<set >
				gqsl = gqsl + #{item.gqsl}
			</set>
			where cmbh = #{item.cmbh}
		</foreach>
	</update>

	<update id="decGqs" parameterType="java.util.List">
		<foreach collection="list" item="item" index="index" open="" close="" separator=";">
			update ts_gqk
			<set >
				gqsl = gqsl - #{item.gqsl}
			</set>
			where cmbh = #{item.cmbh}
		</foreach>
	</update>


	<update id="addGq" parameterType="com.tzx.commapi.rest.vo.GqkVo">
		update ts_gqk set gqsl = gqsl + #{reqParam.count}
		where cmbh = #{reqParam.item_id}
	</update>
	<update id="decGq" parameterType="com.tzx.commapi.rest.vo.GqkVo">
		update ts_gqk set gqsl = gqsl - #{reqParam.count}
		where cmbh = #{reqParam.item_id}
	</update>
	<update id="updateFklsByJzid">
		update tq_fklslsk set fkje=fkje-#{yhje},bbje=bbje-#{yhje} where kdzdbh=#{kdzdbh} and jzid=#{jzid}
	</update>
	<update id="updateFklsByFklsid">
		update tq_fklslsk set fkje=0,bbje=0 where kdzdbh=#{kdzdbh} and id=#{fklsid}
	</update>
	<delete id="deleteYhfsParam" parameterType="com.tzx.commapi.rest.vo.YhfsParam">
		delete from tq_yhfsparams where zdbh = #{yhfsParam.zdbh}
	</delete>
	<insert id="saveYhfsParam" parameterType="com.tzx.commapi.rest.vo.YhfsParam">
		insert into tq_yhfsparams(sguid,bbrq,opertype,zdbh,yhsx,yhfsid,sparam1,
		sparam2,sparam3,sparam4,sparam5,iparam1,iparam2,iparam3,iparam4,nparam1,
		nparam2,nparam3,nparam4,nparam5) values(#{yhfsParam.sguid},#{yhfsParam.bbrq},#{yhfsParam.optype},#{yhfsParam.zdbh},#{yhfsParam.yhsx},#{yhfsParam.yhfsid},#{yhfsParam.sparam1}
		,#{yhfsParam.sparam2},'','',#{yhfsParam.sparam5},0,0,0,0,#{yhfsParam.nparam1},0,0,0,0)
	</insert>
	<delete id="DeleteYhMTcouponsTempItems" parameterType="com.tzx.commapi.rest.vo.YHMTCouponsTemp">
		delete from tq_yhmtcouponstemp where kdzdbh = #{yhmtCouponsTemp.kdzdbh}
	</delete>
	<insert id="SaveYhMTcouponsTempItems"  parameterType="com.tzx.commapi.rest.vo.YHMTCouponsTemp">
		insert into  tq_yhmtcouponstemp(yhfsid, clmxid, kdzdbh, payableamt,pqlx,yzm,
		jzid,optype,sjje,cmid,buyPrice,wdrwid)
		values(#{yhmtCouponsTemp.yhfsid},#{yhmtCouponsTemp.clmxid},#{yhmtCouponsTemp.kdzdbh},#{yhmtCouponsTemp.payableamt},#{yhmtCouponsTemp.pqlx},#{yhmtCouponsTemp.yzm}
		,#{yhmtCouponsTemp.jzid},#{yhmtCouponsTemp.optype},#{yhmtCouponsTemp.sjje},#{yhmtCouponsTemp.cmid},#{yhmtCouponsTemp.buyprice},#{yhmtCouponsTemp.wdrwid}
		)
	</insert>
	<select id="AddThirdYhfs" resultType="java.lang.Integer" >
		select * from P_ADDYHFS(#{itemid}, #{yhfsid}, #{kdzdbh},#{skjh},'1',0,#{code},#{jgtxbh});
	</select>

	<select id="UpdateDsljByZdbh" resultType="java.lang.Integer" >
		update tq_zdk set dslj = 0 where kdzdbh = #{kdzdbh};
	</select>
	<select id="ExecBillDiscountShare" resultType="java.lang.Integer">
		select * from P_ZRTR(#{kdzdbh});
	</select>
	<select id="CalcMoney" resultType="com.tzx.commapi.rest.vo.CalcMoneyRt">
		select * from p_calcmoney(#{kdzdbh})
	</select>

	<!--<resultMap id="ListTqThirdTempOrder" type="com.tzx.commapi.rest.vo.TqThirdTempOrderrder">-->
		<!--<id column="id" property="id" jdbcType="BIGINT"/>-->
		<!--<result column="billnum" property="billnum" jdbcType="VARCHAR"/>-->
		<!--<result column="reportdate" property="reportdate" jdbcType="DATE"/>-->
		<!--<result column="orderno" property="orderno" jdbcType="VARCHAR"/>-->
		<!--<result column="datatype" property="datatype" jdbcType="VARCHAR"/>-->
		<!--<result column="errcount" property="errcount" jdbcType="INTEGER"/>-->
		<!--<result column="status" property="status" jdbcType="INTEGER"/>-->
		<!--<result column="runinterval" property="runinterval" jdbcType="INTEGER"/>-->
	<!--</resultMap>-->

	<!--id,billnum,reportdate,orderno,datatype,errcount,status,runinterval-->


<!--	<select id="getTqThirdTempOrder" resultType="com.tzx.commapi.rest.vo.TqThirdTempOrder" >-->
<!--		select * from TQ_THIRD_TEMPORDER where datatype = #{dataType} and status = 0-->
<!--		and to_date(to_char(createtime,'yyyy-MM-dd'),'YYYY-MM-DD HH24:MI:SS') >= to_date(to_char((now()- interval '3 Days'),'yyyy-MM-dd'),'yyyy-MM-dd')-->
<!--		and createtime &lt;= now()- interval '10 Seconds'-->
<!--	</select>-->

	<select id="getTqThirdTempOrder" resultType="com.tzx.commapi.rest.vo.TqThirdTempOrder" >
		select * from TQ_THIRD_TEMPORDER where datatype = #{dataType} and status = 0
		and createtime &lt;= now()- interval '10 Seconds'
	</select>


	<select id="getTqThirdTempOrderByBillNum" resultType="com.tzx.commapi.rest.vo.TqThirdTempOrder" >
		select * from TQ_THIRD_TEMPORDER where datatype = #{dataType} and billnum = #{billNum}
		and to_date(to_char(createtime,'yyyy-MM-dd'),'YYYY-MM-DD HH24:MI:SS') >= to_date(to_char((now()- interval '3 Days'),'yyyy-MM-dd'),'yyyy-MM-dd')

	</select>

	<update id="UpdateTqThirdTempOrder" parameterType="com.tzx.commapi.rest.vo.TqThirdTempOrder">
		update TQ_THIRD_TEMPORDER set errcount = #{param.errcount}, status = #{param.status},
		runinterval = #{param.runinterval}, remark = #{param.remark}
		where id = #{param.id}
	</update>
	<insert id="AddTqThirdTempOrder" parameterType="com.tzx.commapi.rest.vo.TqThirdTempOrder">
		insert into TQ_THIRD_TEMPORDER(billnum,reportdate,orderno,datatype,createtime,updatetime,remark,jzsj) VALUES (#{param.billnum}
		,#{param.reportdate},#{param.orderno},#{param.datatype},now(),now(),#{param.remark},#{param.jzsj})
	</insert>

	<select id="getVqJpwyZdk" resultType="com.tzx.commapi.rest.vo.Vq_JPWY_Zdk" >
		select jzsj,jzbbrq,kdzdbh,zdbz,zdje,fkje,jzczry,* from vq_zdk where kdzdbh = #{billNum} and jzbbrq = #{reportDate}
	</select>
	
	<update id="updateJtztkToToken">
		update ts_jtsdk set token = #{token}, updateTime = #{updateTime} where jtbh = #{jtbh}
	</update>

	<select id="getFXZdmxList" resultType="java.util.Map" >
		select cmbh as item_no,cmsl as sale_qnty,cmdj as sale_price,cmje as sale_money from vq_zdmxk where kdzdbh = #{billNum} and jzbbrq = #{reportDate}
		and (COALESCE(wdbz,'') = '' or wdbz = 'WDBZ_CJ') and (cmsx ='CMSX_DP' or cmsx='CMSX_TC')
	</select>
	
	<select id="getVqZdk" resultType="com.tzx.mobilepos.rest.model.TqZdk" >
		select * from vq_zdk where kdzdbh = #{kdzdbh} and jzbbrq = #{bbrq} limit 1
	</select>
	
	<select id="getKCZdmxList" resultType="java.util.Map" >
<!-- 		select mx.dcxh as salesLineNumber, mx.fwyh as salesman, mx.cmbh as itemCode,  gg.sdnr as itemOrgId,  -->
<!-- 		'*' as itemLotNum, '' as serialNumber, 0 as inventoryType, mx.cmsl as qty, 0 as itemDiscountLess,  -->
<!-- 		0 as totalDiscountLess, mx.sjje as netAmount, mx.kwbz as salesItemRemark, '' as extendParameter  -->
<!-- 		from vq_zdmxk mx left join ts_ggcsk gg on gg.sdbt = 'FDJGBH' where mx.kdzdbh = #{billNum} and jzbbrq = #{reportDate}  -->
<!-- 		and (coalesce(mx.wdbz,'') = '' or mx.wdbz = 'WDBZ_CJ') and (mx.cmsx ='CMSX_DP' or mx.cmsx='CMSX_TC') order by mx.dcxh -->
		select 1 as salesLineNumber, mx.fwyh as salesman, #{itemCode} as itemCode,  #{itemOrgid} as itemOrgId, 
		'*' as itemLotNum, '' as serialNumber, 0 as inventoryType, sum(mx.cmsl) as qty, 0 as itemDiscountLess, 
		0 as totalDiscountLess, sum(mx.sjje) as netAmount, '' as salesItemRemark, '' as extendParameter 
		from vq_zdmxk mx left join ts_ggcsk gg on gg.sdbt = 'FDJGBH' where mx.kdzdbh = #{billNum} and jzbbrq = #{reportDate} 
<!-- 		and (coalesce(mx.wdbz,'') = '' or mx.wdbz = 'WDBZ_CJ')  -->
		and (mx.cmsx ='CMSX_DP' or mx.cmsx='CMSX_TC') 
		group by mx.fwyh, gg.sdnr 
	</select>
	
	<select id="getKCFklsList" resultType="java.util.Map">
		select ls.kdzdbh, ls.jzzdbh, ls.fklxsx, sd.yl3, ls.jzid, max(ls.fkfsmc1), sum(ls.fkje) as fkje,
				ls.skyh
		from vq_fklsk ls left join ts_fkfssdk sd on ls.jzid = sd.id 
		where kdzdbh = #{billNum} group by ls.jzid, ls.kdzdbh, ls.jzzdbh, ls.fklxsx, sd.yl3,ls.skyh
	</select>
	
	<select id="getFXFklsList" resultType="java.util.Map">
		select ls.kdzdbh, ls.jzzdbh, ls.fklxsx, sd.yl3, ls.jzid, sd.fkfsmc2, max(ls.fkfsmc1), sum(ls.fkje) as fkje, to_char(ls.czsj,'YYYY-MM-DD hh24:mi:ss') as czsj, sd.memo
		from vq_fklsk ls left join ts_fkfssdk sd on ls.jzid = sd.id 
		where kdzdbh = #{billNum} and ls.fkje &lt;&gt; 0
		group by ls.jzid, ls.kdzdbh, ls.jzzdbh, ls.fklxsx, sd.yl3, sd.fkfsmc2, ls.czsj, sd.memo
	</select>
	
	<select id="getTqThirdTempOrderByInType" parameterType="java.util.List" resultType="com.tzx.commapi.rest.vo.TqThirdTempOrder" >
		select * from tq_third_temporder where datatype in 
		<foreach item="datatype" index="index" collection="dataTypes" open="(" separator="," close=")">
			#{datatype}
		</foreach>
		and status = 0 and to_date(to_char(createtime,'yyyy-MM-dd'),'YYYY-MM-DD HH24:MI:SS') >= to_date(to_char((now()- interval '3 Days'),'yyyy-MM-dd'),'yyyy-MM-dd') 
		and createtime &lt;= now()- interval '10 Seconds' order by createtime
	</select>
	
	<select id="getJLZdmxList" resultType="java.util.Map" >
		select cmmc1 as name, cmbh as cmbh, cmdj as price, abs(cmsl) as salescount, abs(sjje) as salesprice, dwbh as salesunit,
		       sjje, yhje
		from vq_zdmxk where kdzdbh = #{billNum} and jzbbrq = #{reportDate}
		and (COALESCE(wdbz,'') = '' or wdbz = 'WDBZ_CJ' or wdbz = 'WDBZ_QX' or wdbz = 'WDBZ_FS' ) and COALESCE (memo, '') &lt;&gt; 'QX' and (cmsx ='CMSX_DP' or cmsx='CMSX_TC')
	</select>
	
	<select id="getWy2VqZdkList" resultType="com.tzx.mobilepos.rest.model.TqZdk" >
		select * from vq_zdk where kdbbrq = #{bbrq}  and kdzdbh not in (select kdzdbh from tq_bzdqzdk where bbrq = #{bbrq})
</select>
	
	<update id="updateZdkToWyscbj">
		update tq_zdlsk set wyscbj = #{wyscbj} where kdzdbh = #{kdzdbh}
	</update>
	
	<select id="getCYZdmxList" resultType="java.util.Map" >
		select cmje,cmmc1, cmbh, cmdj, cmsl, sjje, cmdj, dcxh,cmsx,dwbh, to_char(tmbj,'YYYY-MM-DD hh24:mi:ss') as tmbj from vq_zdmxk where kdzdbh = #{billNum} and jzbbrq = #{reportDate}
		and (COALESCE(wdbz,'') = '' or wdbz = 'WDBZ_CJ' or wdbz = 'WDBZ_QX' or wdbz = 'WDBZ_FS' ) and COALESCE (memo, '') &lt;&gt; 'QX' and (cmsx ='CMSX_DP' or cmsx='CMSX_TC')
	</select>

	<select id="getOrderSummarizing" resultType="com.tzx.commapi.rest.vo.DayEndSummarizing" >
		select a.*,b.*,c.* from
			(select string_agg(case when cznr = 'KSSY' then to_char(czsj, 'YYYY/MM/DD HH24:MI:SS') end, '') as endaccountstartdate,
				string_agg(case when cznr = 'JSSY' then to_char(czsj, 'YYYY/MM/DD HH24:MI:SS') end, '') as endaccountenddate , to_char(MAX(bbrq), 'YYYYMMDD') as endaccountuniquecode
				from tq_jtztk where (cznr = 'KSSY' OR cznr = 'JSSY') AND bbrq = #{bbrq} AND JHID = '99') a,
			(select sum(xfks) as ticketcount, count(kdzdbh) as totalcount, sum(fkje) as totalsellamount, sum(yhje) as totaloffamount
				from tq_zdlsk where kdbbrq = #{bbrq}  and kdzdbh not in (select kdzdbh from tq_bzdqzdk where bbrq = #{bbrq})) b,
			(select string_agg(case when bb.fklxsx = 'FKSX_XJ' then bb.fkje||'' end, '0') as cashpay, '0' as creditcardpay,
				string_agg(case when bb.fklxsx = 'ERP_FKFS_WX' then bb.fkje||'' end, '0') as wechatpay, string_agg(case when bb.fklxsx = 'ERP_FKFS_ZFB' then bb.fkje||'' end, '0') as alipay,
				'0' as vipcardpay, '0' as costbillpay, string_agg(case when bb.fklxsx = 'OTHER' then bb.fkje||'' end, '0') as otherPpay
				from (select aa.fklxsx, sum(aa.fkje) as fkje from (select case when sd.fklxsx = 'FKSX_XJ' then sd.fklxsx when sd.yl3 = 'ERP_FKFS_WX' or sd.yl3 = 'ERP_FKFS_ZFB' then sd.yl3 else 'OTHER' end as fklxsx, ls.fkje
				from tq_fklsk ls left join ts_fkfssdk sd on ls.jzid = sd.id where ls.jzbbrq = #{bbrq}) aa group by aa.fklxsx) bb) c
	</select>

	<select id="getTqXfMsgLog" resultType="java.util.Map" >
		select id, to_char(createtime,'YYYY-MM-DD hh24:mi:ss') as createtime, updatetime, dbname, zlbh, jgxh, versionstime, count, status from tq_xfmsglog where createtime &lt;= now()- interval ${pdmm} and status = #{status} limit 1
	</select>

	<update id="updateTqXfMsgLog">
		update tq_xfmsglog set status = #{status} where versionstime = #{versionstime}
	</update>

	<select id="getWyList" resultType="com.tzx.commapi.rest.vo.TqThirdTempOrderNew">
		select a.* from (
		select id, billnum, orderno, reportdate, datatype, status, errcount, runinterval, createtime,  updatetime, '1' as type from tq_third_temporder where datatype = 'WUYE3'
		<if test="billnum != ''">
			and billnum like '%${billnum}%'
		</if>
		<if test="reportdate != ''">
			and reportdate = to_date(#{reportdate},'YYYY-MM-DD HH24:MI:SS')
		</if>
		<if test="orderno != ''">
			and orderno like '%${orderno}%'
		</if>
		<if test="status != -1">
			and status = #{status}
		</if>
		union all
		select id, billnum, orderno, reportdate, datatype, status, errcount, runinterval, createtime,  updatetime, '2' as type from tq_third_temporder_his where datatype = 'WUYE3'
		<if test="billnum != ''">
			and billnum like '%${billnum}%'
		</if>
		<if test="reportdate != ''">
			and reportdate = to_date(#{reportdate},'YYYY-MM-DD HH24:MI:SS')
		</if>
		<if test="orderno != ''">
			and orderno like '%${orderno}%'
		</if>
		<if test="status != -1">
			and status = #{status}
		</if>
		) a order by a.id desc
	</select>

	<update id="updateTqThirdTempOrderStatus">
		update TQ_THIRD_TEMPORDER set errcount = 0, status = 0, runinterval = 0
		where id = #{id,jdbcType=INTEGER} and billnum = #{billnum}
	</update>

	<insert id="repetitionTTTO">
		insert  into TQ_THIRD_TEMPORDER(id,billnum,reportdate,orderno,status,errcount,runinterval,datatype,createtime,updatetime)
			select id,billnum,reportdate,orderno,0 as status,0 as errcount,0 as runinterval,datatype,createtime,updatetime from  tq_third_temporder_his
			where id = #{id,jdbcType=INTEGER}
			and billnum = #{billnum}
			and reportdate = to_date(#{reportdate},'YYYY-MM-DD HH24:MI:SS')
			and orderno = #{orderno}
			and datatype = 'WUYE3'
	</insert>

	<delete id="delTTTO">
		delete from tq_third_temporder_his
		where id = #{id,jdbcType=INTEGER}
		and billnum = #{billnum}
		and reportdate = to_date(#{reportdate},'YYYY-MM-DD HH24:MI:SS')
		and orderno = #{orderno}
		and datatype = 'WUYE3'
	</delete>

	<update id="updateTqThirdTempOrderStatusByBbrq">
		update TQ_THIRD_TEMPORDER set errcount = 0, status = 0, runinterval = 0
		where datatype = 'WUYE3' and reportdate = to_date(#{reportdate},'YYYY-MM-DD HH24:MI:SS')
		<if test="status != -1">
			and status = #{status}
		</if>
		<if test="billnum != ''">
			and billnum like '%${billnum}%'
		</if>
		<if test="orderno != ''">
			and orderno like '%${orderno}%'
		</if>
	</update>

	<insert id="repetitionTTTOByBbrq">
		insert  into TQ_THIRD_TEMPORDER(id,billnum,reportdate,orderno,status,errcount,runinterval,datatype,createtime,updatetime)
			select id,billnum,reportdate,
			orderno,0 as status,0 as errcount,0 as runinterval,datatype,createtime,updatetime from  tq_third_temporder_his
			where datatype = 'WUYE3' and reportdate = to_date(#{reportdate},'YYYY-MM-DD HH24:MI:SS')
			<if test="status != -1">
				and status = #{status}
			</if>
			<if test="billnum != ''">
				and billnum like '%${billnum}%'
			</if>
			<if test="orderno != ''">
				and orderno like '%${orderno}%'
			</if>
	</insert>

	<delete id="delTTTOByBbrq">
		delete from tq_third_temporder_his where datatype = 'WUYE3' and reportdate = to_date(#{reportdate},'YYYY-MM-DD HH24:MI:SS')
		<if test="status != -1">
			and status = #{status}
		</if>
		<if test="billnum != ''">
			and billnum like '%${billnum}%'
		</if>
		<if test="orderno != ''">
			and orderno like '%${orderno}%'
		</if>
	</delete>

    <update id="updateLockCtrlGgcsN">
        update ts_ggcsk set sdnr = '1q#2w#3e#4r#' || sdnr where sdbt in ('MQUrl', 'RIFSERVER', 'TZXMQWEBADD', 'UPDATESERVER', 'TZXIMGWEBADD', 'POS_REALTIME')
    </update>

    <update id="updateLockCtrlGgcsXfN">
        update ts_ggcsk_xf set sdnr = '1q#2w#3e#4r#' || sdnr where sdbt in ('MQUrl', 'RIFSERVER', 'TZXMQWEBADD', 'UPDATESERVER', 'TZXIMGWEBADD', 'POS_REALTIME')
    </update>

	<update id="updateLockCtrlGgcsY">
		update ts_ggcsk set sdnr = replace(sdnr,'1q#2w#3e#4r#','') where sdbt in ('MQUrl', 'RIFSERVER', 'TZXMQWEBADD', 'UPDATESERVER', 'TZXIMGWEBADD', 'POS_REALTIME');
    </update>

	<update id="updateLockCtrlGgcsXfY">
        update ts_ggcsk_xf set sdnr = replace(sdnr,'1q#2w#3e#4r#','') where sdbt in ('MQUrl', 'RIFSERVER', 'TZXMQWEBADD', 'UPDATESERVER', 'TZXIMGWEBADD', 'POS_REALTIME');
    </update>

	<select id="getFklsBySfsrList" resultType="java.util.Map">
		select ls.kdzdbh, ls.jzzdbh, ls.fklxsx, sd.yl3, ls.jzid, sd.fkfsmc2, max(ls.fkfsmc1), sum(ls.fkje) as fkje, to_char(ls.czsj,'YYYY-MM-DD hh24:mi:ss') as czsj, max(ls.fkfsmc1) as fkmc
		from vq_fklsk ls left join ts_fkfssdk sd on ls.jzid = sd.id
		where kdzdbh = #{billNum} and ls.fkje &lt;&gt; 0 and sd.sfsr  &lt;&gt; 'N' group by ls.jzid, ls.kdzdbh, ls.jzzdbh, ls.fklxsx, sd.yl3, sd.fkfsmc2, ls.czsj
	</select>

	<select id="getVqJpwyByBbrq" resultType="com.tzx.commapi.rest.vo.Vq_JPWY_Zdk" >
		select jzsj,jzbbrq,kdzdbh,zdbz,zdje,fkje,jzczry,* from vq_zdk where jzbbrq = #{reportDate}
	</select>

	<insert id="insertZdkInDown" parameterType="java.util.List">
		insert into tq_zdk (
		                    jzzdbh,kdzdbh,lsdh,kdbbrq,jzbbrq,
		                    cbid,zwbh,xfks,ktsj,jzsj,
		                    jzcs,ktskjh,jzskjh,fwyh,ktczry,
		                    jzczry,ktbcid,jzbcid,yddh,yjje,
		                    xmxjje,zdje,fkje,fkce,zkje,
		                    zrje,mlje,dpzkje,yhje,fsje,
		                    dslj,mdje,yhczry,zkl,mdyybh,
		                    zdbz,xsms,cwlxbh,zdzt,jzsx,
		                    ksdcsj,ksjzsj,jzjssj,bcqrsj,source,
		                    psfje,yhyhje,ptyhje,pyyjje,yhfkje,
		                    yongjje,delivery_type,ecototalfee,zpsfje,wmtype,
		                    cardno,phone,membername,sfzblq,scbj,
		                    qch
		                    ) values
		<foreach collection="list" item="info" index="index" separator=",">
			(
			 #{info.jzzdbh},#{info.kdzdbh},#{info.lsdh},#{info.kdbbrq},#{info.jzbbrq},
			 #{info.cbid},#{info.zwbh},#{info.xfks},#{info.ktsj},#{info.jzsj},
			 #{info.jzcs},#{info.ktskjh},#{info.jzskjh},#{info.fwyh},#{info.ktczry},
			 #{info.jzczry},#{info.ktbcid},#{info.jzbcid},#{info.yddh},#{info.yjje},
			 #{info.xmxjje},#{info.zdje},#{info.fkje},#{info.fkce},#{info.zkje},
			 #{info.zrje},#{info.mlje},#{info.dpzkje},#{info.yhje},#{info.fsje},
			 #{info.dslj},#{info.mdje},#{info.yhczry},#{info.zkl},#{info.mdyybh},
			 #{info.zdbz},#{info.xsms},#{info.cwlxbh},#{info.zdzt},#{info.jzsx},
			 #{info.ksdcsj},#{info.ksjzsj},#{info.jzjssj},#{info.bcqrsj},#{info.source},
			 #{info.psfje},#{info.yhyhje},#{info.ptyhje},#{info.pyyjje},#{info.yhfkje},
			 #{info.yongjje},#{info.deliveryType},#{info.ecototalfee},#{info.zpsfje},#{info.wmtype},
			 #{info.cardno},#{info.phone},#{info.membername},1,#{info.scbj},
			 'BOH'
			)
		</foreach>
	</insert>

	<insert id="insertWdkInDown" parameterType="java.util.List">
		insert into tq_wdk (
		                    rwid,kdzdbh,jzzdbh,clmxid,cmid,
		                    cmbh,cmmc1,dwbh,tcfs,tcbl,
		                    tcje,fzsl,fzje,zdsj,fwyh,
		                    cmdj,cmsl,sjje,zkzt,xlzkzt,
		                    zkl,yhfsid,yhfs,cmsx,wdbz,
		                    tmbj,dcxh,fsbbrq,jzbbrq,fsskjh,
		                    jzskjh,kwbz,dcbz,csbh,scbj,
		                    jjcrwid,cmje
		) values
		<foreach collection="list" item="info" index="index" separator=",">
			(
			 #{info.rwid},#{info.kdzdbh},#{info.jzzdbh},#{info.clmxid},#{info.cmid},
			 #{info.cmbh},#{info.cmmc1},#{info.dwbh},#{info.tcfs},#{info.tcbl},
			 #{info.tcje},#{info.fzsl},#{info.fzje},#{info.zdsj},#{info.fwyh},
			 #{info.cmdj},#{info.cmsl},#{info.sjje},#{info.zkzt},#{info.xlzkzt},
			 #{info.zkl},#{info.yhfsid},#{info.yhfs},#{info.cmsx},#{info.wdbz},
			 #{info.tmbj},#{info.dcxh},#{info.fsbbrq},#{info.jzbbrq},#{info.fsskjh},
			 #{info.jzskjh},#{info.kwbz},#{info.dcbz},#{info.csbh},#{info.scbj},
			 #{info.rwid},#{info.cmje}
			)
		</foreach>
	</insert>

	<insert id="insertFklslskInDown" parameterType="java.util.List">
		insert into tq_fklslsk (
		                        id,kdzdbh,jzzdbh,jzid,fkfsmc1,
		                        fkje,fksl,fkhm,sfzhm,lxdh,
		                        fkbz,jzbbrq,jzbcid,skjh,skyh,
		                        czsj,sfysk,sfzl,lv,bbje,
		                        scbj
		) values
		<foreach collection="list" item="info" index="index" separator=",">
			(
			#{info.id},#{info.kdzdbh},#{info.jzzdbh},#{info.jzid},#{info.fkfsmc1},
			#{info.fkje},#{info.fksl},#{info.fkhm},#{info.sfzhm},#{info.lxdh},
			#{info.fkbz},#{info.jzbbrq},#{info.jzbcid},#{info.skjh},#{info.skyh},
			#{info.czsj},#{info.sfysk},#{info.sfzl},#{info.lv},#{info.bbje},
			#{info.scbj}
			)
		</foreach>
	</insert>

	<update id="updateBh">
		update TS_BMKZK set nr = #{nr} where BMC = #{bmc} and ZDMC = #{zdmc}
	</update>

	<select id="nextvals" resultType="java.lang.Integer">
		select nextval(#{seq})
	</select>

	<select id="setvals" resultType="java.lang.Integer">
		select setval(#{seq}, #{value})
	</select>

	<select id="getCzsj" resultType="com.tzx.mobilepos.rest.model.TqJtztk">
		select min(ktsj) as czsj, min(kdbbrq) as bbrq from tq_zdk
	</select>

	<insert id="insertJtztk" parameterType="com.tzx.mobilepos.rest.model.TqJtztk">
		insert into tq_jtztk (id,jhid,cznr,rybh,ryxm,sqczrybh,czsj,bbrq,clbz,ygdlcs,memo,yl1,yl2)
		values (#{id},#{jhid},#{cznr},#{rybh},#{ryxm},#{sqczrybh},#{czsj},#{bbrq},#{clbz},#{ygdlcs},#{memo},#{yl1},#{yl2})
	</insert>

	<insert id="insertCzzdkInDown" parameterType="java.util.List">
		insert into tq_czzdk (
		                      kdzdbh,lsdh,kdbbrq,cbid,xfks,
		                      ktsj,jzsj,jzcs,ktskjh,jzskjh,
		                      fwyh,ktczry,jzczry,ktbcid,jzbcid,
		                      yjje,xmxjje,zdje,fkje,fkce,
		                      mlje,yhje,jzsx,zdzt,scbj,
		                      ksdcsj,ksjzsj,jzjssj,source,ygdlcs,
		                      zdbz,cardno,bizid
		) values
		<foreach collection="list" item="info" index="index" separator=",">
			(
			#{info.kdzdbh},#{info.lsdh},#{info.kdbbrq},#{info.cbid},#{info.xfks},
			#{info.ktsj},#{info.jzsj},#{info.jzcs},#{info.ktskjh},#{info.jzskjh},
			#{info.fwyh},#{info.ktczry},#{info.jzczry},#{info.ktbcid},#{info.jzbcid},
			#{info.yjje},#{info.xmxjje},#{info.zdje},#{info.fkje},#{info.fkce},
			#{info.mlje},#{info.yhje},#{info.jzsx},#{info.zdzt},#{info.scbj},
			#{info.ksdcsj},#{info.ksjzsj},#{info.jzjssj},#{info.source},#{info.ygdlcs},
			#{info.zdbz},#{info.cardno},#{info.bizid}
			)
		</foreach>
	</insert>

	<insert id="insertCzlslskInDown" parameterType="java.util.List">
		insert into tq_czlslsk (
		                        id,kdzdbh,zwbh,fklxsx,jzid,
		                        fkfsmc1,fkfsmc2,fkje,fksl,fkkz,
		                        fkhm,sfzhm,lxdh,fkbz,jzbbrq,
		                        jzbcid,skjh,skyh,czsj,jgxh,
		                        sfysk,sfzl,jthybh,lv,bbje,
		                        scbj
		) values
		<foreach collection="list" item="info" index="index" separator=",">
			(
			#{info.id},#{info.kdzdbh},#{info.zwbh},#{info.fklxsx},#{info.jzid},
			#{info.fkfsmc1},#{info.fkfsmc2},#{info.fkje},#{info.fksl},#{info.fkkz},
			#{info.fkhm},#{info.sfzhm},#{info.lxdh},#{info.fkbz},#{info.jzbbrq},
			#{info.jzbcid},#{info.skjh},#{info.skyh},#{info.czsj},#{info.jgxh},
			#{info.sfysk},#{info.sfzl},#{info.jthybh},#{info.lv},#{info.bbje},
			#{info.scbj}
			)
		</foreach>
	</insert>

	<select id="getBbscjdkByBbrq" resultType="com.tzx.commapi.rest.vo.TqBbscjdk">
		select * from tq_bbscjdk where mlmc = 'BUSINESS' and bbrq = #{bbrq} and curprogres = 0  order by starttime desc limit 1
	</select>

	<update id="setBbscjdkToCurprogres">
		update tq_bbscjdk set curprogres = #{curprogres}, curcode = #{curcode}, curmsg = #{curmsg}, stoptime = #{stoptime} where id = #{id}
	</update>

	<select id="getBcid" resultType="com.tzx.mobilepos.rest.model.TsBck" >
		select * from ts_bck where id = (select * from p_getbcmc(CAST(#{date} AS DATETIME)))
	</select>

	<select id="getWmZdkList" resultType="com.tzx.mobilepos.rest.model.TqZdk">
		select * from vq_zdk where xsms = 'XSMS_WS'
		and jzbbrq = to_date(#{bbrq},'YYYY-MM-DD HH24:MI:SS')
		and zdzt &lt;&gt; 'ZDZT_QJ' and zdzt &lt;&gt; 'ZDZT_ZDQX'
	</select>

	<select id="getFklsXyList" resultType="java.util.Map">
		select ls.kdzdbh, ls.jzzdbh, ls.fklxsx, sd.yl3, ls.jzid, sd.fkfsmc2, ls.fkfsmc1,
		       ls.fkje as fkje, to_char(ls.czsj,'YYYY-MM-DD hh24:mi:ss') as czsj, ls.fkfsmc1 as fkmc
		from vq_fklsk ls left join ts_fkfssdk sd on ls.jzid = sd.id
		where kdzdbh = #{billNum} and ls.fkje &lt;&gt; 0 and sd.sfsr  &lt;&gt; 'N'
	</select>

	<select id="getTqThirdTempOrderByTime" resultType="com.tzx.commapi.rest.vo.TqThirdTempOrder" >
		select * from TQ_THIRD_TEMPORDER where datatype = #{dataType} and status = 0
		<if test="dateType != null and dateType != ''">
			and jzsj &lt; NOW()::date
		</if>
	</select>

	<update id="updateTqThirdTempOrderbatch" parameterType="com.tzx.commapi.rest.vo.TqThirdTempOrder">
		update TQ_THIRD_TEMPORDER set errcount = #{param.errcount}, status = #{param.status},
		runinterval = #{param.runinterval}, remark = #{param.remark}
		where id in
		<foreach item="item" index="index" collection="tttpIds" open="(" separator="," close=")">
			#{item}
		</foreach>
	</update>

	<select id="getVqFklsList" resultType="java.util.Map">
		select ls.kdzdbh, ls.jzzdbh, ls.fklxsx, sd.yl3, ls.jzid, max(ls.fkfsmc1), sum(ls.fkje) as fkje
		from vq_fklsk ls left join ts_fkfssdk sd on ls.jzid = sd.id
		where kdzdbh = #{billNum} group by ls.jzid, ls.kdzdbh, ls.jzzdbh, ls.fklxsx, sd.yl3
	</select>

	<select id="geVqZdmxList" resultType="java.util.Map" >
		select cmje,cmmc1, cmbh, cmdj, cmsl, sjje, cmdj, dcxh, to_char(tmbj,'YYYY-MM-DD hh24:mi:ss') as tmbj
		from vq_zdmxk where kdzdbh = #{billNum} and jzbbrq = #{reportDate}
						and (COALESCE(wdbz,'') = '' or wdbz = 'WDBZ_CJ' or wdbz = 'WDBZ_QX' or wdbz = 'WDBZ_FS' ) and COALESCE (memo, '') &lt;&gt; 'QX' and (cmsx ='CMSX_DP' or cmsx='CMSX_TC')
	</select>

</mapper>
