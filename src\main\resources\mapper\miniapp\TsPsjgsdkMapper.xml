<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.miniapp.rest.mapper.MiniAppTsPsjgsdkMapper">
	<select id="findLocalShopConfig" resultType="com.tzx.miniapp.rest.model.TsPsjgsdk" >
		select jg.* from ts_psjgsdk jg ,(select sdnr from ts_ggcsk where sdbt = 'FDJGBH') gg where jg.jgbh = gg.sdnr
	</select>
</mapper>