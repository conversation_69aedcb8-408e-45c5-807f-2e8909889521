package com.tzx.publics.util;

import com.tzx.publics.common.BillNoData;
import com.tzx.publics.service.IInitDataService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.locks.ReentrantLock;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019-12-16.
 */
@Component
public class GlobalLockGetDataUtil {
    private final static Logger LOGGER = LoggerFactory.getLogger(GlobalLockGetDataUtil.class);
    private ReentrantLock billLock = new ReentrantLock();
    @Autowired
    private IInitDataService initDataService;
    public BillNoData getBillNoData(String jtbh, String preChar) throws Exception {
        billLock.lock();
        LOGGER.info(this.toString());
        BillNoData billNoData = null;
        try
        {
            billNoData = initDataService.getBillNoData(jtbh, preChar);
        }
        finally {
            billLock.unlock();
        }
        return  billNoData;
    }
}
