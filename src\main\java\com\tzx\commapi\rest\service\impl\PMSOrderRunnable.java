package com.tzx.commapi.rest.service.impl;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tzx.commapi.rest.mapper.PMSOrderMapper;
import com.tzx.commapi.rest.service.ITaskApiService;
import com.tzx.commapi.rest.vo.*;
import com.tzx.publics.common.ApplicationContextUtils;
import com.tzx.publics.listener.InitDataListener;
import com.tzx.receiver.common.utils.DBUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.web.client.RestTemplate;
import java.io.IOException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

public class PMSOrderRunnable implements Runnable{
    private final static Logger LOGGER = LoggerFactory.getLogger(PMSOrderRunnable.class);
    private final Integer EXCEPT_REPEAT_COUNT = 5;//发生系统异常后重试次数
    private TqTask tqTask;
    private RestTemplate restTemplate;
    private PMSOrderMapper pmsOrderMapper;
    private ITaskApiService taskApiService;
    private JdbcTemplate jdbcTemplate;


    public TqTask getTqTask() {
        return tqTask;
    }

    public PMSOrderRunnable(TqTask tqTask) {
        this.restTemplate = ApplicationContextUtils.applicationContext.getBean(RestTemplate.class);
        this.pmsOrderMapper = ApplicationContextUtils.applicationContext.getBean(PMSOrderMapper.class);
        this.taskApiService = ApplicationContextUtils.applicationContext.getBean(ITaskApiService.class);
        this.jdbcTemplate = ApplicationContextUtils.applicationContext.getBean(JdbcTemplate.class);
        this.tqTask = tqTask;
    }

    private void doRun(){
        PMSSync pmsSync = new PMSSync();
        Integer pmsServiceType = 0;
        try{
            pmsServiceType = Integer.parseInt(DBUtils.getGGCSK("PMS_SERVICE_TYPE"));
        }catch (Exception e){
            LOGGER.info("获取服务器类型失败，使用默认值0");
        }
        //这里开始拼接实体类
        //如果是新增账单就这么拼，删除账单就那么拼
        if(StringUtils.isBlank(tqTask.getZdzt())){
            //先检查是否已经上传过了，如果上传过了。则跳过，然后直接返回成功.
            SqlRowSet sqlRowSet = jdbcTemplate.queryForRowSet("SELECT A.* from tq_task_success A\n" +
                    "inner join ts_bmkzk b on to_char(a.bbrq,'YYYY-MM-DD') = b.nr and b.zdmc = 'BBRQ' AND A.KDZDBH = ? AND A.tasttype = ?",tqTask.getObjid(),"PMS");
            if(sqlRowSet.next()){
                LOGGER.info("当前涉及到的账单已经上传过，不再做再次上传。任务唯一识别标识:{}",tqTask.getUuid());
                taskApiService.removePMSOrderTask(tqTask.getUuid());
                return;
            }
            pmsSync.setSync_type("add");//add 表示新增

            PMSOrder pmsOrder = pmsOrderMapper.findPMSOrderByKdzdbh(tqTask.getObjid());

            if("Y".equals(InitDataListener.ggcsMap.get("JZSFXSZP"))
                    &&StringUtils.isNotEmpty(pmsOrder.getTable_code())){
               pmsOrder.setTake_no(pmsOrder.getTable_code());
            }

            pmsSync.setOrder(pmsOrder);

            //读取制作时间
            Integer makeTime = 10;
            try{
                makeTime = Integer.parseInt(DBUtils.getGGCSK("PMS_DISH_COOKING_TIME"));
            }catch (Exception e){
                LOGGER.info("读取配置制作时间失败，使用默认值");
            }


            List<PMSOrderItem> pmsOrderItemList = pmsOrderMapper.findPMSOrderItems(tqTask.getObjid(),false,makeTime*60);
            pmsSync.setOrder_items(pmsOrderItemList);

            //对口味进行处理，口味不可以直接关联TQ_ZFKWWDK用SQL查询。因为1、副机并未上传该表，2、小程序或者部分外面未写入该表。
            //为保证其他模块不需改动，直接读取KWBZ字段。
            for (PMSOrderItem pmsOrderItem : pmsOrderItemList) {
                List<PMSCondiment> pmsCondimentList = new ArrayList<>();
                if (StringUtils.isNotBlank(pmsOrderItem.getKwbz())){
                    String[] kwbzAry = pmsOrderItem.getKwbz().split(" ");
                    int index = 0;
                    kwfor:for (String kw : kwbzAry) {
                        kw = kw.trim();
                        //堂食的标记
                        if("堂".equals(kw)){
                            continue kwfor;
                        }
                        //外带的标记，单品改外带，可以在PMS中通过口味展示
                        PMSCondiment pmsCondiment = null;
                        if("带".equals(kw)){
                            //用100000表示外带的标注
                            pmsCondiment = new PMSCondiment(pmsOrderItem.getOrder_item_id(),"100000",kw);
                        }else{
                            pmsCondiment = new PMSCondiment(pmsOrderItem.getOrder_item_id(),++index+"",kw);
                        }
                        pmsCondimentList.add(pmsCondiment);

                    }
//                    if(pmsCondimentList.size()>0){
//                        pmsOrderItem.setCondiments(pmsCondimentList);
//                    }
                }
                //安卓服务要求不管有没有口味都要传，没口味就传空数组[]
                pmsOrderItem.setCondiments(pmsCondimentList);
            }
        }else{
            pmsSync.setSync_type("delete");//delete 表示删除

            PMSOrder pmsOrder = new PMSOrder();
            pmsOrder.setOrder_id(tqTask.getObjid());
            PMSOrder orderChanel = pmsOrderMapper.findOrderChanel(tqTask.getObjid());
            pmsOrder.setOrder_channel(orderChanel.getOrder_channel());
            pmsSync.setOrder(pmsOrder);
            //这里PMS要求即便为空数组，也要传递。否则会报错。
            List<PMSOrderItem> pmsOrderItemList = new ArrayList<>();
            pmsSync.setOrder_items(pmsOrderItemList);
        }


        //这里调用接口
        //如果是返回非200 。则可能是网络异常，停顿500毫秒后再次调用。直至调通为止。
        String pms_url = DBUtils.getGGCSK("PMS_URL");
        //如果为空，就给一个默认值
        if(StringUtils.isBlank(pms_url)){
            pms_url = "http://127.0.0.1:82/order/sync/index";
        }
        Integer repeatTimes = 5;
        try{
            repeatTimes = Integer.parseInt(DBUtils.getGGCSK("PMS_REPEAT_TIMES"));
        }catch (Exception e){
            LOGGER.info("读取重试次数失败，使用默认值5");
        }

//        ResponseEntity<PMSResult> responseEntity = null;
        //不能直接返回到实体，因为PMS返回的是text/html类型。得获取成字符串，然后自己再转换一下。
        ResponseEntity<String> responseEntityWindows = null;
        ResponseEntity<PMSResult> responseEntityAndroid = null;
        //开始发送请求
        ObjectMapper objectMapper = new ObjectMapper();
        DateFormat dateformat= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        objectMapper.setDateFormat(dateformat);//设置时间格式
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);

        try {
            LOGGER.info("开始发送请求到PMS，请求地址:{},请求参数{}",pms_url,objectMapper.writeValueAsString(pmsSync));
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        if(pmsServiceType==0){
            try{
                responseEntityWindows = restTemplate.postForEntity(pms_url, pmsSync, String.class);
                LOGGER.info("请求返回网络状态码为{}",responseEntityWindows.getStatusCodeValue());
            }catch (Exception e){
                LOGGER.info(e.toString());
                e.printStackTrace();
            }
        }else{
            try{
                responseEntityAndroid = restTemplate.postForEntity(pms_url, pmsSync, PMSResult.class);
                LOGGER.info("请求返回网络状态码为{}",responseEntityAndroid.getStatusCodeValue());
            }catch (Exception e){
                LOGGER.info(e.toString());
                e.printStackTrace();
            }
        }


        int count = 0;
        //网络异常时，进入重试逻辑
        while( ( pmsServiceType==0&& (responseEntityWindows==null||!responseEntityWindows.getStatusCode().is2xxSuccessful()))
                || ( pmsServiceType==1&& (responseEntityAndroid==null||!responseEntityAndroid.getStatusCode().is2xxSuccessful())) ){
            //停顿10秒，用于等待PMS服务恢复。
            try{
                TimeUnit.SECONDS.sleep(10);
            }catch (InterruptedException e){
                e.printStackTrace();
            }
            LOGGER.info("发送PMS请求失败，正在进行第{}次重试",++count);
            try {
                LOGGER.info("开始发送请求到PMS，请求地址:{},请求参数{}",pms_url,objectMapper.writeValueAsString(pmsSync));
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
            if(pmsServiceType==0){
                try{
                    responseEntityWindows = restTemplate.postForEntity(pms_url, pmsSync, String.class);
                    LOGGER.info("请求返回网络状态码为{}",responseEntityWindows.getStatusCodeValue());
                }catch (Exception e){
                    LOGGER.info(e.toString());
                    e.printStackTrace();
                }
            }else{
                try{
                    responseEntityAndroid = restTemplate.postForEntity(pms_url, pmsSync, PMSResult.class);
                    LOGGER.info("请求返回网络状态码为{}",responseEntityAndroid.getStatusCodeValue());
                }catch (Exception e){
                    LOGGER.info(e.toString());
                    e.printStackTrace();
                }
            }

            //如果达到了最大重试次数，说明距离点菜过后已经足够长时间了。此时再上传至KVS也无太大意义，且为了避免队列堆积过多造成OOM。做丢弃处理
            if(count>repeatTimes){
                LOGGER.info("已达到最大重试次数，此次账单发送PMS失败。任务被丢弃。任务信息{}",tqTask.toString());
                //删除持久化的任务队列
                taskApiService.removePMSOrderTask(tqTask.getUuid());
                return;
            }
        }
        PMSResult pmsResult = null ;
        if(pmsServiceType==0){
            String responeStr = responseEntityWindows.getBody();
            //此种是网络成功了，但是没有返回载体。认为PMS服务异常了，或者请求体异常了。此时无论重试几次都认为异常，这种情况需要人为干预，
            //或者修改代码处理了。所以这种情况不重试，且要删除持久化任务队列
            if(responeStr==null){

                LOGGER.info("未得到有效返回体，此次账单发送PMS失败。任务被丢弃。任务信息{}",tqTask.toString());
                //删除持久化的任务队列
                taskApiService.removePMSOrderTask(tqTask.getUuid());
                return;
            }
            LOGGER.info("PMS返回的内容为:{} 。任务唯一识别标识：{}",responeStr,tqTask.getUuid());
            try {
                pmsResult = objectMapper.readValue(responeStr,PMSResult.class);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }else{
            pmsResult = responseEntityAndroid.getBody();
            try {
                LOGGER.info("PMS返回的内容为:{} 。任务唯一识别标识：{}",objectMapper.writeValueAsString(pmsResult),tqTask.getUuid());
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
        }

        if(pmsResult==null){

            LOGGER.info("转换到实体类失败，此次账单发送PMS失败。任务被丢弃。任务信息{}",tqTask.toString());
            //删除持久化的任务队列
            taskApiService.removePMSOrderTask(tqTask.getUuid());
            return;
        }
        //判断返回体的值。0表示成功，2表示重复订单，此处默认为PMS是幂等的。所以0和1都是成功。
        //其余状态码认为失败。状态码是失败的，也不再重试。因为业务层的失败，重试几次仍是失败
        if(pmsResult.getCode().intValue()==2||pmsResult.getCode().intValue()==0){
            LOGGER.info("发送请求成功,任务唯一识别号:{}",tqTask.getUuid());
            //发送成功后，记录成功日志。因为副机做数据重传的时候会把tq_zdk删除重新新增。此时会触发触发器，插入任务表。然后会再次触发上传
            String sql = " insert into tq_task_success(kdzdbh,bbrq,tasttype) values (?,?,?)";
            jdbcTemplate.update(sql,tqTask.getObjid(),tqTask.getJzbbrq(),"PMS");

        }else{
            LOGGER.info("发送请求返回失败，错误代码:{},错误信息:{},唯一识别号:{}",pmsResult.getCode(),pmsResult.getMsg(),tqTask.getUuid());
        }
        //删除持久化任务
        taskApiService.removePMSOrderTask(tqTask.getUuid());
        LOGGER.info("上传任务处理完成，任务唯一识别码:{}",tqTask.toString());
    }

    @Override
    public void run() {
        int retryCount = 0;//异常重试次数
        boolean doRunSuccess = false;
        //对未被上传逻辑中处理的异常进行处理。比如POS数据库断开之类的。
        while (!doRunSuccess && retryCount<EXCEPT_REPEAT_COUNT+1){
            try{
                //
                if(retryCount>0){
                    LOGGER.info("正在进行异常后的第{}次重试",retryCount);
                    //停顿2秒，再进行重试。目的是等待让一些其他环境有可能再这段时间内恢复
                    try{
                        TimeUnit.SECONDS.sleep(2);
                    }catch (InterruptedException e){
                        e.printStackTrace();
                    }
                }
                doRun();
                doRunSuccess = true;
                //上传次数加1
                retryCount++;
            }catch (Exception e){
                LOGGER.info("上传到PMS发生异常" + e.getMessage());
                e.printStackTrace();
            }
        }
        //如果经过N次重试后，仍然有异常。那么就丢弃不再处理
        if(!doRunSuccess){
            LOGGER.info("上传过程发生异常，并经过{}次重试后，仍未被处理。此次任务被移除" ,EXCEPT_REPEAT_COUNT);
            taskApiService.removePMSOrderTask(tqTask.getUuid());
        }

    }
}
