package com.tzx.receiver.common.msg;

import java.beans.BeanInfo;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.MissingResourceException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class MsgSqlUtil {
	protected static Logger	logger	= LoggerFactory.getLogger(MsgSqlUtil.class);
	protected static final Object[]	NULL_ARGS	= {};

	public static String getTruncatSql(String tbname)
	{
		String sql = "TRUNCATE TABLE " + tbname;
		return sql;
	}

	public static String getFindSql(String tbname, Object object, String where)
	{
		PropertyDescriptor[] propertyDescriptors = null;
		Class beanClass = object.getClass();
		if (beanClass != null)
		{
			try
			{
				BeanInfo beanInfo = Introspector.getBeanInfo(beanClass);
				propertyDescriptors = beanInfo.getPropertyDescriptors();
			}
			catch (IntrospectionException e)
			{
				e.printStackTrace();
			}
		}
		if (propertyDescriptors == null)
		{
			propertyDescriptors = new PropertyDescriptor[0];
		}
		int size = propertyDescriptors.length;
		DateFormat dateformat = new SimpleDateFormat("yyyy-MM-dd");
		ResourceManager resource = ResourceManager.getInstance();
		String colsql = "SELECT 1 FROM " + tbname;
		String[] primarys = where.split(",");
		String primarySqls = "";
		for (int i = 0; i < size; i++)
		{
			PropertyDescriptor propertyDescriptor = propertyDescriptors[i];
			String name = propertyDescriptor.getName();
			Method readMethod = propertyDescriptor.getReadMethod();
			Method writeMethod = propertyDescriptor.getWriteMethod();
			Class propertyType = propertyDescriptor.getPropertyType();
			String colName = tbname + "_" + name.toUpperCase();
			if (!name.equalsIgnoreCase("CLASS"))
			{
				try
				{
					String col = "";
					try
					{
						col = resource.getString(colName);
					}
					catch (MissingResourceException e)
					{
						col = "";
					}
					for (int j = 0; j < primarys.length; j++)
					{
						if (!col.equals("NULL") && col.equalsIgnoreCase(primarys[j]))
						{
							Object data = readMethod.invoke(object, NULL_ARGS);
							if (data != null)
							{
								primarySqls = primarySqls + col + "=" + "'" + data.toString() + "' AND ";
								//System.out.println(primarySqls);
							}
						}
					}
				}
				catch (Exception e)
				{
					e.printStackTrace();
				}
			}
		}
		String sql = colsql + " WHERE " + primarySqls.substring(0, primarySqls.length() - 4);
		//System.out.println(sql);
		logger.info(sql);
		return sql;
	}

	public static String getDeleteSql(String tbname, Object object, String where)
	{
		PropertyDescriptor[] propertyDescriptors = null;
		Class beanClass = object.getClass();
		String sql = "";
		if (beanClass != null)
		{
			try
			{
				BeanInfo beanInfo = Introspector.getBeanInfo(beanClass);
				propertyDescriptors = beanInfo.getPropertyDescriptors();
			}
			catch (IntrospectionException e)
			{
				e.printStackTrace();
			}
		}
		if (propertyDescriptors == null)
		{
			propertyDescriptors = new PropertyDescriptor[0];
		}
		int size = propertyDescriptors.length;
		DateFormat dateformat = new SimpleDateFormat("yyyy-MM-dd");
		ResourceManager resource = ResourceManager.getInstance();
		String colsql = "DELETE FROM " + tbname;
		String[] primarys = where.split(",");
		String primarySqls = "";
		for (int i = 0; i < size; i++)
		{
			PropertyDescriptor propertyDescriptor = propertyDescriptors[i];
			String name = propertyDescriptor.getName();
			Method readMethod = propertyDescriptor.getReadMethod();
			Method writeMethod = propertyDescriptor.getWriteMethod();
			Class propertyType = propertyDescriptor.getPropertyType();
			String colName = tbname + "_" + name.toUpperCase();
			if (!name.equalsIgnoreCase("CLASS"))
			{
				try
				{
					String col = "";
					try
					{
						col = resource.getString(colName);
					}
					catch (MissingResourceException e)
					{
						col = "";
					}
					for (int j = 0; j < primarys.length; j++)
					{
						if (!col.equals("NULL") && col.equalsIgnoreCase(primarys[j]))
						{
							Object data = readMethod.invoke(object, NULL_ARGS);
							if (data != null)
							{
								primarySqls = primarySqls + col + "=" + "'" + data.toString() + "' AND ";
							}
						}
					}
				}
				catch (Exception e)
				{
					e.printStackTrace();
					logger.error("System Error", e);
				}
			}
		}
		if(!"".equals(primarySqls)){
			sql = colsql + " WHERE " + primarySqls.substring(0, primarySqls.length() - 4);
		}
		logger.info(sql);
		return sql;
	}

	/*
	 * 生成sql; 如果要进行转换格式如下： $Y:N,N:Y
	 */
	public static String getInsertSql(String tbname, Object object)
	{

		PropertyDescriptor[] propertyDescriptors = null;
		Class beanClass = object.getClass();
		String sql = "";
		if (beanClass != null)
		{
			try
			{
				BeanInfo beanInfo = Introspector.getBeanInfo(beanClass);
				propertyDescriptors = beanInfo.getPropertyDescriptors();
			}
			catch (IntrospectionException e)
			{
				e.printStackTrace();
			}
		}
		if (propertyDescriptors == null)
		{
			propertyDescriptors = new PropertyDescriptor[0];
		}
		int size = propertyDescriptors.length;
		DateFormat dateformat = new SimpleDateFormat("yyyy-MM-dd");
		ResourceManager resource = ResourceManager.getInstance();
		String colsql = "INSERT INTO " + tbname + " (";
		String valuesql = " VALUES (";
		for (int i = 0; i < size; i++)
		{
			PropertyDescriptor propertyDescriptor = propertyDescriptors[i];
			String name = propertyDescriptor.getName();
			Method readMethod = propertyDescriptor.getReadMethod();
			Method writeMethod = propertyDescriptor.getWriteMethod();
			Class propertyType = propertyDescriptor.getPropertyType();
			String colName = tbname + "_" + name.toUpperCase();
			String nvarcharName = tbname + "_NVARCHAR";
			if (!name.equalsIgnoreCase("CLASS"))
			{

				try
				{
					String col = "";
					String value = "";
					String nCol = "";
					try
					{
 						col = resource.getString(colName);
 
					}
					catch (MissingResourceException e)
					{
						col = "";
						logger.info("下发字段:"+colName+"门店没有对应字段：");
					}
					try
					{
						nCol = resource.getString(nvarcharName);
					}
					catch (MissingResourceException e)
					{
						nCol = "";
					}

					if (col.indexOf("$") >= 0)
					{// 判断字段值是否需要转换
						value = col.substring(col.indexOf("$") + 1);
					}

					if (!col.equals("NULL") && !col.equals(""))
					{
						col = col.indexOf("$") >= 0 ? col.substring(0, col.indexOf("$")) : col;
						colsql = colsql + col + ",";
						Object data = readMethod.invoke(object, NULL_ARGS);
						if (data != null)
						{
							if (Arrays.binarySearch(nCol.trim().split(","), col.trim()) >= 0)
							{
								nCol = "N";
							}
							else
							{
								nCol = "";
							}

							if (value != null && value.length() > 0)
							{// 生成sql的值，如果在配置文件字段后面有值则用配置文件的值将现有的值替换了
								if (propertyType.getName().equalsIgnoreCase(java.lang.Double.class.getName()) || propertyType.getName().equalsIgnoreCase(java.lang.Integer.class.getName()))
								{// 如果是是整形
									valuesql = valuesql + (value.indexOf(data.toString()) >= 0 ? value.substring(value.indexOf(data.toString()) + 2, value.indexOf(",")) : data.toString()) + ",";
								}
								else if (propertyType.getName().equalsIgnoreCase(java.util.Date.class.getName()))
								{// 如果是日期类型
									valuesql = valuesql + nCol + "'" + (value.indexOf(data.toString()) >= 0 ? value.substring(value.indexOf(data.toString()) + 2, value.indexOf(",")) : dateformat.format(data)) + "',";
								}
								else
								{
									if (data.toString().equals("")){
										valuesql = valuesql + "'',";
									}else{
										String[] clent_values = value.split(",");
										String clent_sql = "";
										for (int k = 0; k < clent_values.length; k++)
										{
											if (clent_values[k].startsWith(data.toString()))
											{
												clent_sql = clent_values[k];
											}
										}
										valuesql = valuesql + nCol + "'" + (clent_sql.indexOf(data.toString()) >= 0 ? clent_sql.substring(data.toString().length() + 1) : data.toString()) + "',";
										System.out.println(valuesql);
									}


								}
								// System.out.println(valuesql);
							}
							else
							{
								if (propertyType.getName().equalsIgnoreCase(java.lang.Double.class.getName()) || propertyType.getName().equalsIgnoreCase(java.lang.Integer.class.getName()))
								{
									valuesql = valuesql + data.toString() + ",";
								}
								else if (propertyType.getName().equalsIgnoreCase(java.util.Date.class.getName()))
								{
									valuesql = valuesql + nCol + "'" + dateformat.format(data) + "',";
								}
								else
								{
									if (!(data.toString().equals("-1")))
									{
										valuesql = valuesql + nCol + "'" + data.toString() + "',";
									}
									else
									{
										valuesql = valuesql + nCol + "'" + "" + "',";
									}

								}
							}
						}
						else
						{
 							if (propertyType.getName().equalsIgnoreCase(java.lang.Double.class.getName()) || propertyType.getName().equalsIgnoreCase(java.lang.Integer.class.getName()))
							{
								valuesql = valuesql + null + ",";
							}
							else if (propertyType.getName().equalsIgnoreCase(java.util.Date.class.getName()))
							{
								valuesql = valuesql + "" + null + ",";
							}
							else
							{
								valuesql = valuesql + "'',";
							}
						}
					}
				}
				catch (Exception e)
				{
					//e.printStackTrace();
					logger.error("System Error", e);
				}
			}
		}
		colsql = colsql.substring(0, colsql.length() - 1);
		valuesql = valuesql.substring(0, valuesql.length() - 1);
		sql = colsql + " ) " + valuesql + ")";
		System.out.println(sql);

		if (sql != null || sql.length() > 0)
		{
			return sql;
		}
		else
		{
			return null;
		}
	}

	public static String getUpdateSql(String tbname, Object object, String wheres)
	{

		PropertyDescriptor[] propertyDescriptors = null;
		Class beanClass = object.getClass();
		String sql = "";
		String[] where = wheres.split(",");
		if (beanClass != null)
		{
			try
			{
				BeanInfo beanInfo = Introspector.getBeanInfo(beanClass);
				propertyDescriptors = beanInfo.getPropertyDescriptors();
			}
			catch (IntrospectionException e)
			{
				e.printStackTrace();
			}
		}
		if (propertyDescriptors == null)
		{
			propertyDescriptors = new PropertyDescriptor[0];
		}
		int size = propertyDescriptors.length;
		DateFormat dateformat = new SimpleDateFormat("yyyy-MM-dd");
		ResourceManager resource = ResourceManager.getInstance();
		String colsql = "UPDATE " + tbname + " SET ";
		String condition = "";
		for (int i = 0; i < size; i++)
		{
			PropertyDescriptor propertyDescriptor = propertyDescriptors[i];
			String name = propertyDescriptor.getName();
			Method readMethod = propertyDescriptor.getReadMethod();
			Method writeMethod = propertyDescriptor.getWriteMethod();
			Class propertyType = propertyDescriptor.getPropertyType();
			String colName = tbname + "_" + name.toUpperCase();
			String nvarcharName = tbname + "_NVARCHAR";

			if (!name.equalsIgnoreCase("CLASS"))
			{
				try
				{
					String col = "";
					String value = "";
					String nCol="";
					try
					{
						col = resource.getString(colName);
					}
					catch (MissingResourceException e)
					{
						col = "";
						logger.info("下发字段:"+colName+"门店没有对应字段：");
					}
					try
					{
						nCol = resource.getString(nvarcharName);
					}
					catch (MissingResourceException e)
					{
						nCol = "";
					}
					if (col.indexOf("$") >= 0)
					{// 判断字段值是否需要转换
						value = col.substring(col.indexOf("$") + 1);
					}
					if (!col.equals("NULL")&& !col.equals(""))
					{

						Object data = readMethod.invoke(object, NULL_ARGS);
						if (data != null)
						{
							// colsql = colsql+ (col.indexOf("$")>=0 ?
							// col.substring(0,col.indexOf("$")):col) + ",";
							
							if (Arrays.binarySearch(nCol.trim().split(","), col.trim()) >= 0)
							{
								nCol = "N";
							}
							else
							{
								nCol = "";
							}

							if (value != null && value.length() > 0)
							{// 生成sql的值，如果在配置文件字段后面有值则用配置文件的值将现有的值替换了
								if (propertyType.getName().equalsIgnoreCase(java.lang.Double.class.getName()) || propertyType.getName().equalsIgnoreCase(java.lang.Integer.class.getName()))
								{
									colsql = colsql + (col.indexOf("$") >= 0 ? col.substring(0, col.indexOf("$")) : col) + "=" + (value.indexOf(data.toString()) >= 0 ? value.substring(value.indexOf(data.toString()) + 2, value.indexOf(",")) : data.toString()) + ",";
								}
								else if (propertyType.getName().equalsIgnoreCase(java.util.Date.class.getName()))
								{
									colsql = colsql + (col.indexOf("$") >= 0 ? col.substring(0, col.indexOf("$")) : col) + "='" + (value.indexOf(data.toString()) >= 0 ? value.substring(value.indexOf(data.toString()) + 2, value.indexOf(",")) : dateformat.format(data)) + "',";
								}
								else
								{

									String[] clent_values = value.split(",");
									String clent_sql = "";
									for (int k = 0; k < clent_values.length; k++)
									{
										if (clent_values[k].startsWith(data.toString()))
										{
											clent_sql = clent_values[k];
										}
									}
									colsql = colsql + (col.indexOf("$") >= 0 ? col.substring(0, col.indexOf("$")) : col) + "='" + (clent_sql.indexOf(data.toString()) >= 0 ? clent_sql.substring(data.toString().length() + 1) : data.toString()) + "',";
								}

							}
							else
							{
								if (propertyType.getName().equalsIgnoreCase(java.lang.Double.class.getName()) || propertyType.getName().equalsIgnoreCase(java.lang.Integer.class.getName()))
								{
									colsql = colsql + col + "=" + data.toString() + ",";
								}
								else if (propertyType.getName().equalsIgnoreCase(java.util.Date.class.getName()))
								{
									colsql = colsql + col + "='" + dateformat.format(data) + "',";
								}
								else
								{
									colsql = colsql + col + "=" + nCol + "'" + data.toString() + "',";
								}
							}
							if (where.length > 0)
							{
								for (int z = 0; z < where.length; z++)
								{
									if (col.equalsIgnoreCase(where[z]))
									{
										condition = condition != null && condition.equals("") && condition.length() <= 0 ? (" WHERE " + col + "='" + data.toString() + "'") : (condition + " and " + col + "='" + data.toString() + "'");
									}
								}
							}

						}
						
					}
					else if (col.equalsIgnoreCase(wheres))
					{
						Object data = readMethod.invoke(object, NULL_ARGS);
						if (where.length > 0)
						{
							for (int z = 0; z < where.length; z++)
							{
								if (col.equalsIgnoreCase(where[z]))
								{
									condition = condition != null && condition.equals("") && condition.length() <= 0 ? (" WHERE " + col + "='" + data.toString() + "'") : (" and " + col + "='" + data.toString() + "'");
								}
							}
						}

					}
				}
				catch (Exception e)
				{
					e.printStackTrace();
					logger.error(" System error ", e);
				}
			}
		}
		colsql = colsql.substring(0, colsql.length() - 1);
		sql = colsql + condition;
		logger.info(sql);
		if (sql != null || sql.length() > 0)
		{
			return sql;
		}
		else
		{
			return null;
		}
	}
}
