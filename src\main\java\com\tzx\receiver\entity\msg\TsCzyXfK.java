package com.tzx.receiver.entity.msg;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by <PERSON>x<PERSON> on 2020-09-11.
 */
public class TsCzyXfK implements Serializable {
    private Integer czyzbid;
    private Integer id;
    private String  czybh;
    private String  czymc1;
    private String  czymc2;
    private String  czyxb;
    private String  czymm;
    private String  qxkbh;
    private String  czyzt;
    private Date    yxrq;
    private String  ime;
    private Date    xgmmsj;
    private String  czykh;
    private String  yl1;
    private String  yl2;
    private String  yl3;
    private String phone;

    public Integer getCzyzbid() {
        return czyzbid;
    }

    public void setCzyzbid(Integer czyzbid) {
        this.czyzbid = czyzbid;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getCzybh() {
        return czybh;
    }

    public void setCzybh(String czybh) {
        this.czybh = czybh;
    }

    public String getCzymc1() {
        return czymc1;
    }

    public void setCzymc1(String czymc1) {
        this.czymc1 = czymc1;
    }

    public String getCzymc2() {
        return czymc2;
    }

    public void setCzymc2(String czymc2) {
        this.czymc2 = czymc2;
    }

    public String getCzyxb() {
        return czyxb;
    }

    public void setCzyxb(String czyxb) {
        this.czyxb = czyxb;
    }

    public String getCzymm() {
        return czymm;
    }

    public void setCzymm(String czymm) {
        this.czymm = czymm;
    }

    public String getQxkbh() {
        return qxkbh;
    }

    public void setQxkbh(String qxkbh) {
        this.qxkbh = qxkbh;
    }

    public String getCzyzt() {
        return czyzt;
    }

    public void setCzyzt(String czyzt) {
        this.czyzt = czyzt;
    }

    public Date getYxrq() {
        return yxrq;
    }

    public void setYxrq(Date yxrq) {
        this.yxrq = yxrq;
    }

    public String getIme() {
        return ime;
    }

    public void setIme(String ime) {
        this.ime = ime;
    }

    public Date getXgmmsj() {
        return xgmmsj;
    }

    public void setXgmmsj(Date xgmmsj) {
        this.xgmmsj = xgmmsj;
    }

    public String getCzykh() {
        return czykh;
    }

    public void setCzykh(String czykh) {
        this.czykh = czykh;
    }

    public String getYl1() {
        return yl1;
    }

    public void setYl1(String yl1) {
        this.yl1 = yl1;
    }

    public String getYl2() {
        return yl2;
    }

    public void setYl2(String yl2) {
        this.yl2 = yl2;
    }

    public String getYl3() {
        return yl3;
    }

    public void setYl3(String yl3) {
        this.yl3 = yl3;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }
}
