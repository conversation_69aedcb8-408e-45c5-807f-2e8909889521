package com.tzx.mobilepos.rest.mapper;

import java.util.List;

import com.tzx.mobilepos.rest.vo.TqBillOtherinfoVo;
import org.apache.ibatis.annotations.Param;

import com.tzx.mobilepos.rest.model.TqFklslsk;
import com.tzx.mobilepos.rest.model.TqMemberInfo;
import com.tzx.publics.base.MyMapper;

/**
 *
 * <AUTHOR>
 * @since 2019-4-4
 */

public interface MobilePosAcewillMemberinfoMapper extends MyMapper<TqMemberInfo> {

	public List<TqMemberInfo> getAcewillMembers(@Param("zdbh") String zdbh, @Param("scancode") String scancode);
	
	public int updateUsaByCardcode(@Param("zdbh") String zdbh, @Param("scancode") String scancode, @Param("usable") int usable);

	public int updateToBalance(@Param("zdbh") String zdbh, @Param("scancode") String scancode, @Param("balance") double balance, @Param("credit") double credit);
	
	public String getACERifUrl();
	
	public TqMemberInfo getAcewillScancode(@Param("zdbh") String zdbh);
	
	public double findBalanceAmount(@Param("zdbh") String zdbh, @Param("fkfsbh") String fkfsbh);
	
	public double findBalanceYhAmount(@Param("zdbh") String zdbh);
	
	public double findPointsYhAmount(@Param("zdbh") String zdbh);

	public List<TqFklslsk> findBalanceList(@Param("zdbh") String zdbh, @Param("fkfsbh") String fkfsbh);
	
	public int updateFklsById(@Param("id") int id, @Param("fkje") double fkje);
	
	public int delFklsById(@Param("id") int id);
	
	public int updateZdkByCwlxbh(@Param("kdzdbh") String kdzdbh, @Param("cwlxbh") String cwlxbh);

	public String getIsVipPrice(@Param("grade") int grade);
	
	public double findBalanceAmountVq(@Param("zdbh") String zdbh, @Param("fkfsbh") String fkfsbh);

	public TqBillOtherinfoVo getTqBillOtherinfo(@Param("zdbh") String zdbh);

	public List<TqMemberInfo> getIsPvMembers(@Param("zdbh") String zdbh, @Param("isPv") String isPv);

}