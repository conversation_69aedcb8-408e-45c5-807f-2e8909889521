package com.tzx;

import com.tzx.miniapp.rest.service.IMiniAppQmOrderPrecheckNew;
import com.tzx.miniapp.rest.service.impl.MiniAppQmOrderPrecheckNewImpl;
import net.sf.json.JSONObject;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * QmServiceTest
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-07-12
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
public class QmServiceTest {


    @Autowired
    private IMiniAppQmOrderPrecheckNew qmOrderPrecheckNew;

    @Test
    public void test() {
        JSONObject member=new JSONObject();
        member.put("cno","1123123123");
        member.put("mobile","15210853162");
        qmOrderPrecheckNew.jointDealdetails("00000000000", member, "2024-07-12", "99");
    }
}
