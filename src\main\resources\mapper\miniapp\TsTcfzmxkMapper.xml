<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.miniapp.rest.mapper.MiniAppTsTcfzmxkMapper">
	<select id="findTsTcfzmxkBasicData" resultType="com.tzx.miniapp.rest.vo.ComboGroup">
        select tcm.id,tcm.mxid as details_id,cm.cmmc1 as details_name,tcm.fzid as group_id,
        tcm.fzsl as item_count,tcm.fzje,tcm.sfmr as is_default,tcm.jgtxbh 
        from ts_tcfzmxk tcm left join ts_cmk cm on cm.cmid = tcm.mxid
    </select>
</mapper>
