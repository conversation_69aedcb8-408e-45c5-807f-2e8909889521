package com.tzx.publics.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public class PosPrintJna {
	private final static Logger LOGGER = LoggerFactory.getLogger(PosPrintJna.class);
//	private TzxReportLibInt lib = null;
//	private String dllPath = "";
	
	// 定义消费者线程池
	private static volatile ThreadPoolExecutor memberUpThreadPool = null;

	/**
	 * @Description 获取线程池对象（通过双重检查锁实现）。
	 * @param
	 * @return ThreadPoolExecutor 线程池对象
	 * @see
	 */
	public static ThreadPoolExecutor getMemberUpThreadPool() {
		if (memberUpThreadPool == null) {
			synchronized (ThreadPoolExecutor.class) {
				if (memberUpThreadPool == null) {
					//核心线程数1，最大线程数1，使用LinkedBlockingQueue阻塞队列，队列深度1000
					//线程数超过队列大小时的策略为重试。
					memberUpThreadPool = new ThreadPoolExecutor(1, 1, 3, TimeUnit.MINUTES, new LinkedBlockingQueue<Runnable>(1000), new ThreadPoolExecutor.DiscardPolicy());
				}
			}
		}
		LOGGER.info("memberUpThreadPool size " +  memberUpThreadPool.getQueue().size());
		return memberUpThreadPool;
	}

	public void tzxReportlib(int printid, String printStr, String posNo) {
		try {
			getMemberUpThreadPool().execute(new RunPosPrint(printid, printStr, posNo));
		} catch (Exception e) {
			e.printStackTrace();
		}

	}

//	public void savePrintParamFile(String fileName, String printStr) {
//		File file = new File(fileName);
//		Writer out = null;
//		try {
//			out = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(file), "GBK"));
//			out.write(printStr);
//			out.close();
//		} catch (IOException e) {
//			e.printStackTrace();
//		}
//	}

}
