package com.tzx.receiver.entity.msg;

import java.io.Serializable;

public class Freetype
  implements Serializable
{
  private Integer id;
  private String mdmc;
  private Integer mdbh;
  private String wbbm;
  private String pyjm;
  private String memo;
  private Integer parentId;
  private String ywmc2;

  public Integer getId()
  {
    return this.id; }

  public void setId(Integer id) {
    this.id = id; }

  public String getMdmc() {
    return this.mdmc; }

  public void setMdmc(String mdmc) {
    this.mdmc = mdmc; }

  public Integer getMdbh() {
    return this.mdbh; }

  public void setMdbh(Integer mdbh) {
    this.mdbh = mdbh; }

  public String getWbbm() {
    return this.wbbm; }

  public void setWbbm(String wbbm) {
    this.wbbm = wbbm; }

  public String getPyjm() {
    return this.pyjm; }

  public void setPyjm(String pyjm) {
    this.pyjm = pyjm; }

  public String getMemo() {
    return this.memo; }

  public void setMemo(String memo) {
    this.memo = memo; }

  public Integer getParentId() {
    return this.parentId; }

  public void setParentId(Integer parentId) {
    this.parentId = parentId; }

  public String getYwmc2() {
    return this.ywmc2; }

  public void setYwmc2(String ywmc2) {
    this.ywmc2 = ywmc2;
  }
}