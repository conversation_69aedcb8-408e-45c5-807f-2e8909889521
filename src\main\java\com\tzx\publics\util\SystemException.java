package com.tzx.publics.util;

import java.io.PrintStream;
import java.io.PrintWriter;
import java.io.StringWriter;


public class SystemException extends RuntimeException
{

	private static final long			serialVersionUID	= 1L;

	private ErrorCode					errorCode;


	public static SystemException wrap(Throwable exception, ErrorCode errorCode)
	{
		if (exception instanceof SystemException)
		{
			return (SystemException) exception;
		}
		else
		{
			return new SystemException(exception.getMessage(), exception, errorCode);
		}
	}

	public static SystemException convertSystemException(Throwable exception)
	{
		if (exception instanceof SystemException)
		{
			SystemException se = (SystemException) exception;
			return se;
		}
		else
		{
			return new SystemException(exception, SystemErrorCode.SYSTEM_ERROR);
		}
	}

	public static SystemException getInstance(ErrorCode errorCode)
	{
		return getInstance(errorCode.getMessage(), errorCode);
	}

	public static SystemException getInstance(String message, ErrorCode errorCode)
	{
		return new SystemException(message, errorCode);
	}

	public SystemException(ErrorCode errorCode)
	{
		this.errorCode = errorCode;
	}

	public SystemException(String message, ErrorCode errorCode)
	{
		super(message);
		this.errorCode = errorCode;
	}

	public SystemException(Throwable cause, ErrorCode errorCode)
	{
		super(cause);
		this.errorCode = errorCode;
	}

	public SystemException(String message, Throwable cause, ErrorCode errorCode)
	{
		super(message, cause);
		this.errorCode = errorCode;
	}

	public ErrorCode getErrorCode()
	{
		return errorCode;
	}

	

	public SystemException setErrorCode(ErrorCode errorCode)
	{
		this.errorCode = errorCode;
		return this;
	}

	

	
	/**
	 * 将ErrorStack转化为String.
	 */
	public String getStackTraceAsString()
	{
		StringWriter stringWriter = new StringWriter();
		this.printStackTrace(new PrintWriter(stringWriter));
		return stringWriter.toString();
	}

	public void printStackTrace(PrintStream s)
	{
		synchronized (s)
		{
			printStackTrace(new PrintWriter(s));
		}
	}

	
}
