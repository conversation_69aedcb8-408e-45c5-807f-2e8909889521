package com.tzx.ecoserver.common;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019-10-26.
 */
public class EcoRepData {
    public String getSuccess() {
        return success;
    }

    public void setSuccess(String success) {
        this.success = success;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getCheck_id() {
        return check_id;
    }

    public void setCheck_id(String check_id) {
        this.check_id = check_id;
    }

    private String success;
    private String msg;
    private String check_id;
    private String tagstr;
    private String isOrderPart;
    private String hasDoneOrderPat;
    private String ygdlcs;
    private String fwyh;
    private String ishis;
    private String bbrq;
    // 接单类型
    // NORMAL：正常；ERROR：异常接单
    private String jdlx;
    private Integer isPlatform;//是否平台类型
    private String source;

    public String getJdlx() {
		return jdlx;
	}

	public void setJdlx(String jdlx) {
		this.jdlx = jdlx;
	}

	public String getHasDoneOrderPat() {
        return hasDoneOrderPat;
    }

    public void setHasDoneOrderPat(String hasDoneOrderPat) {
        this.hasDoneOrderPat = hasDoneOrderPat;
    }

    public String getIsOrderPart() {
        return isOrderPart;
    }

    public void setIsOrderPart(String isOrderPart) {
        this.isOrderPart = isOrderPart;
    }

    public String getTagstr() {
        return tagstr;
    }

    public void setTagstr(String tagstr) {
        this.tagstr = tagstr;
    }

    public String getYgdlcs() {
        return ygdlcs;
    }

    public void setYgdlcs(String ygdlcs) {
        this.ygdlcs = ygdlcs;
    }

    public String getFwyh() {
        return fwyh;
    }

    public void setFwyh(String fwyh) {
        this.fwyh = fwyh;
    }

    public String getIshis() {
        return ishis;
    }

    public void setIshis(String ishis) {
        this.ishis = ishis;
    }

    public String getBbrq() {
        return bbrq;
    }

    public void setBbrq(String bbrq) {
        this.bbrq = bbrq;
    }

    public Integer getIsPlatform() {
        if(null == isPlatform){
            isPlatform = 1;
        }
        return isPlatform;
    }

    public void setIsPlatform(Integer isPlatform) {
        this.isPlatform = isPlatform;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }
}
