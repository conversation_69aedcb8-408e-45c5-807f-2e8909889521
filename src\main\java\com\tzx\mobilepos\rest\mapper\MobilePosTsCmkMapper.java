package com.tzx.mobilepos.rest.mapper;

import com.tzx.publics.base.MyMapper;
import com.tzx.mobilepos.rest.model.TsCmk;
import com.tzx.mobilepos.rest.model.TsTcmxk;
import com.tzx.mobilepos.rest.vo.Dish;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
*
* <AUTHOR>
* @since 2018-05-18
*/
public interface MobilePosTsCmkMapper extends MyMapper<TsCmk> {
	public List<Dish> findDishBasicData(@Param("clmxlb") String clmxlb, @Param("week") int week);
	
	public List<TsTcmxk> findTcmxByXmid(@Param("xmid") int xmid);
	
	public List<Dish> findSpecBasicData();
	
}
