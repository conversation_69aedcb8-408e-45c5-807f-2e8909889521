package com.tzx.mobilepos.rest.model;

import com.tzx.publics.base.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;

@Table(name = "TQ_YHMTCOUPONSTEMP")
public class TqYhMtCouponsTemp  extends BaseEntity implements Serializable{

	private static final long serialVersionUID = -5645213307816746194L;
	
	@Column(name = "YHFSID")
	private Integer yhfsid;
	
	@Column(name = "CLMXID")
	private Integer clmxid;
	
	@Column(name = "CMID")
	private Integer cmid;
	
	@Column(name = "OPTYPE")
	private Integer optype;
	
	@Column(name = "SJJE")
	private BigDecimal sjje;
	
	@Column(name = "KDZDBH")
	private String kdzdbh;
	
	@Column(name = "PAYABLEAMT")
	private BigDecimal payableamt;
	
	@Column(name = "PQLX")
	private String pqlx;
	
	@Column(name = "YZM")
	private String yzm;
	
	@Column(name = "JZID")
	private Integer jzid;
	
	@Column(name = "BUYPRICE")
	private BigDecimal buyprice;
	
	@Column(name = "WDRWID")
	private Integer wdrwid;

	public TqYhMtCouponsTemp() {
		super();
	}

	public TqYhMtCouponsTemp(Integer yhfsid, Integer clmxid, Integer cmid,
			Integer optype, BigDecimal sjje, String kdzdbh,
			BigDecimal payableamt, String pqlx, String yzm, Integer jzid,
			BigDecimal buyprice, Integer wdrwid) {
		super();
		this.yhfsid = yhfsid;
		this.clmxid = clmxid;
		this.cmid = cmid;
		this.optype = optype;
		this.sjje = sjje;
		this.kdzdbh = kdzdbh;
		this.payableamt = payableamt;
		this.pqlx = pqlx;
		this.yzm = yzm;
		this.jzid = jzid;
		this.buyprice = buyprice;
		this.wdrwid = wdrwid;
	}


	public Integer getWdrwid() {
		return wdrwid;
	}

	public void setWdrwid(Integer wdrwid) {
		this.wdrwid = wdrwid;
	}

	public BigDecimal getBuyprice() {
		return buyprice;
	}

	public void setBuyprice(BigDecimal buyprice) {
		this.buyprice = buyprice;
	}


	public Integer getYhfsid() {
		return yhfsid;
	}

	public void setYhfsid(Integer yhfsid) {
		this.yhfsid = yhfsid;
	}

	public Integer getClmxid() {
		return clmxid;
	}

	public void setClmxid(Integer clmxid) {
		this.clmxid = clmxid;
	}

	public String getKdzdbh() {
		return kdzdbh;
	}

	public void setKdzdbh(String kdzdbh) {
		this.kdzdbh = kdzdbh;
	}

	public BigDecimal getPayableamt() {
		return payableamt;
	}

	public void setPayableamt(BigDecimal payableamt) {
		this.payableamt = payableamt;
	}

	public String getPqlx() {
		return pqlx;
	}

	public void setPqlx(String pqlx) {
		this.pqlx = pqlx;
	}

	public String getYzm() {
		return yzm;
	}

	public void setYzm(String yzm) {
		this.yzm = yzm;
	}

	public Integer getJzid() {
		return jzid;
	}

	public void setJzid(Integer jzid) {
		this.jzid = jzid;
	}


	public Integer getCmid() {
		return cmid;
	}


	public void setCmid(Integer cmid) {
		this.cmid = cmid;
	}

	public Integer getOptype() {
		return optype;
	}

	public void setOptype(Integer optype) {
		this.optype = optype;
	}

	public BigDecimal getSjje() {
		return sjje;
	}


	public void setSjje(BigDecimal sjje) {
		this.sjje = sjje;
	}
	
	
}
