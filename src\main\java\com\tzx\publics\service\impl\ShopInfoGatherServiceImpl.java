package com.tzx.publics.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.exceptions.UnirestException;
import com.tzx.publics.listener.InitDataListener;
import com.tzx.publics.service.IShopInfoGatherService;
import com.tzx.publics.util.NetUtil;
import com.tzx.publics.vo.ShopInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * ShopInfoGatherServiceImpl
 *
 * <AUTHOR>
 * @version 0.0.1
 * @since 2023-05-17
 */
//@Service
//@EnableScheduling
public class ShopInfoGatherServiceImpl implements IShopInfoGatherService {

    Logger logger = LoggerFactory.getLogger(ShopInfoGatherServiceImpl.class);

    @Override
    public ShopInfo gatherShopInfo() {
        ShopInfo shopInfo = new ShopInfo();
        shopInfo.setShopIp(NetUtil.getLocalHost());
        shopInfo.setOrganCode(InitDataListener.organVo.getJgbh());
        shopInfo.setOrganName(InitDataListener.organVo.getJgmc1());
        return shopInfo;
    }

    @Scheduled(initialDelay = 1 * 60 * 1000,fixedDelay = 60 * 60 * 1000)
    @Override
    public void uploadShopInfo() {
//        ShopInfo shopInfo = gatherShopInfo();
//        String url = InitDataListener.ggcsMap.get("URL_TZXINTERFACE") + UPLOAD_URI;
//        ObjectMapper objectMapper = new ObjectMapper();
//
//        try {
//            HttpResponse<String> response = Unirest.post(url)
//                    .header("Content-Type", "application/json")
//                    .body(objectMapper.writeValueAsString(shopInfo)).asString();
//            logger.info("上传店铺信息成功:" + response.getBody());
//        } catch (UnirestException e) {
//            logger.error("上传店铺信息失败", e);
//        } catch (JsonProcessingException e) {
//            logger.error("上传店铺信息失败", e);
//        }


    }


}
