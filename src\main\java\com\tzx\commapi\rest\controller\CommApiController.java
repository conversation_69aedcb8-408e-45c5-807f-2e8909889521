package com.tzx.commapi.rest.controller;
import com.tzx.commapi.common.CommApiException;
import com.tzx.commapi.rest.service.ICommApiService;
import com.tzx.commapi.rest.service.ICommSetApiService;
import com.tzx.commapi.rest.service.ISaleOutApiService;
import com.tzx.commapi.rest.service.IUseYhfsApiService;
import com.tzx.commapi.rest.vo.CommApiData;
import com.tzx.commapi.rest.vo.UseYhfsParam;
import com.tzx.publics.base.BaseController;
import com.tzx.publics.util.DateUtil;
import com.tzx.receiver.common.upload.UpLoadTaskList;

import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.UUID;

/**
 * Created by Zhouxh on 2020-01-14.
 */
@RestController
@RequestMapping("/commapi")
public class CommApiController extends BaseController implements InitializingBean, DisposableBean {
    private final static Logger LOGGER = LoggerFactory.getLogger(CommApiController.class);
    @Autowired
    private ISaleOutApiService saleOutApiService;
    @Autowired
    private ICommSetApiService commSetApiService;
    @Autowired
    private IUseYhfsApiService useYhfsApiService;
    @Autowired
    private ICommApiService commApiService;
    @Autowired
    private UpLoadTaskList upLoadTaskList;
    
    private JSONObject GetSaleJsonObject(String methodTag,CommApiData data,String json){
        try
        {
            LOGGER.info("request {},\t  原json={}", new Object[]{methodTag, json});
            data.setCode(0);
            return  JSONObject.fromObject(json);
        }
        catch (Exception e)
        {
            e.printStackTrace();
            data.setMsg("解析json异常,请求JSON格式错误");
            data.setCode(1);
            LOGGER.error(e.getMessage());
            return null;
        }
    }

    @RequestMapping(value = "/set", method = RequestMethod.POST)
    public String Set(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) {
        String  methodTag = "set";
        CommApiData data = new CommApiData();
        JSONObject dejsonobj = GetSaleJsonObject(methodTag, data, json);
        if(!data.getCode().equals(0)){
            return  JSONObject.fromObject(data).toString();
        }
        return  saleOutApiService.SaleOutSet(data, dejsonobj);
    }


    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public String Add(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) {
        String  methodTag = "add";
        CommApiData data = new CommApiData();
        JSONObject dejsonobj = GetSaleJsonObject(methodTag, data, json);
        if(!data.getCode().equals(0)){
            return  JSONObject.fromObject(data).toString();
        }
        return saleOutApiService.SaleOutAdd(data, dejsonobj);
    }

    @RequestMapping(value = "/dec", method = RequestMethod.POST)
    public String Dec(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) {
        String  methodTag = "dec";
        CommApiData data = new CommApiData();
        JSONObject dejsonobj = GetSaleJsonObject(methodTag, data, json);
        if(!data.getCode().equals(0)){
            return  JSONObject.fromObject(data).toString();
        }
        return saleOutApiService.SaleOutDec(data, dejsonobj);
    }

    @RequestMapping(value = "/setecosound", method = RequestMethod.POST)
    public String SetEcoSound(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) {
        String  methodTag = "setecosound";
        CommApiData data = new CommApiData();
        JSONObject dejsonobj = GetSaleJsonObject(methodTag, data, json);
        if(!data.getCode().equals(0)){
            return  JSONObject.fromObject(data).toString();
        }
        return commSetApiService.SetEcoSound(data, dejsonobj);
    }
    @RequestMapping(value = "/getecosound", method = RequestMethod.POST)
    public String GetEcoSound(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) {
        String  methodTag = "getecosound";
        CommApiData data = new CommApiData();
        JSONObject dejsonobj = GetSaleJsonObject(methodTag, data, json);
        if(!data.getCode().equals(0)){
            return  JSONObject.fromObject(data).toString();
        }
        return commSetApiService.GetEcoSound(data, dejsonobj);
    }
    @RequestMapping(value = "/testsome", method = RequestMethod.POST)
    public String TestSome(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) {
        String  methodTag = "TestSome";
        CommApiData data = new CommApiData();
        //模拟储值拆分测试
        UseYhfsParam useYhfsParam1 = new UseYhfsParam();
        useYhfsParam1.setBillid("990000002640");
        useYhfsParam1.setOpType(91002);
        useYhfsParam1.setYhfsId(-126);
        useYhfsParam1.setJzid(0);
        useYhfsParam1.setSkjh("99");//机号
        useYhfsParam1.setJgtxbh("002");//价格体系编号
        useYhfsParam1.setDisAmount(new BigDecimal(8));
        useYhfsParam1.setBbrq(DateUtil.parseDate("2020-05-20"));
        useYhfsParam1.setInnerCalMoney(true);//由接口内部使用均摊及账单金额重计算


        useYhfsApiService.CommUseYhfs(useYhfsParam1);


        //不计收入拆分测试
        UseYhfsParam useYhfsParam = new UseYhfsParam();
        useYhfsParam.setBillid("990000002640");
        useYhfsParam.setOpType(91003);
        useYhfsParam.setYhfsId(-100066);
        useYhfsParam.setFklsid(3289);
        useYhfsParam.setSkjh("99");//机号
        useYhfsParam.setJgtxbh("002");//价格体系编号
        useYhfsParam.setDisAmount(new BigDecimal(9));
        useYhfsParam.setBbrq(DateUtil.parseDate("2020-05-20"));
        useYhfsParam.setInnerCalMoney(true);//由接口内部使用均摊及账单金额重计算


        useYhfsApiService.CommUseYhfs(useYhfsParam);

        return "true";
    }
    
    @RequestMapping(value = "/memberRequest", method = RequestMethod.POST)
    public String memberRequest(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) {
    	CommApiData data = new CommApiData();
    	String uuid = UUID.randomUUID().toString();
    	LOGGER.info("request UUID={},\t json={}", new Object[] {uuid, json});
    	
    	JSONObject dejsonobj = GetSaleJsonObject("memberRequest", data, json);
        if(!data.getCode().equals(0)){
            return  JSONObject.fromObject(data).toString();
        }
        
    	commApiService.qmMemberRequest(data, dejsonobj);
    	LOGGER.info("response UUID={},\t json={}", new Object[] {uuid, JSONObject.fromObject(data).toString()});
    	return  JSONObject.fromObject(data).toString();
    }
    
    @RequestMapping(value = "/merchantRequest", method = RequestMethod.POST)
    public String merchantRequest(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) {
    	CommApiData data = new CommApiData();
    	String uuid = UUID.randomUUID().toString();
    	LOGGER.info("request UUID={},\t json={}", new Object[] {uuid, json});
    	
    	JSONObject dejsonobj = GetSaleJsonObject("merchantRequest", data, json);
        if(!data.getCode().equals(0)){
            return  JSONObject.fromObject(data).toString();
        }
        
    	commApiService.merchantRequest(data, dejsonobj);
    	LOGGER.info("response UUID={},\t json={}", new Object[] {uuid, JSONObject.fromObject(data).toString()});
    	return  JSONObject.fromObject(data).toString();
    }

    @RequestMapping(value = "/qiMaiOtherRequest", method = RequestMethod.POST)
    public String qiMaiOtherRequest(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) {
        CommApiData data = new CommApiData();
        String uuid = UUID.randomUUID().toString();
        LOGGER.info("request UUID={},\t json={}", new Object[] {uuid, json});

        JSONObject dejsonobj = GetSaleJsonObject("qiMaiOtherRequest", data, json);
        if(!data.getCode().equals(0)){
            return  JSONObject.fromObject(data).toString();
        }
        commApiService.qiMaiOtherRequest(data, dejsonobj);
        LOGGER.info("response UUID={},\t json={}", new Object[] {uuid, JSONObject.fromObject(data).toString()});
        return  JSONObject.fromObject(data).toString();
    }
    
    @RequestMapping(value = "/merchantRequestPost", method = RequestMethod.POST)
    public String merchantRequestPost(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) {
    	CommApiData data = new CommApiData();
    	String uuid = UUID.randomUUID().toString();
    	LOGGER.info("request UUID={},\t json={}", new Object[] {uuid, json});
    	
    	JSONObject dejsonobj = GetSaleJsonObject("merchantRequestPost", data, json);
        if(!data.getCode().equals(0)){
            return  JSONObject.fromObject(data).toString();
        }
        
    	commApiService.merchantRequestPost(data, dejsonobj);
    	LOGGER.info("response UUID={},\t json={}", new Object[] {uuid, JSONObject.fromObject(data).toString()});
    	return  JSONObject.fromObject(data).toString();
    }

    @RequestMapping(value = "/sendSmsPost", method = RequestMethod.POST)
    public String sendSmsPost(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) {
        CommApiData data = new CommApiData();
        String uuid = UUID.randomUUID().toString();
        LOGGER.info("request UUID={},\t json={}", new Object[] {uuid, json});

        JSONObject dejsonobj = GetSaleJsonObject("sendSmsPost", data, json);
        if(!data.getCode().equals(0)){
            return  JSONObject.fromObject(data).toString();
        }

        commApiService.sendSmsPost(data, dejsonobj);
        LOGGER.info("response UUID={},\t json={}", new Object[] {uuid, JSONObject.fromObject(data).toString()});
        return  JSONObject.fromObject(data).toString();
    }
    
	@RequestMapping(value = "/ecoRequestPost", method = RequestMethod.POST)
	public String ecoRequestPost(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) {
		CommApiData data = new CommApiData();
		String uuid = UUID.randomUUID().toString();
		LOGGER.info("request UUID={},\t json={}", new Object[] { uuid, json });

		JSONObject dejsonobj = GetSaleJsonObject("ecoRequestPost", data, json);
		if (!data.getCode().equals(0)) {
			return JSONObject.fromObject(data).toString();
		}

		commApiService.ecoRequestPost(data, dejsonobj);
		LOGGER.info("response UUID={},\t json={}", new Object[] { uuid, JSONObject.fromObject(data).toString() });
		return JSONObject.fromObject(data).toString();
	}
	
	@RequestMapping(value = "/udpMsgRequest", method = RequestMethod.POST)
    public String udpMsgRequest(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) {
    	CommApiData data = new CommApiData();
    	String uuid = UUID.randomUUID().toString();
    	LOGGER.info("request UUID={},\t json={}", new Object[] {uuid, json});
    	
    	JSONObject dejsonobj = GetSaleJsonObject("udpMsgRequest", data, json);
        if(!data.getCode().equals(0)){
            return  JSONObject.fromObject(data).toString();
        }
        String recvStr = dejsonobj.optString("recvStr", "");
		try {
			upLoadTaskList.addTask(recvStr);
		} catch (Exception e) {
			e.printStackTrace();
			data.setMsg("系统错误");
			data.setCode(1);
			LOGGER.error(e.getMessage());
		}
    	LOGGER.info("response UUID={},\t json={}", new Object[] {uuid, JSONObject.fromObject(data).toString()});
    	return  JSONObject.fromObject(data).toString();
    }

    @RequestMapping(value = "/getWyList", method = RequestMethod.POST)
    public String getWyList(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) {
        CommApiData data = new CommApiData();
        String uuid = UUID.randomUUID().toString();
        LOGGER.info("request UUID={},\t json={}", new Object[] { uuid, json });

        JSONObject dejsonobj = GetSaleJsonObject("getWyList", data, json);
        if (!data.getCode().equals(0)) {
            return JSONObject.fromObject(data).toString();
        }
        commApiService.getWyList(data, dejsonobj);
        LOGGER.info("response UUID={},\t json={}", new Object[] { uuid, JSONObject.fromObject(data).toString() });
        return JSONObject.fromObject(data).toString();
    }

    @RequestMapping(value = "/repetitionSendWy", method = RequestMethod.POST)
    public String repetitionSendWy(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) {
        CommApiData data = new CommApiData();
        String uuid = UUID.randomUUID().toString();
        LOGGER.info("request UUID={},\t json={}", new Object[] { uuid, json });

        try {
            JSONObject dejsonobj = GetSaleJsonObject("repetitionSendWy", data, json);
            if (!data.getCode().equals(0)) {
                return JSONObject.fromObject(data).toString();
            }
            commApiService.repetitionSendWy(data, dejsonobj);
        }  catch (CommApiException cae) {
            cae.printStackTrace();
            LOGGER.error("Ignore this CommApiException", cae.getMessage());
            data.setCode(1);
            data.setMsg(cae.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            LOGGER.error("Ignore this Exception", e);
            data.setCode(1);
            data.setMsg("系统错误:" + e);
        }

        LOGGER.info("response UUID={},\t json={}", new Object[] { uuid, JSONObject.fromObject(data).toString() });
        return JSONObject.fromObject(data).toString();
    }

    @RequestMapping(value = "/heartbeat", method = RequestMethod.GET)
    public String heartbeat(HttpServletRequest request, HttpServletResponse response) {
        LOGGER.info("request(heartbeat)：----心跳检测！");
        //LOGGER.info("response(heartbeat)：----心跳检测结束！");
        return "心跳返回";
    }

    @RequestMapping(value = "/tzxShopPullData/businessData", method = RequestMethod.POST)
    public String businessData(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) {
        CommApiData data = new CommApiData();
        String uuid = UUID.randomUUID().toString();
        LOGGER.info("request(businessData) UUID={},\t json={}", new Object[] {uuid, json});

        JSONObject dejsonobj = GetSaleJsonObject("businessData", data, json);
        if(!data.getCode().equals(0)){
            return  JSONObject.fromObject(data).toString();
        }

        commApiService.businessData(data, dejsonobj);
        LOGGER.info("response(businessData) UUID={},\t json={}", new Object[] {uuid, JSONObject.fromObject(data).toString()});
        return  JSONObject.fromObject(data).toString();
    }

    @RequestMapping(value = "/getEcoOrderBack", method = RequestMethod.POST)
    public String getEcoOrderBack(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) {
        CommApiData data = new CommApiData();
        String uuid = UUID.randomUUID().toString();
        LOGGER.info("request(getEcoOrderBack) UUID={},\t json={}", new Object[] {uuid, json});

        JSONObject dejsonobj = GetSaleJsonObject("getEcoOrderBack", data, json);
        if(!data.getCode().equals(0)){
            return  JSONObject.fromObject(data).toString();
        }

        commApiService.getEcoOrderBack(data, dejsonobj);
        LOGGER.info("response(getEcoOrderBack) UUID={},\t json={}", new Object[] {uuid, JSONObject.fromObject(data).toString()});
        return  JSONObject.fromObject(data).toString();
    }

    @Override
    public void destroy() throws Exception {

    }

    @Override
    public void afterPropertiesSet() throws Exception {

    }
}
