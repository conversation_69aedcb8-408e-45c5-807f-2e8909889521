package com.tzx.miniapp.rest.vo;

import javax.persistence.Entity;
import java.io.Serializable;

@Entity
public class Shops implements Serializable {

	private int sid;
	private int bid;
	private String shopname;
	private String shopadd;
	private String shoplocation;
	private String lng;
	private String lat;
	private int ordermode;
	private int is_bind_user;
	private String shopcode;

	public int getSid() {
		return sid;
	}

	public void setSid(int sid) {
		this.sid = sid;
	}

	public int getBid() {
		return bid;
	}

	public void setBid(int bid) {
		this.bid = bid;
	}

	public String getShopname() {
		return shopname;
	}

	public void setShopname(String shopname) {
		this.shopname = shopname;
	}

	public String getShopadd() {
		return shopadd;
	}

	public void setShopadd(String shopadd) {
		this.shopadd = shopadd;
	}

	public String getShoplocation() {
		return shoplocation;
	}

	public void setShoplocation(String shoplocation) {
		this.shoplocation = shoplocation;
	}

	public String getLng() {
		return lng;
	}

	public void setLng(String lng) {
		this.lng = lng;
	}

	public String getLat() {
		return lat;
	}

	public void setLat(String lat) {
		this.lat = lat;
	}

	public int getOrdermode() {
		return ordermode;
	}

	public void setOrdermode(int ordermode) {
		this.ordermode = ordermode;
	}

	public int getIs_bind_user() {
		return is_bind_user;
	}

	public void setIs_bind_user(int is_bind_user) {
		this.is_bind_user = is_bind_user;
	}

	public String getShopcode() {
		return shopcode;
	}

	public void setShopcode(String shopcode) {
		this.shopcode = shopcode;
	}

}
