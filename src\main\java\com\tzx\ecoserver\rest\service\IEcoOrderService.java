package com.tzx.ecoserver.rest.service;

import com.tzx.publics.common.BillNoData;
import com.tzx.ecoserver.common.EcoRepData;

import net.sf.json.JSONObject;

import javax.servlet.http.HttpServletRequest;

import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019-10-26.
 */
public interface IEcoOrderService {
    public EcoRepData orderPrecheck(JSONObject orderData);
    public EcoRepData printOrder(JSONObject orderData);
    public EcoRepData orderback(JSONObject orderData);
    public EcoRepData ordernum(HttpServletRequest request);
    public EcoRepData orderPrecheckBefore(EcoRepData data,Map<String, String> inparam,JSONObject orderData);
    public EcoRepData orderPrecheckProcess(BillNoData billNoData,EcoRepData data, Map<String, String> inparam, JSONObject orderData);
//    public BillNoData getBillNoData();
    public EcoRepData orderbackBefore(EcoRepData data,Map<String, String> inparam,JSONObject orderData);
    public EcoRepData orderbackProcess(BillNoData billNoData,EcoRepData data, Map<String, String> inparam, JSONObject orderData,BillNoData partBillNoData);
    public EcoRepData sendover(EcoRepData data,Map<String, String> inparam,JSONObject orderData);

    public EcoRepData printCommon(EcoRepData data,Map<String, String> inparam);
    public void sendUdpMsg(String action,String partMsgBody);
    public void sendShowMsg(String  msg);
    
    // 2020-09-15，杨文彦，增加异常订单处理接口
    public boolean checkErrorOrderStatus(String outOrderIdInDB, EcoRepData data) ;
    public EcoRepData orderErrorPrecheckProcess(EcoRepData data, Map<String, String> inparam, JSONObject orderData);
    public EcoRepData printErrorBill(EcoRepData data,Map<String, String> inparam);
    public EcoRepData orderbackErrorBefore(EcoRepData data,Map<String, String> inparam,JSONObject orderData);
    public EcoRepData orderErrorbackProcess(EcoRepData data, Map<String, String> inparam, JSONObject orderData);
    public EcoRepData sendKvsData(EcoRepData data,Map<String, String> inparam);
    public EcoRepData deleteKvsData(EcoRepData data,Map<String, String> inparam);
    public void execPrintSingle(Map<String, String> inparam);
    int saveTcSelectMx(JSONObject orderData,String yddh);

}
