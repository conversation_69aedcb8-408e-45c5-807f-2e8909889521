package com.tzx.ecoserver.common;
import com.tzx.publics.util.DatagramUtil;
import com.tzx.receiver.common.upload.UploadGloVar;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;

/**
 * Created by <PERSON>x<PERSON> on 2019-11-08.
 */

public class RunONProcessor implements Runnable {

    private final static Logger LOGGER = LoggerFactory.getLogger(RunONProcessor.class);
    HashMap<String, String> data = new HashMap<String, String>();

    public RunONProcessor(HashMap<String, String> data, PlayNewOrderThread playNewOrderThread) {
        this.data = data;
        if(!playNewOrderThread.isstart){
            playNewOrderThread.isstart = true;
            playNewOrderThread.initPath();
            playNewOrderThread.start();
        }
        if(null!=data.get("test")&&data.get("test").equals("1")){
            int val = NumberUtils.toInt(data.get("soundVoice"),50);
            val = Math.round(val * 86 / 100 - 80) ;
            if(val > 6){
                val = 6;
            }
            else if(val < -80)
            {
                val = -80;
            }
            playNewOrderThread.soundVoice = val;
            playNewOrderThread.istestvoice = true;
        }
        else {
            String msgBody = "ACTION=ordernum|FDJGXH=" + UploadGloVar.getOrganizeID() + "|YDDH=" + "";
            msgBody = msgBody + "|neworder1=" + data.get("neworder1");
            msgBody = msgBody + "|receiveorder1=" + data.get("receiveorder1");//制作中
            msgBody = msgBody + "|deliveryorder1=" + data.get("deliveryorder1");//配送中
            msgBody = msgBody + "|waitorder_hand1=" + data.get("waitorder_hand1");
            msgBody = msgBody + "|endorder1=" + data.get("endorder1");//配送完成
            msgBody = msgBody + "|quitorder1=" + data.get("quitorder1");//已退订单
            DatagramUtil.sendUdpMsg(msgBody, Integer.valueOf(Constant.ECO_BOH_PORT).intValue(), "127.0.0.1");
            Integer  neworder1 = NumberUtils.toInt(data.get("neworder1"),0);
            PlayWavUtil.addNewOrderNum(neworder1);
            playNewOrderThread.runpay = neworder1 > 0;

            Integer  waitorder1 = NumberUtils.toInt(data.get("waitorder1"),0);
            playNewOrderThread.runwaitorderback = waitorder1 > 0;

            Integer  waitorderHand1 = NumberUtils.toInt(data.get("waitorder_hand1"),0);
            playNewOrderThread.runwaitorderhandorderback = waitorderHand1 > 0;

            Integer  quitorder1 = NumberUtils.toInt(data.get("quitorder1"),0);
            Integer  hasquitorder1 = PlayWavUtil.getRefundNum();
            if(quitorder1 > hasquitorder1) {
                playNewOrderThread.runorderback = true;
            }
            PlayWavUtil.setRefundNum(quitorder1);
        }

    }
    @Override
    public void run() {

    }


}