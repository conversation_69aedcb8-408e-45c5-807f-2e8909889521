<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.mobilepos.rest.mapper.MobilePosClearDishMapper">
	<select id="findClearDish" resultType="com.tzx.mobilepos.rest.vo.ClearDish">
		select cl.xmid as item_id,cl.id as details_id,gq.gqsl as count, cl.xmbh as dishsno
		from ts_gqk gq, tq_clmxk cl where cl.xmid = gq.cmid
	</select>
</mapper>
