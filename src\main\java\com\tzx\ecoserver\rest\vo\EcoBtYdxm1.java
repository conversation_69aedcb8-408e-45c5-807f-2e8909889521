package com.tzx.ecoserver.rest.vo;

import javax.persistence.Entity;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Created by <PERSON>x<PERSON> on 2019-12-24.
 */
@Entity
public class EcoBtYdxm1 implements Serializable  {
    private String yddh;//预订单号
    private String xmbh;//项目编号
    private String xmmc;//项目名称
    private BigDecimal yl1;//退款金额
    private BigDecimal yl2;//项目数量
    private String yl3;//部分退款状态


    public String getYddh() {
        return yddh;
    }

    public void setYddh(String yddh) {
        this.yddh = yddh;
    }

    public String getXmbh() {
        return xmbh;
    }

    public void setXmbh(String xmbh) {
        this.xmbh = xmbh;
    }

    public String getXmmc() {
        return xmmc;
    }

    public void setXmmc(String xmmc) {
        this.xmmc = xmmc;
    }

    public BigDecimal getYl1() {
        return yl1;
    }

    public void setYl1(BigDecimal yl1) {
        this.yl1 = yl1;
    }

    public BigDecimal getYl2() {
        return yl2;
    }

    public void setYl2(BigDecimal yl2) {
        this.yl2 = yl2;
    }

    public String getYl3() {
        return yl3;
    }

    public void setYl3(String yl3) {
        this.yl3 = yl3;
    }
}
