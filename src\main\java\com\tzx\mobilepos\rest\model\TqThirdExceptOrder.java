package com.tzx.mobilepos.rest.model;

import com.tzx.publics.base.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2018-10-23
 */
@Table(name = "TQ_THIRD_EXCEPT_ORDER")
public class TqThirdExceptOrder extends BaseEntity implements Serializable {
	private static final long serialVersionUID = 1L;

//	@Column(name = "ID")
//	private int id;
	@Column(name = "BILLID")
	private String billid;
	@Column(name = "CREATETIME")
	private Date createtime;
	@Column(name = "UPDATETIME")
	private Date updatetime;
	@Column(name = "SCAN_CODE")
	private String scan_code;
	@Column(name = "PAYTYPEID")
	private String paytypeid;
	@Column(name = "PAYTIME")
	private String paytime;
	@Column(name = "ORDERNO")
	private String orderno;
	@Column(name = "REFUND_ORDERNO")
	private String refund_orderno;
	@Column(name = "PRODUCTCODE")
	private String productcode;
	@Column(name = "DYNAMICID_TYPE")
	private String dynamicid_type;
	@Column(name = "CASHAMOUNT")
	private double cashamount;
	@Column(name = "ORDERMC")
	private String ordermc;
	@Column(name = "DATANAME")
	private String dataname;
	@Column(name = "UPDATECOUNT")
	private int updatecount;
	@Column(name = "FIRST_PAY_STATUS")
	private String first_pay_status;
	@Column(name = "LAST_PAY_STATUS")
	private String last_pay_status;
	@Column(name = "PAY_STATUS")
	private String pay_status;
	@Column(name = "POSNO")
	private String posno;
	@Column(name = "PAYNAME")
	private String payname;
	@Column(name = "ERRCOUNT")
	private int errcount;

//	public int getId() {
//		return id;
//	}

//	public void setId(int id) {
//		this.id = id;
//	}

	public String getBillid() {
		return billid;
	}

	public void setBillid(String billid) {
		this.billid = billid;
	}

	public Date getCreatetime() {
		return createtime;
	}

	public void setCreatetime(Date createtime) {
		this.createtime = createtime;
	}

	public Date getUpdatetime() {
		return updatetime;
	}

	public void setUpdatetime(Date updatetime) {
		this.updatetime = updatetime;
	}

	public String getScan_code() {
		return scan_code;
	}

	public void setScan_code(String scan_code) {
		this.scan_code = scan_code;
	}

	public String getPaytypeid() {
		return paytypeid;
	}

	public void setPaytypeid(String paytypeid) {
		this.paytypeid = paytypeid;
	}

	public String getPaytime() {
		return paytime;
	}

	public void setPaytime(String paytime) {
		this.paytime = paytime;
	}

	public String getOrderno() {
		return orderno;
	}

	public void setOrderno(String orderno) {
		this.orderno = orderno;
	}

	public String getRefund_orderno() {
		return refund_orderno;
	}

	public void setRefund_orderno(String refund_orderno) {
		this.refund_orderno = refund_orderno;
	}

	public String getProductcode() {
		return productcode;
	}

	public void setProductcode(String productcode) {
		this.productcode = productcode;
	}

	public String getDynamicid_type() {
		return dynamicid_type;
	}

	public void setDynamicid_type(String dynamicid_type) {
		this.dynamicid_type = dynamicid_type;
	}

	public double getCashamount() {
		return cashamount;
	}

	public void setCashamount(double cashamount) {
		this.cashamount = cashamount;
	}

	public String getOrdermc() {
		return ordermc;
	}

	public void setOrdermc(String ordermc) {
		this.ordermc = ordermc;
	}

	public String getDataname() {
		return dataname;
	}

	public void setDataname(String dataname) {
		this.dataname = dataname;
	}

	public int getUpdatecount() {
		return updatecount;
	}

	public void setUpdatecount(int updatecount) {
		this.updatecount = updatecount;
	}

	public String getFirst_pay_status() {
		return first_pay_status;
	}

	public void setFirst_pay_status(String first_pay_status) {
		this.first_pay_status = first_pay_status;
	}

	public String getLast_pay_status() {
		return last_pay_status;
	}

	public void setLast_pay_status(String last_pay_status) {
		this.last_pay_status = last_pay_status;
	}

	public String getPay_status() {
		return pay_status;
	}

	public void setPay_status(String pay_status) {
		this.pay_status = pay_status;
	}

	public String getPosno() {
		return posno;
	}

	public void setPosno(String posno) {
		this.posno = posno;
	}

	public String getPayname() {
		return payname;
	}

	public void setPayname(String payname) {
		this.payname = payname;
	}

	public int getErrcount() {
		return errcount;
	}

	public void setErrcount(int errcount) {
		this.errcount = errcount;
	}

}
