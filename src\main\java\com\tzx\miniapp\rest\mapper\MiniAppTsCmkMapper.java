package com.tzx.miniapp.rest.mapper;

import com.tzx.publics.base.MyMapper;
import com.tzx.miniapp.rest.model.TsCmk;
import com.tzx.miniapp.rest.model.TsTcmxk;
import com.tzx.miniapp.rest.vo.Dish;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
*
* <AUTHOR>
* @since 2018-05-18
*/
public interface MiniAppTsCmkMapper extends MyMapper<TsCmk> {
	public List<Dish> findDishBasicData(@Param("clmxlb") String clmxlb);
	
	public List<TsTcmxk> findTcmxByXmid(@Param("xmid") int xmid);
	
}
