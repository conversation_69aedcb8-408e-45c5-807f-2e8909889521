<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.miniapp.rest.mapper.TqWdkMapper">
  <resultMap id="BaseResultMap" type="com.tzx.miniapp.rest.entity.TqWdk">
    <!--@mbg.generated-->
    <!--@Table tq_wdk-->
    <id column="rwid" jdbcType="INTEGER" property="rwid" />
    <id column="tmbj" jdbcType="TIMESTAMP" property="tmbj" />
    <id column="fsskjh" jdbcType="VARCHAR" property="fsskjh" />
    <result column="yrwid" jdbcType="INTEGER" property="yrwid" />
    <result column="kdzdbh" jdbcType="VARCHAR" property="kdzdbh" />
    <result column="jzzdbh" jdbcType="VARCHAR" property="jzzdbh" />
    <result column="clmxid" jdbcType="INTEGER" property="clmxid" />
    <result column="cmid" jdbcType="INTEGER" property="cmid" />
    <result column="cmbh" jdbcType="VARCHAR" property="cmbh" />
    <result column="cmmc1" jdbcType="VARCHAR" property="cmmc1" />
    <result column="cmmc2" jdbcType="VARCHAR" property="cmmc2" />
    <result column="dwbh" jdbcType="VARCHAR" property="dwbh" />
    <result column="cmggid" jdbcType="INTEGER" property="cmggid" />
    <result column="cmggbh" jdbcType="VARCHAR" property="cmggbh" />
    <result column="yzwbh" jdbcType="VARCHAR" property="yzwbh" />
    <result column="zwbh" jdbcType="VARCHAR" property="zwbh" />
    <result column="tcfs" jdbcType="VARCHAR" property="tcfs" />
    <result column="tcbl" jdbcType="DOUBLE" property="tcbl" />
    <result column="tcje" jdbcType="NUMERIC" property="tcje" />
    <result column="fzsl" jdbcType="NUMERIC" property="fzsl" />
    <result column="fzje" jdbcType="NUMERIC" property="fzje" />
    <result column="zdsj" jdbcType="INTEGER" property="zdsj" />
    <result column="xdh" jdbcType="VARCHAR" property="xdh" />
    <result column="xdhshry" jdbcType="VARCHAR" property="xdhshry" />
    <result column="fwyh" jdbcType="VARCHAR" property="fwyh" />
    <result column="cbdj" jdbcType="NUMERIC" property="cbdj" />
    <result column="cmdj" jdbcType="NUMERIC" property="cmdj" />
    <result column="cmsl" jdbcType="NUMERIC" property="cmsl" />
    <result column="cmje" jdbcType="NUMERIC" property="cmje" />
    <result column="sjje" jdbcType="NUMERIC" property="sjje" />
    <result column="yhje" jdbcType="NUMERIC" property="yhje" />
    <result column="dpzkje" jdbcType="NUMERIC" property="dpzkje" />
    <result column="zrje" jdbcType="NUMERIC" property="zrje" />
    <result column="zkzt" jdbcType="VARCHAR" property="zkzt" />
    <result column="xlzkzt" jdbcType="VARCHAR" property="xlzkzt" />
    <result column="zkl" jdbcType="INTEGER" property="zkl" />
    <result column="yhfsid" jdbcType="INTEGER" property="yhfsid" />
    <result column="yhfsbh" jdbcType="VARCHAR" property="yhfsbh" />
    <result column="yhfs" jdbcType="VARCHAR" property="yhfs" />
    <result column="xlid" jdbcType="INTEGER" property="xlid" />
    <result column="xlbh" jdbcType="VARCHAR" property="xlbh" />
    <result column="cmsx" jdbcType="VARCHAR" property="cmsx" />
    <result column="wdbz" jdbcType="VARCHAR" property="wdbz" />
    <result column="cdbj" jdbcType="VARCHAR" property="cdbj" />
    <result column="tszt" jdbcType="VARCHAR" property="tszt" />
    <result column="qczt" jdbcType="VARCHAR" property="qczt" />
    <result column="tcid" jdbcType="INTEGER" property="tcid" />
    <result column="tcdch" jdbcType="INTEGER" property="tcdch" />
    <result column="fzdch" jdbcType="INTEGER" property="fzdch" />
    <result column="tcsfgh" jdbcType="VARCHAR" property="tcsfgh" />
    <result column="dcxh" jdbcType="INTEGER" property="dcxh" />
    <result column="fsbbrq" jdbcType="TIMESTAMP" property="fsbbrq" />
    <result column="jzbbrq" jdbcType="TIMESTAMP" property="jzbbrq" />
    <result column="fsbcid" jdbcType="INTEGER" property="fsbcid" />
    <result column="jzbcid" jdbcType="INTEGER" property="jzbcid" />
    <result column="jzskjh" jdbcType="VARCHAR" property="jzskjh" />
    <result column="kwbz" jdbcType="VARCHAR" property="kwbz" />
    <result column="cpbh" jdbcType="VARCHAR" property="cpbh" />
    <result column="dcbz" jdbcType="VARCHAR" property="dcbz" />
    <result column="zwh" jdbcType="VARCHAR" property="zwh" />
    <result column="csbh" jdbcType="VARCHAR" property="csbh" />
    <result column="pqhm" jdbcType="VARCHAR" property="pqhm" />
    <result column="syyhfkfsid" jdbcType="INTEGER" property="syyhfkfsid" />
    <result column="scbj" jdbcType="INTEGER" property="scbj" />
    <result column="xsms" jdbcType="VARCHAR" property="xsms" />
    <result column="cjgqbj" jdbcType="INTEGER" property="cjgqbj" />
    <result column="cpbj" jdbcType="INTEGER" property="cpbj" />
    <result column="yhyybh" jdbcType="VARCHAR" property="yhyybh" />
    <result column="sfxsmx" jdbcType="VARCHAR" property="sfxsmx" />
    <result column="memo" jdbcType="VARCHAR" property="memo" />
    <result column="scqrbj" jdbcType="INTEGER" property="scqrbj" />
    <result column="jjcrwid" jdbcType="INTEGER" property="jjcrwid" />
    <result column="yongjje" jdbcType="NUMERIC" property="yongjje" />
    <result column="yongjzkl" jdbcType="INTEGER" property="yongjzkl" />
    <result column="ydxmid" jdbcType="INTEGER" property="ydxmid" />
    <result column="yhxh" jdbcType="INTEGER" property="yhxh" />
    <result column="ecodiscount_fee" jdbcType="NUMERIC" property="ecodiscountFee" />
    <result column="distype" jdbcType="INTEGER" property="distype" />
    <result column="packid" jdbcType="INTEGER" property="packid" />
    <result column="yhqjsje" jdbcType="NUMERIC" property="yhqjsje" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    rwid, tmbj, fsskjh, yrwid, kdzdbh, jzzdbh, clmxid, cmid, cmbh, cmmc1, cmmc2, dwbh, 
    cmggid, cmggbh, yzwbh, zwbh, tcfs, tcbl, tcje, fzsl, fzje, zdsj, xdh, xdhshry, fwyh, 
    cbdj, cmdj, cmsl, cmje, sjje, yhje, dpzkje, zrje, zkzt, xlzkzt, zkl, yhfsid, yhfsbh, 
    yhfs, xlid, xlbh, cmsx, wdbz, cdbj, tszt, qczt, tcid, tcdch, fzdch, tcsfgh, dcxh, 
    fsbbrq, jzbbrq, fsbcid, jzbcid, jzskjh, kwbz, cpbh, dcbz, zwh, csbh, pqhm, syyhfkfsid, 
    scbj, xsms, cjgqbj, cpbj, yhyybh, sfxsmx, memo, scqrbj, jjcrwid, yongjje, yongjzkl, 
    ydxmid, yhxh, ecodiscount_fee, distype, packid, yhqjsje
  </sql>
  <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from tq_wdk
    where kdzdbh = #{kdzdbh,jdbcType=INTEGER}
    and cmsx in ('CMSX_TC','CMSX_MX','CMSX_DP')
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.tzx.miniapp.rest.entity.TqWdk">
    <!--@mbg.generated-->
    update tq_wdk
    <set>
      <if test="yrwid != null">
        yrwid = #{yrwid,jdbcType=INTEGER},
      </if>
      <if test="kdzdbh != null">
        kdzdbh = #{kdzdbh,jdbcType=VARCHAR},
      </if>
      <if test="jzzdbh != null">
        jzzdbh = #{jzzdbh,jdbcType=VARCHAR},
      </if>
      <if test="clmxid != null">
        clmxid = #{clmxid,jdbcType=INTEGER},
      </if>
      <if test="cmid != null">
        cmid = #{cmid,jdbcType=INTEGER},
      </if>
      <if test="cmbh != null">
        cmbh = #{cmbh,jdbcType=VARCHAR},
      </if>
      <if test="cmmc1 != null">
        cmmc1 = #{cmmc1,jdbcType=VARCHAR},
      </if>
      <if test="cmmc2 != null">
        cmmc2 = #{cmmc2,jdbcType=VARCHAR},
      </if>
      <if test="dwbh != null">
        dwbh = #{dwbh,jdbcType=VARCHAR},
      </if>
      <if test="cmggid != null">
        cmggid = #{cmggid,jdbcType=INTEGER},
      </if>
      <if test="cmggbh != null">
        cmggbh = #{cmggbh,jdbcType=VARCHAR},
      </if>
      <if test="yzwbh != null">
        yzwbh = #{yzwbh,jdbcType=VARCHAR},
      </if>
      <if test="zwbh != null">
        zwbh = #{zwbh,jdbcType=VARCHAR},
      </if>
      <if test="tcfs != null">
        tcfs = #{tcfs,jdbcType=VARCHAR},
      </if>
      <if test="tcbl != null">
        tcbl = #{tcbl,jdbcType=DOUBLE},
      </if>
      <if test="tcje != null">
        tcje = #{tcje,jdbcType=NUMERIC},
      </if>
      <if test="fzsl != null">
        fzsl = #{fzsl,jdbcType=NUMERIC},
      </if>
      <if test="fzje != null">
        fzje = #{fzje,jdbcType=NUMERIC},
      </if>
      <if test="zdsj != null">
        zdsj = #{zdsj,jdbcType=INTEGER},
      </if>
      <if test="xdh != null">
        xdh = #{xdh,jdbcType=VARCHAR},
      </if>
      <if test="xdhshry != null">
        xdhshry = #{xdhshry,jdbcType=VARCHAR},
      </if>
      <if test="fwyh != null">
        fwyh = #{fwyh,jdbcType=VARCHAR},
      </if>
      <if test="cbdj != null">
        cbdj = #{cbdj,jdbcType=NUMERIC},
      </if>
      <if test="cmdj != null">
        cmdj = #{cmdj,jdbcType=NUMERIC},
      </if>
      <if test="cmsl != null">
        cmsl = #{cmsl,jdbcType=NUMERIC},
      </if>
      <if test="cmje != null">
        cmje = #{cmje,jdbcType=NUMERIC},
      </if>
      <if test="sjje != null">
        sjje = #{sjje,jdbcType=NUMERIC},
      </if>
      <if test="yhje != null">
        yhje = #{yhje,jdbcType=NUMERIC},
      </if>
      <if test="dpzkje != null">
        dpzkje = #{dpzkje,jdbcType=NUMERIC},
      </if>
      <if test="zrje != null">
        zrje = #{zrje,jdbcType=NUMERIC},
      </if>
      <if test="zkzt != null">
        zkzt = #{zkzt,jdbcType=VARCHAR},
      </if>
      <if test="xlzkzt != null">
        xlzkzt = #{xlzkzt,jdbcType=VARCHAR},
      </if>
      <if test="zkl != null">
        zkl = #{zkl,jdbcType=INTEGER},
      </if>
      <if test="yhfsid != null">
        yhfsid = #{yhfsid,jdbcType=INTEGER},
      </if>
      <if test="yhfsbh != null">
        yhfsbh = #{yhfsbh,jdbcType=VARCHAR},
      </if>
      <if test="yhfs != null">
        yhfs = #{yhfs,jdbcType=VARCHAR},
      </if>
      <if test="xlid != null">
        xlid = #{xlid,jdbcType=INTEGER},
      </if>
      <if test="xlbh != null">
        xlbh = #{xlbh,jdbcType=VARCHAR},
      </if>
      <if test="cmsx != null">
        cmsx = #{cmsx,jdbcType=VARCHAR},
      </if>
      <if test="wdbz != null">
        wdbz = #{wdbz,jdbcType=VARCHAR},
      </if>
      <if test="cdbj != null">
        cdbj = #{cdbj,jdbcType=VARCHAR},
      </if>
      <if test="tszt != null">
        tszt = #{tszt,jdbcType=VARCHAR},
      </if>
      <if test="qczt != null">
        qczt = #{qczt,jdbcType=VARCHAR},
      </if>
      <if test="tcid != null">
        tcid = #{tcid,jdbcType=INTEGER},
      </if>
      <if test="tcdch != null">
        tcdch = #{tcdch,jdbcType=INTEGER},
      </if>
      <if test="fzdch != null">
        fzdch = #{fzdch,jdbcType=INTEGER},
      </if>
      <if test="tcsfgh != null">
        tcsfgh = #{tcsfgh,jdbcType=VARCHAR},
      </if>
      <if test="dcxh != null">
        dcxh = #{dcxh,jdbcType=INTEGER},
      </if>
      <if test="fsbbrq != null">
        fsbbrq = #{fsbbrq,jdbcType=TIMESTAMP},
      </if>
      <if test="jzbbrq != null">
        jzbbrq = #{jzbbrq,jdbcType=TIMESTAMP},
      </if>
      <if test="fsbcid != null">
        fsbcid = #{fsbcid,jdbcType=INTEGER},
      </if>
      <if test="jzbcid != null">
        jzbcid = #{jzbcid,jdbcType=INTEGER},
      </if>
      <if test="jzskjh != null">
        jzskjh = #{jzskjh,jdbcType=VARCHAR},
      </if>
      <if test="kwbz != null">
        kwbz = #{kwbz,jdbcType=VARCHAR},
      </if>
      <if test="cpbh != null">
        cpbh = #{cpbh,jdbcType=VARCHAR},
      </if>
      <if test="dcbz != null">
        dcbz = #{dcbz,jdbcType=VARCHAR},
      </if>
      <if test="zwh != null">
        zwh = #{zwh,jdbcType=VARCHAR},
      </if>
      <if test="csbh != null">
        csbh = #{csbh,jdbcType=VARCHAR},
      </if>
      <if test="pqhm != null">
        pqhm = #{pqhm,jdbcType=VARCHAR},
      </if>
      <if test="syyhfkfsid != null">
        syyhfkfsid = #{syyhfkfsid,jdbcType=INTEGER},
      </if>
      <if test="scbj != null">
        scbj = #{scbj,jdbcType=INTEGER},
      </if>
      <if test="xsms != null">
        xsms = #{xsms,jdbcType=VARCHAR},
      </if>
      <if test="cjgqbj != null">
        cjgqbj = #{cjgqbj,jdbcType=INTEGER},
      </if>
      <if test="cpbj != null">
        cpbj = #{cpbj,jdbcType=INTEGER},
      </if>
      <if test="yhyybh != null">
        yhyybh = #{yhyybh,jdbcType=VARCHAR},
      </if>
      <if test="sfxsmx != null">
        sfxsmx = #{sfxsmx,jdbcType=VARCHAR},
      </if>
      <if test="memo != null">
        memo = #{memo,jdbcType=VARCHAR},
      </if>
      <if test="scqrbj != null">
        scqrbj = #{scqrbj,jdbcType=INTEGER},
      </if>
      <if test="jjcrwid != null">
        jjcrwid = #{jjcrwid,jdbcType=INTEGER},
      </if>
      <if test="yongjje != null">
        yongjje = #{yongjje,jdbcType=NUMERIC},
      </if>
      <if test="yongjzkl != null">
        yongjzkl = #{yongjzkl,jdbcType=INTEGER},
      </if>
      <if test="ydxmid != null">
        ydxmid = #{ydxmid,jdbcType=INTEGER},
      </if>
      <if test="yhxh != null">
        yhxh = #{yhxh,jdbcType=INTEGER},
      </if>
      <if test="ecodiscountFee != null">
        ecodiscount_fee = #{ecodiscountFee,jdbcType=NUMERIC},
      </if>
      <if test="distype != null">
        distype = #{distype,jdbcType=INTEGER},
      </if>
      <if test="packid != null">
        packid = #{packid,jdbcType=INTEGER},
      </if>
      <if test="yhqjsje != null">
        yhqjsje = #{yhqjsje,jdbcType=NUMERIC},
      </if>
    </set>
    where rwid = #{rwid,jdbcType=INTEGER}
    and kdzdbh = #{kdzdbh,jdbcType=VARCHAR}
  </update>
</mapper>