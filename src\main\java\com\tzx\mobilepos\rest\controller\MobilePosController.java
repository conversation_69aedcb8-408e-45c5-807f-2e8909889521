package com.tzx.mobilepos.rest.controller;

import com.google.gson.Gson;
import com.tzx.publics.annotations.RestMethod;
import com.tzx.publics.base.BaseController;
import com.tzx.publics.common.BillNoData;
import com.tzx.publics.listener.InitDataListener;
import com.tzx.commapi.common.CommApiException;
import com.tzx.mobilepos.common.*;
import com.tzx.mobilepos.rest.service.*;
import com.tzx.publics.util.ErrorCode;
import com.tzx.publics.util.GlobalLockGetDataUtil;
import com.tzx.publics.util.ParamUtil;
import com.tzx.publics.util.ReqDataUtil;
import com.tzx.publics.util.SystemException;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.lang.reflect.Method;
import java.util.Map;
import java.util.UUID;

@RestController
public class MobilePosController extends BaseController {
	private final static Logger LOGGER = LoggerFactory.getLogger(MobilePosController.class);

	@Autowired
	private IMobilePosRegisterService registerService;
	@Autowired
	private IMobilePosLoginService loginService;
	@Autowired
	private IMobilePosBasicDataService basicDataService;
	@Autowired
	private IMobilePosBillService billService;
	@Autowired
	private IMobilePosAcewillMemberService acewillMemberService;
	@Autowired
	private IMobilePosQimaiMemberService qimaiMemberService;
	@Autowired
	private GlobalLockGetDataUtil globalLockGetDataUtil;

	@RequestMapping(value = "/rest", method = RequestMethod.POST)
	public void rest(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) {
		response.setContentType("text/html;charset=utf-8");
		Data data = new Gson().fromJson(json, Data.class);
		Type type = data != null ? data.getType() : null;
		Oper oper = data != null ? data.getOper() : null;
		if (type != null && oper != null) {
			String uuid = UUID.randomUUID().toString();
			LOGGER.info("request UUID={},\t type={},\t json={}", new Object[] { uuid, type.name(), json });
			Method method = searchMethod(type.toString(), oper.toString());
			Data result = new Data();
			if (method != null) {
				try {
					method.invoke(this, new Object[] { response, data, result });
				} catch (SystemException e) {
					LOGGER.error("Ignore this exception", e);
					buildSysExceptionData(e, result, type.name());
				} catch (Exception e) {
					LOGGER.error("Ignore this exception", e);
					buildExceptionData(e, result, type.name());
				} finally {
					LOGGER.info("response UUID={},\t type={},\t json={}", new Object[] { uuid, type.name(), JSONObject.fromObject(result).toString() });
					responseResult(response, result);
				}
			} else {
				defaultMethod(response, data);
				LOGGER.info("json 中 type 查找不到 ：" + json);
			}
		} else {
			defaultMethod(response, data);
			LOGGER.info("报文中type或者oper查询不到 ：" + json);
		}
	}

	// 注册
	@RestMethod(type = "REGISTER", oper = "add")
	public void registerAdd(HttpServletResponse response, Data data, Data result) {
		try {
			registerService.register(data, result);
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			result.setSuccess(false);
			result.setCode(-1);
			result.setMsg(SysDictionary.SYSTEM_ERROR);
		}
	}

	// 登录
	@RestMethod(type = "LOGIN", oper = "check")
	public void login(HttpServletResponse response, Data data, Data result) {
		try {
			loginService.login(data, result);
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			result.setSuccess(false);
			result.setCode(-1);
			result.setMsg(SysDictionary.SYSTEM_ERROR);
		}
	}

	// 数据同步
	@RestMethod(type = "BASIC_DATA", oper = "check")
	public void basicData(HttpServletResponse response, Data data, Data result) {
		try {
			basicDataService.findBasicData(data, result);
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			result.setSuccess(false);
			result.setCode(-1);
			result.setMsg(SysDictionary.SYSTEM_ERROR);
		}
	}
		
	// 登出
	@RestMethod(type = "LOGOUT", oper = "check")
	public void logout(HttpServletResponse response, Data data, Data result) {
		try {
			loginService.logout(data, result);
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			result.setSuccess(false);
			result.setCode(-1);
			result.setMsg(SysDictionary.SYSTEM_ERROR);
		}
	}

	// 创建账单
	@RestMethod(type = "CREATE_BILL", oper = "check")
	public void createBill(HttpServletResponse response, Data data, Data result) {
		try {
			Map<String, Object> map = ReqDataUtil.getDataMap(data);
			String jtbh = ParamUtil.getStringValue(map, "jtbh", false, null);// 机台号
			BillNoData billNoData = globalLockGetDataUtil.getBillNoData(jtbh, "0");
			billService.createBill(data, result, billNoData);
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			result.setSuccess(false);
			result.setCode(-1);
			result.setMsg(SysDictionary.SYSTEM_ERROR + "：获取账单号失败；");
		}
	}

	// 下单
	@RestMethod(type = "ORDERING", oper = "add")
	public void ordering(HttpServletResponse response, Data data, Data result) throws Exception {
		try {
			billService.ordering(data, result);
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			result.setSuccess(false);
			result.setCode(-1);
			result.setMsg(SysDictionary.SYSTEM_ERROR);
		}
	}

	/**
	 * @Description: 查询优惠活动
	 * @param @param response
	 * @param @param data
	 * @param @param result
	 * @return void
	 * @throws 
	 * <AUTHOR>
	 * @email <EMAIL>
	 * @date 2018-10-17
	 */
	@RestMethod(type = "DISCOUNT_QUERY", oper = "find")
	public void queryDiscount(HttpServletResponse response, Data data, Data result) {
		try {
			billService.queryDiscount(data, result);
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			result.setSuccess(false);
			result.setCode(-1);
			result.setMsg(SysDictionary.SYSTEM_ERROR);
		}
	}

	// 优惠活动校验
	@RestMethod(type = "CHECK_DISCOUNT", oper = "check")
	public void checkDiscount(HttpServletResponse response, Data data, Data result) throws Exception {
		Map<String, Object> paramMap = ReqDataUtil.getDataMap(data);
		String number = ParamUtil.getStringValue(paramMap, "bill_num", false, null);// 账单编号
		Data dataB = billService.billSynchronized(number, 1);
		try {
			if (dataB.isSuccess()) {
				billService.checkDiscount(data, result);
			} else {
				result.setSuccess(false);
				result.setCode(-1);
				result.setMsg(dataB.getMsg());
			}
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			result.setSuccess(false);
			result.setCode(-1);
			result.setMsg(SysDictionary.SYSTEM_ERROR);
		} finally {
			if (dataB.isSuccess()) {
				billService.billSynchronized(number, 2);	
			}
		}
	}

	// 账单优惠
	@RestMethod(type = "BILL_DISCOUNT", oper = "check")
	public void billDiscount(HttpServletResponse response, Data data, Data result) throws Exception {
		Map<String, Object> paramMap = ReqDataUtil.getDataMap(data);
		String number = ParamUtil.getStringValue(paramMap, "bill_num", false, null);// 账单编号
		Data dataB = billService.billSynchronized(number, 1);
		try {
			if (dataB.isSuccess()) {
				billService.discountOrder(data, result);
			} else {
				result.setSuccess(false);
				result.setCode(-1);
				result.setMsg(dataB.getMsg());
			}
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			result.setSuccess(false);
			result.setCode(-1);
			result.setMsg(SysDictionary.SYSTEM_ERROR);
		} finally {
			if (dataB.isSuccess()) {
				billService.billSynchronized(number, 2);	
			}
		}
	}

	// 支付
	@RestMethod(type = "BILL_PAYMENT", oper = "check")
	public void billPayment(HttpServletResponse response, Data data, Data result) throws Exception {
		Map<String, Object> paramMap = ReqDataUtil.getDataMap(data);
		String number = ParamUtil.getStringValue(paramMap, "zdbh", false, null);// 账单编号
		Data dataB = billService.billSynchronized(number, 1);
		try {
			if (dataB.isSuccess()) {
				billService.payment(data, result);
			} else {
				result.setSuccess(false);
				result.setCode(-1);
				result.setMsg(dataB.getMsg());
			}
		}catch (CommApiException ce) {
			LOGGER.error("Ignore this exception", ce);
			result.setSuccess(false);
			result.setCode(-1);
			result.setMsg(ce.getMessage());
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			result.setSuccess(false);
			result.setCode(-1);
			result.setMsg(SysDictionary.SYSTEM_ERROR);
		} finally {
			if (dataB.isSuccess()) {
				billService.billSynchronized(number, 2);	
			}
		}
	}

	// 结账
	@RestMethod(type = "BILL_ACCOUNTS", oper = "check")
	public void billAccounts(HttpServletResponse response, Data data, Data result) throws Exception {
		try {
			billService.accountsOrder(data, result);
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			result.setSuccess(false);
			result.setCode(-1);
			result.setMsg(SysDictionary.SYSTEM_ERROR);
		}
	}

	// 打印
	@RestMethod(type = "POS_PRINT_JNA", oper = "print")
	public void PosPrintJna(HttpServletResponse response, Data data, Data result) {
		try {
			billService.posPrintJna(data, result);
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			result.setSuccess(false);
			result.setCode(-1);
			result.setMsg(SysDictionary.SYSTEM_ERROR);
		}
	}
	
	/**
	 * @Description: 退单
	 * @param @param response
	 * @param @param data
	 * @param @param result
	 * @return void
	 * @throws 
	 * <AUTHOR>
	 * @email <EMAIL>
	 * @date 2018-10-11
	 */
	@RestMethod(type = "REFUND_ORDER", oper = "refund")
	public void refundOrder(HttpServletResponse response, Data data, Data result) {
		try {
			billService.refundOrder(data, result);
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			result.setSuccess(false);
			result.setCode(-1);
			result.setMsg(SysDictionary.SYSTEM_ERROR);
		}
	}

	// 登录后设置班次
	@RestMethod(type = "LOGIN_SHIFT", oper = "update")
	public void updateLoginState(HttpServletResponse response, Data data, Data result) {
		try {
			loginService.updateLoginShiftState(data, result);
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			result.setSuccess(false);
			result.setCode(-1);
			result.setMsg(SysDictionary.SYSTEM_ERROR);
		}
	}

	// 校验交现金，班结信息
	@RestMethod(type = "CHECK_LIMIT", oper = "check")
	public void checkLimit(HttpServletResponse response, Data data, Data result) {
		try {
			billService.checkLimit(data, result);
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			result.setSuccess(false);
			result.setCode(-1);
			result.setMsg(SysDictionary.SYSTEM_ERROR);
		}
	}

	// 核单页面添加单品口味备注
	@RestMethod(type = "ADD_TASTE", oper = "add")
	public void addTaste(HttpServletResponse response, Data data, Data result) {
		try {
			billService.addTaste(data, result);
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			result.setSuccess(false);
			result.setCode(-1);
			result.setMsg(SysDictionary.SYSTEM_ERROR);
		}
	}

	/**
	 * 
	 * @Description: 修改密码
	 * @param @param response
	 * @param @param data
	 * @param @param result
	 * @return void
	 * @throws 
	 * <AUTHOR>
	 * @email <EMAIL>
	 * @date 2018-12-3
	 */
	@RestMethod(type = "UPDATE_PASSWORD", oper = "update")
	public void updatePassword(HttpServletResponse response, Data data, Data result) {
		try {
			loginService.updatePassword(data, result);
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			result.setSuccess(false);
			result.setCode(-1);
			result.setMsg(SysDictionary.SYSTEM_ERROR);
		}
	}

	/**
	 * 根据会员动态二维码/手机号/卡号/磁道 信息查询微生活会员信息
	 * 
	 * @param response
	 * @param data
	 * @param result
	 */
	@RestMethod(type = "FIND_ACEWILL_MEMBER", oper = "find")
	public void findAcewillMember(HttpServletResponse response, Data data, Data result) {
		try {
			if ("1".equals(InitDataListener.ggcsMap.get("POS_MEMBER_TYPE"))) {
				acewillMemberService.findAcewillMember(data, result);
			}
			if ("4".equals(InitDataListener.ggcsMap.get("POS_MEMBER_TYPE"))) {
				qimaiMemberService.findQimaiMember(data, result);
			}
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			result.setSuccess(false);
			result.setCode(-1);
			result.setMsg(SysDictionary.SYSTEM_ERROR);
		}
	}

	/**
	 * 根据账单号清除该账单关联的会员信息
	 * 
	 * @param param
	 * @param result
	 */
	@RestMethod(type = "REFUND_ACEWILL_MEMBER", oper = "refund")
	public void refundAcewillMember(HttpServletResponse response, Data data, Data result) {
		try {
			if ("1".equals(InitDataListener.ggcsMap.get("POS_MEMBER_TYPE"))) {
				acewillMemberService.refundAcewillMember(data, result);
			}
			if ("4".equals(InitDataListener.ggcsMap.get("POS_MEMBER_TYPE"))) {
				qimaiMemberService.refundQimaiMember(data, result);
			}
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			result.setSuccess(false);
			result.setCode(-1);
			result.setMsg(SysDictionary.SYSTEM_ERROR);
		}
	}

	/**
	 * 根据账单号查询缓存中的微生活会员信息
	 * 
	 * @param response
	 * @param data
	 * @param result
	 */
	@RestMethod(type = "CACHE_ACEWILL_MEMBER", oper = "find")
	public void cacheAcewillMember(HttpServletResponse response, Data data, Data result) {
		try {
			if ("1".equals(InitDataListener.ggcsMap.get("POS_MEMBER_TYPE"))) {
				acewillMemberService.cacheAcewillMember(data, result);
			}
			if ("4".equals(InitDataListener.ggcsMap.get("POS_MEMBER_TYPE"))) {
				qimaiMemberService.cacheQimaiMember(data, result);
			}
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			result.setSuccess(false);
			result.setCode(-1);
			result.setMsg(SysDictionary.SYSTEM_ERROR);
		}
	}

	/**
	 * 微生活会员，预消费
	 * 
	 * @param response
	 * @param data
	 * @param result
	 */
	@RestMethod(type = "ACEWILL_MEMBER_PREVIEW", oper = "add")
	public void acewillMemberPreview(HttpServletResponse response, Data data, Data result) {
		try {
			if ("1".equals(InitDataListener.ggcsMap.get("POS_MEMBER_TYPE"))) {
				acewillMemberService.acewillPreview(data, result);
			}
			if ("4".equals(InitDataListener.ggcsMap.get("POS_MEMBER_TYPE"))) {
				qimaiMemberService.qimaiPreview(data, result);
			}
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			result.setSuccess(false);
			result.setCode(-1);
			result.setMsg(SysDictionary.SYSTEM_ERROR);
		}
	}

	/**
	 * 微生活会员，提交消费
	 * 
	 * @param response
	 * @param data
	 * @param result
	 */
	@RestMethod(type = "ACEWILL_MEMBER_COMMIT", oper = "add")
	public void acewillMemberCommit(HttpServletResponse response, Data data, Data result) {
		try {
			if ("1".equals(InitDataListener.ggcsMap.get("POS_MEMBER_TYPE"))) {
				acewillMemberService.acewillCommit(data, result);
			}
			if ("4".equals(InitDataListener.ggcsMap.get("POS_MEMBER_TYPE"))) {
				qimaiMemberService.qimaiCommit(data, result);
			}
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			result.setSuccess(false);
			result.setCode(-1);
			result.setMsg(SysDictionary.SYSTEM_ERROR);
		}
	}

	/**
	 * @Description: 查询订单
	 * @param @param
	 *            response
	 * @param @param
	 *            data
	 * @param @param
	 *            result
	 * @return void
	 * @throws <AUTHOR>             zhangzhibin
	 * @email <EMAIL>
	 * @date 2018-10-11
	 */
	@RestMethod(type = "BILL_QUERY", oper = "find")
	public void queryOrder(HttpServletResponse response, Data data, Data result) {
		try {
			billService.queryOrder(data, result);
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			result.setSuccess(false);
			result.setCode(-1);
			result.setMsg(SysDictionary.SYSTEM_ERROR);
		}
	}

	/**
	 * @Description: 查看详情
	 * @param @param response
	 * @param @param data
	 * @param @param result
	 * @return void
	 * @throws <AUTHOR>
	 * @email <EMAIL>
	 * @date 2018-10-15
	 */
	@RestMethod(type = "BILL_DETAIL_QUERY", oper = "find")
	public void queryBillDetail(HttpServletResponse response, Data data, Data result) {
		try {
			billService.queryBillDetail(data, result);
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			result.setSuccess(false);
			result.setCode(-1);
			result.setMsg(SysDictionary.SYSTEM_ERROR);
		}
	}
	
	/**
	 * 查询异常账单
	 * 
	 * @param response
	 * @param data
	 * @param result
	 */
	@RestMethod(type = "EXCEPTION_BILL_QUERY", oper = "find")
	public void queryExceptionBill(HttpServletResponse response, Data data, Data result) {
		try {
			billService.queryExceptionBill(data, result);
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			result.setSuccess(false);
			result.setCode(-1);
			result.setMsg(SysDictionary.SYSTEM_ERROR);
		}
	}
	
	/**
	 * 查询异常账单明细
	 * 
	 * @param response
	 * @param data
	 * @param result
	 */
	@RestMethod(type = "EXCEPTION_BILL_DETAIL_QUERY", oper = "find")
	public void queryExceptionBillDetail(HttpServletResponse response, Data data, Data result) {
		try {
			billService.queryBillDetail(data, result);
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			result.setSuccess(false);
			result.setCode(-1);
			result.setMsg(SysDictionary.SYSTEM_ERROR);
		}
	}
	
	@RestMethod(type = "EXCEPTION_BILL_HANDLER", oper = "check")
	public void exceptionBillHandler(HttpServletResponse response, Data data, Data result) {
		try {
			billService.exceptionBillHandler(data, result);
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
			result.setSuccess(false);
			result.setCode(-1);
			result.setMsg(SysDictionary.SYSTEM_ERROR);
		}
	}
	
	
	
	
	
	
	
	
	
	

	private void responseResult(HttpServletResponse response, Data result) {
		PrintWriter out = null;
		try {
			String responseStr = JSONObject.fromObject(result).toString();
			out = response.getWriter();
			out.print(responseStr);
			out.flush();
			out.close();
		} catch (Exception e) {
			LOGGER.error("Ignore this exception", e);
		} finally {
			if (out != null)
				out.close();
		}
	}

	private void buildSysExceptionData(SystemException se, Data result, String message) {
		ErrorCode error = se.getErrorCode();
		String msg = se.getMessage();
		result.setCode(error.getCode());
		result.setMsg(msg);
		LOGGER.error(message + ",原因：" + msg + ",错误码：" + error.getCode());
	}

	public void buildExceptionData(Exception e, Data result, String message) {
		result.setCode(Constant.CODE_INNER_EXCEPTION);
		result.setMsg("内部错误");
		e.printStackTrace();
	}

	private void defaultMethod(HttpServletResponse response, Data data) {
		Data result = new Data();
		result.setCode(-1);
		result.setMsg(Constant.NOT_EXISTS_TYPE);
		responseResult(response, result);
	}
}
