package com.tzx.receiver.common.upload;

import com.tzx.publics.util.DateUtil;
import com.tzx.publics.util.MD5Util;
import com.tzx.receiver.common.utils.DBUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.util.*;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 * @Date 2019-04-20
 * @Descption
 **/
@Component
@Lazy(true)
@SuppressWarnings("all")
public class UpLoadTaskList {
    @Autowired
    UpLoadParamsList upLoadParamsList;
    private final Logger logger	= LoggerFactory.getLogger(getClass());
    private ArrayList<UploadTask> tasks
            = new ArrayList<UploadTask>();
    private Set<String> hashset = new HashSet<String>(); //这个只是为了去重
    private Set<String> dbSet = new HashSet<String>();// 数据库记录md5 值

    public UpLoadTaskList() {
        super();
    }
    private ReentrantLock lock = new ReentrantLock();
    //直接添加到队列
    private void addTask(UploadTask task){
//        lock.lock();
        try{
            //如果同样的任务已经添加过，并且未执行完，那就调过
            if(hashset.contains((MD5Util.md5(task.toString())))) {
                if(!task.isOnlySendMQ()){
                    DBUtils.deleteUploadDBLog(task.getGuid());
                }
//                DBUtils.updateUploadDBLog("upprocess","100%",task.getGuid());
//                DBUtils.updateUploadDBLog("endtime",DateUtil.getNowDateYYDDMMHHMMSS(),task.getGuid());
//                DBUtils.updateUploadDBLog("runresult","1",task.getGuid());
//                DBUtils.updateUploadDBLog("upstate","完成",task.getGuid());
                return;
            }

            if(dbSet.contains(task.getGuid())){
                if(!task.isOnlySendMQ()){
                    DBUtils.deleteUploadDBLog(task.getGuid());
                }
                else{
                    //更新task
                    int index = -1;
                    for(int i =0;i< this.tasks.size() - 1 ;i++){
                        UploadTask  upLoadTask = this.tasks.get(i);
                        if(task.getGuid().equals(upLoadTask.getGuid())){
                            index = i;
                            break;
                        }
                    }
                    //找到对应对象 进行删除，更新
                    if(index > -1){
                        tasks.remove(index);
                        tasks.add(task);
                        logger.info("成功更新队列" + task.toString() + "，更新后队列长度:" + tasks.size());
                    }
                }
                return;
            }
            tasks.add(task);
            dbSet.add(task.getGuid());
            hashset.add(MD5Util.md5(task.toString()));
            logger.info("成功插入队列" + task.toString() + "，插入后队列长度:" + tasks.size());
        }finally {
//            lock.unlock();
        }


    }
    //由接收到的stirng解析完成后再添加到队列
    public void addTask(String task){
        lock.lock();
        try{
            logger.info("插入到任务队列:" + task);
            //去掉头和尾
            task = task.replace("{","");
            task = task.replace("}","");
            if(task.indexOf("extend")==-1){
                task = task.toUpperCase();
            }else{
                task = task.substring(0,task.indexOf("extend")).toUpperCase() +
                        task.substring(task.indexOf("extend"));
            }



            //分成到数据
            String[] arrays = task.split("\\|");
            Map<String,String> map = new HashMap<String,String>();
            String key,value;
            //数组转换到map
            for (String str : arrays) {
                String[] kv = str.split("=");
                if (kv.length==2){
                    key = kv[0];
                    value = kv[1];
                    map.put(key,value);
                }
            }
            String guid = null;
            //added by zhouxh 201909
            if(map.get("ONLYSENDMQ")==null||!map.get("ONLYSENDMQ").equals("1")?false:true) {
                guid = map.get("GUID").toLowerCase();
            }else{
                guid = DBUtils.insertUploadDBLog(task);
            }

            //开始复制参数
            String fdjgxh = map.get("FDJGXH");
            DBUtils.updateUploadDBLog("upstate","解析命令",guid);
            if(!StringUtils.isBlank(fdjgxh)&&!fdjgxh.equals(UploadGloVar.getOrganizeID())){
                logger.info("分店机构序号无效，已经忽略 。");
                DBUtils.updateUploadDBLog("exceptionmsg","分店机构序号无效，已经忽略 。",guid);
                DBUtils.updateUploadDBLog("upprocess","100%",guid);
                DBUtils.updateUploadDBLog("endtime",DateUtil.getNowDateYYDDMMHHMMSS(),guid);
                DBUtils.updateUploadDBLog("runresult","1",guid);
                DBUtils.updateUploadDBLog("upstate","完成",guid);
                return;
            }
            String[] bbrqs = map.get("BBRQ").split(",");
            for (String bbrq : bbrqs) {
                try {
                    DateUtils.parseDate(bbrq,"yyyy-MM-dd");
                } catch (ParseException e) {
                    logger.info("报表日期无效，已经忽略 %s 报表日期的上传消息。");
                    DBUtils.updateUploadDBLog("exceptionmsg","报表日期无效，已经忽略",guid);
                    DBUtils.updateUploadDBLog("upprocess","100%",guid);
                    DBUtils.updateUploadDBLog("endtime",DateUtil.getNowDateYYDDMMHHMMSS(),guid);
                    DBUtils.updateUploadDBLog("runresult","1",guid);
                    DBUtils.updateUploadDBLog("upstate","完成",guid);
                    return;
                }
            }
            UploadTask uploadTask = new UploadTask();
            uploadTask.setOrganizeCode(UploadGloVar.getOrganizeCode());
            uploadTask.setOrganizeName(UploadGloVar.getOrganizeName());
            uploadTask.setOpId(map.get("OPTBH")==null?"":map.get("OPTBH"));
            uploadTask.setShift(map.get("BBBC")==null?"":map.get("BBBC"));
            uploadTask.setOrganizeId(UploadGloVar.getOrganizeID());
            uploadTask.setReportDate(map.get("BBRQ")==null?"":map.get("BBRQ"));
            //其余的扩充参数格式规则为：zdbh[:]990209323[#]bbrq=2020-07-23
            if(!StringUtils.isBlank(map.get("extend"))){
                Map<String,String> extendPropertyMap = new HashMap<>();
                String extendProperty = map.get("extend");
                String[] extendAry = extendProperty.split("\\[#\\]");
                for (String extendStr : extendAry) {
                    String[] curExendStr = extendStr.split("\\[:\\]");
                    if(curExendStr.length==2){
                        String extendPName = curExendStr[0];
                        String extendPValue = curExendStr[1];
                        extendPropertyMap.put(extendPName,extendPValue);
                    }
                }
                uploadTask.setExtendProperty(extendPropertyMap);
            }
            uploadTask.setFileName("");
            uploadTask.setIsOnlySendMQ(map.get("ONLYSENDMQ")==null||!map.get("ONLYSENDMQ").equals("1")?false:true);
            if(uploadTask.getIsOnlySendMQ()){
                uploadTask.setFileName(map.get("FILENAME")==null?"":map.get("FILENAME"));
            }
            uploadTask.setState(UploadState.tsWait);
            //added by zhouxh  20190926 处理参数没有初始化的异常情况
            if(!upLoadParamsList.isHasDoneInit()){
                logger.info("命令所关联参数尚未初始化完毕");
                DBUtils.updateUploadDBLog("exceptionmsg","命令所关联参数尚未初始化完毕",guid);
                DBUtils.updateUploadDBLog("upprocess","25%",guid);
                DBUtils.updateUploadDBLog("endtime",DateUtil.getNowDateYYDDMMHHMMSS(),guid);
                DBUtils.updateUploadDBLog("runresult","-1",guid);
                DBUtils.updateUploadDBLog("upstate","等待上传",guid);
                return;
            }

            UploadParam param = upLoadParamsList.findUploadParamByCommand(map.get("ACTION")==null?"":map.get("ACTION"));
            if(param==null){
                logger.info("未匹配到命令所关联参数。");
                DBUtils.updateUploadDBLog("exceptionmsg","未匹配到命令所关联参数",guid);
                DBUtils.updateUploadDBLog("upprocess","100%",guid);
                DBUtils.updateUploadDBLog("endtime",DateUtil.getNowDateYYDDMMHHMMSS(),guid);
                DBUtils.updateUploadDBLog("runresult","1",guid);
                DBUtils.updateUploadDBLog("upstate","完成",guid);
                return;
            }
            uploadTask.setParam(param);
            uploadTask.setGuid(guid);
            DBUtils.updateUploadDBLog("command",param.getCommand(),guid);
            DBUtils.updateUploadDBLog("upstate","等待上传",guid);
            DBUtils.updateUploadDBLog("upprocess","25%",guid);
            addTask(uploadTask);
            DBUtils.writeReportTransProgres(uploadTask);

        }finally {
            lock.unlock();
        }
    }
    public UploadTask getHighPriorityTask(){
        lock.lock();
        try{
            //首先找到第一条没有上传过的
            for(int i =0;i< this.tasks.size() - 1 ;i++){
                UploadTask  upLoadTask = this.tasks.get(i);
                if (!upLoadTask.getIsOnlySendMQ()){
                    //如果找到了第一个不是重发MQ的，那么就直接返回就行了
                    return getTask(i);
                }

            }
            //如果没有找到，那么就返回第一个
            return  getTask(0);
        }finally {
            lock.unlock();
        }
    }
    public UploadTask getTask(int inx){
        lock.lock();
        try{
            UploadTask upLoadTask = tasks.get(inx);
            //取出即删除，不用管是否执行成功
            tasks.remove(inx);
            hashset.remove(MD5Util.md5(upLoadTask.toString()));
            dbSet.remove(upLoadTask.getGuid());
            return upLoadTask;

        }finally {
            lock.unlock();
        }
    }
    @Deprecated
    public UploadTask getTask(){
        return  getTask(0);
    }
    public int size(){
        return tasks.size();
    }
    public void clearTask(){
        lock.lock();
        try{
            tasks.clear();
            hashset.clear();
            dbSet.clear();;
        }finally {
            lock.unlock();
        }
    }
}
