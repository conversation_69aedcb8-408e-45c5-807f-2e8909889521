package com.tzx.receiver.common.utils;

import org.apache.commons.beanutils.converters.DateTimeConverter;

/**
 * 处理Date null 复制错误
 * 
 * <AUTHOR>
 * 
 */
public class DateConvert extends DateTimeConverter
{

	@Override
	public Object convert(Class type, Object value)
	{
		if (value == null)
		{
			return null;
		}
		return super.convert(type, value);
	}

	@Override
	protected Class getDefaultType()
	{
		return java.util.Date.class;
	}

}
