<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.mobilepos.rest.mapper.MobilePosTqYhMtCouponsMapper">
	
	<update id="updateYhMtCouponsMapper">
		update tq_yhmtcouponsorders set last_status = #{status} ,updatetime = #{datenow} ,status = #{status} ,pqlx = #{pqlx}
		<if test="payableamt != null and payableamt != ''">
			,payableamt = #{payableamt}
		</if>
		<if test="xmid != null and xmid != ''">
			,xmid = #{xmid }
		</if>
		<if test="isDone != null and isDone != ''">
			,isdone = #{isDone }
		</if>
		<if test="wdrwid != null and wdrwid != ''">
			,wdrwid = #{wdrwid}
		</if>
		<if test="sjje != null and sjje != ''">
			,sjje = #{sjje}
		</if>
		<if test="buyprice != null and buyprice != ''">
			,buyprice = #{buyprice}
		</if>
		where yzm = #{yzm} and kdzdbh = #{kdzdbh}
	</update>
	<update id="updateYhMtCouponsMapper1">
		update tq_yhmtcouponsorders set last_status = #{status} ,updatetime = #{datenow} ,status = #{status} ,pqlx = #{pqlx}
		<if test="payableamt != null and payableamt != ''">
			,payableamt = #{payableamt}
		</if>
		<if test="xmid != null and xmid != ''">
			,xmid = #{xmid }
		</if>
		<if test="isDone != null and isDone != ''">
			,isdone = #{isDone }
		</if>
		<if test="wdrwid != null and wdrwid != ''">
			,wdrwid = #{wdrwid}
		</if>
		<if test="sjje != null and sjje != ''">
			,sjje = #{sjje}
		</if>
		<if test="buyprice != null and buyprice != ''">
			,buyprice = #{buyprice}
		</if>
		where yzm = #{yzm} and kdzdbh = #{kdzdbh} and isdone = 'n' and status = '0'
	</update>
	
	<update id="updateYhMtCouponsTempMapper">
		update tq_yhmtcouponstemp  
		set last_status = #{status}
		    ,updatetime = #{datenow}
		    ,status = #{status}
		    ,pqlx = #{pqlx}
		    <if test="payableamt != null and payableamt != ''">
		        ,payableamt = #{payableamt}
		    </if>
		    <if test="xmid != null and xmid != ''">
		   		,xmid = #{xmid }
		    </if>
		    <if test="isDone != null and isDone != ''">
		   		,isdone = #{isDone }
		    </if>
		    <if test="wdrwid != null and wdrwid != ''">
				,wdrwid = #{wdrwid}
			</if>
			<if test="sjje != null and sjje != ''">
				,sjje = #{sjje}
			</if>
			<if test="buyprice != null and buyprice != ''">
				,buyprice = #{buyprice}
			</if>
		where yzm = #{yzm} and kdzdbh = #{kdzdbh}
	</update>
	
	
	<update id="updateYhMtCouponsByYzm">
		update tq_yhmtcouponsorders  
			set last_status = #{status}
				,status = #{status}
			where yzm = #{yzm}
	</update>
	
	<delete id="deleteYhMtCouponsMapper">
		delete from tq_yhmtcouponsorders where kdzdbh = #{kdzdbh} 
	 	<if test="yzm != null and yzm != ''">
			and yzm = #{yzm}
		</if>
	</delete>
	
	<select id="findMtYzm" resultType="String">
		select yzm from tq_yhmtcouponsorders where yhfsid=#{yhfsid} and status = #{status} and kdzdbh = #{zdbh} and payableamt = #{fkje} order by sjje
	</select>
	
	<update id="updateYhMtCouponsMapperToLogin">
		update tq_yhmtcouponsorders set last_status = #{status}, status = #{status}, isdone = #{isdone} where kdzdbh = #{zdbh} and status = '0'
	</update>
	
	<select id="findClid" resultType="Integer">
		select a.id from tq_clmxk a 
		left join tq_clsdk b on a.clid = b.id 
		left join tq_cbsdk c on b.cbid = c.id 
		left join ts_yhfssdk d on a.yhfsid = d.id 
		where c.yl1 = 'TS' and d.yhsx = #{yhsx} and d.id is not null
	</select>

	<delete id="deleteYhDyCouponsMapper">
		delete from tq_yhmtcouponsorders where kdzdbh = #{kdzdbh}
		<if test="outtradeno != null and outtradeno != ''">
			and outtradeno = #{outtradeno}
		</if>
	</delete>

	<update id="updateYhDyCouponsMapper">
		update tq_yhmtcouponsorders set last_status = #{status} ,updatetime = #{datenow} ,status = #{status} ,pqlx = #{pqlx}
		<if test="payableamt != null and payableamt != ''">
			,payableamt = #{payableamt}
		</if>
		<if test="xmid != null and xmid != ''">
			,xmid = #{xmid }
		</if>
		<if test="isDone != null and isDone != ''">
			,isdone = #{isDone }
		</if>
		<if test="wdrwid != null and wdrwid != ''">
			,wdrwid = #{wdrwid}
		</if>
		<if test="sjje != null and sjje != ''">
			,sjje = #{sjje}
		</if>
		<if test="buyprice != null and buyprice != ''">
			,buyprice = #{buyprice}
		</if>
		where yzm = #{yzm} and kdzdbh = #{kdzdbh} and outtradeno = #{outtradeno}
	</update>

	<update id="updateDyMtCouponsByYzm">
		update tq_yhmtcouponsorders
			set last_status = #{status}
				,status = #{status}
			where outtradeno = #{outtradeno}
	</update>
</mapper>
