package com.tzx.receiver.entity.msg;

import java.io.Serializable;
import java.util.Date;

public class Cpysmx
  implements Serializable
{
  private Integer id;
  private String mxbh;
  private Integer cpysid;
  private String mxlb;
  private Integer xmid;
  private String xmbh;
  private String xmmc1;
  private String xmmc2;
  private Integer yhfsid;
  private String yhfsbh;
  private Date ksrq;
  private Date jsrq;
  private String kssj;
  private String jssj;
  private Integer xssx;
  private String anys;
  private String bz;
  private String yl1;
  private String yl2;
  private String yl3;
  private Integer jgxh;
  private Double xmdj;
  private String xsms;
  private Integer yl4;
  private Double yl5;

  public Integer getId()
  {
    return this.id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public String getMxbh() {
    return this.mxbh;
  }

  public void setMxbh(String mxbh) {
    this.mxbh = mxbh;
  }

  public Integer getCpysid() {
    return this.cpysid;
  }

  public void setCpysid(Integer cpysid) {
    this.cpysid = cpysid;
  }

  public String getMxlb() {
    return this.mxlb;
  }

  public void setMxlb(String mxlb) {
    this.mxlb = mxlb;
  }

  public Integer getXmid() {
    return this.xmid;
  }

  public void setXmid(Integer xmid) {
    this.xmid = xmid;
  }

  public String getXmbh() {
    return this.xmbh;
  }

  public void setXmbh(String xmbh) {
    this.xmbh = xmbh;
  }

  public String getXmmc1() {
    return this.xmmc1;
  }

  public void setXmmc1(String xmmc1) {
    this.xmmc1 = xmmc1;
  }

  public String getXmmc2() {
    return this.xmmc2;
  }

  public void setXmmc2(String xmmc2) {
    this.xmmc2 = xmmc2;
  }

  public Integer getYhfsid() {
    return this.yhfsid;
  }

  public void setYhfsid(Integer yhfsid) {
    this.yhfsid = yhfsid;
  }

  public String getYhfsbh() {
    return this.yhfsbh;
  }

  public void setYhfsbh(String yhfsbh) {
    this.yhfsbh = yhfsbh;
  }

  public Date getKsrq() {
    return this.ksrq;
  }

  public void setKsrq(Date ksrq) {
    this.ksrq = ksrq;
  }

  public Date getJsrq() {
    return this.jsrq;
  }

  public void setJsrq(Date jsrq) {
    this.jsrq = jsrq;
  }

  public String getKssj() {
    return this.kssj;
  }

  public void setKssj(String kssj) {
    this.kssj = kssj;
  }

  public String getJssj() {
    return this.jssj;
  }

  public void setJssj(String jssj) {
    this.jssj = jssj;
  }

  public Integer getXssx() {
    return this.xssx;
  }

  public void setXssx(Integer xssx) {
    this.xssx = xssx;
  }

  public String getAnys() {
    return this.anys;
  }

  public void setAnys(String anys) {
    this.anys = anys;
  }

  public String getBz() {
    return this.bz;
  }

  public void setBz(String bz) {
    this.bz = bz;
  }

  public String getYl1() {
    return this.yl1;
  }

  public void setYl1(String yl1) {
    this.yl1 = yl1;
  }

  public String getYl2() {
    return this.yl2;
  }

  public void setYl2(String yl2) {
    this.yl2 = yl2;
  }

  public String getYl3() {
    return this.yl3;
  }

  public void setYl3(String yl3) {
    this.yl3 = yl3;
  }

  public Integer getJgxh() {
    return this.jgxh;
  }

  public void setJgxh(Integer jgxh) {
    this.jgxh = jgxh;
  }

  public Double getXmdj() {
    return this.xmdj;
  }

  public void setXmdj(Double xmdj) {
    this.xmdj = xmdj;
  }

  public String getXsms() {
    return this.xsms;
  }

  public void setXsms(String xsms) {
    this.xsms = xsms;
  }

  public Integer getYl4() {
    return this.yl4;
  }

  public void setYl4(Integer yl4) {
    this.yl4 = yl4;
  }

  public Double getYl5() {
    return this.yl5;
  }

  public void setYl5(Double yl5) {
    this.yl5 = yl5;
  }
}