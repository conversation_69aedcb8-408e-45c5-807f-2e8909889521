package com.tzx.miniapp.rest.mapper;

import java.util.Date;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.tzx.miniapp.rest.model.TqJtztk;
import com.tzx.miniapp.rest.vo.ShopStatus;
import com.tzx.publics.base.MyMapper;

/**
 *
 * <AUTHOR>
 * @since 2018-07-18
 */

public interface MiniAppShopStatusMapper extends MyMapper<ShopStatus> {

	public Map<String, String> findBbrq();

	public int checkOpenStart(@Param("bbrq") Date bbrq);

	public int checkOpenEnd(@Param("bbrq") Date bbrq);
	
	public int loginDlcs(@Param("bbrq") Date bbrq);
	
	public int loginTccs(@Param("bbrq") Date bbrq);
	
	public String getSys24yy();
	
	public String getDyxzsjd();

	public TqJtztk checkShopOpenStart(@Param("bbrq") Date bbrq);
	
	public TqJtztk checkShopClassesStart(@Param("bbrq") Date bbrq);
}