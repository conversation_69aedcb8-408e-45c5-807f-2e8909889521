<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzx.miniapp.rest.mapper.MiniAppTqZdkMapper">
	<select id="getBh" resultType="com.tzx.miniapp.rest.model.TsBmkzk" >
		SELECT * FROM TS_BMKZK WHERE BMC = #{bmc} and ZDMC = #{zdmc}
	</select>
	<select id="getZdCount" resultType="com.tzx.miniapp.rest.model.TqZdk" >
		select * from tq_zdk where ktskjh = #{ktskjh} and jzcs = 0
	</select>
	<update id="initZdk">
		update tq_zdk set yjje = '0',xmxjje = '0',zdje = '0',fkje = '0',zrje = '0', zkl = '100', zdzkl = null, yhfsid = null, yhfsbh = null, ksdcsj = null, ktbcid = #{ktbcid} where kdzdbh = #{kdzdbh}
	</update>
	<delete id="delWdk">
		delete from tq_wdk where kdzdbh = #{kdzdbh}
	</delete>
	<update id="updateBh">
		update TS_BMKZK set nr = #{nr} where BMC = #{bmc} and ZDMC = #{zdmc}
	</update>
	<select id="addTc" resultType="java.lang.Integer" >
		select * from P_ADDTC(#{szdbh}, #{aitemid}, #{ixmsl});
	</select>
	<select id="addCm" resultType="java.lang.Integer" >
		select * from P_AddCM (#{szdbh}, #{aitemid}, #{ixmsl}, #{sskjh}, #{sxsyh}, #{skwbh}, #{sggbh});
	</select>
	<select id="getYhsx" resultType="java.lang.String" >
		select yhsx from ts_yhfssdk where id = #{id} 
	</select>
	<select id="yhfstj" resultType="java.lang.Integer" >
		select * from p_yhfstj(#{iyhfsid}, #{szdbh}, #{isl}, #{syhsx});
	</select>
	<select id="addYhfs" resultType="java.lang.Integer" >
		select * from P_ADDYHFS(#{aitemid}, #{iyhfsid}, #{szdbh},#{sskjh},#{ixmsl});
	</select>
	
	<select id="findBillMoney" resultType="com.tzx.miniapp.rest.vo.BillMoney" >
		select kdzdbh, sum(cmje) as cope_with_money, sum(sjje) as actual_money ,
		sum(yhje) as discount_money from tq_wdk where kdzdbh = #{zdbh} and wdbz &lt;&gt; 'WDBZ_FS' 
		and wdbz &lt;&gt; 'WDBZ_MD' and wdbz &lt;&gt; 'WDBZ_SS' and wdbz &lt;&gt; 'WDBZ_TC' 
		and wdbz &lt;&gt; 'WDBZ_QX' and (cmsx isnull or cmsx &lt;&gt; 'CMSX_MX') 
		and yhfs = '' group by kdzdbh ;
	</select>
	<select id="findCalcMoney" resultType="com.tzx.miniapp.rest.vo.CalcMoney" >
		select * from p_calcmoney(#{zdbh});
	</select>
	<select id="accountsOrder" resultType="com.tzx.miniapp.rest.vo.AccountsOrder" >
		select * from p_payment(#{szdbh}, #{ijzid}, #{ifkje}, #{ifksl}, #{sfkhm}, #{ssfzhm}, #{slxdh}, #{sfkbz}, #{sskjh}, #{sskyh});
	</select>
	<update id="updateZdk">
		update tq_zdk set jzzdbh = #{jzzdbh}, jzbbrq = #{jzbbrq}, jzsj = #{jzsj}, jzcs = #{jzcs}, jzskjh = #{jzskjh}, jzczry = #{jzczry}, jzsx = #{jzsx}, ksjzsj = #{ksjzsj}, jzjssj = #{jzjssj}, jzbcid = #{jzbcid} where kdzdbh = #{kdzdbh} 
	</update>
	<update id="updateWdk">
		update tq_wdk set jzzdbh = #{jzzdbh}, jzbbrq = #{jzbbrq}, jzskjh = #{jzskjh}, jzbcid = #{jzbcid} where kdzdbh = #{kdzdbh} 
	</update>
	<update id="updateFklslsk">
		update tq_fklslsk set jzzdbh = #{jzzdbh}, jzbcid = #{jzbcid} where kdzdbh = #{kdzdbh} 
	</update>
	<select id="getFkfsid" resultType="java.lang.String" >
		select id from ts_fkfssdk where yl3 = #{yl3} 
	</select>
	<select id="getJg" resultType="com.tzx.miniapp.rest.model.TsPsjgsdk" >
		select jg.* from ts_psjgsdk jg ,(select sdnr from ts_ggcsk where sdbt = 'FDJGBH' and bzsm = '分店机构编号') gg where jg.jgbh = gg.sdnr
	</select>
	<select id="getWdDish" resultType="com.tzx.miniapp.rest.vo.WdDishVo" >
		select clmxid,cmmc1,cmsl,sjje from tq_wdk where kdzdbh = #{kdzdbh} and wdbz &lt;&gt; 'WDBZ_FS' 
		and wdbz &lt;&gt; 'WDBZ_MD' and wdbz &lt;&gt; 'WDBZ_SS' and wdbz &lt;&gt; 'WDBZ_TC' 
		and wdbz &lt;&gt; 'WDBZ_QX' and (cmsx isnull or cmsx &lt;&gt; 'CMSX_MX') and yhfs = '' 
	</select>
	<select id="getRifUrl" resultType="java.lang.String" >
		select sdnr from ts_ggcsk where sdbt = 'TZXMQWEBADD'
	</select>
	<select id="getDiscountR" resultType="com.tzx.miniapp.rest.vo.ItemVo" >
		select clmxid as item_id, cmmc1 as item_name, cmsl as item_count, cmje as item_price, 
		dcxh as item_serial, yhfsid as discount_id from tq_wdk where kdzdbh = #{kdzdbh} 
		and wdbz &lt;&gt; 'WDBZ_FS' and wdbz &lt;&gt; 'WDBZ_MD' and wdbz &lt;&gt; 'WDBZ_SS' and wdbz &lt;&gt; 'WDBZ_TC' 
		and wdbz &lt;&gt; 'WDBZ_QX' and (cmsx isnull or CMSX &lt;&gt; 'CMSX_MX') and yhfs &lt;&gt; ''
	</select>
	<select id="getItemR" resultType="com.tzx.miniapp.rest.vo.ItemVo" >
		select wd.clmxid as item_id, wd.cmmc1 as item_name, wd.cmsl as item_count, 
		wd.dwbh as item_unit_name, wd.cmdj as item_price, wd.cmje as amount_price, wd.dcxh as item_serial, 
		wd.kwbz as item_taste, wd.cmsx as is_combo from tq_wdk wd 
		where wd.kdzdbh = #{kdzdbh} and wd.wdbz &lt;&gt; 'WDBZ_FS' and wd.wdbz &lt;&gt; 'WDBZ_MD' 
		and wd.wdbz &lt;&gt; 'WDBZ_SS' and wd.wdbz &lt;&gt; 'WDBZ_TC' and wd.wdbz &lt;&gt; 'WDBZ_QX' 
		and (wd.cmsx isnull or wd.cmsx &lt;&gt; 'CMSX_MX') and wd.yhfs = ''
	</select>
	<select id="getComboDetailsR" resultType="com.tzx.miniapp.rest.vo.ComboDetails" >
		select clmxid as item_id, cmid as details_id, cmmc1 as details_name, cmje as price, 
		cmsl::bigint as item_count, dwbh as item_unit_name from tq_wdk where kdzdbh = #{kdzdbh} 
		and clmxid = #{clmxid} and cmsx = 'CMSX_MX'
	</select>
	<select id="getTasteNames" resultType="java.lang.String" >
		select array_to_string(array(select kwnr1 from ts_kwsdk where kwbh in (${kwbhs})),',')
	</select>
	<select id="getBcid" resultType="java.lang.Integer" >
<!-- 		select id from ts_bck where to_char(kssj, 'HH24:MI:SS') &lt; to_char(now(), 'HH24:MI:SS') and to_char(jssj, 'HH24:MI:SS') &gt; to_char(now(), 'HH24:MI:SS') -->
<!-- 		select id from ts_bck where bcbh = (select * from p_getbcbh(CAST(now() AS DATETIME))) -->
		select * from p_getbcmc(CAST(now() AS DATETIME))
	</select>
	<update id="updateKtczry">
		update tq_zdk set fwyh = #{ktczry}, ktczry = #{ktczry}, ygdlcs = #{ygdlcs} where kdzdbh = #{kdzdbh} 
	</update>
	
	<update id="updateZrje">
		update tq_zdk set zrje = #{zrje} where kdzdbh = #{kdzdbh}
	</update>
	
	<delete id="delZdkByYddh">
		delete from tq_zdk where yddh = #{yddh}
	</delete>
	
	<delete id="delWdkByYddh">
		delete from tq_wdk where kdzdbh in (select kdzdbh from tq_zdk where yddh = #{yddh})
	</delete>
	
	<delete id="delFklslskByYddh">
		delete from tq_fklslsk where kdzdbh in (select kdzdbh from tq_zdk where yddh = #{yddh})
	</delete>
	
</mapper>
