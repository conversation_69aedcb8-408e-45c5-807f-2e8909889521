package com.tzx.receiver.common.msg.xml;

import com.tzx.receiver.common.msg.datatype.DataTypeAttribute;
import com.tzx.receiver.common.msg.datatype.DataTypeUnit;
import com.tzx.receiver.common.msg.datatype.DataTypeUnit.DataType;

/**
 * 
 * 元数据字段类。
 * 
 * <AUTHOR>
 * 
 */
public class MetaDataField {

	private String fieldName;

	private DataType dataType;

	private DataTypeAttribute dataTypeAttribute;

	private boolean isPrimaryKey;

	private boolean isRequired;

	private boolean isUnique;

	/**
	 * 构造一个 MetaDataField 对象。<code>dataType</code>的默认值为<code>DataType.dtInteger</code>
	 * 
	 */
	public MetaDataField() {
		fieldName = "";
		setDataType(DataType.dtInteger);
		isPrimaryKey = false;
		isRequired = false;
		isUnique = false;
	}

	/**
	 * 获取数据类型。
	 * 
	 * @return
	 */
	public DataType getDataType() {
		return dataType;
	}

	/**
	 * 设置数据类型。同时设置新的数据类型属性对象。
	 * 
	 * @param dataType
	 *            写入值。
	 */
	public void setDataType(DataType dataType) {
		if (dataType != this.dataType) {
			dataTypeAttribute = null;
			Class attributeClass = null;
			attributeClass = DataTypeUnit.DataTypeAttributeClasses[dataType
					.ordinal()];
			if (attributeClass != null) {
				try {
					Object instance = attributeClass.newInstance();
					dataTypeAttribute = (DataTypeAttribute) instance;
				} catch (IllegalAccessException e) {
					e.printStackTrace();
				} catch (InstantiationException e) {
					e.printStackTrace();
				}

			}
			this.dataType = dataType;
		}

	}

	/**
	 * 获取字段名称。
	 * 
	 * @return 字段名称
	 */
	public String getFieldName() {
		return fieldName;
	}

	/**
	 * 设置字段名称。
	 * 
	 * @param fieldName
	 *            字段名称。
	 */
	public void setFieldName(String fieldName) {
		this.fieldName = fieldName;
	}

	/**
	 * 获取是否主键。
	 * 
	 * @return 是否主键
	 */
	public boolean getIsPrimaryKey() {
		return isPrimaryKey;
	}

	/**
	 * 设置是否主键。
	 * 
	 * @param isPrimaryKey
	 *            是否主键。
	 */
	public void setIsPrimaryKey(boolean isPrimaryKey) {
		this.isPrimaryKey = isPrimaryKey;
		if (this.isPrimaryKey) {
			this.isRequired = true;
		}
	}

	/**
	 * 获取是否必须。
	 * 
	 * @return 是否必须
	 */
	public boolean getIsRequired() {
		return isRequired;
	}

	/**
	 * 设置是否必须。
	 * 
	 * @param isRequired
	 *            是否必须。
	 */
	public void setIsRequired(boolean isRequired) {
		if (this.isPrimaryKey)
			return;
		this.isRequired = isRequired;
	}

	/**
	 * 获取是否唯一。
	 * 
	 * @return
	 */
	public boolean getIsUnique() {
		return isUnique;
	}

	/**
	 * 设置是否唯一。
	 * 
	 * @param isUnique
	 *            是否唯一。
	 */
	public void setIsUnique(boolean isUnique) {
		this.isUnique = isUnique;
	}

	/**
	 * 获取数据类型属性对象的实例。
	 * 
	 * @return 
	 */
	public DataTypeAttribute getDataTypeAttribute() {
		return dataTypeAttribute;
	}

	public String toString() {
		String format = "MetaDataField{fieldName='%s', dataType=%s, dataTypeAttribute=%s, isPrimaryKey=%s, isRequired=%s, isUnique=%s}";
		return String.format(format, fieldName, dataType, dataTypeAttribute,
				isPrimaryKey, isRequired, isUnique);
	}
}
