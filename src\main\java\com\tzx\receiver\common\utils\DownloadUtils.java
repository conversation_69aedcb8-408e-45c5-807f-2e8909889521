package com.tzx.receiver.common.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

public class DownloadUtils {

    private final static String savePath = "D\\";
    private static final Logger log = LoggerFactory.getLogger(DownloadUtils.class);

    /**
     * 下载文件
     */
    public static String downLoadFromUrl(String urlStr) {
        String xmlstr = "";
        try {
            //1下载文件
            URL url = new URL(urlStr);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            //设置超时间为3秒
            conn.setConnectTimeout(30 * 1000);
            //防止屏蔽程序抓取而返回403错误
            conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
            conn.setRequestProperty("lfwywxqyh_token", "v32Eo2Tw+qWI/eiKW3D8ye7l19mf1NngRLushO6CumLMHIO1aryun0/Y3N3YQCv/TqzaO/TFHw4=");

            //得到输入流
            InputStream inputStream = conn.getInputStream();
            //解压后的字节用于保存文件
            byte[] getData = null;
            //文件名称
            String fileName = "";
            //2解压文件
            ZipEntry zipEntry;
            ByteArrayInputStream bis = new ByteArrayInputStream(inputToByte(inputStream, inputStream.available()));
            ZipInputStream zip = new ZipInputStream(bis);
            while ((zipEntry = zip.getNextEntry()) != null) {
                byte[] buf = new byte[1024];
                int num = -1;
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                while ((num = zip.read(buf, 0, buf.length)) != -1) {
                    baos.write(buf, 0, num);
                }
                fileName = zipEntry.getName();
                getData = baos.toByteArray();
                xmlstr = new String(getData, "UTF-8");
                baos.flush();
                baos.close();
            }
            zip.close();
            bis.close();

            //3保存文件
//            File saveDir = new File(savePath);
//            if (!saveDir.exists()) {
//                saveDir.mkdir();
//            }
//            File file = new File(saveDir + File.separator + fileName);
//            FileOutputStream fos = new FileOutputStream(file);
//            fos.write(getData);
//            if (fos != null) {
//                fos.close();
//            }
//            if (inputStream != null) {
//                inputStream.close();
//            }
//            System.out.println("下载文件成功 " + file.getCanonicalPath());
        } catch (Exception ex) {
            log.error("下载文件失败",ex);
//            System.out.println("下载文件失败....");
//            ex.printStackTrace();
        }
        return xmlstr;
    }

    /**
     * 从输入流中获取字节数组
     */
    private static byte[] inputToByte(InputStream inStream, int fileSize) throws IOException {
        ByteArrayOutputStream swapStream = new ByteArrayOutputStream();
        byte[] buff = new byte[fileSize];
        int rc;
        while ((rc = inStream.read(buff, 0, fileSize)) > 0) {
            swapStream.write(buff, 0, rc);
        }
        return swapStream.toByteArray();
    }
}
