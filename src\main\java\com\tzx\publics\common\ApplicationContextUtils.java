package com.tzx.publics.common;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import org.springframework.beans.BeansException;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

@Component
@EnableAutoConfiguration(exclude = {DruidDataSourceAutoConfigure.class,DataSourceAutoConfiguration.class})
public class ApplicationContextUtils implements ApplicationContextAware {

	public static ApplicationContext applicationContext = null;// 可写成单利模式，这里为了方便

	@Override
	public void setApplicationContext(ApplicationContext arg0) throws BeansException {
		applicationContext = arg0;
		System.out.println("设置ApplicationContext成功！");
	}

}