package com.tzx.miniapp.rest.controller;

import com.tzx.miniapp.common.Data;
import com.tzx.miniapp.common.MiniAppData;
import com.tzx.miniapp.common.MiniAppLocker;
import com.tzx.miniapp.rest.service.IMiniAppCancelBillService;
import com.tzx.miniapp.rest.service.IMiniAppClearDishService;
import com.tzx.miniapp.rest.service.IMiniAppFirstPayService;
import com.tzx.miniapp.rest.service.IMiniAppNotification;
import com.tzx.miniapp.rest.service.IMiniAppOmpService;
import com.tzx.miniapp.rest.service.IMiniAppOrderPrecheckService;
import com.tzx.miniapp.rest.service.IMiniAppQmCancelBillService;
import com.tzx.miniapp.rest.service.IMiniAppShopBaseInfoService;
import com.tzx.miniapp.rest.service.IMiniAppShopStatusService;
import com.tzx.miniapp.rest.service.IMiniAppZsOrderPrecheck;
import com.tzx.miniapp.rest.service.IMiniAppZsSyncData;
import com.tzx.publics.base.BaseController;
import com.tzx.publics.common.BillNoData;
import com.tzx.publics.listener.InitDataListener;
import com.tzx.publics.service.IInitDataService;
import com.tzx.publics.util.GlobalLockGetDataUtil;
import com.tzx.receiver.common.utils.DBUtils;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.UUID;

/**
 *
 * <AUTHOR>
 * @2019-03-12，杨文彦，整理代码，添加注释，准备接手后续工作。
 * @2019-03-15，准备接入微生活优惠活动
 */
@RestController
@RequestMapping("/posapi")
public class MiniAppController extends BaseController {
	// 定义生成日志对象
	private final static Logger LOGGER = LoggerFactory.getLogger(MiniAppController.class);

	@Autowired
	// 基本数据同步接口，微生活插件调用
	private IMiniAppShopBaseInfoService shopBaseInfoService;

	@Autowired
	// 门店状态查询接口，微生活插件调用
	// 是否已经开店
	private IMiniAppShopStatusService shopStatusService;

	@Autowired
	// 菜品沽清查询接口，微生活插件调用
	private IMiniAppClearDishService clearDishService;
	
	@Autowired
	private IMiniAppZsSyncData zsSynchroData;
	
	@Autowired
	private IMiniAppZsOrderPrecheck zsOrderPrecheck;

	@Autowired
	// 预落单接口，微生活插件调用
	// 先付不会调用，后付才会调用
	private IMiniAppOrderPrecheckService orderPrecheckService;

	@Autowired
	// 清台接口，生成账单，微生活插件调用
	private IMiniAppFirstPayService firstPayService;

	@Autowired
	// 退单接口
	private IMiniAppCancelBillService cancelBillService;

	@Autowired
	// 通知接口，KVS调用
	// 向顾客手机发送取餐消息
	private IMiniAppNotification notification;

	@Autowired
	private IInitDataService initDataService;
	
	@Autowired
	private GlobalLockGetDataUtil globalLockGetDataUtil;
	
	@Autowired
	// 退单接口
	private IMiniAppQmCancelBillService qmCancelBillService;

    @Autowired
    private MiniAppLocker locker;
    @Autowired
    private IMiniAppOmpService miniAppOmpService;

    /**
	 * 基本数据同步接口，微生活插件调用
	 *
	 * @param request
	 * @param response
	 * @param json
	 * @return
	 */
	@RequestMapping(value = "/shop/shopBaseInfo", method = RequestMethod.POST)
	public String shopBaseInfo(HttpServletRequest request, HttpServletResponse response, @RequestBody(required = false) String json) {
		long startTime=System.currentTimeMillis();
		String uuid = UUID.randomUUID().toString();
		response.setContentType("text/html;charset=utf-8");
		LOGGER.info(uuid+"=>request(shopBaseInfo)：" + json);
		String data = shopBaseInfoService.shopBaseInfo();
		String logStr = data.length() > 1000 ? data.substring(0, 1000) : data;
		LOGGER.info(uuid+"=>response(shopBaseInfo)，耗时"+(System.currentTimeMillis()-startTime)+"ms:" + logStr);
		return data;
	}

	/**
	 * 门店状态查询接口，微生活插件调用
	 *
	 * @param request
	 * @param response
	 * @param json
	 * @return
	 */
	@RequestMapping(value = "/shop/shopStatus", method = RequestMethod.POST)
	public String shopStatus(HttpServletRequest request, HttpServletResponse response, @RequestBody(required = false) String json) {
		response.setContentType("text/html;charset=utf-8");
		LOGGER.debug("request(shopStatus)：----开业状态接口开始！");
		Data data = shopStatusService.shopStatus();
		LOGGER.debug("response(shopStatus)：" + JSONObject.fromObject(data).toString());
		return JSONObject.fromObject(data).toString();
	}

	/**
	 * 菜品沽清查询接口，微生活插件调用
	 *
	 * @param request
	 * @param response
	 * @param json
	 * @return
	 */
	@RequestMapping(value = "/shop/clearDish", method = RequestMethod.POST)
	public String clearDish(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) {
		response.setContentType("text/html;charset=utf-8");
		LOGGER.info("request(clearDish)：----沽清接口开始！");
		String data = clearDishService.clearDish();
		String logStr = data.length() > 1000 ? data.substring(0, 1000) : data;
		LOGGER.info("response(clearDish)：" + logStr);
		return data;
	}

	/**
	 * 预落单接口，微生活插件调用，先付不会调用，后付才会调用
	 *
	 * @param request
	 * @param response
	 * @param json
	 * @return
	 */
	@RequestMapping(value = "/shop/orderPrecheck", method = RequestMethod.POST)
	public String orderPrecheck(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) {
		response.setContentType("text/html;charset=utf-8");
		LOGGER.info("request(orderPrecheck)：" + json);
		Data data = orderPrecheckService.orderPrecheck(JSONObject.fromObject(json));
		LOGGER.info("response(orderPrecheck)：" + JSONObject.fromObject(data).toString());
		return JSONObject.fromObject(data).toString();
	}

	/**
	 * 清台接口，生成账单，微生活插件调用
	 *
	 * @param request
	 * @param response
	 * @param json
	 * @return
	 */
	@RequestMapping(value = "/shop/firstPay", method = RequestMethod.POST)
	public String firstPay(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) {
		response.setContentType("text/html;charset=utf-8");
		LOGGER.info("request(firstPay)：" + json);
		JSONObject o = JSONObject.fromObject(json);
        Data data = new Data();

        //读取参数，启用OMP对接
//        String enableOMP = DBUtils.getGGCSK("WlifeMiniEnableOMP");
        String miniAppVersion = DBUtils.getGGCSK("WlifeMiniAppVersion");

		boolean isOnLinePerferntial = o.optBoolean("isOnLinePerferntial", true); // 传入的小程序参数, 是否是营销版小程序, true: 是

		//默认为v1
		if(miniAppVersion.isEmpty() || !isOnLinePerferntial){ // 非营销本小程序, 走版本1
			miniAppVersion="1";
		}

		//v1版本 非营销版小程序
		if("1".equals(miniAppVersion)){
			data = firstPayService.getBillStatus(o, 1);
			if (data.getSuccess() == 1) {
				BillNoData billNoData = null;
				try {
					billNoData  = globalLockGetDataUtil.getBillNoData("99", "9");
				} catch (Exception e) {
					LOGGER.info("微生活小程序，账单号获取失败！");
					e.printStackTrace();
				}
				data = firstPayService.firstPay(o, billNoData);
				if (data.getSuccess() != 0) {
					// 结账清台成功
					firstPayService.sendPrintDetailed(o);
					// 打印账单
					firstPayService.sendPrint(o, "jzd");
					// 生成KVS数据
					firstPayService.sendKVS(o);
					// 生成厨打数据
					firstPayService.sendPrintKichen(o);
				} else {
					// 失败需要退单
					// TODO：退单存在风险
					// 什么情况之下才能退单，需要明确定义场景。
					// 曾经出现提交账单到第三方成功，然后查询账单支付结果，但是查不到此账单，所以出现异常退款问题。
					// 原因是提交成功账单的第三方还未处理，查询结果请求却先处理了。
					firstPayService.exceptionCancelBill(data);
				}
			}
			firstPayService.getBillStatus(o, 2);
		}


		//omp版本
		if("0".equals(miniAppVersion)) {
			String outOrderId = o.optString("out_order_id");
			MiniAppData dataL = new MiniAppData();
			try {
				dataL = locker.lockerByNum(outOrderId, 1);
				LOGGER.info("Type： {},TradeNo： {},locker.lockerByNum：{}", "orderFirstPay", outOrderId, dataL.isSuccess());
				if (dataL.isSuccess()) {
					data = miniAppOmpService.orderPrecheckBefore(o);
					if (data.getSuccess() == 1) {
						BillNoData billNoData = null;
						billNoData  = globalLockGetDataUtil.getBillNoData("99", "9");
						data = miniAppOmpService.orderPrecheckCode(o, billNoData);
//                        if (data.getSuccess() != 0 && data.getCode() != 2) {
						if (data.getSuccess() != 0) {
							if (data.getCode() != 2) {
								firstPayService.sendPrintDetailed(o);
								firstPayService.sendPrint(o, "jzd");
								firstPayService.sendKVS(o);
								firstPayService.sendPrintKichen(o);
							}
						} else {
							firstPayService.exceptionCancelBill(data);
						}
					}
				} else {
					data.setSuccess(0);
					data.setMsg("账单处理中，请勿重复提交:" + outOrderId);
					data.setData(new HashMap<String, Object>());
				}
			} catch (Exception e) {
				e.printStackTrace();
				LOGGER.error("Ignore this exception", e);
				data.setSuccess(0);
				data.setMsg("系统错误:" + e);
				data.setData(new HashMap<String, Object>());
			} finally {
				if (dataL.isSuccess()) {
					locker.lockerByNum(outOrderId, 2);
					LOGGER.info("Type： {}, TradeNo： {}, locker.lockerByNum 解锁成功", "orderFirstPayCode", outOrderId);
				}
			}


		}

		//v2版本 营销版小程序
        if("2".equals(miniAppVersion)) {
            String outOrderId = o.optString("out_order_id");
            MiniAppData dataL = new MiniAppData();
            try {
                dataL = locker.lockerByNum(outOrderId, 1);
                LOGGER.info("Type： {},TradeNo： {},locker.lockerByNum：{}", "orderFirstPay", outOrderId, dataL.isSuccess());
                if (dataL.isSuccess()) {
                    data = miniAppOmpService.orderPrecheckBefore(o);
                    if (data.getSuccess() == 1) {
                        BillNoData billNoData = null;
                        billNoData  = globalLockGetDataUtil.getBillNoData("99", "9");
                        data = miniAppOmpService.orderPrecheckCode(o, billNoData);
//                        if (data.getSuccess() != 0 && data.getCode() != 2) {
						if (data.getSuccess() != 0) {
                            if (data.getCode() != 2) {
                                firstPayService.sendPrintDetailed(o);
                                firstPayService.sendPrint(o, "jzd");
                                firstPayService.sendKVS(o);
                                firstPayService.sendPrintKichen(o);
                            }
                        } else {
							Data refundData = new Data();
							BeanUtils.copyProperties(data, refundData);
							firstPayService.exceptionCancelBill(refundData);
						}
                    }
                } else {
                    data.setSuccess(0);
                    data.setMsg("账单处理中，请勿重复提交:" + outOrderId);
                    data.setData(new HashMap<String, Object>());
                }
            } catch (Exception e) {
//                e.printStackTrace();
                LOGGER.error("微生活小程序接单异常", e);
				Data refundData = new Data();
				BeanUtils.copyProperties(data, refundData);
				firstPayService.exceptionCancelBill(refundData);
                data.setSuccess(0);
                data.setMsg("系统错误:" + e.getMessage());
                data.setData(new HashMap<String, Object>());
            } finally {
                if (dataL.isSuccess()) {
                    locker.lockerByNum(outOrderId, 2);
                    LOGGER.info("Type： {}, TradeNo： {}, locker.lockerByNum 解锁成功", "orderFirstPayCode", outOrderId);
                }
            }


        }


        String result=JSONObject.fromObject(data).toString();
        LOGGER.info("response(orderFirstPay)：" +result );
        return result;
	}

	/**
	 * 退单接口，微生活调用
	 *
	 * @param request
	 * @param response
	 * @param json
	 * @return
	 */
	@RequestMapping(value = "/shop/cancelBill", method = RequestMethod.POST)
	public String cancelBill(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) {
		response.setContentType("text/html;charset=utf-8");
		LOGGER.info("request(cancelBill)：" + json);
		Data data = cancelBillService.cancelBill(JSONObject.fromObject(json));
		// 打印退单账单，并且送厨打
		firstPayService.sendPrint(JSONObject.fromObject(json), "zdqx");
		LOGGER.info("response(cancelBill)：" + JSONObject.fromObject(data).toString());
		return JSONObject.fromObject(data).toString();
	}

	/**
	 * 退单接口，POS调用
	 *
	 * @param request
	 * @param response
	 * @param json
	 * @return
	 */
	@RequestMapping(value = "/shop/posCcancelBill", method = RequestMethod.POST)
	public String posCcancelBill(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) {
		response.setContentType("text/html;charset=utf-8");
		LOGGER.info("request(posCcancelBill)：" + json);
		Data data = new Data();
		data.setSuccess(1);
		data.setMsg("退单失败，没有配置对应小程序，请检查！");
		if ("1".equals(InitDataListener.ggcsMap.get("POS_MEMBER_TYPE"))) {
			// 微生活退单
			data = cancelBillService.posCancelBill(JSONObject.fromObject(json));
		}
		if ("3".equals(InitDataListener.ggcsMap.get("POS_MEMBER_TYPE"))) {
			// 众赏退单
			data = zsOrderPrecheck.posCancelBill(JSONObject.fromObject(json));
		}
		if ("4".equals(InitDataListener.ggcsMap.get("POS_MEMBER_TYPE"))) {
			// 企迈退单
			data = qmCancelBillService.posCancelBill(JSONObject.fromObject(json));
		}
		if ("5".equals(InitDataListener.ggcsMap.get("POS_MEMBER_TYPE"))) {
			if ("4".equals(InitDataListener.ggcsMap.get("POS_MINIAPP_TYPE"))) {
				// 企迈退单
				data = qmCancelBillService.posCancelBill(JSONObject.fromObject(json));
			}
			
		}
		// 打印退单账单 
//		if (data.getSuccess() == 0) {
//			JSONObject obj = JSONObject.fromObject(json);
//			obj.put("out_order_id", obj.optString("yddbh"));
//			firstPayService.sendPrint(obj, "zdqx");
//		}
		LOGGER.info("response(posCcancelBill)：" + JSONObject.fromObject(data).toString());
		return JSONObject.fromObject(data).toString();
	}

	/**
	 * 同步菜品通知，POS调用 (POS上点击同步信息按钮)
	 *
	 * @param request
	 * @param response
	 * @param json
	 * @return
	 */
	@RequestMapping(value = "/shop/synchrodataDish", method = RequestMethod.POST)
	public String synchrodataDish(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) {
		response.setContentType("text/html;charset=utf-8");
		LOGGER.info("request(synchrodataDish)：" + json);
//		InitDataListener.ggcsMap = initDataService.getGgcsMap();
		initDataService.refreshInternalStorage();
		Data data = new Data();
		if ("1".equals(InitDataListener.ggcsMap.get("POS_MEMBER_TYPE"))
			|| "3".equals(InitDataListener.ggcsMap.get("POS_MEMBER_TYPE"))) {
			// 更新缓存
			shopBaseInfoService.updateCache();
			// 微生活同步
			data = notification.synchrodataDish(JSONObject.fromObject(json));

		}
		if ("3".equals(InitDataListener.ggcsMap.get("POS_MEMBER_TYPE"))) {
			// 众赏同步
			data = zsSynchroData.synchrodata(JSONObject.fromObject(json));
		}
//		if ("4".equals(InitDataListener.ggcsMap.get("POS_MEMBER_TYPE"))) {
//			// 企迈同步
//			data = notification.qmSynchrodataDish(JSONObject.fromObject(json));
//		}
		LOGGER.info("response(synchrodataDish)：" + JSONObject.fromObject(data).toString());
		return JSONObject.fromObject(data).toString();
	}

	/**
	 * 向顾客手机发送取餐消息
	 *
	 * @param request
	 * @param response
	 * @param json
	 * @return
	 */
	@RequestMapping(value = "/shop/sendmealmsg", method = RequestMethod.POST)
	public String sendmealmsg(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) {
		response.setContentType("text/html;charset=utf-8");
		LOGGER.info("request(sendmealmsg)：" + json);
		Data data = notification.sendmealmsg(JSONObject.fromObject(json));
		LOGGER.info("response(sendmealmsg)：" + JSONObject.fromObject(data).toString());
		return JSONObject.fromObject(data).toString();
	}
	
	/**
	 * 给第三方推送沽清信息，POS调用
	 *
	 * @param request
	 * @param response
	 * @param json
	 * @return
	 */
	@RequestMapping(value = "/shop/posSoldOut", method = RequestMethod.POST)
	public String posSoldOut(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) {
		response.setContentType("text/html;charset=utf-8");
		LOGGER.info("request(posSoldOut)：" + json);
		Data data = new Data();
		if ("4".equals(InitDataListener.ggcsMap.get("POS_MEMBER_TYPE"))) {
			// 企迈沽清
			data = qmCancelBillService.posSoldOut(JSONObject.fromObject(json));
		}
		if ("1".equals(InitDataListener.ggcsMap.get("POS_MEMBER_TYPE"))) {
			// 重新初始化沽清信息缓存
			clearDishService.initClearDishCache();
		}
		LOGGER.info("response(posSoldOut)：" + JSONObject.fromObject(data).toString());
		return JSONObject.fromObject(data).toString();
	}

}


