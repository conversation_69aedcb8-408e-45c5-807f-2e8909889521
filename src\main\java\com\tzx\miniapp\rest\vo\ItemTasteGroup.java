package com.tzx.miniapp.rest.vo;

import java.util.List;

public class ItemTasteGroup {

	private int omkid;// 分组ID
	private String title;// 备注名称
	private int is_must;// 是否必选 1: 必选 2: 非必选
	private int is_radio;// 是否单选 1: 单选 2: 多选
	private int min;// 最小选量，必选是最小1 0 默认没限制
	private int max;// 最大选几个 默认是0 没限制， 大于等于min,最大选择量小于等于总数量
	private List<ItemTaste> items;// 口味备注列表

	public int getOmkid() {
		return omkid;
	}

	public void setOmkid(int omkid) {
		this.omkid = omkid;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public int getIs_must() {
		return is_must;
	}

	public void setIs_must(int is_must) {
		this.is_must = is_must;
	}

	public int getIs_radio() {
		return is_radio;
	}

	public void setIs_radio(int is_radio) {
		this.is_radio = is_radio;
	}

	public int getMin() {
		return min;
	}

	public void setMin(int min) {
		this.min = min;
	}

	public int getMax() {
		return max;
	}

	public void setMax(int max) {
		this.max = max;
	}

	public List<ItemTaste> getItems() {
		return items;
	}

	public void setItems(List<ItemTaste> items) {
		this.items = items;
	}

}
