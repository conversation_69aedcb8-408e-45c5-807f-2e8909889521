package com.tzx.receiver.entity.msg;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON> on 2020-05-26.
 */
public class TasteType implements Serializable{
    private Integer id;
    private String memo;
    private String kwmc;
    private String sfbx;
    private Integer minnum;
    private Integer maxnum;
    private String sort;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getKwmc() {
        return kwmc;
    }

    public void setKwmc(String kwmc) {
        this.kwmc = kwmc;
    }

    public String getSfbx() {
        return sfbx;
    }

    public void setSfbx(String sfbx) {
        this.sfbx = sfbx;
    }

    public Integer getMinnum() {
        return minnum;
    }

    public void setMinnum(Integer minnum) {
        this.minnum = minnum;
    }

    public Integer getMaxnum() {
        return maxnum;
    }

    public void setMaxnum(Integer maxnum) {
        this.maxnum = maxnum;
    }

    public String getSort() {
        return sort;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }
}
